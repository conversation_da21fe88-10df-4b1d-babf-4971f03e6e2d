{
  "pages": [
    {
      "path": "pages/home/<USER>",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "微页面",
        "onReachBottomDistance": 400
      }
    },
    {
      "path": "pages/second-tab/index",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "微页面",
        "onReachBottomDistance": 400
      }
    },
    {
      "path": "pages/shopping-cart/index",
      "style": {
        "navigationBarTitleText": "购物车",
        "componentPlaceholder": {
          "shopcart": "view"
        }
      }
    },
    {
      "path": "pages/tab-personal/index",
      "style": {
        "navigationBarTitleText": "个人中心"
      }
    },
    {
      "path": "pages/third-tab/index",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "逛逛"
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/micro-page",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "微页面"
          }
        }
      ],
      "plugins": {
        "routePlan": {
          "version": "1.0.19",
          "provider": "wx50b5593e81dd937a"
        }
      }
    },
    {
      "root": "pages/backcoupon",
      "pages": [
        {
          "path": "backcoupon",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages/bankpay",
      "pages": [
        {
          "path": "add-bank/add-bank",
          "style": {
            "navigationBarTitleText": "添加银行卡",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "add-bank/bank-list",
          "style": {
            "navigationBarTitleText": "银行卡列表",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages/questionnaire",
      "pages": [
        {
          "path": "answer",
          "style": {
            "navigationBarTitleText": "问卷调查"
          }
        }
      ]
    },
    {
      "root": "pages/home/<USER>",
      "pages": [
        {
          "path": "index"
        }
      ]
    },
    {
      "root": "pages/base",
      "pages": [
        {
          "path": "search/index",
          "style": {
            "navigationBarTitleText": "搜索联想页"
          }
        }
      ]
    },
    {
      "root": "pages/pay",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "订单支付"
          }
        }
      ]
    },
    {
      "root": "pages/public",
      "pages": [
        {
          "path": "webview/webview",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "activity/webview",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black"
          }
        }
      ]
    },
    {
      "root": "pages/common",
      "pages": [
        {
          "path": "blank-page/index"
        }
      ]
    },
    {
      "root": "pages/customer-service",
      "pages": [
        {
          "path": "customer-service-list/index",
          "style": {
            "navigationBarTitleText": "客服列表"
          }
        }
      ]
    },
    {
      "root": "pages/appraises",
      "pages": [
        {
          "path": "form/index",
          "style": {
            "navigationBarTitleText": "评价商品"
          }
        },
        {
          "path": "list/index",
          "style": {
            "navigationBarTitleText": "商品评价列表"
          }
        }
      ]
    },
    {
      "root": "pages/live",
      "pages": [
        {
          "path": "room-list/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "直播间列表"
          }
        }
      ],
      "plugins": {
        "live-player-plugin": {
          "version": "1.3.5",
          "provider": "wx2b03c6e691cd7370"
        }
      }
    },
    {
      "root": "pages/goods",
      "pages": [
        {
          "path": "goods-bygroup/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "分组商品列表"
          }
        },
        {
          "path": "goods-category/index",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": "商城分类",
            "disableScroll": true
          }
        },
        {
          "path": "goods-list/index",
          "style": {
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "搜索过渡页"
          }
        },
        {
          "path": "goods-code-list/index",
          "style": {
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "你要找的商品"
          }
        },
        {
          "path": "goods-detail/index",
          "style": {
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "商品详情"
          }
        },
        {
          "path": "sales-rank/index",
          "style": {
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "商品销量排行榜"
          }
        },
        {
          "path": "goodsrules/goodsrules",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "零钱规则"
          }
        }
      ]
    },
    {
      "root": "pages/order",
      "pages": [
        {
          "path": "order-detail/index",
          "style": {
            "navigationBarTitleText": "订单详情"
          }
        },
        {
          "path": "order-logistics/index",
          "style": {
            "navigationBarTitleText": "订单物流"
          }
        },
        {
          "path": "order-pay-result/index",
          "style": {
            "navigationBarTitleText": "订单支付结果"
          }
        },
        {
          "path": "order-confirm/index",
          "style": {
            "navigationBarTitleText": "订单确认"
          }
        },
        {
          "path": "order-refunds/form/index",
          "style": {
            "navigationBarTitleText": "申请退款"
          }
        },
        {
          "path": "order-refunds/submit/index",
          "style": {
            "navigationBarTitleText": "退款确认"
          }
        },
        {
          "path": "order-list/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "订单列表"
          }
        },
        {
          "path": "order-refunds/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "退换/售后"
          }
        }
      ],
      "plugins": {
        "logisticsPlugin": {
          "version": "2.2.28",
          "provider": "wx9ad912bf20548d92"
        }
      }
    },
    {
      "root": "pages/distribution",
      "pages": [
        {
          "path": "distribution-center/index",
          "style": {
            "navigationBarTitleText": "分销中心"
          }
        },
        {
          "path": "distribution-withdraw/index",
          "style": {
            "navigationBarTitleText": "分销提现"
          }
        },
        {
          "path": "distribution-card/index"
        },
        {
          "path": "distribution-withdraw-list/index",
          "style": {
            "navigationBarTitleText": "分销提现列表"
          }
        },
        {
          "path": "distribution-withdraw-detail/index",
          "style": {
            "navigationBarTitleText": "分销提现详情"
          }
        },
        {
          "path": "distribution-promotion-statistical/index",
          "style": {
            "navigationBarTitleText": "分销统计"
          }
        },
        {
          "path": "distribution-order-list/index",
          "style": {
            "navigationBarTitleText": "分销订单列表"
          }
        },
        {
          "path": "distribution-promotion-ranking/index",
          "style": {
            "navigationBarTitleText": "分销排名"
          }
        },
        {
          "path": "distribution-commission-month/distribution-commission-month",
          "style": {
            "navigationBarTitleText": "待结算佣金",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "distribution-withdrawn-commission/distribution-withdrawn-commission",
          "style": {
            "navigationBarTitleText": "已提现佣金",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "distribution-commodity-center/distribution-commodity-center",
          "style": {
            "navigationBarTitleText": "我的分销",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "distribution-my-client/distribution-my-client",
          "style": {
            "navigationBarTitleText": "我的客户",
            "enablePullDownRefresh": false
          }
        },
        {
          "/**/": "银行卡",
          "path": "bank-card/index",
          "style": {
            "navigationBarTitleText": "银行卡"
          }
        },
        {
          "/**/": "我的银行卡",
          "path": "my-bank-card/index",
          "style": {
            "navigationBarTitleText": "我的银行卡"
          }
        },
        {
          "/**/": "设置密码",
          "path": "set-password/index",
          "style": {
            "navigationBarTitleText": "设置密码"
          }
        },
        {
          "/**/": "确认设置密码",
          "path": "set-confirm-password/index",
          "style": {
            "navigationBarTitleText": "确认设置密码"
          }
        },
        {
          "/**/": "交易密码",
          "path": "password/index",
          "style": {
            "navigationBarTitleText": "交易密码"
          }
        },
        {
          "/**/": "提现管理",
          "path": "withdraw-admin/index",
          "style": {
            "navigationBarTitleText": "提现管理"
          }
        },
        {
          "/**/": "提现列表",
          "path": "withdraw-list/index",
          "style": {
            "navigationBarTitleText": "提现列表"
          }
        },
        {
          "/**/": "收入明细",
          "path": "revenue-details/index",
          "style": {
            "navigationBarTitleText": "收入明细"
          }
        }
      ]
    },
    {
      "root": "pages/seckill",
      "pages": [
        {
          "path": "seckill-list/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "秒杀列表"
          }
        },
        {
          "path": "seckill-detail/index",
          "style": {
            "navigationBarTitleText": "秒杀详情"
          }
        }
      ]
    },
    {
      "root": "pages/shop",
      "pages": [
        {
          "/**/": "店铺商品分类",
          "path": "shop-goods-classify/index",
          "style": {
            "navigationBarTitleText": "店铺商品分类"
          }
        },
        {
          "path": "shop-cart/index",
          "style": {
            "navigationBarTitleText": "购物车"
          }
        },
        {
          "path": "shop-detail/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTextStyle": "white",
            "navigationBarTitleText": "店铺首页"
          }
        },
        {
          "path": "shop-list/index",
          "style": {
            "navigationBarTitleText": "店铺列表"
          }
        },
        {
          "path": "enter/enter",
          "style": {
            "navigationBarTitleText": "加入松雷商业",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "Myrights/Myrights",
          "style": {
            "navigationBarTitleText": "店铺会员权益",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "InstructionsCard/InstructionsCard",
          "style": {
            "navigationBarTitleText": "店铺会员卡",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "memberAuthorization/memberAuthorization",
          "style": {
            "navigationBarTitleText": "店铺会员认证",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "point/index",
          "style": {
            "navigationBarTitleText": "店铺会员积分",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "hesuoyou/index",
          "style": {
            "navigationBarTitleText": "何所有",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages/groupon",
      "pages": [
        {
          "path": "groupon-list/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "拼团购列表"
          }
        },
        {
          "path": "groupon-detail/index",
          "style": {
            "navigationBarTitleText": "拼团购详情"
          }
        },
        {
          "path": "groupon-user-list/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "拼团购用户列表"
          }
        },
        {
          "path": "groupon-user-detail/index",
          "style": {
            "navigationBarTitleText": "拼团购详情"
          }
        },
        {
          "path": "groupon-order-confirm/index",
          "style": {
            "navigationBarTitleText": "拼团购下单"
          }
        }
      ]
    },
    {
      "root": "pages/article",
      "pages": [
        {
          "path": "article-list/index",
          "style": {
            "navigationBarTitleText": "文章列表"
          }
        },
        {
          "path": "article-info/index",
          "style": {
            "navigationBarTitleText": "文章详情"
          }
        }
      ]
    },
    {
      "root": "pages/login",
      "pages": [
        {
          "path": "index"
        },
        {
          "path": "register"
        },
        {
          "path": "logout"
        },
        {
          "path": "blank"
        },
        {
          "path": "authorization",
          "style": {
            "mp-weixin": {
              "usingComponents": {
                "mp-half-screen-dialog": "weui-miniprogram/half-screen-dialog/half-screen-dialog"
              }
            }
          }
        }
      ]
    },
    {
      "root": "pages/user",
      "pages": [
        {
          "path": "user-info/index",
          "style": {
            "navigationBarTitleText": "用户信息"
          }
        },
        {
          "path": "user-hold-address/index"
        },
        {
          "path": "user-address/list/index",
          "style": {
            "navigationBarTitleText": "用户地址"
          }
        },
        {
          "path": "user-address/form/index",
          "style": {
            "navigationBarTitleText": "地址管理"
          }
        },
        {
          "path": "user-collect/index",
          "style": {
            "navigationBarTitleText": "我的收藏"
          }
        },
        {
          "path": "user-footprint/index",
          "style": {
            "navigationBarTitleText": "我的足迹"
          }
        },
        {
          "path": "user-integral-shop/index"
        },
        {
          "path": "user-appraises/index",
          "style": {
            "navigationBarTitleText": "我的评价"
          }
        },
        {
          "path": "user-points-record/index",
          "style": {
            "navigationBarTitleText": "我的积分"
          }
        },
        {
          "path": "user-qrcode/index",
          "style": {
            "navigationBarTitleText": "我的二维码"
          }
        },
        {
          "path": "user-authentication/index",
          "style": {
            "navigationBarTitleText": "我的认证"
          }
        },
        {
          "path": "mycardbag/mycardbag",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages/ranking",
      "pages": [
        {
          "/**/": "商品排名",
          "path": "goods-ranking/index",
          "style": {
            "navigationBarTitleText": "商品排名"
          }
        }
      ]
    },
    {
      "root": "pages/wallet",
      "pages": [
        // {
        // 	"/**/": "钱包认证方式",
        // 	"path": "wallet-authentication/index"
        // },
        {
          "/**/": "人工审核",
          "path": "wallet-artificial/index",
          "style": {
            "navigationBarTitleText": "人工审核"
          }
        },
        {
          "/**/": "钱包开通方式",
          "path": "wallet-open/index",
          "style": {
            "navigationBarTitleText": "钱包开通方式"
          }
        },
        {
          "/**/": "钱包填写个人信息",
          "path": "personal-information/index",
          "style": {
            "navigationBarTitleText": "钱包填写个人信息"
          }
        },
        {
          "/**/": "钱包上传银行卡",
          "path": "upload-bank-card/index",
          "style": {
            "navigationBarTitleText": "钱包上传银行卡"
          }
        },
        {
          "/**/": "钱包客户补充个人信息",
          "path": "cust-info/index",
          "style": {
            "navigationBarTitleText": "钱包客户补充个人信息"
          }
        },
        {
          "/**/": "钱包验证手机号",
          "path": "verification-phone/index",
          "style": {
            "navigationBarTitleText": "钱包验证手机号"
          }
        },
        {
          "/**/": "我的钱包",
          "path": "wallet-pages/index",
          "style": {
            "navigationBarTitleText": "我的钱包"
          }
        },
        {
          "/**/": "钱包密码",
          "path": "wallet-password/index",
          "style": {
            "navigationBarTitleText": "钱包密码"
          }
        },
        {
          "/**/": "我的证件",
          "path": "my-certificates/index",
          "style": {
            "navigationBarTitleText": "我的证件"
          }
        },
        {
          "/**/": "我的余额",
          "path": "my-balance/index"
        },
        {
          "/**/": "账单",
          "path": "bill/index"
        },
        {
          "/**/": "开通成功",
          "path": "open-success/index"
        },
        {
          "/**/": "充值提现",
          "path": "pay/index"
        },
        {
          "/**/": "民生信用卡页",
          "path": "webview/index"
        },
        {
          "/**/": "设置",
          "path": "wallet-settings/index"
        }
      ]
    },
    {
      "root": "pages/bargain",
      "pages": [
        {
          "path": "bargain-list/index",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": "砍价列表"
          }
        },
        {
          "path": "bargain-detail/index",
          "style": {
            "navigationBarTitleText": "砍价详情"
          }
        },
        {
          "path": "bargain-user-list/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "用户砍价列表"
          }
        },
        {
          "path": "bargin-rules/bargin-rules",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": "砍价规则"
          }
        }
      ]
    },
    {
      "root": "pages/signrecord",
      "pages": [
        {
          "path": "signrecord-info/index"
        },
        {
          "path": "signrecord-list/index"
        }
      ]
    },
    {
      "root": "pages/coupon",
      "pages": [
        {
          "path": "coupon-user-list/index",
          "style": {
            "enablePullDownRefresh": false,
			 "navigationBarTitleText": "我的优惠券"
          }
        },
        {
          "path": "coupon-tiktok/index",
          "style": {
            "enablePullDownRefresh": true,
			 "navigationBarTitleText": "抖音对券"
          }
        },
        {
          "path": "coupon-shop-list/index",
          "style": {
            "enablePullDownRefresh": true,
			 "navigationBarTitleText": "店铺优惠券"
          }
        },
        {
          "path": "coupon-list/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "优惠券列表"
          }
        },
        {
          "path": "coupon-detail/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "优惠券详情"
          }
        },
        {
          "path": "coupon-offline-detail/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "线下优惠券详情"
          }
        },
        {
          "path": "coupon-offline-detail-plus/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "线下优惠券详情"
          }
        },
        {
          "path": "coupon-centre/index",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": "领券中心"
          }
        },
        {
          "path": "coupon-centre-details/index",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": "线下优惠券详情"
          }
        },
        {
          "path": "coupon-nopay-list/index",
          "style": {
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "coupon-nopay-details/index",
          "style": {
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "coupon-activation/index",
          "style": {
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "coupon-give/index",
          "style": {
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "coupon-give-info-load/index",
          "style": {
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "coupon-give-details/index",
          "style": {
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "coupon-give-receive/index",
          "style": {
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "coupon-give-history/index",
          "style": {
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "coupon-offline-gebraucht/index",
          "style": {
            "enablePullDownRefresh": true
          }
        }
      ]
    },
    {
      "root": "pages/message",
      "pages": [
        {
          "path": "list/index",
          "style": {
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "typesList/index",
          "style": {
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "typeList/index"
        }
      ]
    },
    {
      "root": "pages/business-circle",
      "pages": [
        {
          "path": "index"
        }
      ]
    },
    {
      "root": "pages/redirect",
      "pages": [
        {
          "path": "index"
        }
      ]
    },
    {
      "root": "pages/parkinglot",
      "pages": [
        {
          "path": "parking-home/index",
          "style": {
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "select-store/index",
          "style": {
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "bind-car/bind-car",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "park-coupon/park-coupon",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "parkingrate/parkingrate",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "fast-fee/fast-fee",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "fee-detail/fee-detail",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "fee-success/fee-success",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "charging-standard/charging-standard",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "empty-carquery/empty-carquery",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "patrol-car/index",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "searchtime-park/searchtime-park",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "unlicense/unlicense",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "open-etc/index",
          "style": {
            "navigationBarTitleText": "开启etc",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages/jielong",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "松鼠接龙"
          }
        }
      ]
    },
    {
      "root": "pages/gift",
      "pages": [
        {
          "/**/": "礼品卡",
          "path": "gift-card/index"
        },
        {
          "/**/": "我的礼品卡",
          "path": "my-gift-card/index"
        },
        {
          "/**/": "绑定新卡",
          "path": "new-card/index"
        },
        {
          "/**/": "付款码",
          "path": "payment-code/index"
        },
        {
          "/**/": "礼品卡付款码",
          "path": "payment-code/gift-payment-code/index"
        },
        {
          "/**/": "验证码",
          "path": "set-password/code/index"
        },
        {
          "/**/": "设置密码",
          "path": "set-password/password/index"
        },
        {
          "/**/": "确认密码",
          "path": "set-password/confirm-password/index"
        },
        {
          "/**/": "设置打开",
          "path": "set-password/index"
        },
        {
          "/**/": "礼品卡账单",
          "path": "bill-list/index"
        },
        {
          "/**/": "礼品卡赠送",
          "path": "give/index"
        },
        {
          "/**/": "礼品卡赠送列表",
          "path": "give/list/index"
        },
        {
          "/**/": "礼品卡领取",
          "path": "give/receive/index"
        },
        {
          "/**/": "礼品卡收送列表",
          "path": "give/record-list/index"
        },
        {
          "/**/": "待领取礼品卡列表",
          "path": "give/unclaimed-list/index"
        },
        {
          "/**/": "礼品卡规定与章程",
          "path": "help/index"
        },
        {
          "/**/": "礼品卡支付成功",
          "path": "success/index"
        }
      ]
    },
    {
      "root": "pages/paycoupon",
      "pages": [
        {
          "path": "paycoupon-sh/index"
        }
      ]
    },
    {
      "root": "pages/qrcodepay",
      "pages": [
        {
          "path": "identification/identification",
          "style": {
            "navigationBarTitleText": "松雷商业一码付"
          }
        },
        {
          "path": "manage/index",
          "style": {
            "navigationBarTitleText": "一码付管理"
          }
        },
        {
          "path": "pay-list/index",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "webview/webview",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "/**/": "一码付支付成功",
          "path": "success/index"
        }
      ]
    },
    {
      "root": "pages/invoice",
      "pages": [
        {
          "/**/": "停车发票",
          "path": "parkApply/index"
        },
        {
          "/**/": "停车发票",
          "path": "parkApply/orderList"
        },
        {
          "/**/": "申请开票",
          "path": "parkApply/openInvoice"
        },
        {
          "/**/": "停车发票",
          "path": "parkApply/applyRes"
        },
        {
          "/**/": "重发发票",
          "path": "parkApply/againInvoice"
        },
        {
          "/**/": "开票历史",
          "path": "parkApply/history",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": ""
          }
        },
        {
          "/**/": "发票详情",
          "path": "parkApply/invoiceDetail"
        },
        {
          "/**/": "订单详情",
          "path": "parkApply/orderDetail"
        },
        {
          "/**/": "发票中心",
          "path": "invoiceCenter/index"
        },
        {
          "/**/": "线上订单",
          "path": "invoiceCenter/onlineOrder"
        },
        {
          "/**/": "线下订单",
          "path": "invoiceCenter/offlineOrder"
        },
        {
          "/**/": "申请开票",
          "path": "invoiceCenter/writeInvoice"
        },
        {
          "/**/": "发票详情",
          "path": "invoiceCenter/invoiceDesc"
        },
        {
          "/**/": "开票中心",
          "path": "invoiceCenter/posScan"
        },
        {
          "/**/": "申请开票",
          "path": "invoiceCenter/applyFinish"
        },
        {
          "/**/": "抬头管理",
          "path": "userInvoice/index"
        },
        {
          "/**/": "申请发票",
          "path": "applicat/index"
        },
        {
          "/**/": "发票详情",
          "path": "detail/index"
        }
      ]
    },
    {
      "root": "pages/squirrelParadise",
      "pages": [
        {
          "path": "home/index",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "task/browseProduct",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "task/placeOrder",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "task/rewardList",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "task/offlineGuidance",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "task/codeResult",
          "style": {
            "navigationBarTitleText": ""
          }
        }
      ]
    },
    {
      "root": "pages/myPrize",
      "pages": [
        {
          "path": "index",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": "我的奖品"
          }
        }
      ]
    },
    {
      "root": "pages/camera",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "照片搜索"
          }
        },
        {
          "path": "select-list",
          "style": {
            "navigationBarTitleText": "历史记录"
          }
        }
      ]
    },
    {
      "root": "pages/activity",
      "pages": [
        {
          "path": "lottery/index",
          "style": {
            "navigationBarTitleText": "大转盘"
          }
        },
        {
          "path": "nineGrid/index",
          "style": {
            "navigationBarTitleText": "九宫格"
          }
        },
        {
          "path": "assiatance/index",
          "style": {
            "navigationBarTitleText": "抽奖助力"
          }
        },
        {
          "path": "mycompetition/index",
          "style": {
            "navigationBarTitleText": "我的竞猜"
          }
        },
        {
          "path": "my-gold-medal/index",
          "style": {
            "navigationBarTitleText": "奥运会竞猜"
          }
        },
        {
          "path": "iceWorld/index",
          "style": {
            "navigationBarTitleText": "冰雪大世界"
          }
        },
        {
          "path": "iceWorld/prize",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "audio-game/index",
          "style": {
            "navigationBarTitleText": ""
          }
        }
      ]
    },
    {
      "root": "pages/perfect",
      "pages": [
        {
          "/**/": "完善用户信息",
          "path": "user-info/index"
        }
      ]
    },
    {
      "root": "pages/customImage-preview",
      "pages": [
        {
          "/**/": "图片二维码预览",
          "path": "index"
        }
      ]
    },
    {
      "root": "pages/pos",
      "pages": [
        {
          "path": "agreeauth"
        },
        {
          "path": "loginagree"
        }
      ]
    },
    {
      "root": "pages/memberactivity",
      "pages": [
        {
          "/**/": "会员活动列表",
          "path": "list/index"
        },
        {
          "/**/": "会员活动列表",
          "path": "activityTimeList/index"
        },
        {
          "/**/": "会员活动详情",
          "path": "detail/index"
        },
        {
          "/**/": "会员报名",
          "path": "application/index"
        },
        {
          "/**/": "会员报名详情",
          "path": "application-detail/index"
        },
        {
          "/**/": "会员活动列表",
          "path": "my-application-list/index"
        },
        {
          "/**/": "会员退款",
          "path": "refund/index"
        },
        {
          "/**/": "活动评价",
          "path": "appraises/form/index"
        },
        {
          "/**/": "活动评价",
          "path": "appraises/list/index"
        }
      ]
    },
    {
      "root": "pages/prepaid",
      "pages": [
        {
          "path": "pay-me/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "帮我付"
          }
        },
        {
          "path": "prepay/index",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "帮我付"
          }
        }
      ]
    },
    {
      "root": "pages/storagelocker",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "自助存取包"
          }
        },
        {
          "path": "my",
          "style": {
            "navigationBarTitleText": "我的"
          }
        },
        {
          "path": "choice",
          "style": {
            "navigationBarTitleText": "请选择寄存柜"
          }
        },
        {
          "path": "self-service-cabinet",
          "style": {
            "navigationBarTitleText": "自选柜"
          }
        },
        {
          "path": "neighbourhood",
          "style": {
            "navigationBarTitleText": "附近柜组"
          }
        },
        {
          "path": "success",
          "style": {
            "navigationBarTitleText": "成功"
          }
        },
        {
          "path": "order",
          "style": {
            "navigationBarTitleText": "我的寄存"
          }
        },
        {
          "path": "order-confirmation",
          "style": {
            "navigationBarTitleText": "订单确认"
          }
        },
        {
          "path": "administrator",
          "style": {
            "navigationBarTitleText": "管理员"
          }
        }
      ]
    },
    {
      "root": "pages/tiktok",
      "pages": [
        {
          "path": "voucher",
          "style": {
            "navigationBarTitleText": "抖音兑换券"
          }
        }
      ]
    },
    {
      "root": "pages/jigsaw",
      "pages": [
        {
          "path": "startGames",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "myprizes",
          "style": {
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "tabBar": {
    "color": "#666666",
    "selectedColor": "#cb0101",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "松鼠美妆",
        "iconPath": "/static/public/img/icon/1-001.jpg",
        "selectedIconPath": "/static/public/img/icon/1-002.jpg"
      },
      {
        "pagePath": "pages/second-tab/index",
        "text": "松鼠好物",
        "iconPath": "/static/public/img/icon/2-001.jpg",
        "selectedIconPath": "/static/public/img/icon/2-002.jpg"
      },
      {
        "pagePath": "pages/third-tab/index",
        "text": "逛逛",
        "iconPath": "/static/public/img/icon/3-001.jpg",
        "selectedIconPath": "/static/public/img/icon/3-002.jpg"
      },
      {
        "pagePath": "pages/shopping-cart/index",
        "text": "购物车",
        "iconPath": "/static/public/img/icon/4-001.jpg",
        "selectedIconPath": "/static/public/img/icon/4-002.jpg"
      },
      {
        "pagePath": "pages/tab-personal/index",
        "text": "我的",
        "iconPath": "/static/public/img/icon/5-001.jpg",
        "selectedIconPath": "/static/public/img/icon/5-002.png"
      }
    ]
  },
  "easycom": {
    "autoscan": true,
    "custom": {
      "share-cart-(.*)": "@/components/share-$1/share-$1.vue",
      "goods-card-(.*)": "@/components/goods-$1/goods-$1.vue",
      "suspension-cart-(.*)": "@/components/suspension-$1/suspension-$1.vue",
      "goods-sku-(.*)": "@/components/goods-$1/goods-$1.vue",
      "class-label-(.*)": "@/components/class-$1/class-$1.vue",
      "coupon-user-info-platform-(.*)": "@/components/coupon-$1/coupon-$1.vue",
      "delivery-timeperiod-(.*)": "@/components/delivery-$1/delivery-$1.vue",
      "pay-components-(.*)": "@/components/pay-$1/pay-$1.vue",
      "coupon-info-(.*)": "@/components/coupon-$1/coupon-$1.vue",
      "price-handle": "@/components/price-handle/index.vue",
      "format-price": "@/components/format-price/index.vue"
    }
  },
  "globalStyle": {
    "navigationStyle": "custom",
    "navigationBarTextStyle": "black",
    "maxWidth": 375,
    "dynamicRpx": true
  },
  "singlePage": {
    "navigationBarFit": "squeezed"
  },
  "usingComponents": {}
}
