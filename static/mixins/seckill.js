import api from '@/utils/api';


export const seckillMixin={
	// components:{
	// 	countDown
	// },
	watch:{
		curHour(){}
	},
    data() {
        return {   
            curHour: 0, //当前小时
			seckillList: [],
			curSeckillHall: {//当前会场
			
			},
			outTime: -1,
			hasSeckill:false,
			day: null,
			hour: null,
			minute: null,
			second: null,
			timer: null,
        }
    },
	
	created: function() {
		this.curHour = this.$moment().format("H");
		this.seckillhallList();
	},
	destroyed: function() {
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
    methods: {
       countDownDone() {
       	this.seckillhallList();
       },
       seckillhallList() {
       	let curDate = this.$moment().format("YYYY-MM-DD");
       	let that = this;
       	api.seckillhallList(curDate).then(res => {
       		let seckillList = res.data;
       		if(seckillList&&seckillList.length>0){
				
       			 this.hasSeckill = false;
       			this.seckillList = seckillList;
       			seckillList.forEach((item,index)=>{
					console.log("======item.hallTime====",that.curHour,item.hallTime)
       				if(item.hallTime==that.curHour){//默认设置当前小时的秒杀时间，如果没有就设置最近时间的秒杀时间
       					this.curSeckillHall = item;
       					this.hasSeckill = true;
       					return;
       				}else if(!this.hasSeckill&&item.hallTime>that.curHour){//秒杀时间必须大于当前时间
       					this.curSeckillHall = item;
       					this.hasSeckill = true;
       					return;
       				}
       			})
       			if(!this.hasSeckill){
       				this.curSeckillHall = seckillList[0];
       			}
       			this.setCountDown();
       		}else{
       			this.seckillList = [];
       		}
       	});
       },
       setCountDown(){
       	// 设置倒计时时间，
       	// 如果当前时间大于会场时间，结束
       	// 如果当前时间等于，正在进行中
       	// 如果小于暂未开始
       	if(this.curSeckillHall.hallTime<this.curHour){
       		this.outTime = 0;
       	}else if(this.curSeckillHall.hallTime==this.curHour){//计算倒计时多少秒
       		let curDateTime = new Date().getTime();//当前时间
       		let nextHour = Number(this.curHour) + 1;
       		let nextDateTime = this.curSeckillHall.hallDate + ' ' + nextHour + ':00:00';
       		let timeTemp = this.$moment(nextDateTime).toDate();
       		this.outTime = new Date(timeTemp).getTime() - curDateTime;//下一个整点时间
			this.CaculateDate();
       	}else{
       		this.outTime=-1;
       	}
       },
	   
	   CaculateDate: function() {
		   console.log("======CaculateDate=========")
	   	var that = this;
	   	if (this.totalTime == 0) {
	   		this.totalTime = this.outTime;
	   	}
	   	this.timer = setInterval(function() {
	   		var leftTime = that.totalTime - 1000;
	   		var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10);
	   		var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10);
	   		var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);
	   		var seconds = parseInt(leftTime / 1000 % 60, 10);
	   		if (leftTime > 0) {
	   			that.totalTime = leftTime;
	   			that.day = days > 0 ? that.timeFormat(days) : null;
	   			that.hour = hours > 0 ? that.timeFormat(hours) : null;
	   			that.minute = minutes > 0 ? that.timeFormat(minutes) : null;
	   			that.second = seconds > 0 ? that.timeFormat(seconds) : 0;
	   		} else {
	   			//结束
	   			clearInterval(that.timer);
	   			setTimeout(function() {
	   				that.$emit('countDownDone', null);
	   			}, 2000);
	   		}
	   	}, 1000);
	   },
	   
	   timeFormat(param) {
	   	//小于10的格式化函数
	   	return param < 10 ? '0' + param : param;
	   }
    }
}