import { senGoods } from '@/public/js_sdk/sensors/utils.js';
export const navigateUtil = {
	data() {
		return {
			pages: [],
			pagesLength: 0,
			//白名单
			urlList: [
				'/pages/home/<USER>', //首页
				'/pages/second-tab/index', //松鼠好物
				'/pages/shopping-cart/index', //购物车
				'/pages/tab-personal/index', //个人中心
			]
		}
	},
	onLoad(options) {
		//监听当前页面栈的个数内容
		let pages = getCurrentPages()
		this.pagesLength = pages.length //当前页面栈的个数
	},
	created: function () {
		//监听当前页面栈的个数内容
		let pages = getCurrentPages()
		this.pagesLength = pages.length //当前页面栈的个数
	},

	methods: {
		// 这里是一个自定义方法
		// type : 'goodDetail' 商品详情， 点击商品卡 数据统计使用  
		// data 完整的数据信息
		toPage: function (page, type, data, index, search_keyword, search_type, source_module) {
			let _this = this
            console.log("跳转数据", page)
            // 自动拼接 source_module
            if (page && page.indexOf('/pages/goods/goods-detail/index') === 0 && source_module) {
                if (page.indexOf('?') > -1) {
                    page += `&source_module=${encodeURIComponent(source_module)}`;
                } else {
                    page += `?source_module=${encodeURIComponent(source_module)}`;
                }
            }
			if (_this.pagesLength == 8) {
				uni.redirectTo({
					url: page
				})
			} else if (_this.pagesLength == 10) {
				uni.redirectTo({
					url: page
				})
			} else if (this.urlList.includes(`${page}`)) {
				console.log("2222", page);
				uni.switchTab({
					url: page
				});
			} else {
				console.log("3333", page);
				uni.navigateTo({
					url: page
				})
			}

			if (type == 'goodDetail') {
				console.error("=======to goodDetail =============", data);
				let original_price = '';
				if (data.priceOriginal && data.priceOriginal > 0 && data.priceOriginal > data.priceDown && data
					.priceOriginal != data.priceUp) {
					original_price = Number(data.priceOriginal);
				} else if (data.estimatedPriceVo) {
					original_price = Number(data.estimatedPriceVo.price);
				}
				let current_price = "";
				if (data.estimatedPriceVo && data.estimatedPriceVo.price > 0 && data.estimatedPriceVo
					.promotionsDiscountPrice > 0) {
					current_price = Number(data.estimatedPriceVo.price - data.estimatedPriceVo.promotionsDiscountPrice)
				} else {
					current_price = Number(data.priceDown)
				}

				//有搜索类型 则上报神策搜索结果点击
				if(this.searchType){
					const promotion_type_list = [];
					if(data.estimatedPriceVo?.promotionsDiscountPrice>0){
						promotion_type_list.push('折扣');
					}
					if(data.estimatedPriceVo?.coupon>0 || data.estimatedPriceVo?.discountPrice>0){
						promotion_type_list.push('优惠券');
					}
					senGoods("SearchResultClick", {
						search_keyword,
						keyword_type: search_type,
						goods_rank: index,
						goods_crossed_price:data.estimatedPriceVo?.originalPrice>0?Number(data.estimatedPriceVo.originalPrice):Number(data.priceUp),
						goods_strike_price: data.estimatedPriceVo?.estimatedPrice>0?Number(data.estimatedPriceVo.estimatedPrice):Number(data.priceDown),
						business_format:  data.formatType,
						is_flash_sale: false,
						promotion_type_list,
					}, data);
				}
				
			}
		}
	},

}