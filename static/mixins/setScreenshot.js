export const setScreenshot = {
	data() {
		return {}
	},
	onReady() {},

	onLoad(options) {
		this.addFlags()
	},

	onShow() {},

	onHide() {},

	onUnload: function () {
		this.clearFlags()
	},

	methods: {
		// 禁止截屏
		addFlags() {
			// #ifdef APP-PLUS
			let osname = plus.os.name
			if (osname == "Android") {
				var activity = plus.android.runtimeMainActivity()
				console.log(activity);
				console.log('开启防截屏');
				plus.android.invoke(plus.android.invoke(activity, "getWindow"), "addFlags", 0x00002000)
			}
			// #endif

			// #ifdef MP-WEIXIN
			// if (uni.getSystemInfoSync().platform == 'android') {
			console.log('运行Android上');
			// uni.onUserCaptureScreen(function () {
			// console.log('用户截屏了');
			if (wx.setVisualEffectOnCapture) {
				console.log('关闭用户截屏了111')
				wx.setVisualEffectOnCapture({
					visualEffect: 'hidden',
				})
			};
			// })
			// #endif
		},

		//  允许截屏  
		clearFlags() {
			// #ifdef APP-PLUS
			let osname = plus.os.name
			if (osname == "Android") {
				var activity = plus.android.runtimeMainActivity()
				console.log('关闭防截屏');
				plus.android.invoke(plus.android.invoke(activity, "getWindow"), "clearFlags", 0x00002000)
			}
			// #endif

			// #ifdef MP-WEIXIN
			// if (uni.getSystemInfoSync().platform == 'android') {
			console.log('运行Android上');
			// uni.offUserCaptureScreen(function () {
			console.log('取消关闭用户截屏')
			if (wx.setVisualEffectOnCapture) {
				wx.setVisualEffectOnCapture({
					visualEffect: 'none',
				})
				// };
			}
			// })
			// #endif
		}
	}
}