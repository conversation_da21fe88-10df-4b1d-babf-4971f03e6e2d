import api from '@/utils/api';
const app = getApp();
export const cartCountMix = {
	data() {
		return {
			shoppingCartCount: 0,
		}
	},
	onShow() {
		this.shoppingCartCountFun();
	},

	methods: {
		//购物车数量
		shoppingCartCountFun() {
			api.shoppingCartCount().then(res => {
				this.shoppingCartCount = res.data; //设置TabBar购物车数量
				console.log("this.shoppingCartCount==>", this.shoppingCartCount);
				app.globalData.shoppingCartCount = this.shoppingCartCount + '';
			});
		},
	}
}
