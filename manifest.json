{
    "name" : "松鼠美淘APP",
    "appid" : "__UNI__9C3B8BC",
    "description" : "",
    "versionName" : "3.8.33",
    "versionCode" : 3833,
    "transformPx" : false,
    "sassImplementationName" : "node-sass",
    "app-plus" : {
        "debug" : true,
        "webView" : {
            "debug" : true
        },
        "compatible" : {
            "ignoreVersion" : true
        },
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "Payment" : {},
            "Share" : {},
            "Maps" : {},
            "Barcode" : {},
            "Camera" : {},
            "FaceID" : {},
            "Geolocation" : {},
            "VideoPlayer" : {},
            "Push" : {},
            "OAuth" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_LOCATION_PROVIDER\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_SHORTCUT\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "permissionExternalStorage" : {
                    // app首次启动关闭权限申请
                    "request" : "none", //none、once、always
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                },
                "permissionPhoneState" : {
                    // app首次启动关闭权限申请
                    "request" : "none", //none、once、always
                    "prompt" : "程序运行时，顾客需要咨询问题时可以拨打商家电话，请允许"
                },
                "permissionCameraState" : {
                    // app首次启动关闭权限申请
                    "request" : "none", //none、once、always
                    "prompt" : "程序运行时，可以扫条码查看对应商品"
                },
                "schemes" : "songlei,meitao",
                "targetSdkVersion" : 30,
                "minSdkVersion" : 24
            },
            "ios" : {
                "privacyDescription" : {
                    "NSLocalNetworkUsageDescription" : "获取商品相关的数据内容",
                    "NSCameraUsageDescription" : "扫商品条码识别当前商品信息"
                },
                "dSYMs" : false,
                "idfa" : false,
                "urltypes" : "songlei,meitao"
            },
            "sdkConfigs" : {
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wx562765da70f9a939",
                        "UniversalLinks" : "https://shopapi.songlei.com/"
                    }
                },
                "push" : {},
                "oauth" : {
                    "weixin" : {
                        "appid" : "wx562765da70f9a939",
                        "UniversalLinks" : "https://shopapi.songlei.com/"
                    },
                    "univerify" : {}
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx562765da70f9a939",
                        "UniversalLinks" : "https://shopapi.songlei.com/"
                    },
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "appleiap" : {}
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "278d90973d47ac68f95f02fefc2a4acd",
                        "appkey_android" : "67ddc848a8292df6954d6873a1d8154a"
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "nativePlugins" : {
            "Mpaas-Scan" : {
                "AppId" : "ALIPUB3B6314C241841",
                "License" : "VNWwlad834dCDtVP1JAcp/kbV8idc40LcFIKfjGQ4GkAnZsNJ4VGyEZrVfAM8FbKoyy72j6ROUZKcezq5SXXkV9Gx7Rbh47eMEmnxNb81/DGICTff3Z6cEzITDIZl149zVMjCWjPxkIy6JZwivW2hinJ/a1LWtbuOJw4z2GWoXn0pdsk+qsFgGvw6Pxsuc6Z8rFh83Q8gb6Ao2d4WvnTAqykpV6cIgAKsQH9Dg8nLkJD1QHcUdgzdkGdkBQpEIYrJCCoOMzmK6s3F70oEgu3zFgFyR+7R01MiNsbIB2gUlT9BNiVDudZgwqYKzHByDB7qrddPDZ6wfxcAXromp9sdw==",
                "WorkspaceId" : "default",
                "__plugin_info__" : {
                    "name" : "支付宝原生扫码插件",
                    "description" : "支付宝原生扫码组件，包体积仅0.7MB，15分钟即可完成接入。同时，mPaaS提供「扫码分析」大盘",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=2636",
                    "android_package_name" : "com.squirrel.meitao",
                    "ios_bundle_id" : "com.meitao.ios",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "2636",
                    "parameters" : {
                        "AppId" : {
                            "des" : "Android平台的AppId，请填写Android的config文件中的appId对应的值",
                            "key" : "mobilegw.appid",
                            "value" : ""
                        },
                        "License" : {
                            "des" : "Android平台的License,，请填写Android的config文件中的mpaasConfigLicense对应的值",
                            "key" : "mpaasConfigLicense",
                            "value" : ""
                        },
                        "WorkspaceId" : {
                            "des" : "Android平台的WorkspaceId，请填写Android的config文件中的workspaceId对应的值",
                            "key" : "workspaceId",
                            "value" : ""
                        }
                    }
                }
            },
            "JY-JPushThird" : {
                "android_appkey" : "e0210f8e88be50f0a44fd385",
                "android_channel" : "android",
                "android_honor_appid" : "",
                "android_hw_appid" : "",
                "android_mz_appid" : "",
                "android_mz_appkey" : "",
                "android_oppo_appid" : "",
                "android_oppo_appkey" : "",
                "android_oppo_appsecret" : "",
                "android_vivo_appid" : "",
                "android_vivo_appkey" : "",
                "android_xm_appid" : "",
                "android_xm_appkey" : "",
                "ios_appkey" : "e0210f8e88be50f0a44fd385",
                "ios_channel" : "ios",
                "__plugin_info__" : {
                    "name" : "【JY-JPush】极光推送插件 - 支持最新厂商推送",
                    "description" : "支持华为、小米、OPPO、VIVO、魅族、华硕厂商推送",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=4039",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "4039",
                    "parameters" : {
                        "android_appkey" : {
                            "des" : "极光推送的APPKEY",
                            "key" : "JPUSH_APPKEY",
                            "value" : ""
                        },
                        "android_channel" : {
                            "des" : "极光推送的渠道名称",
                            "key" : "JPUSH_CHANNEL",
                            "value" : ""
                        },
                        "android_honor_appid" : {
                            "des" : "荣耀APPID",
                            "key" : "com.hihonor.push.app_id",
                            "value" : "104416312"
                        },
                        "android_hw_appid" : {
                            "des" : "极光推送（厂商推送）-华为APPID例：appid=*****",
                            "key" : "com.huawei.hms.client.appid",
                            "value" : ""
                        },
                        "android_mz_appid" : {
                            "des" : "魅族APPID，以'MZ-'开头",
                            "key" : "MEIZU_APPID",
                            "value" : ""
                        },
                        "android_mz_appkey" : {
                            "des" : "魅族APPKEY，以'MZ-'开头",
                            "key" : "MEIZU_APPKEY",
                            "value" : ""
                        },
                        "android_oppo_appid" : {
                            "des" : "OPPO的APPID，以'OP-'开头",
                            "key" : "OPPO_APPID",
                            "value" : "31337951"
                        },
                        "android_oppo_appkey" : {
                            "des" : "OPPO的APPKEY，以'OP-'开头",
                            "key" : "OPPO_APPKEY",
                            "value" : "d9fd901bfd874781a7d24d3aa480b741"
                        },
                        "android_oppo_appsecret" : {
                            "des" : "OPPO的APPSECRET，以'OP-'开头",
                            "key" : "OPPO_APPSECRET",
                            "value" : "49cae390fa074c3197d3ba4bbfddfc63"
                        },
                        "android_vivo_appid" : {
                            "des" : "VIVO的APPID",
                            "key" : "com.vivo.push.app_id",
                            "value" : "105680248"
                        },
                        "android_vivo_appkey" : {
                            "des" : "VIVO的APPKEY",
                            "key" : "com.vivo.push.api_key",
                            "value" : "d8a326aaa321b7cf772325742ff77039"
                        },
                        "android_xm_appid" : {
                            "des" : "极光推送（厂商推送）-小米APPID，例：MI-*****",
                            "key" : "XIAOMI_APPID",
                            "value" : "2882303761520270698"
                        },
                        "android_xm_appkey" : {
                            "des" : "极光推送（厂商推送）-小米APPKEY，例：MI-*****",
                            "key" : "XIAOMI_APPKEY",
                            "value" : "9WSGuVqwW8FFrFbabmDksg=="
                        },
                        "ios_appkey" : {
                            "des" : "极光推送的APPKEY",
                            "key" : "JPUSH_APPKEY",
                            "value" : "e0210f8e88be50f0a44fd385"
                        },
                        "ios_channel" : {
                            "des" : "极光推送的渠道名称",
                            "key" : "JPUSH_CHANNEL",
                            "value" : "JPUSH_IOS"
                        }
                    }
                }
            },
            "Sensorsdata-UniPlugin-App" : {
                "__plugin_info__" : {
                    "name" : "神策分析原生插件",
                    "description" : "神策分析原生插件",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=4179",
                    "android_package_name" : "com.squirrel.meitao",
                    "ios_bundle_id" : "com.meitao.ios",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "4179",
                    "parameters" : {}
                }
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "useExtendedLib" : {
            "weui" : true
        },
        "appid" : "wx5bdb3c8113221e5d",
        "setting" : {
            "urlCheck" : true,
            "minified" : true,
            "es6" : false,
            "postcss" : false
        },
        "usingComponents" : true,
        "plugins" : {
            "business-circle-plugin" : {
                "version" : "2.6.2",
                "provider" : "wxfab2bf944bfc4da6"
            }
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序位置接口的效果展示"
            },
            "scope.requireFloatingWindow" : false // 禁用悬浮窗权限
        },
        "requiredPrivateInfos" : [ "getLocation" ],
        "optimization" : {
            "subPackages" : true
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "h5" : {
        "devServer" : {
            "disableHostCheck" : true,
            "port" : 8085,
            "proxy" : {
                "/mallapi" : {
                    "target" : "http://shopadmin.songlei.com/",
                    "pathRewrite" : {
                        "^/mallapi" : "/mallapi"
                    }
                }
            },
            "https" : false
        },
        "router" : {
            "mode" : "history"
        },
        "title" : "松鼠美淘",
        "domain" : "jiuzhuokeji.cn",
        "sdkConfigs" : {
            "maps" : {}
        },
        "template" : "template.h5.html"
    }
}
