<template>
	<view>
		<!-- #ifndef MP -->
    <!-- <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
      <block slot="content">{{ title }}</block>
    </cu-custom> -->
		<!-- #endif -->
		<view class="margin-top-xl">
			<web-view :src="url" :webview-styles="webviewStyles" :update-title="false" :fullscreen="false"></web-view>
		</view>
		<share-single-page v-if="scene==1154" />
	</view>
</template>
<script>
	import util from "utils/util";
	import __config from 'config/env';

	import shareSinglePage from "@/components/share-single-page/index.vue";
	import api from 'utils/api'
	const app = getApp();
	import {
		gotoPage
	} from 'components/div-components/div-base/div-page-urls.js'

	export default {
		components: {
			shareSinglePage
		},
		data() {
			return {
				// #ifndef MP
				theme: app.globalData.theme, //全局颜色变量
				// #endif
				url: "",
				title: "浏览",
				actid: "",
				webviewStyles: {
					progress: {
						color: "#FF3333",
					},
				},
				//有数统计使用
				page_title: "H5页面",
				scene: '',
				shareObj: {
					title: '松鼠美淘大转盘',
					imageUrl: ''
				},
				// 大转盘要跳转的活动页面
				goToUrl: '',
				onShowTimes: 0,
				fromUrl: ''
			};
		},

		onShareTimeline: function(res) {
			console.log(res);
		},

		onShareAppMessage: function(res) {
			let that = this;
			if (res.from == 'menu') {
				console.log("===onShareAppMessage===1===", that.url);
				var paras = that.url.split("=", 10)[1];
				console.log("===onShareAppMessage===2===", paras);
				var id = paras.split("&")[0];
				api.getActShare(id).then(res => {
					console.log('res===', res.data);
					uni.showToast({
						title: res.data,
						icon: 'none'
					})
					const tempFromUrl = this.url;
					this.url = "";
					setTimeout(() => {
						// this.url="https://shopapi.songlei.com/slshop-h5/agreement/user.html"
						this.url = tempFromUrl + '&times=' + (new Date().getTime());
					}, 100)
				}).catch(e => {
					console.log("===onShareAppMessage===4===", 4);
				})
			}
			return {
				title: this.shareObj.title,
				imageUrl: this.shareObj.imageUrl,
				success(res) {
					console.log(res, '分享成功111111')
				},
				fail(res) {
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					})
				}
			}
		},

		onLoad: function(option) {
			let that = this;
			//#ifdef MP-WEIXIN
			wx.showShareMenu({
				withShareTicket: true,
				//设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
				menus: ["shareAppMessage", "shareTimeline"]
			})
			let getLaunchOptions = uni.getLaunchOptionsSync()
			this.scene = getLaunchOptions.scene
			//#endif
			uni.setNavigationBarColor({
				frontColor: "#000000", // 必写项
				backgroundColor: "#ffffff", // 必写项
				// animation: { // 可选项
				//     duration: 400,
				//     timingFunc: 'easeIn'
				// }
			});
			console.log("===", option.scene)
			const {
				scene,
				type,
				id
			} = option;
			if (scene) {
				//接受二维码中参数
				let scenes = decodeURIComponent(option.scene).split("&");
				console.log("scenes===", scenes)
				this.fromUrl = scenes[0];
			} else {
				if (type == 'nineGrid') {
					this.fromUrl = "nineGrid?id=" + id
				} else {
					this.fromUrl = decodeURIComponent(option.url);
				}
			}
			console.log("fromUrl=111==", this.fromUrl)
			if (this.fromUrl && this.fromUrl.indexOf("nineGrid") != -1) {
				console.log("fromUrl=222==", this.fromUrl);
				const pages = getCurrentPages();
				const pagesLength = pages.length; //当前页面栈的个数

				// 如果是直接进来到大转盘页面，大转盘是第一个页面
				if (pagesLength == 1) {
					uni.navigateTo({
						url: "/pages/public/activity/webview?url=" + encodeURIComponent(this.fromUrl)
					})
					let paras = this.fromUrl.split("=", 10)[1];
					let id = paras.split("&")[0];
					//获取大转盘id
					api.getActInfo(id).then(res => {
						// that.shareObj.title = res.data.actInfo.name;
						that.goToUrl = res.data.actInfo.goToUrl;
					});
					return
				}

				// 如果大转盘不是第一个页面
				if (this.fromUrl.startsWith("nineGrid")) {
					this.fromUrl = this.fromUrl.replace('nineGrid', __config.nineGridUrl)
				}
				const clientType = "MA"; //客户端类型小程序
				const appId = uni.getAccountInfoSync().miniProgram.appId; //小程序appId
				const thirdSession = uni.getStorageSync("third_session") ?
					uni.getStorageSync("third_session") :
					"";
				if (!thirdSession) {
					util.backLoginPage();
					return;
				}
				this.fromUrl += `&client-type=${clientType}&app-id=${appId}&third-session=${thirdSession}`;
				let paras = this.fromUrl.split("=", 10)[1];
				let id = paras.split("&")[0];
				api.getActInfo(id).then(res => {
					// that.shareObj.title = res.data.actInfo.name;
					that.shareObj.imageUrl = res.data.actInfo.shareImg;
					that.goToUrl = res.data.actInfo.goToUrl;
				});
			}
			console.log("====this.fromUrl============", this.fromUrl)
			this.url = this.fromUrl;
			if (option.title) {
				this.page_title = option.title;
			}
		},

		onShow() {
			this.onShowTimes++;
			const pages = getCurrentPages();
			const pagesLength = pages.length; //当前页面栈的个数
			//如果是跳转到大转盘又返回到该页面的，就跳转到活动页面
			if (this.onShowTimes >= 2) {
				// 属于大转盘的承接页面
				if (this.fromUrl && this.fromUrl.indexOf("nineGrid") != -1) {
					if (this.goToUrl) {
						//监听当前页面栈的个数内容
						gotoPage(this.goToUrl,pagesLength, true);
					} else {
						if (pagesLength > 1) {
							this.goToUrl = "";
							uni.navigateBack({
								delta: 1
							})
						} else {
							uni.reLaunch({
								url: "/pages/home/<USER>"
							})
						}
					}
				}

			}
		},

		onUnload() {
			if (this.fromUrl && this.fromUrl.indexOf("nineGrid") != -1 && this.goToUrl) {
				uni.redirectTo({
					url: this.goToUrl
				})
			}
		}
	};
</script>
