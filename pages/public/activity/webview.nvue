<template>
    <view>
		<view class="uni-navbar">
			<view :class="{ 'uni-navbar--fixed': fixed}" :style="{ 'background-color': backgroundColor }"
			 class="uni-navbar__content">
				<view :style="{ height: statusBarHeight }" class="uni-status-bar"></view>
				<view :style="{ color: color,backgroundColor: backgroundColor }" class="uni-navbar__header uni-navbar__content_view">

					<view class="uni-navbar__header-container " @click="BackPage">
						<view  class="uni-navbar__header-btns uni-navbar__header-btns-left ">
							<view class="uni-navbar-btn-text uni-navbar__content_view">
								<text :style="{ color: color, fontSize: '16px' }"><返回</text>
							</view>
						</view>
						<view class="uni-navbar__header-container-inner " >
							<text class="uni-nav-bar-text" :style="{color: color }">{{ title }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view>
			<web-view :src="url" :style="'width:'+screenWidth+';height:' +screenHeight+';'" :webview-styles="webviewStyles"></web-view>
		</view>
	</view>
</template>
<script>
	const app = getApp();
	var statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px'
	var screenHeight = (uni.getSystemInfoSync().screenHeight-80) + 'px'
	var screenWidth = uni.getSystemInfoSync().screenWidth + 'px'
	export default {
		data() {
			return {
				globalData: app.globalData,
				url:"",
				title:'浏览',
				fixed: false,
				color:  "#000000",
				backgroundColor: "#FFFFFF",
				statusBarHeight: statusBarHeight,
				screenHeight: screenHeight,
				screenWidth: screenWidth,
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				},
				//有数统计使用
				page_title:'H5页面'
			}
		},

		onLoad: function(option) {
			this.url = option.url;
            console.log(this.url,'this.url')
			if(option.title){
				this.title = option.title;
				uni.setNavigationBarTitle({
					title:option.title,
				})
				this.page_title =  option.title;				
			}
		},
		onShow() {
		},
		methods: {
			BackPage() {
				uni.navigateBack();
			}
		}
	}
</script>
<style lang="scss" scoped>
	// .uni-status-bar {
	// 	width: 750rpx;
	// 	height: 20px;
	// 	// height: var(--status-bar-height);
	// }

	// $nav-height: 44px;
	// .uni-nav-bar-text {
	// 	/* #ifdef APP-PLUS */
	// 	font-size: 34rpx;
	// 	/* #endif */
	// 	/* #ifndef APP-PLUS */
	// 	font-size: $uni-font-size-lg;
	// 	/* #endif */
	// }
	// .uni-nav-bar-right-text {
	// 	font-size: $uni-font-size-base;
	// }

	// .uni-navbar {
	// 	width: 750rpx;
	// }

	// .uni-navbar__content {
	// 	position: relative;
	// 	width: 750rpx;
	// 	background-color: $uni-bg-color;
	// 	overflow: hidden;
	// }

	// .uni-navbar__content_view {
	// 	/* #ifndef APP-NVUE */
	// 	display: flex;
	// 	/* #endif */
	// 	align-items: center;
	// 	flex-direction: row;
	// 	// background-color: #FFFFFF;
	// }

	// .uni-navbar__header {
	// 	/* #ifndef APP-NVUE */
	// 	display: flex;
	// 	/* #endif */
	// 	flex-direction: row;
	// 	width: 750rpx;
	// 	height: $nav-height;
	// 	line-height: $nav-height;
	// 	font-size: 16px;
	// 	// background-color: #ffffff;
	// }

	// .uni-navbar__header-btns {
	// 	/* #ifndef APP-NVUE */
	// 	display: flex;
	// 	/* #endif */
	// 	flex-wrap: nowrap;
	// 	width: 120rpx;
	// 	padding: 0 6px;
	// 	justify-content: center;
	// 	align-items: center;
	// }

	// .uni-navbar__header-btns-left {

	// 	/* #ifndef APP-NVUE */
	// 	display: flex;
	// 	/* #endif */
	// 	width: 150rpx;
	// 	justify-content: flex-start;
	// }

	// .uni-navbar__header-btns-right {
	// 	/* #ifndef APP-NVUE */
	// 	display: flex;
	// 	/* #endif */
	// 	width: 150rpx;
	// 	padding-right: 30rpx;
	// 	justify-content: flex-end;
	// }

	// .uni-navbar__header-container {
	// 	flex: 1;
	// }

	// .uni-navbar__header-container-inner {
	// 	/* #ifndef APP-NVUE */
	// 	display: flex;
	// 	/* #endif */
	// 	flex: 1;
	// 	align-items: center;
	// 	justify-content: center;
	// 	font-size: $uni-font-size-base;
	// 	margin-top: -40rpx;
	// }


	// .uni-navbar__placeholder-view {
	// 	height: $nav-height;
	// }

	// .uni-navbar--fixed {
	// 	position: fixed;
	// 	z-index: 998;
	// }

	// .uni-navbar--shadow {
	// 	/* #ifndef APP-NVUE */
	// 	box-shadow: 0 1px 6px #ccc;
	// 	/* #endif */
	// }

	// .uni-navbar--border {
	// 	border-bottom-width: 1rpx;
	// 	border-bottom-style: solid;
	// 	border-bottom-color: $uni-border-color;
	// }
</style>
