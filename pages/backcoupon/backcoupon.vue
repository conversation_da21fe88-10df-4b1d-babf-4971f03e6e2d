<template>
	<view>
		<view v-if="list">
			<cu-custom :bgColor="list.couponStyle[1]" v-if="list.couponStyle[1]" :isBack="true"
				:hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">个人中心</block>
			</cu-custom>
		</view>

		<!-- 第一部分 -->
		<view v-if="list.couponStyle[1]&&list.couponInfos&&list.couponInfos.length"
			:style="{'width': '100%','height': '390rpx','background':list.couponStyle[1],'display': 'flex','flex-direction': 'column'}">
			<!-- 联名图片logo -->
			<view class="Joint_logo">
				<image src="https://slshop-file.oss-cn-beijing.aliyuncs.com/upload/2023/logo%20(1).png"
					mode="aspectFit">
				</image>
			</view>

			<!-- 劵包名称 -->
			<image v-if="list.couponInfos&&list.couponInfos.length" class="Coupon_name" :src="list.couponStyle[0]">
			</image>
		</view>
		<!-- 展示劵 -->
		<view class="Coupon_list" v-if="list.couponInfos&&list.couponInfos.length">
			<view class="Coupon_every" v-for="(item,index) in list.couponInfos" :key='index'>
				<view class="Coupon_er_name">
					{{item.name}}
				</view>

				<view :style="{'font-size':'88rpx',
												'height': '100rpx',
												'display': 'flex',
												'align-items': 'center',
												'justify-content': 'center',
												'font-weight': '900',
												'text-align': 'center',
												'width': '100%',
												'color':list.couponStyle[1]}">
					<text class="text-price text-xl" style="margin-bottom: 41rpx;"></text>
					<text class="text-bold" style="font-size: 50rpx;" v-if="item.type=='2'">{{item.discount}}折</text>
					<text class="number text-bold" v-else>{{item.reduceAmount}}</text>
				</view>
				<view class="Coupon_pirce" style="text-align: center;width: 100%;">
					满{{item.premiseAmount}}可用
				</view>

				<view class="Pick_up_now" @click="couponUserSave(item)">
					立即领取
				</view>
			</view>
		</view>
		<!-- 第二部分 -->
		<view style="margin-top: 18rpx;" v-if="newData.goodsList&&newData.goodsList.length>0">
			<goods-waterfall-recommend-mixed :newData="newData" :goodsSpace="2" :newDataSize="size"
				:showimage="false" />
		</view>
		<!-- 第三部分 -->
		<view class="Module_Points" v-if="list.pointGoodsSpu&&list.pointGoodsSpu.length">
			<image class="images"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/upload/2023/%E5%9C%86%E8%A7%92%E7%9F%A9%E5%BD%A2%2012%20(1).png"
				mode="aspectFit"></image>
			<view style="position: absolute;top: 24rpx;right: 70rpx;font-size: 32rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #000000;">
				积分账户余额 : <text style="font-size: 32rpx;
											
											font-weight: bold;margin-left: 10rpx;
											color: #000000;">{{list.pointBalance}}</text>
			</view>
			<view class="cu-list shop-list" style="background-color: white">
				<view class="cu-item shop-item" v-for="(item, index) in list.pointGoodsSpu" :key="index">
					<navigator class="content-ex" hover-class="none"
						:url="'/pages/goods/goods-detail/index?id=' + item.id">
						<image mode="aspectFit" class="shop-img" :src="item.picUrls[0]"></image>
					</navigator>
					<!-- 		
				    <view class="content">
				      <view class="text-sm content-text">{{ item.name }}</view>
				      <view class="flex justify-around align-center">
				        <view class="text-gray text-xs" style="text-decoration: line-through;" v-show="item.priceOriginal">零售价￥{{ item.priceOriginal }}</view>
				        <view>
							<text class="text-xs text-gray">积分：</text>
							<text class="text-lg" style="color: #cda185;">
							  {{ item.goodsPoints ? item.goodsPoints : '' }}
							</text>
						</view>
				      </view> -->

					<!--   <view class="content-box" style="margin-top: 20rpx;">
				      
				          兑换
				        </navigator>
				      </view> -->
				</view>
			</view>
			<view class="point_btn_img">
				<image @click="$noMultipleClicks(JumpPoints)"
					src="https://slshop-file.oss-cn-beijing.aliyuncs.com/upload/2023/%E6%9F%A5%E7%9C%8B%E6%9B%B4%E5%A4%9A%E5%85%91%E6%8D%A2%E5%95%86%E5%93%81.png"
					style="width: 500rpx;height: 100rpx;padding-bottom: 20rpx;"></image>
			</view>
		</view>
		<!-- 第四部分 -->
		<view style="margin-top: 24rpx;" v-if="pageDivData.goodsList&&pageDivData.goodsList.length>0">
			<goods-waterfall-recommend-mixed :newData="pageDivData" :goodsSpace="4" :newDataSize="3"
				:showimage="false" />
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
	</view>
</template>

<script>
	import goodsWaterfallRecommendMixed from "components/goods-waterfall-recommend-mixed/index";
	const app = getApp();
	import api from 'utils/api'
	export default {
		components: {
			goodsWaterfallRecommendMixed
		},
		data() {
			return {
				pointsRecord: [],
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				newData: {},
				list: "",
				pageDivData: {
					goodsList: []
				},
				//获取店铺商品的分页查询
				page: {
					searchCount: false,
					current: 0,
					size: 20,
					ascs: '',
					//升序字段
					descs: ''
				},
				loadmore: true,
			}
		},
		onPageScroll(res) {
			uni.$emit('vonVideoPageScroll', res);
		},
		methods: {

			//跳转积分页面
			JumpPoints() {
				uni.navigateTo({
					url: '/pages/user/user-integral-shop/index'
				})
			},
			onReachBottom() {
				this.getRecommendMixedList();
			},

			couponUserSave(item) {
				api.couponUserSave({
					couponId: item.id
				}).then(res => {
					uni.showToast({
						title: '领取成功',
						icon: 'success',
						duration: 2000
					});
				});
			},
			async getRecommendMixedList() {
				console.log('推荐混排-获取数据')
				try {
					this.page.current++;
					const params = {
						current: this.page.current,
						size: this.page.size,
						businessType: 'all'
					}
					const res = await api.getRecommendMixedList(params)
					const data = res.data?.records || []
					//记录第一页数据长度
					if (res.data && res.data.current == "1") {
						this.recordCurrentPage = data.length
					}
					// console.log(res, data,data.length, '========推荐混排======')
					if (data.length) {
						// console.log(this.pageDivData, '-==============')
						this.pageDivData.goodsList = this.pageDivData.goodsList.concat(data);
						// console.log(this.pageDivData, 'this.pageDivData')
						if (this.pageDivData.goodsList.length === res.data.total) {
							this.loadmore = false;
						}
					}
				} catch (e) {
					console.error(e)
				}
			},
		},
		created() {
			api.backflow().then(res => {
				if (res.data) {
					this.list = res.data
				}
				api.themeMobileGet().then(res => {
					// api.pagedevisePage(res.data.tabbarItem.info[0].pagePathId).then(res => {
					// this.newData = res.data.pageComponent.componentsList[20].data;
					this.newData.goodsList = this.list?.productSuggestVos,
						// this.pageDivData = res.data.pageComponent.componentsList[20].data;
						// conso
						this.getRecommendMixedList();
					// })
				})
			})
		}

	}
</script>

<style lang="scss" scoped>
	.Joint_logo {
		display: flex;
		display: flex;
		justify-content: center;
		height: 80rpx;
		margin-top: 10rpx;

		image {
			height: 80rpx;
			width: 466rpx;
		}
	}

	.Coupon_name {
		height: 60rpx;
		width: 500rpx;
		// position: absolute;
		// top: 110rpx;
		// left: 123rpx;
		z-index: 1;
		margin-left: 134rpx;
		margin-top: 28rpx;

	}



	.Coupon_list {
		margin-top: -241rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		display: flex;
		// justify-content: space-around;
		// left: 3%;X
		height: 296rpx;
		// position: absolute;
		// top: 140rpx;
		border-radius: 40rpx;
		background: white;
		// padding-top: 29px;
		padding-bottom: 40rpx;
		min-width: 238rpx;
		box-sizing: border-box;
		padding-left: 10rpx;
		padding-right: 10rpx;
		padding-top: 60rpx;

		// width: 100%;S
		.Coupon_every {
			width: 230rpx;
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
			height: 100%;
			box-sizing: border-box;

			.Coupon_er_name {
				font-size: 30rpx;
				
				font-weight: 600;
				color: #000000;
				width: 100%;
				text-align: center;
			}

		}


		.Coupon_pirce {
			font-size: 22rpx;
			
			font-weight: 400;
			color: #000000;
			margin-top: -16rpx;
			margin-bottom: 10rpx
		}
	}

	.Pick_up_now {
		width: 190rpx;
		height: 50rpx;
		border: 1px solid #888888;
		border-radius: 25px;
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: 600;
		color: #000000;
	}

	.shop-list {
		display: flex;
		flex-wrap: wrap;
		margin-top: -120rpx;

		.shop-item {
			// background-color: #fff;
			border-radius: 30rpx;
			margin: 5rpx;
			margin-bottom: 15rpx;

			.content-box {
				margin-bottom: 24rpx;

				.content-ex {
					width: 216rpx;
					height: 50rpx;
					background: linear-gradient(90deg, #fc0932 0%, #fc5560 100%);
					border-radius: 25rpx;
					text-align: center;
					font-size: 24rpx;
					color: #f7f7f7;
					line-height: 50rpx;
					margin: 0 auto;
				}
			}

			.content-text {
				width: 315rpx;
				height: 50rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				font-size: 28rpx;
				font-weight: 500;
				margin: 0 18rpx 18rpx;
				border-bottom: 1rpx dashed #eee;
				color: #000000;
			}

			.content-Price {
				color: #666666;
				font-size: 20rpx;
				height: 48rpx;
				padding-left: 18rpx;
				margin-right: 50rpx;
				text-decoration: line-through;
			}

			.shop-img {
				width: 356rpx;
				height: 356rpx;
				border-radius: 10px;
			}
		}
	}

	.text-size {
		font-size: 46rpx;

		.text-position {
			font-size: 22rpx;
			margin-left: 10rpx;
			position: relative;
			top: -6rpx;
		}
	}

	.Module_Points {
		margin-left: 12rpx;
		margin-right: 10rpx;
		position: relative;
		background: white;
		border-radius: 40rpx;

		.images {
			width: 827rpx;
			margin-left: -15rpx;
			height: 178rpx;
		}
	}

	.point_btn_img {
		display: flex;
		justify-content: center;
	}

	.Coupon_list>view:nth-child(1) {
		border-right: 1rpx dashed;
		color: #999999;
	}

	.Coupon_list>view:nth-child(2) {
		border-right: 1rpx dashed;
		color: #999999;
	}
</style>
