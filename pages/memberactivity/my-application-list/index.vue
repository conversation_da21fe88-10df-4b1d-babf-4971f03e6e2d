<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">我的活动</block>
		</cu-custom>
		<view class="navTab" id="navTab">
			<scroll-view :scroll-x="true" style="white-space: nowrap;">
				<view class="tabBox">
					<view class="tabList" v-for="(item,index) in tablist" :key="index">
						<view :class="[index == tabindex ? 'activetab' : 'list']" @click="tabclick(item.id, index)">{{ item.name }}</view>
					</view>
				</view>
			</scroll-view>
		</view>
			
		<scroll-view scroll-y :style="{height: `calc(100vh - ${CustomBar}px - ${navHeight}px)`}" @scrolltolower="getMore">
			<view
        class="cu-list menu-avatar margin-sm radius-lg"
        v-for="(item, index) in goodsList"
        :key="index"
      >
        <view 
					@click="toPage(`/pages/memberactivity/application-detail/index?id=${item.id}`)"
					class="flex padding bg-white align-center solid-bottom justify-between">
          <view 
            class="cu-avatar"
            style="width: 256rpx;height: 160rpx;"
            :style="'background-image:url(' + (item.memberActivityInfo.activityPicUrl ? item.memberActivityInfo.activityPicUrl : 'https://img.songlei.com/live/img/no_pic.png') + ');'"
          >
          </view>

          <view class="margin-left-sm text-right">
            <view class="text-black text-df overflow-1">
              {{item.memberActivityInfo.name}}
            </view>

            <view class="text-gray text-sm text-cut margin-top-xs">
              {{item.memberActivitySessions}}
            </view>

						<view v-if="item.status=='5'" class="text-gray text-sm text-cut margin-top-xs">
              报名人数：/人
            </view>
            <view v-else class="text-gray text-sm text-cut margin-top-xs">
              报名人数：{{item.peopleNum}}人
            </view>

            <view class="text-gray text-sm text-cut margin-top-xs">
              报名费用：
							<text class="text-red" v-if="item.status=='5'&& item.hasRefundPoints>0">{{ item.hasRefundPoints }}积分</text> 
							<text class="text-red" v-else-if="item.paymentPoints">{{ item.paymentPoints }}积分</text> 
							<text class="text-red" v-if="item.status=='5'&& item.hasRefundPrice>0">{{ item.hasRefundPrice }}元</text> 
							<text class="text-red" v-else-if="item.paymentPrice">{{ item.paymentPrice }}元</text> 
							<text class="text-red" v-if="item.memberActivityInfo.registrationMethod == '1'">免费报名</text> 
            </view>
          </view>
        </view>

        <view class="cu-form-group">
					<veiw 
						style="
							width: 135rpx;
							height: 45rpx;
							line-height: 45rpx;
							text-align: center;
							color: #FFFFFF;
							background-size: cover;
							background-position: center;
							font-size: 22rpx !important;
							"
						:style="'background-image:url(' + activityType(item.status).url + ')'"
						>
						{{activityType(item.status).name}}
					</veiw>
					<view 
						v-if="item.status=='0'"
						@click="cancelOrder(item.id)"
						style="width: 145rpx;height: 45rpx;color: #000000;background-color:#FFFFFF;border:2rpx solid #000000;"
						class="cu-btn round text-white text-xs"
						> 
						{{'取消报名'}}
					</view>
					<!-- 倒计时 -->
          <count-down v-if="item.status=='0'&&item.outTime&&item.outTime>=0"
            class="bg-white round padding-xs text-xs" :textColor="'red'" :connectorColor="'red'" :backgroundColor="'#FFFFFF'"
            :outTime="item.outTime*1000" @countDownDone="countDownDone">
          </count-down>
					<view 
						v-if="item.status=='0'"
						@click="showPay(item)"
						style="width: 145rpx;height: 45rpx;color: red;background-color:#FFFFFF;border:2rpx solid red;"
						class="cu-btn round text-white text-xs"
						> 
							立即支付
					</view>
					<view 
						v-if="item.status=='2'"
						@click="goAppraise(item.isAppraises,item)"
						style="width: 145rpx;height: 45rpx;color: #FFFFFF;background-color: rgba(248, 127, 58, 1)"
						class="cu-btn round text-white text-xs"
						> 
							{{item.isAppraises?'我的评价':'去评价'}}
					</view>
        </view>
      </view>

			<view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
		</scroll-view>
		<!-- 支付 -->
    <view class="toast-pay" v-if="toastHide">
			<pay-components ref="pay" :pageTitle="pageTitle" :callBack="true" @success="successBack"
				@fail="failBack"></pay-components>
			<view @click="$noMultipleClicks(payOrder)" class="one-btn">确定</view>
		</view>
		<view class="ZZC" @click="toastClose" v-if="toastHide"></view>
	</view>
</template>
<script>
	const app = getApp();

  import { myAppraisesPage,cancel } from '@/pages/memberactivity/api/memberactivity';
	import countDown from "@/components/count-down/index";
	import { navigateUtil } from "@/static/mixins/navigateUtil.js";

	import { mapState } from 'vuex'
	export default {
		mixins: [ navigateUtil ],
		computed: {
			...mapState([ 'CustomBar' ])
		},

		components: {
    countDown
  },

		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				noClick: true,
				navHeight: 0,
				page: {
					current: 1,
					size: 10,
				},
				loadmore: true,
				goodsList: [],
				tablist:[
          {name: '全部', id: ''},
          {name: '已报名', id: '1'},
          {name: '已参加', id: '2'},
          {name: '已结束', id: '3'},
          {name: '已取消', id: '4'},
        ], // TAB分类列表
				tabindex: 0,
				pageTitle:'会员活动',
      	toastHide: false, //支付选择弹框
				payInfo:{},
			};
		},

		async onLoad() {
			let query = uni.createSelectorQuery()
			query.select('#navTab').boundingClientRect() 
			await query.exec((res) => {
				this.navHeight = res[0].height
			})
		},

		async onShow() {
			app.initPage().then(async res => {
				this.goodsList = []
				this.goodsPage(); //就第一次掉店铺信息传入，后续就没有店铺信息了
			});
		},

		methods: {
			 // 评价
			goAppraise(bool,i) {
				// 去评价
				if(bool){
					uni.navigateTo({
						url: '/pages/memberactivity/appraises/list/index?type=2&activityId=' +i.activityId
					});
				}else{
					// 查看评价
					uni.navigateTo({
						url: '/pages/memberactivity/appraises/form/index?id=' +i.id
					});
				}
			},

			// 立即支付pay
			showPay(i){
				this.payInfo = i;
				this.toastHide = true
			},

			// 关闭支付
			toastClose() {
					this.toastHide = false;
			},

			// 支付
			payOrder() {
				uni.showLoading({
					title: '支付中',
					mask: true
				});

				const params = {
					... this.payInfo
				}
				console.log("memberactivity===>",params);
				
				this.$refs.pay?.payOrder(params, false, 'memberactivity')
			},

			// 支付失败的回调
			failBack() {
				uni.showToast({
					title: '支付失败',
					icon: 'none'
				})
				this.toastHide = false;
				this.refresh();
			},

		// 支付成功的回调
			successBack() {
				const that = this;
				uni.showModal({
						title: '提示',
						content: '支付成功',
						icon: 'none',
						cancelText: '再想想',
						confirmText: '去使用',
						success(res) {
								if (res.confirm) {
										uni.navigateTo({
												url: '/pages/memberactivity/application-detail/index?id='+that.payInfo.id
										});
								} else if (res.cancel) {
									that.toastClose();
								}
						}
				})
			},

			// 取消订单
			cancelOrder(id){
				uni.showModal({
						title:'温馨提示',
						content: '是否确认取消报名？',
						cancelText: '否',
						confirmText:'是',
						cancelColor:'#eeeeee',
						confirmColor:'#000000',
						success (res) {
							if (res.confirm) {
								cancel(id).then(res => {
									console.log('cancelOrder===>',res);
									if (res.ok) {
										uni.showModal({
											title:'温馨提示',
											content: '本次活动报名取消成功',
											showCancel: false,
											success (res) {
												if (res.confirm) {
													this.refresh();
												}
											}
										});
									}
								});
							}
						}
					});
			},

			// 倒计时
			countDownDone() {
				this.refresh();
			},

			// 活动状态
			activityType(key) {
        const statusMap = {
            '0': { name: '待付款', url: 'https://img.songlei.com/live/bg-red.png'},
            '1': { name: '已报名', url: 'https://img.songlei.com/live/bg-orange.png' },
            '2': { name: '已参加', url: 'https://img.songlei.com/live/bg-green.png' },
            '3': { name: '已结束', url: 'https://img.songlei.com/live/bg-orange.png' },
            '4': { name: '已取消', url: 'https://img.songlei.com/live/bg-grey.png' },
            '5': { name: '已取消', url: 'https://img.songlei.com/live/bg-grey.png' },
        };
        const activityStatus = statusMap[key];
          if (activityStatus) {
              return { name: activityStatus.name, url: activityStatus.url };
          } else {
              return { name: '未知状态', disabled: false };
          }
      },

			// tab切换
			tabclick(id, index){
				this.tabindex  = index;
        if (id) {
          this.page.status = id;
        }else{
          delete this.page.status
        }
				this.goodsList = [];
				this.loadmore = true;
				this.page.current = 1;
				this.goodsPage()
			},

			// 滚动到底部
			getMore() {
				if (this.loadmore) {
					this.page.current = this.page.current + 1;
					this.goodsPage();
				}
			},

			//搜索列表
			goodsPage() {
        myAppraisesPage(this.page).then(res => {
          let data = res.data.records
          this.goodsList = [...this.goodsList, ...data];
          if (this.goodsList.length >= res.data.total) {
            this.loadmore = false;
          }
        }).catch((err)=> {
            console.log("err",err);
            this.loadmore = false;
        })
			},

			refresh () {
				this.loadmore = true;
				this.goodsList = [];
				this.page.current = 1;
				this.goodsPage();
			},
		}
	};
</script>
<style scoped lang="scss">
.topfixed-active {
	width: 100%;
	position: fixed;
	top: 0rpx;
	left: 0rpx;
	background: #fff;
	z-index: 9999;
	box-sizing: border-box;
}

.cuIcon-triangledownfill {
	margin-top: -22rpx;
}
.navTab {
	background: white; 
	margin: 0 auto;
	.tabBox {
		width: 100%;
		display: flex;
    justify-content: center;
	}
	
	.tabList {
		margin: 10rpx;
		.list {
			display: inline-block;
			padding: 20rpx;
		}
		.activetab{
			color: red;
			padding: 20rpx;
			display: inline-block;
		}
	}
	
	.tabList:first-child {
		margin-left: 0;
	}
}

.one-btn {
		width: 570upx;
		margin: 50upx auto 30upx;
		text-align: center;
		line-height: 90upx;
		background: linear-gradient(-90deg, #FFBF00 0%, #FFDD00 100%);
		border-radius: 45upx;
	}

.toast-pay {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100vw;
		background-color: #fff;
		padding: 30upx 0 20upx;
		border-radius: 20upx 20upx 0 0;
		z-index: 10;
	}

	.ZZC {
		position: fixed;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, .5);
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 2;
	}
</style>
