<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">会员活动</block>
    </cu-custom>
      
      <!-- 商品信息 -->
      <view :style="{marginBottom:'80rpx'}">
        <view class="margin-top margin-sm" v-for="(item, index) in myGiftCardListPages" :key="index">
          <view style="position: relative;">
            <view 
              style="height:420rpx;"
              @click="toPage(`/pages/memberactivity/detail/index?id=${item.id}`)">
              <image style="width:100%;height: 100%;"
                :src="item.activityPicUrl | formatImg360" mode="aspectFill" >
              </image>
            </view>

            <view class="flex justify-around padding-xs margin-top-xs"
                style="height:100%;background: #FFFFFF;margin:0 auto;">
                <view class="padding-lr-sm" style="flex:1;position: relative;">
                  <view class="flex justify-start align-center">
                    <view class="text-black overflow-2" style="width:350rpx;">
                      {{item.name}}
                    </view>
                  </view>

                  <view class="flex justify-between align-end">
                    <view class="text-light-gray text-xs">
                        {{item.activityBeginTime}}-{{item.activityEndTime}}
                    </view>
                    <view class="flex" v-if="item.apiStatus == '2'">
                      <!--头像组件 urls数组 -->
                      <a-avatar-slideshow ref="avatar" :urls="item.headimgList" width="40" height="40" :overlap="10" :interval="interval" :maxDisplayCount="5"> </a-avatar-slideshow>
                      <view class="text-light-gray text-xs margin-top-xs" v-if="item.activityResidualNum>0">
                        余<text style="color: #F87F3A;">{{item.activityResidualNum || 0}}</text>人
                      </view>
                      <view class="text-light-gray text-xs" v-else>
                        已报满
                      </view>
                    </view>

                    <view 
                      v-if="item.apiStatus == '1'&&item.beginSecond>0" 
                      class="text-sm margin-left-xs padding-bottom-xs" 
                      style="color: #FF0000;">
                      距离开始:
                      <!-- 倒计时 -->
                      <count-down v-if="item.beginSecond>=0"
                        class="bg-white round padding-xs text-xs" :textColor="'red'" :backgroundColor="'#FFFFFF'"
                        :outTime="item.beginSecond*1000" @countDownDone="countDownDone">
                      </count-down>
                    </view>
                  </view>
    
                      
                  <view class="text-xs" style="position:absolute;top: 0px;right: 0px;">
                    <text class="text-xs margin-right-sm"
                      style="width:350rpx;color: #F87F3A;">
                      {{item.apiStatus | statusType}}
                    </text>
                    <view 
                      @click="toPage(`/pages/memberactivity/detail/index?id=${item.id}`)"
                      style="width: 145rpx;height: 35rpx;;color: #F87F3A;background-color: rgba(240, 183, 93, 0.349019607843137);"
                      class="cu-btn round text-white text-xs"> 
                        查看详情
                    </view>
                  </view>
    
                  <view 
                    @click="toPage(`/pages/memberactivity/appraises/list/index?activityId=${item.id}&type=1`)"
                    v-if="item.apiStatus=='2'||item.apiStatus=='3'" 
                    class="flex align-center text-xs">
                    <view class="text-purple-grey flex justify-between margin-top-xs text-xs">
                      <view class="text-black">活动评价({{item.activityAppraisesNum || 0}})</view>
                    </view>
                    <view 
                      class="text-xs margin-left-sm margin-top-xs">
                        查看全部<text class="cuIcon-right margin-left-xs"></text>
                    </view>
                  </view>
                </view>
            </view>
          </view>
        </view>

        <view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
      </view>
  </view>
</template>

<script>

const app = getApp();
import { memberactivityinfo} from '@/pages/memberactivity/api/memberactivity'
import aAvatarSlideshow from "../components/a-avatar-slideshow/a-avatar-slideshow.vue";
import {mapState} from 'vuex'
import { navigateUtil } from "@/static/mixins/navigateUtil.js";
import countDown from "@/components/count-down/index";

export default {
  mixins: [navigateUtil],
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      page: {
        apiType: '1',
        activityTime:'',
        pageNo: 1,
        isShow: 1,
        pageSize: 10,
      },
      loadmore: true,
      //有数统计使用
      page_title: '会员活动',
      interval: 2000,
      priceStyle: "display: flex; justify-content: center; font-weight: bold; align-items: baseline;",
      priceStyle2: "display: flex; justify-content: center; align-items: baseline;",
      myGiftCardListPages:[],//我的礼品卡
      // 测试组件数据
      headimgList: [
					'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
					'https://img.songlei.com/1/material/ec3b8c7b-d812-4d43-adad-aecf3c57343b.jpg-jpg_w360_q90',
          'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
					'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
          'https://img.songlei.com/1/material/3e4d4561-eb84-4da3-b001-1f0afd275e0b.gif-gif_q90_s80',
					'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
          'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
					'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
				],
    };
  },

  components: {
    aAvatarSlideshow,
    countDown
  },
  props: {},

  onLoad (options) {
    if (options) {
      if (options.activityTime&&options.apiType) {
        this.page.activityTime = options.activityTime
        this.page.apiType = options.apiType
        this.refresh()
      }
    }
  },

  onShow () { 
    app.initPage().then(res => {
      // this.refresh()
      this.interval = 2000
    });
  },

  beforeDestroy() {
    console.log("beforeDestroy---->",);
    this.interval = 0
    this.$nextTick(()=>{
      this.$refs.avatar.forEach((item)=>{item.stop()})
    })
  },

  onHide() {
    console.log("onHide---->",);
    this.interval = 0
    this.$nextTick(()=>{
      this.$refs.avatar.forEach((item)=>{item.stop()})
    })
  },

  onReachBottom () {
    if (this.loadmore) {
      this.page.pageNo = this.page.pageNo + 1;
      this.myGiftCardList();
    }
  },

  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框
    uni.hideNavigationBarLoading(); // 停止下拉动作
    uni.stopPullDownRefresh();
  },

  computed: {
    ...mapState([
			'windowWidth',
			'HeightBar',
			'CustomBar',
			'menuWidth',
			'leftMenuWidth',
			'pixelRatio'
		])
  },

  filters: {
    statusType(key) {
      const statusMap = {
          '1': '未开始',
          '2': '进行中',
          '3': '已结束',
          '4': '已报满',
          '5': '积分不足',
          '6': '已报名',
          '7': '可报名'
      };
        return statusMap[key] || '未知状态';
    }
  },

  methods: {

    // 倒计时
    countDownDone() {
			this.refresh();
		},

    //我的活动列表
    myGiftCardList(){
      memberactivityinfo(Object.assign({},this.page)).then((res)=>{
        console.log("memberactivityinfo==res==>",res);
        if (res.data) {
          if (res.data.records) {
            let myGiftCardListPages=res.data.records
            this.myGiftCardListPages = [...this.myGiftCardListPages, ...myGiftCardListPages];
            if (myGiftCardListPages.length < this.page.pageSize) {
              this.loadmore = false;
            }
          }else{
            this.loadmore = false;
          }
        }
      })
    },

    refresh () {
      this.loadmore = true;
      this.myGiftCardListPages = [];
      this.page.pageNo = 1;
      this.myGiftCardList();
    },
  }
};
</script>
<style  scoped>
.text-color{
  color: #F87F3A
}

.line{
    width: 108rpx;
    height: 4rpx;
    background: #F87F3A;
    border-radius: 2rpx;
    margin: 0px auto;
}
</style>
