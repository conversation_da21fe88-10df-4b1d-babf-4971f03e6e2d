<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">报名详情</block>
    </cu-custom>
    <!--活动信息  -->
    <view 
			class="flex padding bg-white align-center solid-bottom justify-between">
      <view 
        class="cu-avatar"
        style="width: 210rpx;height: 110rpx;"
        :style="'background-image:url(' + (activityDetail.memberActivityInfo.activityPicUrl ? activityDetail.memberActivityInfo.activityPicUrl : 'https://img.songlei.com/live/img/no_pic.png') + ');'"
      >
      </view>

      <view class="margin-left-sm text-right">
        <view class="text-black text-df overflow-1">
          {{activityDetail.memberActivityInfo.name}}
        </view>

        <view class="text-gray text-sm text-cut margin-top-xs">
          {{activityDetail.memberActivityInfo.marketStoreName}}
        </view>

        <view class="text-gray text-sm text-cut margin-top-xs">
          {{activityDetail.memberActivityInfo.sponsor}}
        </view>
      </view>
    </view>
    <!-- 二维码 -->
    <view class="text-center bg-white">
      <image 
        v-if="activityDetail.status!='0'"
        @click="()=>previewQrImg(1)" 
        style="width: 320rpx; height: 320rpx; margin-top: 32rpx" 
        mode="aspectFit" 
        :src="qrImg">
			</image>

			<!-- <view 
        class="text-xdf"
        style="
          color: #999999;
          margin-top: 4rpx;
          margin-bottom: 25rpx;
          text-decoration: underline;
        "
        >
				刷新二维码
			</view> -->

      <view class="flex justify-between align-center padding-lr padding-bottom-lg">
        <view class="text-xsm text-bold">门票信息（{{activityDetail.peopleNum}}张可用）</view>
        <!-- v-if="activityDetail.status=='1'||activityDetail.status=='3'" -->
        <view 
          @click="refund(activityDetail)"
          v-if="(activityDetail.status=='1'||activityDetail.status=='3')&&activityDetail.memberActivityInfo.registrationMethod!='1'"
          style="
            width: 145rpx;
            height: 45rpx;
            color: #FFFFFF;
            background-color: rgba(248, 127, 58, 1)
            "
					class="cu-btn round text-white text-xs"> 
						申请退款
				</view>
      </view>
    </view>
    <!-- 订单信息 -->
    <view class="flex justify-between align-center padding-lr padding-tb-xs bg-white text-smx" style="margin-top: 2rpx;padding-top: 40rpx;">
      <view>订单编码：</view><view>{{activityDetail.id}}</view>
    </view>
    <view class="flex justify-between align-center padding-lr padding-tb-xs bg-white text-smx">
      <view>下单时间：</view><view>{{activityDetail.createTime}}</view>
    </view>
    <view class="flex justify-between align-center padding-lr padding-tb-xs bg-white text-smx">
      <view>购 票 人：</view><view>{{activityDetail.name}}</view>
    </view>
    <view class="flex justify-between align-center padding-lr padding-tb-xs bg-white text-smx">
      <view>手 机 号：</view><view>{{activityDetail.phone}}</view>
    </view>
    <view class="flex justify-between align-center padding-lr padding-tb-xs bg-white text-smx">
      <view>参与场次：</view><view>{{activityDetail.memberActivitySessions}}</view>
    </view>
    <view class="flex justify-between align-center padding-lr padding-tb-xs bg-white text-smx" v-if="activityDetail.memberActivityInfo">
      <view>报名方式：</view><view>{{statusType(activityDetail.memberActivityInfo.registrationMethod)}}</view>
    </view>
    <view v-if="activityDetail.memberActivityInfo&&activityDetail.memberActivityInfo.registrationMethod!='1'" class="flex justify-between align-center padding-lr padding-tb-xs bg-white text-smx">
      <view>使用积分：</view>
      <view v-if="activityDetail.status=='5'">{{activityDetail.hasRefundPoints}}</view>
      <view v-else>{{activityDetail.paymentPoints}}</view>
    </view>
    <view v-if="activityDetail.memberActivityInfo&&activityDetail.memberActivityInfo.registrationMethod!='1'" class="flex justify-between align-center padding-lr padding-tb-xs bg-white text-smx">
      <view>付款金额：</view>
      <view v-if="activityDetail.status=='5'">￥{{activityDetail.hasRefundPrice || '0'}}</view>
      <view v-else>￥{{activityDetail.paymentPrice || '0'}}</view>
    </view>
    <view class="flex justify-between align-center padding-lr padding-tb-xs bg-white text-smx" style="margin-bottom: 2rpx;padding-bottom: 40rpx;">
      <view>门票数量：</view><view>{{activityDetail.usableNum}}</view>
    </view>
    <!-- 温馨提示 -->
    <view class="bg-white padding" v-if="activityDetail.memberActivityInfo.activityDetails">
      <view class="text-bold">温馨提示：</view>
      <jyf-parser :html="activityDetail.memberActivityInfo.activityDetails"></jyf-parser>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { memberactivityorder } from '@/pages/memberactivity/api/memberactivity'
const QR = require("utils/wxqrcode.js");
import { navigateUtil } from "@/static/mixins/navigateUtil.js";
import { getByType } from "@/api/base.js"

export default {
  mixins: [navigateUtil],

  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      activityDetail:{},//活动详情信息
      codeImgArray:[],
      qrImg: null,
      loadingImg: 'https://img.songlei.com/-1/material/a3332b63-c7a4-4c3a-99c1-dd46cb6ac133.png',
      getByTypeArr:[],
      id:'',
    };
  },

  components: {
  },
  props: {},

  onLoad (options) {
    if (options&&options.id) {
      this.id = options.id
    }
  },

  onShow () { 
    // 页面显示时记录进入时间（单位：毫秒）
    this.enterTime = new Date().getTime();
    console.log('组件 mounted，进入时间enterTime:', this.enterTime);
    app.initPage().then(res => {
      this.getByType()
      if(this.id){
        this.getDetail(this.id);
      }
    });
  },

  methods: {
    getByTypeFilter(typeName, code) {
			let name = null;
        this.getByTypeArr.forEach(item => {
            if (item.typeName == typeName) {
                item.records.forEach(record => {
                    if (record.code == code) {
                        name = record.name;
                    }
                });
            }
        });
        console.log("getByType",typeName, code,this.getByTypeArr);
        return name;
		},

    // 获取门店
    async getByType() {
			await getByType().then(res => {
				this.getByTypeArr = res.data;
			})
		},

    // 退款
    refund(i){
      uni.setStorageSync('refundInfo',i)
      uni.navigateTo({url: `/pages/memberactivity/refund/index`})
    },

    // 活动状态
    statusType(key) {
      const statusMap = {
          '1': '免费报名',
          '2': '积分报名',
          '3': '付费报名',
          '4': '积分付费报名',
      };
        return statusMap[key] || '未知状态';
    },

    // 预览图片
    previewQrImg(index){
		  uni.previewImage({
		  	current: index,
			  urls: this.codeImgArray
		  })
		},

    //活动列表
    getDetail(id){
      memberactivityorder(id).then((res)=>{
        if (res.data) {
          console.log("res.data====",res.data);
          this.activityDetail = res.data
          let marketStoreName = this.getByTypeFilter("appmkt", this.activityDetail.memberActivityInfo.marketStore)
          this.$set(this.activityDetail.memberActivityInfo, 'marketStoreName', marketStoreName);
          const statusMap = {
              '1': QR.createQrCodeImg(`/pages/mall/memberactivityinfo/off?id=${res.data.id}`, {size: parseInt(300)}),//二维码大小
              '2': 'https://img.songlei.com/live/icon_2.png',
              '3': 'https://img.songlei.com/live/icon_3.png',
              '4': 'https://img.songlei.com/live/icon_4.png',
              '5': 'https://img.songlei.com/live/icon_4.png',
          };
          this.qrImg = statusMap[res.data.status]
          // this.qrImg = QR.createQrCodeImg(res.data.id, {
					// 		size: parseInt(300) //二维码大小  
					// 	});
          this.codeImgArray.push(this.qrImg);
          
        }
      })
    },
  }
};
</script>
<style  scoped>
</style>
