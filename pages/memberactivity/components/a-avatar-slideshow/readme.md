# a-avatars
![](./头像无线滚动.gif)

## 用法
```html 
<template>
	<view>
	<!-- urls数组 -->
		<a-avatar-slideshow :urls="urls"> </a-avatar-slideshow>
		<!-- urls对象 -->
		<a-avatar-slideshow :urls="urlsObj"> </a-avatar-slideshow>
		<!-- 设置宽高 -->
		<a-avatar-slideshow :urls="urlsObj" width="100" height="100"></a-avatar-slideshow>
		<!-- overlap头像重叠部分大小 -->
		<a-avatar-slideshow :urls="urlsObj" width="100" height="100" :overlap="50"></a-avatar-slideshow>
		<!-- interval轮播时间间隔 -->
		<a-avatar-slideshow :urls="urlsObj" width="100" height="100" :interval="1000"></a-avatar-slideshow>
		<a-avatar-slideshow :urls="urlsObjProp" width="100" height="100" :interval="1000" :prop="{url: 'header'}"></a-avatar-slideshow>
	</view>
</template>

<script>
	export default {
		data () {
			return {
				urlsObj: [
					{ id: '11', url: 'https://p.qqan.com/up/2023-7/202371914533633.jpg'},
					{ id: '212', url: 'https://p.qqan.com/up/2023-7/202371914533633.jpg'},
					{ id: '112', url: 'https://img.huimin111.com/uploads/img/20210609/3138bb99e34be076556ae950206ab206.jpg'},
				],
				urlsObjProp: [
					{ id: '11', header: 'https://p.qqan.com/up/2023-7/202371914533633.jpg'},
					{ id: '212', header: 'https://p.qqan.com/up/2023-7/202371914533633.jpg'},
					{ id: '112', header: 'https://img.huimin111.com/uploads/img/20210609/3138bb99e34be076556ae950206ab206.jpg'},
				],
				urls: [
					'https://img.huimin111.com/uploads/img/20210609/3138bb99e34be076556ae950206ab206.jpg',
					'https://p.qqan.com/up/2023-7/202371914533633.jpg',
					'https://p.qqan.com/up/2023-7/202371914533633.jpg',
					'https://p.qqan.com/up/2023-7/202371914533633.jpg'
				]
			}
		}
	}
</script>
```

## 属性说明：

|  属性名  |  类型  |  默认值  |  说明  |
| ---- | ---- | ---- | ---- |
|  urls  |  Array  |  []  |  图片数组  |
|  width  |  Number |  50 |   |
|  height  |  Number |  50 |   |
|  overlap  |  Number |  50 | 头像重叠部分大小  |
|  interval  |  Number |  50 | 轮播时间间隔  |
|  prop  |  Object |  {url: 'url'} | urls图片数组中，图片url对应属性的key  |
