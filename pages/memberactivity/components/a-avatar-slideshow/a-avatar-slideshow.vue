<template>
	<div class="relative" :style="[boxBaseSyle()]">
		<img
		v-for="(avatar, index) in displayList"
		:key="avatar.id"
		:src="avatar.url"
		class="absolute br-50_"
		:style="[itemBaseSyle(),itemSyle(index, avatar)]">
		</img>
	</div>
</template>

<script>

export default {
	props: {
		interval: {
			type: Number,
			default: 1500,
			desc: '轮播间隔时间'
		},
		urls: {
			type: Array,
			default: ()=> { return []}
		},
		width: {
			type: Number,
			default: 50
		},
		height: {
			type: Number,
			default: 50
		},
		overlap: {
			type: Number,
			default: 20,
			desc: '重叠的部分'
		},
		prop: {
			type: Object,
			default: () => ({url: 'url'}),
			desc: 'urls数组中，图片url对应属性的key'
		},
		maxDisplayCount: {
        type: Number,
        default: 3, // 默认展示3张图片
        desc: '控制展示的图片数量'
    },
	},
	data() {
		return {
			list: [],
			uid: 0,
			intervalTimer: null,
			displayList: [] // 新增displayList变量
		};
	},
	computed: {
		marginLeft() {
			return this.width - this.overlap
		}
	},
	watch:{
		urls:{
			handler (newVal, oldVal){
				// TODO 新数据到来，一个一个的替换，
				if(newVal.length) {
					this.list = newVal.map((item,index)=> {
						let url = item && item[this.prop.url]
						if(typeof item === 'string'){
							url = item
						}

						return {url,order: index + 1, id: `${this._uid}-avatar-${index}` }
					})
					this.stop();
					if(this.interval>0){
						this.start();
					}
					// 更新displayList，根据实际数据长度和maxDisplayCount进行处理
					const tempList = [];
          const actualLength = Math.min(this.list.length, this.maxDisplayCount);
            for (let i = 0; i < this.maxDisplayCount; i++) {
                tempList.push(this.list[i % actualLength]);
            }
            
            if (actualLength <= this.maxDisplayCount) {
                this.displayList = this.list.slice(0, actualLength);
            } else {
                this.displayList = tempList;
            }
					
				} else {
					this.stop();
				}
			},
			immediate: true
		}
	},
	onUnload() {
		this.stop();
	},
	onHide() {
    this.stop(); // 页面隐藏时停止定时器
  },

	methods: {
		itemSyle(index, item) {
			// 正常状态的展示
			const style = {
				'z-index': item.order,
				transition: '0.25s',
				transform: ` translateX(${(item.order -1) * this.marginLeft}rpx) scale(1)`
			};
			// const total = this.list.length
			const total = this.displayList.length; // 改为displayList的长度

			// 缩小中
			if(item.order == 0){
				style.transform = ` translateX(${(item.order ) * this.marginLeft}rpx) scale(0)`
			}

			// 位移中 移动到末尾
			if(item.order == -1){
				style.transform = ` translateX(${(total-1) * this.marginLeft}rpx) scale(0)`
				style['z-index'] = total
			}

			// 进行缩放和位移状态的改变
			this.nextState(item)

			if(item.order == total){
				style['z-index'] = total
			}
			return style;
		},
		itemBaseSyle() {
			return { width: this.width + 'rpx', height: this.height + 'rpx' };
		},
		boxBaseSyle() {
			const len = this.displayList.length
			// const len = this.list.length
			
			return {
				width: len * this.width -((len - 1) * this.overlap) + 'rpx',
				height: this.height + 'rpx' ,
			};
		},
		start() {
			console.log('==== start 11 :', this.uid);
			this.intervalTimer = setInterval(() => {
				console.log('==== 11 :', this.uid);
				this.forwardMove();
			}, this.interval); // 可以根据需要调整间隔时间
		},
		stop() {
			clearInterval(this.intervalTimer)
		},
		// 进行缩放和位移 状态 的改变 不具体执行缩放
		nextState(item) {
			if(item.order == 0){
				const timer = setTimeout(() => {
					// 300后缩放结束 开始移动到末尾
					item.order = -1
					clearTimeout(timer)
				}, 300);
			} else if(item.order == -1){
				const timer1 = setTimeout(() => {
					// 300后移动到末尾结束 开始正常展示
					item.order = this.displayList.length
					// item.order = this.list.length
					clearTimeout(timer1)
				}, 300);
			}
		},
		forwardMove() {
			this.displayList.forEach((item, index) => {
				if (item.order == 1) {
					const timer = setTimeout(() => {
						item.order = 0
						clearTimeout(timer)
					}, 250);
				}
				item.order--
			});
		}
	}
};
</script>

<style scoped>
.avatar-slider {
	overflow: hidden;
	width: 100%;
}

.relative{
	position: relative;
}
.absolute{
	position: absolute;
}
.br-50_ {
	border-radius: 50%;
}

</style>
