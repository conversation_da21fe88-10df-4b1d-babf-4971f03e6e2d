<template>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">活动评价</block>
    </cu-custom>

    <view class="cu-list menu-avatar comment">
      <view
        class="cu-item"
        v-for="(item, index) in goodsAppraises"
        :key="index"
      >
        <view
          class="cu-avatar round"
          :style="'background-image:url(' + item.headimgUrl + ')'"
        >{{!item.headimgUrl ? '头' : ''}}</view>

        <view class="content margin-top-xs">
          <view class="text-black flex">{{item.nickName ? item.nickName : '匿名'}}
            <view class="text-gray margin-left-sm text-sm">{{item.createTime}}</view>
          </view>

          <base-rade
            :value="item.goodsScore"
            size="lg"
          ></base-rade>

          <view class="text-black text-content text-sm">{{item.content ? item.content : '此用户未填写评价内容'}}</view>

          <view class="grid col-4 grid-square flex-sub">
            <view
              class="bg-img margin-top-sm"
              v-for="(picUrl, index2) in item.picUrls"
              :key="index2"
              @click="previewImage([picUrl])"
            >
              <image
                :src="picUrl"
                mode='aspectFill'
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
  </view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api';
  import {myappraises,goodsAppraisesPage} from '@/pages/memberactivity/api/memberactivity';
	import baseRade from "components/base-rade/index";
import { type } from 'os';

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'goods_score'
				},
				parameter: {},
				loadmore: true,
				goodsAppraises: [],
				userInfo: uni.getStorageSync('user_info'),
				//有数统计使用
				page_title:'评价列表',
        type:null
			};
		},

  components: {
    baseRade
  },
  props: {},

  onLoad (options) {
    // type 1 是活动评价 2 是我的活动评价
    if (options.activityId && options.type) {
      this.parameter.activityId = options.activityId;
      this.type = options.type;
      if (options.type=='1') {
        this.appraisesPage();
      }
      if (options.type=='2') {
        this.myappraisesPage();
      }
    }
  },

  onShow () {
    app.initPage().then(res => {
      // this.goodsAppraises = []
      // this.page.current = 1;
      // this.loadmore = true;
    });
  },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      if (this.type&&this.type=='1') {
        this.appraisesPage();
      }
    }
  },

  methods: {
    myappraisesPage () {
      let that = this
      myappraises(Object.assign({}, that.page, util.filterForm(that.parameter))).then(res => {
        if (res.data) {
          that.goodsAppraises.push(res.data)
          that.loadmore = false;
        }
      });
    },

    appraisesPage () {
      let that = this
      goodsAppraisesPage(Object.assign({}, that.page, util.filterForm(that.parameter))).then(res => {
        if (res.data) {
          let goodsAppraises = res.data.records;
          this.goodsAppraises = [...this.goodsAppraises, ...goodsAppraises];
            if (goodsAppraises.length < this.page.size) {
              this.loadmore = false;
            }
        }
      });
    },

    previewImage (picUrl) {
      // 预览图片
      uni.previewImage({
        urls: picUrl,
        longPressActions: {
          itemList: picUrl,
          success: function (data) {

          },
          fail: function (err) {
            console.log(err.errMsg);
          }
        }
      });
    },
  }
};
</script>
