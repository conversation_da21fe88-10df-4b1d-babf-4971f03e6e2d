<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">立即报名</block>
    </cu-custom>

    <form @submit="memberSub" >
      <view class="cu-form-group ">
        <view class="title">活动场次</view>
      </view>

      <view class="cu-form-group ">
        <view class="title text-sm">选择日期：</view>
        <view class="text-right flex text-sm" @click="isShow=true">
          <input class="text-sm padding-right-xs"  disabled  placeholder="请选择" name="phone" v-model="memberactivityInfo.date"></input>
          <text class="cuIcon-right"></text>
        </view>
      </view>

      <view class="cu-form-group ">
        <view class="title text-sm">选择场次：</view>
        <view class="text-right flex text-sm" @click="sessionTime()">
          <input class="text-sm padding-right-xs"  disabled placeholder="请选择" name="phone" v-model="memberactivityInfo.time"></input>
          <text class="cuIcon-right"></text>
        </view>
      </view>

      <view class="cu-form-group margin-top">
        <view class="title">报名信息</view>
      </view>

      <view class="cu-form-group ">
        <view class="title text-sm">姓 名：</view>
        <view class="text-right text-sm">
          <input class="text-sm padding-right-xs"  placeholder="必填" name="phone" v-model="form.name"></input>
        </view>
      </view>

      <view class="cu-form-group ">
        <view class="title text-sm">手机号码：</view>
        <view class="text-right text-sm">
          <input class="text-sm padding-right-xs"  placeholder="必填" name="phone" v-model="form.phone"></input>
        </view>
      </view>

      <view class="cu-form-group">
        <view class="title text-sm">VIP邀请码：</view>
        <view class="text-right text-sm">
          <input class="text-sm padding-right-xs" disabled  placeholder="邀请码" name="phone" v-model="form.vipInvitationCode"></input>
        </view>
      </view>

      <view class="cu-form-group">
        <textarea
          maxlength="40"
          placeholder-style="font-size:36rpx;text-algin:left"
          v-model="form.remark"
          :data-index="index"
          placeholder="备注"
        ></textarea>
      </view>

      <view class="cu-form-group margin-top">
        <view class="title">报名方式</view>
        <view class="text-right text-sm flex">
          <view class="margin-right-xs">{{memberactivityInfo.registrationMethod | statusType}}</view> | 
          <view class="margin-let-xs" v-if="memberactivityInfo.points">{{memberactivityInfo.points}}积分</view>
          <view class="margin-lr-xs" v-if="memberactivityInfo.amount">{{memberactivityInfo.amount}}元</view>
        </view>
      </view>

      <view class="cu-form-group margin-top">
        <view class="title">选择人数</view>
        <view class="text-right text-sm flex" v-if="memberactivityInfo.registrationPeopleOnec">
          <view>限{{memberactivityInfo.registrationPeopleOnec}}人</view>
        </view>
      </view>

      <view class="cu-form-group">
        <view class="title text-sm">参加人数</view>
        <view class="text-right text-sm flex">
          <base-stepper 
          :reduceBtnStyle="`border: 2rpx solid #ccc;padding: 0 10rpx;`"
          :addBtnStyle="`border: 2rpx solid #ccc;padding: 0 10rpx;`"
          :inputStyle="`padding-right:0rpx;`"
          :stNum="form.peopleNum"
          :min="1"
          :max="form.registrationPeopleOnec>0?form.registrationPeopleOnec:form.activityResidualNum"
					@numChange="cartNumChang($event,item)"
					:data-index="index">
        </base-stepper>
        </view>
      </view>


      <view 
        class="cu-bar bg-white tabbar border shop foot " 
        style="background-color: #FFFFFF;" :style="{'z-index': '1' }"
      >
        <view class="flex justify-between align-center">
          <view class="margin-left-sm flex">
            <view>合计：</view>
            
            <text v-if="memberactivityInfo.registrationMethod=='1'" class="margin-left-xs">/</text>
            <view class="margin-right"  v-if="form.paymentPoints">
              <text style="color: #F87F3A">{{form.paymentPoints}}</text>
              <text>积分</text>
            </view>

            <view class="margin-right" v-if="form.paymentPrice" >
              <text style="color: #F87F3A">{{form.paymentPrice}}</text>
              <text>元</text>
            </view>
          </view>
        </view>
  
        <view class="flex justify-between align-center margin-right-sm" >
          <button 
            form-type="submit"
            class="cu-btn round text-black"
            style="background:#F87F3A;color:#FFFFFF;border: 1px solid #F87F3A;">
            确认报名
          </button>
        </view>
      </view>
		</form>
    <!-- 日历 -->
    <view
      class="cu-modal bottom-modal"
      :class="isShow ? 'show animation-slide-bottom' : ''"
      @tap.stop
    >
      <view
        class="cu-dialog dialo-sku bg-white"
        :class="isShow ? 'animation-slide-bottom' : ''"
        style="height: 50%"
        @tap.stop
      >
          <zeng-calen  :actDay="actDay" :chooseDay="chooseDay" @onDayClick="onDayClick" @close="isShow=false"></zeng-calen>
      </view>
    </view>

    <!-- 场次 -->
    <view
      class="cu-modal bottom-modal"
      :class="sessionShow ? 'show animation-slide-bottom' : ''"
      @tap.stop
    >
      <view
        class="cu-dialog dialo-sku bg-white"
        :class="isShow ? 'animation-slide-bottom' : ''"
        style="height: 50%"
        @tap.stop
      >
        <view 
					class="flex justify-between align-center text-xs padding-lr" 
					style="
					background-color: rgba(255, 122, 81, 1);
    			height: 80rpx;
				">
					<view class="text-white text-df">选择场次</view>

					<view @click="sessionShow = false" class="cuIcon-close text-white text-df margin-left-xs"></view>
        </view>
        <view class="padding-sm flex flex-wrap" style="overflow-x: auto;max-width: 100%;height: 80%;">
          <view v-for="(item, index) in sessionList" :key="index">

            <view 
              @click="getSessionId(item)"
              class="padding-sm radius-sm margin-sm" 
              style="color: #F87F3A; border: 2rpx solid #F87F3A;width: 200rpx;height: 132rpx;overflow: hidden;"
              >
              <view>{{item.sessionsBeginTime}}~{{item.sessionsEndTime}}</view>
              <view>剩余：{{item.activityResidualNum}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付 -->
    <view class="toast-pay" v-if="toastHide">
			<pay-components ref="pay" :pageTitle="pageTitle" :callBack="true" @success="successBack"
				@fail="failBack"></pay-components>
			<view @click="$noMultipleClicks(payOrder)" class="one-btn">确定</view>
		</view>
		<view class="ZZC" @click="toastClose" v-if="toastHide"></view>
  </view>
</template>

<script>

import zengCalen from "../components/zeng-calen/zeng-calen.vue";
import baseStepper from "components/base-stepper/index";
import { getsession,signUp } from '@/pages/memberactivity/api/memberactivity';
import validate from 'utils/validate'
import payComponents from "@/components/pay-components/pay-components.vue";

const util = require("utils/util.js");
const app = getApp();
import { senTrack } from '@/public/js_sdk/sensors/utils.js'

export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      noClick: true,
      form: {
        activityId:'',//会员活动ID
        activitySessionsId:'',//场次id
        name:'',//姓名
        phone:'',//手机号
        vipInvitationCode:'',//vip邀请码
        peopleNum:1,//	参加人数
        paymentPoints:'',//支付积分
        paymentPrice:'',//支付金额
        remark:'',//	备注
        registrationPeopleOnec:0,
        activityResidualNum:0,
      },//表单数据
      memberactivityInfo:{
      },//活动信息
      isShow:false,//弹框默认不显示
      sessionShow:false,//场次默认不展示
      sessionList:[],//场次列表
      actDay: [], //用户选择的日期
			chooseDay: [
        // "2024-10-13", "2024-10-15", "2024-10-16", "2024-10-05"
      ], //活动日历数据
      pageTitle:'会员活动',
      toastHide: false, //支付选择弹框
      memberActivities:{},//会员活动
      price:'',//价格
      points:'',//价格
      // 神策埋点
      trackParams: {
        page_name: "立即报名",
        forward_source: "活动详情",
        page_level: '一级',
        activity_id: "",
        activity_name: "",
        activity_type_first: "营销活动",
        activity_type_second: "会员活动",
      },
    };
  },

  components: {
    zengCalen,
    baseStepper,
    payComponents
  },

  onLoad (options) {
    app.initPage().then(res => {
      const value = uni.getStorageSync('memberactivity');
      this.memberactivityInfo = value
      this.form.name = uni.getStorageSync('user_info').nickName
      this.form.phone = uni.getStorageSync('user_info').phone
      this.form.paymentPoints = value.points
      this.form.paymentPrice = value.amount
      this.form.registrationPeopleOnec = value.registrationPeopleOnec
      this.price = value.amount
      this.points = value.points
      this.form.vipInvitationCode = value.vipInvitationCode ||''
      this.chooseDay = value.activityCalendar.map(item => item.date);
    });
  },

  filters: {
    statusType(key) {
      const statusMap = {
          '1': '免费报名',
          '2': '积分报名',
          '3': '付费报名'
      };
        return statusMap[key] || '未知状态';
    }
  },

  onShow () { },

  methods: {
  // 关闭支付
  toastClose() {
		this.toastHide = false;
	},

  // 支付
  payOrder() {
		uni.showLoading({
			title: '支付中',
			mask: true
		});

    const params = {
			...this.memberActivities,
			...this.form
		}
    console.log("memberactivity===>",params);
    
		this.$refs.pay?.payOrder(params, false, 'memberactivity')
	},

  // 支付失败的回调
	failBack() {
		uni.showToast({
			title: '支付失败',
			icon: 'none'
		})
		// this.loading = false;
		this.toastHide = false;
		// 跳转活动列表
		uni.navigateTo({
			url: '/pages/memberactivity/list/index'
		})
	},

  // 支付成功的回调
		successBack() {
      console.log("====successBack====");
			const that = this;
      that.toastClose();
      uni.showModal({
        title: '提示',
        content: '支付成功',
        success: function (res) {
          if (res.confirm) {
            senTrack("ActivityClick", {
              ...this.trackParams,
              activity_id: this.memberactivityInfo.activity_id,
              activity_name: this.memberactivityInfo.activity_name,
            });
            uni.navigateTo({
                url: '/pages/memberactivity/application-detail/index?id='+that.memberActivities.id
            });
          } else if (res.cancel) {
            uni.navigateBack({
                delta: 1
            });
          }
        }
      });
		},

    // 获取场次id
    getSessionId(e){
      this.form.activitySessionsId = e.id
      this.memberactivityInfo.time = `${e.sessionsBeginTime}~${e.sessionsEndTime}`
      // 剩余人数
      this.form.activityResidualNum = e.activityResidualNum
      console.log("===getSessionId===",this.form);
      
      this.sessionShow = false
    },

    // 场次弹框
    sessionTime(){
      if (this.memberactivityInfo.date) {
        this.sessionShow =true
      }else{
        uni.showToast({
              title: "请先选择日期!",
              icon: "none"
          });
      }
    },

    // 数量变化
		cartNumChang(val, item) {
      console.log("cartNumChang",val, item);
      this.form.peopleNum = val
      if(this.form.paymentPrice){
        // 计算总价格（以分为单位）
        const priceInCents = this.price * 100
        let totalPriceInCents = priceInCents * val;
        // 将总价格转换为正确的价格格式（美元）
        let totalPrice = (totalPriceInCents / 100).toFixed(2);
        // 如果总价格是整数，去掉小数部分
        if (totalPrice.endsWith('.00')) {
            totalPrice = totalPrice.split('.')[0];
        }
        this.form.paymentPrice = totalPrice
      }
      if(this.form.paymentPoints){
        this.form.paymentPoints = this.points * val;
      }	
		},

    // 展开日历
		onDayClick(data) {
			let choose = data.date //用户点中的数据
      let bol  = this.chooseDay && this.chooseDay.length > 0
      if (!bol) {
          uni.showToast({
              title: "暂无活动日期!",
              icon: "none"
          });
        return
      }
      // 获取当前日期
      const currentDate = new Date().toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD

      // 检查选择的日期是否在过去
      if (choose < currentDate) {
        uni.showToast({
          title: "不能选择过去的日期!",
          icon: "none"
        });
        return;
      }
      
			if (this.actDay.includes(choose)) { //如果用户点击的日期已经存在
				// 移除元素下标
				const index = this.actDay.indexOf(choose);
				//删除用户点击的日期
				this.actDay.splice(index, 1)
			} else if (this.chooseDay.includes(choose)) { //判断是否已经被投标
				console.log("choose===>",choose);
        let index = this.chooseDay.indexOf(choose);
        if (index !== -1) {
          this.memberactivityInfo.activityCalendar[index].id;
          this.form.activityId = this.memberactivityInfo.activityCalendar[index].id
          this.memberactivityInfo.date = choose
          this.memberactivityInfo.time = ''
          this.getsessionFun()
        }
			} else {
        uni.showToast({
          title: "请选择标记的活动日期!",
					icon: "none"
				})
        //添加用户点击的日期
				// this.actDay.push(choose)
			}
		},

    // 备注
    textareaInput (e) {
      let dataset = e.currentTarget.dataset;
      let index = dataset.index;
      console.log("textareaInput===>",index,dataset);
      
    },
    async getsessionFun(){
      let parameter = {
          activityId: this.form.activityId,
          activityDate: this.memberactivityInfo.date
        }
      const { data } =  await getsession(Object.assign({},parameter))
      this.sessionList = data
      console.log("data",data);
      this.isShow = false
    },

    // 提交文本
    async memberSub(e) {
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码!',
						icon: 'none',
						duration: 3000
					});
					return;
				}

        if (!this.form.name) {
					uni.showToast({
						title: '请输入报名人姓名!',
						icon: 'none',
						duration: 3000
					});
					return;
				}

        if (!this.form.activitySessionsId) {
					uni.showToast({
						title: '请选择场次时间!',
						icon: 'none',
						duration: 3000
					});
					return;
				}
        
        if (!this.form.activityId) {
					uni.showToast({
						title: '请选择活动日期!',
						icon: 'none',
						duration: 3000
					});
					return;
				}

        // 表单数据有效，调用 活动报名
        const {data} = await signUp(Object.assign({},this.form));
        if (data) {
          console.log("活动报名成功！",data);
          senTrack("ActivityClick", {
            ...this.trackParams,
            activity_id: this.memberactivityInfo.activity_id,
            activity_name: this.memberactivityInfo.activity_name,
          });
          this.memberActivities = data
          if(this.form.paymentPrice){
            // 如果有金额就换起支付
            this.toastHide = true
          }else{
            uni.showModal({
							title:'温馨提示',
							content: '报名成功，我要查看报名详情',
							showCancel: false,
							success (res) {
								if (res.confirm) {
									uni.redirectTo({
                    url: '/pages/memberactivity/application-detail/index?id='+ data.id
                  })
								}
							}
						});
          }
        } else {
            console.log("活动报名失败！");
        }
		},
  }
};
</script>
<style  scoped>
.parent {
    display: flex;
    flex-wrap: wrap;
    overflow-x: auto; /* 水平方向超出部分可以滚动查看 */
    max-width: 100%; /* 父盒子宽度不超过视口宽度 */
}

.child {
    width: 25%; /* 每行显示四个子盒子 */
    flex-shrink: 0; /* 防止子盒子缩小 */
    box-sizing: border-box; /* 边框和内边距计算在内 */
    border: 1px solid #ccc; /* 边框样式，可根据需要调整 */
    text-align: center; /* 文字居中显示 */
    padding: 10px; /* 内边距，可根据需要调整 */
}

.one-btn {
		width: 570upx;
		margin: 50upx auto 30upx;
		text-align: center;
		line-height: 90upx;
		background: linear-gradient(-90deg, #FFBF00 0%, #FFDD00 100%);
		border-radius: 45upx;
	}

.toast-pay {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100vw;
		background-color: #fff;
		padding: 30upx 0 20upx;
		border-radius: 20upx 20upx 0 0;
		z-index: 10;
	}

	.ZZC {
		position: fixed;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, .5);
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 2;
	}
</style>
