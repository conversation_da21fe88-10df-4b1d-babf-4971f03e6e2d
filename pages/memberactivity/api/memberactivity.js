import {
  requestApi as request
} from '@/utils/api.js'

// 会员活动表分页列表
export const memberactivityinfo = (data) => {
  return request({
    url: '/mallapi/memberactivityinfo/page',
    method: 'get',
    data
  })
}

// 会员活动详情
export const memberactivityDetail  = (id) => {
  return request({
    url: '/mallapi/memberactivityinfo/' + id,
    method: 'get',
  })
}

// 会员活动日历
export const activityCalendar  = (data) => {
  return request({
    url: '/mallapi/memberactivityinfo/activityCalendar',
    method: 'get',
    data
  })
}

// 会员活动订单查询
export const memberactivityorder  = (id) => {
  return request({
    url: '/mallapi/memberactivityorder/' + id,
    method: 'get',
  })
}

// 会员活动评价表分页列表
export const goodsAppraisesPage = (data) => {
  return request({
    url: '/mallapi/memberactivityappraises/page',
    method: 'get',
    data
  })
}

// 我的会员活动评价
export const myappraises = (data) => {
  return request({
    url: '/mallapi/memberactivityappraises/myappraises',
    method: 'get',
    data
  })
}

// 活动评价新增
export const addEvaluate = (data = {}) => {
  return request({
    url: '/mallapi/memberactivityappraises',
    method: 'post',
    // showLoading: false,
    data
  })
}

// 我的活动分页列表
export const myAppraisesPage = (data) => {
  return request({
    url: '/mallapi/memberactivityorder/page',
    method: 'get',
    data
  })
}

// 会员活动报名
export const signUp = (data = {}) => {
  return request({
    url: '/mallapi/memberactivityorder/signUp',
    method: 'post',
    // showLoading: false,
    data
  })
}

// 会员活动场次表查询
export const getsession  = (data) => {
  return request({
    url: '/mallapi/memberactivitysessions/getsession',
    method: 'get',
    data
  })
}

// 会员活动邀请码表查询
export const Invitationcode  = (data) => {
  return request({
    url: '/mallapi/memberactivityinvitecode/getInviteCode',
    method: 'get',
    data
  })
}

// 会员活动支付
export const payActivityOrder = (data = {}) => {
  return request({
    url: '/mallapi/memberactivityorder/payActivityOrder-zt',
    method: 'post',
    // showLoading: false,
    data
  })
}

// 会员活动订单查询
export const cancel  = (id) => {
  return request({
    url: '/mallapi/memberactivityorder/cancel/' + id,
    method: 'put',
  })
}

// 会员活动支付
export const addAppraises = (data = {}) => {
  return request({
    url: '/mallapi/memberactivityappraises',
    method: 'post',
    // showLoading: false,
    data
  })
}

// 会员活动支付退款
export const refund = (data = {}) => {
  return request({
    url: '/mallapi/memberactivityorder/refund',
    method: 'post',
    // showLoading: false,
    data
  })
}