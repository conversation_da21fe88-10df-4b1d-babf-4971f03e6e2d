<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">活动详情</block>
    </cu-custom>
    <view class="relative">
      <!-- 图片 -->
      <image
        mode="widthFix"
        style="width: 750rpx"
        :src="activityDetail.activityPicUrl"
      ></image>
      <view
        style="position: absolute; top: 320rpx; width: 100%"
        :style="{ marginBottom: '42px' }"
      >
        <!-- 活动信息 -->
        <view class="cu-card article">
          <view class="cu-item" style="margin-bottom: 0rpx !important">
            <view
              class="flex justify-between align-center margin-left-sm padding-top-xs"
            >
              <view class="text-black overflow-2 text-bold">
                {{ activityDetail.name || "" }}
              </view>
              <view
                @click="shareShowFun"
                style="
                  margin: 10rpx 5rpx 10rpx 10rpx;
                  flex-direction: column;
                  justify-content: center;
                "
                class="flex"
              >
                <image
                  style="width: 70rpx; height: 76rpx"
                  src="https://img.songlei.com/live/u1743.png"
                ></image>
                <!-- <text style="font-size: 20rpx; color: #e54d42; width: 54rpx;">分享</text> -->
              </view>
            </view>
            <!-- 活动日期  只有固定时间展示场次，指定时间不展示-->
            <view class="padding-left flex align-center">
              <veiw class="cuIcon-calendar text-xl"></veiw>
              <view class="padding-left text-sm"
                >{{ activityDetail.activityBeginTime || "" }}-{{
                  activityDetail.activityEndTime || ""
                }}</view
              >
            </view>
            <scroll-view
              scroll-y
              class="bg-white collection-types"
              v-if="activityDetail.sessionMode == '1'"
              style="height: 90rpx; margin-left: 110rpx"
            >
              <!-- 活动场次时间 -->
              <view
                v-for="(
                  item, index
                ) in activityDetail.memberActivitySessionsList"
                :key="index"
              >
                <view class="flex text-xss margin-xs">
                  <view
                    style="background-color: #f87f3a; color: #ffffff"
                    class="padding-lr-xs"
                    >活动{{ index + 1 }}</view
                  >
                  <view class="margin-left-sm"
                    >{{ item.sessionsBeginTime || "" }}-{{
                      item.sessionsEndTime || ""
                    }}</view
                  >
                </view>
              </view>
            </scroll-view>

            <!-- vip -->
            <view class="padding-left flex align-center margin-top-sm">
              <image
                style="width: 39rpx; height: 38rpx"
                src="https://img.songlei.com/live/vip.png"
              ></image>
              <veiw
                class="margin-left"
                v-for="card in activityDetail.cards"
                :key="card.name"
              >
                <view
                  class="text-xs text-center radius-lg padding-lr-xs"
                  :style="{
                    color: card.color,
                    border: `2rpx solid ${card.color}`,
                  }"
                >
                  {{ card.name || "" }}
                </view>
              </veiw>
            </view>
            <!-- 报名人员 -->
            <view
              class="padding-left flex align-center margin-top-sm"
              v-if="activityDetail.apiStatus != '1'"
            >
              <veiw class="cuIcon-people text-xl"></veiw>
              <veiw class="margin-left-sm">
                <view class="flex align-center">
                  <!--头像组件 urls数组 -->
                  <a-avatar-slideshow
                    ref="avatar"
                    :urls="activityDetail.headimgList"
                    width="40"
                    height="40"
                    :overlap="10"
                    :interval="interval"
                    :maxDisplayCount="9"
                  ></a-avatar-slideshow>
                  <view class="text-light-gray text-xs margin-top-xs">
                    已有
                    <text style="color: #f87f3a">{{
                      activityDetail.activitySignUpNum
                    }}</text>
                    人报名
                  </view>
                </view>
              </veiw>
            </view>
            <!-- 倒计时 -->
            <view
              v-if="
                activityDetail.apiStatus == '1' &&
                activityDetail.beginSecond > 0
              "
              class="text-sm margin-left-xs padding-bottom-xs padding-left margin-top-sm text-bold"
              style="color: #ff0000"
            >
              距离开始:
              <!-- 倒计时 -->
              <count-down
                v-if="activityDetail.beginSecond >= 0"
                class="bg-white round padding-xs text-xs"
                :connectorColor="'#FF0000'"
                :outTime="activityDetail.beginSecond * 1000"
                @countDownDone="countDownDone"
              ></count-down>
            </view>
          </view>
        </view>
        <!-- 主办方 -->
        <view class="cu-card article">
          <view class="cu-item" style="margin: 20rpx 30rpx !important">
            <view
              class="flex justify-between align-center margin-lr-sm padding-top-xs"
            >
              <view class="text-black overflow-2 text-bold">主办方：</view>
              <view class="text-black text-bold">
                {{ activityDetail.sponsor }}
              </view>
            </view>
            <view class="flex justify-between margin-lr-sm padding-tb-xs">
              <veiw class="text-sm">活动地点：</veiw>
              <veiw class="text-sm">
                {{ activityDetail.activityLocation || "" }}
              </veiw>
            </view>
            <view class="flex justify-between margin-lr-sm padding-tb-xs">
              <veiw class="text-sm">咨询电话：</veiw>
              <veiw class="text-sm">
                {{ activityDetail.contactNumber || "" }}
              </veiw>
            </view>
            <view class="flex justify-between margin-lr-sm padding-tb-xs">
              <veiw class="text-sm">联系人：</veiw>
              <veiw class="text-sm">
                {{ activityDetail.contactPerson || "" }}
              </veiw>
            </view>
          </view>
        </view>
        <!-- 活动详情、规制 说明 -->
        <view class="cu-card article">
          <view
            class="cu-item"
            style="margin: 0rpx 30rpx 20rpx !important"
            :style="{
              marginBottom: activityDetail.qrCode ? '' : '150rpx',
            }"
          >
            <view class="align-center margin-lr-sm padding-top-xs">
              <view class="text-black overflow-2 text-bold">活动详情</view>
              <view class="bg-white">
                <jyf-parser :html="activityDetail.activityDetails"></jyf-parser>
                <view
                  @click="
                    toPage(
                      `/pages/memberactivity/appraises/list/index?activityId=${activityDetail.id}&type=1`,
                    )
                  "
                  class="flex align-center justify-between"
                  style="
                    border-top: 2rpx solid #eeeeee;
                    margin-top: 20rpx;
                    padding-top: 20rpx;
                  "
                >
                  <view
                    class="text-purple-grey flex justify-between margin-top-xs"
                  >
                    <view class="text-black"
                      >活动评价({{
                        activityDetail.activityAppraisesNum || 0
                      }})</view
                    >
                  </view>
                  <view class="margin-left-sm margin-top-xs">
                    查看全部
                    <text class="cuIcon-right margin-left-xs"></text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 二维码图片 -->
        <view
          class="cu-card article"
          style="margin-bottom: 150rpx"
          v-if="activityDetail.qrCode"
        >
          <view
            class="cu-item padding-top"
            style="margin: 0rpx 30rpx !important; text-align: center"
          >
            <view class="text-xs"
              >↓↓↓长按识别下方二维码入群获取更多精彩内容</view
            >
            <view>
              <image
                style="width: 139rpx; height: 138rpx"
                :src="activityDetail.qrCode"
              ></image>
            </view>
          </view>
        </view>
      </view>
      <!-- 底部按钮-->
      <view
        class="cu-bar bg-white tabbar border shop foot"
        style="background-color: #ffffff"
        :style="{ 'z-index': '1' }"
      >
        <view
          class="flex justify-between align-center"
          @click="
            activityType(activityDetail.apiStatus).disabled ||
            activityDetail.apiStatus == '7'
              ? ''
              : toPage(
                  `/pages/memberactivity/application-detail/index?id=${activityDetail.activityOrderId}`,
                )
          "
        >
          <view
            class="margin-left-sm"
            :class="
              activityType(activityDetail.apiStatus).disabled ||
              activityDetail.apiStatus == '7'
                ? 'text-light-gray'
                : 'text-black'
            "
          >
            我的报名
            <text class="cuIcon-right margin-left-xs"></text>
          </view>
        </view>

        <view class="flex justify-between align-center margin-right-sm">
          <view class="margin-right" v-if="activityDetail.registrationPoints">
            <text>{{ activityDetail.registrationPoints }}</text>
            <text>积分</text>
          </view>

          <view class="margin-right" v-if="activityDetail.registrationAmount">
            <text>{{ activityDetail.registrationAmount }}</text>
            <text>元</text>
          </view>
          <button
            @click="signUp()"
            :disabled="
              activityType(activityDetail.apiStatus).disabled ||
              activityDetail.apiStatus == '6'
            "
            class="cu-btn round text-black"
            :class="
              activityType(activityDetail.apiStatus).disabled ||
              activityDetail.apiStatus == '6'
                ? ''
                : 'btn'
            "
            style="background: #ffffff; border: 1px solid #999999"
          >
            {{ activityType(activityDetail.apiStatus).name }}
          </button>
        </view>
      </view>
    </view>
    <!-- 分享组件 -->
    <share-component
      v-model="showShare"
      :shareParams="shareParams"
      :showShareHeight="85"
    ></share-component>
    <!-- VIP弹框 -->
    <view
      class="cu-modal"
      :class="modalDialog ? 'show' : ''"
      @tap="hideModalDialog"
    >
      <view
        class="cu-dialog bg-white"
        :class="modalDialog ? 'animation-slide-bottom' : ''"
        style="height: 450upx; border-radius: 30rpx"
        @tap.stop
      >
        <view class="cu-card article no-card" style="padding-top: 20upx">
          <view
            class="text-xl close-icon text-center padding-left-sm padding-right-sm"
          >
            <text class="text-df text-black">请填写VIP邀请码</text>
          </view>
          <!-- 关闭弹框icon -->
          <text
            class="cuIcon-close"
            style="
              color: #000;
              background-color: #eee;
              position: absolute;
              top: 3%;
              right: 3%;
              border: 1rpx solid #fff;
              width: 40rpx;
              border-radius: 50%;
            "
            @tap="hideModalDialog"
          ></text>
        </view>

        <view
          class="flex justify-center"
          style="margin-top: 100rpx; margin-bottom: 90rpx"
        >
          <view class="title">VIP邀请码：</view>
          <view style="border-bottom: 1px solid #999999">
            <input v-model="vipCode" placeholder="请输入活动邀请码" />
          </view>
        </view>
        <view class="flex justify-around">
          <button
            @click="notHave()"
            class="cu-btn text-black"
            style="
              background: #ffffff;
              border: 1px solid #999999;
              width: 235rpx;
            "
          >
            我没有邀请码
          </button>
          <button
            class="cu-btn text-black"
            @click="confirm()"
            style="
              background: #ffffff;
              border: 1px solid #999999;
              width: 235rpx;
            "
          >
            确定
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import formatPrice from "@/components/format-price/index.vue";

import {
  memberactivityDetail,
  Invitationcode,
} from "@/pages/memberactivity/api/memberactivity";
import { navigateUtil } from "@/static/mixins/navigateUtil.js";
import countDown from "@/components/count-down/index";
import shareComponent from "@/components/share-component/index";
import util from "@/utils/util";

import aAvatarSlideshow from "../components/a-avatar-slideshow/a-avatar-slideshow.vue";

import { senTrack, getCurrentTitle } from "@/public/js_sdk/sensors/utils.js";

export default {
  mixins: [navigateUtil],
  components: {
    formatPrice,
    aAvatarSlideshow,
    countDown,
    shareComponent,
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      //有数统计使用
      page_title: "我的礼品卡",
      priceStyle:
        "display: flex; justify-content: center; font-weight: bold; align-items: baseline;",
      priceStyle2:
        "display: flex; justify-content: center; align-items: baseline;",
      activityDetail: {}, //详情

      isShow: true,
      cards: [],
      article_description: "", //详情
      headimgList: [
        "https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png",
        "https://img.songlei.com/1/material/ec3b8c7b-d812-4d43-adad-aecf3c57343b.jpg-jpg_w360_q90",
        "https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png",
        "https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png",
        "https://img.songlei.com/1/material/3e4d4561-eb84-4da3-b001-1f0afd275e0b.gif-gif_q90_s80",
        "https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png",
        "https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png",
        "https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png",
      ],
      modalDialog: false, //控制弹框
      vipCode: "", //邀请码
      id: "", //活动id
      interval: 2000,
      activityCalendar: [], //活动日历
      showShare: false, //分享控制
      shareParams: {}, //分享参数
      // 神策埋点
      trackParams: {
        page_name: "活动详情",
        forward_source: "会员活动",
        page_level: "一级",
        activity_id: "",
        activity_name: "",
        activity_type_first: "营销活动",
        activity_type_second: "会员活动",
      },
      enterTime: 0, // 进入页面时间戳（毫秒）
    };
  },
  props: {},

  onLoad(options) {
    if (options && options.id) {
      this.id = options.id;
    }
    //兼容通过扫码商品小票上面的二维码进来
    if (options.scene) {
      let scene = decodeURIComponent(decodeURIComponent(options.scene)); // 双重解码
      if (scene && scene.length > 0 && scene != "undefined") {
        if (util.getUrlParam(scene, "id")) {
          this.id = util.getUrlParam(scene, "id");
        } else {
          // 分享进来
          // let scenes = decodeURIComponent(options.scene).split('&');
          let scenes = scene.split("&");
          this.id = scenes[0];
        }
      }
    }
    uni.removeStorageSync("memberactivity");
  },

  watch: {},
  onShow() {
    this.interval = 2000;
    this.hideModalDialog();
    app.initPage().then((res) => {
      this.getDataList(this.id);
    });
  },
  beforeDestroy() {
    console.log("beforeDestroy---->");
    this.interval = 0;
    this.$nextTick(() => {
      this.$refs.avatar.stop();
    });
  },

  onHide() {
    console.log("onHide---->");
    this.interval = 0;
    this.$nextTick(() => {
      this.$refs.avatar.stop();
    });
  },

  onUnload() {
    const leaveTime = new Date().getTime();
    // 组件销毁前记录离开时间
    const durationSeconds = Math.floor((leaveTime - this.enterTime) / 1000);
    console.log("组件生命周期内停留时长（秒）：", durationSeconds);
    if (this.activityDetail && this.activityDetail.id) {
      senTrack("ActivityPageLeave", {
        ...this.trackParams,
        activity_id: this.activityDetail.id,
        activity_name: this.activityDetail.name,
        stay_duration: durationSeconds,
      });
    }
  },

  onShareAppMessage: function (ops) {
    let from_type = "menu";
    if (ops.from === "button") {
      from_type = "button";
    }
    let goodsSpu = this.activityDetail;
    let title = goodsSpu.name;
    let sharePic = "";

    if (goodsSpu.sharePicUrl) {
      sharePic = goodsSpu.sharePicUrl;
    }
    // 有分享图就用，没有就活动图
    let imageUrl = sharePic
      ? sharePic + "-jpg_w360_q90"
      : goodsSpu.activityPicUrl;

    const userInfo = uni.getStorageSync("user_info");
    let userCode = userInfo
      ? "&type=1&sharer_user_code=" + userInfo.userCode
      : "";
    let path =
      "/pages/memberactivity/application/index?id=" + goodsSpu.id + userCode;

    return {
      title: title,
      path: path,
      imageUrl: imageUrl,
      success: function (res) {
        uni.showToast({
          title: "分享成功",
        });
      },
      fail: function (res) {
        // 转发失败
        uni.showToast({
          title: "分享失败",
          icon: "none",
        });
      },
    };
  },

  onShareTimeline: function () {
    let goodsSpu = this.activityDetail;
    let title = goodsSpu.name;
    let sharePic = "";
    if (goodsSpu.sharePicUrl) {
      sharePic = goodsSpu.sharePicUrl;
    }
    // 有分享图就用，没有就活动图
    let imageUrl = sharePic ? sharePic : goodsSpu.activityPicUrl;

    const userInfo = uni.getStorageSync("user_info");
    let userCode = userInfo
      ? "&type=1&sharer_user_code=" + userInfo.userCode
      : "";
    let path = "id=" + goodsSpu.id + userCode;

    return {
      title: title,
      query: path,
      imageUrl: imageUrl,
      success: function (res) {
        uni.showToast({
          title: "分享成功",
        });
      },
      fail: function (res) {
        // 转发失败
        uni.showToast({
          title: "分享失败",
          icon: "none",
        });
      },
    };
  },

  computed: {},

  methods: {
    statusMethodType(key) {
      const statusMap = {
        1: "免费报名:",
        2: "积分报名:",
        3: "付费报名:",
      };
      return statusMap[key] || "未知状态:";
    },
    // 分享控制
    async shareShowFun() {
      // 分享海报需要配置的参数
      let sharePic = "";
      let registrationMethod = "";
      let desc = "长按识别小程序码"; // 添加这行定义
      if (this.activityDetail.registrationMethod) {
        registrationMethod = this.statusMethodType(
          this.activityDetail.registrationMethod,
        );
      }

      if (this.activityDetail && this.activityDetail.sharePicUrl) {
        sharePic = this.activityDetail.sharePicUrl;
      }
      senTrack("ActivityPageShareOrCollect", {
        ...this.trackParams,
        activity_id: this.activityDetail.id,
        activity_name: this.activityDetail.name,
        action_type: "分享",
      });
      let shareImg = sharePic || this.activityDetail.activityPicUrl; // 海报图片
      // #ifdef H5 || APP-PLUS
      desc = "长按识别二维码";
      // h5的海报分享的图片有的有跨域问题，所以统一转成base64的
      // 之所以不在组件里面转换是因为无法区分哪张image图片需要处理，一般处理主图
      shareImg = await util.imgUrlToBase64(shareImg);
      // #endif
      //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
      let posterConfig = {
        width: 750,
        height: 750,
        backgroundColor: "#fff",
        debug: false,
        lines: [{}],
        blocks: [
          {
            width: 480,
            height: 500,
            x: 135,
            y: 20,
            borderRadius: 20,
          },
          //竖线
          {
            x: 500,
            y: 790,
            width: 2,
            height: 180,
            backgroundColor: "#939393",
            zIndex: 200,
          },
        ],
        texts: [
          //商品名称
          {
            x: 15,
            y: 830,
            fontSize: 36,
            baseLine: "middle",
            text: this.activityDetail.name,
            // text: this.activityDetail.name && this.activityDetail.length > 17 ? this.activityDetail.substring(0, 16) + '...' : this.activityDetail.name,
            width: 445,
            lineNum: 5,
            color: "#333333",
            zIndex: 200,
          },
          // 扫描/长按识别
          {
            x: 555,
            y: 940,
            fontSize: 20,
            baseLine: "top",
            text: "扫描/长按识别",
            width: 250,
            lineNum: 10,
            lineHeight: 70,
            color: "#666666",
            zIndex: 200,
          },
          // 报名方式
          {
            x: 220,
            y: 695,
            baseLine: "top",
            text: registrationMethod,
            fontSize: 40,
            fontWeight: "900",
            color: "#e54d42",
            zIndex: 200,
          },
        ],
        images: [
          // 分享图片
          {
            width: 730,
            height: 480,
            x: 10,
            y: 160,
            url: shareImg,
          },
          {
            width: 735,
            height: 65,
            x: 5,
            y: 680,
            url: null,
            url: "https://img.songlei.com/live/vermicelli.png",
          },
          {
            width: 116,
            height: 116,
            x: 555,
            y: 800,
            url: null,
            qrCodeName: "qrCodeName", // 二维码唯一区分标识
          },
        ],
      };

      let userInfo = uni.getStorageSync("user_info");
      if (userInfo && userInfo.headimgUrl) {
        //如果有头像则显示
        posterConfig.images.push({
          width: 120,
          height: 120,
          x: 15,
          y: 0,
          borderRadius: 110,
          url: userInfo.headimgUrl,
          zIndex: 200,
        });
        // 手机号 或者名称
        posterConfig.texts.push({
          x: 160,
          y: 40,
          baseLine: "middle",
          text: userInfo.nickName,
          fontSize: 28,
          fontWeight: "bold",
          color: "#666666",
        });
        // 文案
        posterConfig.texts.push({
          x: 160,
          y: 90,
          baseLine: "middle",
          text: "就等你了，赶快来参加活动吧!",
          fontSize: 28,
          color: "#666666",
        });
      }

      // 价格积分显示
      if (this.activityDetail.registrationMethod) {
        // 免费
        if (this.activityDetail.registrationMethod == "1") {
          posterConfig.texts.push({
            x: 420,
            y: 695,
            baseLine: "top",
            text: "/",
            fontSize: 40,
            fontWeight: "900",
            color: "#e54d42",
            zIndex: 200,
          });
        } else {
          // 积分报名 + 付费
          if (
            this.activityDetail.registrationPoints &&
            this.activityDetail.registrationAmount
          ) {
            posterConfig.texts.push({
              x: 400,
              y: 695,
              baseLine: "top",
              text: this.activityDetail.registrationAmount + "元",
              fontSize: 40,
              fontWeight: "900",
              color: "#e54d42",
              zIndex: 200,
            });

            posterConfig.texts.push({
              x: 520,
              y: 695,
              baseLine: "top",
              text: this.activityDetail.registrationPoints + "积分",
              fontSize: 40,
              fontWeight: "900",
              color: "#e54d42",
              zIndex: 200,
            });
          } else {
            // 积分报名
            if (this.activityDetail.registrationPoints) {
              posterConfig.texts.push({
                x: 420,
                y: 695,
                baseLine: "top",
                text: this.activityDetail.registrationPoints + "积分",
                fontSize: 40,
                fontWeight: "900",
                color: "#e54d42",
                zIndex: 200,
              });
            }

            // 付费报名
            if (this.activityDetail.registrationAmount) {
              posterConfig.texts.push({
                x: 420,
                y: 695,
                baseLine: "top",
                text: this.activityDetail.registrationAmount + "元",
                fontSize: 40,
                fontWeight: "900",
                color: "#e54d42",
                zIndex: 200,
              });
            }
          }
        }
      }

      // 海报样式2不带边框  海报样式1带边框
      // let posterConfig2 = JSON.parse(JSON.stringify(posterConfig));
      this.shareParams = {
        title: "发现一个好物，推荐给你呀",
        desc: this.activityDetail.name,
        imgUrl: this.activityDetail.activityPicUrl,
        scene: this.activityDetail.id,
        page: "pages/memberactivity/detail/index",
        posterConfig,
        trackParams: {
          ...this.trackParams,
          activity_id: this.activityDetail.id,
          activity_name: this.activityDetail.name,
          action_type: "分享",
        },
      };
      this.showShare = true;
    },

    // 处理日历数据
    handleDate(list) {
      // 处理数据并保证日期不重复
      const date = [];
      const dateSet = new Set();

      list.forEach((item) => {
        const activityDate = item.activityDate;
        if (!dateSet.has(activityDate)) {
          date.push({ date: activityDate, id: item.activityId });
          dateSet.add(activityDate);
        }
      });
      console.log("date", date);
      this.activityCalendar = date;
    },

    // 倒计时
    countDownDone() {
      this.getDataList(this.id);
    },

    // 立即报名
    signUp() {
      // isInvitationCode 是否有vip邀请码 1：是；0：否
      if (this.activityDetail.isInvitationCode == "1") {
        this.modalDialog = true;
      } else {
        let registrationData = {
          activity_id: this.activityDetail.id,
          activity_name: this.activityDetail.name,
          registrationMethod: this.activityDetail.registrationMethod, //报名方式
          amount: this.activityDetail.registrationAmount, //报名金额
          points: this.activityDetail.registrationPoints, //报名积分
          activityCalendar: this.activityCalendar, //活动日历
          registrationPeopleOnec: this.activityDetail.registrationPeopleOnec, //单次可报名人数
        };

        uni.setStorageSync("memberactivity", registrationData);
        this.toPage(`/pages/memberactivity/application/index`);
      }
    },

    // 没有邀请码
    notHave() {
      console.log(
        "this.activityDetail.registrationPeopleOnec",
        this.activityDetail,
      );

      let registrationData = {
        activity_id: this.activityDetail.id,
        activity_name: this.activityDetail.name,
        registrationMethod: this.activityDetail.registrationMethod, //报名方式
        amount: this.activityDetail.registrationAmount, //报名金额
        points: this.activityDetail.registrationPoints, //报名积分
        activityCalendar: this.activityCalendar, //活动日历
        registrationPeopleOnec: this.activityDetail.registrationPeopleOnec, //单次可报名人数
      };
      uni.setStorageSync("memberactivity", registrationData);
      this.toPage(`/pages/memberactivity/application/index`);
    },

    //查询VIP邀请码
    async verificationCode() {
      let parameter = {
        activityId: this.activityDetail.id,
        vipInvitationCode: this.vipCode,
      };
      let { data } = await Invitationcode(Object.assign({}, parameter));
      console.log("data", data);
      if (data) {
        let registrationData = {
          activity_id: this.activityDetail.id,
          activity_name: this.activityDetail.name,
          registrationMethod: this.activityDetail.registrationMethod, //报名方式
          amount: data.saleRegistrationAmount, //报名金额
          points: data.saleRegistrationPoints, //报名积分
          vipInvitationCode: data.vipInvitationCode,
          activityCalendar: this.activityCalendar, //活动日历
          registrationPeopleOnec: this.activityDetail.registrationPeopleOnec, //单次可报名人数
        };
        uni.setStorageSync("memberactivity", registrationData);
        this.toPage(`/pages/memberactivity/application/index`);
      } else {
        uni.showToast({
          title: "请输入正确邀请码！",
          icon: "none",
        });
      }
    },

    // 邀请码确认
    confirm() {
      if (this.vipCode) {
        this.verificationCode();
      } else {
        uni.showToast({
          title: "请输入正确邀请码！",
          icon: "none",
        });
      }
    },

    //关闭弹框
    hideModalDialog() {
      this.modalDialog = false;
    },

    // 活动状态
    activityType(key) {
      const statusMap = {
        1: { name: "未开始", disabled: true },
        2: { name: "进行中", disabled: true },
        3: { name: "已结束", disabled: true },
        4: { name: "已报满", disabled: true },
        5: { name: "积分不足", disabled: true },
        6: { name: "已报名", disabled: false },
        7: { name: "立即报名", disabled: false },
      };
      const activityStatus = statusMap[key];
      if (activityStatus) {
        return { name: activityStatus.name, disabled: activityStatus.disabled };
      } else {
        return { name: "未知状态", disabled: false };
      }
    },

    // 会员状态 cust: "101,102,103,108"
    statusType(key) {
      const statusMap = {
        101: { name: "积分卡", color: "blue" },
        102: { name: "金卡", color: "gold" },
        103: { name: "钻石卡", color: "silver" },
        108: { name: "黑钻卡", color: "black" },
        104: { name: "返利卡", color: "green" },
        105: { name: "超市员工卡", color: "purple" },
      };

      const keys = key.split(",");
      const cardInfoList = keys.map((k) => {
        const cardInfo = statusMap[k];
        if (cardInfo) {
          return { name: cardInfo.name, color: cardInfo.color };
        } else {
          return { name: "未知状态卡", color: "gray" };
        }
      });
      return JSON.parse(JSON.stringify(cardInfoList));
    },

    //活动列表
    getDataList(id) {
      memberactivityDetail(id).then((res) => {
        if (res.data) {
          console.log("res.data==22==", res.data);
          if (res.data && res.data.id) {
            senTrack("ActivityPageView", {
              ...this.trackParams,
              activity_id: res.data.id,
              activity_name: res.data.name,
            });
          }
          // res.data.apiStatus = 7
          this.activityDetail = res.data;
          const cards = this.statusType(res.data.cust);
          this.$set(
            this.activityDetail,
            "cards",
            JSON.parse(JSON.stringify(cards)),
          );
          // 处理日历数据未报名做准备
          if (
            res.data.parameterMemberActivitySessionsList &&
            res.data.parameterMemberActivitySessionsList.length > 0
          ) {
            this.handleDate(res.data.parameterMemberActivitySessionsList);
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.collection-types {
  top: unset !important;
}
.btn {
  background-color: rgba(240, 183, 93, 0.***************) !important;
  color: #f87f3a;
  border: none !important;
}
</style>
