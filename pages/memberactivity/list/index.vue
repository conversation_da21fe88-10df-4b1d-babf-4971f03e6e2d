<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">会员活动</block>
    </cu-custom>

    <!-- 余额 -->
      <view 
        class="cu-bar bg-white" 
        id="navTab" 
        :style="{boxShadow:'none'}" 
        style="background-color: #F5F5F7;">
        <view class="relative" style="width: 100%;">
          <!-- 广告 -->
          <view v-if="advertisementInfo" @click="toPage(advertisementInfo.linkUrl)" class="store-item" >
            <view style="padding: 20rpx 20rpx 0 20rpx;position: relative;">
              <image mode="widthFix" style="width: 100%;border-radius: 20rpx;height: 200rpx;"
                :src="advertisementInfo.imgUrl | formatImg750"></image>
            </view>
          </view>

          <view class="flex margin-lr-sm margin-tb-xs">
          <!-- 日历 -->
            <view 
            class="padding-left flex align-unset"
            @click="isShow = true"
            style="
                width: 65%;
                text-align: left;
                color: #FFFFFF;
                height: 90rpx;
                line-height: 90rpx;
                border-radius: 15rpx 0 0 15rpx;
                background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);">
              <veiw class="cuIcon-calendar text-xxl"></veiw>
              <veiw 
                class="margin-left-sm"
                style="
                height: 90rpx;
                line-height: 90rpx;">
                活动日历
              </veiw>
            </view>

            <!-- 我的活动 -->
            <view 
              @click="toPage(`/pages/memberactivity/my-application-list/index`)"
              style="
                width: 35%;
                background: #AF815F;
                border-radius: 0 15rpx 15rpx 0;
                color: #FFFFFF;">
              <view class="padding-left text-xs margin-top-xs">SCHEDULE</view>
              <view class="text-right padding-right-sm">我的活动</view>
            </view>
          </view>

          <!-- tap切换 -->
          <view class="flex text-center" style="background-color: #F5F5F7;">
              <view 
                class="cu-item flex-sub text-lg"
                :class="item.key == tabCur ? 'text-color' : ''"
                style="padding: 0rpx 20rpx; height: 60rpx; line-height: 60rpx;"
                v-for="(item, index) in giftStatus"
                :key="index"
                @tap="tabSelect"
                :data-index="index"
                :data-key="item.key"
              >
              <view>
                {{ item.value }}
                <view v-if="item.key == tabCur" class="line"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 商品信息 -->
      <scroll-view scroll-y :style="{height: `calc(100vh - ${navHeight}px)`}" @scrolltolower="getMore">
        <view class="margin-top margin-sm" v-for="(item, index) in myGiftCardListPages" :key="index">
          <view style="position: relative;">
            <view 
              style="height:420rpx;position: relative;"
              @click="toPage(`/pages/memberactivity/detail/index?id=${item.id}`)">
              <image style="width:100%;height: 100%;"
                :src="item.activityPicUrl" mode="aspectFill" >
              </image>
              <view style="width: 130rpx;height: 127rpx;position: absolute;top: 0;right: 0;">
                <image style="width:100%;height: 100%;"
                  :src="statusType(item.registrationMethod)" mode="aspectFill" >
                </image>
              </view>
            </view>

            <view class="flex justify-around padding-xs margin-top-xs"
                style="height:100%;background: #FFFFFF;margin:0 auto;">
                <view class="padding-lr-sm" style="flex:1;position: relative;">
                  <view class="flex justify-start align-center">
                    <view class="text-black overflow-2" style="width:350rpx;">
                      {{item.name}}
                    </view>
                  </view>

                  <view class="flex justify-between align-end">
                    <view class="text-light-gray text-xs">
                        {{item.activityBeginTime}}-{{item.activityEndTime}}
                    </view>
                    <view class="flex" v-if="item.apiStatus == '2'">
                      <!--头像组件 urls数组 -->
                      <a-avatar-slideshow ref="avatar" :urls="item.headimgList" width="40" height="40" :overlap="10" :interval="interval" :maxDisplayCount="5"> </a-avatar-slideshow>
                      <view class="text-light-gray text-xs margin-top-xs" v-if="item.activityResidualNum>0">
                        余<text style="color: #F87F3A;">{{item.activityResidualNum || 0}}</text>人
                      </view>
                      <view class="text-light-gray text-xs" v-else>
                        已报满
                      </view>
                    </view>

                    <view 
                      v-if="item.apiStatus == '1'&&item.beginSecond>0" 
                      class="text-sm margin-left-xs padding-bottom-xs" 
                      style="color: #FF0000;">
                      距离开始:
                      <!-- 倒计时 -->
                      <count-down v-if="item.beginSecond>=0"
                        class="bg-white round padding-xs text-xs" :textColor="'red'" :connectorColor="'red'" :backgroundColor="'#FFFFFF'"
                        :outTime="item.beginSecond*1000" @countDownDone="countDownDone">
                      </count-down>
                    </view>
                  </view>
    
                      
                  <view class="text-xs" style="position:absolute;top: 0px;right: 0px;">
                    <text class="text-xs margin-right-sm"
                      style="width:350rpx;color: #F87F3A;">
                      {{item.apiStatus | statusType}}
                    </text>
                    <view 
                      @click="toPage(`/pages/memberactivity/detail/index?id=${item.id}`)"
                      style="width: 145rpx;height: 35rpx;;color: #F87F3A;background-color: rgba(240, 183, 93, 0.349019607843137);"
                      class="cu-btn round text-white text-xs"> 
                        查看详情
                    </view>
                  </view>
    
                  <view 
                    @click="toPage(`/pages/memberactivity/appraises/list/index?activityId=${item.id}&type=1`)"
                    v-if="item.apiStatus=='2'||item.apiStatus=='3'" 
                    class="flex align-center text-xs">
                    <view class="text-purple-grey flex justify-between margin-top-xs text-xs">
                      <view class="text-black">活动评价({{item.activityAppraisesNum || 0}})</view>
                    </view>
                    <view 
                      class="text-xs margin-left-sm margin-top-xs">
                        查看全部<text class="cuIcon-right margin-left-xs"></text>
                    </view>
                  </view>
                </view>
            </view>
          </view>
        </view>
        
        <view :class="'cu-load ' + (loadmore ? 'loading' : 'over')"></view>
      </scroll-view>

      <view
        class="cu-modal bottom-modal"
        :class="isShow ? 'show animation-slide-bottom' : ''"
        @tap.stop
      >
        <view
          class="cu-dialog dialo-sku bg-white"
          :class="isShow ? 'animation-slide-bottom' : ''"
          style="height: 50%"
          @tap.stop
        >
          <zeng-calen :actDay="actDay" :chooseDay="chooseDay" @onDayClick='onDayClick' @close="isShow=false"></zeng-calen>
        </view>
      </view>
      
  </view>
</template>

<script>

const app = getApp();
import api from 'utils/api'
import { memberactivityinfo, activityCalendar} from '@/pages/memberactivity/api/memberactivity'
import zengCalen from "../components/zeng-calen/zeng-calen.vue";
import aAvatarSlideshow from "../components/a-avatar-slideshow/a-avatar-slideshow.vue";
import {mapState} from 'vuex'
import { navigateUtil } from "@/static/mixins/navigateUtil.js";
import countDown from "@/components/count-down/index";

export default {
  mixins: [navigateUtil],
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      actDay: [], //用户选择的日期
			chooseDay: [
        // "2024-10-13", "2024-10-15", "2024-10-16", "2024-10-05"
      ], //活动日历数据
      page: {
        apiType: '1',
        current: 1,
        size: 10,
        isShow: 1,
        cust:uni.getStorageSync('user_info').erpCustType
      },
      navHeight: 0,
      loadmore: true,
      userCollect: [],
      //有数统计使用
      page_title: '会员活动',
      advertisementInfo: {},//广告信息
      giftStatus: [
          { value: '精彩活动', key: '1',num:'0' },
          { value: '往期回顾', key: '2',num:'0' }, 
        ],
      tabCur: '1',//默认精彩活动
      priceStyle: "display: flex; justify-content: center; font-weight: bold; align-items: baseline;",
      priceStyle2: "display: flex; justify-content: center; align-items: baseline;",
      myGiftCardListPages:[],//我的礼品卡
      triggered: false, // 下拉加载
      isShow:false,//弹框默认不显示
      interval:2000,
      // 测试组件数据
      headimgList: [
					'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
					'https://img.songlei.com/1/material/ec3b8c7b-d812-4d43-adad-aecf3c57343b.jpg-jpg_w360_q90',
          'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
					'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
          'https://img.songlei.com/1/material/3e4d4561-eb84-4da3-b001-1f0afd275e0b.gif-gif_q90_s80',
					'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
          'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
					'https://img.songlei.com/1/material/b694559a-49bd-4c96-9625-3dacb508e1d3.png',
				],
    };
  },

  components: {
    zengCalen,
    aAvatarSlideshow,
    countDown
  },
  props: {},

  async onLoad (options) {
    if (options) {
      if (options.goodsList) {
        let goodsList = decodeURIComponent(options.goodsList)
        this.userCollect = JSON.parse(goodsList)
      }
    }
    let query = uni.createSelectorQuery()
		query.select('#navTab').boundingClientRect() 
		await query.exec((res) => {
			this.navHeight = res[0].height
		})

    console.log("options===", this.userCollect);
    // MEMBER_ACTIVITY SECKILL_LIST
    this.advertisement("MEMBER_ACTIVITY")
  },

  onShow () { 
    app.initPage().then(res => {
      this.interval = 2000
      this.refresh()
      this.grtActivityCalendarList()
    });
  },

  beforeDestroy() {
    console.log("beforeDestroy---->",);
    this.interval = 0
    this.$nextTick(()=>{
      if (this.$refs.avatar&&this.$refs.avatar.length) {
        this.$refs.avatar.forEach((item)=>{item.stop()})
      }
    })
  },

  onHide() {
    console.log("onHide---->",);
    this.interval = 0
    this.$nextTick(()=>{
      if (this.$refs.avatar&&this.$refs.avatar.length) {
        this.$refs.avatar.forEach((item)=>{item.stop()})
      }
    })
  },

  computed: {
    ...mapState([
			'windowWidth',
			'HeightBar',
			'CustomBar',
			'menuWidth',
			'leftMenuWidth',
			'pixelRatio'
		])
  },

  filters: {
    statusType(key) {
      const statusMap = {
          '1': '未开始',
          '2': '进行中',
          '3': '已结束',
          '4': '已报满',
          '5': '积分不足',
          '6': '已报名',
          '7': '可报名'
      };
        return statusMap[key] || '未知状态';
    }
  },

  methods: {
    // 滚动到底部
		getMore() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
        this.myGiftCardList();
			}
		},

    statusType(key) {
      const statusMap = {
          '1': `${this.$imgUrl('live/method1.png')}`,
          '2': `${this.$imgUrl('live/method2.png')}`,
          '3': `${this.$imgUrl('live/method3.png')}`,
      };
        return statusMap[key];
    },

    // 获取活动日历数据
    async grtActivityCalendarList(){
      const {data} = await activityCalendar(Object.assign({},{cust:uni.getStorageSync('user_info').erpCustType}))
      if (data) {
        console.log("date===>",data);
        const newData = data.map(item => item.date);
        this.chooseDay = newData
      }
    },

    // 倒计时
    countDownDone() {
			this.refresh();
		},
    
    // 展开日历
		onDayClick(data) {
			let choose = data.date //用户点中的数据
      let bol  = this.chooseDay && this.chooseDay.length > 0
      if (!bol) {
          uni.showToast({
              title: "暂无活动日期!",
              icon: "none"
          });
        return
      }
			if (this.actDay.includes(choose)) { //如果用户点击的日期已经存在
				// 移除元素下标
				const index = this.actDay.indexOf(choose);
				//删除用户点击的日期
				this.actDay.splice(index, 1)
			} else if (this.chooseDay.includes(choose)) { //判断是否已经被投标
				console.log("choose===>",choose);
        
        uni.navigateTo({
						url: `/pages/memberactivity/activityTimeList/index?apiType=${this.page.apiType}&activityTime=${choose}`
					});
        this.isShow = false
			} else {
        uni.showToast({
          title: "请选择标记的活动日期!",
					icon: "none"
				})
			}
		},

    //tab切换
    tabSelect (e) {
      let dataset = e.currentTarget.dataset;
      if (dataset.key != this.tabCur) {
        this.tabCur = dataset.key;
        this.page.apiType = dataset.key;
        this.refresh();
      }
    },

    //我的活动列表
    myGiftCardList(){
      memberactivityinfo(Object.assign({},this.page)).then((res)=>{
        console.log("memberactivityinfo==res==>",res);
        if (res.data) {
          if (res.data.records) {
            let myGiftCardListPages=res.data.records
            this.myGiftCardListPages = [...this.myGiftCardListPages, ...myGiftCardListPages];
            if (myGiftCardListPages.length < this.page.size) {
              this.loadmore = false;
            }
          }else{
            this.loadmore = false;
          }
        }
      })
    },

    //广告
		advertisement(id) {
			api.advertisementinfo({
				bigClass: id
			}).then(res => {
				this.advertisementInfo = res.data;
			});
		},

    refresh () {
      this.loadmore = true;
      this.myGiftCardListPages = [];
      this.page.current = 1;
      this.myGiftCardList();
    },
  }
};
</script>
<style  scoped>
.text-color{
  color: #F87F3A
}

.line{
    width: 108rpx;
    height: 4rpx;
    background: #F87F3A;
    border-radius: 2rpx;
    margin: 0px auto;
}
</style>
