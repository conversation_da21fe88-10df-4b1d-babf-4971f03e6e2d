<template>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">申请退款</block>
    </cu-custom>

    <view
      class="bg-white"
      style="margin-bottom: 100rpx;"
    >
      <view class="cu-list menu-avatar">
        <view class="flex padding bg-white align-center solid-bottom justify-between">
          <view 
            class="cu-avatar"
            style="width: 256rpx;height: 160rpx;"
            :style="'background-image:url(' + (refundInfo.memberActivityInfo.activityPicUrl ? refundInfo.memberActivityInfo.activityPicUrl : 'https://img.songlei.com/live/img/no_pic.png') + ');'"
          >
          </view>

          <view class="margin-left-sm text-right">
            <view class="text-black text-df overflow-2">{{refundInfo.memberActivityInfo.name}}</view>
            <view class="text-gray text-sm text-cut margin-top-xs">{{refundInfo.memberActivitySessions}}</view>
          </view>
        </view>

        <view class="flex justify-between align-center margin-lr-sm padding-tb">
          <view class="text-black" >
            <view class="overflow-2 text-bold">退款数量</view>
            <view class="text-xsm ">最多可退{{ refundInfo.usableNum || 0}}张</view>
          </view>
          <view class="text-black" >
            <base-stepper 
              :reduceBtnStyle="`border: 2rpx solid #ccc;padding: 0 10rpx;`"
              :addBtnStyle="`border: 2rpx solid #ccc;padding: 0 10rpx;`"
              :inputStyle="`padding-right:0rpx;`"
              :stNum="refundInfo.peopleNum"
              :min="1"
              :max="refundInfo.usableNum"
              @numChange="cartNumChang($event,item)"
              :data-index="index">
            </base-stepper>
          </view>
        </view>

        <view class="flex justify-between align-center margin-lr-sm padding-tb">
          <view class="text-black text-bold" >
            退还积分
          </view>
          <view class="text-red" >
            {{ refundInfo.paymentPoints }}
          </view>
        </view>

        <view class="flex justify-between align-center margin-lr-sm padding-tb">
          <view class="text-black text-bold" >
            退还金额
          </view>
          <view class="text-red" >
            ￥{{ refundInfo.paymentPrice }}
          </view>
        </view>
      </view>
    </view>

    <view class="cu-bar bg-white justify-center foot">
      <button
        class="cu-btn round shadow-blur lg"
        :class="'bg-'+theme.themeColor"
        style="width: 90%;"
        @tap="subAppraises"
      >提交</button>
    </view>
  </view>
</template>

<script>
	const app = getApp();
  import {refund} from '@/pages/memberactivity/api/memberactivity';
  import baseStepper from "components/base-stepper/index";
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				goodsAppraises: [],
        refundInfo:{},
				//有数统计使用
				page_title:'申请退款',
        totalPrice:0,//总价格
        totalPoints:0,//总积分
        totalUsableNum:0,//总件数
			};
		},

  components: {
    baseStepper
  },

  onShow () { },

  onLoad (options) {
    app.initPage().then(res => {
      this.refundInfo = uni.getStorageSync('refundInfo')
      this.totalPrice = uni.getStorageSync('refundInfo').paymentPrice
      this.totalPoints = uni.getStorageSync('refundInfo').paymentPoints
      this.totalUsableNum = uni.getStorageSync('refundInfo').usableNum
    });
  },

  methods: {
    // 数量变化
		cartNumChang(val, item) {
      console.log("cartNumChang",val, item);
      this.refundInfo.peopleNum = val
      if(this.refundInfo.paymentPrice){
        // 计算总价格（以分为单位）
        const priceInCents = this.totalPrice * 100
        // 计算每个人的平均价格
        const averagePrice = priceInCents / this.totalUsableNum;
        let totalPriceInCents = averagePrice * val;
        // 将总价格转换为正确的价格格式（美元）
        let totalPrice = (totalPriceInCents / 100).toFixed(2);
        // 如果总价格是整数，去掉小数部分
        if (totalPrice.endsWith('.00')) {
            totalPrice = totalPrice.split('.')[0];
        }
        this.refundInfo.paymentPrice = totalPrice
      }

      if(this.refundInfo.paymentPoints){
        // 计算每个人的平均积分
        const points = this.totalPoints / this.totalUsableNum;
        let totalPoints = points * val;
        this.refundInfo.paymentPoints = totalPoints;
      }	
		},

    subAppraises () {
      let that = this;
      console.log("this.refundInfo",this.refundInfo);
      
      uni.showModal({
        title:'温馨提示',
        content: '确认提交退款申请吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',
        success (res) {
          let obj = {
            orderId: that.refundInfo.id,
            refundQuantity: that.refundInfo.peopleNum,
          }
          if (res.confirm) {
            refund(obj).then(res => {
              uni.navigateBack({
                delta: 1
              });
            });
          }
        }
      });
    },
  }
};
</script>
<style>
/* .goods-detail {
  width: 74%;
} */
</style>
