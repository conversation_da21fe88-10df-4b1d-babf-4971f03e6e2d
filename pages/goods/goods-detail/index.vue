<template>
  <view class="padding-bottom">
    <view v-if="scene != '1154'">
      <cu-custom :bgColor="bgColor" :isBack="true" :hideMarchContent="true">
        <block slot="backText">返回</block>
        <block slot="content">
          <text
            :style="{
              color: pageScrollTop > 20 ? '#fff' : 'transparent',
            }"
          >
            商品详情
          </text>
        </block>
      </cu-custom>
      <view
        v-if="!loading"
        :style="{
          marginTop: `-${CustomBar}px`,
        }"
      >
        <view class="product-bg bg-white">
          <view class="markUrlsList" v-if="goodsSpu.groupMarkUrl">
            <image
              class="imgbox"
              v-for="(item, index) in goodsSpu.groupMarkUrl"
              :key="index"
              :src="item"
            ></image>
          </view>
          <!-- 商品顶部轮播 -->
          <swiper
            class="screen-swiper"
            circular="true"
            duration="500"
            :current="currents"
            @change="change"
            :style="{
              height:
                (currents == 0 ||
                  (goodsSpu.videoAddress &&
                    (currents == 0 || currents == 1))) &&
                isMoreHeight
                  ? `calc(100vw + ${isPhone ? '110rpx' : '50px'})`
                  : '100vw',
            }"
          >
            <swiper-item
              v-for="(item, index) in swperPicUrl"
              :key="index"
              @click.stop="handlePreviewImage(index)"
            >
              <over-view
                v-if="
                  (((index == 1 || index == 0) && goodsSpu.videoAddress) ||
                    index == 0) &&
                  isMoreHeight
                "
                class="swiper-img"
                :style="{
                  height: `calc(100vw + ${isPhone ? '110rpx' : '50px'})`,
                }"
              >
                <!--这种裁剪的方式 ios不支持 borderImage: `url(${spuPicPriceBg})  20 25 64 25 fill` 
								style="border: 10px solid transparent; border-image-slice: 20 15 64 25; border-image-width: 20px 15px 64px 25px; border-image-repeat: stretch"-->
                <view
                  :style="{
                    top: spuPriceStyle == 1 ? 0 : 'auto',
                    height: spuPriceStyle == 1 ? '100%' : '160rpx',
                    backgroundSize: '100% 100%',
                    backgroundImage: `url(${spuPicPriceBg})`,
                    backgroundRepeat: 'no-repeat',
                  }"
                  class="img-bg"
                ></view>

                <view class="img-price font-xmd">
                  <view class="img-price-box ml-sm">
                    <view
                      class="img-price-view ml-xxxmd"
                      :style="{
                        marginBottom: '2rpx',
                        color: spuPriceColorLeft,
                      }"
                      >预估到手价</view
                    >
                    <format-price
                      styleProps="font-weight:bold; display:flex; align-items:baseline; line-height:1;"
                      :color="spuPriceValueColorLeft"
                      signFontSize="30rpx"
                      smallFontSize="34rpx"
                      priceFontSize="60rpx"
                      :price="
                        goodsSpu.estimatedPriceVo &&
                        goodsSpu.estimatedPriceVo.estimatedPrice
                      "
                    ></format-price>
                  </view>
                  <view
                    class="img-price-box3 font-xmd"
                    style="text-algin: right"
                    :style="{ color: spuPriceColorRight }"
                    >=</view
                  >
                  <view
                    :class="
                      goodsSpu.estimatedPriceVo &&
                      goodsSpu.estimatedPriceVo.originalPrice == '0.0' &&
                      goodsSpu.estimatedPriceVo.coupon == '0.0'
                        ? 'img-price-box4'
                        : 'img-price-box2'
                    "
                  >
                    <view
                      class="img-price-view"
                      :style="{ color: spuPriceColorRight }"
                      >总价格</view
                    >
                    <view class="flex align-center justify-center">
                      <format-price
                        styleProps="font-weight:bold; display:flex; align-items:baseline;"
                        :color="spuPriceValueColorRight"
                        signFontSize="24rpx"
                        smallFontSize="24rpx"
                        priceFontSize="40rpx"
                        :price="goodsSpu.estimatedPriceVo.originalPrice"
                      ></format-price>
                    </view>
                  </view>

                  <view
                    class="img-price-box3 font-xmd"
                    :style="{
                      color: spuPriceColorRight,
                    }"
                    v-if="
                      goodsSpu.estimatedPriceVo &&
                      goodsSpu.estimatedPriceVo.promotionsDiscountPrice !=
                        '0.0' &&
                      goodsSpu.estimatedPriceVo.promotionsDiscountPrice != '0'
                    "
                  >
                    -
                  </view>
                  <view
                    v-if="
                      goodsSpu.estimatedPriceVo &&
                      goodsSpu.estimatedPriceVo.promotionsDiscountPrice !=
                        '0.0' &&
                      goodsSpu.estimatedPriceVo.promotionsDiscountPrice != '0'
                    "
                    :class="
                      goodsSpu.estimatedPriceVo &&
                      goodsSpu.estimatedPriceVo.promotionsDiscountPrice == '0.0'
                        ? 'img-price-box4'
                        : 'img-price-box2'
                    "
                  >
                    <view
                      class="img-price-view"
                      :style="{
                        color: spuPriceColorRight,
                      }"
                    >
                      折扣活动
                    </view>
                    <view class="flex align-center justify-center">
                      <format-price
                        styleProps="font-weight:bold; display:flex; align-items:baseline;"
                        :color="spuPriceValueColorRight"
                        signFontSize="24rpx"
                        smallFontSize="24rpx"
                        priceFontSize="40rpx"
                        :price="
                          goodsSpu.estimatedPriceVo &&
                          goodsSpu.estimatedPriceVo.promotionsDiscountPrice
                        "
                      ></format-price>
                    </view>
                  </view>

                  <view
                    class="img-price-box3 font-xmd"
                    :style="{
                      color: spuPriceColorRight,
                    }"
                    v-if="
                      goodsSpu.estimatedPriceVo.discountPrice != '0.0' &&
                      goodsSpu.estimatedPriceVo.discountPrice != '0'
                    "
                  >
                    -
                  </view>
                  <view
                    v-if="
                      goodsSpu.estimatedPriceVo.discountPrice != '0.0' &&
                      goodsSpu.estimatedPriceVo.discountPrice != '0'
                    "
                    :class="
                      goodsSpu.estimatedPriceVo &&
                      goodsSpu.estimatedPriceVo.discountPrice == '0.0' &&
                      goodsSpu.estimatedPriceVo.coupon == '0.0'
                        ? 'img-price-box4'
                        : 'img-price-box2'
                    "
                  >
                    <view
                      class="img-price-view"
                      :style="{
                        color: spuPriceColorRight,
                      }"
                    >
                      折扣减免
                    </view>

                    <view class="flex align-center justify-center">
                      <format-price
                        styleProps="font-weight:bold; display:flex; align-items:baseline;"
                        :color="spuPriceValueColorRight"
                        signFontSize="24rpx"
                        smallFontSize="24rpx"
                        priceFontSize="40rpx"
                        :price="
                          goodsSpu.estimatedPriceVo &&
                          goodsSpu.estimatedPriceVo.discountPrice
                        "
                      ></format-price>
                    </view>
                  </view>
                  <view
                    class="img-price-box3 font-xmds"
                    :style="{
                      color: spuPriceColorRight,
                    }"
                    v-if="
                      goodsSpu.estimatedPriceVo &&
                      goodsSpu.estimatedPriceVo.coupon != '0.0' &&
                      goodsSpu.estimatedPriceVo.coupon != '0'
                    "
                  >
                    -
                  </view>
                  <view
                    :class="
                      goodsSpu.estimatedPriceVo &&
                      goodsSpu.estimatedPriceVo.price == '0.0' &&
                      goodsSpu.estimatedPriceVo.coupon == '0.0'
                        ? 'img-price-box4'
                        : 'img-price-box2'
                    "
                    v-if="
                      goodsSpu.estimatedPriceVo.coupon != '0.0' &&
                      goodsSpu.estimatedPriceVo.coupon != '0'
                    "
                  >
                    <view
                      class="img-price-view"
                      :style="{
                        color: spuPriceColorRight,
                      }"
                    >
                      优惠券
                    </view>
                    <view class="flex align-center justify-center">
                      <format-price
                        styleProps="font-weight:bold; display:flex; align-items:baseline;"
                        :color="spuPriceValueColorRight"
                        signFontSize="24rpx"
                        smallFontSize="24rpx"
                        priceFontSize="40rpx"
                        :price="goodsSpu.estimatedPriceVo.coupon"
                      ></format-price>
                    </view>
                  </view>
                </view>
              </over-view>

              <!-- 视频组件 -->
              <view
                v-if="index == 0 && goodsSpu.videoAddress"
                class="videoSwiper"
              >
                <video
                  id="myVideo"
                  v-if="playVideo"
                  :src="goodsSpu.videoAddress"
                  :show-center-play-btn="false"
                  :show-play-btn="true"
                  @ended="onVideoEnded"
                  @play="handleVideoBeginPlay"
                  @pause="isPlaying = false"
                  :controls="false"
                  :autoplay="goodsSpu.isAutoPlay == 1"
                  :muted="muted"
                  show-mute-btn
                  :picture-in-picture-mode="['push', 'pop']"
                >
                  <cover-view
                    class="videoBtn"
                    v-if="goodsSpu.videoAddress"
                    style="width: 100%; height: 100%"
                  >
                    <cover-image
                      :src="
                        !muted
                          ? 'http://slshop-file.oss-cn-beijing.aliyuncs.com/client/mp/yinliang1.png'
                          : 'http://slshop-file.oss-cn-beijing.aliyuncs.com/client/mp/jingyin1.png'
                      "
                      class="voiceBtn"
                      style="width: 44rpx; height: 44rpx"
                    ></cover-image>
                  </cover-view>
                </video>
                <view class="parent-container" v-else>
                  <image :src="item | formatImg750" class="video-cover"></image>
                  <image
                    :src="
                      !playVideo
                        ? 'http://slshop-file.oss-cn-beijing.aliyuncs.com/client/mp/bofang.png'
                        : 'http://slshop-file.oss-cn-beijing.aliyuncs.com/client/mp/zanting.png'
                    "
                    class="playBtn"
                    style="width: 180rpx; height: 180rpx; position: absolute"
                    :style="{ opacity: playVideo ? 0.2 : 1 }"
                  ></image>
                </view>
              </view>

              <image
                v-else
                :class="
                  ((goodsSpu &&
                    goodsSpu.estimatedPriceVo &&
                    goodsSpu.estimatedPriceVo.discountPrice &&
                    goodsSpu.estimatedPriceVo.discountPrice != '0.0' &&
                    goodsSpu.estimatedPriceVo.discountPrice != '0') ||
                    (goodsSpu.estimatedPriceVo &&
                      goodsSpu.estimatedPriceVo.promotionsDiscountPrice &&
                      goodsSpu.estimatedPriceVo.promotionsDiscountPrice !=
                        '0.0' &&
                      goodsSpu.estimatedPriceVo.promotionsDiscountPrice !=
                        '0') ||
                    (goodsSpu.estimatedPriceVo &&
                      goodsSpu.estimatedPriceVo.coupon &&
                      goodsSpu.estimatedPriceVo.coupon != '0.0' &&
                      goodsSpu.estimatedPriceVo.coupon != '0')) &&
                  spuPriceStyle == 2
                    ? 'img-border'
                    : ''
                "
                :style="{
                  borderColor,
                }"
                :src="item | formatImg750"
                mode="aspectFill"
              ></image>

              <!-- 立减标识 -->
              <view
                class="reduce-layout"
                v-if="
                  goodsSpu.showDiscount != 0 &&
                  goodsSpu.estimatedPriceVo &&
                  goodsSpu.estimatedPriceVo.totalDiscountPrice > 0
                "
                :style="{
                  top: CustomBar + 10 + 'px',
                  backgroundImage: `url(${reductionGoodsBg})`,
                }"
              >
                <view
                  class="flex align-center justify-center reduce-tag font-xxl line-height-1 mt-sm"
                  >立减</view
                >
                <view
                  class="flex align-baseline justify-center reduce-price-content"
                >
                  <text class="pt-xs">￥</text>
                  <text class="reduce-price line-height-1 mt-sm text-bold">{{
                    totalDiscountPrice | rounding
                  }}</text>
                </view>
              </view>
            </swiper-item>
          </swiper>
          <!-- 模拟淘宝展示视频或者图片切换的按钮 -->
          <view
            v-if="goodsSpu.videoAddress"
            class="video-img-switch flex align-center font-md"
            :class="
              (goodsSpu.videoAddress && (currents == 0 || currents == 1)) ||
              currents == 0
                ? ''
                : 'isImg'
            "
          >
            <text
              @tap.stop="handleSwiperChange(0)"
              class="plr-md padding-tb-xs"
              :class="goodsSpu.videoAddress && currents == 0 ? 'selected' : ''"
              >视频</text
            >
            <text
              @tap.stop="handleSwiperChange(1)"
              class="plr-md padding-tb-xs"
              :class="goodsSpu.videoAddress && currents == 0 ? '' : 'selected'"
              >图片</text
            >
          </view>
        </view>

        <view
          class="bg-white detail-price-layout ptb-xs"
          :style="{
            backgroundImage: `url(${spuDownPicPriceBg})`,
          }"
        >
          <view
            v-if="goodsSpu.spuType == 3 || goodsSpu.spuType == 5"
            class="text-white margin-top-xs pl-sm"
          >
            <text class="text-lg text-bold"
              >积分：{{ goodsSpu.goodsPoints ? goodsSpu.goodsPoints : 0 }}</text
            >
          </view>

          <!-- 新客专享 -->
          <view
            v-else-if="
              goodsSpu.newCustomerGoods &&
              goodsSpu.newCustomerGoods.newPersonPrice
            "
          >
            <view
              class="text-lg plr-md"
              style="display: flex; align-items: center"
            >
              <format-price
                color="#fff"
                signFontSize="30rpx"
                smallFontSize="34rpx"
                priceFontSize="60rpx"
                :price="goodsSpu.newCustomerGoods.newPersonPrice"
              />
              <text class="text-bold text-white" style="margin-right: 10rpx"
                >（ 新客专享 ）</text
              >
            </view>
          </view>

          <!-- 普通商品 1:实物商品 2:虚拟商品 3:积分商品 5:付费优惠券 6:礼品卡-->
          <view v-else class="flex align-center pl-sm">
            <format-price
              styleProps="font-weight:bold; display:flex; align-items:baseline;"
              color="#fff"
              signFontSize="30rpx"
              smallFontSize="34rpx"
              priceFontSize="60rpx"
              :price="
                goodsSpu.estimatedPriceVo &&
                goodsSpu.estimatedPriceVo.estimatedPrice
              "
            ></format-price>
            <view
              v-if="preferentialPrice"
              class="flex align-baseline plr-md bg-white radius-sm text-bold ml-xmd"
              style="color: #ff362d; height: fit-content"
              :style="{
                backgroundColor: spuFavorablePriceBg,
                color: spuFavorablePriceColor,
              }"
            >
              <text class="font-xxmd pr-ss">省</text>
              <format-price
                styleProps="font-weight:bold; display:flex; align-items:baseline;"
                :color="spuFavorablePriceColor"
                signFontSize="20rpx"
                smallFontSize="24rpx"
                priceFontSize="40rpx"
                :price="preferentialPrice"
              ></format-price>
            </view>
          </view>

          <view
            class="flex align-center pl-sm text-white font-md pt-xs"
            style="opacity: 0.8"
          >
            <text
              v-if="
                goodsSpu.estimatedPriceVo &&
                goodsSpu.estimatedPriceVo.originalPrice > 0
              "
              >￥{{ goodsSpu.estimatedPriceVo.originalPrice }}</text
            >
            <text
              class="pl-xxxsm"
              v-if="
                goodsSpu.estimatedPriceVo &&
                goodsSpu.estimatedPriceVo.originalPrice > 0
              "
              >总价格</text
            >
            <view
              class="mlr-xxxsm"
              v-if="
                goodsSpu.estimatedPriceVo &&
                goodsSpu.estimatedPriceVo.originalPrice > 0
              "
              style="width: 1px; height: 14rpx; background-color: #fff"
            ></view>
            <text>已售{{ goodsSpu.saleNum }}</text>
          </view>
        </view>

        <!-- 分割线第一部分模块 -->
        <view class="plr-md bg-white pb-md">
          <!-- 是否分销员 -->
          <view
            v-if="
              goodsSpu.isDistributionUser == '1' && goodsSpu.rebateAmount > 0
            "
            class="pt-md flex justify-between align-center"
          >
            <view class="flex align-center">
              <text class="font-xxxxsm text-black pr-ss">分享最高可赚:</text>
              <view
                v-if="checkSpecVal && goodsSpu.skus && goodsSpu.skus.length"
              >
                <view v-for="(item, index) in goodsSpu.skus" :key="index">
                  <view v-if="item.specs && item.specs.length">
                    <view
                      v-for="(specItem, specIndex) in item.specs"
                      :key="specIndex"
                    >
                      <format-price
                        v-if="specItem.specValueId == checkSpecVal"
                        signFontSize="24rpx"
                        smallFontSize="30rpx"
                        priceFontSize="30rpx"
                        :price="item.rebateAmount"
                      />
                    </view>
                  </view>
                </view>
              </view>
              <format-price
                v-else
                signFontSize="24rpx"
                smallFontSize="30rpx"
                priceFontSize="30rpx"
                :price="goodsSpu.rebateAmount || '0.00'"
              />
            </view>
            <view class="flex align-center justify-end">
              <view
                style="font-size: 22rpx; color: #000000; margin-right: 10rpx"
                >提示：自买也可以返佣金</view
              >
              <view
                @click="shareShowFun"
                class="plr-xxxmd ptb-xxs"
                style="
                  background: #f2a844;
                  border-radius: 25rpx;
                  color: white;
                  line-height: 100%;
                "
                >分享</view
              >
            </view>
          </view>

          <!-- 母码一样的产品 -->
          <view
            class="cu-bar bg-white pt-md myBar"
            v-if="oldSimilarSpuList && oldSimilarSpuList.length"
          >
            <view class="flex align-center response justify-between">
              <scroll-view
                scroll-x
                style="flex: 1; overflow: hidden; white-space: nowrap"
              >
                <view class="flex" style="display: inline-flex">
                  <view
                    style="background: #f9f7fc; display: inline-flex"
                    class="align-center padding-right radius-sm font-md text-orange overflow-1 text-orange mr-xxsm"
                    v-for="itemGood in oldSimilarSpuList"
                    :class="[
                      itemGood.spuId == goodsSpu.id
                        ? 'similarSelect'
                        : 'similarDefault',
                    ]"
                    @click="changeOtherGoods(itemGood.spuId, false)"
                  >
                    <image
                      class="similarSpu"
                      :src="itemGood.picUrl"
                      mode="aspectFill"
                    ></image>
                    <view
                      class="overflow-1 font-xmd plr-xsm overflow-1"
                      style="max-width: 60vw"
                    >
                      {{ itemGood.name }}
                    </view>
                  </view>
                </view>
              </scroll-view>

              <view
                class="text-black text-right font-xmd"
                style="min-width: 80px"
                @tap="showModalSku"
              >
                {{ goodsSpu.similarSpuList.length }}款可选
                <text class="cuIcon-right text-88 font-md ml-xs"></text>
              </view>
            </view>
          </view>

          <!-- 可横向滑动的优惠券列表 -->
          <view
            v-if="
              goodsSpu.spuType !== 3 &&
              couponInfoList != null &&
              couponInfoList &&
              couponInfoList.length > 0 &&
              goodsSpu.spuType !== 5
            "
            class="pt-xxxsm product-bg"
          >
            <view class="cu-bar myBar">
              <view class="flex justify-between align-center response">
                <scroll-view
                  scroll-x
                  style="width: 640rpx; overflow: hidden; white-space: nowrap"
                >
                  <view class="flex" style="display: inline-flex">
                    <view
                      style="
                        background: #ffe8e6;
                        display: inline-block;
                        border-radius: 6rpx;
                      "
                      class="ptb-sss plr-xxxsm font-md text-orange overflow-1 text-orange mr-xsm"
                      v-for="coupon in couponInfoList"
                    >
                      {{ coupon.name }}
                    </view>
                  </view>
                </scroll-view>

                <view
                  class="text-orange text-right font-md"
                  style="min-width: 90rpx"
                  @tap="showModalCoupon"
                >
                  领券
                  <text class="cuIcon-right ml-xs"></text>
                </view>
              </view>
            </view>
          </view>

          <!-- 商品名称 分享 -->
          <view class="flex justify-between bg-white align-center pt-md">
            <view class="cu-bar myBar">
              <!-- <view> -->
              <!-- 新需求没了 -->
              <!-- <class-label v-for="(item, index_) in titleLable" :key="index_" :text="item.text" :value="item.value" :marginRight="20"></class-label> -->
              <text
                class="text-black text-bold font-lg"
                @longpress="copyText"
                >{{ goodsSpu.name }}</text
              >
              <!-- 新需求没了 -->
              <!-- <class-label
									v-if="goodsSpu.presaleState && goodsSpu.presaleType === '1'"
									:text="goodsSpu.presaleState"
									value="4"
									:marginBottom="0"
									:padding="[5, 10]"
									:fontSize="26"
									:marginLeft="10"
								></class-label> -->
              <!-- </view> -->
            </view>

            <view
              @click="shareShowFun"
              class="flex justify-center flex-direction align-center"
              style="min-width: 70rpx"
            >
              <image
                class="size-lg"
                src="https://img.songlei.com/live/goods/share.png"
              ></image>
              <text class="text-orange font-md mt-s">分享</text>
            </view>
          </view>

          <!--之前的需求 进店铺图 -->
          <!--  style="margin: 20rpx;display: block;border-radius: 30rpx;" -->
          <!-- 	<view v-if="goodsSpu.storeBgImgUrl" class="bg-white" style="padding: 20rpx; display: block; margin: 1rpx 0" @click="handleToShop">
					<view class="storeBgImgUrl" v-if="goodsSpu.storeBgImgUrl">
						<image :src="goodsSpu.storeBgImgUrl | formatImg750"></image>
					</view>
				</view> -->

          <!-- 优惠 -->
          <view
            class="pt-xxsm"
            v-if="
              goodsSpu.pointsGiveSwitch == '1' ||
              goodsSpu.pointsDeductSwitch == '1' ||
              goodsSpu.promotionsInfoSwitch == '1' ||
              goodsSpu.freeFreightSwitch == '1'
            "
          >
            <view class="flex response">
              <view class="label_goodsChoice font-xxmd">
                <view>优惠</view>
              </view>
              <view class="flex-four font-md">
                <view
                  class="flex align-center layout-discount"
                  v-if="
                    goodsSpu.promotionsInfos &&
                    goodsSpu.promotionsInfos.length > 0
                  "
                >
                  <text
                    class="light radius font-md text-orange line-height-1 plr-xsm ptb-s"
                    style="background: #ffe8e6"
                    >折扣活动</text
                  >
                  <view
                    v-for="(item, index) in goodsSpu.promotionsInfos"
                    :key="index"
                  >
                    <text class="ml-xxxs font-xmd goods-tip"
                      >{{ item.discount }}折优惠</text
                    >
                  </view>
                </view>

                <view
                  v-if="goodsSpu.pointsDeductSwitch == '1'"
                  class="flex align-center justify-between"
                  :class="{
                    'mb-xxxs': goodsSpu.freeFreightSwitch == '1' && isMoreShow,
                    'mt-xxxs':
                      goodsSpu.promotionsInfos &&
                      goodsSpu.promotionsInfos.length > 0,
                  }"
                >
                  <view>
                    <text
                      class="light radius font-md text-orange line-height-1 plr-xsm ptb-s"
                      style="background: #ffe8e6"
                      >积分抵扣</text
                    >
                    <text class="ml-xxxs font-xmd goods-tip"
                      >每2000积分抵扣20元</text
                    >
                  </view>
                  <view
                    v-if="
                      goodsSpu.freeFreightSwitch == '1' &&
                      goodsSpu.promotionsInfos &&
                      goodsSpu.promotionsInfos.length > 0
                    "
                    @click="toggleMoreShow"
                    class="cuIcon-right text-orange"
                    style="transition: transform 0.3s ease"
                    :class="{ arrowDown: isMoreShow }"
                  ></view>
                </view>

                <view
                  v-if="goodsSpu.freeFreightSwitch == '1' && isMoreShow"
                  class="flex align-center"
                >
                  <text
                    class="light radius font-md text-orange line-height-1 plr-xsm ptb-s"
                    style="background: #ffe8e6"
                    >包邮</text
                  >
                  <text class="ml-xxxs font-xmd goods-tip">{{
                    goodsSpu.freeFreightMessage
                  }}</text>
                </view>
              </view>
            </view>
          </view>

          <view class="cu-bar myBar pt-xxsm" v-if="goodsSpu.specType == '1'">
            <view class="flex response" @tap="showModalSku">
              <view class="label_goodsChoice font-xxmd">选择</view>
              <view class="flex-treble font-xmd text-black">
                <view
                  class="display-ib"
                  v-for="(item, index) in goodsSpecData"
                  :key="index"
                >
                  <view class="display-ib" v-if="!item.checked">{{
                    item.value
                  }}</view>
                  <view
                    class="display-ib"
                    v-if="item.checked"
                    v-for="(item2, index2) in item.leaf"
                    :key="index2"
                  >
                    <view class="display-ib" v-if="item.checked == item2.id">{{
                      item2.value
                    }}</view>
                  </view>
                  <view
                    class="display-ib"
                    v-if="goodsSpecData.length != index + 1"
                    >,</view
                  >
                </view>
                <view
                  class="colorClass"
                  v-if="
                    goodsSpu.skus && goodsSpu.skus[0] && goodsSpu.skus[0].picUrl
                  "
                >
                  <image
                    class="size-xxxl ml-sm"
                    v-for="(item, index) in goodsSpu.skus.slice(0, 4)"
                    :key="index"
                    :src="item.picUrl"
                    mode="widthFix"
                  ></image>
                  <class-label
                    :text="
                      '共' +
                      (goodsSpu.skus && goodsSpu.skus.length
                        ? goodsSpu.skus.length
                        : 0) +
                      '种分类可选'
                    "
                    value="lightDarkGrey"
                    :marginRight="0"
                    :marginBottom="0"
                    :padding="[18, 18]"
                  ></class-label>
                </view>
              </view>
              <text class="cuIcon-more margin-right-sm"></text>
            </view>
          </view>

          <view class="cu-bar myBar pt-xxsm">
            <!-- 自提 -->
            <view
              class="flex response"
              v-if="
                goodsSpu.distributionMode &&
                goodsSpu.distributionMode.length == 1 &&
                goodsSpu.distributionMode.indexOf('1') != -1
              "
            >
              <view class="label_goodsChoice font-xxmd">发货</view>
              <view class="flex-treble font-xmd goods-tip">
                <text>自提</text>
              </view>
            </view>
            <view class="flex response align-center" v-else>
              <view class="label_goodsChoice font-xxmd">发货</view>
              <view class="flex-treble font-xmd goods-tip">
                <text
                  class="cuIcon-location text-black"
                  v-if="goodsSpu.deliveryPlace"
                  >{{ goodsSpu.deliveryPlace.place }} |</text
                >
                <!-- <text v-if="goodsSpu.freightTemplat && (goodsSpu.distributionMode && goodsSpu.distributionMode.indexOf('0')!=-1)">运费：{{
										  goodsSpu.freightTemplat.type == '2'
											? '全国包邮'
											: '￥' + goodsSpu.freightTemplat.firstFreight
										}}</text> -->
                <text v-if="goodsSpu.freightMsg && goodsSpu.freightMsg != null">
                  {{ goodsSpu.freightMsg }}
                </text>
                <!-- <text v-if="goodsSpu.freightTemplat && (goodsSpu.distributionMode && goodsSpu.distributionMode.indexOf('2')!=-1)">同城配送费：{{
									  goodsSpu.freightTemplat.type == '2'
										? '免配送费'
										: '￥' + goodsSpu.freightTemplat.firstFreight
									}}</text> -->
              </view>
              <!-- <view class="flex-sub font-md text-gray text-right margin-right">销量{{goodsSpu.saleNum?goodsSpu.saleNum:0}}</view> -->
            </view>
          </view>

          <view class="cu-bar myBar pt-xsm" v-if="ensureList.length > 0">
            <view class="flex response align-center" @tap="showModalService">
              <view class="label_goodsChoice font-xxmd">保障</view>
              <view class="flex-treble font-xmd goods-tip">
                <text
                  >{{ ensureList[0].name
                  }}{{ ensureList[1] ? " | " + ensureList[1].name : "" }}</text
                >
              </view>
              <text class="cuIcon-more goods-tip"></text>
            </view>
          </view>

          <!-- 排行榜 -->
          <navigator
            class="pt-md"
            v-if="goodsSpu.rankNum"
            :url="`/pages/goods/sales-rank/index?id=${
              goodsSpu.categorySecond
            },${goodsSpu.categoryThird}&name=${
              goodsSpu.categoryName.split('/')[2]
            }`"
          >
            <view
              class="flex justify-between align-center text-black ptb-xxs plr-xxsm"
              style="background: #fef1e9; border-radius: 6rpx; color: #a86b2b"
            >
              <view class="flex align-center text-bold font-md">
                <image
                  class="size-xxmd mr-xxmd"
                  src="https://img.songlei.com/live/goods/rank-icon.png"
                />
                <view class="font-md"
                  >{{ goodsSpu.categoryName.split("/")[2] }}品类销量排行榜</view
                >
                <view class="ml-xxxsm">
                  第
                  <text class="font-md mlr-ss" style="color: #f85420">{{
                    goodsSpu.rankNum
                  }}</text>
                  名
                </view>
              </view>
              <text
                class="cuIcon-right font-md mr-s"
                style="color: #a86b2b"
              ></text>
            </view>
          </navigator>

          <!--  联名会员图片-->
          <view
            class="pt-md"
            v-if="jionmumberdatas && jionmumberdatas.brandVipConfig"
          >
            <!-- /加入会员的 -->
            <image
              v-if="
                jionmumberdatas &&
                jionmumberdatas.brandVipConfig &&
                jionmumberdatas.brandVipMember
              "
              :src="jionmumberdatas.brandVipConfig.joinPic"
              mode=""
              style="width: 100%; height: 222rpx; border-radius: 30rpx"
              @click="putcur"
            ></image>
            <!-- 未加入会员 -->
            <image
              v-else-if="
                jionmumberdatas &&
                jionmumberdatas.brandVipConfig &&
                jionmumberdatas.brandVipConfig.notJoinPic
              "
              :src="jionmumberdatas.brandVipConfig.notJoinPic"
              mode=""
              style="width: 100%; height: 222rpx; border-radius: 30rpx"
              @click="putcur"
            ></image>
          </view>
        </view>

        <!-- 种草列表 -->
        <!-- <view class="bg-white margin-bottom-xs" style="padding: 20rpx 0 0" v-if="goodsSpu.articleInfos">
					<view class="padding-lr-sm response">
						<view class="label_goodsChoice text-df" style="width: 130rpx">
							<view>种草笔记</view>
						</view>

						<swiper :duration="500" :circular="true" style="height: 145rpx">
							<swiper-item v-for="(item, index_) in goodsSpu.articleInfos" :key="index_">
								<navigator :url="'/pages/article/article-info/index?id=' + item.id" hover-class="none">
									<view class="flex response">
										<view class="content shop-detail align-center">
											<image :src="item.picUrl" mode="aspectFit"></image>
										</view>

										<view class="flex-four text-df">
											<view>
												<text class="text-black margin-left-xs text-df">
													{{ item.articleTitle }}
												</text>
											</view>

											<view>
												<text class="text-xs margin-left-xs lines-gray">
													{{ item.articleExtension }}
												</text>
											</view>

											<view>
												<text class="text-xs margin-left-xs lines-gray">{{ item.authorName }}</text>
												<text class="text-xs margin-left-lg lines-gray">{{ item.pageview || 0 }}人看过</text>
											</view>
										</view>
									</view>
								</navigator>
							</swiper-item>
						</swiper>
					</view>
				</view> -->

        <!-- 分割线 -->
        <view
          v-if="goodsAppraises.total > 0"
          class="height-xxs"
          style="background: #f6f7fc"
        ></view>

        <!-- 分割线第二部分模块 -->
        <view class="plr-md bg-white pb-sm">
          <!-- 商品评价 -->
          <view v-if="goodsAppraises.total > 0">
            <view class="cu-bar">
              <view class="flex response">
                <view class="flex-sub font-xxmd">
                  <view class="text-black text-bold"
                    >评价（{{
                      goodsAppraises.total ? goodsAppraises.total : 0
                    }}）</view
                  >
                </view>
                <navigator
                  :url="'/pages/appraises/list/index?spuId=' + id"
                  hover-class="none"
                  class="flex-sub font-md text-right"
                  style="color: #a86b2b"
                  v-if="goodsAppraises.total > 0"
                >
                  超98%买家赞不绝口
                  <text class="cuIcon-right text-88 ml-s"></text>
                </navigator>
              </view>
            </view>

            <!-- 循环遍历 -->
            <navigator
              :url="'/pages/appraises/list/index?spuId=' + goodsSpu.id"
              hover-class="none"
              class="flex"
              v-for="(item, index) in goodsAppraises.records"
              :key="index"
              :class="index != 0 ? 'mt-xxmd' : ''"
            >
              <image
                class="size-xxxxl round"
                :src="
                  item.headimgUrl || $imgUrl('live/user-center/default_pic.png')
                "
              ></image>
              <view style="flex: 1" class="content ml-xmd">
                <view class="flex align-center">
                  <text class="goods-tip font-xmd">{{
                    item.nickName | nickNameFilter
                  }}</text>
                  <!-- <view class="text-gray margin-left text-xsm">{{ item.createTime }}</view> -->
                  <image
                    class="height-xxxxxl width-172 ml-ss"
                    :src="memberLevel[item.memberLevel]"
                    mode="aspectFill"
                  ></image>
                </view>
                <view class="goods-tip font-md" v-if="item.specInfo"
                  >规格：{{ item.specInfo }}</view
                >
                <!-- <base-rade :value="item.goodsScore" size="lg"></base-rade> -->
                <view class="text-black text-content mt-sm font-md overflow-2">
                  {{ item.content ? item.content : "此用户未填写评价内容" }}
                </view>
                <view class="grid col-4 grid-square flex-sub">
                  <view
                    class="bg-img mt-xxsm"
                    v-for="(picUrl, index2) in item.picUrls"
                    :key="index2"
                  >
                    <image :src="picUrl" mode="aspectFill"></image>
                  </view>
                </view>
              </view>
            </navigator>
          </view>
        </view>

        <view :class="'cu-modal bottom-modal ' + modalService">
          <view class="cu-dialog bg-white">
            <view class="padding-xl">
              <view class="text-lg text-center">基础服务</view>
              <view class="cu-list text-left solid-bottom">
                <view
                  class="cu-item text-df"
                  v-for="(item, index) in ensureList"
                  :key="index"
                >
                  <view class="content padding-tb-sm">
                    <view>
                      <text
                        class="cuIcon-roundcheckfill text-orange padding-right-sm"
                      ></text>
                      {{ item.name }}
                    </view>
                    <view
                      class="text-gray font-md margin-left-xl margin-top-sm"
                      v-if="item.detail"
                    >
                      {{ item.detail }}
                    </view>
                  </view>
                </view>
              </view>
              <button
                class="cu-btn margin-top response lg"
                :class="'bg-' + theme.themeColor"
                @tap="hideModalService"
              >
                确定
              </button>
            </view>
          </view>
        </view>

        <coupon-receive
          :couponInfoList="couponInfoList"
          :modalCoupon="modalCoupon"
          @changeModalCoupon="modalCoupon = $event"
          @receiveCouponChange="receiveCouponChange($event)"
        ></coupon-receive>

        <customer-service-modal
          :modalCustomerService="modalCustomerService"
          @changeModal="modalCustomerService = $event"
          :shopId="shopInfo.id"
          :phone="shopInfo.phone"
          :wxCustomerUrl="shopInfo.wxCustomerUrl"
          :goodsSpuId="goodsSpu.id"
          :goodsName="goodsSpu.name"
          :goodsPic="goodsSpu.picUrls[0]"
          :sharePic="goodsSpu.sharePic"
          :wxworkQrcode="shopInfo.wxworkQrcode"
        ></customer-service-modal>

        <!-- 分割线 -->
        <view class="height-xxs" style="background: #f6f7fc"></view>
        <!-- 店铺组件 -->
        <shopInfo :shopInfo="shopInfo"></shopInfo>
        <!-- 分割线 -->
        <view style="height: 1px; background: #f6f7fc"></view>

        <recommend-goods :spuId="goodsSpu.id" />

        <!-- 分割线 -->
        <view class="height-xxs" style="background: #f6f7fc"></view>

        <!-- 活动专区 -->
        <activity-shop
          :shopActAdverts="goodsSpu.shopActAdverts"
        ></activity-shop>

        <!-- 富文本商品详情 -->
        <view class="bg-white" v-if="article_description">
          <view class="padding-tb padding-lr-sm text-black text-bold text-df"
            >商品介绍</view
          >
          <jyf-parser :html="article_description"></jyf-parser>
        </view>

        <!-- 规格参数 商品详情页接口参数返回 -->
        <specificationsGoods
          :specificationVo="goodsSpu.specificationVo"
          :brandAuthImg="goodsSpu.brandAuthImg"
          :serviceConfig="goodsSpu.serviceConfig"
        />

        <!-- 商品推荐列表 -->
        <recommendComponents
          :itemId="id"
          :sceneId="goodsSpu.sceneId"
          :canLoad="canLoad"
          @getGoodsRecom="getGoodsRecomBottom"
          ref="recommend"
          :count="50"
        />

        <view class="cu-bar bg-white tabbar shop foot font-xmd" v-if="shopInfo">
          <view @click="handleToShop" class="action bg-white">
            <view class="action-icon cuIcon-shop"></view>
            <text class="font-xmd">店铺</text>
          </view>

          <view
            class="action bg-white font-xmd"
            @click="showModalCustomerService"
          >
            <view class="action-icon cuIcon-service"></view>
            <text class="font-xmd">客服</text>
          </view>

          <view class="action" @tap="wxTemplate(10)">
            <view
              class="action-icon font-xmd"
              :class="
                'cuIcon-' + (goodsSpu.collectId ? 'likefill text-red' : 'like')
              "
            ></view>
            <text class="font-xmd">{{
              goodsSpu.collectId ? "已收藏" : "收藏"
            }}</text>
          </view>
          <!--spuType 1:实物商品 2:虚拟商品 3:积分商品 5:付费优惠券 6:礼品卡 -->

          <view
            class="btn-group"
            v-if="goodsSpu.spuType == 3 && goodsSpu.isCanBuy"
          >
            <button
              class="cu-btn round shadow-blur lg font-xmd"
              :class="'bg-' + theme.themeColor"
              style="width: 90%"
              :disabled="goodsSku.stock <= 0 || goodsSku.enable == '0'"
              @tap="showModalSku"
              data-type="2"
            >
              {{
                goodsSku.stock <= 0
                  ? "已售罄"
                  : goodsSku.enable == "0"
                  ? "已售罄"
                  : "积分兑换"
              }}
            </button>
          </view>

          <view v-else-if="goodsSpu.spuType == 5 && goodsSpu.isCanBuy">
            <button
              class="cu-btn round shadow-blur lg selecjc font-xmd"
              :class="'bg-' + theme.themeColor"
              :disabled="goodsSku.stock <= 0 || goodsSku.enable == '0'"
              @tap="showModalSku"
              data-type="3"
            >
              {{
                goodsSku.stock <= 0
                  ? "已售罄"
                  : goodsSku.enable == "0"
                  ? "已售罄"
                  : "积分兑换"
              }}
            </button>
          </view>

          <block v-else>
            <view class="action font-xmd" @tap="gotoShop(goodsSpu.spuType)">
              <view class="cuIcon-cart" style="width: 85rpx">
                <view
                  style="position: absolute; right: 8rpx; top: -10rpx"
                  class="cu-tag badge"
                  v-show="shoppingCartCount != '0'"
                >
                  {{ shoppingCartCount }}
                </view>
              </view>
              {{ goodsSpu.spuType == 6 ? "卡清单" : "购物车" }}
            </view>

            <view class="btn-group">
              <block
                v-if="
                  ((!goodsSpu.newCustomerGoods && inventoryAll == 0) ||
                    !isShelf) &&
                  goodsSpu.isCanBuy
                "
              >
                <button
                  :style="{
                    flex: '1',
                    background: goodsSpu.registerCollectId
                      ? '#888888'
                      : 'linear-gradient(0deg, #FF7C01 0%, #FF5B04 100%)',
                    padding: 0,
                    color: '#fff',
                  }"
                  class="cu-btn bg-orange round shadow-blur detail-btn font-xmd"
                  @tap="wxTemplate(11)"
                  data-type="1"
                >
                  {{ goodsSpu.registerCollectId ? "取消到货提醒" : "到货提醒" }}
                </button>
              </block>

              <block
                v-else-if="
                  isShelf == true && inventoryAll != 0 && goodsSpu.isCanBuy
                "
              >
                <button
                  v-if="goodsSpu.isShowShoppingCart == 1"
                  :style="{
                    flex: '1',
                    background:
                      isShelf == true
                        ? inventoryAll == 0
                          ? '#888888'
                          : 'linear-gradient(90deg, #FFC305 0%, #FF9700 100%)'
                        : '#888888',
                    borderRadius: '36px 0px 0px 36px',
                    padding: 0,
                    color: '#fff',
                  }"
                  class="cu-btn bg-orange round shadow-blur detail-btn font-xmd"
                  @tap="handleAddCart"
                  data-type="1"
                  :disabled="isShelf == true ? inventoryAll == 0 : true"
                >
                  {{ goodsSpu.spuType == 6 ? "加入卡清单" : "加入购物车" }}
                </button>

                <button
                  :style="{
                    whiteSpace: 'pre-wrap',
                    flex: '1',
                    background:
                      isShelf == true
                        ? inventoryAll == 0
                          ? '#888888'
                          : goodsSpu.presaleType === '1'
                          ? '#A80119'
                          : 'linear-gradient(90deg, #FF7C01 0%, #FF5B04 100%);'
                        : '#888888',
                    borderRadius:
                      goodsSpu.isShowShoppingCart != 1
                        ? '36px'
                        : '0px 36px 36px 0px',
                    color: '#fff',
                    padding: 0,
                  }"
                  class="cu-btn round shadow-blur detail-btn font-xmd"
                  :class="'bg-' + theme.themeColor"
                  @tap="showModalSku"
                  data-type="2"
                  :disabled="
                    isShelf == true
                      ? inventoryAll == 0
                        ? true
                        : goodsSpu.presaleType === '1'
                        ? true
                        : false
                      : true
                  "
                >
                  {{
                    isShelf == true
                      ? inventoryAll == 0
                        ? "已售罄"
                        : goodsSpu.presaleType === "1"
                        ? handlePresaleStartTime
                        : "立即购买"
                      : "已下架"
                  }}
                </button>
              </block>

              <block v-else>
                <button
                  :style="{
                    flex: '1',
                    borderRadius: '36px',
                    background: '#888888',
                    padding: 0,
                    color: '#fff',
                  }"
                  class="cu-btn bg-orange round shadow-blur detail-btn font-xmd"
                  @tap="membershipLevel()"
                >
                  {{ getBackground(goodsSpu.memberLevelLimit) }}
                </button>
              </block>
            </view>
          </block>
        </view>

        <goods-sku
          ref="goodsSkuDia"
          showSimilarSpu
          :goodsSpu="goodsSpu"
          :cartNum="cartNum"
          :shopInfo="shopInfo"
          @numChange="handleNumberChange"
          :goodsSku="goodsSku"
          @changeGoodsSku="goodsSku = $event"
          :goodsSpecData="goodsSpecData"
          @changeSpec="goodsSpecData = $event"
          :needUserCertified="needUserCertified"
          :modalSku="modalSku"
          @changeModalSku="modalSku = $event"
          :modalSkuType="modalSkuType"
          @operateCartEvent="operateCartEvent"
          @changeOtherGoods="changeOtherGoods"
          :customerGoodsId="customerGoodsId"
          :customerId="customerId"
        />

        <!-- <view :class="
          'cu-modal ' +
          (goodsSpu.shelf == '0' || shopInfo.enable == '0' ? 'show' : '')
        ">
          <view class="cu-dialog">
            <view class="cu-bar bg-white justify-end">
              <view class="content">提示</view>
            </view>
            <view class="padding-xl">抱歉，该{{
              goodsSpu.shelf == '0'
                ? '商品'
                : shopInfo.enable == '0'
                ? '商品所属店铺'
                : ''
            }}已下架</view>
          </view>
        </view> -->
        <!-- 分享组件 -->
        <share-component
          v-model="showShare"
          :shareParams="shareParams"
        ></share-component>
      </view>
    </view>
    <!-- 分享朋友圈 -->
    <view v-if="scene && scene == '1154'">
      <share-single-page />
    </view>

    <template v-if="goodsSpu && goodsSpu.estimatedPriceVo">
      <share-product-pic
        ref="poster"
        :riTitle="isPrice ? '松鼠优惠' : '松鼠爆款'"
        :riTime="preferentialPrice"
        :showDesc="isPrice"
        :estimatedPrice="goodsSpu.estimatedPriceVo.estimatedPrice"
        :originalPrice="goodsSpu.estimatedPriceVo.originalPrice"
        :spuImg="goodsSpu.picUrls[0]"
        @success="shareSuccess"
      ></share-product-pic>
    </template>

    <!-- 送券通知 -->
    <template>
      <coupon-notification ref="topMessage"></coupon-notification>
    </template>

    <!-- <share-product-pic 
	  ref="sharePic" 
	  @success="shareSuccess" 
	  /> -->
    <redPackage showType="top"></redPackage>
  </view>
</template>

<script>
const app = getApp();
import formatPrice from "@/components/format-price/index.vue";
import recommendComponents from "@/components/recommend-components/index";
import shareSinglePage from "@/components/share-single-page/index";
import shareProductPic from "@/components/share-product-pic/index";
import couponNotification from "@/components/coupon-notification/index";
const { base64src } = require("utils/base64src.js");
import api from "utils/api";
// import jweixin from '@/utils/jweixin'
import util from "@/utils/util";
import goodsSku from "@/components/goods-sku/goods-sku";
import baseRade from "components/base-rade/index";
import couponReceive from "@/components/coupon-receive/index";
// 店铺组件
import shopInfo from "../component/shop-info";
// 轮播商品推荐
import recommendGoods from "../component/recommend-goods";
//规格参数
import specificationsGoods from "../component/specifications-goods";
// 活动专区
import activityShop from "../component/activity-shop";
// 分享组件
import shareComponent from "@/components/share-component/index";
import customerServiceModal from "@/components/customer-service-modal";
import redPackage from "@/components/red-package/index.vue";
import { getWxTemplate } from "@/api/message.js";
import { mapState } from "vuex";
import { senGoods, getCurrentTitle } from "@/public/js_sdk/sensors/utils.js";
import { senTrack } from "@/public/js_sdk/sensors/utils.js";
export default {
  components: {
    baseRade,
    couponReceive,
    shopInfo,
    shareComponent,
    recommendComponents,
    customerServiceModal,
    shareSinglePage,
    shareProductPic,
    formatPrice,
    goodsSku,
    couponNotification,
    redPackage,
    recommendGoods,
    specificationsGoods,
    activityShop,
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      shopInfo: {},
      cartNum: 1,
      goodsSpu: {
        picUrls: [],
      },
      goodsSku: {},
      goodsSpecData: [],
      //会员级别
      memberLevel: {
        101: "https://img.songlei.com/live/appraises/level-101.png",
        102: "https://img.songlei.com/live/appraises/level-102.png",
        103: "https://img.songlei.com/live/appraises/level-103.png",
        108: "https://img.songlei.com/live/appraises/level-108.png",
      },
      goodsAppraises: {},
      currents: 0,
      modalSku: false,
      modalSkuType: "",
      shoppingCartCount: 0,
      shareShow: "",
      curLocalUrl: "",
      ensureList: [],
      modalService: "",
      modalCoupon: false,
      modalCustomerService: false,
      couponInfoList: [],
      promotionsInfoList: [],
      id: "",
      posterUrl: "",
      posterShow: false,
      posterConfig: "",
      article_description: "",
      showShare: false,
      shareParams: {},
      timer: null, // 定时器
      duration: 0, // 停留时长
      titleLable: [
        // {
        // 	text:'松鼠秒杀',
        // 	value:'titleLable'
        // }
      ],
      isChoice: false,
      playVideo: false,
      showVideo: false,
      videoContext: null,
      muted: true,
      canLoad: false,
      loading: true,
      scene: "", //场景值
      //有数统计使用
      page_title: "商品详情",
      shareImgSrc: "",
      onShowTimes: 0,
      jionmumberdatas: "",
      sharer_user_code: "",
      needUserCertified: "", //礼品卡商品是否需要实名认证 0不需要  1 需要
      skuId: "",
      checkSpecVal: "",
      customerGoodsId: "", // 新客商品id
      customerId: "", // 新客活动 id
      loadmore: true,
      isPlaying: false,
      videoEnded: false,
      oldSimilarSpuList: null, ////母码关联商品里面使用，渲染第一次加载的商品里面的similarSpu,防止每次切换更多版本，渲染顺序发生变化，
      pageScrollTop: 0,
      bgColor: "rgba(186, 156, 128, 0)",
      isMoreShow: false,
      enterTime: 0,
      source_module: "", // 神策统计使用来源模块
      activity_id: "", //当商品在来自活动页面时，上报活动ID
    };
  },

  filters: {
    nickNameFilter(value) {
      if (!value) return "匿名";
      return util.maskMiddle(value);
    },
  },

  watch: {
    goodsSpecData: {
      handler: function (val, oldVal) {
        if (val) {
          val.forEach((item, index) => {
            if (item.leaf && item.leaf.length) {
              item.leaf.forEach((item2, index2) => {
                if (item.checked == item2.id) {
                  this.checkSpecVal = item.checked;
                }
              });
            }
          });
        }
      },
      deep: true,
    },
  },

  onLoad(options) {
    // --------小程序分享朋友圈开始------
    // #ifdef MP-WEIXIN
    uni.showShareMenu({
      withShareTicket: true,
      menus: ["shareAppMessage", "shareTimeline"],
    });
    // --------小程序分享朋友圈结束------
    let getLaunchOptions = uni.getLaunchOptionsSync();
    this.scene = getLaunchOptions.scene;
    // #endif
    // 保存别人分享来的 userCode
    util.saveSharerUserCode(options);
    let id;
    if (options.scene) {
      //接受二维码中参数
      let scenes = decodeURIComponent(options.scene).split("&");
      id = scenes[0];
    } else {
      id = options.id;
    }
    this.id = id;
    if (options.skuId) {
      this.skuId = options.skuId;
    }
    // 新客商品id
    if (options.customerGoodsId) {
      this.customerGoodsId = options.customerGoodsId;
    }
    // 新客活动id
    if (options.customerId) {
      this.customerId = options.customerId;
    }

    this.source_module = options.source_module
      ? decodeURIComponent(options.source_module)
      : ""; // 神策统计使用来源模块

    this.loading = true;
    //场景值等于 1154 分享单页模式
    if (this.scene && this.scene == 1154) return;
    app.initPage().then((res) => {
      this.initData(this.id);
      setTimeout(() => {
        // 场景类型(1:大转盘 2:订单支付 3: 商品预览)
        this.$refs.topMessage.setMessage(3, 5000); // 将数据传递给顶部信息提示组件，并设置持续时间为0ms
      }, 6000);
    });
  },

  onReady() {
    this.videoContext = uni.createVideoContext("myVideo", this);
  },

  onShareAppMessage: function (ops) {
    // this.$refs.poster.onCanvas()
    let from_type = "menu";
    if (ops.from === "button") {
      from_type = "button";
    }
    let goodsSpu = this.goodsSpu;
    let title = goodsSpu.name;
    let sharePic = "";
    // if (goodsSpu.sharePic && goodsSpu.sharePic.startsWith("http")) {
    if (goodsSpu.sharePic) {
      sharePic = goodsSpu.sharePic;
    }
    // let imageUrl = '' || goodsSpu.picUrls[0];
    let imageUrl = sharePic ? sharePic + "-jpg_w360_q90" : this.shareImgSrc;

    const userInfo = uni.getStorageSync("user_info");
    let userCode = userInfo
      ? "&type=1&sharer_user_code=" + userInfo.userCode
      : "";
    let path = "/pages/goods/goods-detail/index?id=" + goodsSpu.id + userCode;
    this.dataController({
      bhv_type: "share",
      item_id: this.goodsSpu.id,
    });
    const app = getApp();
    if (app.globalData.gdtVid) {
      api.postTimelineAdvertAction({
        action: "SHARE",
        clickId: app.globalData.gdtVid || "",
      });
    }
    this.handleSenGoods("GoodsShareOrCollect", {
      click_content: "分享",
    });
    return {
      title: title,
      path: path,
      imageUrl: imageUrl,
      success: function (res) {
        uni.showToast({
          title: "分享成功",
        });
      },
      fail: function (res) {
        // 转发失败
        uni.showToast({
          title: "分享失败",
          icon: "none",
        });
      },
    };
  },

  onShareTimeline: function () {
    let goodsSpu = this.goodsSpu;
    let title = goodsSpu.name;
    let sharePic = "";
    if (goodsSpu.sharePic) {
      sharePic = goodsSpu.sharePic;
    }
    // let imageUrl = goodsSpu.picUrls[0];
    let imageUrl = sharePic ? sharePic : goodsSpu.picUrls[0];

    const userInfo = uni.getStorageSync("user_info");
    let userCode = userInfo
      ? "&type=1&sharer_user_code=" + userInfo.userCode
      : "";
    let path = "id=" + goodsSpu.id + userCode;
    this.dataController({
      bhv_type: "share",
      item_id: this.goodsSpu.id,
    });
    const app = getApp();
    if (app.globalData.gdtVid) {
      api.postTimelineAdvertAction({
        action: "SHARE",
        clickId: app.globalData.gdtVid || "",
      });
    }

    return {
      title: title,
      query: path,
      imageUrl: imageUrl,
      success: function (res) {
        uni.showToast({
          title: "分享成功",
        });
      },
      fail: function (res) {
        // 转发失败
        uni.showToast({
          title: "分享失败",
          icon: "none",
        });
      },
    };
  },

  onShow() {
    // 获取实名认证返回条件 true 认证 false 未认证
    if (app.globalData.AuthenticationType) {
      this.needUserCertified = app.globalData.AuthenticationType;
    }
    this.enterTime = Date.now();
    if (this.onShowTimes > 1) {
      this.handleSenGoods("GoodsDetailPageView");
    }
    // 进入页面时长
    this.onShowTimes++;
    //更新购物车数量
    uni.$on("updateCart", () => {
      this.shoppingCartCount = app.globalData.shoppingCartCount;
    });
  },

  onHide() {
    this.handleHideView();
  },

  onUnload() {
    console.log("=======onUnload=========");
    this.handleHideView();
    //清空分销人的code,只有打开分享的商品详情页，在该页面下单，加购有效，其他都不算业绩。  加购之后购物车的数量修改也做业绩计算，这块逻辑后端处理
    util.removeSharerUserCode();
  },

  onPageScroll(e) {
    this.pageScrollTop = e.scrollTop;
    if (this.pageScrollTop > 10) {
      this.bgColor =
        "rgba(205, 173, 144, " + (this.pageScrollTop + 30) / 100 + ")";
    } else {
      this.bgColor = "rgba(205, 173, 144, 0)";
    }
  },

  //下拉加载更多
  onReachBottom() {
    if (this.loadmore) {
      this.$refs.recommend.getGoodsRecom();
    }
  },

  onPullDownRefresh() {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    // this.refresh(); // 隐藏导航栏加载框
    uni.hideNavigationBarLoading(); // 停止下拉动作
    uni.stopPullDownRefresh();
  },

  computed: {
    ...mapState(["CustomBar", "rootFontSize", "isPhone"]),
    swperPicUrl() {
      if (
        this.goodsSpu.videoAddress &&
        this.goodsSpu.picUrls &&
        this.goodsSpu.picUrls.length >= 2
      ) {
        return [this.goodsSpu.picUrls[1], ...this.goodsSpu.picUrls];
      } else {
        return this.goodsSpu.picUrls;
      }
    },

    isMoreHeight: function () {
      // showDiscount 是否展示优惠信息
      return (
        (this.goodsSpu &&
          this.goodsSpu.showDiscount != 0 &&
          this.goodsSpu.estimatedPriceVo &&
          this.goodsSpu.estimatedPriceVo.discountPrice &&
          this.goodsSpu.estimatedPriceVo.discountPrice != "0.0" &&
          this.goodsSpu.estimatedPriceVo.discountPrice != "0") ||
        (this.goodsSpu.showDiscount != 0 &&
          this.goodsSpu.estimatedPriceVo &&
          this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice &&
          this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0.0" &&
          this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0") ||
        (this.goodsSpu.showDiscount != 0 &&
          this.goodsSpu.estimatedPriceVo &&
          this.goodsSpu.estimatedPriceVo.coupon &&
          this.goodsSpu.estimatedPriceVo.coupon != "0.0" &&
          this.goodsSpu.estimatedPriceVo.coupon != "0")
      );
    },
    spuPriceColorLeft: function () {
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        return customFiled.leftColor;
      } else {
        return "#ffffff";
      }
    },
    spuPriceValueColorLeft: function () {
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        return customFiled.leftValueColor || customFiled.leftColor;
      } else {
        return "#ffffff";
      }
    },
    spuPriceColorRight: function () {
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        return customFiled.rightColor;
      } else {
        return "#ffffff";
      }
    },

    spuPriceValueColorRight: function () {
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        return customFiled.rightValueColor || customFiled.rightColor;
      } else {
        return "#ffffff";
      }
    },

    borderColor: function () {
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        return customFiled.spuPicBorder;
      }
      return "";
    },

    spuPicPriceBg: function () {
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        if (customFiled.spuPriceStyle == 1) {
          return (
            customFiled.spuDetailPicPriceBigBg ||
            customFiled.spuPicPriceBigBg ||
            "https://img.songlei.com/1/material/b1deb9ff-1687-44e9-a64d-616ad97e17aa.png"
          );
        }
        return (
          customFiled.spuPicPriceBg ||
          "http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/list_price_bg2.png"
        );
      }
      return "http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/list_price_bg.png";
    },

    spuDownPicPriceBg: function () {
      let pic = "";
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        pic = customFiled.spuDetailDownPriceBg;
      }
      return pic || "https://img.songlei.com/live/goods/down-pic.png";
    },
    spuFavorablePriceBg: function () {
      let color = "";
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        color = customFiled.favorablePriceBg;
      }
      return color || "#ffffff";
    },

    spuFavorablePriceColor: function () {
      let color = "";
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        color = customFiled.favorablePriceTextColor;
      }
      return color || "#ff362d";
    },

    reductionGoodsBg: function () {
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        return (
          customFiled.reductionGoodsIcon ||
          "https://img.songlei.com/live/goods/reduce-bg.png"
        );
      }
      return "https://img.songlei.com/live/goods/reduce-bg.png";
    },

    totalDiscountPrice() {
      if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.totalDiscountPrice > 0
      ) {
        return this.goodsSpu.estimatedPriceVo.totalDiscountPrice;
      }
      return "";
    },

    spuPriceColor: function () {
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        if (customFiled.spuPriceStyle == 1) {
          return customFiled.spuPriceColor1 || "#fff";
        }
        return customFiled.spuPriceColor2 || "#fff";
      }
      return "#fff";
    },
    spuPriceStyle: function () {
      if (this.theme && this.theme.customFiled) {
        const customFiled = JSON.parse(this.theme.customFiled);
        return customFiled.spuPriceStyle || 2;
      }
      return 2;
    },

    //只有上架和审核通过
    isShelf() {
      if (this.goodsSpu.shelf == "1" && this.goodsSpu.verifyStatus == "1") {
        return true;
      }
      return false;
    },

    //库存数量
    inventoryAll() {
      let inventoryAll = 0;
      if (
        this.goodsSpu &&
        this.goodsSpu.skus &&
        this.goodsSpu.skus.length > 0
      ) {
        this.goodsSpu.skus.map((item) => {
          inventoryAll += item.stock;
        });
        return inventoryAll;
      }
    },

    //松鼠优惠价显示与否  有优惠价并且划线减优惠价大于0 显示
    isPrice() {
      if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.estimatedPrice
      ) {
        if (
          this.goodsSpu.estimatedPriceVo.originalPrice -
            this.goodsSpu.estimatedPriceVo.estimatedPrice >
          0
        ) {
          return true;
        }
      }
      return false;
    },
    //计算松鼠优惠价
    preferentialPrice() {
      let price = "";
      if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.estimatedPrice
      ) {
        let originalPrice = this.goodsSpu.estimatedPriceVo.originalPrice;
        let estimatedPrice = this.goodsSpu.estimatedPriceVo.estimatedPrice;
        if (originalPrice - estimatedPrice > 0) {
          price = originalPrice - estimatedPrice;
          let pric = price.toFixed(1);
          return pric;
        }
      }
      return null;
    },
    //预售时间处理
    handlePresaleStartTime() {
      if (
        this.goodsSpu.goodsSpuInfoVo &&
        this.goodsSpu.goodsSpuInfoVo.presaleStartTime
      ) {
        let time = this.goodsSpu.goodsSpuInfoVo.presaleStartTime
          .substr(5, 11)
          .replace("-", "月");
        let mouthDay = time.substr(0, 5); //月 日
        let ytd = time.substr(5, 9); //时间
        return `${mouthDay}日\n${ytd} 开售`;
      }
    },
  },
  methods: {
    handleHideView() {
      const seconds = (Date.now() - this.enterTime) / 1000;
      this.dataController({
        bhv_type: "stay",
        item_id: this.goodsSpu.id,
        bhv_value: seconds,
      });
      this.handleSenGoods("GoodsDetailPageLeave", {
        // 单位秒
        stay_duration: seconds,
      });
    },
    handleNumberChange(e) {
      this.cartNum = e;
    },
    handleVideoBeginPlay() {
      this.isPlaying = true;
    },
    // 当播放到末尾时
    onVideoEnded() {
      console.log("当播放到末尾时====>", this.playVideo);
      this.playVideo = !this.playVideo;
      this.videoEnded = false;
      this.isPlaying = false;
    },
    // --优先--下拉加载
    getGoodsRecomBottom(data) {
      console.log("getGoodsRecomBottom", data);
      if (data && data.length > 0) {
        this.loadmore = true;
      } else {
        this.loadmore = false;
      }
    },
    membershipLevel() {
      uni.showToast({
        title: "会员等级不足无法购买!",
        icon: "none",
      });
    },
    getBackground(value) {
      let label = "";
      switch (value) {
        // case '101':
        // label = '积分卡.png';
        //   break;
        case "102":
          label = "仅限金卡以上专享";
          break;
        case "103":
          label = "仅限钻石以上专享";
          break;
        case "108":
          label = "仅限黑钻以上专享";
          break;
        default:
          label = "会员等级不够";
      }
      return label;
    },
    //showModal 是否弹出规格框，切换更多关联商品的时候需要弹出
    async initData(id, showModal) {
      this.id = id;
      await this.goodsGet(id);
      // this.couponInfoPage(id);
      const type = this.goodsSpu.spuType == 6 ? 3 : 0;
      await this.shoppingCartCountFun(type);
      this.goodsAppraisesPage();
      this.listEnsureBySpuId(id);
      // 全场折扣 折扣活动
      // this.getPromotionsInfoByGoodsSpuId(id);
      setTimeout(() => {
        if (showModal) this.dealModalSku(this.buttonEvent);
      }, 0);
    },

    //切换到关联的商品
    //默认切换的母码一样的商品，会弹框
    changeOtherGoods(id, isShowDialog = true) {
      this.initData(id, isShowDialog);
    },

    putcur() {
      uni.navigateTo({
        url: `/pages/shop/shop-detail/index?id=${this.shopInfo.id}&cur=6`,
      });
    },

    shareSuccess(e) {
      this.shareImgSrc = e;
    },

    handlePreviewImage(index) {
      // 如果弹框出现
      // if(this.modalSku) return;
      if (index == 0 && this.goodsSpu.videoAddress) {
        this.handlePlayVideo();
      } else {
        // 预览图片
        uni.previewImage({
          urls: this.goodsSpu.picUrls,
          current: index,
        });
      }
    },

    copyText() {
      uni.setClipboardData({
        data: this.goodsSpu.name,
        success() {
          uni.showToast({
            title: "已复制到剪贴板",
            icon: "none",
            position: "top",
          });
        },
      });
    },
    gotoShop(spuType) {
      if (spuType && spuType == 6) {
        uni.navigateTo({
          url: "/pages/shop/shop-cart/index?cartType=3",
        });
      } else {
        uni.navigateTo({
          url: "/pages/shop/shop-cart/index",
        });
      }
    },

    handleToShop() {
      let pages = getCurrentPages();
      if (
        pages &&
        pages.length > 1 &&
        pages[pages.length - 2] &&
        pages[pages.length - 2].$page &&
        pages[pages.length - 2].$page.fullPath &&
        pages[pages.length - 2].$page.fullPath.indexOf(
          "/pages/shop/shop-detail/index?id=" + this.shopInfo.id,
        ) != -1
      ) {
        uni.navigateBack({
          delta: 1,
        });
      } else {
        uni.navigateTo({
          url: "/pages/shop/shop-detail/index?id=" + this.shopInfo.id,
        });
      }
    },

    handlePlayVideo() {
      if (this.playVideo) {
        this.playVideo = false;
        this.videoContext.pause();
        // if (uni.getSystemInfoSync().platform == 'ios') {
        // 	uni.showToast({
        // 		title: 'ios play'
        // 	});
        // 	this.isPlaying = false;
        // }
      } else {
        this.videoContext.play();
        this.playVideo = true;
        // if (uni.getSystemInfoSync().platform == 'ios') {
        // 	this.isPlaying = true;
        // }
      }
    },

    handlePlayVoice() {
      this.muted = !this.muted;
    },

    async goodsGet(id) {
      await api
        .goodsGet(id, this.skuId, this.customerGoodsId)
        .then((res) => {
          this.loading = false;
          this.goodsSpu = res.data;
          if (this.goodsSpu.isAutoPlay == 1) {
            this.playVideo = true;
          }
          if (this.oldSimilarSpuList == null) {
            this.oldSimilarSpuList = this.goodsSpu.similarSpuList;
          }

          this.muted = this.goodsSpu.isMute == 0 ? false : true;
          this.isDistributionUser = res.data.isDistributionUser;

          this.couponInfoList = res.data.listCouponInfo;
          this.goodsSpecGet(id);
          this.handleSenGoods("GoodsDetailPageView");
          //获取店铺信息
          this.shopInfoGet(this.goodsSpu.shopId);
          if (this.goodsSpu) {
            this.goodsSku =
              this.goodsSpu.specType == "0" ? this.goodsSpu.skus[0] : {};
            if (this.goodsSku && this.goodsSku.minBuyNum > 0) {
              this.cartNum = this.goodsSku.minBuyNum;
            }
          }
          console.error("=====this.cartNum ========", this.cartNum);
          // WxParse.wxParse('description', 'html', goodsSpu.description, this, 0)
          setTimeout(() => {
            this.article_description = this.goodsSpu.description;
          }, 300);
          this.$nextTick(() => {
            this.canLoad = true;
            //生成图片
            this.$refs.poster?.onCanvas();
          });
        })
        .catch((res) => {
          if (res.data && res.data.code == 1) {
            uni.showModal({
              title: "该商品不存在或已删除",
              showCancel: false,
              success: function (res) {
                uni.navigateBack({
                  delta: 1,
                });
              },
            });
          }
        });
    },
    //查询店铺
    shopInfoGet(shopId) {
      let that = this;
      api.shopInfoGet(shopId).then((res) => {
        that.shopInfo = res.data;
        if (res.data.brandId) {
          api.getBrandVipMember(res.data.brandId).then((res) => {
            this.jionmumberdatas = res.data;
          });
        }
      });
    },

    goodsSpecGet(spuId) {
      api
        .goodsSpecGet({
          spuId: spuId,
        })
        .then((res) => {
          res.data
            ? res.data.map((t) => {
                t.checked = "";
              })
            : [];
          this.goodsSpecData = res.data;
        });
    },

    goodsAppraisesPage() {
      api
        .goodsAppraisesPage({
          current: 1,
          size: 2,
          descs: "goods_score",
          spuId: this.id,
        })
        .then((res) => {
          this.goodsAppraises = res.data;
        });
    },

    receiveCouponChange(obj) {
      //更新单条数据
      this.couponInfoList[obj.index] = obj.item;
      this.couponInfoList.splice(); //确保页面刷新成功
      //强制刷新
      this.goodsGet(this.id);
      // this.goodsGet(this.id);
      // uni.redirectTo({
      //   url: 'pages/goods/goods-detail/index'
      // });
    },

    //获取商品保障
    listEnsureBySpuId(spuId) {
      api
        .listEnsureBySpuId({
          spuId: spuId,
        })
        .then((res) => {
          this.ensureList = res.data;
        });
    },

    change: function (e) {
      this.currents = e.detail.current;
      if (this.playVideo) {
        this.videoContext.pause();
        this.playVideo = false;
      }
    },

    showModalService() {
      this.modalService = "show";
    },

    hideModalService() {
      this.modalService = "";
    },

    showModalCoupon() {
      this.modalCoupon = true;
    },

    hideModalCoupon() {
      this.modalCoupon = false;
    },

    showModalCustomerService() {
      this.modalCustomerService = true;
    },

    hideModalCustomerService() {
      this.modalCustomerService = false;
    },

    showModalSku(e) {
      //保存起来，切换其他关联商品的时候用到
      this.buttonEvent = e;
      this.dealModalSku(e);
    },

    dealModalSku(e) {
      if (app.isLogin()) {
        this.modalSku = true;
        this.modalSkuType = e.target.dataset.type
          ? e.target.dataset.type + ""
          : "";

        //type ==2 是立即购买 type ==3 是积分兑换
        // 要给神策发送埋点数据
        if (this.modalSkuType == 2 || this.modalSkuType == 3) {
          this.handleSenGoods("GoodsBuySoon", {
            goods_sale: this.goodsSpu.saleNum,
            page_name: getCurrentTitle(0),
          });
        }
        //要先暂停视频，否则视频上方弹框内的view 点击没反应
        if (this.playVideo) {
          this.playVideo = false;
          this.videoContext.pause();
        }
      } else {
        uni.reLaunch({
          url:
            "/pages/login/index?reUrl=" +
            encodeURIComponent(
              `/pages/goods/goods-detail/index?id=${this.goodsSpu.id}`,
            ),
        });
      }
    },

    shoppingCartCountFun(type) {
      const params = {};
      // 卡清单传 3
      if (type == 3) {
        params.type = type;
      }
      api.shoppingCartCount(params).then((res) => {
        this.shoppingCartCount = res.data; //设置TabBar购物车数量
        app.globalData.shoppingCartCount = this.shoppingCartCount + "";
        uni.$emit("updateCart");
      });
    },

    operateCartEvent() {
      const type = this.goodsSpu.spuType == 6 ? 3 : 0;
      this.shoppingCartCountFun(type);
    },
    //收藏
    userCollect() {
      let goodsSpu = this.goodsSpu;
      let collectId = goodsSpu.collectId;
      if (collectId) {
        api.userCollectDel(collectId).then((res) => {
          uni.showToast({
            title: "已取消收藏",
            icon: "success",
            duration: 2000,
          });
          goodsSpu.collectId = null;
          this.goodsSpu = goodsSpu;
          this.handleCollect("取消收藏");
        });
      } else {
        api
          .userCollectAdd({
            type: "1",
            relationIds: [goodsSpu.id],
          })
          .then((res) => {
            uni.showToast({
              title: "收藏成功",
              icon: "success",
              duration: 2000,
            });
            goodsSpu.collectId = res.data[0].id;
            this.goodsSpu = goodsSpu;
            this.handleCollect("收藏");
          });
      }
    },

    async shareShowFun() {
      const customFiled = JSON.parse(this.theme.customFiled);
      // 分享海报需要配置的参数
      let desc = "";
      let qrCode = ""; //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
      let sharePic = "";
      if (this.goodsSpu.sharePic && this.goodsSpu.sharePic.startsWith("http")) {
        sharePic = this.goodsSpu.sharePic;
      }
      let shareImg = sharePic || this.goodsSpu.picUrls[0]; // 海报图片
      // #ifdef H5 || APP-PLUS
      desc = "长按识别二维码";
      // h5的海报分享的图片有的有跨域问题，所以统一转成base64的
      // 之所以不在组件里面转换是因为无法区分哪张image图片需要处理，一般处理主图
      shareImg = await util.imgUrlToBase64(shareImg);
      // #endif
      //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
      let posterConfig = {
        width: 750,
        height: 750,
        backgroundColor: "#fff",
        debug: false,
        lines: [{}],
        blocks: [
          {
            width: 480,
            height: 500,
            x: 135,
            y: 20,
            borderRadius: 20,
          },
          //竖线
          {
            x: 375,
            y: 550,
            width: 2,
            height: 180,
            backgroundColor: "#939393",
            zIndex: 200,
          },
        ],
        texts: [
          {
            x: 130,
            y: 580,
            fontSize: 30,
            baseLine: "middle",
            text:
              this.goodsSpu.name && this.goodsSpu.name.length > 16
                ? this.goodsSpu.name.substring(0, 14) + "..."
                : this.goodsSpu.name,
            width: 210,
            lineNum: 5,
            color: "#333333",
            zIndex: 200,
          },
          {
            x: 430,
            y: 690,
            fontSize: 20,
            baseLine: "top",
            text: "扫描/长按识别",
            width: 250,
            lineNum: 10,
            lineHeight: 70,
            color: "#666666",
            zIndex: 200,
          },
          {
            x: 150,
            y: 680,
            baseLine: "top",
            text: "￥" + this.goodsSpu.estimatedPriceVo.estimatedPrice,
            fontSize: 40,
            fontWeight: "900",
            color: "#e54d42",
          },
        ],
        images: [
          {
            width: 480,
            height: 480,
            x: 135,
            y: 10,
            url: shareImg,
          },
          {
            width: 275,
            height: 55,
            x: 355,
            y: 490,
            url: null,
            url: "https://img.songlei.com/live/logo/sharelogo1.jpeg",
          },
          {
            width: 116,
            height: 116,
            x: 430,
            y: 560,
            url: null,
            qrCodeName: "qrCodeName", // 二维码唯一区分标识
          },
        ],
      };

      let userInfo = uni.getStorageSync("user_info");
      if (userInfo && userInfo.headimgUrl) {
        //如果有头像则显示
        posterConfig.images.push({
          width: 42,
          height: 42,
          x: 135,
          y: 510,
          borderRadius: 42,
          url: userInfo.headimgUrl,
          zIndex: 200,
        });
        posterConfig.texts.push({
          x: 190,
          y: 530,
          baseLine: "middle",
          text: userInfo.nickName, // 手机号 或者名称
          fontSize: 22,
          color: "#666666",
        });
      }

      if (
        this.goodsSpu.spuType != 3 ||
        this.goodsSpu.spuType != 5 ||
        this.goodsSpu.spuType != 6
      ) {
        if (
          this.goodsSpu.priceOriginal &&
          this.goodsSpu.spuType != 5 &&
          this.goodsSpu.spuType != 6 &&
          this.goodsSpu.priceOriginal > 0 &&
          this.goodsSpu.priceOriginal > this.goodsSpu.priceDown &&
          this.goodsSpu.priceOriginal != this.goodsSpu.priceUp
        ) {
          posterConfig.texts.push({
            x: 140,
            y: 640,
            baseLine: "top", // 下面的价钱
            text: "零售价￥" + this.goodsSpu.priceOriginal,
            fontSize: 26,
            color: "#999999",
            textDecoration: "line-through",
          });
        } else if (
          this.goodsSpu.estimatedPriceVo &&
          this.goodsSpu.estimatedPriceVo.price &&
          this.goodsSpu.estimatedPriceVo.price != this.goodsSpu.priceDown &&
          this.goodsSpu.spuType != 5 &&
          this.goodsSpu.spuType != 6
        ) {
          posterConfig.texts.push({
            x: 140,
            y: 640,
            baseLine: "top", // 下面的价钱
            text: "零售价￥" + this.goodsSpu.estimatedPriceVo.price,
            fontSize: 26,
            color: "#999999",
            textDecoration: "line-through",
          });
        }
      }
      // 海报样式2不带边框  海报样式1带边框
      let posterConfig2 = JSON.parse(JSON.stringify(posterConfig));

      if (
        (this.goodsSpu.estimatedPriceVo &&
          this.goodsSpu.estimatedPriceVo.showDiscount == 1 &&
          this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != null &&
          this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0.0" &&
          this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0") ||
        (this.goodsSpu.estimatedPriceVo.coupon != "0.0" &&
          this.goodsSpu.estimatedPriceVo.coupon != "0") ||
        (this.goodsSpu.estimatedPriceVo.discountPrice != "0.0" &&
          this.goodsSpu.estimatedPriceVo.discountPrice != "0")
      ) {
        if (customFiled.spuPriceStyle == "2") {
          // 判断是否显示边框如果是整图不显示边框
          posterConfig.lines.push(
            //边框
            {
              width: 4,
              startX: 135,
              startY: 10,
              endX: 135,
              endY: 490,
              color: this.borderColor,
              zIndex: 200,
            },
            {
              width: 4,
              startX: 135,
              startY: 10,
              endX: 615,
              endY: 10,
              color: this.borderColor,
              zIndex: 200,
            },
            {
              width: 4,
              startX: 615,
              startY: 10,
              endX: 615,
              endY: 490,
              color: this.borderColor,
              zIndex: 200,
            },
          );
          // 如果不是整图
          posterConfig.images.push({
            width: 480,
            height: 106,
            x: 135,
            y: 384,
            url: this.spuPicPriceBg,
          });
        } else if (customFiled.spuPriceStyle == "1") {
          //如果是整图
          posterConfig.images.push({
            width: 480,
            height: 480,
            x: 135,
            y: 10,
            url: this.spuPicPriceBg,
          });
        }
      }

      // 如果优惠券，折扣活动，折扣减免
      if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.showDiscount == 1 &&
        this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != null &&
        this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0.0" &&
        this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0" &&
        this.goodsSpu.estimatedPriceVo.coupon != "0.0" &&
        this.goodsSpu.estimatedPriceVo.coupon != "0" &&
        this.goodsSpu.estimatedPriceVo.discountPrice != "0.0" &&
        this.goodsSpu.estimatedPriceVo.discountPrice != "0"
      ) {
        posterConfig.texts.push(
          {
            x: 520,
            y: 426,
            baseLine: "top", // 折扣活动
            text: "折扣活动",
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 520,
            y: 460,
            baseLine: "top", // 折扣活动
            text:
              "━" +
              " " +
              "￥" +
              (this.goodsSpu.estimatedPriceVo &&
                this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice),
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
        );

        posterConfig.texts.push(
          {
            x: 480,
            y: 426,
            baseLine: "top", // 优惠劵
            text: "优惠劵",
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 480,
            y: 460,
            baseLine: "top", // 优惠劵
            text: "━" + "  " + "￥" + this.goodsSpu.estimatedPriceVo.coupon,
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
        );

        posterConfig.texts.push(
          {
            x: 344,
            y: 426,
            baseLine: "top", // 折扣减免
            text: "折扣减免",
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 333,
            y: 460,
            baseLine: "top", //
            text:
              "━" + " " + "￥" + this.goodsSpu.estimatedPriceVo.discountPrice, // 折扣减免
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 300,
            y: 426,
            baseLine: "top",
            text: "总价格", // 总价格
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceColorLeft,
            zIndex: 300,
          },
          {
            x: 300,
            y: 426,
            baseLine: "top", // 总价格
            text: "￥" + this.goodsSpu.estimatedPriceVo.originalPrice,
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },

          {
            x: 160,
            y: 426,
            baseLine: "top", // 预估到手价  显示
            text: "预估到手价",
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceColorLeft,
            zIndex: 300,
          },
          {
            x: 160,
            y: 460,
            baseLine: "top", // 预估到手价
            text:
              "￥" + this.goodsSpu.estimatedPriceVo.estimatedPrice + " " + "=",
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorLeft,
            zIndex: 300,
          },
        );
      }

      // 如果只有折扣减免，折扣活动
      else if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.showDiscount == 1 &&
        this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != null &&
        this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0.0" &&
        this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0" &&
        this.goodsSpu.estimatedPriceVo.discountPrice != "0.0" &&
        this.goodsSpu.estimatedPriceVo.discountPrice != "0"
      ) {
        posterConfig.texts.push(
          {
            x: 520,
            y: 426,
            baseLine: "top", //折扣活动
            text: "折扣活动",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 510,
            y: 460,
            baseLine: "top", // 折扣活动
            text:
              "━" +
              "" +
              "￥" +
              this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice,
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
        );

        posterConfig.texts.push(
          {
            x: 410,
            y: 426,
            baseLine: "top", // 折扣减免
            text: "折扣减免",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 400,
            y: 460,
            baseLine: "top", // 折扣减免
            text:
              "━" + "" + "￥" + this.goodsSpu.estimatedPriceVo.discountPrice, // 折扣减免
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 320,
            y: 426,
            baseLine: "top",
            text: "总价格", // 总价格
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 320,
            y: 460,
            baseLine: "top", // 总价格
            text: "￥" + this.goodsSpu.estimatedPriceVo.originalPrice,
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },

          {
            x: 160,
            y: 422,
            baseLine: "top", // 预估到手价  显示
            text: "预估到手价",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorLeft,
            zIndex: 300,
          },
          {
            x: 170,
            y: 456,
            baseLine: "top", // 预估到手价
            text:
              "￥" + this.goodsSpu.estimatedPriceVo.estimatedPrice + "  " + "=",
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorLeft,
            zIndex: 300,
          },
        );
      }

      // 如果只有折扣活动和优惠券
      else if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.showDiscount == 1 &&
        this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != null &&
        this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0.0" &&
        this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice != "0" &&
        this.goodsSpu.estimatedPriceVo.coupon != "0.0" &&
        this.goodsSpu.estimatedPriceVo.coupon != "0"
      ) {
        posterConfig.texts.push(
          {
            x: 520,
            y: 426,
            baseLine: "top", // 优惠劵
            text: "优惠劵",
            fontSize: 24,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },

          {
            x: 508,
            y: 460,
            baseLine: "top", // 优惠劵
            text: "━" + "  " + "￥" + this.goodsSpu.estimatedPriceVo.coupon,
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
        );

        posterConfig.texts.push(
          {
            x: 414,
            y: 426,
            baseLine: "top",
            text: "折扣活动",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 396,
            y: 460,
            baseLine: "top",
            text:
              "━" +
              "  " +
              "￥" +
              this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice,
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 320,
            y: 426,
            baseLine: "top",
            text: "总价格", // 总价格
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 320,
            y: 460,
            baseLine: "top", // 总价格
            text: "￥" + this.goodsSpu.estimatedPriceVo.originalPrice,
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },

          {
            x: 160,
            y: 422,
            baseLine: "top", // 预估到手价  显示
            text: "预估到手价",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorLeft,
            zIndex: 300,
          },
          {
            x: 170,
            y: 456,
            baseLine: "top", // 预估到手价
            text:
              "￥" + this.goodsSpu.estimatedPriceVo.estimatedPrice + "  " + "=",
            fontSize: 26,
            fontWeight: "900",
            color: this.spuPriceValueColorLeft,
            zIndex: 300,
          },
        );
      }

      // 如果只有优惠券，折扣减免
      else if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.showDiscount == 1 &&
        this.goodsSpu.estimatedPriceVo.coupon != "0.0" &&
        this.goodsSpu.estimatedPriceVo.coupon != "0" &&
        this.goodsSpu.estimatedPriceVo.discountPrice != "0.0" &&
        this.goodsSpu.estimatedPriceVo.discountPrice != "0"
      ) {
        posterConfig.texts.push(
          {
            x: 520,
            y: 426,
            baseLine: "top", // 优惠劵
            text: "优惠劵",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 510,
            y: 460,
            baseLine: "top", // 优惠劵
            text: "━" + "  " + "￥" + this.goodsSpu.estimatedPriceVo.coupon,
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
        );

        posterConfig.texts.push(
          {
            x: 410,
            y: 426,
            baseLine: "top",
            text: "折扣减免",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 400,
            y: 460,
            baseLine: "top",
            text:
              "━" + " " + "￥" + this.goodsSpu.estimatedPriceVo.discountPrice, // 折扣减免
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 320,
            y: 426,
            baseLine: "top",
            text: "总价格", // 总价格
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 320,
            y: 460,
            baseLine: "top", // 总价格
            text: "￥" + this.goodsSpu.estimatedPriceVo.originalPrice,
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },

          {
            x: 160,
            y: 422,
            baseLine: "top", // 预估到手价  显示
            text: "预估到手价",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorLeft,
            zIndex: 300,
          },
          {
            x: 170,
            y: 456,
            baseLine: "top", // 预估到手价
            text:
              "￥" + this.goodsSpu.estimatedPriceVo.estimatedPrice + " " + "=",
            fontSize: 20,
            fontWeight: "900",
            color: this.spuPriceValueColorLeft,
            zIndex: 300,
          },
        );
      }

      //如果只有折扣减免
      else if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.showDiscount == 1 &&
        this.goodsSpu.estimatedPriceVo.discountPrice != "0.0" &&
        this.goodsSpu.estimatedPriceVo.discountPrice != "0"
      ) {
        posterConfig.texts.push(
          {
            x: 500,
            y: 426,
            baseLine: "top", // 折扣减免
            text: "折扣减免",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 460,
            y: 460,
            baseLine: "top", //
            text:
              "━" +
              "      " +
              "￥" +
              this.goodsSpu.estimatedPriceVo.discountPrice, // 折扣减免
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 340,
            y: 426,
            baseLine: "top",
            text: "总价格", // 总价格
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 340,
            y: 460,
            baseLine: "top", // 总价格
            text: "￥" + this.goodsSpu.estimatedPriceVo.originalPrice,
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },

          {
            x: 170,
            y: 426,
            baseLine: "top", // 预估到手价  显示
            text: "预估到手价",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorLeft,
            zIndex: 300,
          },
          {
            x: 170,
            y: 460,
            baseLine: "top", // 预估到手价
            text:
              "￥" +
              this.goodsSpu.estimatedPriceVo.estimatedPrice +
              "         " +
              "=",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorLeft,
            zIndex: 300,
          },
        );
      }

      //如果只有优惠券
      else if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.showDiscount == 1 &&
        this.goodsSpu.estimatedPriceVo.coupon != "0.0" &&
        this.goodsSpu.estimatedPriceVo.coupon != "0"
      ) {
        posterConfig.texts.push(
          {
            x: 500,
            y: 426,
            baseLine: "top", // 优惠券
            text: "优惠券",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 460,
            y: 460,
            baseLine: "top", //
            text: "━" + "      " + "￥" + this.goodsSpu.estimatedPriceVo.coupon, // 优惠券
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 340,
            y: 426,
            baseLine: "top",
            text: "总价格", // 总价格
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 340,
            y: 460,
            baseLine: "top", // 总价格
            text: "￥" + this.goodsSpu.estimatedPriceVo.originalPrice,
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 160,
            y: 426,
            baseLine: "top", // 预估到手价  显示
            text: "预估到手价",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorLeft,
            zIndex: 300,
          },
          {
            x: 170,
            y: 460,
            baseLine: "top", // 预估到手价
            text:
              "￥" +
              this.goodsSpu.estimatedPriceVo.estimatedPrice +
              "     " +
              "=",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorLeft,
            zIndex: 300,
          },
        );
      }
      //如果只有折扣活动
      else if (
        this.goodsSpu.estimatedPriceVo &&
        this.goodsSpu.estimatedPriceVo.showDiscount == 1 &&
        this.goodsSpu.estimatedPriceVo.coupon != "0.0" &&
        this.goodsSpu.estimatedPriceVo.coupon != "0"
      ) {
        posterConfig.texts.push(
          {
            x: 480,
            y: 426,
            baseLine: "top", //折扣活动
            text: "折扣活动",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 430,
            y: 460,
            baseLine: "top", //
            text:
              "━" +
              "      " +
              "￥" +
              this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice, // 折扣活动
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 320,
            y: 426,
            baseLine: "top",
            text: "总价格", // 总价格
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorRight,
            zIndex: 300,
          },
          {
            x: 320,
            y: 460,
            baseLine: "top", // 总价格
            text: "￥" + this.goodsSpu.estimatedPriceVo.originalPrice,
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorRight,
            zIndex: 300,
          },
          {
            x: 160,
            y: 426,
            baseLine: "top", // 预估到手价  显示
            text: "预估到手价",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceColorLeft,
            zIndex: 300,
          },
          {
            x: 164,
            y: 460,
            baseLine: "top", // 预估到手价
            text:
              "￥" +
              this.goodsSpu.estimatedPriceVo.estimatedPrice +
              "   " +
              "=",
            fontSize: 22,
            fontWeight: "900",
            color: this.spuPriceValueColorLeft,
            zIndex: 300,
          },
        );
      }
      const promotion_type_list = [];
      if (this.goodsSpu.estimatedPriceVo?.promotionsDiscountPrice > 0) {
        promotion_type_list.push("折扣");
      }
      if (
        this.goodsSpu.estimatedPriceVo?.coupon > 0 ||
        this.goodsSpu.estimatedPriceVo?.discountPrice > 0
      ) {
        promotion_type_list.push("优惠券");
      }

      this.shareParams = {
        title: "发现一个好物，推荐给你呀",
        desc: this.goodsSpu.name,
        imgUrl: this.goodsSpu.picUrls[0],
        scene: this.goodsSpu.id,
        page: "pages/goods/goods-detail/index",
        posterConfig,
        posterConfig2,
        trackParams: {
          forward_source: getCurrentTitle(1),
          source_module: decodeURIComponent(this.source_module),
          activity_id: this.activity_id,
          //商品原价划线价
          goods_crossed_price: Number(
            this.goodsSpu.estimatedPriceVo?.originalPrice,
          ),
          // 到手价
          goods_strike_price: Number(
            this.goodsSpu.estimatedPriceVo?.estimatedPrice,
          ),
          //优惠类型
          promotion_type_list,
          // 是否是推荐商品
          is_recommend_goods: false,
          click_content: "分享",
          trackType: "GoodsShareOrCollect",
          // 是否描述 秒杀入口进来判断
          is_flash_sale: false,
          goodsSpu: this.goodsSpu,
        },
      };
      this.showShare = true;
    },
    dataController(query) {
      api.dataController({
        bhv_type: query.bhv_type,
        item_id: this.goodsSpu.id,
      });
    },

    // 更新自提地点
    updateShopAddress(obj) {
      this.$refs?.goodsSkuDia?.$refs?.orderPay?.updateShopAddress(obj);
    },
    // 更新可选择银行卡
    updataBankList() {
      this.$refs.goodsSkuDia?.updataBankList();
    },
    wxTemplate(type) {
      // #ifdef MP
      getWxTemplate({
        type: type || 10,
      }).then((res) => {
        uni.requestSubscribeMessage({
          tmplIds: res.data,
          complete: () => {
            this.wxTemplateNext(type);
          },
        });
      });
      // #endif
      // #ifndef MP
      this.wxTemplateNext(type);
      // #endif
    },
    wxTemplateNext(type) {
      if (+type === 11) {
        const { goodsSpu } = this;
        if (this.goodsSpu.registerCollectId) {
          api.userCollectDel(this.goodsSpu.registerCollectId).then((res) => {
            uni.showToast({
              title: "已取消到货提醒",
              icon: "success",
              duration: 2000,
            });
            this.goodsSpu.registerCollectId = null;
          });
        } else {
          api
            .userCollectAdd({
              type: "3",
              relationIds: [goodsSpu.id],
            })
            .then((res) => {
              uni.showToast({
                title: "到货提醒成功",
                icon: "success",
                duration: 2000,
              });
            });
          this.goodsSpu.registerCollectId = goodsSpu.id;
        }
        return;
      }
      this.userCollect();
    },

    handleSwiperChange(pos) {
      if ((pos == 0 && this.currents == 0) || (pos == 1 && this.currents > 0)) {
        return;
      }
      this.currents = pos;
      console.log("===this.currents ==", this.currents);
    },

    async handleAddCart(e) {
      if (
        (!this.goodsSpecData || this.goodsSpecData.length == 0) &&
        (!this.goodsSpu.similarSpuList ||
          !this.goodsSpu.similarSpuList.length == 0)
      ) {
        const params = {
          spuId: this.goodsSpu.id,
          skuId: this.goodsSku.id,
          quantity:
            this.goodsSku && this.goodsSku.minBuyNum > 0
              ? this.goodsSku.minBuyNum
              : 1,
          addPrice: this.goodsSku.salesPrice,
          shopId: this.goodsSpu.shopId,
          spuName: this.goodsSpu.name,
          specInfo: "",
          picUrl: this.goodsSku.picUrl
            ? this.goodsSku.picUrl
            : this.goodsSpu.picUrls[0],
        };
        if (this.goodsSpu.spuType == 6) {
          params.type = 3;
        }
        await api.shoppingCartAdd(params);
        await this.operateCartEvent();
        setTimeout(() => {
          uni.showToast({
            title: "加购成功",
            duration: 2000,
            icon: "none",
          });
        }, 500);
        this.handleSenGoods("GoodsAddToCart", {
          page_name: getCurrentTitle(0),
          sku_id: this.goodsSku.id,
          sku_name: this.goodsSpu.name + "-单规格",
        });
      } else {
        this.showModalSku(e);
      }
    },

    toggleMoreShow() {
      this.isMoreShow = !this.isMoreShow;
    },

    handleCollect(data) {
      this.handleSenGoods("GoodsShareOrCollect", {
        click_content: data,
      });
    },

    handleSenGoods(type, params = {}) {
      const promotion_type_list = [];
      if (this.goodsSpu.estimatedPriceVo?.promotionsDiscountPrice > 0) {
        promotion_type_list.push("折扣");
      }
      if (
        this.goodsSpu.estimatedPriceVo?.coupon > 0 ||
        this.goodsSpu.estimatedPriceVo?.discountPrice > 0
      ) {
        promotion_type_list.push("优惠券");
      }
      senGoods(
        type,
        {
          forward_source: getCurrentTitle(1),
          source_module: decodeURIComponent(this.source_module),
          activity_id: this.activity_id,
          //商品原价划线价
          goods_crossed_price: Number(
            this.goodsSpu.estimatedPriceVo?.originalPrice,
          ),
          // 到手价
          goods_strike_price: Number(
            this.goodsSpu.estimatedPriceVo?.estimatedPrice,
          ),
          //优惠类型
          promotion_type_list,
          // 是否是推荐商品
          is_recommend_goods: false,
          // 是否描述 秒杀入口进来判断
          is_flash_sale: false,
          ...params,
        },
        this.goodsSpu,
      );
    },
  },
};
</script>
<style lang="scss" scoped>
@import "public/colorui/media.scss";

.detail-btn {
  height: 72rpx;
  border-radius: 72rpx;
}

.shareIcon {
  width: 42rpx;
  height: 42rpx;
}

.action-icon {
  width: 85rpx;
}

.reduce-layout {
  width: 180rpx;
  height: 146rpx;
  right: 40rpx;
  top: 40rpx;
  position: absolute;
  z-index: 3;
  background-size: 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;

  .reduce-tag {
    color: #fff;
  }

  .reduce-price-content {
    font-size: 24rpx;
    color: #fff;
  }

  .reduce-price {
    font-size: 44rpx;
  }
}

@media (min-width: $breakpoint-medium) and (max-width: $breakpoint-large) {
  .detail-btn {
    font-size: 14px;
    height: 40px;
  }

  .action-icon {
    width: 42px;
  }

  .reduce-layout {
    width: 90px;
    height: 73px;
    right: 20px;
    top: 20px;
    padding-top: 4rpx;

    .reduce-price-content {
      font-size: 12px;
    }

    .reduce-price {
      font-size: 22px;
    }
  }

  .shareIcon {
    width: 20px;
    height: 20px;
  }

  .swiper-img .img-price {
    bottom: 4px;
  }
}

.shop-detail image {
  width: 134rpx !important;
  height: 134rpx !important;
  margin: 6rpx 0;
}

.cu-btn2 {
  opacity: 1;
  color: #ffffff;
}

.img-border {
  border-left-width: 6rpx;
  border-right-width: 6rpx;
  border-top-width: 6rpx;
  border-style: solid;
  border-color: transparent;
}

// swiper视频
.videoBtn {
  color: #fff;

  .playBtn {
    position: absolute;
    z-index: 100;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 120rpx;
  }

  .voiceBtn {
    position: absolute;
    z-index: 100;
    bottom: 10rpx;
    right: 10rpx;
    transform: translate(-50%, -50%);
    // font-size: 120rpx;
  }
}

// 货品名称部分
.goodsNameCon {
  margin: 20rpx;
  border-radius: 20rpx;
}

// 商品选择部分
.goodsChoice {
  .colorClass {
    display: flex;
    align-items: center;
    width: 100%;

    image {
      width: 12%;
      height: 120rpx;
      margin-right: 8rpx;
    }
  }
}

.label_goodsChoice {
  width: 90rpx;
  font-weight: bold;
  color: #000;
}

.myBar {
  min-height: auto !important;
}

.myBar1 {
  min-height: 50rpx !important;
}

.myBar1 .content {
  font-size: 28rpx;
}

.product-bg {
  width: 100%;
  position: relative;
}

.product-bg swiper {
  width: 100%;
  // height: calc(100vw);
  // height: 914rpx;
  position: relative;
}

.product-bg video {
  width: 100%;
  height: calc(100vw);
  position: relative;
}

.product-bg .page-index {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
}

.cu-bar.tabbar.shop .action {
  width: unset;
}

.to-down {
  margin-bottom: 100rpx;
}

.coupon {
  // border-radius: 4rpx;
  // padding: 8rpx 20rpx 8rpx 20rpx;
  // background: radial-gradient(circle at bottom left, transparent 4px, #f9e7d4 0) top left,
  //             radial-gradient(circle at bottom right, transparent 4px, #f9e7d4 0) top right,
  //             radial-gradient(circle at top left, transparent 4px, #f9e7d4 0) bottom left,
  //             radial-gradient(circle at top right, transparent 4px, #f9e7d4 0) bottom right;
  // background-repeat: no-repeat;
  // background-size: 51% 51%;
  padding: 8rpx;
  background: #fff6f5;
  border-radius: 10rpx;
}

.show-bg {
  height: 84%;
  margin-top: 120rpx;
}

.image-box {
  height: 90%;
}

.show-btn {
  margin-top: -130rpx;
}

.markUrlsList {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  height: 40rpx;
  display: flex;
  flex-direction: row-reverse;
  z-index: 1;

  .imgbox {
    display: block;
    width: 40rpx;
    height: auto;
  }
}

.transparent {
  color: transparent;
}

.activityBgImgUrl,
.activityBgImgUrl image {
  width: 100%;
  height: 88rpx;
  margin-bottom: 8px;
}

.storeBgImgUrl,
.storeBgImgUrl image {
  width: 100%;
  height: 97rpx;
  border-radius: 25rpx;
}

.swiper-img {
  width: 750rpx;
  position: absolute;
}

.swiper-img .img-bg {
  position: absolute;
  z-index: 2;
  bottom: 20rpx;
  width: 750rpx;
  height: 160rpx;
}

.swiper-img .img-text {
  // position: absolute;
  z-index: 3;
  width: 255rpx;
  height: 72rpx;
  margin: 0 auto;
  padding-top: 10rpx;
}

.swiper-img .img-price {
  width: 100%;
  position: absolute;
  z-index: 4;
  bottom: 14rpx;
  color: #fff;
  display: flex;
}

.video-img-switch {
  background: #000000;
  border-radius: 10rpx 0rpx 0rpx 0rpx;
  opacity: 0.4;
  position: absolute;
  z-index: 4;
  bottom: 64px;
  color: #c9c9c9;
  right: 0;

  text {
    flex: 1;
  }

  .selected {
    color: #fff;
  }
}

// 在非首屏轮播的时候，距离底部是0
.isImg {
  bottom: 0;
}

.img-price-box {
  width: 42%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.img-price-box .img-price-text {
  font-size: 66rpx;
  font-weight: bold;
}

.img-price-box .img-price-text text,
.img-price-box2 .img-price-text text,
.img-price-box4 .img-price-text text {
  font-size: 20rpx;
}

.img-price-box3 {
  width: 5%;
  text-align: center;
  color: #fff;
}

.img-price-box2 {
  width: 30%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.img-price-box4 {
  width: 16%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.img-price-box2 .img-price-text {
  font-size: 38rpx;
  font-weight: bold;
}

.img-price-box4 .img-price-text {
  font-size: 30rpx;
  font-weight: bold;
}

.selecjc {
  width: 97%;
  margin-right: 121px;
}

.screen-swiper image,
.screen-swiper video,
.swiper-item image,
.swiper-item video {
  height: calc(100vw);
}

.parent-container {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.layout-discount_left {
  width: 150rpx;
}

.detail-price-layout {
  background-image: url(https://img.songlei.com/live/goods/customer-bg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.similarSpu {
  width: 52rpx;
  height: 52rpx;
}

.similarDefault {
  background: #f9f7fc;
  color: #000000;
}

.similarSelect {
  background: #fff7f5;
  border: 1px solid #ff530c;
  color: #ff530c;
}

.arrowDown {
  transform: rotate(90deg);
}

.goods-tip {
  color: #959396;
}

.height100vw {
  height: 100vw;
}

// .swiper-img .img-price {
// 	width: 100%;
// 	position: absolute;
// 	z-index: 4;
// 	bottom: 8rpx;
// 	color: #fff;
// 	display: flex;
// 	height: 104rpx;
// }
</style>
