// 使用wxs处理交互动画, 提高性能, 同时避免小程序bounce对下拉刷新的影响

var me = {
  offset: 80, //设置距离
  translateY: 0
};

/* 根据点击滑动事件获取第一个手指的坐标 */
me.getPoint = function(e) {
	if (!e) {
		return {x: 0,y: 0}
	}
	if (e.touches && e.touches[0]) {
		return {x: e.touches[0].pageX,y: e.touches[0].pageY}
	} else if (e.changedTouches && e.changedTouches[0]) {
		return {x: e.changedTouches[0].pageX,y: e.changedTouches[0].pageY}
	} else {
		return {x: e.clientX,y: e.clientY}
	}
}
/* 下拉过程中的回调,滑动过程一直在执行 (rate<1为inOffset; rate>1为outOffset) */
me.onMoving = function (ins){
	ins.requestAnimationFrame(function () {
		ins.selectComponent('.top-refresh').setStyle({
			'will-change': 'transform', // 可解决下拉过程中, image和swiper脱离文档流的问题
			'transform': 'translateY(' + me.translateY + 'px)',
		})
    ins.selectComponent('.flex-lunbo').setStyle({
    	'will-change': 'transform', // 可解决下拉过程中, image和swiper脱离文档流的问题
    	'transform': 'translateY(' + me.translateY + 'px)',
    })
	})
}

/* 调用逻辑层的方法 */
me.callMethod = function(ins, param) {
	if(ins) ins.callMethod('wxsCall', param)
}

function touchstartEvent(e) {
  var pageX = e.changedTouches[0].pageX;
  var pageY = e.changedTouches[0].pageY;
  me.allPageY = pageY;
  me.curPointY = pageY;
}

function touchmoveEvent(e, ins) {
  var pageY = e.touches[0].pageY;
  var allPageY = me.allPageY || 0;
  var diff = pageY - me.curPointY;
  var isBlod = diff >= 0 ? 1 : -1;
  diff = diff * isBlod;
  if(!me.isTop && isBlod > 0) {
    return;
  }
  if(!me.isBottom && isBlod < 0) {
    return;
  }
  var num = 0;
  if(me.translateY * isBlod < 0) {
    num = diff;
  } else {
    if(me.translateY <= me.offset) {
      num = diff * 0.7;
    } else {
      num = diff * 0.2;
    }
  }
  
  me.translateY += num * isBlod;
  me.translateY = Math.round(me.translateY)
  me.onMoving(ins);
  me.curPointY = pageY;
}

function touchendEvent(e, ins) {
  if(me.translateY >= me.offset) {
    me.callMethod(ins, 'up')
  } else if(me.translateY <= -1 * me.offset) {
    me.callMethod(ins, 'down')
  }
  me.allPageY = 0;
  me.translateY = 0;
  ins.setTimeout(function(){
    me.onMoving(ins)
  }, 100)
}

function callObserver(callProp, oldValue, ins) {
  if(callProp) {
    me.isTop = callProp.isTop;
    me.isBottom = callProp.isBottom;
  }
}

module.exports = {
  translateY: function() {
    return me.translateY;
  },
	callObserver: callObserver,
	touchstartEvent: touchstartEvent,
	touchmoveEvent: touchmoveEvent,
	touchendEvent: touchendEvent
}
