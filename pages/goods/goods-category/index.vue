<template>
	<view style="overflow: hidden;width: 100vw;height: 100vh;">
		<cu-custom ref="custom" bgColor="#f6f6f6" simple :isBack="false" :pageTo="pageTo" :iconColor="black" :hideMarchContent="false">
			<view slot="marchContent">
				<view 
          class="search-form round" 
          :style="{
            height: `${HeightBar}px`,
            lineHeight: '100%',
            marginRight:' 20rpx',
            marginLeft:' 0',
            border:' 1rpx solid #EAEAEA',
            background: '#fff',
            width: `calc(750rpx - ${ menuWidth + leftMenuWidth/2 }px)`
          }"
        >
					<text class="cuIcon-search" style="font-size: 30rpx;color: #A1A1A1"></text>
          <input 
            :disabled="true"
            class="uni-input"
            placeholder-style="color: #a1a1a1; font-size:30rpx"
            v-model="name"
            type="text"
            :placeholder="placeholder"
            confirm-type="search"
            @confirm="searchClick"
            style="flex: 1;"
            @click="navTor('/pages/base/search/index')"
          >
          </input>
          <text
            @click.stop="scanCode"
            class="cuIcon-scan"
            style="font-size: 32rpx;padding: 0rpx 20rpx 0rpx 16rpx;margin: 0;"
          ></text>
					<image 
            :style="{maxHeight: `48rpx`, width: `4rpx`}"
            mode="aspectFit"
						src="https://img.songlei.com/share/scan.png"
						style="height: 60rpx;width: 20rpx;"
          ></image>
          <text
            @click.stop="cameraImages"
            class="cuIcon-camera"
            style="font-size: 32rpx;padding: 0rpx 20rpx 0rpx 16rpx;margin: 0;"
          ></text>
				</view>
			</view>
		</cu-custom>
		<view class="verticalbox flex" id="main" :style="{ height: `calc(100vh - ${initHeight}px - 1px)` }">
      <view v-if="TabCur == 0" style="position: absolute;background-color: #fff;width: 40upx;height: 40upx;z-index: 0;left: 160rpx;"></view>
			<scroll-view 
        class="VerticalNav nav" 
        scroll-y scroll-with-animation 
        :scroll-anchoring = "true"
        :scroll-top="VerticalNavTop"
				style="background: #F6F6F6;font-size: 30upx;">
				<view class="set-text" :class="'cu-item ' + (index==TabCur?'cur':'')"
					v-for="(item, index) in goodsCategory" :key="index" @tap="tabSelect" :data-id="index">
          <view style="border-bottom: 4upx solid #fff;">
            <image v-if="index==TabCur"  src="https://img.songlei.com/-1/material/96103b3e-269c-4ef5-8bdc-def078eaa2a4.png" class="cur-icon"></image>
            <text style="position: relative;z-index: 1;" v-if="item.name.length <= 3">{{item.name}}</text>
            <text style="position: relative;z-index: 1;" v-if="item.name.length > 3">{{item.name}}</text>
          </view>
          <image v-if="index==TabCur" class="cur-img" src="https://img.songlei.com/-1/material/a2a684a6-b660-455b-8760-b62fa60aea6c.png"></image>
				</view>
			</scroll-view>
      <!-- 右侧导航栏, 主要是实现下拉效果和上拉效果 -->
      <scroll-view
        class="verticalmain" 
        scroll-y scroll-with-animation
        @touchstart="wxsBiz.touchstartEvent"
        @touchmove="wxsBiz.touchmoveEvent" 
        @touchend="wxsBiz.touchendEvent" 
        @touchcancel="wxsBiz.touchendEvent"
      >
        <view class="top-refresh" style="position: absolute;top: -50px;">
          <image src="https://img.songlei.com/-1/material/0f54de8f-6e0d-46e3-ae9a-f97cfb41dac3.png" mode="widthFix"></image>
          <text v-if="isTop">下拉继续浏览{{ goodsCategory[TabCur - 1].name }}</text>
        </view>
        <!-- 右侧导航栏具体内容 -->
				<scroll-view 
          class="flex-lunbo"
          scroll-y scroll-with-animation
          :scroll-top="scrollTop"
          @scroll="VerticalMain"
          :change:prop="wxsBiz.callObserver" 
          :prop="callProp"
        >
          <view>
            <!-- 广告swiper -->
            <view 
              id="bannerSwiper"
              v-if="bannerSwiperList" 
              style="width: 100%;padding:35rpx 28rpx 0rpx;">
              <swiper
                indicator-color='rgba(248, 231, 221, 1)'
                indicator-active-color="#ffffff"
								circular
                class="square-dot"
                :indicator-dots="true"
                style="height: 198rpx;width: 100%;border-radius: 15rpx;overflow: hidden;">
                <swiper-item 
                  v-for="(item15,index14) in bannerSwiperList.children"
                  :key="index14" @tap="navTor(`${item15.page}`)">
                    <image
                      mode="aspectFill"
                      style="width: 100%;height: 100%;border-radius: 15rpx;"
                      :src="item15.picUrl ? item15.picUrl : 'https://img.songlei.com/live/img/no_pic.png'"
                    >
                    </image>
                </swiper-item>
              </swiper>
            </view>
            <!-- 内容中的顶部导航栏 -->
            <view v-if="contentList.length > 1" class="sticky" style="height:35px;line-height: 35px;">
              <view style="overflow: hidden; display: flex;flex-wrap: nowrap;box-sizing: border-box;">
                <scroll-view
                  scroll-x="true"
                  class="scroll-view_H" 
                  scroll-y scroll-with-animation
                  style="overflow: hidden;"
                  :scroll-left="scrollLeft"
                >
                  <text v-for="(item9, index9) in contentList" :key="index9"
                    :id="'main-top-' + index9"
                    class="viewsHeader"
                    :class="[index9==ind?'selectext':'']"
                    @click="viewmd(item9,index9)">{{item9.name}}</text>
                </scroll-view>
              </view>
            </view>
            <!-- 滑动内容数据 -->
            <scroll-view 
              class="cu-bar bg-white border-top-radius"
              v-if="contentList.length>0"
              style="display: flex;flex-direction: column;"
            >
              <view v-for="(item2, index2) in contentList" :key="index2"
                :id="`main-${index2}`"
                style="display: flex;flex-direction: column;width: 100%;">
                <!-- 轮播图数据 -->
                <view v-if="item2.swiperChildren && item2.swiperChildren.length > 0" style="width: 100%;">
                  <view style="width:100%" :id="'main-' + index2">
                    <navigator hover-class="none" v-if="item2.categoryType=='2'">
                      <view class="text-df">{{item2.name}}</view>
                    </navigator>
                  </view> 
                  <swiper 
                    circular="true"
                    indicator-color='rgba(248, 231, 221, 1)'
                    indicator-active-color="#DDC7BA"
                    :class="item2.swiperChildren.length === 1 ? '': 'square-dot'"
                    :style="{ 'height' : (item2.swiperChildren.length && (item2.swiperChildren[0].length % 3 ? ~~(item2.swiperChildren[0].length / 3) + 1 : item2.swiperChildren[0].length / 3 ) * itemHeight + 40 + 'rpx')}"
                    style="display: flex;padding-right: 28rpx;padding-left: 28rpx;"
                    :indicator-dots="item2.swiperChildren.length === 1 ? false: true">
                    <swiper-item v-for="(item6, index6) in item2.swiperChildren" :key="index6">
                      <view class="viewafter"
                        style="display: flex;flex-wrap: wrap;justify-content: space-between;overflow: hidden;"
                      >
                        <view 
                          v-for="(item7,index7) in item6"
                          :key="index7"
                          class="cate text-xs text-center"
                          :style="{ height: itemHeight + 'rpx'}"
                        >
                          <view
                            @click="navTor(`/pages/goods/goods-list/index?categorySecond=${item7.shopBrandCategoryId}&title=${item7.name}&shopBrandId=${item7.shopBrandId}&categoryType=${item7.categoryType}&name=${item7.name}`)" 
                            v-if="item2.categoryType=='2'"
                          >
                            <image class="cate-img" mode="aspectFit"
                              :src="item7.picUrl ? item7.picUrl : 'https://img.songlei.com/live/img/no_pic.png'">
                            </image>
                            <view class="cate-type text-sm"
                              style="width: 136rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                              {{item7.name}}
                            </view>
                          </view>
                        </view>
                      </view>
                    </swiper-item>
                  </swiper>
                </view>
                
                <view class="two_tree" v-else-if="item2.children.length>0">
                  <view style="width: 100%;">
                    <view
                      class="text-df"
                      @click="navTor(`/pages/goods/goods-list/index?categoryThird=${item2.id}&title=${item2.name}`)"
                    >{{item2.name}}</view>
                  </view>
                  <view
                    style="display: flex;flex-wrap: wrap;justify-content: space-between;padding-left: 28rpx;padding-right: 28rpx;width: 100%;"
                    class="viewafter" v-if="item2.type=='2'||item2.type=='1'">
                    <view v-for="(item3, index2) in item2.children" :key="index2">
                      <view v-if="item2.type=='2'" class="cate text-xs text-center" @click="navTor(`${item3.page}`)">
                        <image mode="aspectFit" class="cate-img"
                          :src="item3.picUrl ? item3.picUrl : 'https://img.songlei.com/live/img/no_pic.png'">
                        </image>
                        <view class="cate-type text-sm"
                          style="width: 136rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                          {{item3.name}}
                        </view>
                      </view>
                      <view v-else-if="item2.type=='1'" class="cate text-xs text-center">
                        <view
                          v-if="item2.type!='99'&&item3.hierarchy=='1'"
                          @click="navTor(`${item3.page}`)"> 
                          <image class="cate-img" mode="aspectFit"
                            :src="item3.picUrl ? item3.picUrl : 'https://img.songlei.com/live/img/no_pic.png'">
                          </image>
                          <view class="cate-type text-sm"
                            style="width: 136rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                            {{item3.name}}
                          </view>
                        </view>
                        <view
                          v-else-if="item2.type!='99'&&item3.hierarchy=='2'"
                          @click="navTor(`/pages/goods/goods-list/index?categorySecond=${item3.id}&title=${item3.name}`)">
                          <image class="cate-img" mode="aspectFit"
                            :src="item3.picUrl ? item3.picUrl : 'https://img.songlei.com/live/img/no_pic.png'">
                          </image>
                          <view class="cate-type text-sm"
                            style="width: 136rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                            {{item3.name}}
                          </view>
                        </view>
                        <view
                          v-else-if="item2.type!='99'&&item3.hierarchy=='3'"
                          @click="navTor(`/pages/goods/goods-list/index?categoryThird=${item3.id}&title=${item3.name}`)"
                        >
                          <image class="cate-img" mode="aspectFit"
                            :src="item3.picUrl ? item3.picUrl : 'https://img.songlei.com/live/img/no_pic.png'">
                          </image>
                          <view class="cate-type text-sm"
                            style="width: 136rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                            {{item3.name}}
                          </view>
                        </view>
                        <view
                          v-else-if="item2.type!='99'&&item3.hierarchy=='4'"
                          @click="navTor(`/pages/goods/goods-list/index?categoryFourth=${item3.id}&title=${item3.name}`)"
                        >
                          <image class="cate-img" mode="aspectFit"
                            :src="item3.picUrl ? item3.picUrl : 'https://img.songlei.com/live/img/no_pic.png'">
                          </image>
                          <view class="cate-type text-sm"
                            style="width: 136rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                            {{item3.name}}
                          </view>
                        </view>
                        <view
                          v-else-if="item2.type!='99'&&item3.hierarchy=='5'"
                          @click="navTor(`/pages/goods/goods-list/index?categoryFifth=${item3.id}&title=${item3.name}`)"
                        >
                          <image class="cate-img" mode="aspectFit"
                            :src="item3.picUrl ? item3.picUrl : 'https://img.songlei.com/live/img/no_pic.png'">
                          </image>
                          <view class="cate-type text-sm"
                            style="width: 136rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                            {{item3.name}}
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                  <view class="" v-else-if="item2.type=='3'&&item2.children&&item2.children.length" style="height: 170rpx;width: 100%;display: flex;
                      align-items: center;
                      justify-content: center;
                      padding-left: 66rpx;
                      padding-right: 45rpx;">
                    <swiper :indicator-dots="true" style="height: 300rpx;width: 100%;">
                      <swiper-item v-for="(item15,index14) in item2.children" style="height: 300rpx;"
                        :key="index14">
                        <view
                          style="height:300rpx;display: flex;align-items: center;justify-content: center;"
                          @click="navTor(`${item15.page}`)"
                        >
                          <image mode="aspectFit"
                            style="height:200rpx;width:100%;margin-right: 20rpx;"
                            :src="item15.picUrl ? item15.picUrl : 'https://img.songlei.com/live/img/no_pic.png'">
                          </image>
                        </view>
                      </swiper-item>
                    </swiper>
                  </view>
                </view>
              </view>
              <view class="bottom-refresh" v-if="isBottom">
                <image src="https://img.songlei.com/-1/material/0f54de8f-6e0d-46e3-ae9a-f97cfb41dac3.png" mode="widthFix"></image>
                <text>上拉继续浏览{{ goodsCategory[TabCur + 1].name }}</text>
              </view>
            </scroll-view>
          </view>
        </scroll-view>
      </scroll-view>
		</view>
    <camera-model :showPopup.sync="showPopup"></camera-model>
	</view>
</template>

<script src="./index.wxs" module="wxsBiz" lang="wxs"></script>

<script>
	const app = getApp();
	const util = require("utils/util.js");
	import api from "utils/api";
    import { gotoPage } from "utils/goPage"
	import {
		mapState,
	} from 'vuex'
  import CameraModel from '@/components/camera-model/index'
	export default {
    components: { CameraModel },
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				TabCur: 0,
				MainCur: 0,
				VerticalNavTop: 0,
				goodsCategory: [],
				load: true,
				parentId: 0,
				//有数统计使用
				page_title: "商城商品分类",
				placeholder: "搜索",
				black: "black",
				itemHeight: 200,
				mainnameIndex: "",
				ind: '',
        initHeight: 0,
        translateY: 0,
        allPageY: 0,
        isTop: false,
        isBottom: false,
        scrollLeft: 0,
        contentList: [],
        selectCategoryId: '',
        bannerSwiperList: null,
        scrollTop: 0,
        showPopup: false
			};
		},
    computed: {
		...mapState([
			'windowWidth',
			'HeightBar',
			'CustomBar',
			'menuWidth',
			'leftMenuWidth',
			'pixelRatio'
		]),
      callProp() {
        return {
          isTop: this.isTop,
          isBottom: this.isBottom
        }
      }
    },
		onLoad(options) {
			this.parentId = options.id || 0;
      this.selectCategoryId = options.typeId || '';
			app.initPage().then((res) => {
				this.goodsCategoryTree();
			});
      this.initHeight = (this.$refs?.custom?.CustomBar) || 50;
		},

		onShow() {
			let that = this;
			uni.removeStorageSync("param-goods-category-index"); //清空参数
			//获取本地参数，选中指定分类
			uni.getStorage({
				key: "param-goods-category-index",
				success: function(res) {
					// uni.removeStorageSync('param-goods-category-index');//清空参数
					let index = res.data;
					that.tabSelect({
						//选中指定分类
						currentTarget: {
							dataset: {
								id: index,
							},
						},
					});
				},
			});
		},

		methods: {
			viewmd(item, index) {
				this.ind = index;
				// this.MainCur = this.ind;
        this.getTopScollWidth();
        this.load = true;
        this.scrollTop = this.contentList[index].top 
        setTimeout(() => {
          this.load = false;
        }, 1000)
			},
			scanCode() {
				uni.navigateTo({
					url: "/pages/base/search/index",
				});
			},
			jumpPage(url) {
				if (url) {
					uni.navigateTo({
						url: url,
					});
				}
			},
			goodsCategoryTree() {
				api
        .goodsCategoryTreeByLevel({
          parentId: this.parentId,
          selectCategoryId: this.selectCategoryId,
          level: 4,
        })
        .then((res) => {
          if (res.data && res.data.length) {
            this.goodsCategory = res.data;
            this.goodsCategory.forEach((itemOne, index) => {
              const children = []
              itemOne?.children?.forEach((itemTwo) => {
                if (itemTwo.categoryType == "2") {
                  const iconsArr = []; // 声明数组
                  let num = 12;
                  itemTwo?.children?.forEach((item, index) => {
                    const page = Math.floor(index /
                    num); // 计算该元素为第几个素组内
                    if (!iconsArr[page]) {
                      // 判断是否存在
                      iconsArr[page] = [];
                    }
                    iconsArr[page].push(item);
                  });
                  itemTwo.swiperChildren = iconsArr;
                }
              });
              
            });
            const goodsCategory = [];
            this.goodsCategory.forEach(item => {
              if(item.children?.length) {
                const children = [];
                item.children?.forEach(items => {
                  if(items.children?.length) {
                    children.push(items);
                  }
                })
                if(children.length) {
                  goodsCategory.push({ ...item, children });
                }
              }
            });
            this.goodsCategory = goodsCategory;
            this.tabSelect({
              currentTarget: {
              	dataset: {
              		id: 0,
              	},
              },
            })
          }
        });
			},
			tabSelect(e) {
        const id = e.currentTarget.dataset.id;
        if(+id === +this.TabCur && +id !== 0) {
          return ;
        }
				this.TabCur = e.currentTarget.dataset.id;
				this.MainCur = 0;
        this.ind = 0;
        this.scrollLeft = 0;
				this.VerticalNavTop = (e.currentTarget.dataset.id - 1) * 50;
        this.isTop = true;
        this.isBottom = true;
        this.contentList = []
        this.bannerSwiperList = null
        if(+this.TabCur === 0) {
          this.isTop = false;
        }
        if(+this.TabCur === this.goodsCategory.length - 1) {
          this.isBottom = false;
        }
        this.$nextTick(() => {
          let bannerSwiperList = null;
          this.contentList = this.goodsCategory[this.TabCur].children.filter(item => {
            if(+item.type === 3) {
              if(!bannerSwiperList) {
                bannerSwiperList = item;
              }
              return false;
            }
            return true;
          });
          console.log('contentList', this.contentList)
          this.bannerSwiperList = bannerSwiperList
          this.$nextTick(() => {
            this.getallHeight();
          })
        })
			},
      getallHeight() {
        let tabHeight = 0;
        let tabWidth = 0
        const { contentList } = this;
        const { length = 0 } = contentList;
        const { bannerSwiperList } = this;
        if(bannerSwiperList) {
          const swiperView = uni.createSelectorQuery().in(this).select("#bannerSwiper")
          swiperView.fields({
            size: true
          }, data => {
            tabHeight+= (data && data.height)|| 0
          }).exec()
        }
        for (let i = 0; i < length; i++) {
          const view = uni.createSelectorQuery().in(this).select("#main-" + i);
          view.fields({
            size: true
          }, data => {
            contentList[i].top = tabHeight;
            if (data && data.height > 0) {
              tabHeight = tabHeight + data.height;
              contentList[i].height = data.height;
              contentList[i].width = data.width;
            }
            contentList[i].bottom = tabHeight;
          }).exec();
          const views = uni.createSelectorQuery().in(this).select("#main-top-" + i);
          views.fields({
            size: true
          }, data => {
            contentList[i].left = tabWidth;
            if(data && data.width > 0) {
              tabWidth = tabWidth + data.width;
            }
          }).exec()
        }
        this.load = false
      },
      wxsCall(type) {
        if(type === 'up') {
          this.tabSelect({
            //选中指定分类
            currentTarget: {
              dataset: {
                id: this.TabCur - 1,
              },
            },
          });
        } else {
          this.tabSelect({
            //选中指定分类
            currentTarget: {
              dataset: {
                id: this.TabCur + 1,
              },
            },
          });
        }
      },
			VerticalMain(e, ins) {
				let that = this;
        if(this.load) {
          return;
        }
        const { length = 0 } = this.contentList;
        const children = this.contentList
				let scrollTop = e.detail.scrollTop + 5;
        for (let o = 0; o <  length; o++) {
          if(scrollTop >= children[o].top && scrollTop < children[o].bottom) { 
            if(this.ind !== o) {
              this.ind = o;
              this.getTopScollWidth();
            }
            break;
          }
        }
			},
      getTopScollWidth() {
        const { ind } = this;
        const { width: allWidth, left = 0 } = this.contentList[ind];
        this.scrollLeft = left - allWidth / 3
      },
      touchstartEvent(e) {
        const { pageX, pageY } = e.changedTouches[0]
        this.allPageY = pageY;
      },
      touchmoveEvent(e) {
        const { pageX, pageY } = e.changedTouches[0];
        const { allPageY } = this;
        let translateY = pageY - allPageY;
        const isBlod = translateY >= 0 ? 1 : -1;
        translateY = translateY * isBlod;
        if(!this.isTop && isBlod > 0) {
          return;
        }
        if(!this.isBottom && isBlod < 0) {
          return;
        }
        let num = 0;
        if (translateY <= 50) {
          num = translateY * 0.9;
        } else {
          num = 50 * 0.9;
          translateY -= 50;
          const fors = Math.ceil(translateY / 20);
          for(let i=0; i < fors; i++) {
            const bian = (0.8 - i*0.1) < 0.1 ? 0.1 : (0.8 - i*0.1);
            num += 20 * bian
          }
        }
        this.translateY = num * isBlod;
      },
      touchendEvent() {
        if(this.translateY >= 75) {
          this.tabSelect({
          	//选中指定分类
          	currentTarget: {
          		dataset: {
          			id: this.TabCur - 1,
          		},
          	},
          });
        } else if(this.translateY <= -50) {
          this.tabSelect({
          	//选中指定分类
          	currentTarget: {
          		dataset: {
          			id: this.TabCur + 1,
          		},
          	},
          });
        }
        this.allPageY = 0;
        this.translateY = 0;
      },
      navTor(page) {
        // console.log(page);
        gotoPage(page || '/pages/goods/goods-list/index');
      },
      cameraImages() {
        this.showPopup = true;
      },
		},
	};
</script>

<style scoped>
	.border-top-radius {
		/* border-radius: 15rpx 15rpx 0rpx 0rpx; */
	}

	.border-bottom-radius {
		/* border-radius: 0rpx 0rpx 15rpx 15rpx; */
	}
</style>
<style>
	.swiper {
		height: 300rpx;
	}



	.scroll-view_H {
		height: 100%;
		width: 100%;
		white-space: nowrap;
    padding-left: 28upx;
	}

	.viewsHeader {
		display: inline-block;
    height: 28px;
    line-height: 28px;
    padding-right: 28rpx;
    font-size: 28upx;
    text-align: center;
	}

	.QS_fadeInLeft {
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		background: #fff;
		padding-left: 20rpx;
		height: 60rpx;
	}

	.set-text {
		white-space: nowrap;
		text-overflow: ellipsis;
		/* overflow: hidden; */
	}
  .VerticalNav {
    width: 160upx;
    height: 100%;
  }
	.VerticalNav.nav {
		white-space: initial;
	}

  .verticalmain {
    overflow: hidden;
    border-top-left-radius: 30upx;
    border-top-right-radius: 30upx;
    background: #fff;
    flex: 1;
  }
	.VerticalNav.nav .cu-item {
		width: 100%;
		text-align: center;
		margin: 0;
		border: none;
		height: 98rpx;
		line-height: 98rpx;
		position: relative;
	}
	.VerticalNav.nav .cu-item.cur {
		/* background-color: #ffffff; */
    /* background-image: url('https://img.songlei.com/-1/material/a2a684a6-b660-455b-8760-b62fa60aea6c.png');
    background-repeat: no-repeat;
    background-size: 120%; */
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
		/* font-size: 34rpx; */
  }

  .VerticalNav.nav .cu-item .cur-img {
    width: 100%;
    height: 130upx;
    position: absolute;
    left: 0;
    top: -15upx;
  }
	.VerticalNav.nav .cu-item .cur-icon {
		content: "";
		width: 11rpx;
		height: 33rpx;
		position: relative;
    z-index: 1;
    margin-right: 10upx;
    border-radius: 20upx;
	}

	.verticalbox {
		/* margin-top: 100rpx; */
    display: flex;
    width: 100vw;
    background-color: #f6f6f6;
	}

	.search {
		top: unset !important;
	}
	.img-banner {
		width: 94%;
		height: 148rpx;
		margin: auto;
	}

	.cate-list {
		width: 100%;
	}

	.cate {
		/* width: 164rpx; */
		padding: 12rpx 0;
    text-align: center;
	}

	.cate-img {
		width: 137rpx;
		height: 137rpx;
		border-radius: 10rpx;
    display: block;
    margin: 0 auto 15rpx;
	}

	.text-df {
		padding-left: 11rpx;
		display: flex;
		justify-content: flex-start;
		width: 100%;
    padding:0rpx 28rpx 8rpx;
		width: 100%;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #001818;
	}

	.two_tree {
		display: flex;
		flex-wrap: wrap;
		background: white;
		width: 100%;
	}

	.viewafter:after {
		content: "";
		width: 140rpx;
	}

	.sticky {
		/* #ifndef APP-PLUS-NVUE */
		display: flex;
		position: -webkit-sticky;
		/* #endif */
		position: sticky;
    /* position: absolute; */
		top: 0rpx;
		width: 100%;
		z-index: 99;
		background: white;
	}

	.selectext {
		color: #DE3025;
    /* border-color: #DE3025; */
    /* background: rgba(255, 0, 0, .1); */
    font-weight: bold;
	}
  .bg-images-cate {
    background-image: url('https://img.songlei.com/-1/material/a2a684a6-b660-455b-8760-b62fa60aea6c.png');
    background-repeat: no-repeat;
    background-size: 100% 110%;
  }
  .flex-lunbo {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
  }
  .top-refresh {
    font-size: 24upx;
    color: #999;
    text-align: center;
    height: 50px;
    position: absolute;
    top: -50px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .top-refresh image{
    width: 30upx;
    height: 30upx;
    margin-right: 10upx;
    transform: rotate(180deg);
  }
  .bottom-refresh image{
    width: 30upx;
    height: 30upx;
    margin-right: 10upx;
  }
  .bottom-refresh {
    font-size: 24upx;
    color: #999;
    text-align: center;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
