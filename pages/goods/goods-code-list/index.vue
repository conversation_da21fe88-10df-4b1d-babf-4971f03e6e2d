<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">你要找的商品</block>
    </cu-custom>

    <!-- 商品列表 -->
    <view
      v-if="userCollect&&userCollect.length>0"
      class="cu-list menu-avatar"
      style="border-radius: 21rpx; margin-left: 10rpx; margin-right: 10rpx"
    >
      <view style="margin:10rpx">相关商品</view>
      <goods-row
        :goodsList="userCollect"
        :goodsSpace='10'
      ></goods-row>
    </view>

    <!-- 猜你喜欢 -->
    <view
      v-if="loadmore"
      :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"
    ></view>
    <recommendComponents
      v-if="!loadmore"
      canLoad
    />
  </view>
</template>

<script>
import recommendComponents from "components/recommend-components/index";
import goodsRow from "components/goods-row/index";

const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'

export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量

      page: {
        searchCount: false,
        current: 1,
        size: 15,
        ascs: '',
        //升序字段
        descs: 'create_time'
      },
      parameter: {
        type: '1'
      },
      loadmore: true,
      userCollect: [],
      ListTouchStart: "",
      ListTouchDirection: "",
      modalName: "",
      //有数统计使用
      page_title: '我的收藏'
    };
  },

  components: {
    recommendComponents,
    goodsRow
  },
  props: {},

  onLoad (options) {
    if (options) {
      let goodsList = decodeURIComponent(options.goodsList)
      console.log("options===", JSON.parse(goodsList));
      this.userCollect = JSON.parse(goodsList)
    }
    app.initPage().then(res => {
      this.userCollectPage();
    });
  },

  onShow (options) { },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.userCollectPage();
    }
  },

  methods: {

    refresh () {
      this.loadmore = true;
      this.userCollect = [];
      this.page.current = 1;
      this.userCollectPage();
    },
    userCollectPage () {
      api.userCollectPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
        let userCollect = res.data.records;
        this.userCollect = [...this.userCollect, ...userCollect];
        if (userCollect.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },

  }
};
</script>
<style  scoped>
</style>
