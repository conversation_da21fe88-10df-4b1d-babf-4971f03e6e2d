<template>
	<view>
		<cu-custom :bgColor="goodsgroupBackgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{page_title}}</block>
		</cu-custom>

		<totalGoodsList :parameter="parameter" ref="goodslist" />
	</view>
</template>

<script>
	import totalGoodsList from "./components/good-list/index.vue";
	import util from '@/utils/util'

	const app = getApp();
	export default {
		components: {
			totalGoodsList,
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				parameter: {
					goodsGroupIds: ''
				},
				//有数统计使用
				page_title: '分组商品列表'
			}
		},
    computed: {
      goodsgroupBackgroundColor: function() {
        if (this.theme && this.theme.customFiled) {
          const customFiled = JSON.parse(this.theme.customFiled);
          return customFiled.goodsgroupBackgroundColor?customFiled.goodsgroupBackgroundColor:''
        }
        return ''
      }
    },
		onLoad(options) {
			if(options.title) {
			  this.page_title = decodeURIComponent(options.title);
			}
			let groupIds;
			if (options.scene) {
				//接受二维码中参数
				let scenes = decodeURIComponent(options.scene).split('&');
				groupIds = scenes[0];
			} else {
				groupIds = options.groupIds;
			}
			this.parameter.goodsGroupIds = groupIds
		}
	}
</script>

<style>
</style>
