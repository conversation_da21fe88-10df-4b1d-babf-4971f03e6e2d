<template>
	<view class="padding-bottom-xs" style="backgroundColor': '#f0eeef">

		<!-- <view class="cu-bar justify-center  shop-card fixed-tab" :style="{
				'backgroundColor': '#ffffff',
				'minHeight':'auto',
				'padding':'3px 0',
				'top': CustomBar+'px',
			}">

			<view class="grid response text-center align-start" >
				<view class="flex-sub padding-xs radius">
					<view class="tab-item"
						:class="multiple=='desc' ? 'text-bold text-'+theme.themeColor : 'tab-item-color'"
						@tap="sortHandle" data-type="multiple">综合</view>
				</view>

				<view class="flex-sub padding-xs  radius  ">
					<view class="grid text-center justify-center tab-item" @tap="sortHandle" data-type="sales"
						:class="sales=='asc' ||sales=='desc' ? 'text-bold text-'+theme.themeColor : 'tab-item-color'">销量
						<view style="font-size: 36rpx; line-height: 18rpx; ">
							<view
								:class="'cuIcon-triangleupfill ' + (sales=='asc' ? 'text-'+theme.themeColor : 'tab-item-color')"
								data-type="sales"></view>
							<view class="basis-df"></view>
							<view
								:class="'cuIcon-triangledownfill ' + (sales=='desc' ? 'text-'+theme.themeColor : 'tab-item-color')"
								data-type="sales"></view>
						</view>
					</view>
				</view>

				<view class="flex-sub padding-xs radius ">
					<view class="tab-item"
						:class="createTime=='desc' ? 'text-bold text-'+theme.themeColor : 'tab-item-color'"
						@tap="sortHandle" data-type="createTime">新品</view>
				</view>

				<view class="flex-sub padding-xs  radius ">
					<view class="grid text-center justify-center tab-item" @tap="sortHandle" data-type="price"
						:class="price=='asc' ||price=='desc' ? 'text-bold text-'+theme.themeColor : 'tab-item-color'">价格
						<view style="font-size: 36rpx; line-height: 18rpx; ">
							<view
								:class="'cuIcon-triangleupfill ' + (price=='asc' ? 'text-'+theme.themeColor : 'tab-item-color')"
								data-type="price"></view>
							<view class="basis-df"></view>
							<view
								:class="'cuIcon-triangledownfill ' + (price=='desc' ? 'text-'+theme.themeColor : 'tab-item-color')"
								data-type="price"></view>
						</view>
					</view>
				</view>
			</view>

			<view class="action">
				<view class="text-xxl tab-item">
					<text class="tab-item" style="font-size:30rpx;"
						:class="'cuIcon-' + (viewType ? 'list' : 'cascades')" @tap="viewTypeEdit"></text>
				</view>
			</view>
		</view> 

		<view class="shop-content" style="margin-top: 58rpx;">
			<view  v-if="viewType">
				<goods-card :goodsList="goodsList"></goods-card>
			</view>
			<view v-if="!viewType">
				<goods-row :goodsList="goodsList"></goods-row>
			</view>
			<view style="width: 750rpx;" :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
		</view> -->

		<view class="verticalbox flex">
			<view>
				<image class="imgUrl" :src="goodsgroupPriceBigBg"></image>
			</view>

			<scroll-view class="VerticalNav nav" scroll-y scroll-with-animation :scroll-top="VerticalNavTop" :style="{
					height:`calc(100vh - ${CustomBar+220}rpx)`,
					backgroundColor:`${goodsgroupBackgroundColor}`,
					paddingTop: `${goodsgroupPriceBigBg?'45rpx':''}`,
					marginTop: `${goodsgroupPriceBigBg?'130rpx':''}`,
					borderTopRightRadius: '15rpx',
					}">
				<view class="set-text"
					:class="'cu-items ' + (index==TabCur?'cur text-'+theme.themeColor:goodsgroupBackgroundColor)"
					v-for="(item, index) in goodsCategory" :key="index" @tap="tabSelect($event,item.id)"
					:data-id="index">
					<view class="goodsListNameBox" :style="{color:`${goodsgroupColor}`}">
						<view v-if="item.pic" class="goodsListName" style="height: 80rpx">
							<image style="height: 80rpx" :src="item.pic"></image>
						</view>
					
						<view ref="mycolor"  v-else class="goodsListNameText" :style="{
							color:index==TabCur?`${goodsgroupBackgroundColor}`:`${goodsgroupColor}`,
							wordBreak: 'break-all',
							overflow: 'hidden',
							height: item.name&&item.name.length>2?'70rpx':'38rpx'}">{{item.name}}</view>
					</view>
				</view>
			</scroll-view>

			<scroll-view class="verticalmain" scroll-y scroll-with-animation @scrolltolower="reachBottom" :style="{
				height:`calc(100vh - ${CustomBar + 5}px)`
			}" :scroll-into-view="'main-' + MainCur" lower-threshold="100">
				<goods-waterfall :fullWidth="684" :goodsList="goodsList" :loadMore="loadMore"></goods-waterfall>
				<view style="width: 750rpx;" :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
			</scroll-view>
		</view>

	</view>
</template>

<script>
	import util from '@/utils/util'
	import goodsRow from "components/goods-row/index";
	import goodsWaterfall from "components/goods-waterfall/index.vue";
	import api from '@/utils/api'
	const app = getApp();
	export default {
		components: {
			goodsRow,
			goodsWaterfall
		},
		props: {
			//分组Id
			parameter: {
				type: Object,
				default: {}
			},
			heightHead: {
				type: Number,
				default: 100
			},
			pageScrollTop: {
				type: Number,
				default: 0
			}
		},
		watch: {
			parameter: {
				handler(val) {
					if (val && val.goodsGroupIds) {
						// this.relod();
						this.goodsGroupName()
					}
				},
				deep: true,
				immediate: true
			}

			// 	goodsgroupId: {
			//   handler (val, oldVal) {
			//     //普通的watch监听
			//     console.log("监听 goodsgroupId" + val, oldVal);
			//     if (val!=oldVal) {
			// 			this.relod();

			//     }
			//   },
			//   immediate: true
			// },
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				loadmore: true,
				goodsList: [],
				viewType: false,
				page: {
					searchCount: false,
					current: 1,
					size: 20,
					ascs: '',
					//升序字段
					descs: ''
				},
				multiple: 'desc',
				price: '',
				sales: '',
				createTime: '',
				goodsCategory: [],
				TabCur: 0,
				MainCur: 0,
				VerticalNavTop: 0,
				load: true,
				goodsgroupId: '',
				prev:0
			}
		},

		created() {
			console.log("====this.parameter=======", this.parameter)
			if (this.parameter && this.parameter.goodsGroupIds > 0) {
				// this.relod();
				this.goodsGroupName()
			}
		},

		computed: {
			goodsgroupPriceBigBg: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.goodsgroupPriceBigBg ? customFiled.goodsgroupPriceBigBg : ''
				}
				return ''
			},
			goodsgroupColor: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.goodsgroupColor ? customFiled.goodsgroupColor : ''
				}
				return ''
			},

      goodsgroupBackgroundColor: function() {
        if (this.theme && this.theme.customFiled) {
          const customFiled = JSON.parse(this.theme.customFiled);
          return customFiled.goodsgroupBackgroundColor?customFiled.goodsgroupBackgroundColor:''
        }
        return ''
      }
    
		},

		mounted() {
			// this.goodsGroupName()
		},

		methods: {
			VerticalMain(e) {
				let that = this;
				let list = this.goodsCategory;
				let tabHeight = 0;

				if (this.load) {
					for (let i = 0; i < list.length; i++) {
						let view = uni.createSelectorQuery().select("#main-" + i);
						view.fields({
							size: true
						}, data => {
							list[i].top = tabHeight;
							if (data && data.height > 0) {
								tabHeight = tabHeight + data.height;
							}
							list[i].bottom = tabHeight;
						}).exec();
					}

					that.load = false;
					that.goodsCategory = list;
				}

				let scrollTop = e.detail.scrollTop + 20;

				for (let i = 0; i < list.length; i++) {
					if (scrollTop > list[i].top && scrollTop < list[i].bottom) {

						that.VerticalNavTop = (i - 1) * 50;
						that.TabCur = i;
						return false;
					}
				}
			},
			//侧边栏点击控制
			tabSelect(e, id) {
				console.log("======e=======>", e, "id", id);
				if (this.goodsgroupId != id) {
					this.goodsgroupId = id
					this.relod()
				}
				this.TabCur = e.currentTarget.dataset.id;
				this.MainCur = e.currentTarget.dataset.id;
				this.VerticalNavTop = (e.currentTarget.dataset.id - 1) * 50;
				console.log("VerticalNavTop====>", this.VerticalNavTop, "CustomBar", this.CustomBar);
			},
			//获取分组名称
			goodsGroupName() {
				console.log("this.parameter.goodsGroupIds", this.parameter.goodsGroupIds);
				api.goodsGroupListByIds(Object.assign({}, {
					ids: this.parameter.goodsGroupIds
				})).then(res => {
					let goodsListName = res.data;
					console.log("goodsListName", goodsListName);
					this.goodsCategory = goodsListName;
					//默认第一条
					this.goodsgroupId = goodsListName[0].id
					//获取商品列表
					this.relod(true)
				});
			},

			goodsPage(fresh) {
				api.goodsGroupsGet(Object.assign({}, this.page, {
					goodsGroupIds: this.goodsgroupId
				})).then(res => {
					let goodsList = res.data.records;
					if (fresh) {
						this.goodsList = goodsList;
					} else {
						this.goodsList = [...this.goodsList, ...goodsList];
					}
					console.log("this.goodsList", this.goodsList);
					if (goodsList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},
			
			reachBottom() {
				if (this.loadmore) {
					
					//throttle 节流函数
					util.throttle(this.getMore(), 1500)
					// let now = new Date().getTime();
					// console.log("到底时间===>",this.prev,"now - this.prev",now - this.prev,now - this.prev >= 3000);
					//  if (now - this.prev >= 3000) {
					// 	 this.getMore();
					// 	  this.prev = now;
					//  }
				}
			},

			 getMore () {
				this.page.current = this.page.current + 1;
				this.goodsPage();
			},

			viewTypeEdit() {
				this.viewType = !this.viewType;
			},

			relod() {
				this.loadmore = true;
				this.goodsList = [];
				this.page.current = 1;
				this.goodsPage(true);
			},

			needLoad() {
				if (this.goodsList.length <= 0) {
					this.goodsPage();
				}
			},

			sortHandle(e) {
				let type = e.target.dataset.type;
				switch (type) {
					case 'price':
						if (this.price == '') {
							this.price = 'asc';
							this.page.descs = '';
							this.page.ascs = 'price_down';
						} else if (this.price == 'asc') {

							this.price = 'desc';
							this.page.descs = 'price_down';
							this.page.ascs = '';
						} else if (this.price == 'desc') {
							this.price = '';
							this.page.descs = '';
							this.page.ascs = '';
						}
						this.sales = '';
						this.createTime = '';
						this.multiple = '';
						break;

					case 'sales':
						if (this.sales == '') {
							this.sales = 'desc';
							this.page.descs = 'sale_num';
							this.page.ascs = '';
						} else if (this.sales == 'desc') {
							this.sales = 'asc';
							this.page.descs = '';
							this.page.ascs = 'sale_num';

						} else if (this.sales == 'asc') {
							this.sales = '';
							this.page.descs = '';
							this.page.ascs = '';
						}
						this.price = '';
						this.createTime = '';
						this.multiple = '';
						break;

					case 'createTime':
						if (this.createTime == '') {
							this.createTime = 'desc';
							this.page.descs = 'create_time';
							this.page.ascs = '';
						} else if (this.createTime == 'desc') {
							this.createTime = '';
							this.page.descs = '';
							this.page.ascs = '';
						}
						this.price = '';
						this.sales = '';
						this.multiple = '';
						break;
					default:
						this.page.descs = '';
						this.page.ascs = '';
						this.createTime = '';
						this.price = '';
						this.sales = '';
						this.multiple = 'desc';
						break;
				}

				this.relod();
			},

		},


	}
</script>
/***
<style scoped>
	.fixed-tab {
		position: fixed;
		left: 0;
		width: 100%;
		z-index: 9999;
	}

	.tab-item {

		font-size: 24rpx;
	}

	.tab-item-color {
		color: #525252;
	}

	.triangle {
		color: #a5a5a5;
	}

	.location {
		width: 480rpx;
	}

	.shop-detail {
		height: 160rpx;
	}

	.shop-detail image {
		width: 120rpx !important;
		height: 120rpx !important;
	}

	.show-bg {
		height: 84%;
		margin-top: 120rpx;
	}

	.image-box {
		height: 90%;
	}

	.show-btn {
		margin-top: -130rpx;
	}

	.econds-kill {
		width: 40%;
		height: 100rpx;
	}

	.econds-kill image {
		width: 100%;
		height: 100rpx;
	}

	.spellGroup {
		width: 700rpx;
		height: 100rpx;
	}

	.spellGroup image {
		width: 100%;
		height: 100rpx;
	}

	.bargain {
		width: 700rpx;
		height: 100rpx;
	}

	.bargain image {
		width: 100%;
		height: 100rpx;
	}

	.coupons {
		width: 40%;
		height: 100rpx;
	}

	.coupons image {
		width: 100%;
		height: 100rpx;
	}
</style>
*/

<style scoped>
	.imgUrl {
		width: 76rpx;
		height: 180rpx;
		position: absolute;
		left: 0px;
	}

	.border-top-radius {
		border-radius: 15rpx 15rpx 0rpx 0rpx;
	}

	.border-bottom-radius {
		border-radius: 0rpx 0rpx 15rpx 15rpx;
	}
</style>
<style>
.goodsListNameText {
		width: 59rpx;
		font-size: 25rpx;
		/* height: 38rpx; */
		max-height: 70rpx;
		margin: auto;
		position: absolute;
		text-align: left;
		left: 0;
		top: 0;
		right: 0;
		display: block;
		bottom: 0;
	}

	.goodsListName {
		width: 59rpx;
		height: 72rpx;
		margin: auto;
		position: absolute;
		text-align: center;
		left: 0;
		top: 0;
		right: 0;
		display: block;
		bottom: 0;
	}

	.goodsListNameBox {
		width: 72rpx;
		height: 100rpx;
		text-align: center;
		position: relative;
		display: block;
	}

	.set-text {
		overflow: hidden;
		-webkit-line-clamp: 2;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;

	}

	.VerticalNav.nav {
		width: 80rpx;
		white-space: initial;
	}

	.VerticalNav.nav .cu-items {
		width: 100%;
		text-align: center;
		// background-color: #fff;
		margin: 0;
		border: none;
		// height: 50px;
		// border-radius: 12rpx;
		position: relative;
	}

	.VerticalNav.nav .cu-items.cur {
		background-color: #f1f1f1;
	}

	.VerticalNav.nav .cu-items.cur::after {
		content: '';
		width: 8rpx;
		height: 30rpx;
		border-radius: 10rpx 0 0 10rpx;
		position: absolute;
		background-color: currentColor;
		top: 0;
		right: 0rpx;
		bottom: 0;
		margin: auto;
	}

	.verticalbox {
		/* margin-top: 20rpx; */
		width: 750rpx;
		overflow: hidden;
	}

	.search {
		top: unset !important;
	}

	.img-banner {
		width: 94%;
		height: 148rpx;
		margin: auto;
	}

	.cate-list {
		width: 100%;
	}

	.cate {
		width: 150rpx;
		margin: 15rpx;
	}

	.cate-img {
		width: 140rpx;
		height: 140rpx;
	}
</style>
