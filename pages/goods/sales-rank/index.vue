import viewGoods from '@/components/div-components/div-goods/div-goods.vue';
<template>
  <view>
    <!-- 页面标题 -->
    <view>
      <cu-custom 
        :bgColor="'bg-' + theme.backgroundColor" 
        :isBack="true" 
        :hideMarchContent="true" 
        bgImage="https://img.songlei.com/rank/rank-banner-title.png"
      >
        <block slot="backText">返回</block>
        <block slot="content">商品销量排行榜</block>
      </cu-custom>
    </view>
    <!-- banner -->
    <view style="position: relative; height: 327rpx;">
      <image 
        style="width: 100%; height: 327rpx;"
        class="margin-right-sm"
        src="https://img.songlei.com/rank/rank-banner.png" 
      />
      <view class="title">
        {{ name }}品类排行榜
      </view>
    </view>
    <!-- 三级分类下的 tab -->
    <view style="position: relative; top: -14rpx; width: 100%; z-index: 1;" >
      <!-- <view 
        class="tab text-cut" 
        style="position: absolute; top: 14rpx; left:0; width: 30%;"
        >{{ name }}品类</view> -->
      <scroll-view
        scroll-x 
        class="nav" 
        style="display: inline-block; position:relative;"
        :scroll-into-view="`tab-${currentCategory.id}`"
      > 
        <view
          class="tab" 
          v-for="item in goodscategoryData" 
          :key="item.id" 
          :id="`tab-${item.id}`"
          :class="item.id == currentCategory.id ? 'activity' : ''"
          :style="item.id != currentCategory.id ? 'margin-top: 14rpx;' : 'margin-top: 0' "
          @tap="handleTab(item)"
        >{{ item.name }}</view>
        <view class="tab-bg" id=""></view>
      </scroll-view>
    </view>
    <!-- 商品数据 -->
    <view v-if="goodsData.length" style="margin-top: -28rpx;">
      <goods-row goodsListType="goodsrank" :goodsList="goodsData" />
    </view>
    <view v-else style="width: 100%; text-align: center;">暂无数据</view>
  </view>
</template>
<script>
const app = getApp();

import { goodscategory, rankGoods } from "@/pages/goods/api/goods-rank.js"
import goodsRow from "components/goods-row/index";
import util from 'utils/util'
export default {
  components: {goodsRow},
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      id: '',
      name: '',
      goodscategoryData: [],
      currentCategory: {
        id: '',
        name: ''
      },
      goodsData: []
    }
  },
  onLoad(options) {
    app.initPage().then(async () => {
      if (util.isUserLogin()) {
        this.id = options.id.split(',')[0];
        this.name = options.name;
    
        if (this.id) await this.getGoodscategory();

        this.currentCategory = {
          id: options.id.split(',')[1],
          name: options.name
        }

        await this.getRankGoods()

      } else {
        const pages = getCurrentPages()
        const url = pages[pages.length - 1]['$page']['fullPath']
        uni.reLaunch({
          url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
        })
      }
    });
    
  },
  onShow() {
    
  },
  methods: {
    // 获取分类数据
    async getGoodscategory() {
      try {
        const res = await goodscategory({parentId: this.id})
        this.goodscategoryData = res.data || []
        this.currentCategory = this.goodscategoryData.length ? this.goodscategoryData[0] : {}
      } catch (error) {

      }
    },
    // 点击分类数据
    handleTab(obj) {
      this.currentCategory = obj
      this.name = obj.name;
      this.getRankGoods()
    },
    async getRankGoods() {
      try {
        const res = await rankGoods({cateId: this.currentCategory.id})
        this.goodsData = res.data || []
      } catch (error) {

      }
    }
  }
}
</script>
<style lang="scss" scoped>
.title{
  width: 459rpx;
  height: 58rpx;
  background: rgba(49,28,16,0.6);
  border-radius: 29rpx;
  color: #fff;
  line-height: 58rpx;
  text-align: center;
  position: absolute;
  bottom: 32rpx;
  left: 50%;
  transform: translateX(-50%);
}
.tab{
  height: 72rpx;
  display: inline-block;
  line-height: 72rpx;
  background: #AC6E3C;
  padding: 0 48rpx;
  text-align: center;
  color: #fff;
  position: relative;
  z-index: 1;
}
.activity{
  height: 86rpx;
  background: #FA5A20; 
  border-radius: 18rpx 18rpx 0rpx 0rpx;
  line-height: 86rpx;
  vertical-align: bottom;
  // margin-top: -14rpx;
}
.tab-bg{
  width: 100%;
  height: 72rpx;
  position: absolute;
  top: 14rpx;
  left: 0;
  z-index: 0;
  background: #AC6E3C;
}
</style>