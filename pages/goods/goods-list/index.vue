<template>
	<view>
		<cu-custom :bgColor="'#F9F9F9'" simple :iconColor="'#000'" :boderColor="'#ccc'">
			<view slot="marchContent">
				<view class="search-form round" :style="{
						height: 'auto',
						lineHeight: 1,
						margin: '0 auto',
						border: '1rpx solid #CDA488',
						zIndex: 9999
					}">
					<view class="flex align-center justify-between" :style="{ height: `${menuHeight - 2}px` }"
						@click="navTo(`/pages/base/search/index?shopId=${parameter.shopId}&searchKeyword=${encodeURIComponent(parameter.name) || ''}`)">
						<text class="cuIcon-search text-xl" style="color: #a1a1a1"></text>
						<input :placeholder-style="{
								'font-size': isPhone ? '28rpx' : '18rpx'
							}" v-model="parameter.name" type="text" :placeholder="parameter.name" :disabled="true" class="text-df"
							confirm-type="search"
							:style="{ width: `${750 - menuWidth * pixelRatio - (leftMenuWidth / 2) * pixelRatio - (parameter.name ? 180 : 108)}rpx`, paddingRight: `0rpx` }" />
						<text v-if="parameter.name" class="cuIcon-roundclosefill"
							style="font-size: 30rpx; color: #a1a1a1"></text>
					</view>
				</view>
			</view>
		</cu-custom>

		<scroll-view scroll-y :style="{ height: `calc(100vh - ${CustomBar}px)` }" @scroll="handleScroll"
			@scrolltolower="getMore">
			<!-- 搜索导航 -->
			<view class="padding-lr-sm padding-top-sm" v-if="searchNavigationList.length">
				<view class=" bg-white padding-sm margin-bottom-sm" style="border-radius: 45rpx;"
					v-for="(item, index) in searchNavigationList" :key="index">
					<view class="flex align-center justify-between" @click="handleToPage(item.url)">
						<view class="flex align-center">
							<image style="width: 68rpx; height: 68rpx; 
								border-radius: 50%;" :src="item.imageUrl ? item.imageUrl : 'https://img.songlei.com/live/img/no_pic.png'"
								mode="aspectFill" />
							<view class="margin-left-sm text-df text-black">{{ item.name }}</view>
						</view>
						<view class="flex align-center">
							<text class="text-df text-black">进入</text>
							<text class="cuIcon-right" style="color:#B5B5B5"></text>
						</view>
					</view>
				</view>
				
			</view>

			<!-- 优惠券头部布局 -->
			<view class="coupon-top" v-if="parameter.couponId > 0">
				<view style="background-color: #fff; border-radius: 20rpx; padding: 20rpx">
					<view style="display: flex; align-items: center">
						<view
							style="color: d9143f; border: solid 2rpx #d9143f; padding: 10rpx; width: 136rpx; color: #d9143f">
							满减优惠</view>
						<view v-if="couponInfo.validEndTime"
							style="margin-left: 20rpx; color: #a1a1a1; font-size: 24rpx">
							有效期至:
							{{ couponInfo.validEndTime }}
						</view>
					</view>
					<view class="flex flex-direction align-center justify-center"
						style="margin-top: 20rpx; background-color: #fef2f4; border-radius: 10rpx; padding: 20rpx">
						<view class="text-xdf overflow-1" style="color: #f30738; font-weight: bold">
							{{ couponInfo.name }}
						</view>
						<view class="text-df padding-top-sm" style="color: #888888">
							{{ couponInfo.premiseAmount > 0 ? `满 ${couponInfo.premiseAmount}可用` : '无门槛' }}
						</view>
					</view>
				</view>
			</view>
			<!-- 没有过渡页面筛选 -->
			<view class="category-box" v-if="options.length">
				<scroll-view class="prefer-scroll" scroll-x="true" :scroll-into-view="itemIndex">
					<view class="scroll-view-item" v-for="(item, index) in options" :key="item.id">
						<view :id="`index${index}`" @click="getshopList(index, item)">
							<view style="text-align: center">
								<image class="category-img" mode="aspectFill"
									:src="item.picUrl ? item.picUrl : 'https://img.songlei.com/live/img/no_pic.png'">
								</image>
							</view>

							<view class="category-title" :style="{ color: index == nameIndex ? 'red' : '' }">
								{{ item.name }}
							</view>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 页面头部布局 -->
			<view class="shop-top">
				<!-- 如果有店铺信息 -->
				<view v-if="shopInfo && shopInfo.shopId > 0">
					<!-- 页面顶部 -->
					<view style="position: relative; width: 100%">
						<view class="excessive margin-bottom-sm padding-bottom-sm padding-lr-sm">
							<navigator class="flex padding-top-xs justify-between" hover-class="none"
								:url="'/pages/shop/shop-detail/index?id=' + shopInfo.shopId">
								<!-- 第一种样式标签和店铺并齐  切换第二种样式width需要调整-->
								<view class="flex align-center">
									<image mode="aspectFit" class="excessive-banner-icon" :src="shopInfo.imgUrl">
									</image>
									<view class="excessive-banner-text" style="margin-left: 24rpx">
										<view class="excessive-title text-xsm">
											{{ shopInfo.name }}
										</view>

										<view class="flex align-center">
											<view class="excessive-follow text-grey text-xsm text-thin"
												style="margin-right: 5rpx">{{ shopInfo.collectCount }}人关注</view>
											<view v-if="shopInfo.selfSupport == 1"
												class="excessive-sm text-sm cu-tag round"
												style="background: linear-gradient(90deg, #fe3915 0%, #ff651a 100%); margin-left: 6rpx">
												松鼠自营
											</view>

											<view class="cu-tag round excessive-sm text-sm"
												style="background: #ff9d2e; padding-right: 10rpx">上新</view>
										</view>
									</view>
								</view>

								<view
									class="excessive-cu-btn round check-details excessive-btn text-df padding-lr padding-tb-sm"
									style="border: 1px solid #f42e2b; border-radius: 27px; color: #f7302c">
									进店
								</view>
							</navigator>
						</view>
						<!--start轮播图  -->
						<view class="padding-left-sm padding-right-sm" v-if="shopAdvertsList && shopAdvertsList.length">
							<div-swiper v-model="shopAdvertsList"></div-swiper>
						</view>
						<!-- end轮播图  -->
					</view>
				</view>
			</view>

			<view class="flex justify-between bg-white solid-bottom padding-tb-xs padding-right-sm"
				:class="{ 'topfixed-active': scrollY > topHeight }"
				:style="{ top: `${scrollY > topHeight ? CustomBar : 0}px` }">
				<view class="grid response padding-left-sm padding-right-sm item-tab" style="flex: 1">
					<view class="flex-sub flex margin-xs radius overflow text-df align-center item-tab"
						@tap="sortHandle" style="flex: 1" :style="{
							maxWidth: isPhone ? 'auto' : '90rpx'
						}" data-type="title" :class="inventoryState == '' ? 'text-bold text-' + theme.themeColor : ''">
						{{ title }}
					</view>

					<view class="flex align-center margin-xs" style="flex: 1.3" :style="{
							maxWidth: isPhone ? 'auto' : '90rpx'
						}">
						<view class="grid align-center text-df item-tab" @tap="sortHandle" data-type="sales">
							销量
							<view class="margin-left-xs">
								<view class="text-df"
									:class="'cuIcon-triangleupfill ' + (sales == 'asc' ? 'text-' + theme.themeColor : '')"
									data-type="sales"></view>
								<view class="basis-df"></view>
								<view class="text-df"
									:class="'cuIcon-triangledownfill ' + (sales == 'desc' ? 'text-' + theme.themeColor : '')"
									data-type="sales"></view>
							</view>
						</view>
					</view>

					<view class="flex-sub margin-xs" style="flex: 1.3" :style="{
							maxWidth: isPhone ? 'auto' : '90rpx'
						}">
						<view class="grid align-center text-df item-tab" @tap="sortHandle" data-type="price">
							价格
							<view class="margin-left-xs text-df">
								<view class="text-df"
									:class="'cuIcon-triangleupfill ' + (price == 'asc' ? 'text-' + theme.themeColor : '')"
									data-type="price"></view>
								<view class="basis-df"></view>
								<view class="text-df"
									:class="'cuIcon-triangledownfill ' + (price == 'desc' ? 'text-' + theme.themeColor : '')"
									data-type="price"></view>
							</view>
						</view>
					</view>

					<view class="flex flex-sub align-center margin-top-xs margin-bottom-xs margin-left-xs"
						style="flex: 1" :style="{
							maxWidth: isPhone ? 'auto' : '90rpx'
						}">
						<!-- <view :class="createTime=='desc' ? 'text-bold text-'+theme.themeColor : ''" @tap="sortHandle" data-type="createTime">新上架</view> -->
						<navigator class="flex text-df item-tab" hover-class="none" open-type="redirect"
							:url="`/pages/shop/shop-list/index?name=${parameter.name ? encodeURIComponent(parameter.name) : encodeURIComponent(title)}`">
							店铺
						</navigator>
					</view>

					<view class="flex flex-sub margin-top-xs margin-bottom-xs align-center">
						<view class="flex text-df item-tab"
							:class="inventoryState == '0' ? 'text-bold text-' + theme.themeColor : ''" @tap="sortHandle"
							data-type="goods" style="flex: 1.3" :style="{
								maxWidth: isPhone ? 'auto' : '90rpx',
								minWidth: windowWidth < 300 ? '138rpx' : '128rpx'
							}">
							仅看有货
						</view>
					</view>

					<!-- <view class="flex-sub margin-xs radius">
					<view :class="createTime=='desc' ? 'text-bold text-'+theme.themeColor : ''" @tap="sortHandle" data-type="createTime">筛选	<text class="cuIcon-filter"></text> </view>
				</view> -->
				</view>
				<view class="margin-xs padding-left-xs grid text-center align-center">
					<view class="action item-tab">
						<text :class="'text-df cuIcon-' + (viewType == 'list' ? 'list' : 'cascades')"
							@tap="viewTypeEdit"></text>
					</view>
				</view>
			</view>

			<template v-if="getGoodsCategoryShopList">
				<goods-category-shopList :initial="initialCategory" :goodsCategoryShopList="getGoodsCategoryShopList"
					@getCategoryList="getCategoryList"></goods-category-shopList>
			</template>

			<!-- 暂无推荐内容提示 -->
			<view v-if="tips" class="padding text-sm" style="color: #b5b5b5; background: #f8f8f8; padding-bottom: 0">
				{{ tips }}
			</view>

			<template v-if="viewType == 'list' && goodsList.length > 0">
				<goods-card ref="goodscard" :goodsList="goodsList" :otherShopInfos="otherShopInfos"></goods-card>
			</template>

			<template v-if="viewType == 'cascades' && goodsList.length > 0">
				<goods-row @onCaetAnimation="caetShake" :showCart="true" ref="goodscard" :goodsList="goodsList"
					:otherShopInfos="otherShopInfos" :searchType="searchType" :searchKey="parameter.name"></goods-row>
			</template>

			<!-- 反馈文案 -->
			<view v-if="resultTip" class="text-sm text-center ptb-xxxmd" style="color: #b5b5b5;">
				<view v-if="!goodsList.length">
					<image mode="aspectFit" style="width: 160rpx;height: 160rpx;"
						:src="$imgUrl('live/power-bank/scan/search.png')"></image>
				</view>
				<view>
					-<text>{{ resultTip }}</text>
					<text class="margin-left-xs" style="color: #C29666;" @click="isModalShow()"
						v-show="needFeedback === 1">
						反馈意见
					</text>-
				</view>
			</view>

			<!-- 推荐商品 -->
			<template v-if="recommendItems">
				<goods-card ref="goodscard" :goodsList="recommendItems" :otherShopInfos="otherShopInfos"
					:searchType="searchType" :searchKey="parameter.name"></goods-card>
			</template>

			<!--没有更多了-->
			<view v-if="!searchResult.resultTip&&page.start !== 0"
				:class="'cu-load text-sm bg-white ' + (loadmore ? 'loading' : 'over')"></view>
		</scroll-view>

		<!--悬浮购物车-->
		<suspension-cart :shoppingCartCount="shoppingCartCount" :animation="animation"></suspension-cart>

		<!-- 反馈意见弹框 -->
		<view v-show="modalFeedback" :class="'cu-modal bottom-modal show'" style="z-index: 19999;">
			<view class="cu-dialog bg-white-black" @tap.stop>
				<view class="flex padding-top-xs margin margin-bottom-xl margin-top-xl">
					<view class="text-xsm text-left text-bold" style="width: 100%">搜索反馈与建议</view>
					<view class="cuIcon-close line-light-grey width-xxxl height-xxxl lh-xxxl" @click="hideModal()"
						style="border-radius: 35rpx;border: 2rpx solid;">
					</view>
				</view>
				<scroll-view scroll-y="true" style="height: 490rpx;">
					<checkbox-group @change="checkboxChange">
						<view v-for="item in checkfeedback" :key="item" class="m-xxxmd text-left">
							<label class="font-xmd">
								<checkbox :value="item.value" :checked="false" color="#FFCC33"
									style="transform:scale(0.6)" />{{item.value}}
							</label>
						</view>
					</checkbox-group>
					<view v-show="selectedFeedback.includes('其他')" class="m-xxxmd">
						<textarea class="text-left font-xmd p-xmd" style="background: #F3F1F4;width: 100%;"
							maxlength="100" @input="textareaInput" placeholder="请描述问题，帮助改进搜索体验(选填)"></textarea>
					</view>
				</scroll-view>

				<view class="flex justify-center margin margin-top-lg">
					<button class="text-xsm line-light-grey text-center ptb-xxxmd round"
						:class="selectedFeedback.length> 0? 'selectedFeedback' : 'feedback'" :disabled="isDisabled"
						@click="submit" style="width: 100%;">
						提交
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const util = require('utils/util.js');
	const app = getApp();
	import api from 'utils/api';
	import goodsRow from 'components/goods-row/index';
	import divSwiper from '../component/swiper.vue';
	import goodsCategoryShopList from '../component/goods-category-shopList.vue';
	import suspensionCart from '@/components/suspension-cart/suspension-cart.vue';

	import {
		getSearchfeedback,
		addSearchfeedback
	} from '../api/goods.js';
	import {
		mapState
	} from 'vuex';
	import {
		gotoPage
	} from 'components/div-components/div-base/div-page-urls.js';
	import {
		senTrack
	} from "@/public/js_sdk/sensors/utils.js";
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				//测试
				background: {
					backgroundColor: '#000'
				},
				height: 0,
				navbarRight: 0,
				opacity: 0,
				scrollY: 0,
				topHeight: 0,
				//店铺信息
				shopInfo: {},
				//正常的搜索商品列表参数
				page: {
					start: 0,
					hit: 10
				},
				//根据优惠券获取商品列表
				goodsGroupsPage: {
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: ''
				},
				// 排序
				sortList: [],
				parameter: {
					name: '',
					shopId: '',
					couponId: '',
					brandCategoryShopFirst: '',
					brandCategoryShopSecond: ''
				},
				loadmore: true,
				goodsList: [],
				viewType: 'cascades',
				price: '',
				sales: '',
				goodsTitle: '',
				title: '',
				goods: '',
				inventoryState: '', //仅看有货||上架商品  0仅看有货  字段不传查看所有
				topShopInfo: {},
				//有数统计使用
				page_title: '搜索过度页',
				couponInfo: {},
				animation: '', // 动画样式
				shoppingCartCount: 0, //购物车数量
				otherShopInfos: '', // 品牌数据
				shopAdvertsList: [], //店铺广告列表
				getGoodsCategoryShopList: [], //店铺商品分类
				// shopCategoryGoodsId:''//分类的店铺商品id
				categoryType: '',
				shopBrandId: '',
				categorySecondnum: '',
				initialCategory: false, //初始化分类
				options: '', //左右滑动 列表
				nameIndex: null,
				itemIndex: null,
				tips: '',
				searchNavigationList: [],
				searchResult: {}, //搜索结果
				recommendItems: [], //推荐商品
				checkfeedback: [], //反馈意见复选框内容
				selectedFeedback: [], // 选中复选框的反馈内容
				modalFeedback: '', //反馈意见弹窗
				isDisabled: true, //反馈意见按钮控制
				resultTip: '', //反馈意见文案
				needFeedback: 1, //1是反馈意见 0是不要反馈意见
				onePage: true, //第一页数据
				textareaFeedback: '', //反馈其它问题
				searchType: '' //上个页面传的参数 搜索类型
			};
		},

		components: {
			goodsRow,
			divSwiper,
			suspensionCart,
			goodsCategoryShopList
		},
		props: {},

		computed: {
			...mapState(['windowWidth', 'HeightBar', 'CustomBar', 'menuWidth', 'menuHeight', 'leftMenuWidth', 'pixelRatio',
				'isPhone'
			])
		},

		watch: {
			selectedFeedback(newVal) {
				this.isDisabled = !(newVal && newVal.length);
			}
		},

		onLoad(options) {
			// #ifdef MP-WEIXIN
			let {
				height
			} = uni.getMenuButtonBoundingClientRect();
			this.searchHeight = height;
			// #endif
			this.arryParameter();
			let title = options.title ? decodeURI(decodeURIComponent(options.title)) : '热销';
			// console.log("let title",title)
			this.title = title;
			// let pages = getCurrentPages();
			// let prevPage = pages[pages.length - 1];
			// console.log("let prevPage = pages[pages.length - 2]",prevPage)
			if (options.searchKeyword && options.shopId) {
				// console.log("&&!options.searchKeyword",)
				this.parameter.name = decodeURI(options.searchKeyword);
				// let parameter = decodeURIComponent(options.name)
				// this.parameter.name = decodeURI(parameter);
			}
			//商城商品一级分类
			if (options.categoryFirst) {
				this.parameter.categoryFirst = options.categoryFirst;
			}
			//商城商品二级分类
			if (options.categorySecond) {
				this.parameter.categorySecond = options.categorySecond;
			}
			//商城商品三级分类
			if (options.categoryThird) {
				this.parameter.categoryThird = options.categoryThird;
			}
			//商城商品四级分类
			if (options.categoryFourth) {
				this.parameter.categoryFourth = options.categoryFourth;
			}
			//商城商品五级分类
			if (options.categoryFifth) {
				this.parameter.categoryFifth = options.categoryFifth;
			}
			//店铺商品一级分类
			if (options.categoryShopFirst) {
				this.parameter.categoryShopFirst = options.categoryShopFirst;
			}
			//店铺商品二级分类
			if (options.categoryShopSecond) {
				this.parameter.categoryShopSecond = options.categoryShopSecond;
			}
			if (options.brandCategoryShopFirst) {
				this.parameter.brandCategoryShopFirst = options.brandCategoryShopFirst;
			}
			if (options.brandCategoryShopSecond) {
				this.parameter.brandCategoryShopSecond = options.brandCategoryShopSecond;
			}

			if (options.name && !options.searchKeyword) {
				console.log('33333333333333');
				let parameter = decodeURIComponent(options.name);
				this.parameter.name = decodeURI(parameter);
			}

			if (options.type) {
				if (options.type == '1') {
					this.title = '新品首发';
					this.page.descs = 'create_time';
				}

				if (options.type == '2') {
					this.title = '热销单品';
					this.page.descs = 'sale_num';
				}
			}

			if (options.couponUserId) {
				this.parameter.couponUserId = options.couponUserId;
			}

			if (options.shopId) {
				this.parameter.shopId = options.shopId;
			}
			if (options.categoryType == '2') {
				this.parameter.categorySecond = options.categorySecond;
				this.parameter.shopBrandId = options.shopBrandId;
				this.categoryType = '2';
			}

			if (options.searchType) {
				this.searchType = options.searchType;
			}

			//大转盘通过id查询劵详情获取所需字段，查询劵商品列表
			app.initPage().then((res) => {
				if (options.couponId) {
					this.parameter.couponId = options.couponId;
					this.getCouponInfo(options.couponId);
				}
				this.goodsPage(true, true); //就第一次掉店铺信息传入，后续就没有店铺信息了
				this.getSearchfeedback(); //获取反馈内容文案
			});
		},

		methods: {
			// 搜索反馈弹窗其它文本内容
			textareaInput(e) {
				let dataset = e.detail.value;
				this.textareaFeedback = dataset
			},

			// 隐藏反馈弹窗 
			hideModal() {
				this.modalFeedback = ''
				this.selectedFeedback = []
				this.textareaFeedback = ''
				this.checkfeedback = []
			},

			// 显示反馈弹窗
			isModalShow() {
				this.modalFeedback = 'show'
				this.getSearchfeedback(); //获取反馈内容文案
			},

			//提交
			async submit() {
				const combined = [...this.selectedFeedback];
				// 只有 textareaFeedback 有内容才添加
				if (this.textareaFeedback && this.textareaFeedback.trim() !== '') {
					combined.push(this.textareaFeedback);
				}

				// 反馈内容拼接成多选分号分隔
				const formattedStr = '[\n' + combined.map(item => `"${item}";`).join('\n') + '\n]';
				console.log("=提交=submit", formattedStr);
				try {
					const parameter = {
						words: this.parameter.name,
						content: formattedStr,
					}
					const data = await addSearchfeedback(parameter)
					uni.showToast({
						title: '提交成功',
						icon: 'none',
						duration: 2000
					});
					this.hideModal()
				} catch (error) {
					lconsole.log(error)
				}
			},

			// 反馈内容
			checkboxChange(e) {
				const values = e.detail.value;
				this.selectedFeedback = values;
				this.isDisabled = !(values && values.length);
			},

			// 查询反馈内容
			async getSearchfeedback() {
				try {
					const {
						data
					} = await getSearchfeedback()
					// 转换成带checked的对象数组，初始都不选中
					this.checkfeedback = data.map(item => ({
						value: item,
						checked: false,
					}));
					console.log("getSearchfeedback", data, this.checkfeedback);
				} catch (error) {
					console.log(error);
				}
			},

			//删除搜索内容
			delSearchKeyword() {
				this.parameter.name = '';
			},

			// 判断是否是tab页
			isTabBarPage(url) {
				const tabBarPages = ['pages/home/<USER>', 'pages/second-tab/index', 'pages/shopping-cart/index',
					'pages/tab-personal/index', 'pages/third-tab/index'
				]; // 添加你的tabBar页面路径
				return tabBarPages.includes(url);
			},
			handleToPage(url) {
				if (url == 'CustomerService') {
					util.handleCustomerService();
				} else if (url.startsWith('pages')) {
					if (this.isTabBarPage(url)) {
						uni.switchTab({
							url: '/' + url
						})
					} else {
						uni.navigateTo({
							url: '/' + url
						})
					}
				}
			},

			getshopList(index, item) {
				if (this.nameIndex == index) {
					this.parameter.goodsCategoryParameterOptionId = '';
					this.nameIndex = null;
					this.relod();
				} else {
					this.parameter.goodsCategoryParameterOptionId = item.id;
					this.itemIndex = `index${index}`;
					this.nameIndex = index;
					this.relod();
					console.log(index, this.nameIndex, '----');
				}
			},
			//获取店铺分类商品
			getCategoryList(item, boole) {
				console.log('店铺分类==》', item, item.shopId, boole);
				if (item) {
					if (boole) {
						// 店铺一级分类  categoryShopFirst
						if (item.level == '1') {
							this.parameter.brandCategoryShopSecond = '';
							this.parameter.brandCategoryShopFirst = item.id;
						}
						// 店铺二级分类 categoryShopSecond
						if (item.level == '2') {
							this.parameter.brandCategoryShopFirst = '';
							this.parameter.brandCategoryShopSecond = item.id;
						}
					} else {
						this.parameter.brandCategoryShopFirst = '';
						this.parameter.brandCategoryShopSecond = '';
					}
					this.relod();
				}
			},

			//购物车摇
			caetShake(isShake, num) {
				console.log('isShake', isShake);
				this.animation = '';
				setTimeout(() => {
					this.animation = `${isShake}`;
				}, 800);
				if (isShake) {
					//组件传入的购物车数量
					this.shoppingCartCount = num;
				}
			},

			//解决多次点击搜索与base/search/index页面互相跳转的参数传递问题
			getBackParams(params) {
				this.parameter = {
					...this.parameter,
					...params
				};
				if (this.isRefreshGoodsList) {
					this.title = this.parameter.name;
				}
				// console.log("this.parameter===>", this.parameter,"title==>",this.title)
				this.goodsPage(true, true);
			},

			//通过id查询劵详情获取所需字段
			getCouponInfo(id) {
				api.getCouponInfo(id)
					.then((res) => {
						this.couponInfo = res.data;
					})
					.catch((e) => {});
			},

			//数组传参 arryParameter
			arryParameter() {
				console.log('测', this.sortList);
				let data = {
					sortList: this.sortList
				};

				function parseJson(data, newData, prefix, isArray) {
					if (!prefix) {
						prefix = '';
					}
					// 循环所有键
					for (var key in data) {
						var element = data[key];
						if (element.length > 0 && typeof element == 'object') {
							var tempPrefix;
							if (isArray) {
								tempPrefix = prefix + '.';
							}
							if (prefix) {
								tempPrefix = tempPrefix ? tempPrefix : prefix + '.' + key;
							} else {
								tempPrefix = key;
							}
							parseJson(element, newData, tempPrefix, true);
						} else if (typeof element == 'object') {
							var tempPrefix;
							if (isArray) {
								tempPrefix = prefix + '[' + key + ']';
							} else if (prefix) {
								tempPrefix = prefix + '.' + key;
							} else {
								tempPrefix = key;
							}
							parseJson(element, newData, tempPrefix, false);
						} else {
							if (typeof element == 'undefined' || !element) {
								continue;
							}
							if (!prefix) {
								newData[key] = element;
							} else if (isArray) {
								newData[prefix + '[' + key + ']'] = element;
							} else {
								newData[prefix + '.' + key] = element;
							}
						}
					}
				}
				var newData = {};
				parseJson(data, newData);
				return newData;
			},

			getMore() {
				if (!this.loadmore) return;
				if (this.parameter.couponId > 0) {
					this.goodsGroupsPage.current += 1;
				} else {
					this.onePage = false
					this.page.start += 10;
				}
				this.goodsPage();
			},

			//滚动显示
			handleScroll(e) {
				this.scrollY = parseInt(e.detail.scrollTop);
				//如果距顶部小于20，就直接等于0
				if (this.scrollY < 50) {
					this.scrollY = 0;
				}
			},

			/**
			 * @param {Object} isChangeShopInfo  是否更换店铺的信息，因为带点击查询条件的搜索接口没有店铺信息返回
			 */
			goodsPage(isChangeShopInfo, isRefresh) {
				if (isRefresh) {
					this.page.start = 0;
					this.goodsList = [];
					this.getGoodsCategoryShopList = [];
					this.recommendItems = []
				}
				if (isChangeShopInfo) {
					this.initialCategory = isChangeShopInfo;
					this.shopInfo = {};
					this.parameter.brandCategoryShopFirst = '';
					this.parameter.brandCategoryShopSecond = '';
				}
				//指定优惠券使用的商品
				if (this.parameter.couponId > 0) {
					//指定优惠券
					const params = Object.assign({}, this.parameter, this.goodsGroupsPage, {
						keyWord: this.parameter.name && this.categoryType != '2' ? this.parameter.name : ''
					});
					if (this.inventoryState == '0') {
						params.inventoryState = '0';
					}
					console.log(params, 'paramsparams');
					api.getGoodsInfoByCouponId(params).then((res) => {
						let goodsList = res.data.records;
						console.log('goodsList', goodsList);
						this.goodsList = [...this.goodsList, ...goodsList];
						if (goodsList.length < this.goodsGroupsPage.size) {
							this.loadmore = false;
						}
					});
					return;
				}
				let getser = {
					keyWord: this.parameter.name && this.categoryType != '2' ? this.parameter.name : '',
					shopId: this.parameter.shopId ? this.parameter.shopId : ''
				};
				let data = this.arryParameter();
				//inventoryState == '0'仅看有货
				if (this.inventoryState == '0') {
					this.searchList(
						Object.assign({}, data, util.filterForm(this.page), getser, this.parameter, {
							inventoryState: '0'
						}),
						isChangeShopInfo
					);
				}
				// else if(this.categoryType == '2'){
				// 	this.parameter={}
				// 	this.parameter.categorySecond = this.categorySecondnum;
				// 	this.parameter.shopBrandId = this.shopBrandId;
				// 	this.brandcategoryshopfirst = this.brandcategoryshopfirst
				// 	this.brandcategoryshopsecond = this.brandcategoryshopsecond
				// 	this.searchList(Object.assign(this.parameter),isChangeShopInfo
				// 		)
				// }
				else {
					console.log(Object.assign({}, data, util.filterForm(this.page), getser, this.parameter),
						isChangeShopInfo, '===================');
					this.searchList(Object.assign({}, data, util.filterForm(this.page), getser, this.parameter),
						isChangeShopInfo);
				}
			},

			// 搜索列表方法，调用接口获取搜索结果并处理数据
			searchList(list, isChangeShopInfo) {
				let that = this;
				api.searchList(list).then((res) => {
					const data = res.data.result || {};
					// 保存搜索结果整体数据
					this.searchResult = data;
					console.log('this.page.start', this.page.start)
					// 只需要第一页数据
					if (this.onePage && this.page.start === 0) {
						// 搜索结果提示文案
						this.resultTip = data.resultTip
						// 搜索意见反馈条件
						this.needFeedback = data.needFeedback
					}
					// 处理推荐商品列表
					const recommendItems = data.recommendItems || [];
					// 过滤出含有 goodsInfo 的推荐商品
					const searchRecommendItems = recommendItems.filter(item => item.goodsInfo);
					// 提取商品信息数组
					const recommendGoodsList = searchRecommendItems.map(item => item.goodsInfo);
					// 合并到已有推荐商品列表
					this.recommendItems = [...this.recommendItems, ...recommendGoodsList];
					console.log("=推荐商品=recommendItems==", this.recommendItems, data);
					// 如果推荐商品数量少于分页大小，关闭加载更多
					// if (recommendGoodsList.length < this.page.hit) {
					// 	this.loadmore = false;
					// }
					// 处理搜索商品列表
					const searchGoodsList = data.items || [];
					// 如果是第一页，更新提示信息
					if (list.start === 0) {
						this.tips = data.tip || '';
					}
					// 根据标志位更新店铺信息
					if (data.shopInfoList && isChangeShopInfo) {
						this.shopInfo = data.shopInfoList[0] || {};
					}
					// 更新搜索导航列表
					this.searchNavigationList = data.searchNavigationList || [];
					// 根据标志位更新店铺广告列表
					if (data.shopAdvertsList && isChangeShopInfo) {
						this.shopAdvertsList = data.shopAdvertsList;
					}
					// 更新品牌分类店铺列表，并设置初始分类标志
					if (data.brandCategoryShopList) {
						this.initialCategory = isChangeShopInfo;
						this.getGoodsCategoryShopList = data.brandCategoryShopList;
					}
					// 如果是第一页，更新筛选选项
					if (list.start === 0) {
						this.options = data.options || [];
					}
					// 过滤出含有 goodsInfo 的搜索商品
					const searchGoodsInfo = searchGoodsList.filter(item => item.goodsInfo);
					// 提取商品信息数组
					const goodsList = searchGoodsInfo.map(item => item.goodsInfo);
					// 合并到已有商品列表
					this.goodsList = [...this.goodsList, ...goodsList];
					// 处理其他店铺信息，设置样式属性
					if (data.otherShopInfos && data.otherShopInfos.length) {
						this.otherShopInfos = data.otherShopInfos;
						this.otherShopInfos.forEach(item => {
							item.marginBottomSpacing = '0';
							item.marginLeftSpacing = '0';
							item.marginTopSpacing = '0';
							item.moreType = '';
							item.paddingLeftSpacing = '10';
							item.paddingTopSpacing = '6';
							item.pageSpacing = 0;
							item.titleColor = 'red';
							item.titleIcon = 'cuIcon-message';
							item.titleShow = '0';
							item.background = '#fff';
						});
					}
					// // 如果本次获取的商品数量少于分页大小，关闭加载更多
					// if (searchGoodsInfo.length < this.page.hit) {
					// 	this.loadmore = false;
					// }
					// 判断推荐商品和搜索商品数量是否满足分页大小
					const recommendEnough = recommendGoodsList.length < this.page.hit;
					const goodsEnough = searchGoodsInfo.length < this.page.hit;

					console.log("=搜索商品=searchGoodsInfo==", searchGoodsInfo, goodsEnough);
					console.log("=推荐商品=recommendGoodsList==", recommendGoodsList, recommendEnough);
					console.log("recommendEnough && goodsEnough", recommendEnough && goodsEnough, "this.loadmore",
						this.loadmore);

					// 只有当两个数组都小于分页大小时，才关闭加载更多
					if (recommendEnough && goodsEnough) {
						this.loadmore = false;
					}
					// 获取顶部元素高度，调整页面布局
					that.$nextTick(() => {
						uni.createSelectorQuery().select('.shop-top').boundingClientRect(data => {
							that.topHeight = (data && data.height) || 0;
							// 如果有优惠券，增加顶部高度
							if (that.parameter.couponId > 0) {
								that.topHeight += 140;
							}
						}).exec();
					});
				}).catch(error => {
					console.error('搜索列表接口调用失败:', error);
					// 这里可以添加错误处理逻辑，比如提示用户或重置加载状态
				});
			},
			// handleDealsearchList(data) {
			// 	if (
			// 		data.searchNavigationList &&
			// 		data.searchNavigationList.length > 0 &&
			// 		(!data.shopAdvertsList || data.shopAdvertsList.length == 0) &&
			// 		(!data.shopInfoList || data.shopInfoList.length == 0) &&
			// 		(!data.items || data.items.length == 0)
			// 	) {
			// 		let pages = getCurrentPages();
			// 		if (data.searchNavigationList[0].url.startsWith('pages')) {
			// 			gotoPage('/' + data.searchNavigationList[0].url, pages.length, true);
			// 		} else if (data.searchNavigationList[0].url == 'CustomerService') {
			// 			util.handleCustomerService();
			// 		}
			// 		return true;
			// 	}
			// 	// 不符合上述请求继续渲染该页面
			// 	return false;
			// },
			viewTypeEdit() {
				if (this.viewType == 'cascades') {
					this.viewType = 'list';
				} else {
					this.viewType = 'cascades';
				}
			},

			sortHandle(e) {
				console.error('lizhijuan--sortHandle-->');
				let type = e.target.dataset.type;
				switch (type) {
					//价格
					case 'price':
						if (this.price == '') {
							this.price = 'asc';
							//升序
							this.sortList = [];
							let price = {
								columns: 'price_down',
								sortType: 1
							};
							this.sortList.push(price);
							//优惠劵指定分组，指定商品的情况
							if (this.parameter.couponId) {
								this.goodsGroupsPage = {
									current: 1,
									size: 10,
									ascs: 'price_down',
									//升序字段
									descs: ''
								};
							}
						} else if (this.price == 'asc') {
							this.sortList = [];
							let price = {
								columns: 'price_down',
								sortType: '0'
							};
							this.sortList.push(price);
							this.price = 'desc';
							if (this.parameter.couponId) {
								this.goodsGroupsPage = {
									current: 1,
									size: 10,
									ascs: '',
									//升序字段
									descs: 'price_down'
								};
							}
						} else if (this.price == 'desc') {
							this.price = '';
							this.sortList = [];
							if (this.parameter.couponId) {
								this.goodsGroupsPage = {
									current: 1,
									size: 10,
									ascs: '',
									//升序字段
									descs: ''
								};
							}
						}
						this.sales = '';
						this.createTime = '';
						this.inventoryState = '';
						break;
						//销量
					case 'sales':
						if (this.sales == '') {
							this.sales = 'desc';
							this.sortList = [];
							let sales = {
								columns: 'sale_num',
								sortType: '0'
							};
							this.sortList.push(sales);
							if (this.parameter.couponId) {
								this.goodsGroupsPage = {
									current: 1,
									size: 10,
									ascs: '',
									//升序字段
									descs: 'sale_num'
								};
							}
						} else if (this.sales == 'desc') {
							this.sales = 'asc';
							this.sortList = [];
							let sales = {
								columns: 'sale_num',
								sortType: 1
							};
							this.sortList.push(sales);
							if (this.parameter.couponId) {
								this.goodsGroupsPage = {
									current: 1,
									size: 10,
									ascs: 'sale_num',
									//升序字段
									descs: ''
								};
							}
						} else if (this.sales == 'asc') {
							this.sales = '';
							if (this.parameter.couponId) {
								this.goodsGroupsPage = {
									current: 1,
									size: 10,
									ascs: '',
									//升序字段
									descs: ''
								};
							}
							this.sortList = [];
						}
						this.price = '';
						this.goodsTitle = '';
						this.inventoryState = '';
						break;
						//综合推荐
					case 'title':
						if (this.goodsTitle == '') {
							this.goodsTitle = 'desc';
							this.page.descs = 'create_time';
							this.sortList = [];
							this.page.ascs = '';
						} else if (this.goodsTitle == 'title') {
							this.goodsTitle = '';
							this.sortList = [];
							this.page.descs = '';
							this.page.ascs = '';
							this.inventoryState = '';
						}
						this.price = '';
						this.sales = '';
						this.inventoryState = '';
						if (this.parameter.couponId) {
							this.goodsGroupsPage = {
								current: 1,
								size: 10,
								ascs: '',
								//升序字段
								descs: ''
							};
						}
						break;
						//仅看有货
					case 'goods':
						if (this.inventoryState == '') {
							this.inventoryState = '0';
							this.page.descs = 'create_time';
							this.page.ascs = '';
							if (this.parameter.couponId) {
								this.goodsGroupsPage.inventoryState = '0';
							}
						} else if (this.inventoryState == '0') {
							this.inventoryState = '';
							this.page.descs = '';
							this.page.ascs = '';
							if (this.parameter.couponId) {
								this.goodsGroupsPage = {
									current: 1,
									size: 10,
									ascs: '',
									//升序字段
									descs: ''
								};
							}
						}
						this.price = '';
						this.sales = '';
						this.goodsTitle == '';
						if (this.parameter.couponId) {
							this.goodsGroupsPage.descs = '';
							this.goodsGroupsPage.ascs = '';
						}
						break;
				}
				this.relod();
			},

			relod() {
				this.loadmore = true;
				this.goodsList = [];
				this.page.start = 0;
				this.goodsGroupsPage.current = 1;
				this.goodsPage(false, true);
			},
			navTo(url) {
				uni.navigateTo({
					url
				});
			}
		},

		onUnload() {
			// 给神策发送 搜索结果页离开的事件
			if (this.searchType) {
				senTrack('SearchResultPageLeave', {
					search_keyword: this.parameter.name,
					keyword_type: this.searchType
				})
			}
		}

	};
</script>
<style scoped lang="scss">
	.selectedFeedback {
		color: #FFFFFF;
		background: linear-gradient(-90deg, #CCAC92 0%, #C09979 0%, #CCAB8F 100%);
	}

	.feedback {
		background: #DCDDDF;
		color: #909194;
	}

	.fixed {
		position: fixed;
		top: 100px;
	}

	.excessive-cu-btn {
		position: relative;
		border: 0rpx;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;
		text-align: center;
		text-decoration: none;
		overflow: visible;
		margin-left: initial;
	}

	.excessive-follow {
		font-size: 17rpx;
		height: 25rpx;
	}

	.excessive-sm {
		margin-right: 6rpx;
		color: #ffffff;
		border-radius: 4rpx;
		color: #fff;
		display: inline-block;
		line-height: 1;
		padding: 6rpx 4rpx 3rpx 4rpx;
		vertical-align: middle;
		height: fit-content;
	}

	.excessive-btn {
		font-size: 26rpx;

		font-weight: 400;
		margin-top: 15rpx;
		color: #1a1a1a;
		height: 44rpx;
	}

	.excessive {
		position: relative;
		background: #ffffff;
		width: 750rpx;
		top: 0rpx;
		// left: 12rpx;
	}

	.topfixed-active {
		width: 100%;
		position: fixed;
		top: 0rpx;
		left: 0rpx;
		background: #fff;
		z-index: 9999;
		box-sizing: border-box;
	}

	.excessive-banner-icon {
		width: 152rpx;
		height: 76rpx;
		border-radius: 12rpx;
		border: solid 1px #ededed;
	}

	.excessive-banner-text {
		display: block;
		flex-direction: row;
	}

	.excessive-title {
		font-size: 28rpx;
		/**单行显示超出省略 */
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		width: 255rpx; // 第一种样式标签和店铺并齐
		width: 365rpx; //第二种样式标签在店铺下面
		color: #000;
	}

	.goods-search {
		top: unset !important;
		min-height: 100rpx !important;
	}

	.goods-nav {
		top: unset !important;
		margin-top: 100rpx;
	}

	.cuIcon-triangledownfill {
		margin-top: -22rpx;
	}

	.coupon-top {
		background-image: url(https://img.songlei.com/1/material/5eb12e53-a620-4913-a704-fd34178fa64f.png);
		background-size: contain;
		padding: 30rpx 20rpx;
	}

	.category-box {
		background-color: #fff;
		padding-top: 10rpx;
		padding-left: 10rpx;
	}

	.prefer-scroll {
		white-space: nowrap;
		width: 100%;
	}

	.scroll-view-item {
		display: inline-block;
		width: 115rpx;
		text-align: center;
		font-size: 24rpx;
	}

	.category-img {
		width: 80rpx;
		height: 80rpx;
	}

	.category-title {
		font-size: 22rpx;
		// width: 80rpx;
		text-align: center;
		color: #888888;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	@media (min-width: 549px) {
		.excessive-btn {
			height: 38rpx;
		}

		.excessive-banner-icon {
			width: 92rpx;
			height: 46rpx;
		}

		.my-banner {
			height: 178rpx;
		}
	}
</style>