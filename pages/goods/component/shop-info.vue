<template>
	<view class="plr-md ptb-xxmd bg-white" v-if="localShopInfo">
		<navigator :url="'/pages/shop/shop-detail/index?id=' + localShopInfo.id" class="flex content align-center" hover-class="none">
			<image style="flex-shrink: 0" class="size-100" :src="localShopInfo.imgUrl" mode="aspectFit"></image>
			<view style="flex: 1">
				<view class="ml-xs flex justify-between" style="flex: 1">
					<view class="ml-xs flex flex-direction" style="flex: 1">
						<view class="ml-xxs text-bold flex">
							<text class="text-bold font-xxmd text-black overflow-1">{{ localShopInfo.name | finishingStr }}</text>
							<text class="cuIcon-right text-88 font-md ml-xxs" style="min-width: 50rpx;"></text>
						</view>
					</view>
					<view class="ml-xs flex justify-between" style="flex-shrink: 0;">
						<view class="shop-right plr-md ptb-xs line-height-1" @tap.stop="wxTemplate()">
							<text class="cuIcon-favor font-xxxsm"></text>
							<text class="font-md ml-s">{{ localShopInfo.collectId ? '已收藏' : '收藏' }}</text>
						</view>
					</view>
				</view>
				<view class="ml-xs flex justify-between align-center mt-md" style="flex: 1">
					<view class="ml-xs flex flex-direction" style="flex: 1">
						<view class="font-md flex">
							<text v-if="localShopInfo.selfSupport == 1" class="ptb-s plr-md text-orange line-height-1" style="background: #ffe8e6; border-radius: 6rpx">自营</text>
							<text class="ptb-s plr-md text-orange ml-sm line-height-1" style="background: #ffe8e6; border-radius: 6rpx">上新</text>
						</view>
					</view>
					<text v-if="localShopInfo.collectCount > 0" style="color: #959396; flex-shrink: 0" class="font-md">{{ localShopInfo.collectCount }}人关注</text>
				</view>
			</view>
		</navigator>
	</view>
</template>

<script>
import { getWxTemplate } from '@/api/message.js';
import api from 'utils/api';

export default {
	data() {
		return {};
	},
	filters: {
		finishingStr: function (value) {
			if (!value) return '';
			console.log(value);
			return value.replace(/\n+/g, '');
		}
	},
	props: {
		shopInfo: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			localShopInfo: {}
		};
	},

	watch: {
		shopInfo: {
			handler(newVal, oldVal) {
				this.localShopInfo = newVal;
			},
			immediate: true,
			deep: true
		}
	},

	methods: {
		//跳转到商铺首页
		toShopHome(id) {
			uni.navigateTo({
				url: '/pages/shop/shop-detail/index?id=' + id
			});
		},

		callPhone(phone) {
			uni.makePhoneCall({
				phoneNumber: phone
			});
		},

		wxTemplate(type) {
			// #ifdef MP
			getWxTemplate({ type: 12 }).then((res) => {
				uni.requestSubscribeMessage({
					tmplIds: res.data,
					complete: () => {
						this.userCollect();
					}
				});
			});
			// #endif
			// #ifndef MP
			this.userCollect();
			// #endif
		},

		//收藏
		userCollect() {
			let shopInfo = this.localShopInfo;
			let collectId = shopInfo.collectId;
			if (collectId) {
				api.userCollectDel(collectId).then((res) => {
					// uni.showToast({
					// 	title: '已取消订阅',
					// 	icon: 'success',
					// 	duration: 2000
					// });
					shopInfo.collectId = null;
					shopInfo.collectCount = shopInfo.collectCount - 1;
					this.localShopInfo = shopInfo;
					this.$emit("collect","取消收藏");
				});
			} else {
				api.userCollectAdd({
					type: '2',
					relationIds: [shopInfo.id]
				}).then((res) => {
					// uni.showToast({
					// 	title: '收藏成功',
					// 	icon: 'success',
					// 	duration: 2000
					// });
					this.$emit("collect","收藏");
					shopInfo.collectId = res.data[0].id;
					shopInfo.collectCount = shopInfo.collectCount + 1;
					this.localShopInfo = shopInfo;
				});
			}
		}
	}
};
</script>
<style scoped>
.shop-right {
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(90deg, #FF1B01 0%, #FC5713 100%);
	border-radius: 10rpx;
}
</style>
