<template>
<!-- 过渡页淡入淡出轮播图 -->
    <view class="my-banner" >
      <view class="banner-imgs" @touchstart="touchstart" @touchend="touchend" :class="{'on': bannerIndex == index}" v-for="(item, index) in newData" :key="item.id">
        <image @tap.stop="jumpPage(item.linkUrl)" :src="item.imgUrl" mode="aspectFill" class="banner-imgs_img"></image>
      </view>

      <scroll-view
      v-if="newData&&newData.length>1&&newData[0].name"
        class="prefer-scroll banner-dots"
        scroll-x="true"
        :scroll-into-view="'id-'+bannerIndex"
        >
          <block v-for="(item, index) in newData" :key="item.id">
              <view :id="('id-'+index)" class="banner-dots_item" :class="{'active': bannerIndex == index}" @tap="ckeckImg(index)">{{item.name}}</view>
          </block>
      </scroll-view>
    </view>
</template>

<script>
const app = getApp();
import {
  gotoPage
} from "@/components/div-components/div-base/div-page-urls.js";
export default {
  props: {
    value: {
      type: Object | null,
      default:{}
    }
  },
  components: {},
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      bannerIndex: 0,
      setInter1: null, // 定时器
      startData: { // 滑动数据
        clientX: 0, // 左右滑动
        clientY: 0 // 上下滑动
      },
    };
  },

  mounted() {
    if (this.newData&&this.newData.length>1&&this.newData[0].name) {
        console.log("2222222");
      this.changePic()
    }
  },

  methods: {
    //点击切换图片
    ckeckImg (i) {
      this.bannerIndex =i
    },

    //图片跳转路径
    jumpPage (page) {
      console.log("---page--->",page);
      if (page) {
        gotoPage(page);
      } 
    },

    // 淡入淡出轮播图
    changePic () {
      clearInterval(this.setInter1) // 先将已有的计时器清除
      this.setInter1 = setInterval (function () { // 循环
      console.log(111)
        this.bannerIndex++
        if(this.bannerIndex == this.newData.length){
          this.bannerIndex = 0
        }
      }.bind(this), 2000)
    },
    
    // 滑动开始
    touchstart (e) {
      console.log('开始位置', e.changedTouches[0].clientX)
      this.startData.clientX=e.changedTouches[0].clientX
      this.startData.clientY=e.changedTouches[0].clientY
    },

    // 滑动结束
    touchend (e) {
      console.log('结束位置', e.changedTouches[0].clientX)
      const subX = e.changedTouches[0].clientX - this.startData.clientX
      const subY = e.changedTouches[0].clientY - this.startData.clientY
      if(subY > 50 || subY < -50) {
        console.log('上下滑')
      }else{
        if(subX > 100){ // 右滑，显示前一张，当前的页面减一。如果当前页面是第一张，显示最后一张。
          if(this.bannerIndex == 0) {
            this.bannerIndex = this.newData.length - 1
          } else {
            this.bannerIndex--
          }
          this.changePic()
        }else if(subX < -100){ // 左滑，显示下一张，当前的页面加一。如果当前页面是最后一张，显示第一张。
          if(this.bannerIndex == this.newData.length - 1) {
            this.bannerIndex = 0
          } else {
            this.bannerIndex++
          }
          this.changePic()
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
	.prefer-scroll {
  white-space: nowrap;
  height: 52rpx;
  width: 711rpx;
  // width: 100%;
}
// 淡入淡出轮播图
.my-banner {
		width: 100%;
		height: 296rpx;
		position: relative;
		.banner-imgs {
			width: 100%;
			height: 100%;
			opacity: 0;
			transition-duration: 1s;	/*设置过渡时间*/
			position: absolute;
			.banner-imgs_img {
				position: absolute;		/*把所有图片放在同一个位置*/
				width: 100%;
				height: 100%;
			}
		}
		
		.banner-dots {
      
      height: 52rpx;
      background: #000000;
      opacity: 0.5;
			position: absolute;
			bottom: 0rpx;
			left: 50%;
			transform: translateX(-50%);
			.banner-dots_item {
				display: inline-block;
        text-align: center;
				width: 142rpx;
				height: 50rpx;
				color: #fff;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
        border-right:1rpx solid #fff ;
			}
			.active {
				background-color: #DEAE7F;
			}
		}
		
		.on {
			opacity: 1;
		}
	}

.screen-swiper {
  min-height: 90upx !important;
}
</style>
