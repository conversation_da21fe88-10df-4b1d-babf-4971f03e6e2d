<template>
	<view class="bg-white plr-md pb-md">
		<view class="text-black text-bold font-xxmd ptb-md">精选商品</view>
		<swiper
			class="screen-swiper square-dot recommend-swiper"
			indicator-color="#EBEBEB"
			indicator-active-color="#FFE8E6"
			indicator-dots
			:autoplay="false"
			:interval="3000"
			:duration="1000"
		>
			<swiper-item v-for="(page, index) in goodsPages" :key="index">
				<view class="goods-grid">
					<view class="goods-item p-s" v-for="(item, i) in page" :key="i"  @click="handleToDetail(item)">
						<image :src="item.picUrls[0] | formatImg360" mode="aspectFill"></image>
						<view class="padding-top-xs text-black font-xxmd overflow-2">{{ item.name }}</view>
						<format-price
							styleProps="display:flex; align-items:baseline;"
							color="#FF530C"
							signFontSize="20rpx"
							smallFontSize="24rpx"
							priceFontSize="32rpx"
							:price="item.priceDown"
						></format-price>
					</view>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
import { getGoodsRecom } from '../api/goods.js';
import { pageUrls, gotoPage } from '@/components/div-components/div-base/div-page-urls.js';
export default {
	props: {
		spuId: {
			type: String,
			default: ''
		}
	},

	data() {
		return {
			goodsPages: [] // 分页后的商品列表
		};
	},

	watch: {
		spuId: {
			handler(newVal, oldVal) {
				this.getGoodsRecom();
			},
			immediate: true
		}
	},

	methods: {
		async getGoodsRecom() {
			const res = await getGoodsRecom({
				id: this.spuId
			});
			if (res && res.data && res.data.length > 0) {
				// 将商品数据分页，每页6个商品（三行两列）
				this.goodsPages = this.chunkArray(res.data, 6);
			}
		},

		// 将数组分页
		chunkArray(arr, size) {
			const result = [];
			for (let i = 0; i < arr.length; i += size) {
				result.push(arr.slice(i, i + size));
			}
			return result;
		},

		handleToDetail(item) {
			//监听当前页面栈的个数内容
			let pages = getCurrentPages();
			gotoPage('/pages/goods/goods-detail/index?id=' + item.id, pages.length);
		}
	}
};
</script>

<style lang="scss"  scope>
@import 'public/colorui/media.scss';	
.goods-grid {
	display: flex;
	flex-wrap: wrap;
}

.goods-item {
	width: 33.3%;
	box-sizing: border-box;
}

.goods-item image {
	width: 100%;
	height: 33vw;
	border-radius: 5px;
}

.goods-item text {
	display: block;
	margin-top: 5px;
	font-size: 14px;
}

.recommend-swiper {
	height: calc(100vw + 56rpx); 
	width: 100%;
}

@media (min-width: $breakpoint-medium) and (max-width: $breakpoint-large) {
	.recommend-swiper {
		height: calc(96vw); 
	}
}
</style>
