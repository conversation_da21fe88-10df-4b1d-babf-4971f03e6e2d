<template>
	<view class="bg-white">
		<view v-if="brandAuthImg">
			<view class="text-black text-bold font-xxmd ptb-md plr-md">品牌授权</view>
			<image style="width: 100%" mode="widthFix" :src="brandAuthImg"></image>
		</view>

		<view v-if="specificationVo" class="plr-md">
			<view class="text-black text-bold font-xxmd ptb-md">规格参数</view>
			<view class="table">
				<!-- 表格内容 -->
				<template v-if="specificationVo.brandInfo">
					<view class="table-row" v-for="(value, key) in specificationVo.brandInfo" :key="key">
						<view class="table-cell padding-sm font-md" :style="{ 'border-right': '1px solid #EBEBEB' }">{{ key }}</view>
						<view class="table-cell padding-sm table-cell-r font-md">{{ value }}</view>
					</view>
				</template>

				<template v-if="specificationVo.brandInfo">
					<view v-for="(item, index) in specificationVo.goodsParams" :key="index">
						<view v-for="(valueP, keyP) in item" :key="keyP">
							<view class="table-row">
								<view class="table-cell padding-sm table-cell-p text-black" :style="{ 'border-right': '1px solid #EBEBEB' }" colspan="2">{{ keyP }}</view>
							</view>
							<view class="table-row" v-for="(valueC, keyC) in valueP" :key="keyP">
								<view class="table-cell padding-sm font-md" :style="{ 'border-right': '1px solid #EBEBEB' }">{{ keyC }}</view>
								<view class="table-cell padding-sm table-cell-r font-md">{{ valueC }}</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</view>

		<view v-if="serviceConfig && serviceConfig.length > 0" class="plr-md pt-md pb-lg">
			<view class="text-black text-bold font-xxmd">服务</view>
			<view v-for="item in serviceConfig">
				<view class="flex align-center">
					<view class="line"></view>
					<view class="text-df-px text-gray plr-xsm pt-md">{{ item.name }}</view>
					<view class="line"></view>
				</view>
				<jyf-parser :html="item.content"></jyf-parser>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		// 规格参数
		specificationVo: {
			type: Object,
			default: () => {}
		},

		// 品牌授权图片
		brandAuthImg: {
			type: String,
			default: ''
		},

		// 服务
		serviceConfig: {
			type: Array,
			default: () => []
		}
	}
};
</script>

<style scope>
.table {
	width: 100%;
	border-left: 1px solid #ebebeb;
	border-top: 1px solid #ebebeb;
	border-right: 1px solid #ebebeb;
	border-collapse: collapse;
}

.table-row {
	display: flex;
}

.table-cell {
	flex: 1;
	border-bottom: 1px solid #ebebeb;
}

.table-cell-r {
	flex: 3;
}

.table-cell-p {
	text-align: left;
}
.header {
	font-weight: bold;
	background-color: #f5f5f5;
}

.line {
	height: 1px;
	flex: 1;
	background-color: #ddd;
}
</style>