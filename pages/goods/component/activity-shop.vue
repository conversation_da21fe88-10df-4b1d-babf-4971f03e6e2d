<template>
	<view v-if="shopActAdverts && shopActAdverts.length > 0" class="bg-white " >
		<view class="text-black text-bold font-xxmd ptb-md plr-md">活动专区</view>
		<view v-for="(item, index) in shopActAdverts" :key="index">
			<div-base-navigator :pageUrl="item.linkUrl">
				<image :src="item.imgUrl | formatImg750" style="width: 100%; display: block" mode="widthFix"></image>
			</div-base-navigator>
		</view>
	</view>
</template>

<script>
import { pageUrls, gotoPage } from '@/components/div-components/div-base/div-page-urls.js';
import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue';
export default {
	components: {
		divBaseNavigator
	},
	props: {
		shopActAdverts: {
			type: Array | null,
			default:  () => [] 
		}
	},

	data() {
		return {};
	}
};
</script>

<style scope>
.goods-grid {
	display: flex;
	flex-wrap: wrap;
}

.goods-item {
	width: 33.3%;
	box-sizing: border-box;
}

.goods-item image {
	width: 100%;
	height: 100px;
	border-radius: 5px;
}

.goods-item text {
	display: block;
	margin-top: 5px;
	font-size: 14px;
}
</style>
