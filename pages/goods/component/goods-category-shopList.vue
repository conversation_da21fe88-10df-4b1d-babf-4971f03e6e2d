<template>
	<view class="category-box">
		<scroll-view class="prefer-scroll" scroll-x="true" :scroll-into-view="itemIndex">
			<view class="scroll-view-item padding-lr-sm" v-for="(item, index) in goodsCategoryShopList" :key="item.id">
				<view :id="`index${index}`" @click="getCategoryList(index, item)">
					<view style="text-align: center">
						<image class="category-img" mode="aspectFill" :src="item.picUrl ? item.picUrl : 'https://img.songlei.com/live/img/no_pic.png'"></image>
					</view>

					<view class="category-title text-xss" :style="{ color: nameIndex == index ? 'red' : '' }">
						{{ item.name&&item.name.length>6 ? item.name.substring(0, 7) : item.name }}
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	props: {
		//商品分类列表
		goodsCategoryShopList: {
			type: Array,
			default: []
		},
		//初始化分类
		initial: {
			type: Boolean
			// default:false
		}
	},
	data() {
		return {
			nameIndex: '-1',
			itemIndex: `index${0}`
		};
	},

	watch: {
		initial(newValue, oldValue) {
			console.log('nameIndex', this.nameIndex, 'this.itemIndex', this.itemIndex);

			if (this.initial) {
				this.nameIndex = '-1';
			}
		}
	},

	methods: {
		getCategoryList(index, item) {
			console.log('index', index, 'item', item);
			this.itemIndex = `index${index}`;
			if (this.nameIndex != index) {
				this.nameIndex = index;
				this.$emit('getCategoryList', item, true);
			} else {
				this.nameIndex = '-1';
				this.$emit('getCategoryList', item, false);
			}
		}
	}
};
</script>

<style lang="less" scoped>
.category-box {
	background-color: #fff;
	padding-top: 10rpx;
	padding-left: 10rpx;
}
.prefer-scroll {
	white-space: nowrap;
	width: 100%;
}
.scroll-view-item {
	display: inline-block;
	text-align: center;
	font-size: 24rpx;
}
.category-img {
	width: 80rpx;
	height: 80rpx;
}
.category-title {
	text-align: center;
	color: #888888;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

@media (min-width: 549px) {
	.category-img {
		width: 48rpx;
		height: 48rpx;
	}
}
</style>
