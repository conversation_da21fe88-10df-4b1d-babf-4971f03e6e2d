<template>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">商品评价</block>
    </cu-custom>
    <view class="cu-list menu-avatar comment">
      <view
        class="cu-item"
        v-for="(item, index) in goodsAppraises"
        :key="index"
      >
        <view
          class="cu-avatar round"
          :style="'background-image:url(' + item.headimgUrl + ')'"
        >{{!item.headimgUrl ? '头' : ''}}</view>
        <view class="content margin-top-xs">
          <view class="text-black flex">{{item.nickName ? item.nickName : '匿名'}}
            <view class="text-gray margin-left-sm text-sm">{{item.createTime}}</view>
          </view>
          <view
            class="text-gray text-sm"
            v-if="item.specInfo"
          >规格：{{item.specInfo}}</view>
          <base-rade
            :value="item.goodsScore"
            size="lg"
          ></base-rade>
          <view class="text-black text-content text-sm">{{item.content ? item.content : '此用户未填写评价内容'}}</view>
          <view class="grid col-4 grid-square flex-sub">
            <view
              class="bg-img margin-top-sm"
              v-for="(picUrl, index2) in item.picUrls"
              :key="index2"
              @click="previewImage([picUrl])"
            >
              <image
                :src="picUrl"
                mode='aspectFill'
              ></image>
            </view>
          </view>
          <view
            class="bg-gray padding-sm radius margin-top-sm text-sm"
            v-if="item.sellerReply"
          >
            <view class="flex text-sm text-orange">卖家 {{item.replyTime?item.replyTime:""}} 回复：</view>
            <view class="text-content">{{item.sellerReply}}</view>
          </view>
          <view
            class="margin-top-sm"
            v-if="item.listGoodsAppraises.length > 0"
          >
            <view class="text-orange text-sm padding-top-sm">在 {{item.listGoodsAppraises[0].createTime}} 追评</view>
            <view class="text-black text-content text-sm">{{item.listGoodsAppraises[0].content ? item.listGoodsAppraises[0].content : '此用户未填写评价内容'}}</view>
            <view class="grid col-4 grid-square flex-sub">
              <view
                class="bg-img margin-top-sm"
                v-for="(picUrl2, index3) in item.listGoodsAppraises[0].picUrls"
                :key="index3"
                @click="previewImage([picUrl2])"
              >
                <image
                  :src="picUrl2"
                  mode='aspectFill'
                ></image>
              </view>
            </view>
            <view
              class="bg-gray padding-sm radius margin-top-sm text-sm"
              v-if="item.listGoodsAppraises[0].sellerReply"
            >
              <view class="flex text-sm text-orange">卖家在 {{item.listGoodsAppraises[0].replyTime?item.listGoodsAppraises[0].replyTime:""}} 回复：</view>
              <view class="text-content">{{item.listGoodsAppraises[0].sellerReply}}</view>
            </view>
          </view>
          <view
            class="flex justify-end margin-top-sm"
            v-if="userInfo && userInfo.id == item.userId && item.listGoodsAppraises.length <= 0"
          >
            <button
              class="cu-btn round"
              :class="'bg-'+theme.themeColor"
              @tap="orderAppraise(item.id,item.orderId)"
            >写追评</button>
          </view>
        </view>
      </view>
    </view>
    <view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
  </view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import baseRade from "components/base-rade/index";

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'goods_score'
				},
				parameter: {},
				loadmore: true,
				goodsAppraises: [],
				userInfo: uni.getStorageSync('user_info'),
				//有数统计使用
				page_title:'评价列表'
			};
		},

  components: {
    baseRade
  },
  props: {},

  onLoad (options) {
    this.parameter.spuId = options.spuId;

  },

  onShow () {
    app.initPage().then(res => {
      this.goodsAppraises = []
      this.page.current = 1;
      this.loadmore = true;
      this.goodsAppraisesPage();
    });
  },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.goodsAppraisesPage();
    }
  },

  methods: {
    goodsAppraisesPage () {
      api.goodsAppraisesPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
        let goodsAppraises = res.data.records;
        this.goodsAppraises = [...this.goodsAppraises, ...goodsAppraises];
        if (goodsAppraises.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },
    previewImage (picUrl) {
      // 预览图片
      uni.previewImage({
        urls: picUrl,
        longPressActions: {
          itemList: picUrl,
          success: function (data) {

          },
          fail: function (err) {
            console.log(err.errMsg);
          }
        }
      });
    },
    orderAppraise (id, orderId) {
      uni.navigateTo({
        url: '/pages/appraises/form/index?orderId=' + orderId + '&parentId=' + id
      });
    }
  }
};
</script>
