<template>
  <view class="Welcome-page">
    <uni-popup
      ref="popup"
      :isMaskClick="false"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box">
        <view class="tou-box">{{ rule }}</view>
        <!-- 低于三个奖品 -->
        <swiper
          v-if="prizePics.length>=3"
          class="up-box"
          :display-multiple-items="3"
          :autoplay="true"
          :circular="true"
          :interval="3000"
          :duration="2000"
          next-margin="36rpx"
        >
          <swiper-item v-for="(u, i) in prizePics" :key="i">
            <view class="lunbo-box">
              <view class="lunbo">
                <image class="tu" mode="aspectFit" :src="u.url" />
              </view>
              <view class="wen">{{ u.name }}</view>
            </view>
          </swiper-item>
        </swiper>
        <view class="up-box flex justify-evenly" v-else>
          <view v-for="(u, i) in prizePics" :key="i">
            <view class="lunbo-box">
              <view class="lunbo">
                <image class="tu" mode="aspectFit" :src="u.url" />
              </view>
              <view class="wen">{{ u.name }}</view>
            </view>
          </view>
        </view>
        <view class="down-box">
          <view class="cell-box">
            <view
              class="cell"
              :class="u.id == prizeId ? 'sel' : 'unsel'"
              v-for="u in optionalPrizes"
              :key="u.id"
              @click="optionalChange(u)"
            >
              <image class="xuan" :src="iconPic.xuan" v-if="u.id == prizeId" />
              <image class="xuan" :src="iconPic.buxuan" v-else />
              <image class="tupian" mode="aspectFit" :src="u.url" />
              <view
                class="kucun"
                :style="{ top: u.id == prizeId ? '6rpx' : '8rpx' }"
              >
                <text v-if="u.prizeNum > 0"> 库存:{{ u.prizeNum }}</text>
                <text v-else>已抢光</text>
              </view>
              <view class="ming">{{ u.name }}</view>
            </view>
          </view>
          <view 
            class="absolute" 
            :style="{
              bottom: '0rpx',
              'z-index': '1',
              width: '670rpx',
              height: '123rpx',
              left: '50%',
              'background-image': `url(${iconPic.btnBg})`,
              'background-size': 'cover',
              transform: 'translate(-50%)'}">
            <view class="anniu-box" @click="selectPrize">
              <view class="wen">开始游戏</view>
              <image class="anniu" :src="waterTemplate.templateComponent.selectGoodsBtnimg" />
            </view>
          </view>
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.selectGoodsBgimg" @click="selectPrize" />
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/

import { selectPrizeApi } from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  props: {
    rule: {
      type: String,
      require: true,
      default: "",
    },

    prizePics: {
      type: Array,
      require: true,
      default() {
        return [];
      },
    },

      //游戏模版配置
		waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},

    optionalPrizes: {
      type: Array,
      require: true,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      id: "",
      prizeId: "",
      iconPic: {
        buxuan: "http://img.songlei.com/paradise/home/<USER>",
        xuan: "http://img.songlei.com/paradise/home/<USER>",
        btnBg: "http://img.songlei.com/paradise/btn-bg.png",
        // // 种树用
        // huang: "http://img.songlei.com/paradise/home/<USER>",
        // // 堆雪人用
        // snowmanHuang: "http://img.songlei.com/paradise/home/<USER>",
      },
      prizeNum:0
    };
  },

  mounted() {
    this.id = this.optionalPrizes[0].waterInfoId;
    console.log("prizePics",this.prizePics);

    this.$refs.popup.open("center");
  },
  methods: {
    optionalChange(info) {
      this.prizeNum = info.prizeNum;
      if (info.prizeNum > 0) {
        this.id = info.waterInfoId;
        this.prizeId = info.id;
      }else{
        uni.showToast({
          title: "已抢光了!，请重新选择礼品哦~",
          icon: "none",
        });
      }
    },
    selectPrize() {
      if (!this.prizeId) {
        uni.showToast({
          title: "请先选择礼品哦~",
          icon: "none",
        });
        return
      }

      if (this.prizeNum<= 0 ) {
        uni.showToast({
          title: "已抢光了!，请重新选择礼品哦~",
          icon: "none",
        });
        return
      }
      
      const params = {
        id: this.id,
        prizeId: this.prizeId,
      };
      selectPrizeApi(params).then((res) => {
        this.$emit("close");
        this.$refs.popup.close();
      });
    },
  },
};
</script>
<style scoped lang="scss">
.Welcome-page {
  .core-box {
    position: relative;
    width: 750rpx;
    height: 86vh;
    overflow: auto;
    .tou-box {
      width: 590rpx;
      height: 88rpx;
      line-height: 44rpx;
      margin-top: 176rpx;
      margin-left: 80rpx;
      transform: rotate(-2deg);
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      color: #9f4427;
      font-size: 32rpx;
    }
    .up-box {
      margin-top: 40rpx;
      margin-left: 60rpx;
      width: 630rpx;
      height: 240rpx;
      .lunbo-box {
        width: 193rpx;
        .lunbo {
          width: 192rpx;
          height: 192rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #fef8e2;
          border-radius: 8rpx;
          .tu {
            width: 180rpx;
            height: 180rpx;
          }
        }
        .wen {
          width: 200rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: center;
          font-size: 26rpx;
          font-weight: bold;
          color: #89371d;
        }
      }
    }
    .down-box {
      text-align: center;
      margin-top: 96rpx;
      position: relative;
      .cell-box {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        width: 590rpx;
        height: 730rpx;
        padding-left: 12rpx;
        overflow: auto;
        margin: auto;
        .cell {
          position: relative;
          width: 280rpx;
          height: 280rpx;
          background-color: #fff;
          border-radius: 10rpx;
          margin-top: 20rpx;
          .xuan {
            top: -14rpx;
            left: -14rpx;
            position: absolute;
            width: 60rpx;
            height: 60rpx;
          }
          .tupian {
            margin-top: 6rpx;
            width: 180rpx;
            height: 180rpx;
          }
          .kucun {
            position: absolute;
            right: 4rpx;
            width: 120rpx;
            height: 30rpx;
            line-height: 28rpx;
            text-align: center;
            font-size: 20rpx;
            font-family: PingFang SC;
            color: rgba(255, 255, 255, 1);
            background: rgba(0, 0, 0, 0.6);
            border-radius: 15rpx;
          }
          .ming {
            width: 260rpx;
            height: 60rpx;
            line-height: 60rpx;
            text-align: center;
            background: linear-gradient(to right, #eadcb3, #f7ebc6);
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #89371d;
            border-radius: 10rpx;
            margin: auto;
          }
        }
        .sel {
          border: 4rpx solid #57d83f;
        }
        .unsel {
          box-shadow: 0 0 4rpx 4rpx #bdac91;
        }
      }
      .anniu-box {
        position: relative;
        top: 20rpx;
        margin: auto;
        width: 201rpx;
        height: 77rpx;
        .wen {
          padding-top: 16rpx;
          font-size: 30rpx;
          font-family: PingFang SC;
          font-weight: 800;
          color: #953c22;
        }
        .anniu {
          position: absolute;
          z-index: -1;
          top: 5rpx;
          left: 0;
          width: 201rpx;
          height: 77rpx;
        }
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 738rpx;
      height: 1390rpx;
    }
  }
}
</style>
