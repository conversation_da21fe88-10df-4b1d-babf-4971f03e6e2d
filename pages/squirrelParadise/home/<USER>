<template>
	<view>
		<cu-custom :bgColor="'rgba(255, 255, 255, 0)'" :isBack="true" :hideMarchContent="true">
			<block slot="backText"></block>
			<block slot="content"></block>
		</cu-custom>
		<!-- 松鼠乐园主页配置 -->
		<view v-if="waterTemplate&&waterTemplate.templateComponent" :style="{position: 'fixed',width: '100%',top:0}">
			<!-- 种树 -->
			<plant-trees 
				v-if="waterTemplate.templateComponent.TemplateType==='1'" 
				:waterInfo="waterInfo" 
				:waterTree="waterTree" 
				:waterTemplate="waterTemplate"
				:waterUser="waterUser"
				:isScreenHeight="isScreenHeight"
				:needWaterNum="needWaterNum"
				:fullLevel="fullLevel"
				:schedule="schedule"
				:needWaterTotal="needWaterTotal"
				:plussNum="plussNum"
				:fullLevelPrize="fullLevelPrize"
				:isPrizeBoxShuow="isPrizeBoxShuow"
				:waterdom="waterdom"
				:watercss="watercss"
				:selectedPrize="selectedPrize"
				:prizeUsers="prizeUsers"
				:isShowWinner="isShowWinner"
				:treemove="treemove"
				:info="info"
				:stages="stages"
				:waterTreeImg="waterTreeImg"
				@openTaskDia="openTaskDia"
				@closePrize="closePrize"
				@rainFun="rainFun"
				@tree="tree"
				@openPrizeDialog="openPrizeDialog"
				@water="water"
				@openStrategyDia="openStrategyDia"
				@goMyPrize="goMyPrize"
				>
			</plant-trees>

			<!-- 堆雪人 -->
			<make-snowman 
				v-if="waterTemplate.templateComponent.TemplateType==='2'" 
				:waterInfo="waterInfo" 
				:waterTree="waterTree" 
				:waterTemplate="waterTemplate"
				:waterUser="waterUser"
				:isScreenHeight="isScreenHeight"
				:needWaterNum="needWaterNum"
				:fullLevel="fullLevel"
				:schedule="schedule"
				:needWaterTotal="needWaterTotal"
				:plussNum="plussNum"
				:fullLevelPrize="fullLevelPrize"
				:isPrizeBoxShuow="isPrizeBoxShuow"
				:waterdom="waterdom"
				:watercss="watercss"
				:selectedPrize="selectedPrize"
				:prizeUsers="prizeUsers"
				:isShowWinner="isShowWinner"
				:treemove="treemove"
				:info="info"
				:stages="stages"
				:waterTreeImg="waterTreeImg"
				@openTaskDia="openTaskDia"
				@closePrize="closePrize"
				@rainFun="rainFun"
				@tree="tree"
				@openPrizeDialog="openPrizeDialog"
				@water="water"
				@openStrategyDia="openStrategyDia"
				@goMyPrize="goMyPrize"
				>
			</make-snowman>

			<!-- 年兽 -->
			<nian-beast
				v-if="waterTemplate.templateComponent.TemplateType==='3'" 
				:waterInfo="waterInfo" 
				:waterTree="waterTree" 
				:waterTemplate="waterTemplate"
				:waterUser="waterUser"
				:isScreenHeight="isScreenHeight"
				:needWaterNum="needWaterNum"
				:fullLevel="fullLevel"
				:schedule="schedule"
				:needWaterTotal="needWaterTotal"
				:plussNum="plussNum"
				:fullLevelPrize="fullLevelPrize"
				:isPrizeBoxShuow="isPrizeBoxShuow"
				:waterdom="waterdom"
				:watercss="watercss"
				:nianBeastShow="nianBeastShow"
				:selectedPrize="selectedPrize"
				:prizeUsers="prizeUsers"
				:isShowWinner="isShowWinner"
				:treemove="treemove"
				:info="info"
				:stages="stages"
				:waterTreeImg="waterTreeImg"
				:redEnvelopePrizesAsync="redEnvelopePrizesAsync"
				@openTaskDia="openTaskDia"
				@closePrize="closePrize"
				@rainFun="rainFun"
				@tree="tree"
				@openPrizeDialog="openPrizeDialog"
				@water="water"
				@openStrategyDia="openStrategyDia"
				@goMyPrize="goMyPrize"
				>
			</nian-beast>
		</view>

		<!-- 欢迎页面 -->
		<view v-if="isSelectPrize===1&&waterInfo&&waterTemplate">
			<Welcome 
				:prizePics="prizePics" 
				:rule="waterInfo.detail" 
				:optionalPrizes="optionalPrizes" 
				:waterTemplate="waterTemplate"
				@close="close()"
			/>
		</view>

		<!-- 更换奖品 -->
		<view v-if="waterInfo&&waterInfo.id&&closeReplacePrize&&waterTemplate">
			<replace-prize 
			:water-info-id="waterInfo.id" 
			:waterTemplate="waterTemplate"
			@close="close()" />
		</view>

		<!-- 自动更换奖品 -->
		<view v-if="waterInfo&&waterInfo.id&&closeAutomaticReplacePrize&&waterTemplate">
			<automatic-replace-prize 
			:water-info-id="waterInfo.id" 
			:waterTemplate="waterTemplate"
			@close="close()" />
		</view>

		<!-- 种树阶段奖品 -->
		<view v-if="waterTemplate&&givePrizeShow&&finishNum&&(givePrize || drawPath)">
			<stage-level-prize 
				:waterInfoId="waterInfo.id" 
				:finishNum="finishNum" 
				:isDraw="isDraw" 
				:drawPath="drawPath" 
				:stageName="waterTree.name"
				:givePrize="givePrize"
				:waterTemplate="waterTemplate"
				@close="giveClose()" 
			/>
		</view>

		<!-- 任务列表 -->
		<block v-if="taskDiaShow&&waterTemplate">
			<task-list 
				:water-info-id="waterInfo.id" 
				@close="closeTaskDia" 
				:waterTemplate="waterTemplate"
			/>
		</block>

		<!-- 未参加活动线下扫码结果 -->
		<block v-if="3 == codeDiaState&&waterTemplate">
			<code-res 
				:give-water-num="scanCodeWaterNum" 
				:tomorrow-water-num="scanCodeTomorrowWaterNum"
				:waterTemplate="waterTemplate"
				@close="closeCodeDia" 
			/>
		</block>	

		<!-- 每日签到 -->
		<block v-if="dailyDiaShow&&waterTemplate">
			<daily-attendance 
				:task-id="taskId" 
				:waterTemplate="waterTemplate"
				@close="closeDailyDia" 
			/>
		</block>

		<!-- 邀请好友 -->
		<block v-if="inviteDiaShow&&waterTemplate">
			<invite-friends 
				:invite-data="inviteData"
				:waterTemplate="waterTemplate"
				@close="closeInviteDia"
			/>
		</block>

		<!-- 好友助力结果 -->
		<block v-if="assistanceShow&&waterTemplate">
			<assistance-result 
				:select-prize-state="selectPrizeState" 
				:help-state="helpState" 
				:waterTemplate="waterTemplate"
				@close="closeAssistanceDia" 
			/>
		</block>

		<!-- 品牌助力入口 -->
		<block v-if="brandShow&&waterTemplate">
			<brand-assistance-entry 
			:task-id="taskId" 
			:browseTime="brandBrowseTime"
			:waterTemplate="waterTemplate" 
			@close="closeBrandDia"
			/>
		</block>	

		<!-- 种树完成奖品盲盒 -->
		<view v-if="fullLevel===1&&fullLevelPrize&&waterTemplate">
			<blind-box 
				:fullLevelPrize="fullLevelPrize" 
				:waterInfo="waterInfo" 
				:changeModalDialog="isBlindBox" 
				:waterTemplate="waterTemplate"
				@hideModalDialog="hideBlindBoxDialog"/>
		</view>

		<!-- 种树完成奖品 -->
		<view v-if="fullLevel===1&&fullLevelPrize&&waterTemplate">
			<fullLevel-prize 
				:fullLevelPrize="fullLevelPrize" 
				:waterInfo="waterInfo" 
				:changeModalDialog="isPrizeDialog" 
				:waterTemplate="waterTemplate"
				@hideModalDialog="hidePrizeDialog"
			/>
		</view>

		<!-- 红包雨 -->
		<block v-if="redEnvelopeRainShow">
			<red-envelope-rain 
				:task-id="taskId" 
				:red-envelope-info="redEnvelopeInfo" 
				@close="closeRedEnvelopeRain" 
			/>
		</block>

		<!--  1 未开始 2: 进行中 3:已结束 -->
		<not-start 
			v-if="isStartActivity&&waterInfo&&waterInfo.status==1&&waterTemplate" 
			:waterTemplate="waterTemplate"
		/>

		<game-over 
			:water-info-id="waterInfo.id"
			:waterTemplate="waterTemplate"
			v-if="waterInfo&&waterInfo.status==3&&waterTemplate" 
		/>
	</view>
</template>

<script>
	/**
	 * <AUTHOR>
	 * @date 2023年9月6日
	 **/
	import util from "utils/util.js";
	import Welcome from "./components/Welcome";
	import ReplacePrize from "./components/ReplacePrize";
	import AutomaticReplacePrize from "./components/automaticReplacePrize";
	import StageLevelPrize from "./components/StageLevelPrize";
	import TaskList from "../task/components/TaskList";
	import CodeRes from "../task/components/CodeRes";
	import DailyAttendance from "../task/components/DailyAttendance";
	import InviteFriends from "../task/components/InviteFriends";
	import AssistanceResult from "../task/components/AssistanceResult";
	import BrandAssistanceEntry from "../task/components/BrandAssistanceEntry";
	import GameOver from "./components/GameOver";
	import NotStart from "./components/NotStart";
	import {
		waterTreeApi,
		waterApi,
		testApi
	} from '@/pages/squirrelParadise/api/squirrelParadise'
	const app = getApp();

	import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"
	// import Barrage from "./components/barrage";
	import FullLevelPrize from "./components/fullLevelPrize";
	import BlindBox from "./components/blindBox";
	import AutoScrollText from "./components/autoScrollText";
	import PlantTrees from "./components/plantTrees";
	import MakeSnowman from "./components/makeSnowman";
	import NianBeast from "./components/nianBeast";
	import RedEnvelopeRain from "../task/components/RedEnvelopeRain.vue";
	import { senTrack, getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'

	export default {
		mixins: [navigateUtil],
		components: {
			Welcome,
			ReplacePrize,
			AutomaticReplacePrize,
			StageLevelPrize,
			CodeRes,
			TaskList,
			DailyAttendance,
			InviteFriends,
			AssistanceResult,
			BrandAssistanceEntry,
			// Barrage,
			FullLevelPrize,
			BlindBox,
			GameOver,
			NotStart,
			AutoScrollText,
			PlantTrees,
			MakeSnowman,
			NianBeast,
			RedEnvelopeRain
		},
		data() {
			return {
				theme: app.globalData.theme, // 全局颜色变量
				info: {
					name: '沐枫', // 用户姓名
					sex: 2, // 用户姓别 1男， 2女
					votes: 8, // 水滴值 默认为8
					avatar: '../static/img/detail-bg.jpg' //用户头像
				},
				// rainArr: [28, 63, 5, 902], 	// 雨滴值 点击收取
				// rainArr: [], 	// 雨滴值 点击收取

				stages: 1, // 成长阶段 1(小树[默认])，中2(成长中树) ，大3(开花树)，大4(结果树)
				during: 100, // 阶段阈值 1、小树[100以下](during > votes ) ，2、中树[100及以上 并且小于1000](during <= votes && oldest > votes)
				oldest: 1000, // 阶段阈值 3、大树[1000及以上](oldest <= votes )
				plussNum: null, // 加值数量（默认1）

				pluss: false, // 水滴值+1动画开关
				movetree: true, // 树动画开关
				treemove: false, // 树大小动画类型开关
				wateroff: true, // 浇水动画开关
				watercss: false, // 水壶动画开关
				waterdom: false, // 水滴动画开关
				waterTreeImg: false, //树长大
				waterInfo: { //活动配置
					// bgImg:'../static/img/detail-bg2.jpg',
					// bgImg:'http://img.songlei.com/paradise/nian-detail-bg2.jpg',
				},
				waterTemplate:{
				},//游戏模版配置
				optionalPrizes: [ //可选活动奖品
				],
				prizePics: [ //奖品轮播图
				],
				prizeUsers: [ //中奖用户
				],
				waterUser: { //用户信息
					headImg:''
				},
				waterTree: { //树配置
				},
				selectedPrize: { //选定的奖品
				},
				isSelectPrize: 0, //是否需要选择礼品 0 否 1是
				isDraw: 0, //是否需要进行抽奖 0 否 1是
				isShowWinner: 0, //是否展示中奖用户信息 0 否 1是
				needWaterTotal: 20, //需多少水滴获取最终奖励
				needWaterNum: 20, //需多少水滴获取阶段奖励
				schedule: 0, //浇水进度条进度（0.00） 两位小数
				givePrize: [], //赠送奖品集合
				finishNum: 0, //已浇水滴数量
				fullLevelPrize: {}, //满级奖品列表
				drawPath: '', //抽奖跳转地址
				fullLevel: 0, //是否满级（0否1是）
				isNeedReplace: 0, //是否需要更换奖品(0否 1是)
				isGive: 0, //是否赠送（0否1是）
				upgrade: 0, //是否升级（0否1是）
				closeReplacePrize: false, //更换奖品组件控制
				closeAutomaticReplacePrize:false,//更换自动奖品组件控制
				isBlindBox: false, //是否是盲盒
				isPrizeDialog: false, //是否完成奖品弹框
				isPrizeBoxShuow: false, //是否显示奖品盒子
				isScreenHeight: false, //判断屏幕高度
				barrageLock: true, //弹幕锁
				taskDiaShow: '', // 任务列表弹框
				taskId: '',
				codeDiaState: 1, // 未参加活动线下扫码结果弹框 1一直不显示 2选完奖品前不显示 3显示
				scanCodeWaterNum: '', // 线下扫码明日赠送水滴数量
				scanCodeTomorrowWaterNum: '', // 线下扫码明日赠送水滴数量
				dailyDiaShow: false, // 日常签到弹框
				inviteDiaShow: false, // 邀请好友弹框
				inviteData: '', // 邀请好友配置
				assistanceShow: false, // 好友助力结果弹框
				selectPrizeState: '', // 是否需要选择礼品 0 否 1是
				helpState: '', // 帮别人助力的结果
				brandShow: false, // 品牌助力入口
				brandBrowseTime: '', // 品牌助力浏览时间
				id: '', //种树id
				sharer_user_code: '', //分享人的userCode
				inviteCode: '', //邀请函的要求id
				givePrizeShow:false,//控制阶段性奖励显示隐藏
				systemInfo:uni.getSystemInfoSync(),//获取设备信息
				isStartActivity:false,//是否开始活动
				shareDetermine:'',//分享方式的判断
				isWindowWidth:true,//只第一次展示
				nianBeastShow:true,//年兽显示隐藏
				redEnvelopeRainShow:false,//红包雨显示隐藏
				redEnvelopeInfo:{},//红包雨数据
				fristRedEnvelopeRain: false,//红包雨第一次进入
				redEnvelopePrizesAsync: null,//红包雨奖品
				enterTime: 0,  // 进入页面时间戳（毫秒）
				trackParams: {
					page_name: "松鼠乐园",
					page_level: '一级',
					activity_id: "",
					activity_name: "",
					activity_type_first: "营销活动",
					activity_type_second: "松鼠乐园",
				},
			};
		},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			// 接收扫普通链接二维码里面携带的参数
			this.fristRedEnvelopeRain = true
			if (options.q) {
				// 邀请函的需求
				const q = decodeURIComponent(options.q) // 获取到二维码原始链接内容
				if (q && q.length > 0 && q != 'undefined') {
					this.id = util.getUrlParam(q, 'id');
					this.inviteCode = util.getUrlParam(q, 'inviteCode');
				}
			} else if (options.scene) {
				// 分享海报小程序
				let scenes = decodeURIComponent(options.scene).split('&');
				this.id = scenes[0];
				this.shareUserId = scenes[1];
			} else if (options.id > 0) {
				// 线下扫码
				this.id = options.id;		
			} else if (options.shareId > 0) {
				// 分享直接邀请好友
				this.id = options.shareId;
				this.shareUserId = options.sharer_user_code;				
			}
		},

		onShow() {
			// 页面显示时记录进入时间（单位：毫秒）
    	this.enterTime = new Date().getTime();

			const systemInfo = this.systemInfo;
			const windowHeight = systemInfo.windowHeight;
			const windowWidth =  systemInfo.windowWidth;

			console.log("systemInfo",systemInfo,'this.isWindowWidth',this.isWindowWidth);

			// 判断窗口高度是否小于等于736  ||systemInfo.osName=='android' || windowWidth == 447

			if (windowHeight <= 736 || (windowWidth == 447&&this.isWindowWidth)) {
				// 执行相应的操作  
				console.log("窗口高度小于等于736");
				this.isScreenHeight = true
			} else {
				// 执行其他操作  
				console.log("窗口高度大于736");
				this.isScreenHeight = false
			}
			// 获取窗口高度  兼容双屏
			const windowResizeCallback = (res) => {
				console.log("res==>",res);
				console.log('变化后的窗口宽度=' + res.size.windowWidth)
				console.log('变化后的窗口高度=' + res.size.windowHeight)
				const windowHeight =  res.size.windowHeight;
				const windowWidth =  res.size.windowWidth;
				// 判断窗口高度是否小于等于736  ||systemInfo.osName=='android'
				if (windowHeight <= 736) {
					// 执行相应的操作  
					console.log("窗口高度小于等于736");
					if (windowWidth==250) {
							this.isScreenHeight = false
					}else{
						this.isScreenHeight = true
					}
				} else {
					// 执行其他操作  
					console.log("窗口高度大于736");
					if (windowWidth==447) {
						this.isScreenHeight = true
					}else{
						this.isScreenHeight = false
					}
				}
			}
			uni.onWindowResize(windowResizeCallback)
			app.initPage().then(async() => {
				await this.getWaterTree();
				this.isWindowWidth = false
				this.shareDetermine = '';
				if (this.waterInfo&&this.waterInfo.id) {
					senTrack("ActivityPageView", {
						...this.trackParams,
						forward_source: getCurrentTitle(1),
						activity_id: this.waterInfo.id,
						activity_name: this.waterInfo.name,
					});
				}
			});
		},

		onUnload() {
			const leaveTime = new Date().getTime()
			// 组件销毁前记录离开时间
			const durationSeconds = Math.floor((leaveTime - this.enterTime) / 1000);
			console.log("组件生命周期内停留时长（秒）：", durationSeconds);
			if (this.waterInfo&&this.waterInfo.id) {
				senTrack("ActivityPageLeave", {
					...this.trackParams,
					forward_source: getCurrentTitle(1),
					activity_id: this.waterInfo.id,
					activity_name: this.waterInfo.name,
					stay_duration: durationSeconds,
				});
			}
		},

		computed: {
			// 广告排序
			filteredItems() {
				if (this.waterInfo && this.waterInfo.advers) {
					// 按照 position 属性进行排序  
					return this.waterInfo.advers.sort((a, b) => a.position - b.position);
				}
			},
		},

		onShareAppMessage() {
			const userInfo = uni.getStorageSync("user_info");
			console.log("===onShareAppMessage====1111===",this.waterInfo,'this.shareDetermine',this.shareDetermine);
			let path = "/pages/squirrelParadise/home/<USER>";
			let userCode = userInfo ? "&sharer_user_code=" + userInfo.userCode : "";
			let parameter = '?shareId=' + this.waterInfo.id + userCode;
			let title = '';
			let imageUrl = '';
			// shareDetermine有值是按钮没是三个点
			if (this.shareDetermine) {
				path = path + parameter;
				title = this.inviteData.detail;
				imageUrl = this.inviteData.taskImg;
			}else{
				title = this.waterInfo.name;
				imageUrl = this.waterInfo.shareImg;
			}
			senTrack("ActivityPageShareOrCollect", {
				...this.trackParams,
				activity_id: this.waterInfo.id,
				activity_name: this.waterInfo.name,
				action_type: "分享",
			});
			return {
				title,
				path,
				imageUrl,
			};
		},

		methods: {
			//测试调用会让浇水数量变为100 已浇数量变为0
			async testApi() {
				if (this.waterInfo.id) {
					await testApi(this.waterInfo.id)
				}
			},

			// 种树阶段奖品关闭后
			// fullLevel 判断是否满级（0否1是）是满级获取满级奖品信息 满级弹框
			giveClose() {
				if (this.fullLevel === 1) {
					this.openPrizeDialog()
				}
				this.givePrizeShow = false;
			},

			//打开最终奖品弹框
			//1.校验奖品类型 prizeType 1:优惠券 2:盲盒 3:赠品 4:线下券
			// a.如果是商品、劵直接打开奖品弹框
			// b.如果是盲盒打开盲盒弹框----》拆盲盒----》查看
			openPrizeDialog() {
				if (this.fullLevelPrize.prizeType == 2) {
					this.isBlindBox = true;
					this.isPrizeDialog = false;
				} else {
					this.isBlindBox = false;
					this.isPrizeDialog = true;
				}
			},

			//关闭盲盒弹框
			hideBlindBoxDialog(bool) {
				this.isBlindBox = bool;
				this.isPrizeBoxShuow = true;
				this.getWaterTree()
			},

			//关闭完成奖品弹框
			hidePrizeDialog(bool) {
				this.isPrizeDialog = bool;
				this.isPrizeBoxShuow = true;
			},

			//更换奖品
			closePrize() {
				if (this.fullLevel !== 1) {
					this.closeReplacePrize = true;
				} else {
					uni.showToast({
						title: "已完成游戏活动，不能更换奖品哦~",
						icon: "none",
					});
				}
			},

			//关闭更换奖品弹框
			close() {
				senTrack("ActivityClick", {
					...this.trackParams,
					activity_id: this.waterInfo.id,
					activity_name: this.waterInfo.name,
				});
				this.closeReplacePrize = false;
				this.closeAutomaticReplacePrize = false;
				if(2 == this.codeDiaState){
					this.codeDiaState = 3;
				}
				this.getWaterTree()
			},

			// 处理中奖用户数据
			processArray(arr) {
				this.prizeUsers = arr.map(item => {
					return {
						blessContent: `${item.nickname} 完成游戏兑换 ${item.prizeName} 一件`
					}
				});
			},

			//浇水接口 
			//0.优先判断是否需要更换奖品(0否 1是) 是就弹框提示更换
			//1、upgrade先判断是否升级（0否1是）是升级获取下阶段树信息waterTree
			//2、fullLevel再判断是否满级（0否1是）是满级获取满级奖品信息
			//3、isDraw是否抽奖（0否1是）是使用drawPath跳转抽奖地址
			//4、isGive是否赠送（0否1是）是使用弹框展示赠送奖品集合givePrize
			//5、更新水滴数据、进度条信息
			//6、刷新页面
			async updateWater() {
				try {
					if (this.waterInfo) {
						const res = await waterApi(this.waterInfo.id)
						let {
							data
						} = {
							...res
						}
						console.log("this.updateWater==>", res, data);
						this.fullLevelPrize = data.fullLevelPrize
						// this.needWaterNum = data.needWaterNum;
						this.drawPath = data.drawPath;
						this.waterUser.upgrade = data.upgrade;
						this.fullLevel = data.fullLevel;
						this.isDraw = data.isDraw;
						this.isNeedReplace = data.isNeedReplace
						// 优先判断是否需要更换奖品(0否 1是) 是就弹框提示更换
						if (this.isNeedReplace !==0) {
							this.closeAutomaticReplacePrize = true
						}
						this.waterUser.waterNum = data.surplusNum;
						this.plussNum = data.wateredNum;
						this.schedule = data.schedule;
						// upgrade是否升级（0否1是）
						if (data.waterTree&&data.upgrade===1) {
							this.waterTree = data.waterTree;
							this.waterTreeImg = true;
						}

						this.barrageLock = false;
						this.getWaterTree()
						setTimeout(() => {
							this.waterTreeImg = false;
							// 赠送奖品集合
							this.isGive = data.isGive;
							this.givePrize = data.givePrize || [];
							if (this.isGive=='1'|| this.isDraw== '1') {
								this.givePrizeShow = true;
							}
							this.finishNum = data.finishNum;
						}, 3000);
					}
				} catch (error) {
					console.log("error", error);
				}
			},

			//控制 wateroff 是否浇水 true开false关 ，
			// isSelectPrize 是否需要选择礼品 0 否 1是
			// fullLevel 是否满级（0否1是）
			control() {
				// 用户水滴为零 禁止浇水动画 //需要选择礼品禁止
				if (this.isSelectPrize !== 1 || (this.waterUser && this.waterUser.waterNum == 0) || this.fullLevel !== 0)
					this.wateroff = false
			},

			//获取活动配置
			async getWaterTree() {
				try {
					const params = {};
					// this.closeAutomaticReplacePrize = false;
					if (this.id > 0) {
						params.id = this.id;
					}
					if (this.inviteCode) {
						params.inviteCode = this.inviteCode
					}
					if (this.shareUserId) {
						params.shareUserId = this.shareUserId
						this.shareUserId = '' // 不清空别人那会一直弹出好友助力结果框
					}
					const res = await waterTreeApi(params);
					if (res.data) {
						let {
							data
						} = {
							...res
						}
						this.waterInfo = data.waterInfo;

						this.fullLevelPrize = data.fullLeavePrize
						if(data.waterTemplate){
							this.waterTemplate = data.waterTemplate
							uni.setStorageSync('waterTemplate', this.waterTemplate);
						}
						if (data.waterTree) {
							this.waterTree = data.waterTree;
							this.fullLevel = data.waterTree.fullLevel;
							this.stages = data.waterTree.growthStage;
						}
						// this.isPrizeBoxShuow  = true
						if (data.waterUser) {
							this.waterUser = data.waterUser;
						}
						if (1 == data.isHelpBox) {
							// 邀请好友时，好友助力结果
							this.helpState = data.isHelp;
							this.selectPrizeState = data.isSelectPrize; // 确保先弹出好友助力结果，再弹出选择奖品
							this.assistanceShow = true;
						} else {
							this.isSelectPrize = data.isSelectPrize;
							this.assistanceShow = false;
						}
						if (1 == data.isScanCode) {
							// 是否完成扫码任务 0否不弹出 1是弹出
							this.scanCodeWaterNum = data.scanCodeWaterNum;
							this.scanCodeTomorrowWaterNum = data.scanCodeTomorrowWaterNum;
							this.codeDiaState = 1 == data.isSelectPrize ? 2 : 3;
						} else {
							// 1一直不显示 2选完奖品前不显示 3显示
							this.codeDiaState = 1;
						}						
						this.isShowWinner = data.isShowWinner;
						this.needWaterNum = data.needWaterNum;
						this.needWaterTotal = data.needWaterTotal;
						// 欢迎页最终奖品选择
						this.optionalPrizes = data.optionalPrizes;

						this.prizePics = data.prizePics;
						if (data.isSelectPrize !== 1 && data.prizeUsers && this.barrageLock) {
							this.processArray(data.prizeUsers)
						}
	
						this.selectedPrize = data.selectedPrize;
						setTimeout(() => {
							this.schedule = data.schedule;
							console.log("this.schedule==111==>", this.schedule);
						}, 3000);
	
						if (this.fullLevel === 1 && this.fullLevelPrize) {
							setTimeout(() => {
								this.isPrizeBoxShuow = true
							}, 3500);
						}

						// 红包雨
						if (data.isSelectPrize !== 1 && this.fristRedEnvelopeRain && data.redPacket) {
							this.fristRedEnvelopeRain = false;
							this.redEnvelopeRainShow = true;
						}
						this.redEnvelopeInfo = data.redPacket || null;

						//注意此方法只在测试调试使用，调用一次清空记录，给500滴水，
						//然后注释调用才能走正常流程
						
						// this.testApi()

					}else{
						console.log("活动 未开始===》");
						this.isStartActivity = true;
						this.waterInfo.status=1
						// let that = this
						// uni.showModal({
            //   title: '提示',
            //   content:res.msg ||'网络连接错误,请重试!',
            //   showCancel: false,
            //   success() {
						// 		//有上一级返回上一级，如果是扫码就跳转首页
            //     const pages = getCurrentPages()
            //     const pageUrl = pages[pages.length - 2]; // 上一个页面
						// 		console.log("pageUrl====》",pageUrl);
						// 		if (pageUrl) {
						// 			uni.navigateBack({
						// 				delta: 1
						// 			});
						// 		}else{
						// 			uni.switchTab({
						// 				url: '/pages/home/<USER>'
						// 			});
						// 		}
            //   }
            // })
					}

				} catch (error) {
					console.log("error", error);
				}
			},

			// 设置树的大小，恢复动画
			setTree(time = 4000) {
				setTimeout(() => {
					// this.plussNum = 1;
					this.pluss = false;
					this.wateroff = true;
					this.watercss = false;
					this.treemove = false;
				}, time);
				//年兽炮竹云开始时间
				if (this.waterTemplate.templateComponent.TemplateType==='3') {
					this.$nextTick(()=>{
						this.waterdom = true;
						setTimeout(() => {
							this.nianBeastShow=false
						}, 1500)
					})
				}
			},

			// // 点击用户头像放大
			// zoom(o) {
			// 	uni.previewImage({
			// 		'urls': [o]
			// 	});
			// },

			// 收取雨滴的动画
			rainFun(i, o) {
				this.plussNum = o;
				this.info.votes = Number(this.info.votes) + (o - 0);
				this.pluss = true;
				this.treemove = true;
				setTimeout(() => {
					this.rainArr.splice(i, 1);
				}, 1000);
				this.setTree(2000);
			},

			// 点击树的动画
			tree() {
				if (this.movetree) {
					this.treemove = true;
					this.movetree = false;
					setTimeout(() => {
						this.movetree = true;
						this.treemove = false;
					}, 1000);
				};
			},

			// 水壶浇水动画
			water() {
				senTrack("ActivityClick", {
					...this.trackParams,
					activity_id: this.waterInfo.id,
					activity_name: this.waterInfo.name,
				});
				
				if (this.fullLevel !== 0) {
					uni.showToast({
						title: "宝箱已经打开啦，您已完成当前游戏！",
						icon: "none",
						duration: 2000,
					});
					return
				}
				
				if (this.waterUser && this.waterUser.waterNum == 0) {
					uni.showToast({
						title: `${this.waterTemplate.tip2}不足，完成任务可获得更多${this.waterTemplate.tip2}！`,
						icon: "none",
						duration: 2000,
					});
					return
				}

				if (this.wateroff) {
					this.watercss = true;
					this.wateroff = false;
					// 种树及堆雪人用的时间
					if (this.waterTemplate.templateComponent.TemplateType!=='3') {
						setTimeout(() => {
							this.waterdom = true;
						}, 1500);
					}

					//年兽炮竹消失时间
					if (this.waterTemplate.templateComponent.TemplateType==='3') {
						this.setTree(1000);
					}

					setTimeout(() => {
						// this.info.votes++;
						this.updateWater();
						this.pluss = true;
						this.treemove = true;
						this.movetree = false;
					}, 2000);

					setTimeout(() => {
						// 种树及堆雪人用的时间
						this.waterdom = false;
						this.movetree = true;
						this.nianBeastShow = true;
					}, 3500);

					// 种树及堆雪人用的时间
					if (this.waterTemplate.templateComponent.TemplateType!=='3') {
						this.setTree();
					}
				};
			},

			// 打开任务列表
			openTaskDia() {
				this.taskDiaShow = true;
			},

			// 关闭任务列表
			closeTaskDia(info) {
				this.taskDiaShow = false;
				if (info) {
					switch (info.taskType) {
						case "0":
							this.taskId = info.id;
							this.dailyDiaShow = true;
							break;
						case "2":
							this.inviteData = info;
							this.inviteDiaShow = true;
							break;
						case "4":
							this.taskId = info.id;
							this.brandBrowseTime = info.browseTime;
							this.brandShow = true;
							break;
						case "6":
							this.taskId = info.id;
							// this.brandBrowseTime = info.browseTime;
							this.redEnvelopeRainShow = true;
							break;
						default:
							break;
					};
				}
			},

			// 关闭未参加活动线下扫码结果
			closeCodeDia() {
				this.codeDiaState = 1;
			},

			// 关闭日常签到
			closeDailyDia() {
				this.dailyDiaShow = false;
				this.getWaterTree();
			},

			// 关闭邀请好友
			closeInviteDia(flag,parameter) {
				this.inviteDiaShow = false;
				this.shareDetermine = parameter
				if (flag) {
					this.getWaterTree();
				}
			},

			// 关闭好友助力结果
			closeAssistanceDia(val) {
				this.assistanceShow = false;
				this.isSelectPrize = val;
			},

			// 关闭品牌助力入口
			closeBrandDia() {
				this.brandShow = false;
			},

			// 打开查看攻略
			openStrategyDia() {
				uni.navigateTo({
					url: this.waterInfo.methodUrl,
				});
			},

			// 我的奖品
			goMyPrize() {
				uni.navigateTo({
					url: `/pages/myPrize/index?waterInfoId=${this.waterInfo.id}`,
				});
			},

			// 关闭红包雨
			closeRedEnvelopeRain(async) {
				this.redEnvelopeRainShow = false;
				this.redEnvelopePrizesAsync = async;
				this.barrageLock = false;
				this.getWaterTree();
				setTimeout(() => {
					this.redEnvelopePrizesAsync = false;
				},1500)
			},
		}
	};
</script>
<style>
/* 引入动画库 */
@import '../static/animate/animate.css';
</style>