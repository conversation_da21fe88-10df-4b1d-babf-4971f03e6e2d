<template>
	<view class="GameOver-page">
		<uni-popup ref="popup" :isMaskClick="showMask" mask-background-color="rgba(0,0,0,0.8)">
			<view class="core-box">
				<view style="padding-top: 160rpx;">
					<image class="notstart" :src="waterTemplate.templateComponent.notstartUrl" />
					<view class="one-box">游戏还未开始呢~!</view>
					<view class="one-box">不要着急</view>
					<view class="three-box" v-if="showHome">
						<view class="btn" @click="goHome">返回首页</view>
					</view>
				</view>
				<image class="bg" :src="waterTemplate.templateComponent.assistanceUrl" />
			</view>
		</uni-popup>
	</view>
</template>

<script>
	/**
	 * <AUTHOR>
	 * @date 2023年09月27日
	 **/

	export default {
		props: {
			showMask: {
				type: Boolean,
				require: false,
				default: true,
			},
			showHome: {
				type: Boolean,
				require: false,
				default: true,
			},
			//游戏模版配置
			waterTemplate: {
				type: Object,
				require: true,
				default() {
					return {};
				},
			},
		},
		data() {
			return {
				iconPic: {
				},
			};
		},
		mounted() {
			this.$refs.popup.open("center");
		},
		methods: {
			goHome() {
				uni.reLaunch({
					url: '/pages/home/<USER>'
				});
			},
		},
	};
</script>
<style scoped lang="scss">
	.GameOver-page {
		.core-box {
			position: relative;
			width: 723rpx;
			height: 768rpx;
			text-align: center;

			.one-box {
				text-align: center;
				font-size: 30rpx;
				font-weight: 500;
				color: #9f4227;
				top:80rpx
			}

			.notstart {
				position: absolute;
				width: 525rpx;
				left: 50%;
				transform: translateX(-50%);
				top:200rpx;
			}

			.three-box {
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				bottom: 100rpx;
				.btn {
					width: 236rpx;
					height: 74rpx;
					line-height: 74rpx;
					background-size: 100% 100%;
					font-size: 30rpx;
					font-weight: 800;
					color: #953C22;
					background-image: url(http://img.songlei.com/paradise/task/huang.png);
				}
			}

			.bg {
				z-index: -10;
				position: absolute;
				top: 0;
				right: 0;
				width: 723rpx;
				height: 768rpx;
			}
		}
	}
</style>