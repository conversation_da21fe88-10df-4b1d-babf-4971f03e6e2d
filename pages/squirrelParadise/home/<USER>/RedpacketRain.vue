<template>
  <view class="RedpacketRain-page">
    <view v-show="isActivity" class="rain-wrap">
      <view
        class="r-content"
        :style="{
          top: `${rcTop}rpx`,
          left: `${rcLeft}rpx`,
          transition: `all ${activityData.totalTime}s linear`,
        }"
      >
        <img
          v-for="packet in redPackets"
          :key="packet.id"
          :src="iconPic.packet"
          class="r-packet"
          :class="{ sPacket: packet.small }"
          :style="{ left: `${packet.x}` + 'rpx', top: `${packet.y}` + 'rpx' }"
          @click="cClick"
        />
      </view>
    </view>
  </view>
</template>
<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/

export default {
  data() {
    return {
      speed: 2,
      isActivity: false,
      redPackets: [],
      pId: 0,
      rcTop: 0,
      rcLeft: 0,
      activityData: {
        totalTime: 4, // 活动持续时间 秒
      },
      iconPic: {
        packet: "http://img.songlei.com/paradise/home/<USER>",
      },
    };
  },
  mounted() {
    this.start();
  },
  methods: {
    // 活动开始
    async start() {
      this.isActivity = true;
      let sY = 0;
      for (let i = 0; i < this.activityData.totalTime * this.speed; i++) {
        // 每个水平位置上放置两个红包
        const rowNum = 2;
        for (let i = 0; i < rowNum; i++) {
          const section = 750 / rowNum;
          let base = i * section;
          this.redPackets.push({
            x: base + Math.random() * section,
            y: sY + Math.random() * 70, // 垂直距离上70的距离随机分布
            small: Math.random() > 0.5, // 随机生成小型红包
            id: this.pId++,
          });
        }
        // 每行红包垂直间距200
        sY += 200;
      }
      // 红包雨开始的位置
      this.rcTop = 0 - sY;
      this.rcLeft = sY * 0.268; // sY * tan(15)
      setTimeout(() => {
        // 红包雨结束时的位置
        this.rcTop = 0;
        this.rcLeft = 100;
      }, 100);

      setTimeout(async () => {
        // 红包雨结束
        this.isActivity = false;
        this.redPackets = [];
        this.pId = 0;
        this.$emit("end");
      }, this.activityData.totalTime * 1000);
    },
    cClick() {
      this.isActivity = false;
      this.redPackets = [];
      this.pId = 0;
      this.$emit("end");
    },
  },
};
</script>
<style scoped lang="scss">
.RedpacketRain-page {
  position: fixed;
  top: 0;
  z-index: 999;
  width: 100%;
  min-height: 100vh;
  background-size: 100% 100%;

  .rain-wrap {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.6);
  }

  .r-content {
    position: relative;
    top: -400px;
    transform: rotate(15deg);
  }

  .r-packet {
    position: absolute;
    width: 112rpx;
    height: 224rpx;
  }

  .sPacket {
    width: 72rpx;
    height: 144rpx;
  }
}
</style>
