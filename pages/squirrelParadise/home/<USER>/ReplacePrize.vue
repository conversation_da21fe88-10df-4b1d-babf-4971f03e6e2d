<template>
  <view class="ReplacePrize-page">
    <uni-popup
      ref="popup"
      @maskClick="close"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box">
        <image class="fh" :src="waterTemplate.templateComponent.replaceGoodBackimg" @click="close" />
        <view class="cell-box">
          <view
            class="cell"
            :class="u.id == prizeId ? 'sel' : 'unsel'"
            v-for="u in optionalPrizes"
            :key="u.id"
            @click="optionalChange(u)"
          >
            <image class="xuan" :src="iconPic.xuan" v-if="u.id == prizeId" />
            <image class="xuan" :src="iconPic.buxuan" v-else />
            <image class="tupian" mode="aspectFit" :src="u.url" />
            <view
              class="kucun"
              :style="{ top: u.id == prizeId ? '6rpx' : '8rpx' }"
            >
              <text v-if="u.prizeNum > 0"> 库存:{{ u.prizeNum }}</text>
              <text v-else>已抢光</text>
            </view>
            <view class="ming">{{ u.name }}</view>
          </view>
        </view>
        <view 
          class="absolute" 
          :style="{
            bottom: '0rpx',
            'z-index': '1',
            width: '670rpx',
            height: '123rpx',
            left: '54%',
            'background-image': `url(${iconPic.btnBg})`,
            'background-size': 'cover',
            transform: 'translate(-50%)'}"
        >
          <view class="anniu-box" @click="selectPrize">
            <view class="wen">确认更换</view>
            <image class="anniu" :src="waterTemplate.templateComponent.selectGoodsBtnimg" />
          </view>
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.replaceGoodsimg" @click="selectPrize" />
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import { viewPrizeApi, selectPrizeApi } from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  props: {
    waterInfoId: {
      type: String,
      require: true,
      default: "",
    },
        //游戏模版配置
    waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      id: "",
      prizeId: "",
      optionalPrizes: [],
      iconPic: {
        buxuan: "http://img.songlei.com/paradise/home/<USER>",
        xuan: "http://img.songlei.com/paradise/home/<USER>",
        huang: "http://img.songlei.com/paradise/home/<USER>",
        btnBg: "http://img.songlei.com/paradise/btn-bg.png",
      },
      prizeNum:0,
    };
  },
  mounted() {
    this.viewPrize();
    this.$refs.popup.open("center");
  },
  methods: {
    viewPrize() {
      const params = {
        id: this.waterInfoId,
      };
      viewPrizeApi(params).then((res) => {
        this.optionalPrizes = res.data.waterPrizes;
        this.prizeNum = this.optionalPrizes[0].prizeNum;
        let obj = this.optionalPrizes.find((v) => {
          return 1 == v.isSelect;
        });
        this.id = obj.waterInfoId;
        this.prizeId = obj.id;
      });
    },
    optionalChange(info) {
      this.prizeNum = info.prizeNum;
      if (info.prizeNum > 0) {
        this.id = info.waterInfoId;
        this.prizeId = info.id;
      }else{
        uni.showToast({
          title: "已抢光了!，请重新选择礼品哦~",
          icon: "none",
        });
      }
    },
    selectPrize() {
      if (this.prizeNum<= 0 ) {
        uni.showToast({
          title: "已抢光了!，请重新选择礼品哦~",
          icon: "none",
        });
        return
      }
      if (this.prizeId) {
        const params = {
          id: this.id,
          prizeId: this.prizeId,
        };
        selectPrizeApi(params).then((res) => {
          this.close();
        });
      } else {
        uni.showToast({
          title: "请先选择奖品哦~",
          icon: "none",
        });
      }
    },
    close() {
      this.$refs.popup.close();
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.ReplacePrize-page {
  .core-box {
    position: relative;
    width: 750rpx;
    height: 86vh;
    text-align: center;
    padding-top: 365rpx;
    .fh {
      position: absolute;
      top: 45rpx;
      left: 40rpx;
      width: 188rpx;
      height: 85rpx;
    }
    .cell-box {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      width: 590rpx;
      height: 770rpx;
      padding-left: 12rpx;
      overflow: auto;
      margin: auto;
      .cell {
        position: relative;
        width: 280rpx;
        height: 280rpx;
        background-color: #fff;
        border-radius: 10rpx;
        margin-top: 20rpx;
        .xuan {
          top: -14rpx;
          left: -14rpx;
          position: absolute;
          width: 60rpx;
          height: 60rpx;
        }
        .tupian {
          margin-top: 6rpx;
          width: 180rpx;
          height: 180rpx;
        }
        .kucun {
          position: absolute;
          right: 4rpx;
          width: 120rpx;
          height: 30rpx;
          line-height: 28rpx;
          text-align: center;
          font-size: 20rpx;
          font-family: PingFang SC;
          color: rgba(255, 255, 255, 1);
          background: rgba(0, 0, 0, 0.6);
          border-radius: 15rpx;
        }
        .ming {
          width: 260rpx;
          height: 60rpx;
          line-height: 60rpx;
          text-align: center;
          background: linear-gradient(to right, #eadcb3, #f7ebc6);
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: bold;
          color: #89371d;
          border-radius: 10rpx;
          margin: auto;
        }
      }
      .sel {
        border: 4rpx solid #57d83f;
      }
      .unsel {
        box-shadow: 0 0 4rpx 4rpx #bdac91;
      }
    }
    .anniu-box {
      position: relative;
      top: 20rpx;
      margin: auto;
      width: 201rpx;
      height: 77rpx;
      .wen {
        padding-top: 16rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #953c22;
      }
      .anniu {
        position: absolute;
        z-index: -1;
        top: 5rpx;
        left: 0;
        width: 201rpx;
        height: 77rpx;
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 120rpx;
      right: 0;
      width: 733rpx;
      height: 1063rpx;
    }
  }
}
</style>
