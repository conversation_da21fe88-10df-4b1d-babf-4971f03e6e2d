<template>
  <view>
    <view 
		class="cu-modal" 
		:class="changeModalDialog?'show':''" 
    @tap="hideModalDialog" 
		>
			<view 
				class="cu-dialog" 
				:class="changeModalDialog?'animation-slide-center':''" 
        style="width: 657rpx; border-radius: 45rpx;"
				@tap.stop
			>
      <view 
        v-if="fullLevelPrize"
        style="width: 657upx;margin:0 auto;margin-top: 38rpx;position: relative;"
        :style="{
          height:fullLevelPrize.prizeStartTime?'929rpx':'747rpx',
        }"
        >
        <!--赠品、盲盒类型展示  prizeType 1:优惠券 2:盲盒 3:赠品 4:线下券-->
          <image
            style="width:100%;height: 100%;border-radius:15rpx;"
            :src="fullLevelPrize.prizeStartTime? waterTemplate.templateComponent.fullLevelPrizeMiddleUrl : waterTemplate.templateComponent.fullLevelPrizeSmallUrl" 
            mode="aspectFill" 
            >
          </image>
          
          <view 
            style="position: absolute;left: 0;right: 0;"
            :style="{
              top: fullLevelPrize.prizeType=='3'?(fullLevelPrize.prizeStartTime?'294rpx':'278rpx'):'278rpx',
            }"
          >
            <view class="flex justify-center">
              <view style="width: 247upx; height: 247upx">
                <image
                  style="width:100%;height: 100%;border-radius:15rpx;"
                  :src="fullLevelPrize.url" 
                  mode="aspectFill" 
                  >
                </image>
              </view>
    
              <view style="width: 305rpx;">
                <view 
                :style="{
                  height: fullLevelPrize.prizeType=='3'?'182rpx':''
                }" 
                class="text-left padding-left-xs text-bold">
                  {{fullLevelPrize.name}}
                </view>

                <!--赠品、盲盒类型展示  prizeType 1:优惠券 2:盲盒 3:赠品 4:线下券-->
                <view class="text-left padding-left-xs text-bold text-red">
                  请在兑奖期限内之完成自提，过期自动作废!
                </view>

                <!--
                  <image
                    v-if="fullLevelPrize.prizeType=='3'"
                    class="margin-left-xs"
                    style="width: 210upx;height: 27upx;display: block;"
                    :src="'https://img.songlei.com/paradise/prize-text.png'" 
                    mode="aspectFill" 
                    >
                  </image>
                  -->

                <view v-if="fullLevelPrize.prizeType!='3'" class="padding-left-xs margin-top-xs text-left text-bold">
                  <view class="text-xs">券有效期：
                  </view>
                  <view class="text-xs">
                    {{fullLevelPrize.couponStartDate}}-
                  </view>
                  <view class="text-xs">
                    {{fullLevelPrize.couponEndDate}}
                  </view>
                </view>

                <view v-if="fullLevelPrize.prizeType!='3'" class="flex justify-between margin-top-xs padding-left-xs text-bold">
                  <text class="text-xs">
                    使用范围：{{fullLevelPrize.suitType}}
                  </text>
                </view>
              </view>
            </view>
    
            <view 
            v-if="fullLevelPrize.prizeType!='1'&&fullLevelPrize.prizeStartTime" 
            style="width: 553rpx;margin: 0 auto;"
            :class="fullLevelPrize.detail?'two-line-text':''"
            >
              <view class=" flex justify-between margin-top-xs ">
                <text class="text-xs">兑奖期限：{{fullLevelPrize.prizeStartTime}}-{{fullLevelPrize.prizeEndTime}}</text>
              </view>

              <view class="flex justify-between ">
                <text class="text-xs">兑奖时段：{{fullLevelPrize.prizeTime}}</text>
              </view>

              <view class=" flex justify-between ">
                <text class="text-xs">兑奖地点：{{fullLevelPrize.prizePlace}}</text>
              </view>

              <view class=" flex justify-between ">
                <text class="text-xs">兑奖联系人：{{fullLevelPrize.prizePeople}}</text>
              </view>

              <view class=" flex justify-between ">
                <text class="text-xs">联系电话：{{fullLevelPrize.prizePhone}}</text>
              </view>

              <view class=" flex justify-between ">
                <text class="text-xs">兑奖须知：{{fullLevelPrize.detail}}</text>
              </view>
            </view>

            <view v-else class=""> 该商品需要邮寄，请完善收货地址信息 </view>
    
            <view class="flex justify-center" 
            :class="fullLevelPrize.prizeType!='3'?'margin-top-lg':''"
            >
              <view @click="goMyPrize">
                <image
                  style="width: 233upx;height: 82upx;"
                  :src="'https://img.songlei.com/paradise/my-prize.png'" 
                  mode="aspectFill" 
                  >
                </image>
              </view>

              <view v-if="fullLevelPrize.prizeType=='3'">
                <view v-if="fullLevelPrize.prizeStartTime&&fullLevelPrize.prizeTime" class="margin-left" @click="hideModalDialog()">
                  <image
                    style="width: 233upx;height: 82upx;"
                    :src="'https://img.songlei.com/paradise/close.png'" 
                    mode="aspectFill" 
                    >
                  </image>
                </view>
  
                <view v-else style="position: relative;" class="margin-left-sm" @click="toUse(fullLevelPrize)">
                  <view 
                    class="text-bold" 
                    style="position: absolute;z-index: 1;top: 40%;left: 50%;transform: translate(-50%, -50%);color: #F8DD68;">
                  去领取
                  </view>
                  <image
                    style="width: 239upx;height: 77upx;"
                    :src="'https://img.songlei.com/paradise/gbbtn.png'" 
                    mode="aspectFill" 
                    >
                  </image>
                </view>
              </view>

              <view v-else style="position: relative;" class="margin-left-sm" @click="toUse(fullLevelPrize)">
                <image
                  style="width: 239upx;height: 77upx;"
                  :src="'https://img.songlei.com/paradise/nian-gbbtn.png'" 
                  mode="aspectFill" 
                  >
                </image>
              </view>
            </view>
          </view>
        </view>
			</view>

      <block v-if="writeShow">
        <write-off :coupon-no="prizeUserId" @close="closeWriteDia" />
      </block>
		</view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月18日
 **/

import { navigateUtil } from "@/static/mixins/navigateUtil.js";
import WriteOff from "./WriteOff";
import { senTrack, getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'

export default {
  mixins: [navigateUtil],
  components: {
    WriteOff,
  },

  props: {
    // 奖品数据
    fullLevelPrize: {
      type: Object,
      require: true,
      default() {
        return {
        };
      },
    },

    // 控制弹框
    changeModalDialog: {
      type: Boolean,
      require: true,
      default:false,
    },

    //游戏模版配置
    waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},

    //活动配置
		waterInfo: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      writeShow: false,
      prizeUserId: "",
      iconPic: {
      },
      // 神策埋点
      trackParams: {
        page_name: "松鼠乐园",
        forward_source: getCurrentTitle(0),
        page_level: '一级',
        activity_id: "",
        activity_name: "",
        is_obtain_prize: true,
        activity_type_first: "营销活动",
        activity_type_second: "松鼠乐园",
      },
    };
  },

  mounted() {
  },

  methods: {
    //关闭弹框
		hideModalDialog() {
      this.$emit('hideModalDialog',false)
		},

    // 我的奖品
    goMyPrize() {
      uni.navigateTo({
        url: `/pages/myPrize/index?waterInfoId=${this.fullLevelPrize.waterInfoId}`,
      });
    },

    //劵，去使用
    toUse(prize){
      // prizeType 1:优惠券 2:盲盒 3:赠品 4:线下券
      const { prizeType, prizeValue, waterInfoId, couponNo, id, name} = prize
      let prizeId = "";
      switch (prizeType) {
        case '1':
          prizeId = prizeValue || "";
          break;
        case '3':
          prizeId = waterInfoId || "";
          break;
        case '4':
          prizeId = couponNo || "";
          this.prizeUserId = couponNo;
          this.writeShow = true;
          break;
        default:
          prizeId = id || "";
      }

      senTrack("ActivityResult", {
        ...this.trackParams,
        activity_id: waterInfoId,
        activity_name: this.waterInfo.name,
        prize_name: name || "",
        prize_id: prizeId,
      });

      // 再执行跳转
      switch (prizeType) {
        case '1':
          uni.navigateTo({
            url: "/pages/goods/goods-list/index?couponId=" + prizeValue,
          });
          break;
        case '3':
          uni.navigateTo({
            url: `/pages/order/order-confirm/index?prizeId=${waterInfoId}`,
          });
          break;
        // case '4' 不跳转
        default:
          break;
      }
    },

    closeWriteDia() {
      this.writeShow = false;
    },
  },
};
</script>
<style scoped lang="scss">
</style>
