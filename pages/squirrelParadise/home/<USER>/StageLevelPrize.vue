<template>
  <view class="StageLevelPrize-page">
    <!-- 红包雨开始 -->
    <uni-popup ref="startPopup" :isMaskClick="false">
      <view class="kaishi-box">
        <view class="shu">{{ finishNum }}</view>
        <image class="bg" :src="waterTemplate.templateComponent.prizePromptUrl" />
      </view>
    </uni-popup>
    <!-- 红包雨 -->
    <redpacket-rain v-if="rainFlag" @end="openPopup" />
    <!-- 3:赠品 -->
    <uni-popup
      ref="giftPopup"
      @maskClick="giftClose"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view
        v-if="prizeInfo"
        class="zengpin-box"
        :style="{
          'padding-top': prizeInfo.prizeStartTime ? '280rpx' : '320rpx',
        }"
      >
        <view class="zp-box">
          <view class="zhu-box">
            <view class="zou">
              <image class="tuxiang" :src="prizeInfo.url" mode="aspectFit" />
            </view>
            <view class="you">
              <view class="shang">{{ prizeInfo.name }}</view>
              <block v-if="prizeInfo.prizeStartTime">
                <view class="xia">请您及时兑奖哦~</view>
              </block>
              <block v-else>
                <view class="xia" v-if="1 == prizeInfo.isSelect">
                  游戏升级到{{ stageName }}阶段
                </view>
                <view class="xia" v-else>
                  雪球达{{ prizeInfo.qualifyNum }}个
                </view>
              </block>
            </view>
          </view>
          <block v-if="prizeInfo.prizeStartTime" :class="prizeInfo.detail?'two-line-text':''">
            <view class="hang-box">
              <text>兑奖期限：</text>
              <text>
                {{ prizeInfo.prizeStartTime }}-{{ prizeInfo.prizeEndTime }}
              </text>
            </view>
            <view class="hang-box"> 兑奖时段：{{ prizeInfo.prizeTime }} </view>
            <view class="hang-box"> 兑奖地点：{{ prizeInfo.prizePlace }} </view>
            <view class="hang-box">
              兑奖联系人：{{ prizeInfo.prizePeople }}
            </view>
            <view class="hang-box"> 联系电话：{{ prizeInfo.prizePhone }} </view>
            <view class="hang-box">
              兑奖须知：{{ prizeInfo.detail }}
            </view>
          </block>
          <block v-else>
            <view class="hang2-box"> 该商品需要邮寄，请完善收货地址信息 </view>
          </block>
          <view class="btn-outbox">
            <view class="btn-box" @click="goMyPrize">
              <view class="wen" style="color: #9f4227">我的奖品</view>
              <image class="btn" :src="waterTemplate.templateComponent.selectGoodsBtnimg" />
            </view>
            <view
              class="btn-box"
              @click="giftClose"
              v-if="prizeInfo.prizeStartTime"
            >
              <view class="wen" style="color: #fde488">关闭</view>
              <image class="btn" :src="iconPic.gbbtn" />
            </view>
            <view class="btn-box" @click="goOrder" v-else>
              <view class="wen" style="color: #fde488">去领取</view>
              <image class="btn" :src="iconPic.gbbtn" />
            </view>
          </view>
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.prizePopupMiddleUrl" />
      </view>
    </uni-popup>
    <!-- 1:优惠券 4:线下券 -->
    <uni-popup
      ref="ticketPopup"
      @maskClick="ticketClose"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view v-if="prizeInfo" class="quan-outbox">
        <view class="quan-box">
          <view class="zhu-box">
            <view class="zou">
              <image class="tuxiang" :src="prizeInfo.url" mode="aspectFit" />
            </view>
            <view class="you">
              <view class="shang"> {{ prizeInfo.name }}</view>
              <view class="xia">
                券有效期：
                <text v-if="!prizeInfo.couponStartDate">无</text>
              </view>
              <view class="xia" v-if="prizeInfo.couponStartDate">
                {{ prizeInfo.couponStartDate }}-
              </view>
              <view class="xia" v-if="prizeInfo.couponStartDate">
                {{ prizeInfo.couponEndDate }}
              </view>
              <view class="xia">使用范围：{{ prizeInfo.suitType || "" }}</view>
            </view>
          </view>
          <view class="btn-outbox">
            <view class="btn-box" @click="goMyPrize">
              <view class="wen" style="color: #9f4227">我的奖品</view>
              <image class="btn" :src="waterTemplate.templateComponent.selectGoodsBtnimg" />
            </view>
            <view class="btn-box" @click="toUse">
              <view class="wen" style="color: #fde488">去使用</view>
              <image class="btn" :src="iconPic.gbbtn" />
            </view>
          </view>
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.prizePopupSmallUrl" />
      </view>
    </uni-popup>
    <!-- 中奖 -->
    <uni-popup
      ref="giveSinglePopup"
      :isMaskClick="false"
      @maskClick="giveSingleCloseBefore"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view v-if="iconPic" class="jiang-box">
        <view class="quyu-outbox">
          <div class="biaoti">恭喜您中奖啦！</div>
          <view class="quyu-box">
            <view class="zou">
              <image
                class="tuxiang"
                :src="waterTemplate.templateComponent.choujiangUrl"
                mode="aspectFit"
              />
            </view>
            <view class="you">
              <view class="shang"> 恭喜您获得抽奖机会一次 </view>
              <view class="xia"> 游戏升级到{{ stageName }}阶段 </view>
              <view class="btn-box" @click="goRaffle">
                <view class="wen">开始抽奖</view>
                <image class="btn" :src="iwaterTemplate.templateComponent.selectGoodsBtnimg" />
              </view>
            </view>
          </view>
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.prizePopupSmallUrl" />
      </view>
    </uni-popup>
    <!-- 中奖集合 -->
    <uni-popup
      ref="givePopup"
      :isMaskClick="false"
      @maskClick="giveCloseBefore"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view v-if="iconPic" class="zhongjiang-box">
        <image class="guan" :src="iconPic.guan" @click="giveCloseBefore" />
        <view class="cell-outbox">
          <view class="cell-box" v-for="(u, i) in giveList" :key="u.id">
            <view class="zou">
              <image class="tuxiang" :src="u.url" mode="aspectFit" />
            </view>
            <view class="you">
              <view class="shang">{{ u.name }}</view>
              <view class="xia">{{ u.desc }}</view>
              <view class="btn-box" v-if="0 == i" @click="goRaffle2">
                <view class="wen">开始抽奖</view>
                <image class="btn" :src="waterTemplate.templateComponent.selectGoodsBtnimg" />
              </view>
              <view class="btn-box" v-else @click="goMyPrize">
                <view class="wen">查看奖品</view>
                <image class="btn" :src="waterTemplate.templateComponent.selectGoodsBtnimg" />
              </view>
            </view>
          </view>
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.prizePopupBigUrl" />
      </view>
    </uni-popup>
    <!-- 4:线下券核销 -->
    <block v-if="writeShow">
      <write-off :coupon-no="couponNo" @close="closeWriteDia" />
    </block>
    <!-- 抽奖转盘 -->
    <pop-lottery
      v-if="showLotteryDialog"
      :lotteryId="drawPath"
      @close="lotteryClose"
    />
  </view>
</template>
<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import RedpacketRain from "./RedpacketRain";
import WriteOff from "./WriteOff";
import PopLottery from "@/components/lottery/pop-lottery.vue";

export default {
  components: {
    RedpacketRain,
    WriteOff,
    PopLottery,
  },
  props: {
    waterInfoId: {
      type: String,
      require: true,
      default: "",
    },
    finishNum: {
      type: Number,
      require: true,
      default: 0,
    },
    isDraw: {
      // 是否抽奖（0否1是）
      type: Number,
      require: true,
      default: 0,
    },
    drawPath: {
      type: String,
      require: false,
      default: "",
    },
    stageName: {
      type: String,
      require: false,
      default: "",
    },
    givePrize: {
      type: Array,
      require: true,
      default() {
        return [];
      },
    },
    //游戏模版配置
    waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      rainFlag: false,
      prizeInfo: null,
      writeShow: false,
      couponNo: "",
      giveList: [],
      showLotteryDialog: false,

      iconPic: {
        guan: "http://img.songlei.com/paradise/task/guan.png",

        jpbtn: "https://img.songlei.com/paradise/jpbtn.png",
        gbbtn: "https://img.songlei.com/paradise/gbbtn.png",
      },
    };
  },
  mounted() {
    this.$refs.startPopup.open("center");
    setTimeout(() => {
      this.startClose();
    }, 2000);
  },
  methods: {
    startClose() {
      this.$refs.startPopup.close();
      this.rainFlag = true;
    },
    openPopup() {
      // prizeType 1:优惠券 2:盲盒 3:赠品 4:线下券
      this.rainFlag = false;
      if (0 == this.isDraw) {
        this.prizeInfo = this.givePrize[0];
        if (3 == this.prizeInfo.prizeType) {
          this.$refs.giftPopup.open("center");
        } else {
          // 1:优惠券 4:线下券
          this.$refs.ticketPopup.open("center");
        }
      } else {
        if (0 == this.givePrize.length) {
          this.$refs.giveSinglePopup.open("center");
        } else {
          this.giveList = [];
          this.giveList.push({
            url: "http://img.songlei.com/paradise/home/<USER>",
            name: "恭喜您获得抽奖机会一次",
            desc: `游戏升级到${this.stageName}阶段`,
          });
          this.givePrize.forEach((u) => {
            u.desc = `${waterTemplate.tip2}达${u.qualifyNum}个`;
            this.giveList.push(u);
          });
          this.$refs.givePopup.open("center");
        }
      }
    },
    // 我的奖品
    goMyPrize() {
      uni.navigateTo({
        url: `/pages/myPrize/index?waterInfoId=${this.waterInfoId}`,
      });
    },

    // 3:赠品
    giftClose() {
      this.$refs.giftPopup.close();
      this.$emit("close");
    },
    goOrder() {
      this.giftClose();
      uni.navigateTo({
        url: `/pages/order/order-confirm/index?prizeId=${this.prizeInfo.userPrizeId}`,
      });
    },

    // 1:优惠券 4:线下券
    ticketClose() {
      this.$refs.ticketPopup.close();
      this.$emit("close");
    },
    toUse() {
      if (1 == this.prizeInfo.prizeType) {
        this.ticketClose();
        uni.navigateTo({
          url: `/pages/goods/goods-list/index?couponId=${this.prizeInfo.prizeValue}`,
        });
      } else {
        // 线下券去核销
        this.$refs.ticketPopup.close();
        this.couponNo = this.prizeInfo.couponNo;
        this.writeShow = true;
      }
    },
    closeWriteDia() {
      this.writeShow = false;
      this.$emit("close");
    },

    // 中奖
    giveSingleClose() {
      this.$refs.giveSinglePopup.close();
      this.$emit("close");
    },
    giveSingleCloseBefore() {
      // 配合isMaskClick使用，不然源码关闭
      uni.showModal({
        title: "提示",
        content: "确定放弃本次抽奖机会吗?",
        showCancel: true,
        success: (res) => {
          if (res.confirm) {
            this.giveSingleClose();
          }
        },
      });
    },
    goRaffle() {
      this.$refs.giveSinglePopup.close();
      this.lotteryOpen();
    },

    // 中奖集合
    giveClose() {
      this.$refs.givePopup.close();
      this.$emit("close");
    },
    giveCloseBefore() {
      // 配合isMaskClick使用，不然源码关闭
      uni.showModal({
        title: "提示",
        content: "确定放弃本次抽奖机会吗?",
        showCancel: true,
        success: (res) => {
          if (res.confirm) {
            this.giveClose();
          }
        },
      });
    },
    goRaffle2() {
      this.$refs.givePopup.close();
      this.lotteryOpen();
    },

    // 抽奖转盘打开
    lotteryOpen() {
      this.showLotteryDialog = true;
    },
    // 抽奖转盘关闭
    lotteryClose() {
      this.showLotteryDialog = false;
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.StageLevelPrize-page {
  .kaishi-box {
    position: relative;
    width: 649rpx;
    height: 566rpx;
    .shu {
      width: 134rpx;
      text-align: center;
      position: relative;
      top: 300rpx;
      // left: 325rpx;
      left: 395rpx;
      font-size: 75rpx;
      font-family: DINPro;
      font-weight: bold;
      color: #5C51AF;
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 649rpx;
      height: 566rpx;
    }
  }
  .zengpin-box {
    position: relative;
    width: 657rpx;
    height: 929rpx;
    padding-top: 280rpx;
    .zp-box {
      margin: auto;
      width: 556rpx;
      .zhu-box {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        .zou {
          width: 247rpx;
          height: 247rpx;
          background: #ffffff;
          border-radius: 16rpx;
          .tuxiang {
            width: 247rpx;
            height: 247rpx;
            border-radius: 16rpx;
          }
        }
        .you {
          margin-left: 16rpx;
          .shang {
            width: 273rpx;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 800;
            color: #000000;
          }
          .xia {
            margin-top: 90rpx;
            font-size: 28rpx;
            font-family: zihun100hao-fangfangxianfengti;
            font-weight: 400;
            color: #a1786e;
          }
        }
      }
      .hang-box {
        font-size: 22rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #000000;
        line-height: 35rpx;
      }
      .hang2-box {
        margin-top: 40rpx;
        margin-bottom: 36rpx;
        text-align: center;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #000000;
      }
      .btn-outbox {
        margin-top: 10rpx;
        display: flex;
        align-items: center;
        .btn-box {
          position: relative;
          margin: auto;
          width: 234rpx;
          height: 83rpx;
          text-align: center;
          .wen {
            padding-top: 10rpx;
            font-size: 30rpx;
            font-family: PingFang SC;
            font-weight: 800;
          }
          .btn {
            position: absolute;
            z-index: -1;
            top: 0;
            left: 0;
            width: 234rpx;
            height: 83rpx;
          }
        }
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 657rpx;
      height: 929rpx;
    }
  }
  .quan-outbox {
    position: relative;
    width: 657rpx;
    height: 747rpx;
    padding-top: 280rpx;
    .quan-box {
      margin: auto;
      width: 540rpx;
      .zhu-box {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        .zou {
          width: 247rpx;
          height: 247rpx;
          background: #ffffff;
          border-radius: 16rpx;
          .tuxiang {
            width: 247rpx;
            height: 247rpx;
            border-radius: 16rpx;
          }
        }
        .you {
          margin-left: 16rpx;
          .shang {
            width: 273rpx;
            margin-bottom: 20rpx;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 800;
            color: #000000;
          }
          .xia {
            width: 240rpx;
            font-size: 22rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #000000;
            line-height: 30rpx;
          }
        }
      }
      .btn-outbox {
        margin-top: 10rpx;
        display: flex;
        align-items: center;
        .btn-box {
          position: relative;
          margin: auto;
          width: 234rpx;
          height: 83rpx;
          text-align: center;
          .wen {
            padding-top: 10rpx;
            font-size: 30rpx;
            font-family: PingFang SC;
            font-weight: 800;
          }
          .btn {
            position: absolute;
            z-index: -1;
            top: 0;
            left: 0;
            width: 234rpx;
            height: 83rpx;
          }
        }
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 657rpx;
      height: 747rpx;
    }
  }
  .jiang-box {
    position: relative;
    width: 657rpx;
    height: 747rpx;
    padding-top: 280rpx;
    .quyu-outbox {
      margin: auto;
      width: 540rpx;
      .biaoti {
        font-size: 45rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: #9f4427;
        text-align: center;
      }
      .quyu-box {
        margin-top: 30rpx;
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        .zou {
          width: 200rpx;
          height: 200rpx;
          background: #ffffff;
          border-radius: 16rpx;
          .tuxiang {
            width: 200rpx;
            height: 200rpx;
            border-radius: 16rpx;
          }
        }
        .you {
          text-align: center;
          margin-left: 20rpx;
          .shang {
            width: 350rpx;
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #000000;
          }
          .xia {
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #895f56;
            margin-bottom: 30rpx;
          }
          .btn-box {
            position: relative;
            margin: auto;
            width: 234rpx;
            height: 83rpx;
            text-align: center;
            .wen {
              padding-top: 10rpx;
              font-size: 30rpx;
              font-family: PingFang SC;
              font-weight: 800;
              color: #9f4227;
            }
            .btn {
              position: absolute;
              z-index: -1;
              top: 0;
              left: 0;
              width: 234rpx;
              height: 83rpx;
            }
          }
        }
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 657rpx;
      height: 747rpx;
    }
  }
  .zhongjiang-box {
    position: relative;
    width: 722rpx;
    height: 1173rpx;
    padding-top: 380rpx;
    .guan {
      position: absolute;
      top: 220rpx;
      right: 18rpx;
      width: 67rpx;
      height: 94rpx;
    }
    .cell-outbox {
      width: 600rpx;
      height: 700rpx;
      overflow: auto;
      margin: auto;
      .cell-box {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        .zou {
          width: 200rpx;
          height: 200rpx;
          background: #ffffff;
          border-radius: 16rpx;
          .tuxiang {
            width: 200rpx;
            height: 200rpx;
            border-radius: 16rpx;
          }
        }
        .you {
          text-align: center;
          margin-left: 20rpx;
          .shang {
            width: 350rpx;
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #000000;
          }
          .xia {
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #895f56;
            margin-bottom: 30rpx;
          }
          .btn-box {
            position: relative;
            margin: auto;
            width: 234rpx;
            height: 83rpx;
            text-align: center;
            .wen {
              padding-top: 10rpx;
              font-size: 30rpx;
              font-family: PingFang SC;
              font-weight: 800;
              color: #9f4227;
            }
            .btn {
              position: absolute;
              z-index: -1;
              top: 0;
              left: 0;
              width: 234rpx;
              height: 83rpx;
            }
          }
        }
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 722rpx;
      height: 1173rpx;
    }
  }
}
</style>
