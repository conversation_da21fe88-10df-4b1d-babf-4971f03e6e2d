<template>
	<view class="GameOver-page">
		<uni-popup  :isMaskClick="showMask" ref="popup" mask-background-color="rgba(0,0,0,0.8)">
			<view class="core-box" v-if="waterTemplate&&waterTemplate.templateComponent">
				<view style="padding-top: 160rpx;">
					<view class="one-box">松鼠乐园活动已结束</view>
					<view class="one-box">感谢您的关注</view>
					<image class="liangliang" :src="waterTemplate.templateComponent.runOutUrl" />

					<view class="three-box">
						<view class="btn" @click="goHome">返回首页</view>
						<view class="btn" @click="goMyPrize">查看我的奖品</view>
					</view>
				</view>
				<image class="bg" :src="waterTemplate.templateComponent.assistanceUrl" />
			</view>
		</uni-popup>
	</view>
</template>

<script>
	/**
	 * <AUTHOR>
	 * @date 2023年9月6日
	 **/

	export default {
		props: {
			showMask: {
				type: Boolean,
				require: true,
				default: false,
			},
			waterInfoId: {
				type: String,
				require: true,
				default: "",
			},	
			//游戏模版配置
			waterTemplate: {
				type: Object,
				require: true,
				default() {
					return {};
				},
			},
		},	
		data() {
			return {
				iconPic: {
				},
			};
		},
		mounted() {
			console.log("waterTemplate==>",this.waterTemplate);
			this.$refs.popup.open("center");
		},
		methods: {
			goHome() {
				uni.reLaunch({
					url: '/pages/home/<USER>'
				});
			},
			// 我的奖品
			goMyPrize() {
				uni.navigateTo({
					url: `/pages/myPrize/index?waterInfoId=${this.waterInfoId}`,
				});
			},
		},
	};
</script>
<style scoped lang="scss">
	.GameOver-page {
		.core-box {
			position: relative;
			width: 723rpx;
			height: 768rpx;
			text-align: center;

			.one-box {
				text-align: center;
				font-size: 30rpx;
				font-weight: 500;
				color: #9f4227;
			}

			.liangliang {
				width: 391rpx;
				height: 304rpx;
			}

			.three-box {
				display: flex;
				justify-content: space-between;
				margin: 20rpx 88rpx 0;

				.btn {
					width: 236rpx;
					height: 74rpx;
					line-height: 74rpx;
					background-size: 100% 100%;
					font-size: 30rpx;
					font-weight: 800;
					color: #953C22;
					background-image: url(http://img.songlei.com/paradise/task/huang.png);
				}
			}

			.bg {
				z-index: -10;
				position: absolute;
				top: 0;
				right: 0;
				width: 723rpx;
				height: 768rpx;
			}
		}
	}
</style>