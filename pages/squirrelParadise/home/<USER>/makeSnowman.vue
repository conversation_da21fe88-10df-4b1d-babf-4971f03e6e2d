<template>
	<!-- 松鼠乐园主页配置 -->
		<!-- 背景图片 -->
		<view class="canvas" 
			:style="{
						position: 'relative',
						display: 'flex',
						flexDirection: 'column',
						width: '100%',
						height: '100%',
						background: `url(${waterTemplate.templateComponent.bgimgUrl}) center/cover no-repeat`,
						backgroundSize: 'cover',
						overflow: 'hidden',
						minHeight: '100vh',
				}">
			<!-- 松鼠乐园 https://img.songlei.com/paradise/title.png-->
			<image :src="waterTemplate.templateComponent.themeimgUrl" mode="aspectFit" :style="{
					width: '100%',
					height:'180rpx',
					display: 'block',
					marginTop: '60rpx',
				}">
			</image>

			<!-- 用户信息 -->
			<view class="sumup">
				<!-- 头像框 -->
				<image :src="waterTemplate.templateComponent.headimgUrl" mode="aspectFit" :style="{
						width: '100%',
						height:'151rpx',
						display: 'block',
						'z-index': '2',
						position: 'relative',
						right: '130rpx',
						top: '40rpx',
						pointerEvents: 'none',
					}">
				</image>
				<!-- 名称电话 -->
				<view class="user" v-if="waterUser">
					<view class="cover">
						<image :src="waterUser.headImg" @click.stop="zoom(waterUser.headImg)" hover-class="none">
						</image>
					</view>
					<view class="info">
						<view class="name overflow-1" style="width: 160rpx;line-height: 40rpx;">
							<text class="name text-smx overflow-1" v-text="waterUser.nickName"></text>
							<view :class="'sex-'+ info.sex"></view>
						</view>
						<view class="drop" hover-class="none">
							<!-- 去掉手机号 -->
							<!-- <text class="text-sm" style="font-size: 22rpx" v-text="waterUser.phone"></text> -->
							<!-- <view class="icon">
								<text class="plus" :class="{'pluss': pluss}">+{{plussNum}}</text>
							</view> -->
						</view>
					</view>
				</view>

				<!-- 广告信息 -->
				<view class="speed" v-if="waterInfo">
					<view class="progress">
						<view v-for="(item,index) in filteredItems" :key="item.id">
							<view class="margin-bottom-xs" hover-class="none" @click="toPage(item.jumpLink)">
								<image class="speed-1" :src="item.imgUrl"></image>
								<view style="white-space: nowrap;overflow: hidden;"
									class="text-bold text-center text-xss">{{item.name}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 云朵位移 -->
			<view class="cloud" style="pointer-events: none;">
				<view class="fly-1"></view>
				<view class="fly-2"></view>
				<view class="fly-3"></view>
			</view>

			<!-- 中奖信息 弹幕 isShowWinner是否展示中奖用户信息 0 否 1是-->
			<view class="barrage" style="pointer-events: none;">
				<barrage :barrageData="prizeUsers"></barrage>
			</view>

			<!-- 商品背板 -->
			<view style="width: 28%;position: absolute;top: 430rpx;">
				<view style="position: relative;">
					<!--商品背板图片-->
					<image :src="waterTemplate.templateComponent.goodsBackimgUrl" mode="aspectFit" :style="{
							width: '100%',
							height:'321rpx',
							display: 'block',
						}">
					</image>

					<!-- 背板文案 -->
					<view @click="closePrize()" :style="{
								height: '182rpx',
								width: '255rpx',
								position: 'absolute',
								top:'80rpx',
								left:'-23rpx',
								'z-index': '2',
						}">
						<!-- 控制最多显示六个字-->
						<view
							v-if="selectedPrize"
							class="text-center text-smx text-bold"
							style="width: 162rpx;margin: 0 auto;color: #883C1D;overflow: hidden;">
							<auto-scroll-text :text="selectedPrize.name"></auto-scroll-text>
						</view>

						<view class="text-center text-xs text-bold" style="color: #883C1D;">集{{needWaterTotal}}雪球
						</view>
						<view class="text-center text-xs text-bold" style="color: #883C1D;">即可领取</view>
						<!--更换商品按钮图片-->
						<image :src="waterTemplate.templateComponent.goodsChangeimgUrl" mode="aspectFit"
							:style="{
								width: '100%',
								height:'56rpx',
								display: 'block',
								'z-index':'3'
							}">
						</image>
					</view>
				</view>
			</view>

			<!-- 左侧松鼠 -->
			<view style="position: absolute;top:680rpx;pointer-events: none;">
				<view style="position: relative;">
					<!--左边松鼠图片-->
					<image :src="waterTemplate.templateComponent.leftSquirrelimgUrl" :style="{
								height: '310rpx',
								width: '310rpx',
								display: 'block',
								'z-index': '2',
						}">
					</image>
					<!-- 商品图片盲盒，商品 ，优惠券你，线下劵 1:优惠券 2:盲盒 3:赠品 4:线下券-->
					<view v-if="fullLevel!==1&&(fullLevelPrize===null||fullLevelPrize==='null')">
						<!--松鼠下盲盒图片-->
						<image v-if="selectedPrize&&selectedPrize.prizeType=='2'"
							:src="waterTemplate.templateComponent.goodsTheBoxUrl" :style="{
									height: '182rpx',
									width: '255rpx',
									position: 'absolute',
									top:'252rpx',
									left:'-15rpx',
									display: 'block',
									'z-index': '2',
							}">
						</image>

						<!--松鼠下商品框图片-->
						<image v-if="selectedPrize&&selectedPrize.prizeType!='2'"
							:src="waterTemplate.templateComponent.goodsBorderimgUrl" :style="{
									height: '202rpx',
									width: '182rpx',
									position: 'absolute',
									top:isScreenHeight?'252rpx':'340rpx',
									left:'15rpx',
									display: 'block',
									'z-index': '2',
							}" class="animate__animated animate__delay-3s animate__bounce-min animate__slower animate__infinite">
						</image>

						<image v-if="selectedPrize&&selectedPrize.prizeType!='2'" :src="selectedPrize.url" :style="{
									height: '145rpx',
									width: '146rpx',
									position: 'absolute',
									top:isScreenHeight?'292rpx':'385rpx',
									left:'30rpx',
									display: 'block',
									'z-index': '3',
							}" class="animate__animated animate__delay-3s animate__bounce-min animate__slower animate__infinite">
						</image>
					</view>
				</view>
			</view>
			
			<!-- 树、水滴值 -->
			<view class="tree" :style="{
					height: isScreenHeight?'280rpx':'456rpx',
				}">
				<!-- 树升级框图片 -->
				<view v-if="waterTreeImg&&stages!==1">
					<image :src="waterTemplate.templateComponent.treeUpgradeBoximgUrl"
						:style="{display: 'block','z-index': '9'}" :class="['snowman-'+ stages]" hover-class="none">
					</image>
					<!--树升级框发光图片-->
					<image :src="waterTemplate.templateComponent.luminescenceimgUrl"
						:style="{display: 'block','z-index': '9'}" :class="['snowman-'+ stages]" hover-class="none">
					</image>
					<!--树升级框提示文字图片-->
					<image 
						v-if="stages ==2" 
						:src="waterTemplate.templateComponent.treePrompttextimgUrl2"
						:style="{display: 'block','z-index': '9'}" 
						:class="['snowman-image-text-'+ stages]"
						hover-class="none">
					</image>

					<image 
						v-if="stages ==3" 
						:src="waterTemplate.templateComponent.treePrompttextimgUrl3"
						:style="{display: 'block','z-index': '9'}" 
						:class="['snowman-image-text-'+ stages]"
						hover-class="none">
					</image>

					<image 
						v-if="stages ==4" 
						:src="waterTemplate.templateComponent.treePrompttextimgUrl4"
						:style="{display: 'block','z-index': '9'}" 
						:class="['snowman-image-text-'+ stages]"
						hover-class="none">
					</image>

					<image :src="waterTree.treeImg" :style="{display: 'block','z-index': '9'}"
						:class="['tree2-'+ stages]" hover-class="none">
					</image>
				</view>

				<image 
					:src="waterTree.treeImg" 
					:style="{bottom: isScreenHeight?'-88rpx':'-55rpx',display: 'block'}"
					:class="[
						'tree-'+ stages,
						treemove ? 'move-'+ stages : ''
					]" 
					hover-class="none" @click="tree">
				</image>

				<!--水滴图片-->
				<image v-if="waterInfo&&waterdom" :src="waterTemplate.templateComponent.waterDropimgUrl" :style="{
						width: '184rpx',
						height:'392rpx',
						display: 'block',
					}">
				</image>
			</view>

			<view class="plante-progress" style="position: relative;">
				<!-- 种树完成后 查看按钮 -->
				<view v-if="fullLevel===1&&fullLevelPrize&&isPrizeBoxShuow">
					<!--种树完成宝箱发光图片-->
					<image
						class="animate__animated animate__delay-2s animate__pulse animate__slower animate__infinite"
						:src="waterTemplate.templateComponent.luminescenceimgUrl" :style="{
							width: '558rpx',
							height:'490rpx',
							display: 'block',
							position: 'absolute',
							bottom:'18rpx',
							left: '110rpx',
							'pointer-events': 'none',
							'z-index':'4'
						}">
					</image>
					<!--种树完成宝箱图片-->
					<image
						class="animate__animated animate__delay-2s animate__pulse animate__slower animate__infinite"
						:src="waterTemplate.templateComponent.completeimgUrl" :style="{
							width: '558rpx',
							height:'490rpx',
							display: 'block',
							position: 'absolute',
							bottom:'18rpx',
							left: '110rpx',
							'pointer-events': 'none',
							'z-index':'5'
						}">
					</image>

					<!-- 查看奖品按钮图片 -->
					<image @click="openPrizeDialog()"
						class="animate__animated animate__delay-2s animate__tada animate__slower animate__infinite"
						:src="waterTemplate.templateComponent.viewPrizesBtnimgUrl" :style="{
							width: '210rpx',
							height:'94rpx',
							display: 'block',
							position: 'absolute',
							top: '-15rpx',
							right: '240rpx',
							'z-index':'6'
						}">
					</image>
				</view>
				
				<!-- 水滴进度条背景图 -->
				<image :src="waterTemplate.templateComponent.schedulebgimgUrl" mode="aspectFit" :style="{
						width: '374rpx',
						height:'196rpx',
						display: 'block',
						position: 'relative',
						left: '216rpx',
						bottom: isScreenHeight?'-90rpx':'-86rpx',
						'z-index': '1',
					}">
				</image>

				<!-- 水滴进度条 -->
				<view class="water-drop" style="position: absolute;top: 122rpx;width: 378rpx;right: 165rpx;z-index: 1">

					<view class="drop text-center" hover-class="none">
						<text v-text="'已堆雪球'+ (waterUser.finsheNum || '0')+'个'"></text>
						
						<view class="icon">
							<text class="plus" v-if="plussNum" :class="{'pluss': pluss}">+{{plussNum}}</text>
						</view>
					</view>
					<view class="flex" 
						:style="{
							position: 'relative',
							bottom: systemInfo.uniPlatform==='mp-weixin'?'12rpx':(systemInfo.osName=='ios'?'8rpx':systemInfo.uniPlatform=='app'?'8rpx':(isScreenHeight?'8rpx':'4rpx')),
							'margin-left': '45rpx'
						}">
						<!--#ifdef H5 || MP-WEIXIN-->
						<view class="text-smx" style="padding-right:5rpx;height: 54rpx;line-height: 54rpx;" >{{waterTree.name||"雪堆"}}</view>
						<!--#endif-->
						<!--#ifdef APP-PLUS-->
						<view class="text-smx" style="padding-right:5rpx;padding-top: 14rpx;" >{{waterTree.name||"雪堆"}}</view>
						<!--#endif-->
						<view style="width: 245rpx;">
							<image :src="'https://img.songlei.com/paradise/highlight.png'" 
								:style="{
									position: 'absolute',
									right: '42rpx',
									bottom: '30rpx',
									width: '224rpx',
									height:'12rpx',
									display: 'block',
									'z-index':'1',
								}">
							</image>
							<lx-progress-bar 
								backColor="#3AA2F6"
								titleStyle="font-size: 26rpx; font-weight: bold;text-align: center;" 
								bar-radius="30rpx"
								contentColor="linear-gradient(#ffe930, #f69702)" 
								barHeight="45rpx" 
								:total="1" 
								:firstValue="schedule" 
								:percentum="true"
								:precision="1" 
								:animation="pluss" 
								textPosition = 'middle'/>
						</view>
					</view>

					<view class="text-center text-smx text-bold" v-if="needWaterNum > 0">
						{{waterTemplate.tip1.replace(/\${num}/, needWaterNum) || ''}}
					</view>
					<view class="text-center text-smx text-bold" v-if="fullLevel===1">
						游戏完成，点击查看奖品!
					</view>
				</view>
			</view>

			<!-- 入口牌子、水壶 -->
			<view class="bottom-brand ">
				<!-- 牌子 -->
				<image :src="waterTemplate.templateComponent.brandimgUrl" :style="{
						width: '473rpx',
						height:isScreenHeight?'240rpx':'335rpx',
						display: 'block',
					}">
				</image>
				<!-- 任务、攻略、奖品入口 -->
				<view class="flex"
					:style="{position: 'absolute',left: '45rpx',bottom:isScreenHeight?'30rpx':'62rpx',}">
					<view class="text-bold" @click="openTaskDia">
						<image :src="waterTemplate.templateComponent.tasklistimgUrl" mode="aspectFit" :style="{
								width: '100%',
								height:isScreenHeight?'80rpx':'99rpx',
								display: 'block',
							}">
						</image>
						任务列表
					</view>

					<view class="margin-lr text-bold" @click="openStrategyDia">
						<image :src="waterTemplate.templateComponent.viewGuideimgUrl" mode="aspectFit" :style="{
								width: '100%',
								height:isScreenHeight?'80rpx':'99rpx',
								display: 'block',
							}">
						</image>
						查看攻略
					</view>

					<view class="text-bold" style="z-index: 4" @click.stop="goMyPrize">
						<image :src="waterTemplate.templateComponent.myPrizeimgUrl" mode="aspectFit" :style="{
								width: '100%',
								height:isScreenHeight?'80rpx':'99rpx',
								display: 'block',
							}">
						</image>
						我的奖品
					</view>
				</view>
			</view>

			<!-- 树桩 -->
			<view class="bottom-kettle">
				<view @click="water">
					<view :style="{
							color: '#5C51AF',
							'text-align': 'center',
							width: '180rpx',
							'font-size': '75rpx',
							position: 'absolute',
							bottom: isScreenHeight?'200rpx':'210rpx',
							left: '130rpx',
							'z-index': '1',
						}">
						{{waterUser.waterNum || '0'}}
					</view>

					<image :src="waterTemplate.templateComponent.stumpimgUrl" :style="{
							width: '352rpx',
							height: '510rpx',
							display: 'block',
							'pointer-events': 'none',
						}">
					</image>
				</view>
			</view>
		</view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/

const app = getApp();
import Barrage from "./barrage";
import AutoScrollText from "./autoScrollText";
import lxProgressBar  from "../../components/lx-progress-bar.vue";

import {
	navigateUtil
} from "@/static/mixins/navigateUtil.js"

export default {
	mixins: [navigateUtil],
	components: {
		Barrage,
		AutoScrollText,
		lxProgressBar
	},
	
	props: {
		//活动配置
		waterInfo: {
			type: Object,
			require: true,
			default() {
				return {
					bgImg:'http://img.songlei.com/paradise/snowman-active-bg.jpg',
				};
			},
		},

		//游戏模版配置
		waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},

		//需多少水滴获取阶段奖励
		needWaterNum: {
			type: Number,
			require: true,
			default() {
				return 20;
			},
		},

		//需多少水滴获取最终奖励
		needWaterTotal: {
			type: Number,
			require: true,
			default() {
				return 20;
			},
		},

		//是否满级（0否1是）
		fullLevel: {
			type: Number,
			require: true,
			default() {
				return 0;
			},
		},

		//浇水进度条进度（0.00） 两位小数
		schedule: {
			type: Number,
			require: true,
			default() {
				return 0;
			},
		},

		// 成长阶段 1(小树[默认])，中2(成长中树) ，大3(开花树)，大4(结果树)
		stages: {
			type: Number,
			require: true,
			default() {
				return 1;
			},
		},

		//树配置
		waterTree: {
			type: Object,
			require: true,
			default() {
				return {
					bgImg:'../static/img/detail-bg2.jpg',
				};
			},
		},

		//用户信息
		waterUser: {
			type: Object,
			require: true,
			default() {
				return {
				};
			},
		},

		//判断屏幕高度
		isScreenHeight: {
			type: Boolean,
			require: true,
			default:false,
		},

		//是否显示奖品盒子
		isPrizeBoxShuow: {
			type: Boolean,
			require: true,
			default:false,
		},

		// 水滴动画开关
		waterdom: {
			type: Boolean,
			require: true,
			default:false,
		},

		// 水壶动画开关
		watercss: {
			type: Boolean,
			require: true,
			default:false,
		},

		//树长大
		waterTreeImg: {
			type: Boolean,
			require: true,
			default:false,
		},

		//树大小动画类型开关
		treemove: {
			type: Boolean,
			require: true,
			default:false,
		},

		// 水滴值+1动画开关
		pluss: {
			type: Boolean,
			require: true,
			default:false,
		},

		//获取设备信息
		// systemInfo: {
		// 	type: String,
		// 	require: true,
		// 	default() {
		// 		return uni.getSystemInfoSync();
		// 	},
		// },

		// 加值数量（默认1）
		plussNum: {
			type: Number,
			require: true,
			default() {
				return 0;
			},
		},

		//是否展示中奖用户信息 0 否 1是
		isShowWinner: {
			type: Number,
			require: true,
			default() {
				return 0;
			},
		},

		//是否需要进行抽奖 0 否 1是
		isDraw: {
			type: Number,
			require: true,
			default() {
				return 0;
			},
		},

		//满级奖品列表
		fullLevelPrize: {
			type: Object,
			require: true,
			default() {
				return {
				};
			},
		},

		//选定的奖品
		selectedPrize: {
			type: Object,
			require: true,
			default() {
				return {
				};
			},
		},

		//中奖用户
		prizeUsers: {
			type: Array,
			require: true,
			default() {
				return [];
			},
		},

		//用户信息
		info: {
			type: Object,
			require: true,
			default() {
				return {
					name: '沐枫', // 用户姓名
					sex: 2, // 用户姓别 1男， 2女
					votes: 8, // 水滴值 默认为8
					avatar: '../static/img/detail-bg.jpg' //用户头像
				};
			},
		},
	},

	data() {
		return {
			systemInfo:uni.getSystemInfoSync(),//获取设备信息
		};
	},

	computed: {
		// 广告排序
		filteredItems() {
			if (this.waterInfo && this.waterInfo.advers) {
				// 按照 position 属性进行排序  
				return this.waterInfo.advers.sort((a, b) => a.position - b.position);
			}
		},
	},

	methods: {
		//打开最终奖品弹框
		//1.校验奖品类型 prizeType 1:优惠券 2:盲盒 3:赠品 4:线下券
		// a.如果是商品、劵直接打开奖品弹框
		// b.如果是盲盒打开盲盒弹框----》拆盲盒----》查看
		openPrizeDialog() {
			this.$emit('openPrizeDialog')
		},

		//更换奖品
		closePrize() {
			this.$emit('closePrize')
		},

		// 点击用户头像放大
		zoom(o) {
			uni.previewImage({
				'urls': [o]
			});
		},

		// 收取雨滴的动画
		rainFun(i, o) {
			this.$emit('rainFun',i, o)
		},

		// 点击树的动画
		tree() {
			this.$emit('tree',)
		},

		// 水壶浇水动画
		water() {
			this.$emit('water',)
		},

		// 打开任务列表
		openTaskDia() {
			// this.taskDiaShow = true;
			this.$emit('openTaskDia',true)
		},

		// 打开查看攻略
		openStrategyDia() {
			this.$emit('openStrategyDia',)
		},

		// 我的奖品
		goMyPrize() {
			this.$emit('goMyPrize',)
		}
	}
};
</script>

<style scoped lang="scss">
.canvas {
	position: relative;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	background: url(https://img.songlei.com/paradise/snowman-active-bg.jpg) no-repeat;
	background-size: cover;
	overflow: hidden;
	min-height: 100vh;
}

.canvas .cloud {
	margin-top: -20upx;
}

.canvas .cloud view {
	margin: 40upx 0;
}

.canvas .cloud .fly-1 {
	width: 102upx;
	height: 68upx;
	animation: cloud-1 80s ease-in-out 0s infinite alternate;
	background: url(../../static/img/fly-1.png) no-repeat;
	background-size: contain;
}

.canvas .cloud .fly-2 {
	width: 72upx;
	height: 52upx;
	animation: cloud-2 60s linear 0s infinite alternate;
	background: url(../../static/img/fly-2.png) no-repeat;
	background-size: contain;
}

.canvas .cloud .fly-3 {
	width: 78upx;
	height: 56upx;
	animation: cloud-3 70s ease 0s infinite alternate;
	background: url(../../static/img/fly-3.png) no-repeat;
	background-size: contain;
}

.canvas .barrage {
	position: absolute;
	z-index: 5;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.canvas .tree {
	position: relative;
	display: flex;
	flex-direction: row;
	justify-content: center;
}

.canvas .tree .rain {
	position: relative;
	top: 240upx;
	margin: 10upx;
	width: 60upx;
	height: 60upx;
	line-height: 60upx;
	text-align: center;
	font-size: 16upx;
	color: green;
	background: repeating-radial-gradient(#0fff00, #53c72d);
	box-shadow: 0 0 16upx 6upx #b8ffc4;
	border-radius: 50%;
	animation: rain 4s infinite;
	z-index: 1;
}

.canvas .tree .rain text {
	font-size: 12upx;
}

.canvas .tree .rain-1 {
	top: 50upx;
}

.canvas .tree .rain-2 {
	top: 100upx;
}

.canvas .tree .rain-3 {
	top: 200upx;
}

.canvas .tree .rain:nth-child(1) {
	animation-delay: .8s;
	margin-top: 10px;
}

.canvas .tree .rain:nth-child(2) {
	animation-delay: .5s;
	margin-top: -10px;
}

.canvas .tree .rain:nth-child(4) {
	animation-delay: .1s;
	margin-top: -5px;
}

.canvas .tree image {
	position: absolute;
	// width: 257upx;
	// height: 298upx;
	z-index: 3;
}

.canvas .tree .snowman-1 {
	// width: 257upx;
	// height: 298upx;
	bottom: -55upx;
	width: 545upx;
	height: 550upx;
	right: 60upx;
}

.canvas .tree .snowman-2 {
	// width: 257upx;
	// height: 298upx;
	bottom: -88upx;
	width: 545upx;
	height: 550upx;
	right: 60upx;
}

.canvas .tree .snowman-3 {
	bottom: -60upx;
	width: 545upx;
	height: 550upx;
	right: 60upx;
}

.canvas .tree .snowman-4 {
	bottom: -60upx;
	width: 545upx;
	height: 550upx;
	right: 60upx;
}

.canvas .tree .tree-1 {
	// width: 257upx;
	// height: 298upx;
	// bottom: -88upx;
	width: 510upx;
	height: 275upx;
	right: 0upx;
}

.canvas .tree .tree-2 {
	// width: 257upx;
	// height: 298upx;
	// bottom: -88upx;
	width: 524upx;
	height: 341upx;
	right: 0upx;
}

.canvas .tree .tree-3 {
	width: 470upx;
	height: 438upx;
	// bottom: -88upx;
	right: 0upx;
}

.canvas .tree .tree-4 {
	width: 508upx;
	height: 479upx;
	// bottom: -88upx;
	right: 0upx;
}

.canvas .tree .snowman-image-text-2 {
	// width: 257upx;
	// height: 298upx;
	bottom: 295rpx;
	width: 383rpx;
	height: 48rpx;
	left: 224rpx;
}

.canvas .tree .snowman-image-text-3 {
	// width: 257upx;
	// height: 298upx;
	bottom: 355rpx;
	width: 317rpx;
	height: 48upx;
	left: 274upx;
}

.canvas .tree .snowman-image-text-4 {
	// width: 257upx;
	// height: 298upx;
	bottom: 355rpx;
	width: 317rpx;
	height: 48upx;
	left: 274upx;
}

.canvas .tree .tree2-2 {
	// width: 257upx;
	// height: 298upx;
	bottom: -55upx;
	width: 345upx;
	height: 350upx;
	left: 254upx;
}

.canvas .tree .tree2-3 {
	// width: 257upx;
	// height: 298upx;
	bottom: 0upx;
	width: 345upx;
	height: 350upx;
	left: 254upx;
}

.canvas .tree .tree2-4 {
	// width: 257upx;
	// height: 298upx;
	bottom: 0upx;
	width: 345upx;
	height: 350upx;
	left: 254upx;
}

.canvas .kettle {
	display: flex;
	position: relative;
	flex-direction: row;
	justify-content: flex-end;
	width: 100%;
	height: 80upx;
}

.canvas .kettle view {
	position: absolute;
	z-index: 2;
}

.canvas .kettle .kettls {
	top: -172upx;
	right: 20upx;
	width: 116upx;
	height: 112upx;
	background: url(../../static/img/kettls.png) no-repeat;
	background-size: contain;
	transition: all 2s;
}

.canvas .kettle .flasks {
	top: -176upx;
	right: 28upx;
	width: 113upx;
	height: 70upx;
	background: url(../../static/img/flasks.png) no-repeat;
	background-size: contain;
	transition: all 2s;
}

.canvas .kettle .flasms {
	top: -176upx;
	right: 26upx;
	width: 117upx;
	height: 75upx;
	background: url(../../static/img/flasms.png) no-repeat;
	background-size: contain;
	transition: all 2s;
}

.canvas .kettle .waters {
	top: -240upx;
	right: 316upx;
	width: 85upx;
	height: 150upx;
	background: url(../../static/img/waters.gif) no-repeat;
	background-size: contain;
	transition: all 2s;
}

.canvas .sumup {
	position: absolute;
	top: 200upx;
	display: flex;
	width: 100%;
}

.canvas .sumup .user {
	display: flex;
	flex-direction: row;
	position: absolute;
	left: 15rpx;
	top: 65rpx;
}

.canvas .sumup .user .cover {
	padding: 0 10upx;
}

.canvas .sumup .user .cover image {
	width: 99upx;
	height: 98upx;
	border-radius: 15upx;
}

.canvas .sumup .user .info {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	margin-left: 22upx;
	padding-top: 12upx;
	z-index: 3;
}

.canvas .sumup .user .info .name {
	width: 150upx;
	font-size: 30upx;
	font-weight: bold;
	color: #883C1D;
	// word-wrap: break-word;
}

// .canvas .sumup .user .info .name .sex-1 {
// 	width: 25upx;
// 	height: 31upx;
// 	background: url(../static/img/male.png) no-repeat;
// 	background-size: contain;
// }

// .canvas .sumup .user .info .name .sex-2 {
// 	width: 21upx;
// 	height: 34upx;
// 	background: url(../static/img/women.png) no-repeat;
// 	background-size: contain;
// }

.canvas .sumup .user .info .name view {
	margin-left: 12upx;
	display: inline-block;
	vertical-align: middle;
}

.canvas .sumup .user .info .drop {
	// margin-top: 10upx;
	line-height: 32upx;
	font-size: 26upx;
}

.canvas .sumup .user .info .drop .icon {
	position: relative;
	display: inline-block;
	margin-left: 10upx;
	width: 20upx;
	height: 30upx;
	vertical-align: bottom;
	// background: url(../../static/img/water.png) no-repeat bottom right;
	background-size: contain;
}

.canvas .sumup .user .info .drop .plus {
	position: absolute;
	top: 0upx;
	right: -12upx;
	font-size: 32upx;
	opacity: 0;
	color: #ffbe2d;
}

.canvas .sumup .speed {
	// display: flex;
	// flex-direction: row;
	padding: 0upx 20upx;
	z-index: 4;
	// height: 100upx;
	// align-items: flex-end;
}

.canvas .sumup .speed .progress {
	// position: relative;
	// display: flex;
	// flex-direction: column;
	// justify-content: space-between;
	// align-items: flex-end;
	width: 120upx;
	height: 425upx;
	border-radius: 14upx;
	// background: linear-gradient(#9587ce, #7182d8);
}

.canvas .sumup .speed .progress .speed-1 {
	width: 115upx;
	height: 115upx;
	// margin-left: -8upx;
}

// .canvas .sumup .speed .progress .speed-2 {
// 	width: 115upx;
// 	height: 115upx;
// }

// .canvas .sumup .speed .progress .speed-3 {
// 	width: 115upx;
// 	height: 115upx;
// 	// margin-right: -12upx;
// }

.explain {
	padding: 50upx 16upx;
	font-size: 28upx;
	color: #ff00a5;
	font-weight: bold;
}

.explain .text {
	padding: 16upx;
	line-height: 60upx;
	text-indent: 40upx;
	color: #09b900;
	font-weight: normal;
}

@keyframes rain {
	0% {
		transform: translateY(-6px);
	}

	50% {
		transform: translateY(6px);
	}

	100% {
		transform: translateY(-6px);
	}
}

@keyframes cloud-1 {
	0% {
		opacity: .8;
		transform: translate3d(200%, 0, 0);
	}

	50% {
		opacity: .6;
		transform: translate3d(800%, 0, 0) scale(1.3);
	}

	100% {
		opacity: .8;
		transform: translate3d(-120%, 0, 0);
	}
}

@keyframes cloud-2 {
	0% {
		opacity: .8;
		transform: translate3d(820%, 0, 0);
	}

	50% {
		opacity: .6;
		transform: translate3d(-120%, 0, 0);
	}

	100% {
		opacity: .8;
		transform: translate3d(1080%, 0, 0) scale(1.3);
	}
}

@keyframes cloud-3 {
	0% {
		opacity: .6;
		transform: translate3d(-120%, 0, 0) scale(1.3);
	}

	100% {
		opacity: .8;
		transform: translate3d(1000%, 0, 0);
	}
}

@keyframes move-1 {
	0% {
		height: 298upx;
	}

	10% {
		height: 320upx;
	}

	20% {
		height: 325upx;
	}

	40% {
		height: 305upx;
	}

	70% {
		height: 315upx;
	}

	100% {
		height: 304upx;
	}
}

.move-1 {
	animation: move-1 1s;
}

@keyframes move-2 {
	0% {
		height: 524upx;
	}

	10% {
		height: 529upx;
	}

	20% {
		height: 539upx;
	}

	40% {
		height: 519upx;
	}

	70% {
		height: 534upx;
	}

	100% {
		height: 519upx;
	}
}

.move-2 {
	animation: move-2 1s;
}

@keyframes move-3 {
	0% {
		height: 589upx;
	}

	10% {
		height: 604upx;
	}

	20% {
		height: 614upx;
	}

	40% {
		height: 584upx;
	}

	70% {
		height: 604upx;
	}

	100% {
		height: 574upx;
	}
}

.move-3 {
	animation: move-3 1s;
}

@keyframes move-4 {
	0% {
		height: 589upx;
	}

	10% {
		height: 604upx;
	}

	20% {
		height: 614upx;
	}

	40% {
		height: 584upx;
	}

	70% {
		height: 604upx;
	}

	100% {
		height: 574upx;
	}
}

.move-4 {
	animation: move-4 1s;
}

@keyframes water {
	0% {
		opacity: .5;
		transform: translate3d(0, 0, 0);
	}

	20% {
		opacity: 1;
		transform: translate3d(-150upx, -90upx, 0) scale(1.5);
	}

	30% {
		opacity: 1;
		transform: translate3d(-150upx, -90upx, 0) scale(1.5) rotate(-35deg);
	}

	80% {
		opacity: 1;
		transform: translate3d(-150upx, -90upx, 0) scale(1.5) rotate(-35deg);
	}

	100% {
		opacity: 0;
		transform: translate3d(-150upx, -90upx, 0) scale(1.5) rotate(0deg);
	}
}

	.water {
		animation: water 4s ease-in-out forwards;
	}

	@keyframes pluss {
		0% {
			opacity: 0.8;
			top: -10upx;
		}

		80% {
			opacity: 1;
			top: -80upx;
		}

		100% {
			opacity: 0;
			top: -120upx;
		}
	}

	.pluss {
		animation: pluss 2s;
	}

	.canvas .bottom-brand {
		position: absolute;
		bottom: 0upx;
		left: 0upx;
		display: flex;
	}

	.canvas .bottom-kettle {
		position: absolute;
		bottom: 0upx;
		right: 0upx;
		display: flex;
	}

	.canvas .kettle1 {
		position: absolute;
		bottom: -10upx;
		right: 10upx;
		display: flex;
	}

	.canvas .kettle2 {
		position: absolute;
		bottom: 85upx;
		right: 10upx;
		display: flex;
	}

	.canvas .kettle .waters1 {
		top: -240upx;
		right: 316upx;
		width: 85upx;
		height: 150upx;
		background: url(https://img.songlei.com/paradise/waters1.gif) no-repeat;
		z-index: 4;
		background-size: contain;
		transition: all 2s;
	}

	@keyframes water1 {
		0% {
			opacity: .5;
			z-index: 4;
			transform: translate3d(0, 0, 0);
		}

		20% {
			opacity: 1;
			z-index: 4;
			transform: translate3d(-150upx, -850upx, 0);
		}

		30% {
			opacity: 1;
			z-index: 4;
			transform: translate3d(-150upx, -850upx, 0) rotate(-65deg);
		}

		80% {
			opacity: 1;
			z-index: 4;
			transform: translate3d(-150upx, -850upx, 0) rotate(-65deg);
		}

		100% {
			opacity: 0;
			z-index: 4;
			transform: translate3d(-150upx, -850upx, 0) rotate(0deg);
		}
	}

	.water1 {
		animation: water1 4s ease-in-out forwards;
	}

	.canvas .plante-progress .water-drop .drop .icon {
		position: relative;
		display: inline-block;
		margin-left: 10upx;
		width: 20upx;
		height: 34upx;
		vertical-align: middle;
		// background: url(../../static/img/water.png) no-repeat bottom right;
		background-size: contain;
	}

	.canvas .plante-progress .water-drop .drop .plus {
		position: absolute;
		top: 0upx;
		right: -12upx;
		font-size: 32upx;
		opacity: 0;
		color: #ffbe2d;
	}
</style>

<style lang="scss">

.Welcome-page {
	.uni-popup {
		.uni-popup__wrapper {
			margin-top: 98rpx;
		}
	}
}

.ReplacePrize-page {
	.uni-popup {
		.uni-popup__wrapper {
			margin-top: 98rpx;
		}
	}
}
</style>