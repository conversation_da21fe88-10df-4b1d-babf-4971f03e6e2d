<template>
  <view>
    <view 
		class="cu-modal" 
		:class="changeModalDialog?'show':''" 
    @tap="hideModalDialog" 
		>
			<view 
				class="cu-dialog" 
				:class="changeModalDialog?'animation-slide-center':''" 
        style="width: 723rpx; border-radius: 45rpx;"
				@tap.stop
			>
        <!--赠品、盲盒类型展示  prizeType 1:优惠券 2:盲盒 3:赠品 4:线下券-->
        <!-- 拆开后商品结果 -->
        <view 
          v-if="openPrize" 
          class="animate__animated animate__fadeInUp" 
          style="width: 723rpx;height:768rpx;margin:0 auto;margin-top: 38rpx;position: relative;">
          <image
            style="width:100%;height: 100%;border-radius:15rpx;"
            :src="fullLevelPrize.prizeType!='3'?waterTemplate.templateComponent.openBlindBoxUrl:waterTemplate.templateComponent.completeUrl"
            mode="aspectFill" 
            >
          </image>
          
          <view style="position: absolute;top:200rpx;left: 0;right: 0;">
            <view class="text-df text-bold" style="color: #994124;">
              获得{{prize.name}}商品
            </view>
            <view class="margin-bottom-lg" style="color: #994124;">
              需要去指定门店领取，点击查看我的奖品查看奖品信息
            </view>
            <view class="flex justify-center">
              <view style="width: 247upx; height: 247upx">
                <image
                  style="width:100%;height: 100%;border-radius:15rpx;"
                  :src="prize.url" 
                  mode="aspectFill" 
                  >
                </image>
              </view>
            </view>
    
            <view class="flex justify-center margin-lg">
              <view style="position: relative;" @click="goMyPrize">
                <image
                  style="width: 239upx;height: 77upx;"
                  :src="'https://img.songlei.com/paradise/task/guxdbtn.png'" 
                  mode="aspectFill" 
                  >
                </image>
                <view class="text-bold" style="position: absolute;width:178rpx ;z-index: 1;top: 40%;left: 50%;transform: translate(-50%, -50%);color: #994124;">
                查看我的奖品
                </view>
              </view>

              <view v-if="fullLevelPrize.prizeType=='3'" style="position: relative;" class="margin-left-sm" @click="hideModalDialog()">
                <view class="text-bold" style="position: absolute;z-index: 1;top: 40%;left: 50%;transform: translate(-50%, -50%);color: #994124;">
                关闭
                </view>
                <image
                  style="width: 239upx;height: 77upx;"
                  :src="'https://img.songlei.com/paradise/task/guxdbtn.png'" 
                  mode="aspectFill" 
                  >
                </image>
              </view>

              <view v-else style="position: relative;" class="margin-left-sm" @click="toUse(fullLevelPrize)">
                <view class="text-bold" style="position: absolute;z-index: 1;top: 40%;left: 50%;transform: translate(-50%, -50%);color: #994124;">
                去使用
                </view>
                <image
                  style="width: 239upx;height: 77upx;"
                  :src="waterTemplate.templateComponent.completeUrl" 
                  mode="aspectFill" 
                  >
                </image>
              </view>
            </view>
          </view>
        </view>
        <!--赠品、盲盒类型展示  prizeType 1:优惠券 2:盲盒 3:赠品 4:线下券-->
        <!-- 开始拆盲盒 -->
        <view v-else style="width: 723rpx;height:768rpx;margin:0 auto;margin-top: 38rpx;position: relative;">
          <image
            style="width:100%;height: 100%;border-radius:15rpx;"
            :src="waterTemplate.templateComponent.completeUrl" 
            mode="aspectFill" 
            >
          </image>
          
          <view style="position: absolute;top:200rpx;left: 0;right: 0;">
            <view class="margin-bottom-lg" style="color: #994124;">
              可以开始拆盲盒啦！！！
            </view>
            <view class="flex justify-center">
              <view style="width: 247upx; height: 247upx">
                <image
                  style="width:100%;height: 100%;border-radius:15rpx;"
                  :src="prize.url" 
                  mode="aspectFill" 
                  >
                </image>
              </view>
            </view>
    
            <view class="flex justify-center margin-lg">
              <view style="position: relative;" @click="openBlindBox()">
                <image
                  style="width: 239upx;height: 77upx;"
                  :src="'https://img.songlei.com/paradise/task/guxdbtn.png'" 
                  mode="aspectFill" 
                  >
                </image>
                <view class="text-bold" style="position: absolute;z-index: 1;top: 40%;left: 50%;transform: translate(-50%, -50%);color: #994124;">
                拆盲盒
                </view>
              </view>

              <view style="position: relative;" class="margin-left-sm" @click="hideModalDialog()">
                <view class="text-bold" style="position: absolute;z-index: 1;top: 40%;left: 50%;transform: translate(-50%, -50%);color: #994124;">
                关闭
                </view>
                <image
                  style="width: 239upx;height: 77upx;"
                  :src="'https://img.songlei.com/paradise/task/guxdbtn.png'" 
                  mode="aspectFill" 
                  >
                </image>
              </view>
            </view>
          </view>
        </view>
			</view>

      <block v-if="writeShow">
        <write-off :coupon-no="prizeUserId" @close="closeWriteDia" />
      </block>
		</view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月18日
 **/

import { openBlindBoxApi } from "@/pages/squirrelParadise/api/squirrelParadise";
import { navigateUtil } from "@/static/mixins/navigateUtil.js";
import WriteOff from "./WriteOff";
import { senTrack, getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'
export default {
  mixins: [navigateUtil],

  components: {
    WriteOff,
  },

  props: {
    //中奖数据
    fullLevelPrize: {
      type: Object,
      require: true,
      default() {
        return {};
      },
    },

    //游戏模版配置
			waterTemplate: {
				type: Object,
				require: true,
				default() {
					return {};
				},
			},

    // 控制弹框
    changeModalDialog: {
      type: Boolean,
      require: true,
      default:false,
    },

    //活动配置
		waterInfo: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      prize:this.fullLevelPrize,
      openPrize:false,
      writeShow: false,
      prizeUserId: "",
      iconPic: {
      },
      // 神策埋点
      trackParams: {
        page_name: "松鼠乐园",
        forward_source: getCurrentTitle(0),
        page_level: '一级',
        activity_id: "",
        activity_name: "",
        is_obtain_prize: true,
        activity_type_first: "营销活动",
        activity_type_second: "松鼠乐园",
      },
    };
  },

  mounted() {
  },
  methods: {

    //拆盲盒
    async openBlindBox(){
      let params = {
        waterInfoId:this.fullLevelPrize.waterInfoId,
        prizeId:this.fullLevelPrize.id
      }
      try {
        const res = await openBlindBoxApi(params)
        let {data} = {...res}
        if (data) {
          this.openPrize = true;
          this.prize = data;
          const { name } = this.prize 
          // 先调用senTrack上报
          senTrack("ActivityResult", {
            ...this.trackParams,
            activity_id: this.fullLevelPrize.waterInfoId,
            activity_name: "松鼠乐园",
            prize_name: name || '',
            prize_id: this.fullLevelPrize.id,
          });
        }
        console.log("data====>",data);
      } catch (error) {
        console.log(error);
      }
    },

    //关闭弹框
		hideModalDialog() {
      this.$emit('hideModalDialog',false)
		},

    // 我的奖品
    goMyPrize() {
      uni.navigateTo({
        url: `/pages/myPrize/index?waterInfoId=${this.fullLevelPrize.waterInfoId}`,
      });
    },

    //劵，去使用
    toUse(){
      const { prizeType, prizeValue, couponNo, name, waterInfoId, id } = this.prize;
      // 先准备要传给senTrack的数据
      let prizeId = '';
      switch (prizeType) {
        case '1':
          prizeId = prizeValue || '';
          break;
        case '4':
          prizeId = couponNo || '';
          break;
        default:
          prizeId = id || '';
      }
      // 先调用senTrack上报
      senTrack("ActivityResult", {
        ...this.trackParams,
        activity_id: waterInfoId,
        activity_name: this.waterInfo.name,
        prize_name: name || '',
        prize_id: prizeId,
      });
      // 再根据prizeType处理跳转或显示
      if (prizeType == '1') {
        uni.navigateTo({
          url: "/pages/goods/goods-list/index?couponId=" + prizeValue,
        });
      } else if (prizeType == '4') {
        this.prizeUserId = couponNo;
        this.writeShow = true;
      }
    },

    closeWriteDia() {
      this.writeShow = false;
    },
  },
};
</script>
<style scoped lang="scss">
</style>
