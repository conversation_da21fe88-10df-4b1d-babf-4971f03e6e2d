<template>
	<view class="progress-bar">
		<view class="progress-bar-title" :style="titleStyle">{{ title }}</view>

		<view v-if="!secondValue" class="bar" :style="
		{
			position: textPosition == 'middle'?'relative':''
		}
		">
			<view class="bar-single" :style="{
					backgroundColor: backColor,
					borderRadius: barRadius,
					height: barHeight,
					lineHeight: barHeight
				}">
				<view class="bar-content" :style="{ width: firstWidth, minWidth: minWidth }">
					<view :class="{ 'bar-content-inner': animationReal }" :style="{ background: contentColor, margin:'2rpx', height:'40rpx', borderRadius: firstValue<total-0.05?'20rpx 0 0 20rpx':'20rpx' }">
						<text v-if="textPosition === 'inside'" class="bar-text"
							:style="{ color: textColor, fontSize: textSize }">
							{{ singleText }}
						</text>
						<text v-else class="placeholder">1</text>
					</view>
				</view>
			</view>
			<view v-if="textPosition === 'outside'" class="bar-text" :style="{ color: textColor, fontSize: textSize }">
				{{ singleText }}
			</view>
			<view v-if="textPosition === 'middle'" class="bar-text"
				:style="{ color: textColor, fontSize: textSize, position:'absolute', left:'33%' }">
				{{ singleText }}
			</view>
		</view>

		<view v-else class="bar">
			<view class="bar-single bar-double" :style="{
					backgroundColor: backColor,
					borderRadius: barRadius,
					height: barHeight,
					lineHeight: barHeight
				}">
				<view class="bar-content" :style="{ width: firstWidth, minWidth: minWidth }">
					<view :class="{ 'bar-content-inner': animationReal }" :style="{ background: contentColor }">
						<text v-if="textPosition !== 'none'" class="bar-text"
							:style="{ color: textColor, fontSize: textSize }">
							{{ singleText }}
						</text>
						<text v-else class="placeholder">1</text>
					</view>
				</view>
				<view class="bar-interval placeholder">1</view>
				<view class="bar-content" :style="{ width: secondWidth, minWidth: minWidth }">
					<view :class="{ 'bar-content-inner': animationReal }" :style="{ background: contentColor2 }">
						<text v-if="textPosition !== 'none'" class="bar-text"
							:style="{ color: textColor, fontSize: textSize }">
							{{ doubleText }}
						</text>
						<text v-else class="placeholder">1</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
				// 标题
				title: {
					type: String,
					// required: true
				},
				// 标题样式
				titleStyle: {
					type: String,
				},
				// 总数,计算的分母
				total: {
					type: Number,
					required: true
				},
				// 第一个值
				firstValue: {
					type: Number,
					required: true
				},
				// 第二个值
				secondValue: {
					type: Number
				},
				// 进度条背景颜色
				backColor: {
					type: String,
					default: '#f2f2f2'
				},
				// 进度条高度
				barHeight: {
					type: String
				},
				// 进度条圆角
				barRadius: {
					type: String
				},
				// 进度条颜色
				contentColor: {
					type: String,
					default: '#4cd964'
				},
				// 进度条2颜色
				contentColor2: {
					type: String,
					default: '#f0ad4e'
				},
				// 进度条最小长度
				minWidth: {
					type: String
				},
				// 文字大小
				textSize: {
					type: String,
					default: '16px'
				},
				// 文字颜色
				textColor: {
					type: String,
					default: '#333333'
				},
				// 文字内显、外显、不显，可选值：inside/outside/none
				textPosition: {
					type: String,
					default: 'inside'
				},
				// 展示百分比或值
				percentum: {
					type: Boolean,
					default: false
				},
				// 百分比精确到小数点后几位
				precision: {
					type: [String, Number],
					default: 2
				},
				// 是否选择动画加载
				animation: {
					type: Boolean,
					default: false
				}
			},
		data() {
			return {
				singleText: '',
				doubleText: '',
				firstWidth: '',
				secondWidth: '',
				animationReal: false
			};
		},
		watch: {
			firstValue: {
				handler(newVal) {
					// 进度条长度改变后,重新加载动画
					if (this.animationReal) {
						this.animationReal = false;
						this.firstWidth = this.manageWidth(0, 'singleText');
						setTimeout(() => {
							this.animationReal = true;
							this.firstWidth = this.manageWidth(
								newVal,
								'singleText'
							);
						}, 100);
					} else {
						this.firstWidth = this.manageWidth(newVal, 'singleText');
					}
				},
				immediate: true
			},
			secondValue: {
				handler(newVal) {
					if (this.animationReal) {
						this.animationReal = false;
						this.secondWidth = this.manageWidth(0, 'doubleText');
						setTimeout(() => {
							this.animationReal = true;
							this.secondWidth = this.manageWidth(
								newVal,
								'doubleText'
							);
						}, 100);
					} else {
						this.secondWidth = this.manageWidth(newVal, 'doubleText');
					}
				},
				immediate: true
			},
			animation: {
				handler(newVal) {
					this.animationReal = newVal;
				},
				immediate: true
			}
		},
		methods: {
			manageWidth(target, flag) {
				let value =
					((target / this.total) * 100).toFixed(this.precision) + '%';
				this.percentum ? (this[flag] = value) : (this[flag] = target);
				// 减去间隔所占宽度
				this.secondValue ? (value = `calc(${value} - 1px)`) : '';
				return value;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.progress-bar {
		&-title {
			font-size: 4vw;
			font-weight: bold;
			margin-bottom: 1vw;
		}

		.bar {
			/* #ifdef H5 */
			cursor: pointer;
			/* #endif */
			display: flex;
			align-items: center;
			justify-content: space-between;

			&-single {
				overflow: hidden;
				width: 100vw;
			}

			&-double {
				display: flex;
				align-items: center;
				justify-content: left;
			}

			&-content {
				&-inner {
					animation: addWidth 1.5s 0s;
				}
			}

			&-interval {
				background-color: #ffffff;
				width: 2px;
			}

			&-text {
				margin: 0 5px;
				font-size: 4vw;
			}

			.placeholder {
				color: rgba(0, 0, 0, 0);
				font-size: 4vw;
			}
		}
	}

	@keyframes addWidth {
		from {
			width: 0;
		}

		to {
			width: 100%;
		}
	}
</style>