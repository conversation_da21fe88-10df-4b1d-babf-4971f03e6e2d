/**
 * <AUTHOR>
 * @date 2023年9月11日
 **/
import { requestApi as request } from "@/utils/api.js";
import util from "utils/util.js";

// 测试调用会让浇水数量变为100 已浇数量变为0
export function testApi(id) {
  return request({
    url: "/mallmarket/water/test?id=" + id,
    showLoading: false,
    method: "get",
  });
}

// 用户进入活动页面
export function waterTreeApi(data = {}) {
  const scene = util.getSharerUserCode(true);
  if (scene) {
    data = {
      ...data,
      shareUlserld: scene,
    };
  }
  return request({
    url: "/mallmarket/water/waterTree",
    method: "get",
    showLoading: false,
    data,
  });
}

// 选择奖品
export function selectPrizeApi(data) {
  return request({
    url: "/mallmarket/water/selectPrize",
    method: "post",
    data,
  });
}

// 更换奖品查看
export function viewPrizeApi(data) {
  return request({
    url: "/mallmarket/water/viewPrize",
    method: "get",
    showLoading: false,
    data,
  });
}

// 用户浇水
export function waterApi(id) {
  return request({
    url: "/mallmarket/water/water?warterInfoId=" + id,
    showLoading: false,
    method: "get",
  });
}

// 拆盲盒
export function openBlindBoxApi(data) {
  return request({
    url: "/mallmarket/water/blindBox",
    method: "get",
    data,
  });
}

// 分享前查询助力信息
export function shareApi(data) {
  return request({
    url: "/mallmarket/watertaskuser/share",
    method: "get",
    data,
  });
}

// 签到、浏览商品、浏览店铺、扫码完成
export function doneApi(data) {
  return request({
    url: "/mallmarket/watertaskuser/done",
    method: "get",
    data,
  });
}

// 任务列表
export function taskListApi(data) {
  return request({
    url: "/mallmarket/water/taskList",
    showLoading: false,
    method: "get",
    data,
  });
}

// 品牌助力信息
export function brandHelpApi(data) {
  return request({
    url: "/mallmarket/watertaskuser/brandHelp",
    method: "get",
    data,
  });
}

// 浏览商品信息接口
export function browseProductsApi(data) {
  return request({
    url: "/mallmarket/watertaskuser/browseProducts",
    method: "get",
    data,
  });
}

// 我的下单奖励
export function orderRewardApi(data) {
  return request({
    url: "/mallmarket/watertaskuser/orderReward",
    method: "get",
    data,
  });
}

// 领取奖品
export function receiveApi(data) {
  return request({
    url: "/mallmarket/water/receive",
    method: "get",
    data,
  });
}

// 红包雨任务完成
export function redRainApi(data) {
  return request({
    url: "/mallmarket/watertaskuser/redPackage",
    method: "post",
    data,
  });
}
