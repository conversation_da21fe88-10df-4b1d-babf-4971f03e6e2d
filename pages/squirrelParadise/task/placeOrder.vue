<template>
  <view class="placeOrder-page">
    <cu-custom
      bgColor="rgba(255, 255, 255, 0)"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content" style="color: #000">购物下单</block>
    </cu-custom>
    <image
      class="navbg"
      :src="waterTemplate.templateComponent.prizeTopUrl"
      :style="{ height: CustomBar + 'px' }"
    />
    <view class="core-box">
      <view class="one-box">购物返{{waterTemplate.tip2}}规则</view>
      <view class="two-box">
        <view class="two">
          {{ placeRule }}
        </view>
        <view class="two" v-for="(u, i) in ruleList" :key="i">{{ u }}</view>
      </view>
      <view class="three-box" @click="goRewardList">
        <view class="wen">我的下单奖励</view>
        <image class="btn" :src="iconPic.guxdbtn" @click="close" />
      </view>
      <image class="bg" :src="waterTemplate.templateComponent.shoppingPopUrl" />
    </view>
    <scroll-view
      class="verticalmain"
      scroll-y
      scroll-with-animation
      @scrolltolower="reachBottom"
      :style="{
        height: `calc(100vh - ${CustomBar + 100}rpx)`,
      }"
      :scroll-into-view="'main-' + MainCur"
      lower-threshold="100"
    >
      <goods-waterfall
        :fullWidth="750"
        :goodsList="goodsList"
        :loadMore="loadMore"
      />
      <view
        style="width: 750rpx"
        :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"
      >
      </view>
    </scroll-view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import goodsWaterfall from "components/goods-waterfall/index";

import util from "@/utils/util";
import api from "@/utils/api";

export default {
  components: {
    goodsWaterfall,
  },
  data() {
    return {
      placeRule: "",
      ruleList: [],
      waterInfoId: "",
      spuGroupId: "",
      MainCur: 0,
      CustomBar: this.CustomBar,
      goodsList: [],
      loadmore: true,
      page: {
        searchCount: false,
        current: 1,
        size: 20,
        ascs: "",
        descs: "",
      },
      waterTemplate:{},//游戏模版
      iconPic: {
        guxdbtn: "http://img.songlei.com/paradise/task/guxdbtn.png",
      },
    };
  },
  onLoad(options) {
    const res = JSON.parse(decodeURIComponent(options.placeParam));
    this.waterTemplate = uni.getStorageSync('waterTemplate');
    this.waterInfoId = res.waterInfoId;
    this.placeRule = res.placeRule;
    this.spuGroupId = res.spuGroupId;
    this.ruleHandle(res.buyRewardNums);
    this.goodsPage(true);
  },
  methods: {
    ruleHandle(arr) {
      this.ruleList = [];
      arr.forEach((u, i) => {
        if (i + 1 == arr.length) {
          let str = `订单金额满${u.minAmount}元以上，奖励${u.reward}${this.waterTemplate.tip2},`;
          this.ruleList.push(str);
        } else {
          let str = `订单金额满${u.minAmount}元至${u.maxAmount}元之间，奖励${u.reward}${this.waterTemplate.tip2},`;
          this.ruleList.push(str);
        }
      });
    },
    goRewardList() {
      uni.navigateTo({
        url: `/pages/squirrelParadise/task/rewardList?waterInfoId=${this.waterInfoId}`,
      });
    },

    reachBottom() {
      if (this.loadmore) {
        util.throttle(this.getMore(), 1500);
      }
    },
    getMore() {
      this.page.current = this.page.current + 1;
      this.goodsPage();
    },
    goodsPage(fresh) {
      const params = {
        ...this.page,
        goodsGroupIds: this.spuGroupId,
      };
      api.goodsGroupsGet(params).then((res) => {
        let goodsList = res.data.records;
        if (fresh) {
          this.goodsList = goodsList;
        } else {
          this.goodsList = [...this.goodsList, ...goodsList];
        }
        if (goodsList.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.placeOrder-page {
  position: fixed;
  z-index: 10;
  background-color: #f9f9f9;
  .navbg {
    position: fixed;
    width: 750rpx;
    top: 0;
  }
  .core-box {
    position: relative;
    width: 750rpx;
    height: 515rpx;
    padding-top: 100rpx;
    .one-box {
      text-align: center;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #9f4227;
    }
    .two-box {
      width: 550rpx;
      height: 220rpx;
      overflow: auto;
      margin: auto;
      .two {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #9f4227;
        margin-bottom: 5rpx;
      }
    }
    .three-box {
      position: relative;
      margin: auto;
      margin-top: 20rpx;
      width: 215rpx;
      height: 77rpx;
      text-align: center;
      .wen {
        padding-top: 10rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #9f4227;
      }
      .btn {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        width: 215rpx;
        height: 77rpx;
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 750rpx;
      height: 515rpx;
    }
  }
}
</style>
