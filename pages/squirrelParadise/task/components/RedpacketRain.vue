<template>
  <view class="RedpacketRain-page">
    <view v-show="isActivity">
      <view class="r-title" style="">
        <image class="r-img" :src="waterTemplate.templateComponent.redPacketgobackBg" />
        <view class="r-packets">已抢红包：{{ clickedPackets.length }}</view>
        <view class="r-countdown">倒计时：{{ countdown }}</view>
      </view>
      <view class="r-wrapper">
        <view
          class="r-content"
          :style="{
            top: `${rcTop}rpx`,
            left: `${rcLeft}rpx`,
            transition: `all ${totalTime}s linear`,
          }"
        >
          <view 
            class="r-item" 
            v-for="(packet, index) in redPackets"
            :key="packet.id" 
            :style="{ left: `${packet.x}rpx`, top: `${packet.y}rpx`, }"
          >
            <view class="packet-text" v-if="packet.isAnimating" >
              <text v-if="packet.prizeType == '积分'">+ {{ packet.prize }} 积分</text>
              <text v-else-if="packet.prizeType == '空的'">空的 ～</text>
              <text v-else>+ {{ packet.prize }}</text>
            </view>
            <image
              v-show="!packet.isAnimating"
              :src="iconPic.packet"
              class="r-packet"
              
              @click="handlePacketClick(packet, index)"
            />
            <image
              v-show="packet.isAnimating"
              :src="iconPic.openPacket"
              :class="{ 'opening-packet': packet.isAnimating }"
              class="r-packet"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
	import { redRainApi } from '@/pages/squirrelParadise/api/squirrelParadise'
export default {
  props: {
    taskId: {
      type: String,
      require: true,
      default: "",
    },
    info: {
      type: Object,
      require: true,
      default: () => {},
    },
    //游戏模版配置
    waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      speed: 1,
      isActivity: false,
      redPackets: [],
      pId: 0,
      rcTop: 0,
      rcLeft: 0,
      totalTime: this.info.browseTime || 30, // 活动持续时间 秒
      iconPic: {
        title: 'http://img.songlei.com/paradise/redpackage/red-packet-goback-bg.png', // 标题
        packet: this.info.taskImg || "http://img.songlei.com/paradise/redpackage/red-packet.png",
        openPacket: this.info.openStyleImg || "http://img.songlei.com/paradise/redpackage/oprn-redpacket.png",
      },
      clickedPackets: [], // 存储被点击的红包信息
      screenWidth: 667, // 屏幕宽度
      countdown: 0, // 倒计时
      // prizeTypes: ['积分', '空的'], // 添加奖品类型
    };
  },
  mounted() {
    this.start();
  },
  methods: {
    // 活动开始
    async start() {
      this.isActivity = true;
      let sY = 0;

      const packetWidth = 136; // 红包的宽度，根据实际情况调整
      let waterDropCount = 0; // 记录已生成的水滴红包数量

      for (let i = 0; i < this.totalTime * this.speed; i++) {
        // 每个水平位置上放置两个红包
        const rowNum = 3;
        for (let j = 0; j < rowNum; j++) {
          const section = this.screenWidth / rowNum;
          const base = j * section;
          const xPosition = base + Math.random() * (section - packetWidth); // 确保红包完全在可视区域内

          // 随机设置红包奖品
          let prizeType = '';
          let prize = '';
          // 判断是否生成水滴红包
          if (waterDropCount < this.info.packUpperLimit && Math.random() < 0.5) {
            prizeType = '道具';
            prize = 1;
            waterDropCount++;
          } else if (this.info.packExceedLimit > 0) {
            // 随机生成其他奖品类型
            // const prizeIndex = Math.floor(Math.random() * (this.prizeTypes.length));
            prizeType = '积分' // this.prizeTypes[prizeIndex];
            prize = this.info.packExceedLimit;
          } else {
            prizeType = '空的';
            prize = 0;
          }

          this.redPackets.push({
            x: xPosition,
            y: sY + Math.random() * 70, // 垂直距离上70的距离随机分布
            id: this.pId++,
            prizeType,
            prize
          });

        }
        // 每行红包垂直间距200
        sY += 400;
      }

      // 红包雨开始的位置
      this.rcTop = 0 - sY;
      this.rcLeft = 0; 

      this.handleCountdown(); // 倒计时

      setTimeout(() => {
        // 红包雨结束时的位置
        this.rcTop = 0;
        this.rcLeft = 0;
      }, 100);

    },

    // 倒计时
    handleCountdown() {
      this.countdown = this.totalTime; // 红包倒计时
      // 设置倒计时
      const intervalId = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown -= 1;
        } else {
          clearInterval(intervalId); // 倒计时结束，清除定时器
          this.handleEnd();
        }
      }, 1000); // 每秒更新一次倒计时
    },

    // 红包雨结束
    async handleEnd() {
      try {
        this.isActivity = false;
        this.redPackets = [];
        this.pId = 0;
        let bonusPoint = []
        let stageProps = []
        if (this.clickedPackets.length) {
          bonusPoint = this.clickedPackets.filter(p => p.prizeType === '积分')
          stageProps = this.clickedPackets.filter(p => p.prizeType === '道具')
        }
        const points = bonusPoint.length ? bonusPoint.reduce((total, item) => total + item.prize, 0) : 0;
        const res = await redRainApi({ taskId: this.info.id, redPacket: stageProps.length, points});
        this.$emit("endCountdown", { 
          packetNum: this.clickedPackets.length, 
          points: res.data.points || 0,
          propNum: res.data.propNum || 0,
          joinNum: res.data.joinNum || 0,
        });
      } catch (error) {
        console.log(error);
      }
    },

    // 点击红包获取奖品
    handlePacketClick(packet, index) {
      // this.redPackets = this.redPackets.map(p => {
      //   if (p.id === packet.id) {
      //     return { ...p, isAnimating: true }; // 假设透明度降低到0.5
      //   }
      //   return p;
      // });

      this.$set(this.redPackets, index, {...packet, isAnimating: true})

      if (packet.prizeType !== '空的') {
        if (this.clickedPackets.length === 0) {
          // 如果 clickedPackets 为空，则直接 push
          this.clickedPackets.push({ ...packet });
        } else {
          const existingIndex = this.clickedPackets.findIndex(p => p.id === packet.id);

          if (existingIndex !== -1) {
            // 如果已存在具有相同 id 的红包，则替换
            this.clickedPackets.splice(existingIndex, 1, { ...packet });
          } else {
            // 否则，添加到 clickedPackets
            this.clickedPackets.push({ ...packet });
          }
        }
      }
    },
  },
};
</script>
<style scoped lang="scss">
.RedpacketRain-page {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  min-height: 100vh;

  .r-title{
    width: 667rpx; 
    height: 357rpx; 
    position: absolute; 
    top: 164rpx;
    left: 50%;
    transform: translateX(-50%);
    .r-img{
      width: 667rpx; 
      height: 357rpx; 
    }
    .r-countdown{
      position: absolute;
      bottom: 64rpx;
      right: 68rpx;
      color: #9F4227;
      font-size: 30rpx;
    }
    .r-packets{
      position: absolute;
      bottom: 64rpx;
      left: 141rpx;
      color: #9F4227;
      font-size: 30rpx;
    }
  }
  .r-wrapper{
    position: fixed;
    top: 521rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 999;
    width: 667rpx;
    min-height: 100vh;
    overflow: hidden;
  }
  .r-content {
    position: relative;
  }
  .r-item{
    position: absolute;
    width: 136rpx;
    height: 194rpx;
    .r-packet {
      width: 136rpx;
      height: 194rpx;
    }
    .packet-text {
      width: 200rpx;
      text-align: center;
      position: absolute;
      animation: packet-fade-up .5s forwards;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      color: #ffd816;
      font-size: 40rpx;
      font-weight: 600;
      z-index: 9999;
    }
  }
}
@keyframes packet-fade-up {
  0% { top: 100rpx; }
  5% { top: 60rpx; }
  100% { top: -20rpx; }
}
@keyframes openPacket {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  50% {
    transform: scale(1); /* 稍微放大红包 */
  }
  100% {
    transform: scale(1.5); /* 进一步放大并准备淡出 */
    opacity: 0.5; /* 红包最终透明度为 0.3 */
  }
}
.opening-packet {
  animation: openPacket .3s forwards; /* 动画持续时间和保持结束状态 */
}
</style>
