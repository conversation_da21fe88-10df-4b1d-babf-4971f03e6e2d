<template>
  <view class="BrandAssistanceEntry-page">
    <uni-popup
      ref="popup"
      @maskClick="close"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box">
        <image class="guan" :src="iconPic.guan" @click="close" />
        <view class="one">
          浏览{{ brandName }}品牌首页{{ browseTime }}秒 将送您
          {{ giveWaterNum }}{{waterTemplate.tip2}}
        </view>
        <image class="two" mode="aspectFit" :src="imgUrl" />
        <view class="three" @click="goShopDetail">
          <view class="wen">立即领取</view>
          <image class="btn" :src="iconPic.huang2" />
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.brandUrl" />
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import { brandHelpApi } from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  props: {
    taskId: {
      type: String,
      require: true,
      default: "",
    },
    browseTime: {
      type: Number,
      require: true,
      default: "",
    },
    //游戏模版配置
    waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      brandName: "",
      giveWaterNum: "",
      imgUrl: "",
      shopId: "",
      iconPic: {
        guan: "http://img.songlei.com/paradise/task/guan.png",
        huang2: "http://img.songlei.com/paradise/task/huang2.png",
      },
    };
  },
  mounted() {
    this.brandHelp();
    this.$refs.popup.open("center");
  },
  methods: {
    brandHelp() {
      const params = {
        taskId: this.taskId,
      };
      brandHelpApi(params).then((res) => {
        const { brandName, giveWaterNum, imgUrl, shopId } = res.data;
        this.brandName = brandName;
        this.giveWaterNum = giveWaterNum;
        this.imgUrl = imgUrl;
        this.shopId = shopId;
      });
    },
    goShopDetail() {
      this.close();
      uni.navigateTo({
        url: `/pages/shop/shop-detail/index?id=${this.shopId}&taskId=${this.taskId}&browseTime=${this.browseTime}`,
      });
    },
    close() {
      this.$refs.popup.close();
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.BrandAssistanceEntry-page {
  .core-box {
    position: relative;
    width: 648rpx;
    height: 515rpx;
    text-align: center;
    padding-top: 120rpx;
    .guan {
      position: absolute;
      top: 60rpx;
      right: 10rpx;
      width: 67rpx;
      height: 94rpx;
    }
    .one {
      width: 400rpx;
      height: 96rpx;
      margin: auto;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 800;
      color: #a04527;
    }
    .two {
      width: 270rpx;
      height: 100rpx;
      margin-top: 10rpx;
      border-radius: 20rpx;
    }
    .three {
      position: relative;
      margin: auto;
      margin-top: 10rpx;
      width: 239rpx;
      height: 77rpx;
      .wen {
        padding-top: 10rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #9f4227;
      }
      .btn {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        width: 239rpx;
        height: 77rpx;
      }
    }

    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 648rpx;
      height: 515rpx;
    }
  }
}
</style>
