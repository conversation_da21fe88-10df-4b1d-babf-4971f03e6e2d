<template>
  <view class="InviteFriends-page">
    <uni-popup
      ref="friendPopup"
      @maskClick="friendClose"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box">
        <image class="guan" :src="iconPic.guan" @click="friendClose" />
        <view class="xinxi-box">
          <block v-if="allFlag">
            <view class="xinxi">今日好友助力全部完成</view>
            <view class="xinxi">奖励{{ giveWaterNum }}{{waterTemplate.tip2}}</view>
          </block>
          <block v-else>
            <view class="xinxi">{{ inviteData.name }}</view>
          </block>
        </view>
        <view class="wen-box">当前邀请微信好友：</view>
        <view class="cell-box">
          <view class="cell" v-for="u in friendList" :key="u.val">
            <view class="tu-box">
              <image class="tu" :src="u.headImg" v-if="u.stateFlag" />
              <text v-else>+</text>
            </view>
            <view class="zi">{{ u.nickName }}</view>
          </view>
        </view>
        <view class="other-box" v-if="allFlag">
          明日再来浏览还可得{{ tomorrowWaterNum }}{{waterTemplate.tip2}}哦
        </view>
        <view class="btn-outbox" v-if="allFlag">
          <view class="btn-box" @click="friendClose">
            <view class="wen">关闭</view>
            <image class="btn" :src="iconPic.huang2" />
          </view>
        </view>
        <view class="btn-outbox" v-else>
          <!-- #ifdef MP -->
          <view class="btn-box" @click="friendClose">
            <button class="wen" open-type="share">邀请好友</button>
            <image class="btn" :src="iconPic.huang2" />
          </view>
          <!-- #endif -->
          <!-- #ifndef MP -->
          <view class="btn-box" @click="onShareWX">
            <button class="wen">邀请微信好友</button>
            <image class="btn" :src="iconPic.huang2" />
          </view>
          <!-- #endif -->
          <view class="btn-box" @click="onCreatePoster">
            <view class="wen">生成海报</view>
            <image class="btn" :src="iconPic.huang2" />
          </view>
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.shareUrl" />
      </view>
    </uni-popup>
    <uni-popup
      ref="posterPopup"
      @maskClick="posterClose"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="haibao-box">
        <view class="tou-box" v-if="successFlag">
          <image class="tuxiang" :src="iconPic.duigou" />
          <view class="wenzi">海报已保存到您手机相册</view>
        </view>
        <view class="main-box">
          <image class="haibao" :src="posterUrl" />
        </view>
        <view class="anniu-box" @click="posterClose" v-if="successFlag">
          <view class="wen">完成</view>
          <image class="btn" :src="iconPic.huang3" />
        </view>
        <view style="display: flex" v-else>
          <view class="anniu-box" @click="savePoster">
            <view class="wen">保存到相册</view>
            <image class="btn" :src="iconPic.huang3" />
          </view>
          <view class="anniu-box" @click="posterClose">
            <view class="wen">关闭</view>
            <image class="btn" :src="iconPic.huang3" />
          </view>
        </view>
      </view>
    </uni-popup>
    <poster
      id="poster"
      ref="posterRef"
      :hide-loading="false"
      :preload="false"
      :config="posterConfig"
      @success="onPosterSuccess"
      @fail="onPosterFail"
    />
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import poster from "@/components/wxa-plugin-canvas/poster/index";
const app = getApp();
const { base64src } = require("utils/base64src.js");
import api from "utils/api";
import util from "@/utils/util";
import __config from "@/config/env";
import { shareApi } from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  components: {
    poster,
  },
  props: {
    inviteData: {
      type: Object,
      require: false,
      default: null,
    },
    //游戏模版配置
    waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      allFlag: false, // false未邀请完 true邀请完毕
      giveWaterNum: "",
      tomorrowWaterNum: "",
      friendList: [],

      // 海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
      posterConfig: {
        width: 514,
        height: 750,
        backgroundColor: "#fff",
        images: [],
        texts: [],
        lines: [],
      },
      posterUrl: "",
      successFlag: false,

      iconPic: {
        guan: "http://img.songlei.com/paradise/task/guan.png",
        huang2: "http://img.songlei.com/paradise/task/huang2.png",
        huang3: "http://img.songlei.com/paradise/task/huang3.png",
        duigou: "http://img.songlei.com/paradise/task/duigou.png",
      },
    };
  },
  mounted() {
    this.getShare();
    this.$refs.friendPopup.open("center");
  },
  methods: {
    getShare() {
      this.friendList = [];
      const params = {
        waterInfoId: this.inviteData.waterInfoId,
      };
      shareApi(params).then((res) => {
        let { giveWaterNum, tomorrowWaterNum, waterUserVos } = res.data;
        this.giveWaterNum = giveWaterNum;
        this.tomorrowWaterNum = tomorrowWaterNum;
        const len = waterUserVos.length;
        const sum = this.inviteData.shareNum; //每天邀请助力人数
        this.allFlag = len == sum ? true : false;
        waterUserVos.forEach((u) => {
          u.stateFlag = true;
          this.friendList.push(u);
        });
        if (len < sum) {
          for (let i = 0; i < sum - len; i++) {
            this.friendList.push({
              stateFlag: false,
              val: "",
              url: "",
            });
          }
        }
      });
    },
    friendClose() {
      this.$refs.friendPopup.close();
      this.$emit("close", this.allFlag,'onShare');
    },

    //app邀请微信好友
    onShareWX() {
      // uniapp官方API文档https://uniapp.dcloud.net.cn/api/plugins/share.html#share
      const userInfo = uni.getStorageSync("user_info");
      const userCode = userInfo ? "&sharer_user_code=" + userInfo.userCode : "";
      uni.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 5,
        href: '',
        title: this.inviteData.detail,
        summary: "",
        imageUrl: this.inviteData.taskImg,
        miniProgram: {
          id: __config.originAppid,
          path: `pages/squirrelParadise/home/<USER>
          type: 0,
          webUrl: "https://shopapi.songlei.com/",
          // type: 2,
          // webUrl: "https://site.songlei.com/",
          //根据环境变量设置type webUrl
        },
        success(res) {},
        fail(err) {
          uni.showModal({
            title: "提示",
            content: "正在开发中",
            showCancel: false,
          });
        },
      });
    },

    // 生成海报里的二维码
    onCreatePoster() {
      const userInfo = uni.getStorageSync("user_info");
      const userCode = userInfo ? "&" + userInfo.userCode : "";
      const params = {
        theme: app.globalData.theme, // 全局颜色变量
        page: util.getCurPage(getCurrentPages()), // 当前页面路径
        scene: `${this.inviteData.waterInfoId}${userCode}`,
      };
      api.qrCodeUnlimited(params).then((res) => {
        base64src(res.data, (res2) => {
          this.startCreatePoster(res2);
        });
      });
    },
    startCreatePoster(res) {
      // 开始 生成海报
      uni.hideLoading();

      const userInfo = uni.getStorageSync("user_info");

      if (userInfo && userInfo.headimgUrl) {
        // 如果有头像则显示
        this.posterConfig.images.push(
          {
            width: 480,
            height: 480,
            x: 17,
            y: 17,
            url: this.inviteData.taskImg,
          },
          {
            width: 44,
            height: 44,
            x: 17,
            y: 537,
            borderRadius: 22,
            url: userInfo.headimgUrl,
          },
          {
            width: 134,
            height: 134,
            x: 340,
            y: 536,
            url: res,
          }
        );
        this.posterConfig.texts.push(
          {
            x: 77,
            y: 560,
            baseLine: "middle",
            text: userInfo.nickName, // 手机号 或者名称
            fontSize: 28,
            fontWeight: 500,
            color: "#000",
          },
          {
            x: 17,
            y: 650,
            width: 230,
            lineNum: 2,
            baseLine: "middle",
            text: this.inviteData.detail,
            fontSize: 28,
            fontWeight: 500,
            color: "#000",
          },
          {
            x: 336,
            y: 678,
            width: 142,
            baseLine: "top",
            text: "扫描/长按识别",
            fontSize: 22,
            fontWeight: 500,
            color: "#606060",
          }
        );
        this.posterConfig.lines.push({
          startX: 305,
          endX: 305,
          startY: 554,
          endY: 690,
          width: 2,
          color: "#333",
        });
      } else {
        return;
      }
      this.$refs.friendPopup.close();
      this.$refs.posterRef.onCreate(false, this.posterConfig); // 入参：true为抹掉重新生成
    },
    onPosterSuccess(e) {
      this.posterUrl = e;
      this.$refs.posterPopup.open("center");
    },
    onPosterFail(err) {
      console.error(err);
    },
    posterClose() {
      this.$refs.posterPopup.close();
      this.$emit("close");
    },
    // 点击保存到相册
    savePoster() {
      const that = this;
      uni.saveImageToPhotosAlbum({
        filePath: this.posterUrl,
        success() {
          that.successFlag = true;
        },
      });
    },
  },
};
</script>
<style scoped lang="scss">
.InviteFriends-page {
  .core-box {
    position: relative;
    width: 723rpx;
    height: 761rpx;
    text-align: center;
    padding-top: 180rpx;
    .guan {
      position: absolute;
      top: 60rpx;
      right: 18rpx;
      width: 67rpx;
      height: 94rpx;
    }
    .xinxi-box {
      .xinxi {
        width: 470rpx;
        overflow-wrap: break-word;
        margin: auto;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #994124;
      }
    }
    .wen-box {
      width: 580rpx;
      text-align: left;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #994124;
      margin: auto;
      margin-top: 20rpx;
    }
    .cell-box {
      width: 630rpx;
      height: 190rpx;
      margin: auto;
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      .cell {
        margin: 0 10rpx;
        .tu-box {
          text-align: center;
          width: 144rpx;
          height: 144rpx;
          line-height: 144rpx;
          border-radius: 50%;
          background-color: #e8e6ec;
          .tu {
            width: 144rpx;
            height: 144rpx;
            border-radius: 50%;
          }
        }
        .zi {
          margin-top: 6rpx;
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #994124;
        }
      }
    }
    .other-box {
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #994124;
    }
    .btn-outbox {
      position: absolute;
      left: 90rpx;
      bottom: 90rpx;
      width: 550rpx;
      display: flex;
      .btn-box {
        position: relative;
        margin: auto;
        width: 239rpx;
        height: 77rpx;
        .wen {
          width: 239rpx;
          height: 77rpx;
          line-height: 50rpx;
          background-color: transparent;
          padding-top: 10rpx;
          font-size: 30rpx;
          font-family: PingFang SC;
          font-weight: 800;
          color: #9f4227;
        }
        .btn {
          position: absolute;
          z-index: -1;
          top: 0;
          left: 0;
          width: 239rpx;
          height: 77rpx;
        }
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 723rpx;
      height: 761rpx;
    }
  }
  .haibao-box {
    width: 515rpx;
    text-align: center;
    .tou-box {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: auto;
      margin-bottom: 20rpx;
      .tuxiang {
        width: 82rpx;
        height: 82rpx;
        margin-right: 6rpx;
      }
      .wenzi {
        font-size: 38rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #fffefe;
      }
    }
    .main-box {
      width: 514rpx;
      height: 750rpx;
      background: #ffffff;
      border-radius: 20rpx;
      .haibao {
        // max-width: none;
        width: 514rpx; // 根据控制台调整
        height: 750rpx; // 根据控制台调整
        border-radius: 20rpx;
      }
    }
    .anniu-box {
      position: relative;
      margin: auto;
      margin-top: 20rpx;
      width: 237rpx;
      height: 73rpx;
      .wen {
        width: 239rpx;
        height: 77rpx;
        background-color: transparent;
        padding-top: 10rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #9f4227;
      }
      .btn {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        width: 237rpx;
        height: 73rpx;
      }
    }
  }
}
</style>
