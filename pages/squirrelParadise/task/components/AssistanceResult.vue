<template>
  <view class="AssistanceResult-page">
    <uni-popup
      ref="popup"
      @maskClick="close"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box">
        <image class="guan" :src="iconPic.guan" @click="close" />
        <view class="one">{{ resultInfo.title }}</view>
        <view class="two">{{ resultInfo.subTitle }}</view>
        <image
          class="three"
          :style="resultInfo.yangshi"
          mode="aspectFit"
          :src="resultInfo.url"
        />
        <view class="four">{{ resultInfo.desc }}</view>
        <view class="five" @click="close">
          <view class="wen">{{ resultInfo.text }}</view>
          <image class="btn" :src="iconPic.huang2" />
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.assistanceUrl" />
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/

export default {
  props: {
    selectPrizeState: {
      type: Number,
      require: true,
      default: 0,
    },
    helpState: {
      type: Number,
      require: true,
      default: 0,
    },
    //游戏模版配置
    waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      resultInfo: null,
      iconPic: {
        guan: "http://img.songlei.com/paradise/task/guan.png",
        huang2: "http://img.songlei.com/paradise/task/huang2.png",
      },
    };
  },
  mounted() {
    this.handleCondition();
    this.$refs.popup.open("center");
  },
  methods: {
    handleCondition() {
      const TemplateType = this.waterTemplate.templateComponent.TemplateType
      let TemplateName = this.showActivity(TemplateType)
      switch (this.helpState) {
        case 0:
          this.resultInfo = {
            title: "助力失败",
            subTitle: "请稍后重试...",
            url: "http://img.songlei.com/paradise/task/zlsb.png",
            yangshi: "width:454rpx;height:369rpx;top:50%;",
            desc: "",
            text: "关闭",
          };
          break;
        case 1:
          this.resultInfo = {
            title: "已经来晚了",
            subTitle: "好友助力已完成",
            // url: "http://img.songlei.com/paradise/task/yjlwl.png",
            url: "http://img.songlei.com/paradise/task/snowman-yjlwl.png",
            yangshi: "width:401rpx;height:266rpx;top:50%;",
            // desc: "松鼠乐园种树免费得好礼活动火爆进行中现诚邀您来参加，礼品丰厚!",
            desc: `松鼠乐园${TemplateName}免费得好礼活动火爆进行中现诚邀您来参加，礼品丰厚!`,
            text: "马上了解",
          };
          break;
        case 2:
          this.resultInfo = {
            title: "已给其他好友助力",
            subTitle: "同一活动只能助力一次",
            // url: "http://img.songlei.com/paradise/task/ygqthyzl.png",
            // url: "http://img.songlei.com/paradise/task/snowman-ygqthyzl.png",
            url: "http://img.songlei.com/paradise/task/nian-ygqthyzl.png",
            yangshi: "width:391rpx;height:225rpx;top:52%;",
            // desc: "松鼠乐园种树免费得好礼活动火爆进行中现诚邀您来参加，礼品丰厚!",
            desc: `松鼠乐园${TemplateName}免费得好礼活动火爆进行中现诚邀您来参加，礼品丰厚!`,
            text: "马上了解",
          };
          break;
        case 3:
          this.resultInfo = {
            title: "助力成功!",
            subTitle: "",
            url: "http://img.songlei.com/paradise/task/zlcg.png",
            yangshi: "width:618rpx;height:707rpx;top:45%;",
            // desc: "松鼠乐园种树免费得好礼活动火爆进行中现诚邀您来参加，礼品丰厚!",
            desc: `松鼠乐园${TemplateName}免费得好礼活动火爆进行中，赶快来${TemplateName}吧！`,
            text: "马上了解",
          };
          if (1 == this.selectPrizeState) {
            //是否需要选择礼品 开始种树弹层 0 否 1是
            // this.resultInfo.desc ="松鼠乐园种树免费得好礼活动火爆进行中赶快来浇水吧！";
            this.resultInfo.desc =`松鼠乐园${TemplateName}免费得好礼活动火爆进行中赶快来${TemplateName}吧！`;
            this.resultInfo.text = "进入松鼠乐园";
          }
          break;
        case 4:
          this.resultInfo = {
            title: "已完成助力",
            subTitle: "无法重复助力",
            // url: "http://img.songlei.com/paradise/task/ywczl.png",
            // url: "http://img.songlei.com/paradise/task/snowman-ywczl.png",
            url: "http://img.songlei.com/paradise/task/nian-ywczl.png",
            yangshi: "width:391rpx;height:261rpx;top:50%;",
            // desc: "松鼠乐园种树免费得好礼活动火爆进行中现诚邀您来参加，礼品丰厚!",
            desc: `松鼠乐园${TemplateName}免费得好礼活动火爆进行中现诚邀您来参加，礼品丰厚!`,
            text: "马上了解",
          };
          break;
      }
    },

    showActivity(type) {
      let result = '';
      if (type == 1) {
        result = '种树';
      } else if (type == 2) {
        result = '堆雪人';
      } else if (type == 3) {
        result = '炸年兽';
      } else {
        result = '未知活动';
      }
      return result;
    },

    close() {
      this.$refs.popup.close();
      this.$emit("close", this.selectPrizeState);
    },
  },
};
</script>
<style scoped lang="scss">
.AssistanceResult-page {
  .core-box {
    position: relative;
    width: 723rpx;
    height: 768rpx;
    text-align: center;
    padding-top: 110rpx;
    .guan {
      position: absolute;
      top: 60rpx;
      right: 18rpx;
      width: 67rpx;
      height: 94rpx;
    }
    .one {
      font-size: 45rpx;
      font-family: PingFang SC;
      font-weight: 800;
      color: #994124;
    }
    .two {
      height: 40rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #994124;
    }
    .three {
      position: absolute;
      z-index: -1;

      left: 50%;
      transform: translate(-50%, -50%);
    }
    .four {
      margin: auto;
      margin-top: 290rpx;
      width: 540rpx;
      min-height: 50rpx;
      overflow-wrap: break-word;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #994124;
    }
    .five {
      position: relative;
      margin: auto;
      width: 239rpx;
      height: 77rpx;
      .wen {
        width: 239rpx;
        height: 77rpx;
        line-height: 50rpx;
        background-color: transparent;
        padding-top: 10rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #9f4227;
      }
      .btn {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        width: 239rpx;
        height: 77rpx;
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 723rpx;
      height: 768rpx;
    }
  }
}
</style>
