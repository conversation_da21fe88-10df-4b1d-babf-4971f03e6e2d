<template>
  <view class="CodeRes-page">
    <uni-popup
      ref="popup"
      :isMaskClick="false"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box">
        <view class="one-box">
          <image class="nian-tupian" :src="waterTemplate.templateComponent.codeResPrizeUrl" />
          <view class="wen">x {{ giveWaterNum }}</view>
          <!-- <image class="guang" :src="iconPic.guang" /> -->
        </view>
        <view v-if="tomorrowWaterNum" class="two-box">
          明日再来签到还可得{{ tomorrowWaterNum }}{{waterTemplate.tip2}}哦~
        </view>
        <view class="three-box" @click="close">
          <view class="wen">关闭</view>
          <image class="btn" :src="waterTemplate.templateComponent.selectGoodsBtnimg" />
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.codeResUrl" />
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
export default {
  props: {
    giveWaterNum: {
      type: String,
      require: true,
      default: "",
    },
    tomorrowWaterNum: {
      type: String,
      require: true,
      default: "",
    },
    //游戏模版配置
    waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      iconPic: {
        guang: "http://img.songlei.com/paradise/task/guang.png",
        huang: "http://img.songlei.com/paradise/task/huang.png",
      },
    };
  },
  mounted() {
    this.$refs.popup.open("center");
  },
  methods: {
    close() {
      this.$refs.popup.close();
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.CodeRes-page {
  .core-box {
    position: relative;
    width: 648rpx;
    height: 727rpx;
    text-align: center;

    .one-box {
      display: flex;
      justify-content: center;

      // 雪球大小
      .tupian {
        margin-top: 280rpx;
        width: 160rpx;
        height: 123rpx;
      }

      // 鞭炮大小
      .nian-tupian {
        margin-top: 280rpx;
        width: 290rpx;
        height: 200rpx;
      }

      .wen {
        margin-left: 20rpx;
        margin-top: 320rpx;
        font-size: 50rpx;
        
        font-weight: bold;
        color: #3288fe;
      }

      .guang {
        z-index: -1;
        position: absolute;
        top: 180rpx;
        left: 90rpx;
        width: 349rpx;
        height: 348rpx;
      }
    }

    .two-box {
      text-align: center;
      margin-top: 20rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #9f4227;
    }

    .three-box {
      position: relative;
      margin: auto;
      margin-top: 20rpx;
      width: 201rpx;
      height: 77rpx;

      .wen {
        padding-top: 10rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #9f4227;
      }

      .btn {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        width: 201rpx;
        height: 77rpx;
      }
    }

    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 648rpx;
      height: 727rpx;
    }
  }
}
</style>
