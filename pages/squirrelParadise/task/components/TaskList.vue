<template>
  <view class="TaskList-page">
    <uni-popup
      ref="popup"
      @maskClick="close"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box">
        <image class="guan" :src="iconPic.guan" @click="close" />
        <view class="cell-box">
          <view class="cell" v-for="(u, i) in giftList" :key="i">
            <view class="zou">
              <image class="tupian" mode="aspectFit" :src="u.url" />
            </view>
            <view class="zhong">
              <view class="shang">{{ u.title }}</view>
              <view class="xia">{{ u.name }}</view>
            </view>
            <view class="you" @click="handleTask(u)">
              <view class="wen" :style="u.color">{{ u.text }}</view>
              <image class="btn" :src="u.btnImg" />
            </view>
          </view>
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.taskUrl" />
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import { taskListApi } from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  props: {
    waterInfoId: {
      type: String,
      require: true,
      default: "",
    },
        //游戏模版配置
    waterTemplate: {
			type: Object,
			require: true,
			default() {
				return {};
			},
		},
  },
  data() {
    return {
      giftList: [],
      iconPic: {
        guan: "http://img.songlei.com/paradise/task/guan.png",
      },
    };
  },
  mounted() {
    this.taskList();
    this.$refs.popup.open("bottom");
    if (this.waterTemplate&&this.waterTemplate.templateComponent) {
      uni.setStorageSync('waterTemplate', this.waterTemplate);
    }
  },
  methods: {
    taskList() {
      const params = {
        waterInfoId: this.waterInfoId,
      };
      taskListApi(params).then((res) => {
        const url = "http://img.songlei.com/paradise/task/";
        const imgArr = ["mrqd", "llsp", "yqhy", "gwxd", "ppzl", "xxsm", "hby"];
        const titleArr = [
          "每日签到",
          "浏览商品",
          "邀请好友",
          "购物下单",
          "品牌助力",
          "线下扫码",
          "红包雨"
        ];
        const textArr = ["签到", "逛逛", "邀请", "购物", "助力", "查看", "参加"];
        const textFinishArr = [
          "今日已签到",
          "今日已浏览",
          "查看邀请",
          "查看奖励",
          "今日已助力",
          "今日已扫码",
          "今日已参加",
        ];
        res.data.forEach((u) => {
          const index = Number(u.taskType);
          u.url = `${url}${imgArr[index]}.png`;
          u.title = titleArr[index];
          if (1 == u.finishStatus) {
            u.text = textFinishArr[index];
            if (2 == u.taskType || 3 == u.taskType) {
              u.btnImg = `${url}huang.png`;
            } else {
              u.btnImg = `${url}hui.png`;
            }
            u.color = "#fff";
          } else {
            u.text = `去${textArr[index]}`;
            u.btnImg = `${url}huang.png`;
            u.color = "#953c22";
          }
        });
        this.giftList = res.data;
      });
    },
    handleTask(info) {
      console.log(info, '================')
      if (0 == info.finishStatus) {
        switch (info.taskType) {
          case "0":
            this.$refs.popup.close("close");
            this.$emit("close", info);
            break;
          case "1":
            this.browseTask(info);
            break;
          case "2":
            info.waterInfoId = this.waterInfoId;
            this.$refs.popup.close("close");
            this.$emit("close", info);
            break;
          case "3":
            this.placeTask(info);
            break;
          case "4":
            this.$refs.popup.close("close");
            this.$emit("close", info);
            break;
          case "5":
            this.offlineTask(info);
            break;
          case "6":
            this.$refs.popup.close("close");
            this.$emit("close", info);
            break;
          default:
            break;
        }
      } else {
        if (2 == info.taskType) {
          info.waterInfoId = this.waterInfoId;
          this.$refs.popup.close("close");
          this.$emit("close", info);
        }
        if (3 == info.taskType) {
          this.close();
          uni.navigateTo({
            url: `/pages/squirrelParadise/task/rewardList?waterInfoId=${this.waterInfoId}`,
          });
        }
      }
    },
    browseTask(info) {
      this.close();
      const params = {
        taskId: info.id,
        browseTime: info.browseTime,
        browseRule: info.name,
        spuGroupId: info.spuGroupId,
      };
      const browseParam = encodeURIComponent(JSON.stringify(params));
      uni.navigateTo({
        url: `/pages/squirrelParadise/task/browseProduct?browseParam=${browseParam}`,
      });
    },
    placeTask(info) {
      this.close();
      const params = {
        placeRule: info.name,
        buyRewardNums: info.buyRewardNums,
        waterInfoId: this.waterInfoId,
        spuGroupId: info.spuGroupId,
      };
      const placeParam = encodeURIComponent(JSON.stringify(params));
      uni.navigateTo({
        url: `/pages/squirrelParadise/task/placeOrder?placeParam=${placeParam}`,
      });
    },
    offlineTask(info) {
      this.close();
      const detail = encodeURIComponent(JSON.stringify(info.detail));
      uni.navigateTo({
        url: `/pages/squirrelParadise/task/offlineGuidance?detail=${detail}`,
      });
    },
    close() {
      this.$refs.popup.close("close");
      this.$emit("close", "");
    },
  },
};
</script>
<style scoped lang="scss">
.TaskList-page {
  .core-box {
    position: relative;
    margin: auto;
    width: 720rpx;
    height: 814rpx;
    .guan {
      position: absolute;
      top: 60rpx;
      right: 18rpx;
      width: 67rpx;
      height: 94rpx;
    }
    .cell-box {
      position: relative;
      top: 180rpx;
      width: 620rpx;
      height: 620rpx;
      margin: auto;
      overflow: auto;
      .cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;
        .zou {
          .tupian {
            width: 88rpx;
            height: 118rpx;
          }
        }
        .zhong {
          width: 300rpx;
          .shang {
            font-size: 34rpx;
            font-family: PingFang SC;
            font-weight: 800;
            color: #994124;
          }
          .xia {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #000000;
          }
        }
        .you {
          position: relative;
          text-align: center;
          width: 201rpx;
          height: 77rpx;
          .wen {
            padding-top: 10rpx;
            font-size: 30rpx;
            font-family: PingFang SC;
            font-weight: 800;
          }
          .btn {
            position: absolute;
            z-index: -1;
            top: 0;
            left: 0;
            width: 201rpx;
            height: 77rpx;
          }
        }
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 720rpx;
      height: 814rpx;
    }
  }
}
</style>
