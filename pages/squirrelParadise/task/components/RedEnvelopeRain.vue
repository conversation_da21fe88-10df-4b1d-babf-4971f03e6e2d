<template>
  <view class="RedEnvelopeRain-page">
    <uni-popup
      ref="popup"
      @maskClick="close"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <RedPacketRain v-if="redPacketRainAsync" :info="redEnvelopeInfo" @endCountdown="endCountdown" :waterTemplate="waterTemplate"/>

      <view v-if="!redPacketRainAsync" class="core-box">
        <image class="bg" :src="iconPic.bg" />
        <image class="close" :src="iconPic.close" @click="close" />
        
        <view v-if="thrustAsync" class="content moment">
          <image class="title" :src="iconPic.title" />
          <image class="moment-img" :src="waterTemplate.templateComponent.moment" />
          <image class="moment-rules-btn" :src="iconPic.momentRules" v-if="redEnvelopeInfo" @click="handleRules"/>
          <image class="content-btn" :src="iconPic.momentBtn" v-if="redEnvelopeInfo" @click="handleParticipate" />
          <view class="participations">今日可参与 {{ redEnvelopeInfo ? redEnvelopeInfo.currentNum : 0 }} 次</view>
        </view>
        <!-- 规则说明 -->
        <view v-if="rulesAsync" class="content">
          <view class="rules">
            {{ redEnvelopeInfo.layerTaskDetail }}规则说明
          </view>
          <image class="content-btn rules-btn" :src="iconPic.rulesBtn" @click="handleRulesGoback" />
        </view>
        <!-- 获取奖品 -->
        <view v-if="prizesAsync" class="content prizes">
          <image class="title" :src="iconPic.prizeTitle" />
          <view class="prizes-content">
            <image class="prizes-packet" :src="iconPic.packet" />
            <view class="prizes-info">
              <view>恭喜您抽中{{ prizeObj.packetNum }}个红包</view>
              <view v-if="prizeObj.propNum">获得 <text style="color:#FF0000;"> {{ prizeObj.propNum }} </text> {{waterTemplate.tip2}}</view>
              <view v-if="prizeObj.points">{{ prizeObj.propNum ? '和' : '获得' }} <text style="color:#FF0000;"> {{ prizeObj.points }} </text> 个积分</view>
              <view style="margin-top:42rpx;color:#939393;">用时{{ redEnvelopeInfo.browseTime }}秒</view>
              <view style="color:#939393;">今日还可参{{ prizeObj.joinNum }}次</view>
            </view>
          </view>
          <view class="prizes-btn" @click="handleGoUse" >
            <image class="prizes-btn-img" :src="iconPic.prizeBtn" />
            <text>去使用</text>
          </view>
          
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import RedPacketRain from './RedpacketRain.vue';

export default {
  components: {
    RedPacketRain,
  },
  props: {
    taskId: {
      type: String,
      require: true,
      default: "",
    },
    redEnvelopeInfo: {
      type: Object,
      require: true,
      default: () => {},
    },
  },
  data() {
    return {
      iconPic: {
        bg: this.redEnvelopeInfo?.layerImg || "http://img.songlei.com/paradise/redpackage/red-packet-bg.png",
        close: "http://img.songlei.com/paradise/redpackage/red-packet-close-btn.png",
        title: "http://img.songlei.com/paradise/redpackage/red-packet-btn.png",
        moment: "http://img.songlei.com/paradise/redpackage/red-packet-rules.png",
        momentRules: "http://img.songlei.com/paradise/redpackage/red-packet-rules-btn.png",
        momentBtn: "http://img.songlei.com/paradise/redpackage/red-packet-Involved-btn.png",
        rulesBtn: "http://img.songlei.com/paradise/redpackage/red-packet-goback-btn.png",
        prizeTitle: "http://img.songlei.com/paradise/redpackage/prize-title.png",
        packet: this.redEnvelopeInfo?.taskImg || "http://img.songlei.com/paradise/redpackage/red-packet.png",
        prizeBtn: "http://img.songlei.com/paradise/task/huang.png",
      },
      thrustAsync: true,
      rulesAsync: false,
      prizesAsync: false,
      redPacketRainAsync: false,
      prizeObj: {
        packetNum: 0, // 红包总数量
        points: 0, // 获取的积分数量
        propNum: 0, // 获取的水滴数量
        joinNum: 0, // 剩余次数
      },
      waterTemplate:{},//游戏模版
    };
  },
  mounted() {
    this.$refs.popup.open("center");
    this.waterTemplate = uni.getStorageSync('waterTemplate');
  },
  methods: {
    // 参与按钮
    handleParticipate() {
      this.thrustAsync = false;
      this.redPacketRainAsync = true;
    },
    // 查看规则说明
    handleRules() {
      this.thrustAsync = false;
      this.rulesAsync = true;
    },
    // 规则说明按钮
    handleRulesGoback() {
      this.thrustAsync = true;
      this.rulesAsync = false;
    },
    close() {
      this.$refs.popup.close();
      this.$emit("close");
    },
    endCountdown(obj) {
      this.redPacketRainAsync = false;
      this.prizesAsync = true;
      this.prizeObj = obj;
    },
    handleGoUse() {
      this.$refs.popup.close();
      const disable = this.prizeObj.propNum > 0 ? true : false;
      this.$emit("close", disable);
    },
  },
};
</script>
<style scoped lang="scss">
.RedEnvelopeRain-page {
  position: relative;
  .core-box {
    position: relative;
    width: 648rpx;
    height: 987rpx;
    text-align: center;
    .bg {
      // z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 648rpx;
      height: 987rpx;
    }
    .close{
      position: absolute;
      top: 188rpx;
      right: 0;
      width: 60rpx;
      height: 60rpx;
    }
    .content{
      .title{
        position: absolute;
        top: 370rpx;
        left: 50%;
        width: 347rpx;
        height: 66rpx;
        transform: translateX(-50%);
      }
      .moment-img{
        position: absolute;
        bottom: 322rpx;
        left: 50%;
        width: 553rpx;
        height: 198rpx;
        transform: translateX(-50%);
      }
      .moment-rules-btn{
        position: absolute;
        bottom: 316rpx;
        right: 139rpx;
        width: 150rpx;
        height: 44rpx;
        // transform: translateX(-50%);
      }
      .content-btn{
        position: absolute;
        bottom: 160rpx;
        left: 50%;
        width: 358rpx;
        height: 99rpx;
        transform: translateX(-50%);
      }
      .participations{
        position: absolute;
        bottom: 105rpx;
        left: 50%;
        transform: translateX(-50%);
        font-size: 28rpx;
        color: #974023;
      }
      .rules{
        position: absolute;
        bottom: 233rpx;
        left: 50%;
        transform: translateX(-50%);
        text-align: left;
        width: 539rpx;
        height: 326rpx;
        font-size: 24rpx;
        color: #67524D;
      }
      .rules-btn{
        bottom: 80rpx;
      }
    }
    .prizes{
      .title{
        position: absolute;
        top: 375rpx;
        left: 50%;
        width: 327rpx;
        height: 50rpx;
        transform: translateX(-50%);
      }
      .prizes-content{
        position: absolute;
        bottom: 259rpx;
        display:flex;
        justify-content: space-between;
        width: 500rpx;
        height: 244rpx;
        left: 50%;
        transform: translateX(-50%);
        // font-size: 28rpx;
        .prizes-packet{
          width: 186rpx;
          height: 244rpx;
        }
        .prizes-info{
          text-align: left;
          color: #974023;
          font-size: 28rpx;
          line-height: 34rpx;
        }
        
      }
      .prizes-btn{
        position: absolute;
        bottom: 105rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 233rpx;
        height: 82rpx;
        .prizes-btn-img{
          width: 233rpx;
          height: 82rpx;
        }
        text{
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%,-50%);
          font-size: 30rpx;
          color: #974023;
        }
      }
    }
    
  }
}
</style>
