<template>
  <view class="rewardList-page">
    <image
      class="navbg"
      :src="waterTemplate.templateComponent.prizeTopUrl"
      :style="{ height: CustomBar + 'px' }"
    />
    <cu-custom
      bgColor="rgba(255, 255, 255, 0)"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content" style="color: #000"> 我的下单奖励</block>
    </cu-custom>
    <view class="core-oubox">
      <image
        class="headbg"
        :src="waterTemplate.templateComponent.prizeBgUrl"
        :style="{ height: CustomBar + 'px' }"
      />
      <blcck v-if="orderList.length">
        <view class="core-box" v-for="(u, i) in orderList" :key="i">
          <view class="hang">
            <view class="qian">订单编号：{{ u.orderNo }}</view>
            <view class="hou">订单金额：{{ u.paymentPrice }}</view>
          </view>
          <view class="hang">
            <view class="qian">下单时间：{{ u.paymentTime }}</view>
            <view class="hou">
              奖励{{waterTemplate.tip2}}：<text style="color: #ff4d2c">{{ u.rewardWater }}个</text>
            </view>
          </view>
          <view class="hang">
            <view class="qian">
              退款状态：{{ u.refundStatus | convertStatus }}
            </view>
            <view class="hou"></view>
          </view>
        </view>
      </blcck>
      <blcck v-else><view class="core-box kong">暂无数据</view></blcck>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import { orderRewardApi } from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      waterInfoId: "",
      orderList: [],
      waterTemplate:{},//游戏模版
      iconPic: {
      },
    };
  },
  filters: {
    convertStatus(val) {
      return 1 == val ? "已退款" : "未退款";
    },
  },
  onLoad(options) {
    this.waterInfoId = options.waterInfoId;
    this.waterTemplate = uni.getStorageSync('waterTemplate');
    this.orderReward();
  },
  methods: {
    orderReward() {
      const params = {
        waterInfoId: this.waterInfoId,
      };
      this.orderList = [];
      orderRewardApi(params).then((res) => {
        this.orderList = res.data;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.rewardList-page {
  margin-bottom: 20rpx;
  .navbg {
    position: fixed;
    z-index: 10;
    width: 750rpx;
    top: 0;
  }
  .core-oubox {
    position: relative;
    width: 750rpx;
    padding-top: 50rpx;
    .headbg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 750rpx;
      height: 213rpx;
    }
    .core-box {
      width: 700rpx;
      height: 200rpx;
      background: #ffffff;
      border-radius: 24rpx;
      margin: auto;
      margin-bottom: 20rpx;
      padding: 0 15rpx;
      padding-top: 20rpx;
      .hang {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #000000;
        .qian {
          width: 65%;
        }
        .hou {
          width: 30%;
        }
      }
    }
    .kong {
      text-align: center;
      padding-top: 80rpx;
    }
  }
}
</style>
