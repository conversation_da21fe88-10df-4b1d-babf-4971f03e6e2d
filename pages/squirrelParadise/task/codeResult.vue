<template>
  <view
    class="codeResult-page"
    :style="{ 'background-image': bgFlag ? `url(${templateComponent.homePageUrl})` : '' }"
  >
    <uni-popup
      ref="popup"
      :isMaskClick="false"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box">
        <view class="one-box">
          <image class="nian-tupian" :src="templateComponent.codeResPrizeUrl" />
          <view class="wen">x {{ info.giveWaterNum }}</view>
          <!-- <image class="guang" :src="iconPic.guang" /> -->
        </view>
        <view v-if="info.gtomorrowWaterNum" class="two-box">
          明日再来签到还可得{{ info.gtomorrowWaterNum }}{{waterTemplate.tip2}}哦~
        </view>
        <view class="three-box" @click="close">
          <view class="wen">关闭</view>
          <image class="btn" :src="iconPic.huang" />
        </view>
        <image class="bg" :src="templateComponent.codeResUrl" />
      </view>
    </uni-popup>
    <not-start v-if="startFlag" :showMask="false" :showHome="false" />
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import NotStart from "../home/<USER>/NotStart";
import { doneApi } from "@/pages/squirrelParadise/api/squirrelParadise";
export default {
  components: {
    NotStart,
  },
  data() {
    return {
      iconPic: {
        guang: "http://img.songlei.com/paradise/task/guang.png",

        huang: "http://img.songlei.com/paradise/task/huang.png",
      },
      info: {},
      templateComponent:{},//游戏模版
      startFlag: false, //没有用，后台提示活动未开始一个框
      bgFlag: false,
      waterTemplate :{},
    };
  },
  async onLoad(options) {
    let scenes = decodeURIComponent(options.scene).split("&");
    this.waterTemplate = uni.getStorageSync('waterTemplate');
    const params = {
      taskId: scenes[0],
      channel: scenes[1],
    };
    try {
      const res = await doneApi(params);
      this.info = res.data;
      if (res.data.templateComponent) {
        this.templateComponent = JSON.parse(res.data.templateComponent)
      }
      if (this.info && this.info.giveWaterNum > 0) {
        this.bgFlag = true;
        this.$refs.popup.open("center");
      } else if (this.info.waterInfoId > 0) {
        // 今日已完成过从没有玩过活动
        uni.redirectTo({
          url: `/pages/squirrelParadise/home/<USER>
        });
      }
    } catch (error) {
      // 后台提示活动未开始一个框
    }
  },
  methods: {
    close() {
      this.$refs.popup.close();
      uni.redirectTo({
        url: `/pages/squirrelParadise/home/<USER>
      });
    },
  },
};
</script>
<style scoped lang="scss">
.codeResult-page {
  height: 100vh;
  background-size: 100% 100%;

  .core-box {
    position: relative;
    width: 648rpx;
    height: 727rpx;
    text-align: center;

    .one-box {
      display: flex;
      justify-content: center;

     // 水滴大小
    .tupian {
        margin-top: 280rpx;
        width: 89rpx;
        height: 123rpx;
      }
      // 雪球大小
      .snowman-tupian {
        margin-top: 230rpx;
        width: 231rpx;
        height: 220rpx;
      }

       // 鞭炮大小
      .nian-tupian {
        margin-top: 230rpx;
        width: 231rpx;
        height: 200rpx;
      }

      .wen {
        margin-left: 20rpx;
        margin-top: 320rpx;
        font-size: 50rpx;
        
        font-weight: bold;
        color: #3288fe;
      }

      .guang {
        z-index: -1;
        position: absolute;
        top: 180rpx;
        left: 90rpx;
        width: 349rpx;
        height: 348rpx;
      }
    }

    .two-box {
      text-align: center;
      margin-top: 20rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #9f4227;
    }

    .three-box {
      position: relative;
      margin: auto;
      // margin-top: 120rpx;
      margin-top: 75rpx; // 鞭炮大小
      width: 201rpx;
      height: 77rpx;

      .wen {
        padding-top: 10rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #9f4227;
      }

      .btn {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        width: 201rpx;
        height: 77rpx;
      }
    }

    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 648rpx;
      height: 727rpx;
    }
  }
}
</style>
