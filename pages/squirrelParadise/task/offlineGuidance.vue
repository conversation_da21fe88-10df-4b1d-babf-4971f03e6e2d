<template>
  <view
    class="offlineGuidance-page"
    :style="{
      background: `linear-gradient(180deg, #DFD0F7 ${CustomBar}px, #DFD0F7 30%)`,
    }"
  >
  <view>
    <image
      class="navbg"
      :src="waterTemplate.templateComponent.prizeTopUrl"
      :style="{ height: CustomBar + 'px' }"
    />
    <image
        :src="waterTemplate.templateComponent.prizeBgUrl | formatImg750"
        mode="widthFix"
        :style="{
          position: 'fixed',
          width: '100%',
          height: '309rpx',
          top: CustomBar + 'px',
          'z-index': '0'
        }"
      />
  </view>
    <cu-custom
      bgColor="rgba(255, 255, 255, 0)"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content" style="color: #dfd0f7">线下获取{{waterTemplate.tip2}}指引</block>
    </cu-custom>
    <view style="width: 100%;height: 100%;position: relative;">
      <view class="neirong-box">
        <rich-text space="emsp" :nodes="detail" />
      </view>
    </view>
    <image class="dibu" :src="waterTemplate.templateComponent.prizeBottomUrl" />
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/

export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      detail: "",
      waterTemplate:{},//游戏模版
      iconPic: {
      },
    };
  },
  onLoad(options) {
    let richtext = JSON.parse(decodeURIComponent(options.detail));
    this.waterTemplate = uni.getStorageSync('waterTemplate');
    const regex = new RegExp("<img", "gi");
    richtext = richtext.replace(regex, `<img style=\"max-width: 100%;\"`);
    this.detail = richtext;
  },
  methods: {},
};
</script>
<style scoped lang="scss">
.offlineGuidance-page {
  padding-bottom: 180rpx;
  .navbg {
    position: fixed;
    z-index: 10;
    width: 750rpx;
    top: 0;
  }
  .neirong-box {
    width: 700rpx;
    min-height: 74vh;
    padding: 10rpx 20rpx;
    margin: auto;
    margin-top: 20rpx;
    // border: 8rpx dashed #6da1ff;
    border-radius: 10rpx;
    background: #eff5ff;
  }
  .dibu {
    background-color: #f4faf1;
    position: fixed;
    z-index: 1;
    left: 0;
    bottom: 0;
    width: 750rpx;
    height: 137rpx;
  }
}
</style>
