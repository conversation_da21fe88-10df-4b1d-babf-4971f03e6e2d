<template>
  <view class="browseProduct-page">
    <cu-custom
      bgColor="rgba(255, 255, 255, 0)"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content" style="color: #000">浏览商品</block>
    </cu-custom>
    <image
      class="navbg"
      :src="waterTemplate.templateComponent.prizeTopUrl"
      :style="{ height: CustomBar + 'px' }"
    />
    <view class="core-box">
      <image class="headbg" :src="waterTemplate.templateComponent.prizeBgUrl" />
      <view class="djs-box" v-if="stateFlag">
        <view class="djs">{{ countdown }}</view>
        <view class="djs">{{ browseRule }}</view>
      </view>
      <view class="jg-box" v-else>
        <view class="one"> 浏览完成，获得{{ giveWaterNum }}{{waterTemplate.tip2}}</view>
        <view class="two">
          明日再来签到还可得{{ tomorrowWaterNum }}{{waterTemplate.tip2}}哦~
        </view>
        <view class="three" @click="close">
          <view class="wen">关闭</view>
          <image class="btn" :src="iconPic.huang" />
        </view>
      </view>
      <image class="bg" :src="waterTemplate.templateComponent.brandUrl" />
    </view>
    <scroll-view
      class="verticalmain"
      scroll-y
      scroll-with-animation
      @scrolltolower="reachBottom"
      :style="{
        height: `calc(100vh - ${CustomBar + 100}rpx)`,
      }"
      :scroll-into-view="'main-' + MainCur"
      lower-threshold="100"
    >
      <goods-waterfall
        :fullWidth="750"
        :goodsList="goodsList"
        :loadMore="loadMore"
      />
      <view
        style="width: 750rpx"
        :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"
      >
      </view>
    </scroll-view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import goodsWaterfall from "components/goods-waterfall/index";

import util from "@/utils/util";
import api from "@/utils/api";
import { doneApi } from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  components: {
    goodsWaterfall,
  },
  data() {
    return {
      taskId: "",
      browseTime: "",
      browseRule: "",
      spuGroupId: "",

      stateFlag: true,
      intervalId: null,
      secondVal: "",

      giveWaterNum: "",
      tomorrowWaterNum: "",
      MainCur: 0,
      CustomBar: this.CustomBar,
      goodsList: [],
      loadmore: true,
      page: {
        searchCount: false,
        current: 1,
        size: 20,
        ascs: "",
        descs: "",
      },
      waterTemplate:{},//游戏模版
      iconPic: {
        huang: "http://img.songlei.com/paradise/task/huang.png",
      },
    };
  },
  computed: {
    countdown() {
      if ("" !== this.secondVal) {
        if (this.secondVal < 0) {
          this.done();
          clearInterval(this.intervalId);
          this.stateFlag = false;
        } else {
          let minute = Math.floor(this.secondVal / 60);
          let second = this.secondVal - minute * 60;
          minute = minute > 9 ? minute : "0" + minute;
          second = second > 9 ? second : "0" + second;
          return `00:${minute}:${second}`;
        }
      }
    },
  },
  onLoad(options) {
    const res = JSON.parse(decodeURIComponent(options.browseParam));
    this.waterTemplate = uni.getStorageSync('waterTemplate');
    this.taskId = res.taskId;
    this.browseTime = res.browseTime;
    this.browseRule = res.browseRule;
    this.spuGroupId = res.spuGroupId;
    this.goodsPage(true);
    this.timing();
  },
  onUnload() {
    clearInterval(this.intervalId);
  },
  methods: {
    timing() {
      this.secondVal = Number(this.browseTime);
      this.intervalId = setInterval(() => {
        this.secondVal--;
      }, 1000);
    },
    done() {
      const params = {
        taskId: this.taskId,
      };
      doneApi(params).then((res) => {
        const { giveWaterNum, tomorrowWaterNum } = res.data;
        this.giveWaterNum = giveWaterNum;
        this.tomorrowWaterNum = tomorrowWaterNum;
      });
    },
    close() {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (prevPage.route == "pages/squirrelParadise/home/<USER>") {
        uni.navigateBack();
      } else {
        uni.navigateTo({
          url: "/pages/squirrelParadise/home/<USER>",
        });
      }
    },

    reachBottom() {
      if (this.loadmore) {
        util.throttle(this.getMore(), 1500);
      }
    },
    getMore() {
      this.page.current = this.page.current + 1;
      this.goodsPage();
    },
    goodsPage(fresh) {
      const params = {
        ...this.page,
        goodsGroupIds: this.spuGroupId,
      };
      api.goodsGroupsGet(params).then((res) => {
        let goodsList = res.data.records;
        if (fresh) {
          this.goodsList = goodsList;
        } else {
          this.goodsList = [...this.goodsList, ...goodsList];
        }
        if (goodsList.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.browseProduct-page {
  position: fixed;
  z-index: 10;
  background-color: #f9f9f9;
  .navbg {
    position: fixed;
    width: 750rpx;
    top: 0;
  }
  .core-box {
    position: relative;
    width: 750rpx;
    height: 360rpx;
    text-align: center;
    .headbg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 750rpx;
      height: 273rpx;
    }
    .djs-box {
      padding-top: 130rpx;
      .djs {
        width: 590rpx;
        margin: auto;
        font-size: 34rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: #9f4227;
      }
    }
    .jg-box {
      padding-top: 130rpx;
      .one {
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: #9f4227;
      }
      .two {
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #9f4227;
      }
      .three {
        position: relative;
        margin: auto;
        width: 201rpx;
        height: 77rpx;
        .wen {
          padding-top: 10rpx;
          font-size: 30rpx;
          font-family: PingFang SC;
          font-weight: 800;
          color: #9f4227;
        }
        .btn {
          position: absolute;
          z-index: -1;
          top: 0;
          left: 0;
          width: 201rpx;
          height: 77rpx;
        }
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 35rpx;
      left: 41rpx;
      width: 667rpx;
      height: 318rpx;
    }
  }
}
</style>
