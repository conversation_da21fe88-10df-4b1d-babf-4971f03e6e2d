<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" simple :pageTo="pageTo" :isBack="true"
			:hideMarchContent="false">
			<!-- <block slot="content">搜索</block> -->
			<view slot="marchContent">
				<view class="search-form round" :style="{
			  height: `${searchHeight}px`,
			  lineHeight: '100%',
			  marginRight:' 20rpx',
			  marginLeft:' 0',
			  border:' 1rpx solid #CDA488'
		  }">
					<text class="cuIcon-search" style="font-size: 30rpx;color: #A1A1A1"></text>
					<input placeholder-style="color: #a1a1a1; font-size:26rpx" v-model="name" type="text"
						:placeholder="placeholder" confirm-type="search"
                        @confirm="searchClick" ></input>

					<text @click.native="searchClick()" style="font-size:26rpx; padding: 20rpx;"
						:class="'text-'+theme.themeColor">搜索</text>
				</view>
			</view>
		</cu-custom>
		<live-item :roomList="roomLists" :loading="loading" :loadmore="loadmore" />
	</view>
</template>
<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import recommendComponents from "components/recommend-components/index";
	import baseNodata from "components/base-nodata/index.vue";
	import liveItem from "@/components/div-components/div-living-list/live-item.vue";


	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				loading: false,
				//有数统计使用
				page_title: '直播大厅',
				current: 1,
				total: '',
				loadmore: true,
				roomLists: [],
				name: '',
				placeholder: '',
				searchHeight: 30,
			};
		},

		components: {
			recommendComponents,
			baseNodata,
			liveItem
		},
		props: {},

		onLoad(options) {
			uni.showLoading({
				title: '加载中'
			});
			app.initPage().then(res => {
				this.liveRoomInfoList();
			});
			this.name = options.key ||'';
		},
		//上拉加载
		onReachBottom() {
			if (!this.loading) {
				this.current = this.current + 1
				this.liveRoomInfoList()
			}
		},
		onPullDownRefresh() {
			// 显示顶部刷新图标
			this.current = 1
			this.roomLists = [];
			this.loadmore = true;
			uni.showNavigationBarLoading();
			this.liveRoomInfoList();
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();

		},
		methods: {
			searchClick() {
				this.liveRoomInfoList()
			},
			liveRoomInfoList() {
				let that = this;
				that.loading = true;
				if (this.current == 1) {
					that.roomLists = [];
				}
				api.liveRoomInfoList({
					'liveStatus': '103,102,101',
					current: this.current,
					size: 20,
					name: this.name
				}).then(res => {
					//  直播间状态。101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期
					if (res.data.records && res.data.records.length > 0) {
						this.total = res.data.total;
						that.roomLists = that.roomLists.concat(res.data.records);
						//如果没有数据的数据数据的时候
					}
					if (that.roomLists.length >= res.data.total) {
						this.loadmore = false
					}
					this.loading = false;
					uni.hideLoading();
				}).catch(e => {
					this.loading = false;
					uni.hideLoading();
				});
			}

		}
	};
</script>
<style>
	.live-container {
		justify-content: space-between;
		flex-wrap: wrap;
		box-sizing: content-box;
		padding: 20rpx;
	}

	.live-box {
		width: 349rpx;
		background-color: #fff;
		overflow: hidden;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
		position: relative;
	}

	.live-image {
		width: 100%;
		height: 456rpx;
	}

	.margin-top-sm-img {
		width: 100%;
		height: 560rpx;
		overflow: hidden;
		position: relative;
	}

	.live-image-1 {
		width: 158rpx;
		height: 49rpx;
		position: absolute;
		top: 3rpx;
		left: 0rpx;
		z-index: 2;
	}

	.live-image-2 {
		width: 100%;
		height: 560rpx;
		z-index: 1;
	}

	.live-image-3 {
		width: 125rpx;
		height: 250rpx;
		position: absolute;
		bottom: 0rpx;
		right: -10rpx;
		z-index: 2;
	}

	.live-image-4 {
		width: 82rpx;
		height: 82rpx;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: -21px;
		z-index: 1;
		border-radius: 50%;
	}

	.live-content {
		background-image: url(https://img.songlei.com/1/material/d2eb063b-ca23-47f1-b89d-7c40ef952eb2.png);
		background-size: 100% auto;
		background-repeat: no-repeat;
		padding: 10px 8px 0px 8px;
	}

	.broadcast {
		background: #fff;
		width: 100%;
		border-radius: 10px;
		display: flex;
		flex-wrap: wrap;
		padding: 10rpx 2% 20rpx 2%;
		box-sizing: border-box;
	}

	.broadcast-box {
		width: 25%;
		overflow: hidden;
		text-align: center;
		padding-top: 10px;
	}

	.broadcast-box image {
		width: 126rpx;
		height: 126rpx;
		display: block;
		margin: 0 auto;
	}

	.broadcast-box text {
		margin-top: 3px;
		font-size: 24rpx;
		color: #333333;
		line-height: 50rpx;
	}
</style>
