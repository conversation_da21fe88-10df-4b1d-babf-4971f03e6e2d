import { requestApi as request } from '@/utils/api.js'


// 扫码授权同意pos端的退款申请
export const authQrCode = (data) => {
	return request({
		url: '/sco-customer-admin/slpos/authQrCode',
		method: 'post',
		baseUrl: "posUrl",
		data,
	})
}


//扫码授权同意pos端登录
export const posLogin = (data) => {
	return request({
		url: '/sco-customer-admin/slpos/custQrCode',
		method: 'post',
		baseUrl: "posUrl",
		data,
	})
}

//认证会员信息
export function authInfo(data = {}) {
  return request({
    url: '/sco-customer-api/rest?method=sco.customer.auth',
    method: 'post',
    data
  })
}

//会员等级查询
export function custgrade(data = {}) {
  return request({
    url: '/sco-customer-api/rest?method=sco.customer.custgrade',
    method: 'post',
    data
  })
}

