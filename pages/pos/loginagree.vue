<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">会员登录</block>
		</cu-custom>
		<successView v-if="status==1"/>
		<fail-view v-else-if="status==2"></fail-view> 
	</view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import failView from "./components/fail.vue";
	import successView from "./components/success.vue";
	import {
		posLogin
	} from '@/pages/pos/api/pos.js'
	export default {
		components: {
			failView,
			successView
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				status: 0, // 初始状态  1 登录成功    2 登录失败
				typeTxt: '',
				params: {
					"authKey": "",
					"buildCode": "",
					"entId": 0,
					"manaUnit": "",
					"termNo": ""
				}
			}
		},
		onLoad(options) {
			console.log("====options========", options);
			const q = "";
			//接受二维码中参数
			if (options.scene) {
				q = decodeURIComponent(options.scene);
			} else if (options.q) {
				q = decodeURIComponent(options.q);
			}
			//兼容通过扫码商品小票上面的二维码进来
			if (q) {
				if (q && q.length > 0 && q != 'undefined') {
					this.params.authKey = util.getUrlParam(q, 'authkey') || util.getUrlParam(q, 'authKey');
					this.params.buildCode = util.getUrlParam(q, 'buildcode') || util.getUrlParam(q, 'buildCode');
					this.params.entId = util.getUrlParam(q, 'entid') || util.getUrlParam(q, 'entId');
					this.params.manaUnit = util.getUrlParam(q, 'manaunit') || util.getUrlParam(q, 'manaUnit');
					this.params.termNo = util.getUrlParam(q, 'termno') || util.getUrlParam(q, 'termNo');
					//汉字
					this.typeTxt = decodeURIComponent(util.UrlParamHash(q, 'type'));
					console.log("二维码传进来的参数：", this.typeTxt)
				}
			} else {
				// entid=0&manaunit=002&buildcode=201&termno=2872&authkey=T020128721749805790
				this.params.authKey = options.authkey || options.authKey;
				this.params.buildCode = options.buildcode || options.buildCode;
				this.params.entId = options.entid || options.entId;
				this.params.manaUnit = options.manaunit || options.manaUnit;
				this.params.termNo = options.termno || options.termNo;
				this.typeTxt = decodeURIComponent(options.type);
			}
			this.loginMaUser();
		},

		methods: {
			loginMaUser() {
				const userInfo = uni.getStorageSync("user_info");
				if (userInfo && userInfo.erpCid) {
					this.handlePosLogin(userInfo.erpCid);
				} else {
					uni.navigateTo({
						url: '/pages/login/index?reUrl=' + encodeURIComponent(
							'/pages/pos/loginagree?authKey=' + this.params.authKey +
							'&buildCode=' + this.params.buildCode +
							'&entId=' + this.params.entId +
							'&manaUnit=' + this.params.manaUnit +
							'&termNo=' + this.params.termNo
						)
					})
				}
			},

			handlePosLogin(erpCid) {
				const data = {
					...this.params,
					custId: erpCid,
				}
				posLogin(data).then(res => {
					this.status = 1; //授权成功
				}).catch(e => {
					this.status = 2; //授权失败
				});
			},

			handleClose() {
				uni.exitMiniProgram({
					success: function() {
						console.log('退出小程序成功');
					},
					fail: function(err) {
						console.log('退出小程序失败', err);
					}
				})
			}
		}
	}
</script>

<style scoped>
	.tip-layout {
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>