<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">松雷POS授权</block>
		</cu-custom>
		<view class="padding text-center" style="margin-top: 100rpx;">
			<view v-if="status==0">
				<view class="tip-layout">
					<image style="width: 40rpx;height: 40rpx;margin-right: 20rpx;"
						src="https://img.songlei.com/live/pos/ic_warning2.png"></image>
					点击同意授权按钮，即同意POS发起的{{typeTxt}}申请
				</view>
				<button type="primary"
					style="margin-top: 100rpx;background-image: linear-gradient(90deg, #CDAD90 0%, #CDA185 100%);"
					@click="loginMaUser">{{typeTxt}}授权</button>
			</view>
			<view v-else-if="status==1">
				<view class="tip-layout">
					<image style="width: 40rpx;height: 40rpx;margin-right: 20rpx;"
						src="https://img.songlei.com/live/pos/ic_success.png"></image>
					{{typeTxt}}授权成功！
				</view>
				<button @click="handleClose" type="primary" style="margin-top: 100rpx;background:rgba(2, 167, 240, 1)">完成</button>
			</view>
			<view v-else-if="status==2">
				<view class="tip-layout">
					<image style="width: 40rpx;height: 40rpx;margin-right: 20rpx;"
						src="https://img.songlei.com/live/pos/ic_warning.png"></image>
					授权失败，您当前没有{{typeTxt}}权限！
				</view>
				<button @click="handleClose" type="primary" style="margin-top: 100rpx;background:rgba(2, 167, 240, 1)">完成</button>
			</view>
		</view>
	</view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import {
		authQrCode
	} from '@/pages/pos/api/pos.js'
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				status: 0, // 初始状态
				typeTxt:'',
				params: {
					"authKey": "",
					"buildCode": "",
					"entId": 0,
					"manaUnit": "",
					"termNo": ""
				}
			}
		},
		onLoad(options) {
			//兼容通过扫码商品小票上面的二维码进来
			if (options.q) {
				const q = decodeURIComponent(options.q) // 获取到二维码原始链接内容
				if (q && q.length > 0 && q != 'undefined') {
					this.params.authKey = util.getUrlParam(q, 'authkey');
					this.params.buildCode = util.getUrlParam(q, 'buildcode');
					this.params.entId = util.getUrlParam(q, 'entid');
					this.params.manaUnit = util.getUrlParam(q, 'manaunit');
					this.params.termNo = util.getUrlParam(q, 'termno');
					//汉字
					this.typeTxt =decodeURIComponent(util.UrlParamHash(q, 'type'));
					console.log("二维码传进来的参数：",this.typeTxt)
				}
			}else{
				this.params.authKey = options.authKey || options.authkey;
				this.params.buildCode = options.buildCode  || options.buildcode;
				this.params.entId = options.entId   || options.entid;
				this.params.manaUnit = options.manaUnit   || options.manaunit;
				this.params.termNo = options.termNo   || options.termno;
				this.typeTxt = decodeURIComponent( options.type);
			}
		},

		methods: {
			loginMaUser() {
				const userInfo = uni.getStorageSync("user_info");
				if (userInfo && userInfo.phone) {
					this.handleRefundOrder(userInfo.phone);
				} else {
					uni.navigateTo({
						url: '/pages/login/index?reUrl=' + encodeURIComponent(
							'/pages/pos/agreeauth?authKey='+this.params.authKey
							+'&buildCode='+this.params.buildCode
							+'&entId='+this.params.entId
							+'&manaUnit='+this.params.manaUnit
							+'&termNo='+this.params.termNo
							)
					})
				}
			},
			
			handleRefundOrder(phone) {
				const data = {
					...this.params,
					"mobile": phone
				}
				authQrCode(data).then(res => {
					this.status = 1; //授权成功
				}).catch(e => {
					this.status = 2; //授权失败
				});
			},
			
			handleClose(){
				uni.exitMiniProgram({
					success: function() {
						console.log('退出小程序成功');
					},
					fail: function(err) {
						console.log('退出小程序失败', err);
					}
				})
			}
		}
	}
</script>

<style scoped>
	.tip-layout {
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>