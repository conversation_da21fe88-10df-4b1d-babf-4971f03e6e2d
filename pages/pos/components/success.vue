<template>
	<view>
		<view class="bg-white m-xxxsm plr-xxxsm pt-xxxsm" style="
			  border-radius: 20rpx;
			">
			<navigator url="/pages/user/user-info/index" hover-class="none"
				v-if="userInfo && (userInfo.nickName || userInfo.headimgUrl)"
				class="flex align-center personal-information">
				<image class="size-110 head" style="border-radius: 50%;"
					:src="userInfo.headimgUrl || $imgUrl('live/user-center/default_pic.png')">
				</image>
				<view class="content ml-xxxsm flex flex-direction">
					<view class="font-xxl text-bold">
						{{ userInfo.nickName || '' }}
					</view>
					<view class="text-88 font-md" v-if="userInfo.phone">手机号：{{ userInfo.phone | phoneEncryption }}
					</view>
				</view>
			</navigator>

			<view style="display: flex; flex-direction: column;">
				<view style="position: relative; height: 280rpx; overflow: hidden;">
					<image style="width:100%;" mode="aspectFit" :src="cardTypeImages[userInfo.erpCustType]">
					</image>
					<view class="cardno text-lg text-white">
						<!-- <text>{{ cardNames[carInfo.cust_type] }}</text> -->
						<text>{{ userInfo.erpCustTypename ||''}}卡号</text>
						<text style="font-weight:600; padding-left: 20rpx;">NO:{{ userInfo.erpCardno||''}}</text>
					</view>
				</view>
			</view>
		</view>

		<view class="flex  justify-around bg-white p-xxxsm mlr-sm" style="
			  border-radius: 20rpx;
			">
			<view style="flex: 1" class="flex flex-direction align-center"
				@click="checkLogin('/pages/signrecord/signrecord-info/index')">
				<view class="text-88 font-md">积分</view>
				<view class="pay-num font-xxl mt-xxxs">
					{{ userInfo.pointsCurrent || '0' }}
				</view>
				<view v-if="pointExpire>0" class="font-xxsm text-red">{{pointExpire}}积分即将过期</view>
			</view>

			<view style="flex: 1" class="flex flex-direction align-center"
				@click="checkLogin('/pages/coupon/coupon-user-list/index')">
				<text class="text-88 font-md">优惠劵</text>
				<view class="pay-num font-xxl mt-xxxs">
					{{ userInfo.couponNum ? userInfo.couponNum : 0 }}
					<text class="font-md">张</text>
				</view>
				<view class="font-xxsm text-red" v-if="couponExpiring>0">{{couponExpiring}}张即将过期</view>
			</view>

			<view style="flex: 1" class="flex flex-direction align-center"
				@click="checkLogin('/pages/gift/gift-card/index')">
				<text class="text-88 font-md">礼品卡</text>
				<view class="pay-num font-xxl mt-xxxs">
					{{ num }}
					<text class="font-md">张</text>
				</view>
			</view>
		</view>

		<!-- 轮播 -->
		<view class="m-sm" v-if="MinImages&&MinImages.length">
			<swiper class="swiper" style="height:196rpx;" circular :indicator-dots="false" autoplay :interval="3000">
				<swiper-item v-for="(item,index) in MinImages" :key="index">
					<view v-if="item && item.linkUrl" @click="checkLogin(item.linkUrl)">
						<image :src="item.imgUrl" mode="widthFix"
							style="width: 100%;height:196rpx;border-radius: 20rpx;">
						</image>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<recommendComponents canLoad />
	</view>
</template>

<script>
	const app = getApp();
	import util from '@/utils/util.js';
	import api from 'utils/api';
	import {
		authInfo,
		custgrade
	} from '@/pages/pos/api/pos.js';
	import {
		getPaypayCode
	} from '@/api/gift';
	import recommendComponents from '@/components/recommend-components/index';
    import { getWxTemplate } from '@/api/message.js';
	
	export default {
		components: {
			recommendComponents,
		},
		data() {
			return {
				userInfo: {},
				MinImages: '',
				num: '',
				cardTypeImages: {
					"108": "https://img.songlei.com/usercard/black-card1.png",
					"103": "https://img.songlei.com/usercard/gray-card1.png",
					"102": "https://img.songlei.com/usercard/gold-card1.png",
					"101": "https://img.songlei.com/usercard/pink-card1.png",
				},
				couponExpiring: 0,
				pointExpire: 0
			}
		},

		created() {
			this.userInfoGet();
			this.MinImage();
			this.getPaypayCode();
		},

		methods: {
			//获取商城用户信息
			async userInfoGet() {
				try {
					const res = await api.userInfoGet();
					this.userInfo = res.data || {};
					const authRes = await authInfo({
						"channel": "WECHAT",
						"authType": "C",
						"authKey": this.userInfo.erpCid,
						"fields": "accnt,grade"
					});
					this.couponExpiring = authRes.data.couponExpiring || 0;
					this.pointExpire = authRes.data.grade?.pointExpire || 0;
				} catch (e) {
					console.error(e);
				}


			},

			checkLogin(url) {
				if (!url) {
					uni.showToast({
						title: '开发中，敬请期待',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (!util.isUserLogin()) {
					uni.navigateTo({
						url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
					});
					return;
				} else {
					if (
						'/pages/signrecord/signrecord-info/index' === url ||
						'/pages/coupon/coupon-user-list/index' === url ||
						'/pages/gift/gift-card/index' === url ||
						'/pages/distribution/distribution-center/index' === url
					) {
						let type = 3;
						switch (url) {
							case '/pages/signrecord/signrecord-info/index':
								type = 3;
								break;
							case '/pages/coupon/coupon-user-list/index':
								type = 4;
								break;
							case '/pages/gift/gift-card/index':
								type = 5;
								break;
							case '/pages/distribution/distribution-center/index':
								type = 7;
								break;
						}
						getWxTemplate({
							type
						}).then((res) => {
							// #ifdef MP
							uni.requestSubscribeMessage({
								tmplIds: res.data,
								complete: () => {
									uni.navigateTo({
										url
									});
								}
							});
							// #endif
							// #ifndef MP
							uni.navigateTo({
								url
							});
							// #endif
						});
						return;
					}
					uni.navigateTo({
						url
					});
				}
			},

			// 获取多张图片
			MinImage() {
				api.listMineimg().then(res => {
					this.MinImages = res.data
				})
			},

			//获取礼品卡数量
			getPaypayCode() {
				getPaypayCode().then((res) => {
					if (res.code == 0 && res.data) {
						this.num = res.data.num || 0;
					}
				});
			},
		}
	}
</script>

<style scoped lang="scss">
	.permissions_box {
		padding: 200rpx 30rpx 50rpx;
		background-color: #fff;
		color: #000000;
	}

	.cardno {
		position: absolute;
		display: flex;
		justify-content: flex-end;
		width: 565rpx;
		bottom: 0;
		right: 30rpx;
	}

	.pay-num {
		color: #000000;
		font-weight: bold;
	}
</style>