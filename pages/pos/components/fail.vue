<template>
	<view class="flex flex-direction align-center mt-100">
		<image class="size-200" src="https://img.songlei.com/live/login-fail.png"></image>
		<button type="primary" class="width-250 mt-xxxxl"
			style="background-image: linear-gradient(90deg, #CDAD90 0%, #CDA185 100%);" @click="scanCode">扫一扫</button>

		<uni-popup ref="perpopup" type="center" :mask-click="false">
			<view class="permissions_box">
				当您使用APP时，扫一扫查看相关商品功能需要授权相机权限，获取本地图片需要获取访问设备照片权限，不授权上述权限，不影响APP其他功能使用。
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp();
	import util from '@/utils/util.js';
	import api from 'utils/api';
	// #ifdef APP-PLUS
	const mpaasScanModule = uni.requireNativePlugin('Mpaas-Scan-Module');
	// #endif
	export default {
		data() {
			return {

			}
		},

		methods: {
			//扫码
			scanCode() {
				// #ifdef MP-WEIXIN
				// 允许从相机和相册扫码
				uni.scanCode({
					scanType: ['barCode', 'qrCode'], //所扫码的类型 barCode	一维码 qrCode	二维码
					success: (res) => {
						if (res.result) {
							const code = res.result; //result	所扫码的内容 测试：6902902006811
							console.log('code', code);
							if (code) {
								if (code.indexOf('https://') > -1) {
									util.scanH5ToMaPage(code, api.qrcodequery, 'redirect');
									return;
								}
							}
						} else {
							console.log('请重新扫描');
							return false;
						}
					},
					fail: (res) => {
						console.log('未识别到二维码');
					}
				});
				// #endif

				// #ifdef APP-PLUS
				let that = this;
				// 允许从相机和相册扫码
				var platform = uni.getSystemInfoSync().platform;
				if (platform == 'android') {
					plus.android.checkPermission(
						'android.permission.CAMERA',
						(granted) => {
							if (granted.checkResult == -1) {
								//弹出
								that.$refs.perpopup.open('top');
								plus.android.requestPermissions(['android.permission.CAMERA',
									'android.permission.WRITE_EXTERNAL_STORAGE'
								], (e) => {
									//关闭
									if (e.granted.length > 0) {
										//执行你有权限后的方法
										that.$refs.perpopup.close();
										that.appScan();
									}
									if (e.deniedAlways.length > 0) {
										//权限被永久拒绝
										uni.showModal({
											title: '提示',
											content: '扫码和识别本地图片权限被拒绝，是否前往开启权限',
											success: (res) => {
												if (res.confirm) {
													// 弹出提示框解释为何需要读写手机储存权限，引导用户打开设置页面开启
													var main = plus.android.runtimeMainActivity();
													var Intent = plus.android.importClass(
														'android.content.Intent');
													//直接进入应用列表的权限设置
													var mIntent = new Intent(
														'android.settings.APPLICATION_SETTINGS'
													);
													main.startActivity(mIntent);
													that.$refs.perpopup.close();
												} else if (res.cancel) {
													console.log('用户点击取消');
													that.$refs.perpopup.close();
												}
											}
										});
									}
									if (e.deniedPresent.length > 0) {
										//权限被临时拒绝
										// 弹出提示框解释为何需要读写手机储存权限，可再次调用plus.android.requestPermissions申请权限
										plus.android.requestPermissions(['android.permission.CAMERA']);
										that.$refs.perpopup.close();
									}
								});
							} else {
								//执行你有权限后的方法
								that.appScan();
							}
						},
						(error) => {
							console.error('Error checking permission:', error.message);
						}
					);
				} else {
					//执行你有权限后的方法 ios
					that.appScan();
				}
				// #endif
			},

			appScan() {
				// #ifdef APP-PLUS
				// 允许从相机和相册扫码
				mpaasScanModule.mpaasScan({
						// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
						scanType: ['qrCode', 'barCode'],
						// 是否隐藏相册，默认false不隐藏
						hideAlbum: false
					},
					(res) => {
						if (res.resp_code == 1000 && res.resp_message == 'success') {
							const code = res.resp_result; //result	所扫码的内容
							if (code) {
								if (code.indexOf('https://') > -1) {
									util.scanH5ToMaPage(code, api.qrcodequery);
									return;
								}
								api.goodsGetCode(code)
									.then((res) => {
										console.log('res', res);
										let goodsList = res.data;
										if (res.ok) {
											if (goodsList) {
												let data = JSON.stringify(goodsList);
												if (goodsList.length == '1') {
													//有一个商品就到商品详情页
													uni.navigateTo({
														url: `/pages/goods/goods-detail/index?id=${goodsList[0].id}`
													});
													return;
												}
												//有超过一个商品就展示到列表
												uni.navigateTo({
													url: `/pages/goods/goods-code-list/index?goodsList=${encodeURIComponent(data)}`
												});
											} else {
												//没有商品提示用户
												uni.showToast({
													title: `该商品不存在`,
													icon: 'none',
													duration: 2000
												});
											}
										}
									})
									.catch((res) => {
										console.log('=res=', res);
									});
							} else {
								uni.showToast({
									title: `未识别到内容`,
									icon: 'none',
									duration: 2000
								});
							}
						} else {
							// uni.showToast({
							// 	title: `未识别到信息`,
							// 	duration: 2000
							// });
						}
					}
				);
				// #endif
			},

		}
	}
</script>

<style scoped lang="scss">
	.permissions_box {
		padding: 200rpx 30rpx 50rpx;
		background-color: #fff;
		color: #000000;
	}
</style>