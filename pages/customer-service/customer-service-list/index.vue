<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">客服列表</block>
    </cu-custom>

    <view v-if="customerServiceData && customerServiceData.length>0">
      <view
        style="height: 20rpx; width: 100%"
        :class="'background-color: bg-' + theme.backgroundColor"
      >
      </view>

      <view
        class="cu-list menu-avatar radius-lg position-top"
        v-if=""
      >
        <navigator
          :url="'/pages/message/chat/index?userId=' + item.id + params"
          hover-class="none"
          class="cu-item"
          v-for="(item, index) in customerServiceData"
          :key="index"
        >
          <view
            class="cu-avatar round lg"
            :style="'background-image:url(' + item.avatar + ');'"
          >
            <text
              v-if="!item.avatar"
              class="cuIcon-people"
            ></text>
          </view>

          <view class="flex justify-between">
            <view class="content">
              <view>{{
							 item.nickName
							   ? item.nickName
							   : item.username
							   ? item.username
							   : '客服'
						   }}</view>
            </view>
            <text class="cuIcon-right"></text>
          </view>
        </navigator>
      </view>

      <view
        v-if="loadmore"
        :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"
      ></view>
    </view>
    <no-data
      v-else
      :tip="'该店铺暂无客服'"
    />

    <recommendComponents
      v-if="!loadmore"
      canLoad
    />
  </view>
</template>

<script>
import recommendComponents from "components/recommend-components/index";
import noData from "components/base-nodata/index";
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'

export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      loadmore: true,
      customerServiceData: [],
      params: '', // url 跳转时额外的参数
      shopInfo: {}
    };
  },

  components: {
    recommendComponents,
    noData
  },
  props: {},

  onLoad (options) {
    console.log("=======>", this.theme);
    let shopId = options.shopId
    if (options.goodsSpuId) {
      this.params = '&goodsSpuId=' + options.goodsSpuId + '&shopId=' + options.shopId;
      this.goodsSpuId = options.goodsSpuId;
    }
    if (options.orderInfoId) {
      this.params = '&orderInfoId=' + options.orderInfoId + '&shopId=' + options.shopId;
    }
    if (shopId && shopId > 0) {
      app.initPage().then(res => {
        this.customerServiceList(shopId);
        this.shopInfoGet(shopId);
      });
    } else {
      this.getPlatformCustomerServiceInfo();
    }
  },

  methods: {
    customerServiceList (shopId) {
      api.customerServiceList(shopId).then(res => {
        let customerServiceData = res.data;
        this.customerServiceData = customerServiceData;
        this.loadmore = false;
      });
    },
    shopInfoGet (shopId) {
      api.shopInfoGet(shopId).then(res => {
        let shopInfo = res.data;
        this.shopInfo = shopInfo;
        this.params += '&shopName=' + shopInfo.name;
      });
    },

    getPlatformCustomerServiceInfo () {
      api.platformCustomerServiceList().then(res => {
        this.customerServiceData = res.data;
        this.loadmore = false;
      });
    },
  }
};
</script>
<style scoped>
.position-top {
  position: relative;
  top: -20rpx;
}
</style>
