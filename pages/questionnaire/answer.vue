<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{page_title}}</block>
		</cu-custom>

		<view class="answer-layout">
			<uni-forms ref="form">
				<q-item v-for="(item,index) in formList" :key="index" :node="item" @setValue="setItemValue"> </q-item>
			</uni-forms>
			<button @click="submit">提交</button>
		</view>
	</view>
</template>

<script>
	import api from 'utils/api'
	import QItem from './components/q-item.vue';
	const util = require("utils/util.js");
	const app = getApp();
	import {
		gotoPage
	} from 'components/div-components/div-base/div-page-urls.js'
	import {
		getCentreType
	} from '@/pages/questionnaire/api/questionnaire.js'

	export default {
		components: {
			QItem
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				loading: false,
				id: '',
				//有数统计使用
				page_title: '问卷调查',
				// 表单数据
				formList: [],
				formData: {},
				pagesLength: 1,
			}
		},
		onLoad(options) {
			if (options.scene) {
				//接受二维码中参数
				let scenes = decodeURIComponent(options.scene);
				this.id = util.UrlParamHash(scenes, 'id');
			} else {
				this.id = options.id;
			}
			//监听当前页面栈的个数内容
			let pages = getCurrentPages()
			this.pagesLength = pages.length; //当前页面栈的个数
			const userInfo = uni.getStorageSync('user_info');
			getCentreType({
				id: this.id,
				memberCard: userInfo.erpCid,
			}, this.goPage).then(res => {
				if (res && res.code == 0 && res.data) {
					this.page_title = res.data.title;
					this.formList = JSON.parse(res.data.text);
					this.pageUrl = res.data.linkUrl || '/pages/home/<USER>';
				}
			}).catch(e => {})

		},
		// 实例创建完成，调用 uni.$on() 方法
		created() {
			uni.$on('updateData', (v) => {
				console.log(v,'接收')
				this.formList[v.index].answer = v.answer;
				this.$set(this.formList[v.index],'other', v.other);
			})
				console.log(this.formList)
		},
		methods: {
			goPage() {
				gotoPage(this.pageUrl || '/pages/home/<USER>', this.pagesLength, true)
			},
			setItemValue(e) {
				// console.log("==num, value======", e)
				// this.formList[0].answer = e.answer;
				// this.$set(this.formList, e.num - 1, {
				// 	...this.formList[e.num - 1],
				// 	answer: e.answer
				// });
				// console.log("==this.formList======", this.formList);
				// {num: 2, linetext: "0"}
				// this.$refs.form.setValue('linetext', )
			},
			/**
			 * 复写 binddata 方法，如果只是为了校验，无复杂自定义操作，可忽略此方法
			 * @param {String} name 字段名称
			 * @param {String} value 表单域的值	
			 */
			// binddata(name,value){
			// 通过 input 事件设置表单指定 name 的值
			//   this.$refs.form.setValue(name, value)
			// },
			// 触发提交表单
			submit() {
				let that = this
				for (let i = 0; i < this.formList.length; i++) {
					console.log(this.formList[i])
					// console.log(this.formList[i].answer===null,this.formList[i].answer=='')		
					if (this.formList[i].require) {
						if (this.formList[i].answer === null || this.formList[i].answer == '') {
							uni.showToast({
								icon: 'none',
								title: '请输入' + this.formList[i].name
							})
							return
						}
					}
				}
				const userInfo = uni.getStorageSync('user_info');
				let data = {
					questionnaireId: this.id,
					answer_json: JSON.stringify(this.formList),
					member_phone: userInfo.phone,
					member_appid: userInfo.appId,
					member_card: userInfo.erpCid,
					answer_title: this.page_title,
					memberNickName:userInfo.nickName
				}
				api.saveAnswer(data).then(res => {
					
					// for(let i=0;i<this.formList.length;i++){
					// 	console.log(this.formList[i].answer,this.formList)
					// 	if(this.formList[i].answer===null||this.formList[i].answer==''){
					// 		uni.showToast({
					// 			icon: 'none',
					// 			title: '请输入' + this.formList[i].title
					// 		})
					// 	 	break
					// 	}
					// }
					if (res.code == '0') {
						// uni.showToast({
						// 	title: '提交成功'
						// })
						uni.showModal({
							title: '提示',
							showCancel: false,
							content: '问卷已填写完毕',
							success(res) {},
							complete() {
								that.goPage();
							},
						});
						for (let o = 0; o < this.formList.length; o++) {
							if (this.formList[o].answer&&this.formList[o].type!='9'&&this.formList[o].type!='10') {
								this.formList[o].answer = ''
							}
						}
						that.$children[1].$children.forEach(function(item, index) {
							if (item.node.type == '4') {
								uni.$emit('value', {})
							} else if (item.node.type == '6') {
								uni.$emit('clearImg', {})
							}

						})
					}
				})
			}
		},
	}
</script>

<style scoped>
	.answer-layout {
		background-color: #fff;
	}
</style>
