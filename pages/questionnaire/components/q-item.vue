<template>
	<view>
		<view  v-if="node.type=='9'||node.type=='10'" style="font-weight: 700">
		</view>
		<view v-else style="font-weight: 700;margin-left: 20rpx;margin-right: 20rpx;">
			<text v-if="node.require" class="requry">*</text>  <text v-else style="margin-left: 16rpx;"></text>
			<text> {{label}}</text>
		</view>
		<c1 v-if="node.type==1" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
		<c2 v-else-if="node.type==2" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
		<c3 v-else-if="node.type==3" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
		<c4 v-else-if="node.type==4" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
		<c5 v-else-if="node.type==5" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
		<c6 v-else-if="node.type==6" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
		<c7 v-else-if="node.type==7" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
		<c8 v-else-if="node.type==8" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
		<c9 v-else-if="node.type==9" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
		<c10 v-else-if="node.type==10" :node="node" style="margin-left: 20rpx;margin-right: 20rpx;" />
	</view>
</template>
<script>
	import C1 from './slots/1.vue';
	import C2 from './slots/2.vue';
	import C3 from './slots/3.vue';
	import C4 from './slots/4.vue';	
	import C5 from './slots/5.vue';
	import C6 from './slots/6.vue';
	import C7 from './slots/7.vue';
	import C8 from './slots/8.vue';
	import C9 from './slots/9.vue';
	import C10 from './slots/10.vue';

	export default {
		name: 'questionnaireItem',
		components: {
			C1,
			C2,
			C3,
			C4,
			C5,
			C6,
			C7,
			C8,
			C9,
			C10
			
		},
		props: ['node', 'index'],
		computed: {
			label() {
				console.log(this.node)
				const fullRes = this.node.num  + '. ' + this.node.title;
				if (fullRes) {
					return ' ' + fullRes.replace(/<.*?>/ig, "");
				}
				return ""
			}
		},
		methods: {
			// setValue(e) {
			// 	this.$emit('setValue', e)
			// }
		}
	}
</script>
<style lang="less" scoped>
	.uni-forms-item {
		.top {
			.interlayer {
				display: flex !important;
			}
		}
	}
		
	.requry{
		color: red;
	}
</style>
