<template>
  <view style="display: flex;justify-content: center;margin-top: 30rpx;margin-bottom: 40rpx;border-bottom: 1px dashed #c1bdbf;
    padding-bottom: 40px;
    margin-left: 20rpx;
    margin-right: 20rpx;">
			 <jyf-parser :html="node.answer"></jyf-parser>
  </view>
  
</template>

<script>
	export default {
		props: ['node'],
		data() {
			return {
			
			}
		},
		methods: {
		
		},
		created() {
			console.log(this.node.answer)
		}
	}
</script>

<style>

</style>
