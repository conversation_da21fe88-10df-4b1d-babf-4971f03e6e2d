<template>
	<view   style="margin-bottom: 30rpx;color: #333333;display: flex;margin-left: 20rpx;margin-right: 20rpx;margin-top: -10prx;">
		<view class="" v-for="(item,index) in arr" :key="index"  style="display: flex;">
			 <text>{{item}}</text>
			<input type="text"  @blur="setValue(index, $event)"  style="border-bottom: 1px solid black;width:230rpx;padding-left: 10rpx;" /> 
		</view>
	</view>
</template>

<script>
	export default {
		props: ['node'],
		data() {
			return {
				arr:'',answ:'',
				arrs:[]
				
			}
		},
		computed: {
		},
		methods: {
			setValue(index, event) {
				  let arrs = []
				  let that =this
				this.arr.forEach(function(item,indexs){
					if(indexs==index){
						that.arrs[index]={'name':item,'value':event.detail.value}
					}
					
				})
		  console.log(this.arrs)
	    	uni.$emit('updateData', {
			index: Number(this.node.index),
			answer: this.arrs
		})
			}
		},
		mounted() {
			// 将字符串分割成两个部分并放入数组
			this.node.answer = []
			 this.arr = this.node.content.split("$input").filter(Boolean);
			 console.log(this.arr)
		}
	}
</script>

<style>

</style>
