<template>
	<view style="margin-bottom: 30rpx;margin-left: 20rpx;margin-right: 20rpx;margin-top: -10prx;">
		<uni-rate v-model="node.answer" @change="onChange"/>
	</view>
</template>
<script>
	import uniRate from "../uni-rate/components/uni-rate/uni-rate.vue";
	export default {props: ['node'],
		components: {
			uniRate
		},
		methods: {
			onChange(e){
		uni.$emit('updateData', {
			index: Number(this.node.index),
			answer: e.value
		})
			}
		}
	}
</script>

<style>

</style>
