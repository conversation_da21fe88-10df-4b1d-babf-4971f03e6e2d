<template>
	<view style="margin-bottom: 30rpx;margin-left: 20rpx;margin-right: 20rpx;margin-top: -10prx;" v-if='showupload'>
		<uni-file-picker v-model="fileLists" :limit="node.upload_num_max" fileMediatype="image" mode="grid"
			@select="select" @progress="progress" @success="success" @fail="fail" />
	</view>
</template>
<script>
	import uniFilePicker from "../uni-file-picker/components/uni-file-picker/uni-file-picker";
	const app = getApp();
	const util = require("utils/util.js");
	import __config from "config/env";
	import api from "utils/api";
	export default {
		props: ['node'],
		components: {
			uniFilePicker
		},
		data() {
			return {
				fileLists: [],
				showupload:true
				
			}
		},
		created() {
			uni.$on('clearImg', (v) => {
				this.showupload = false
				this.$nextTick(() => {
					this.fileLists = []
					console.log('执行了')
					this.showupload = true
				})

			})
		},
		methods: {
			select(e) {
				let that = this
				let _url = "/mallapi/file/upload";
				_url = __config.basePath + _url;
				console.log('选择文件：', e.tempFilePaths[0])
				uni.uploadFile({
					header: {
						"client-type": "MA", //客户端类型小程序
						"app-id": uni.getAccountInfoSync().miniProgram.appId, //小程序appId
						"third-session": uni.getStorageSync("third_session") ? uni.getStorageSync(
							"third_session") : "",
					},
					url: _url,
					filePath: e.tempFilePaths[0],
					name: "file",
					formData: {
						fileType: "image",
						dir: "headimg/",
					},
					success: (uploadFileRes) => {
						that.fileLists.push({
							"name": e.tempFiles[0].name,
							"extname": e.tempFiles[0].fileType,
							"url": JSON.parse(uploadFileRes.data).link,
						})
						uni.$emit('updateData', {
							index: Number(this.node.index),
							answer: that.fileLists
						})
					},
					fail: (err) => {
						console.log(err)
					},
					complete: () => {
						uni.hideLoading();
					},
				});

			},
			// 获取上传进度
			progress(e) {
				console.log('上传进度：', e)
			},

			// 上传成功
			success(e) {
				console.log('上传成功')
			},

			// 上传失败
			fail(e) {
				console.log('上传失败：', e)
			}
		}
	}
</script>
<style>
</style>
