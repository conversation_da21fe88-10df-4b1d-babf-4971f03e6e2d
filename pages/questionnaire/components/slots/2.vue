<template>
	<view style="margin-left: 20rpx;margin-right: 20rpx;margin-top: -10prx;" >
	<textarea style="min-height:200rpx; padding-left: 36rpx; border: solid 1rpx #eee;width: 100%;padding-top: 10rpx;" v-model="node.answer"
		:placeholder="node.hint" @input="setValue" auto-height />
		</view>
</template>

<script>
	export default {
		props: ['node'],
		data() {
			return {

			};
		},

		methods: {
			setValue(e) {
				uni.$emit('updateData', {
					index: Number(this.node.index),
					answer: e.detail.value
				})
			}
		}
	};
</script>

<style scoped>

</style>