<template>
	<view style="display: flex;margin-left: 20rpx;margin-right: 20rpx;margin-top: -10prx;"  v-if='show'>
		<checkbox-group class="ques-check" @change="bindchange">
			<label style="margin-left: 10rpx;display: flex;" v-for="(item, index) in node.options"
				:key="node.id + '_' + item.value">
				<checkbox :value="item.value" :checked="value.includes(item.value)" style="transform:scale(0.7); width: 49rpx;height: 40rpx;" />
				<view class="flex-wrap">
					{{item.label}}
					<input v-if="item.type"  style="height: 30rpx; line-height:30rpx; width:400rpx;margin-left:16rpx; border: solid 1px #ccc;margin-top:10rpx;"
					v-model="node.other"
					@input="setOther" />
				</view>
			</label>
		</checkbox-group>
	</view>
</template>

<script>
	export default {
		props: ['node'],
		data() {
			return {
				value:[],
				show:true
			};
		},

		methods: {
			setOther(e) {
				uni.$emit('updateData', {
					index: Number(this.node.index),
					other: e.detail.value,
					answer: this.node.answer,
				})
			},
			bindchange(e) {
				let isHasOther;
				for(let i=0; i<this.node.options.length; i++){
					console.log("=========", e.detail.value, this.node.options[i].value, e.detail.value.indexOf(this.node.options[i].value+''))
					if( e.detail.value.indexOf(this.node.options[i].value+'')>-1 && this.node.options[i].type) {
						isHasOther = true;
					}
				}
				this.value = e.detail.value
				uni.$emit('updateData', {
					index: Number(this.node.index),
					answer: e.detail.value,
					other: isHasOther?this.node.other:'',
				})
			},
			clearValue() {
			}
		},
		created() {
			uni.$on('value', (v) => {
				this.show = false
				          this.$nextTick(() => {
							  this.value= []
							  console.log('执行了')
							  this.show = true
						  })

			})
		}
	};
</script>
<style scoped  lang="scss">
	.flex-wrap {
		display: flex;
		flex-wrap:wrap;
	}
	::v-deep uni-checkbox-group{ width: 100% !important; }
	::v-deep uni-checkbox-group uni-label{ width: 33% !important; display: inline-flex; margin-bottom: 20rpx; }
	/*checkbox 选项框大小  */
	::v-deep uni-checkbox .uni-checkbox-input{ width: 30rpx !important; height: 30rpx !important; }
	/*checkbox选中后样式  */
	::v-deep uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked{ background: #3D7EFF; border-color:#3D7EFF; }
	/*checkbox选中后图标样式  */
	::v-deep uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked::before{
	    width: 20rpx;
	    height: 20rpx;  
		line-height: 20rpx;
		text-align: center;
		font-size: 18rpx;
		color: #fff;
		background: transparent;
		transform: translate(-70%, -50%) scale(1);
		-webkit-transform: translate(-70%, -50%) scale(1);
	}

</style>
