<template>
<view  style="margin-left: 20rpx;margin-right: 20rpx;margin-top: -10prx;" >
	<radio-group @change="setValue">
		<view class="radio-list-cell">
			<label class="radio-list-cell" style="margin-left: 10rpx;" v-for="(item, index) in node.options" :key="node.id + '_' + item.value">
					<radio style="transform:scale(0.7);width: 49rpx;height: 40rpx;" :value="item.value" :checked="item.value == node.answer" />
				<view class="radio-list-cell">
				    {{item.label}}
					<input v-if="item.type"  style="height: 30rpx; line-height:30rpx; width: 200rpx;margin-left:16rpx; border: solid 1px #ccc;margin-top:10rpx;"
					v-model="node.other"
					@input="setOther" /></view>
			</label>
		</view>
	</radio-group>
</view>
</template>

<script>
	export default {
		props: ['node'],
		data() {
			return {

			};
		},

		methods: {
			setOther(e, item) {
				uni.$emit('updateData', {
					index: Number(this.node.index),
					other: e.detail.value,
					answer: this.node.answer,
				})
			},
			
			setValue(e) {
				let selectItem;
				for(let i=0; i<this.node.options.length; i++){
					if(this.node.options[i].value== e.detail.value) {
						selectItem = this.node.options[i];
						break;
					}
				}
				if(selectItem){
					uni.$emit('updateData', {
						index: Number(this.node.index),
						answer: e.detail.value,
						other: selectItem.type?this.node.other:'',
					})
				}
			}
		}
	};
</script>

<style scoped>
	.radio-list-cell {
		display: flex;
		flex-wrap:wrap;
	}
</style>
