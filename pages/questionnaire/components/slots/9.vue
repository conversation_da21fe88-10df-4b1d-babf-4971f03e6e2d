<template>
	<view style="margin-top: -44rpx;height: 300rpx;">
		<swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval"
			:duration="duration"   style="height: 300rpx;">
			<swiper-item v-for="(item,index) in node.answer" :key="index"   style="height: 300rpx;">
				<navigator :url="item.pageUrl">
				<view class="swiper-item uni-bg-red"   style="height: 300rpx;">
				     	<image :src="item.imageUrl"  style="height: 300rpx;"></image>
				</view>
				</navigator>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	export default {
		props: ['node'],
		data() {
			return {
				indicatorDots: true,
				autoplay: true,
				interval: 2000,
				duration: 500
			}
		},
		methods: {
			changeIndicatorDots(e) {
				this.indicatorDots = !this.indicatorDots
			},
			changeAutoplay(e) {
				this.autoplay = !this.autoplay
			},
			intervalChange(e) {
				this.interval = e.target.value
			},
			durationChange(e) {
				this.duration = e.target.value
			}
		},
		created() {
			console.log(this.node.answer)
		}
	}
</script>

<style>

</style>
