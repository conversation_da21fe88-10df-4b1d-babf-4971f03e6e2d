<template>
	<view style="margin-left: 20rpx;margin-right: 20rpx;margin-top: -10prx;">
	<uni-datetime-picker v-model="node.answer"  @change="setValue" />
	</view>
</template>

<script>
	import uniDatetimePicker from "../uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker";
	export default {
		props: ['node'],
		components:{
		  uniDatetimePicker	
		},
		data() {
			return {
				single:''
			}
		},
		methods: {
			setValue(e) {
			  uni.$emit('updateData', {
			  	index: Number(this.node.index),
			  	answer: e
			  })
			}
		}
	}
</script>

<style>

</style>
