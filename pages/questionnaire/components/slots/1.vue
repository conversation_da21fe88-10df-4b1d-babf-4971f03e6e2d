<template>
<view style="margin-left: 20rpx;margin-right: 20rpx;margin-top: -10prx;" >
	<input style="height: 100%; line-height: 100%;padding-left: 36rpx;"
	v-model="node.answer"
	:placeholder="node.hint" 
	:type="node.input_type"
    @input="setValue" />
</view>
	  
		
			
	
</template>
<script>
	export default {
		props: ['node'],
		data() {
			return {
			};
		},
		methods: {
            setValue(e) {
				console.log(this.node.num,e.detail.value)
				uni.$emit('updateData', {
					index: Number(this.node.index),
					answer: e.detail.value
				})
			}
		},
		created() {
			if(!this.node.answer){
			}else{
				this.node.answer = ''
			}
		}
	};
</script>

<style scoped>

</style>
