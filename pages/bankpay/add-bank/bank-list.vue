<template>
	<!-- 添加银行卡,查询支持银行页面 -->
	<view :style="{
		height: windowHeight+'px',
		backgroundColor:'#fff'
	}">
		 <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">银行卡列表</block>
    </cu-custom>

		<scroll-view
      scroll-x
      class="bg-white nav fixed"
    >
      <view class="flex text-center">
        <view
          :class="
            'cu-item flex-sub ' +
            (index == tabCur ? 'cur text-' + theme.themeColor : '')
          "
          style="padding: 0rpx"
          v-for="(item, index) in bankStatus"
          :key="index"
          @tap="tabSelect"
          :data-index="index"
          :data-key="item.key"
        >{{ item.value }}</view>
      </view>
    </scroll-view>

		<view style="margin-top:130rpx">
      <view
        class="flex bill"
        v-for="(item,index) in bankList"
        :key="index"
				style="margin-left: 25rpx;margin-bottom: 20rpx;"
      >
			<!-- <image style="width: 100rpx;height: 100rpx;margin-right: 15rpx;" src="https://img.songlei.com/1/material/28c2c868-b48b-44a4-bd6a-aab63a05a736.jpg"></image> -->
			<image mode="widthFix" style="width: 252rpx; margin: auto;" :src="item.bankLogo"></image>

        <view style="width: 82%;border-bottom: solid 1rpx #eee;">
						<view style="font-size: 30rpx;color: #000000;">
							{{item.bankName}}
						</view>

						<view
						v-if="tabCur=='1'&&item.cardType=='2'"
							class="text-sm"
							style="color: #A2A2A2;"
						>
						{{item.creditCardLimit}}
					 </view>
					 <view
						v-else
							class="text-sm"
							style="color: #A2A2A2;"
						>
						{{item.debitCardLimit}}
					 </view>
        </view>
      </view>
      <view :class="'cu-load ' + (loadmoreRecommendList ?'loading':'over')"></view>
    </view>
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api'
	import validate from 'utils/validate'
	import __config from '@/config/env'; // 配置文件

	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				tabCur: 0,
				bankStatus: [{
					value: '储蓄卡',
					key: '0'
				}, {
					value: '信用卡',
					key: '1'
				}],
				bankList:[
				// 	  {
				// 			bankName: '1民生银行',
        //   trans_time: '2022-11-12',
        //   amount: '1',
        // }, {
        //   bankName: '2民生银行',
        //   trans_time: '2022-11-1',
        //   amount: '10',
        // }, {
        //   bankName: '3民生银行',
        //   trans_time: '2022-11-2',
        //   amount: '11',
        // }, {
        //   bankName: '4民生银行',
        //   trans_time: '2022-01-12',
        //   amount: '12',
        // }, {
        //   bankName: '5民生银行',
        //   trans_time: '2022-01-02',
        //   amount: '100',
        // },
				],//银行卡列表
				allBanklist:[],//所有银行卡列表
			};
		},
		onLoad(options) {
			let that = this;
			that.getPayBankList()
		},
		

		methods: {
			getPayBankList () {
      api.getPayBankList().then(res => {
				// console.log("allBanklist",res);
        this.allBanklist = res.data;
				if (this.tabCur=='0') {
						this.bankList=this.allBanklist.filter(item=>{return item.cardType=='1'})
				}
      });
    },

			// refresh () {
      // this.loadmore = true;
      // this.bankList = [];
      // this.page.current = 1;
      // this.orderPage();
    // },

				tabSelect (e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.index != this.tabCur) {
					this.tabCur = dataset.index;
					if (this.allBanklist) {
						if (this.tabCur=='0') {
							this.bankList=this.allBanklist.filter(item=>{return item.cardType=='1'})
						}
						if (this.tabCur=='1') {
							uni.showToast({
								title: '暂不支持信用卡开通支付！',
								icon: 'none',
								duration: 3000
							});
							// this.bankList=this.allBanklist.filter(item=>{return item.cardType=='2'})
							this.bankList=[]
							
						}
					}
						this.loadmore = true;
					// this.refresh();
				}
			},
		}
	};
</script>

<style scoped lang="scss">
.nav {
  top: unset !important;
}
</style>
