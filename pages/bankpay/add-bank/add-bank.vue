<template>
	<!-- 银行卡支付,添加银行卡页面 -->
	<view :style="{
		height: windowHeight+'px',
		backgroundColor:'#fff'
	}">
		<cu-custom :hideMarchContent="true" :isBack="true" :bgColor="bgColor">
			<block slot="backText">返回</block>
			<block slot="content">添加银行卡</block>
		</cu-custom>
		<view class="list-container">
			<view class="add-form" :style="{marginTop: `${(CustomBar>0?CustomBar:60)}px`,}">
				<form @submit="addSub">
					<view class="form-item">
					  <text class="form-label">卡号</text>	
						<input class="form-input" @blur="getBankType" type="number" placeholder="请输入本人银行卡号" name="accountNo" v-model="form.accountNo">
					</view>

					<view class="form-tip" @tap="toBankList">查看支持银行卡 ></view>

					<view class="form-item">
					  <text class="form-label">卡类型</text>	
						<input disabled class="form-input" placeholder="请输入卡号获取卡类型" name="cardType"  v-model="bankNameType">
					</view>

					<view class="form-item">
					  <text class="form-label">姓名</text>	
						<input class="form-input" type="nickname" placeholder="请输入本人银行卡姓名" name="accountName" v-model="form.accountName">
					</view>

					<view class="form-item">
					  <text class="form-label">证件类型</text>	
						<input  disabled class="form-input" name="certType" v-model="id">
					</view>

					<view class="form-item">
					  <text class="form-label">证件号</text>	
						<input class="form-input" type="text" placeholder="请输入本人证件号" name="certNo" v-model="form.certNo">
					</view>
					
					<view class="form-item">
					  <text class="form-label">手机号</text>	
						<input class="form-input" type="number" placeholder="银行卡预留手机号" name="mobileNo" v-model="form.mobileNo">
					</view>
					
					<view class="flex flex-direction" style="margin-top: 30rpx;">
						<button class="cu-btn margin-btn"
							style="color:#fff;background-color: #ed8681;font-size: 32rpx; height: 90rpx;"
							form-type="submit">下一步</button>
					</view>
				</form>
			</view>
		</view>

		<!--组件与钱包余额通用--> 
		<verification-code-popup @change="mobileCodeChange($event)" @HideModal="HideModal" :showModal="showModal" :mobileNo="form.mobileNo"></verification-code-popup>
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api'
	import validate from 'utils/validate'
	import __config from '@/config/env'; // 配置文件

	import verificationCodePopup from "components/verification-code-popup/index.vue";


	export default {
		components:{
			verificationCodePopup
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				bgColor: "rgba(255, 255, 255, 0)",
				CustomBar: this.CustomBar,
				form: {
					accountNo: '',//卡号
					cardType:'',//卡类型
					accountName: '',//用户姓名
					certType:'01',//开户证件类型
					certNo:'',//证件号
					mobileNo:'',//手机号
					payBankId:'',//银行卡id
					oritranFlow:'',//原签约订单号,发起CAS028上送的流水号
					phoneToken:'',//绑定的手机号令牌信息
					phoneverCode:'',//手机号验证码
				},
				windowHeight: 600,
				bankNameType:'',//银行卡类型名称
				id:'身份证',
				showModal: false,//弹框控制
			};
		},
		onLoad(options) {
			let that = this;
			uni.getSystemInfo({
			  success (res) {
			    that.windowHeight = res.windowHeight;
			  }
			})
		},
		

		methods: {
			HideModal(val){
				this.showModal = val
			},
		
			async mobileCodeChange(value) {
  			console.log("value",value)
				this.form.phoneverCode =value
				this.authorizeBankCard()
			},

			//跳转到支持银行列表
			toBankList(){
				uni.navigateTo({
					url: '/pages/bankpay/add-bank/bank-list'
				});
			},

			// 签约添加成功
			authorizeBankCard(){
				api.authorizeBankCard(this.form).then((res) => {
						console.log('authorizeBankCard', res);
						if (res.code == '0') {
							const pages = getCurrentPages(); // 获取页面栈
							const prevPage = pages[pages.length - 2]; // 上一个页面
							// 添加成功
							uni.showToast({
								title: '银行卡添加成功！',
								icon: 'none',
								duration: 1500
							});
							
							setTimeout(() => {
								uni.hideToast(); 
								//跳转路由
								
								if (prevPage) {
									prevPage.$vm.updataBankList();
								}
								uni.navigateBack({
									delta:1,//返回层数，2则上上页
								})
							}, 2000)
						}
					}).catch((err) => {
						console.log("err", err);
					})
			},

			//获取卡号类型
			getBankType(){
				this.getCardDetail()
			},

			//获取卡类型
			getCardDetail () {
				if (!this.form.accountNo) {
					uni.showToast({
						title: '卡号不能为空！',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				api.getCardDetail(this.form.accountNo).then((res) => {
					console.log('getCardDetail', res);
					if (res.code == '0') {
							let data = res.data			
							if(data.cardType=='1'){
								this.form.cardType =data.cardType;
								this.bankNameType = `${data.bankName}`+' 借记卡';
								this.form.payBankId =data.id;
							}
							if(data.cardType=='2'){
								this.form.cardType =data.cardType;
								this.bankNameType = `${data.bankName}`+' 信用卡';
								this.form.payBankId =data.id;
							}
					}
				}).catch((err) => {
					console.log("err", err);
				})
			},

			addSub(e) {
				if (!validate.validateMobile(this.form.mobileNo)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (!this.form.certNo) {
					uni.showToast({
						title: '证件号不能为空！',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (!this.form.accountName) {
					uni.showToast({
						title: '姓名不能为空！',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (this.form.cardType=='2') {
					uni.showToast({
						title: '暂不支持信用卡开通支付！',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				//身份证比编号默认写死
				this.form.certType = '01'
				//绑定银行卡
				api.bindBankCard(this.form).then((res) => {
					console.log('bindBankCard', res);
					if (res.code == '0') {
							let bindBankInfo = res.data;
							this.form.phoneToken =bindBankInfo.phoneToken
							this.form.oritranFlow =bindBankInfo.oritranFlow
							console.log("showModal",this.showModal)
							this.showModal = true;
					} 
				}).catch((err) => {
					console.log("err", err);
				})
			}
		}
	};
</script>

<style scoped lang="scss">

	page {
	  background-color:#fff;
	}
	.list-container {
		position: absolute;
		top: 0;
		background-image: url(https://img.songlei.com/live/bankpay/top_bg.png);
		background-size: 100% 500rpx;
		width: 100%;
		background-repeat: no-repeat;
	}

	.add-form {
		padding: 90rpx 40rpx 200rpx 40rpx;
		height: 100%;
	}

	.form-item {
		padding: 30rpx 0;
		border-radius: 4rpx;
		height: 110rpx;
		border-bottom: solid #dddddd 1rpx;
		margin-top: 10rpx;
		display: flex;
		color: #000000;
	}
	
	.form-label {
		font-size: 28rpx;
		color: #000000;
		width: 160rpx;
		display: inline-block;
	}

	.form-input {
		font-size: 28rpx;
		width: 350rpx;
	}
	
	.form-tip {
		font-size: 22rpx;
		color: #000000;
		margin-top: 14rpx;
	}
</style>
