<template>
  <!-- 重构原松鼠乐园我的奖品 -->
  <view
      class="myPrize-page"
      :style="{
      background: backgroundColor,
    }"
  >
    <!-- 顶部导航背景 -->
    <image
        class="navbg"
        :src="
        waterTemplate.templateComponent
          ? waterTemplate.templateComponent.prizeTopUrl
          : iconPic.cloud
        "
        :style="{ height: CustomBar + 'px' }"
    />

    <!-- 自定义导航栏 -->
    <cu-custom
        bgColor="rgba(255, 255, 255, 0)"
        :isBack="true"
        :hideMarchContent="true"
        :iconColor="'#000'"
        :boderColor="'#000'"
        :lineColor="'#000'"
        :textColor="'#000'"
    >
      <block slot="backText">返回</block>
      <block slot="content" style="color: #000000"> 我的奖品</block>
    </cu-custom>

    <!-- 奖品顶部背景图 -->
    <image
      :src="
        waterTemplate.templateComponent
          ? waterTemplate.templateComponent.myPrizeBgUrl
          : ''
      "
        mode="widthFix"
        style="width: 100%; height: 309rpx"
    ></image>

    <!-- 奖品内容区域 -->
    <view
        :style="{
        position: 'absolute',
        top: `${CustomBar}px`,
        left: '0rpx',
        right: '0rpx',
      }"
    >
      <!-- 导航按钮 -->
      <view class="plr-xxxsm" style="z-index: 2; width: 100%; position: fixed">
        <scroll-view
            :scroll-into-view="'A' + type"
            scroll-with-animation
            scroll-x
        >
          <view
              class="collection-types text-center flex"
              :class="actArr.length > 4 ? '' : 'justify-between'"
              enable-flex
			 :style="{
				 boxShadow: 'none',
				 'z-index': '1',
				 height: '90rpx',
				 backgroundColor: waterTemplate.templateComponent?'':'#f2f2f2',
			   }"
          >
            <view
                :id="'A' + item.key"
                :class="
                'btn ' +
                (item.key == type ? 'cur bg-' + theme.backgroundColor : '') +
                ' ' +
                (actArr.length > 4 ? 'min-height-nav' : '')
              "
                v-for="(item, index) in actArr"
                :key="index"
                @tap="navTabSelect"
                :data-index="index"
                :data-key="item.key"
            >{{ item.value }}</view
            >
          </view>
        </scroll-view>
      </view>

      <!-- 抽奖转盘入口，marketPath存在时显示 -->
      <view class="margin-lr" v-show="marketPath" @click="lotteryOpen">
        <image
            :src="
            waterTemplate.templateComponent
              ? waterTemplate.templateComponent.turntableUrl
              : ''
          "
          mode="widthFix"
          style="width: 100%; height: 309rpx; border-radius: 20rpx"
        ></image>
      </view>

      <!-- 奖品列表 -->
      <view
          class="core-outbox"
          :class="marketPath ? '' : 'padding-top-sm'"
          :style="prizeListStyle"
          v-if="prizeList.length"
      >
        <scroll-view
          scroll-y
          refresher-enabled
          class="flex-sub"
          :refresher-triggered="triggered"
          @scrolltolower="reachBottom"
          @refresherrefresh="refresher"
          style="overflow: hidden; height: 100%"
        >
          <block v-for="(item, index) in prizeList" :key="index">
            <!-- 盲盒奖品 -->
            <!-- <view
              class="core-box"
              :style="{ 'margin-top': marketPath && index === 0 ? '0' : '20rpx' }"
              v-if="item.blindBox"
            >
              <view class="zou-box">
                <image class="tupian" :src="item.blindBox.url" mode="aspectFit" />
              </view>
              <view class="you-box">
                <view class="hang">{{ item.blindBox.name }}</view>
                <view class="hang">奖励时间：{{ item.createTime }}</view>
                <view class="hang">获奖来源：{{ item.source }}</view>
                <view class="hang">活动名称：{{ item.waterInfoName }}</view>
                <view class="hang qita" v-if="item.blindBox.prizeType === 3">
                  <view style="min-width: 130rpx">兑奖期限：</view>
                  <view>{{ item.blindBox.prizeStartTime }} - {{ item.blindBox.prizeEndTime }}</view>
                </view>
                <view class="anniu-box">
                  <view class="anniu" @click="handlePrize(item, index)">
                    <view class="wen">{{ item.btnVal }}</view>
                    <image class="btn" :src="item.btnUrl" />
                  </view>
                  <view
                    class="anniu"
                    v-if="item.blindBox.prizeType === 3"
                    @click="giftOpen(item)"
                  >
                    <view class="wen">中奖须知</view>
                    <image class="btn" :src="iconPic.huang" />
                  </view>
                </view>
              </view>
            </view> -->

            <!-- 非盲盒奖品 -->
            <!-- <view
              class="core-box"
              :style="{ 'margin-top': marketPath && index === 0 ? '0' : '20rpx' }"
              v-else
            > -->
            <view
              class="core-box"
              :style="{
                'margin-top': marketPath && index === 0 ? '0' : '20rpx',
              }"
            >
              <view class="zou-box">
                <image
                  class="tupian"
                  :src="item.prizeImg ? item.prizeImg : ''"
                  mode="aspectFit"
                />
              </view>
              <view class="you-box">
                <view class="hang">{{ item.prizeName }}</view>
                <view class="hang">奖励时间：{{ item.drawTime }}</view>
                <view class="hang">获奖来源：{{ item.actTypeDesc }}</view>
                <view class="hang">活动名称：{{ item.prizeSource }}</view>
                <view class="hang qita" v-if="item.prizeType === 3">
                  <view style="min-width: 130rpx">兑奖期限：</view>
                  <view
                  >{{ item.waterPrize.prizeStartTime }} -
                    {{ item.waterPrize.prizeEndTime }}</view
                  >
                </view>
                <view class="anniu-box">
                  <view class="anniu" @click="handlePrize(item)">
                    <view class="wen">{{ item.btnVal }}</view>
                    <image class="btn" :src="item.btnUrl ? item.btnUrl : ''" />
                  </view>
                  <view
                      class="anniu"
                      v-if="item.prizeType === 3"
                      @click="giftOpen(item)"
                  >
                    <view class="wen">中奖须知</view>
                    <image
                      class="btn"
                      :src="iconPic.huang ? iconPic.huang : ''"
                    />
                  </view>
                </view>
              </view>
            </view>
          </block>
          <view :class="'cu-load ' + (load ? 'loading' : 'over')"></view>
        </scroll-view>
      </view>

      <!-- 暂无数据展示 -->
      <view class="core-outbox" :style="prizeListStyle" v-else>
        <view class="kong-box">
          <view class="da">暂无数据</view>
          <!-- <view class="xiao">完成{{waterTemplate.tip2}}任务即可得奖品哦~！</view> -->
          <image
              class="tu"
              :src="
              waterTemplate.templateComponent
                ? waterTemplate.templateComponent.runOutUrl
                : ''
            "
          />
          <view class="btn-box" @click="goBack">
            <view class="wen">返回</view>
            <image class="btn" :src="iconPic.huang" />
          </view>
          <image
              class="bg"
              :src="
              waterTemplate.templateComponent
                ? waterTemplate.templateComponent.popNoneUrl
                : ''
            "
          />
        </view>
      </view>

      <!-- 抽奖转盘弹窗 要重新对字段-->
      <pop-lottery
          v-if="showLotteryDialog"
          :lotteryId="marketPath"
          @close="lotteryClose"
      />

      <!-- 赠品中奖须知弹窗组件 要重新对字段-->
      <GiftNoticePopup
          ref="giftPopup"
          :prizeInfo="prizeInfo"
          @close="giftClose"
      />

      <!-- 线下券核销弹窗 要重新对字段-->
      <block v-if="writeShow">
        <write-off :coupon-no="couponNo" @close="closeWriteDia" />
      </block>
    </view>
  </view>
</template>

<script>
/**
 * 重构我的奖品页面逻辑
 * 作者：周五星
 * 日期：2025年5月23日
 */
import PopLottery from "@/components/lottery/pop-lottery.vue";
import WriteOff from "./components/WriteOff";
import GiftNoticePopup from "./components/GiftNoticePopup";
const app = getApp();
import { myPrizeApi, getPrize } from "@/pages/myPrize/api/myPrize";
import {
  receiveApi,
  openBlindBoxApi,
} from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  components: {
    PopLottery,
    WriteOff,
    GiftNoticePopup,
  },
  data() {
    return {
      CustomBar: this.CustomBar, // 状态栏高度
      theme: app.globalData.theme, //全局颜色变量
      actId: "", // 活动ID
      prizeList: [], // 奖品列表
      showLotteryDialog: false, // 抽奖弹窗显示控制
      marketPath: "", // 抽奖ID或路径
      page: {
        current: 1, //页码
        size: 20, //最多20条
      },
      load: true,

      prizeInfo: null, // 赠品详情信息
      // prizeInfo: {
      //   url: 'http://img.songlei.com/myPrize/sample.png',
      //   name: '精美礼品',
      //   prizeStartTime: '2024-06-01',
      //   prizeEndTime: '2024-06-30',
      //   prizeTime: '9:00 - 18:00',
      //   prizePlace: '北京市朝阳区XX路XX号',
      //   prizePeople: '张三',
      //   prizePhone: '13800000000',
      //   detail: '请携带身份证前往兑奖。',
      //   source: '活动赠送',
      // },

      couponNo: "", // 核销券号
      writeShow: false, // 核销弹窗显示控制

      iconPic: {
        huang: "http://img.songlei.com/myPrize/huang.png", // 黄色按钮图标
        cloud: "http://img.songlei.com/live/home/<USER>",
      },
      waterTemplate: {}, // 游戏模板数据
      // 优惠券所有数据
      actArr: [
        {
          value: "东北话",
          key: "3",
        },
        {
          value: "红包雨",
          key: "2",
        },
        {
          value: "大转盘",
          key: "1",
        },
      ],
      type: "3",
      triggered: false, // 下拉加载
    };
  },
  onLoad(options) {
    // 接收传入参数actId
    if (options.actId) {
      this.actId = options.actId;
    }

    // 页面显示时初始化登录并获取奖品列表
    app.initPage().then(() => {
      // 获取奖品信息
      this.getPrizeList();
    });
    this._freshing = false;
  },

  onShow() {
    // 从本地缓存获取模板数据
    this.waterTemplate = uni.getStorageSync("waterTemplate") || {};
  },
  computed: {
    /**
     * 根据模板类型返回背景颜色
     */
    backgroundColor() {
      if (!this.waterTemplate || !this.waterTemplate.templateComponent) {
        return "#f2f2f2"; // 默认颜色
      }
      const type = this.waterTemplate.templateComponent.TemplateType || "";
      if (type === "1") return "#F4FAF1";
      if (type === "2") return "#DDFDFF";
      return "#f2f2f2";
    },
    /**
     * 计算奖品列表容器样式
     */
    prizeListStyle() {
      const heightValue = this.marketPath
          ? `calc(100vh - ${this.CustomBar}px - 404rpx)`
          : `calc(100vh - ${this.CustomBar}px)`;
      const bgUrl = this.waterTemplate.templateComponent?.prizeBottomUrl || "";
      return `
        height: ${heightValue};
        padding-bottom: 10rpx;
        background-image: url(${bgUrl});
        background-repeat: no-repeat;
        background-position: left 0% bottom 0%;
        padding-top: 70rpx;
        background-size: 750rpx 137rpx;
      `;
    },
  },

  // 页面滚动到底部的事件
  onReachBottom() {
    if (this.load) {
      this.page.current = this.page.current + 1;
      // 获取奖品信息
      this.getPrizeList();
    }
  },

  // 监听用户下拉动作
  onPullDownRefresh() {
    uni.showNavigationBarLoading();
    this.refresh().finally(() => {
      uni.hideNavigationBarLoading();
      uni.stopPullDownRefresh();
    });
  },

  methods: {
    refresher() {
      console.log("-----refresher---", this._freshing, this.load);
      if (this._freshing) return;
      this._freshing = true;
      this.triggered = true;
      this.load = true;
      this.page.current = 1;
      this.prizeList = [];
      this.getPrizeList().finally(() => {
        this._freshing = false;
        this.triggered = false;
      });
    },
    // 页面滚动到底部的事件
    reachBottom() {
      if (this._freshing) return;
      this._freshing = true;
      this.page.current += 1;
      this.getPrizeList().finally(() => {
        this._freshing = false;
      });
    },
    // 导航栏切换
    navTabSelect(e) {
      let dataset = e.currentTarget.dataset;
      if (dataset.key != this.type) {
        this.type = dataset.key;
        this.prizeList = [];
        this.page.current = 1;
        // 获取奖品信息
        this.getPrizeList();
      }
    },

    /**
     * 判断奖品是否过期
     * @param {Object} info 奖品信息
     * @returns {Boolean} 是否过期
     */
    checkExpire(info) {
      return !!info.isOverdue;
    },

    /**
     * 设置非盲盒奖品按钮状态和文案
     * @param {Object} info 奖品信息
     * @param {Number} type 奖品类型 1:实物 2:虚拟物品 3:优惠券 4:积分 5:谢谢参与 6:线下券 7:礼品卡
     */
    beyondBlindBox(info, type) {
      const huang = "http://img.songlei.com/myPrize/huang.png";
      const hui = "http://img.songlei.com/myPrize/hui.png";

      if (info.status === "1") {
        //0:未领取 1:已领取 2:未中奖
        switch (type) {
          case "1": // 实物
            info.btnVal = "去查看";
            info.btnUrl = huang;
            break;
          case "2": // 虚拟物品
            info.btnVal = "去使用";
            info.btnUrl = huang;
            break;
          case "3": // 优惠券
            // info.btnVal = this.checkExpire(info) ? "已过期" : "去使用";
            // info.btnUrl = this.checkExpire(info) ? hui : huang;
            info.btnVal = "去使用";
            info.btnUrl = huang;
            break;
          case "4": // 积分
            info.btnVal = "去使用";
            info.btnUrl = huang;
            break;
          case "6": // 线下券
            if (info.writeOffStatus === "1") {
              info.btnVal = "已核销";
              info.btnUrl = hui;
            } else {
              info.btnVal = "去核销";
              info.btnUrl = huang;
            }
            break;
          case "7": // 礼品卡
            info.btnVal = "去查看";
            info.btnUrl = huang;
            break;
          default:
            break;
        }
      } else {
        info.btnVal = "去领取";
        info.btnUrl = huang;
      }
    },

    /**
     * 获取奖品列表数据
     */
    getPrizeList() {
      // 我的页面跳转来不需要传，活动页跳转来传活动ID
      const params = {
        actType: this.type, //活动类型（1:大转盘 2:红包雨）
      };
      if (this.actId) {
        params.actId = this.actId;
      }
      // 关键：return 返回 Promise
      return myPrizeApi(Object.assign({}, params, this.page)).then((res) => {
        let { records } = res.data || {};
        console.log("==myPrizeApi==", res.data);

        records.forEach((item) => {
          this.beyondBlindBox(item, item.prizeType);
        });

        if (this.page.current === 1) {
          // 刷新时替换数据
          this.prizeList = records;
        } else {
          // 加载更多时追加数据
          this.prizeList = [...this.prizeList, ...records];
        }
        if (records.length < this.page.size) {
          this.load = false;
        }
        console.log("==this.prizeList==", this.prizeList);
      });
    },

    /**
     * 处理奖品按钮点击事件
     * @param {Object} info 奖品信息
     * @param {Number} index 奖品索引（盲盒拆开时用）
     */
    handlePrize(info, index) {
      // 按钮为灰色不可操作时直接返回
      if (info.btnUrl === "http://img.songlei.com/myPrize/hui.png") return;
      if (info.prizeType === "2") {
        if (info.relatedActivityId) {
          uni.navigateTo({
            url: "/pages/activity/lottery/index?id=" + info.relatedActivityId,
          });
          return;
        }
        // 盲盒奖品，未拆开时拆盲盒
        if (!info.blindBox) {
          this.openBlindBox(info);
        }
      } else {
        if (info.status === "1") {
          //0:未领取 1:已领取 2:未中奖
          // 已领取状态1:实物 2:虚拟物品 3:优惠券 4:积分 5:谢谢参与 6:线下券 7:礼品卡
          switch (info.prizeType) {
            case "1":
              // 实物去查看订单详情
              uni.navigateTo({
                url: `/pages/order/order-detail/index?id=${info.id}`,
              });
              break;
            case "2":
              // 虚拟物品去使用
              uni.navigateTo({
                url: `/pages/order/order-detail/index?id=${info.id}`,
              });
              break;
            case "3":
              // 优惠券去使用
              uni.navigateTo({
                url: `/pages/goods/goods-list/index?couponId=${info.prizeValue}`,
              });
              break;
            case "4":
              // 积分 去使用 跳积分签到页
              uni.navigateTo({
                url: `/pages/signrecord/signrecord-info/index`,
              });
              break;
            case "6":
              // 线下券去核销
              if (info.writeOffStatus === "0") {
                this.couponNo = info.couponNo;
                // this.writeShow = true; //暂时不做弹框核销
                uni.navigateTo({
                  url: `/pages/coupon/coupon-offline-detail-plus/index?id=${info.couponNo}`,
                });
              }
              break;
            case "7":
              // 礼品卡 去查看 跳我的红包和礼品卡页面
              uni.navigateTo({
                url: `/pages/gift/gift-card/index`,
              });
              break;
            default:
              break;
          }
        } else {
          // 未领取状态
          if (info.prizeType === "1") {
            // 实物去领取页面
            uni.navigateTo({
              url: `/pages/order/order-confirm/index?prizeId=${info.id}`,
            });
          } else {
            // 其他奖品直接领取
            this.receive(info.id);
          }
        }
      }
    },

    /**
     * 拆盲盒接口调用，奖品库存越多概率越大
     * @param {Object} info 奖品信息
     */
    async openBlindBox(info) {
      const params = {
        actId: info.actId,
        prizeId: info.waterPrizeId,
      };
      try {
        const res = await openBlindBoxApi(params);
        if (res.data) {
          this.getPrizeList();
        }
      } catch (error) {
        // 处理异常或提示
        console.error("拆盲盒失败", error);
      }
    },

    /**
     * 领取奖品接口调用
     * @param {String} actUserPrizeId 奖品用户ID
     */
    receive(actUserPrizeId) {
      getPrize(actUserPrizeId).then(() => {
        uni.showToast({
          title: "领取成功",
          duration: 3000,
        });
        this.getPrizeList();
      });
    },

    /**
     * 打开抽奖转盘弹窗
     */
    lotteryOpen() {
      this.showLotteryDialog = true;
    },

    /**
     * 关闭抽奖转盘弹窗
     */
    lotteryClose() {
      this.showLotteryDialog = false;
    },

    /**
     * 打开赠品中奖须知弹窗
     * @param {Object} info 奖品信息
     */
    giftOpen(info) {
      this.prizeInfo = info.blindBox || info.waterPrize || null;
      if (this.prizeInfo) {
        this.prizeInfo.source = info.source;
        this.$refs.giftPopup.open();
      }
    },

    /**
     * 关闭赠品中奖须知弹窗
     */
    giftClose() {
      this.$refs.giftPopup.close();
    },

    /**
     * 关闭线下券核销弹窗
     */
    closeWriteDia() {
      this.writeShow = false;
    },

    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack();
    },

    //重置
    refresh() {
      this.load = true;
      this.prizeList = [];
      this.page.current = 1;
      return this.getPrizeList();
    },
  },
};
</script>

<style scoped lang="scss">
.collection-types {
  top: unset !important;
}

.min-height-nav {
  min-width: 200rpx;
}

.btn {
  height: 50rpx;
  /* width: 352rpx; */
  min-width: 173rpx;
  line-height: 50rpx;
  margin: 18rpx 7rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  background-color: #fff;
  flex: 1;
}

.myPrize-page {
  margin-bottom: 20rpx;
  height: 100vh;

  .navbg {
    position: fixed;
    z-index: 10;
    width: 750rpx;
    top: 0;
  }

  .core-outbox {
    .core-box {
      width: 692rpx;
      min-height: 280rpx;
      background: #ffffff;
      border-radius: 24rpx;
      margin: auto;
      padding: 20rpx;
      display: flex;

      .zou-box {
        width: 209rpx;
        height: 209rpx;
        background: #fdf2eb;
        border-radius: 22rpx;

        .tupian {
          width: 209rpx;
          height: 209rpx;
          border-radius: 22rpx;
        }
      }

      .you-box {
        margin-left: 25rpx;
        font-family: PingFang SC;
        color: #000;

        .hang {
          font-size: 26rpx;
          font-weight: 500;

          &:nth-child(1) {
            width: 410rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 28rpx;
            font-weight: 800;
          }
        }

        .qita {
          display: flex;
        }

        .anniu-box {
          display: flex;

          .anniu {
            position: relative;
            // margin-top: 10rpx;
            width: 221rpx;
            height: 73rpx;

            .wen {
              width: 221rpx;
              text-align: center;
              position: absolute;
              z-index: 1;
              top: 30rpx;
              font-size: 26rpx;
              font-family: PingFang SC;
              font-weight: 800;
              color: #953c22;
            }

            .btn {
              position: absolute;
              top: 0;
              left: 0;
              width: 221rpx;
              height: 73rpx;
            }
          }
        }
      }
    }

    .kong-box {
      position: relative;
      z-index: 10;
      width: 717rpx;
      height: 764rpx;
      margin: auto;
      padding-top: 120rpx;
      text-align: center;

      .da {
        font-size: 45rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: #994024;
      }

      .xiao {
        margin-top: 36rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #994124;
      }

      .tu {
        margin-top: 40rpx;
        width: 391rpx;
        height: 270rpx;
      }

      .btn-box {
        position: relative;
        margin: auto;
        width: 239rpx;
        height: 77rpx;

        .wen {
          width: 239rpx;
          height: 77rpx;
          line-height: 50rpx;
          background-color: transparent;
          padding-top: 25rpx;
          font-size: 30rpx;
          font-family: PingFang SC;
          font-weight: 800;
          color: #9f4227;
        }

        .btn {
          position: absolute;
          z-index: -1;
          top: 0;
          left: 0;
          width: 239rpx;
          height: 77rpx;
        }
      }

      .bg {
        z-index: -10;
        position: absolute;
        top: 40rpx;
        right: 0;
        width: 717rpx;
        height: 764rpx;
      }
    }
  }

  .zengpin-box {
    position: relative;
    width: 657rpx;
    height: 929rpx;
    padding-top: 280rpx;

    .zp-box {
      margin: auto;
      width: 556rpx;

      .zhu-box {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        .zou {
          width: 247rpx;
          height: 247rpx;
          background: #ffffff;
          border-radius: 16rpx;

          .tuxiang {
            width: 247rpx;
            height: 247rpx;
            border-radius: 16rpx;
          }
        }

        .you {
          margin-left: 16rpx;

          .shang {
            width: 273rpx;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 800;
            color: #000000;
          }

          .xia {
            margin-top: 90rpx;
            font-size: 28rpx;
            font-family: zihun100hao-fangfangxianfengti;
            font-weight: 400;
            color: #a1786e;
          }
        }
      }

      .hang-box {
        font-size: 22rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #000000;
        line-height: 35rpx;
      }

      .hang2-box {
        margin-top: 40rpx;
        margin-bottom: 36rpx;
        text-align: center;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #000000;
      }

      .btn-outbox {
        margin-top: 10rpx;
        display: flex;
        align-items: center;

        .btn-box {
          position: relative;
          margin: auto;
          width: 221rpx;
          height: 73rpx;
          text-align: center;

          .wen {
            padding-top: 8rpx;
            font-size: 30rpx;
            font-family: PingFang SC;
            font-weight: 800;
          }

          .btn {
            position: absolute;
            z-index: -1;
            top: 0;
            left: 0;
            width: 221rpx;
            height: 73rpx;
          }
        }
      }
    }

    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 657rpx;
      height: 929rpx;
    }
  }
}
</style>
