<template>
  <uni-popup
    ref="giftPopup"
    @maskClick="giftClose"
    maskBackgroundColor="rgba(0, 0, 0, 0.8)"
  >
    <view
      v-if="prizeInfo"
      class="zengpin-box"
      :style="{ 'padding-top': prizeInfo.prizeStartTime ? '280rpx' : '320rpx' }"
    >
      <view class="zp-box">
        <view class="zhu-box">
          <view class="zou">
            <image class="tuxiang" :src="prizeInfo.url" mode="aspectFit" />
          </view>
          <view class="you">
            <view class="shang">{{ prizeInfo.name }}</view>
            <block v-if="prizeInfo.prizeStartTime">
              <view class="xia">请您及时兑奖哦~</view>
            </block>
            <block v-else>
              <view class="xia">{{ prizeInfo.source }}</view>
            </block>
          </view>
        </view>

        <block v-if="prizeInfo.prizeStartTime" :class="prizeInfo.detail ? 'two-line-text' : ''">
          <view class="hang-box">
            <text>兑奖期限：</text>
            <text>{{ prizeInfo.prizeStartTime }} - {{ prizeInfo.prizeEndTime }}</text>
          </view>
          <view class="hang-box">兑奖时段：{{ prizeInfo.prizeTime }}</view>
          <view class="hang-box">兑奖地点：{{ prizeInfo.prizePlace }}</view>
          <view class="hang-box">兑奖联系人：{{ prizeInfo.prizePeople }}</view>
          <view class="hang-box">联系电话：{{ prizeInfo.prizePhone }}</view>
          <view class="hang-box">兑奖须知：{{ prizeInfo.detail }}</view>
        </block>

        <block v-else>
          <view class="hang2-box">该商品需要邮寄，请完善收货地址信息</view>
        </block>

        <view class="btn-outbox">
          <view class="btn-box" @click="giftClose">
            <view class="wen">关闭</view>
            <image class="btn" :src="iconPic.huang" />
          </view>
        </view>
      </view>
      <image class="bg" :src="prizePopupBg" />
    </view>
  </uni-popup>
</template>

<script>
export default {
  props: {
    prizeInfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      iconPic: {
        huang: "http://img.songlei.com/myPrize/huang.png", // 黄色按钮图标
      },
      prizePopupBg: "http://img.songlei.com/live/home/<USER>", // 弹窗背景图，替换成实际路径
    };
  },
  methods: {
    giftClose() {
      this.$refs.giftPopup.close();
      this.$emit("close");
    },
    open() {
      this.$refs.giftPopup.open();
    },
  },
};
</script>

<style scoped lang="scss">
.zengpin-box {
  position: relative;
  width: 657rpx;
  height: 929rpx;
  padding-top: 280rpx;

  .zp-box {
    margin: auto;
    width: 556rpx;

    .zhu-box {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .zou {
        width: 247rpx;
        height: 247rpx;
        background: #ffffff;
        border-radius: 16rpx;

        .tuxiang {
          width: 247rpx;
          height: 247rpx;
          border-radius: 16rpx;
        }
      }

      .you {
        margin-left: 16rpx;

        .shang {
          width: 273rpx;
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 800;
          color: #000000;
        }

        .xia {
          margin-top: 90rpx;
          font-size: 28rpx;
          font-family: zihun100hao-fangfangxianfengti;
          font-weight: 400;
          color: #a1786e;
        }
      }
    }

    .hang-box {
      font-size: 22rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #000000;
      line-height: 35rpx;
    }

    .hang2-box {
      margin-top: 40rpx;
      margin-bottom: 36rpx;
      text-align: center;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #000000;
    }

    .btn-outbox {
      margin-top: 10rpx;
      display: flex;
      align-items: center;

      .btn-box {
        position: relative;
        margin: auto;
        width: 221rpx;
        height: 73rpx;
        text-align: center;

        .wen {
          padding-top: 8rpx;
          font-size: 30rpx;
          font-family: PingFang SC;
          font-weight: 800;
        }

        .btn {
          position: absolute;
          z-index: -1;
          top: 0;
          left: 0;
          width: 221rpx;
          height: 73rpx;
        }
      }
    }
  }

  .bg {
    z-index: -10;
    position: absolute;
    top: 0;
    right: 0;
    width: 657rpx;
    height: 929rpx;
  }
}
</style>
