<template>
    <!-- 待合并松鼠乐园我的奖品 -->
  <view class="WriteOff-page">
    <uni-popup
      ref="popup"
      @maskClick="close"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box">
        <view class="biaoti">核销二维码：</view>
        <view class="shang-box">
          <!-- <canvas class="tiaoxingma" canvas-id="Brcode" /> -->
          <image class="erweima" :src="qrImg" />
          <view class="shuaixin" v-if="certificateInfo.qrcode" @click="qrcode">
            刷新二维码
          </view>
          <view class="wen">领奖时请给工作人员出示核销二维码</view>
        </view>
        <view class="ming">核销规则：</view>
        <view class="xia-box" :class="certificateInfo.detail?'two-line-text':''">
          <view class="hang">
            <view class="zou">兑奖期限：</view>
            <view class="you">
              {{ certificateInfo.prizeStartTime }}-
              {{ certificateInfo.prizeEndTime }}
            </view>
          </view>
          <view class="hang">
            <view class="zou">兑奖时段：</view>
            <view class="you">{{ certificateInfo.prizeTime }}</view>
          </view>
          <view class="hang">
            <view class="zou">兑奖地点：</view>
            <view class="you">{{ certificateInfo.prizePlace }}</view>
          </view>
          <view class="hang">
            <view class="zou">兑奖联系人：</view>
            <view class="you">{{ certificateInfo.prizePeople }}</view>
          </view>
          <view class="hang">
            <view class="zou">联系电话：</view>
            <view class="you">{{ certificateInfo.prizePhone }}</view>
          </view>
          <view class="hang">
            <view class="zou">兑奖须知：</view>
            <view class="you">{{ certificateInfo.detail }}</view>
          </view>
        </view>
        <view class="anniu" @click="close">
          <view class="wen">关闭</view>
          <image class="btn" :src="iconPic.huang" />
        </view>
        <image class="bg" :src="waterTemplate.templateComponent.QRcodePopUrl||iconPic.qrCodeUrl" />
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 * pages\squirrelParadise\home\components\StageLevelPrize.vue同一个组件同步改
 **/
import { qrcodeApi } from "@/pages/myPrize/api/myPrize";
// const brCode = require("utils/barcode.js");
const QR = require("utils/wxqrcode.js");

export default {
  props: {
    couponNo: {
      type: String,
      require: true,
      default: "",
    },
  },
  data() {
    return {
      qrImg:
        "https://img.songlei.com/-1/material/a3332b63-c7a4-4c3a-99c1-dd46cb6ac133.png",
      certificateInfo: {
        qrcode: "",
      },
      iconPic: {
        huang: "http://img.songlei.com/myPrize/huang.png",
        qrCodeUrl: "http://img.songlei.com/live/home/<USER>",
      },
      waterTemplate:{},//游戏模版
    };
  },
  mounted() {
    this.$refs.popup.open("center");
    this.qrcode();
    this.waterTemplate = uni.getStorageSync('waterTemplate');
  },
  methods: {
    qrcode() {
      const params = {
        couponNo: this.couponNo,
      };
      qrcodeApi(params).then((res) => {
        this.certificateInfo = res.data;
        // 当作组件要加实例化this wx.createCanvasContext({},this);
        // brCode.code128(
        //   wx.createCanvasContext("Brcode", this),
        //   res.data.qrcode,
        //   177,
        //   35
        // );
        this.qrImg = QR.createQrCodeImg(res.data.qrcode, {
          size: parseInt(230), //二维码大小
        });
      });
    },
    close() {
      this.$refs.popup.close();
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.WriteOff-page {
  .core-box {
    position: relative;
    width: 648rpx;
    height: 1092rpx;
    overflow: auto;
    padding-top: 115rpx;
    .biaoti {
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 800;
      color: #a04527;
      margin-left: 70rpx;
    }
    .shang-box {
      text-align: center;
      margin-bottom: 25rpx;
      .tiaoxingma {
        margin: auto;
        margin-top: 15rpx;
        width: 356rpx;
        height: 72rpx;
      }
      .erweima {
        margin: auto;
        margin-top: 20rpx;
        display: block;
        width: 230rpx;
        height: 230rpx;
        box-shadow: 0rpx 3rpx 7rpx 0rpx #af9b82;
      }
      .shuaixin {
        color: #999999;
        font-size: 22rpx;
        margin-top: 24rpx;
        text-decoration: underline;
        text-align: center;
      }
      .wen {
        margin-top: 15rpx;
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #a04527;
      }
    }
    .ming {
      margin-left: 50rpx;
      margin-bottom: 10rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 800;
      color: #a04527;
    }
    .xia-box {
      margin-left: 50rpx;
      .hang {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #a04527;
        margin-bottom: 6rpx;
        display: flex;
        align-items: start;
        .zou {
        }
        .you {
          width: 430rpx;
        }
      }
    }
    .anniu {
      position: relative;
      margin: auto;
      margin-top: 10rpx;
      width: 221rpx;
      height: 73rpx;
      .wen {
        width: 221rpx;
        text-align: center;
        position: absolute;
        z-index: 1;
        top: 10rpx;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #953c22;
      }
      .btn {
        position: absolute;
        top: 0;
        left: 0;
        width: 221rpx;
        height: 73rpx;
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 648rpx;
      height: 1092rpx;
    }
  }
}
</style>
