/**
 * <AUTHOR>
 * @date 日期：2025年5月23日
 **/
import { requestApi as request } from "@/utils/api.js";

// 我的奖品
export function myPrizeApi(data) {
  return request({
    url: "/mallmarket/actuserprize/myPrize",
    method: "get",
    data,
  });
}

// 领取奖品
export function getPrize(data) {
  return request({
    url: "/mallmarket/actuserprize/getPrize/"+data,
    method: "put",
  });
}

// 线下券核销
export function qrcodeApi(data) {
  return request({
    url: "/mallmarket/water/qrcode",
    method: "get",
    showLoading: false,
    data,
  });
}

// 历史接口 我的乐园奖品
export function myWaterPrizeApi(data) {
  return request({
    url: "/mallmarket/water/myPrize",
    method: "get",
    data,
  });
}