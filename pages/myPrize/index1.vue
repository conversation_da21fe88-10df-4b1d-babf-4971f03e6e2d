<template>
  <!-- 原松鼠乐园我的奖品 -->
  <view
    class="myPrize-page"
    :style="{
      background: `${waterTemplate.templateComponent.TemplateType=='1'?'#F4FAF1':(waterTemplate.templateComponent.TemplateType=='2'?'#DDFDFF':'#DFD0F7')}`,
    }"
  >
    <image
      class="navbg"
      :src="waterTemplate.templateComponent.prizeTopUrl"
      :style="{ height: CustomBar + 'px' }"
    />
    <cu-custom
      bgColor="rgba(255, 255, 255, 0)"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content" style="color: #000"> 我的奖品</block>
    </cu-custom>

    <image
      :src="waterTemplate.templateComponent?waterTemplate.templateComponent.myPrizeBgUrl :''"
      mode="widthFix"
      style="width: 100%; height: 309rpx"
    >
    </image>

    <view :style="{
          position: 'absolute',
          top: `${CustomBar}px`,
          left: '0rpx',
          right: '0rpx',
        }">
          <view class="margin-lr" v-show="marketPath" @click="lotteryOpen">
            <image
              :src="waterTemplate.templateComponent?waterTemplate.templateComponent.turntableUrl:''"
              mode="widthFix"
              style="width: 100%; height: 309rpx; border-radius: 20rpx"
            >
            </image>
          </view>
      
          <!-- 奖品列表 -->
          <view
            class="core-outbox"
            :class="marketPath ? '' : 'padding-top-sm'"
            :style="{
              height: marketPath
                ? `calc(100vh - ${CustomBar}px - 404rpx)`
                : `calc(100vh - ${CustomBar}px)`,
              paddingBottom: '10rpx',
              backgroundImage: `url(${waterTemplate.templateComponent.prizeBottomUrl})`,
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'left 0% bottom 0%',
              backgroundSize: '750rpx 137rpx',
              overflow: 'auto',
            }"
            v-if="prizeList.length"
          >
            <block v-for="(u, i) in prizeList" :key="i">
              <view
                class="core-box"
                :style="{ 'margin-top': marketPath && i === 0 ? '0' : '20rpx' }"
                v-if="u.blindBox"
              >
                <view class="zou-box">
                  <image class="tupian" :src="u.blindBox.url" mode="aspectFit" />
                </view>
                <view class="you-box">
                  <view class="hang">{{ u.blindBox.name }}</view>
                  <view class="hang">奖励时间：{{ u.createTime }}</view>
                  <view class="hang">获奖来源：{{ u.source }}</view>
                  <view class="hang">活动名称：{{ u.waterInfoName }}</view>
                  <view class="hang qita" v-if="3 == u.blindBox.prizeType">
                    <view style="min-width: 130rpx">兑奖期限：</view>
                    <view>
                      {{ u.blindBox.prizeStartTime }}-
                      {{ u.blindBox.prizeEndTime }}
                    </view>
                  </view>
                  <view class="anniu-box">
                    <view class="anniu" @click="handlePrize(u, i)">
                      <view class="wen">{{ u.btnVal }}</view>
                      <image class="btn" :src="u.btnUrl" />
                    </view>
                    <view
                      class="anniu"
                      v-if="3 == u.blindBox.prizeType"
                      @click="giftOpen(u)"
                    >
                      <view class="wen">中奖须知</view>
                      <image class="btn" :src="iconPic.huang" />
                    </view>
                  </view>
                </view>
              </view>
              <view
                class="core-box"
                :style="{ 'margin-top': marketPath && i === 0 ? '0' : '20rpx' }"
                v-else
              >
                <view class="zou-box">
                  <image class="tupian" :src="u.waterPrize.url" mode="aspectFit" />
                </view>
                <view class="you-box">
                  <view class="hang">{{ u.waterPrize.name }}</view>
                  <view class="hang">奖励时间：{{ u.createTime }}</view>
                  <view class="hang">获奖来源：{{ u.source }}</view>
                  <view class="hang">活动名称：{{ u.waterInfoName }}</view>
                  <view class="hang qita" v-if="3 == u.drawGiftType">
                    <view style="min-width: 130rpx">兑奖期限：</view>
                    <view>
                      {{ u.waterPrize.prizeStartTime }}-
                      {{ u.waterPrize.prizeEndTime }}
                    </view>
                  </view>
                  <view class="anniu-box">
                    <view class="anniu" @click="handlePrize(u)">
                      <view class="wen">{{ u.btnVal }}</view>
                      <image class="btn" :src="u.btnUrl" />
                    </view>
                    <view
                      class="anniu"
                      v-if="3 == u.drawGiftType"
                      @click="giftOpen(u)"
                    >
                      <view class="wen">中奖须知</view>
                      <image class="btn" :src="iconPic.huang" />
                    </view>
                  </view>
                </view>
              </view>
            </block>
          </view>
      
          <!-- 暂无数据 -->
          <view
            class="core-outbox"
            :style="{
              height: marketPath
                ? `calc(100vh - ${CustomBar}px - 404rpx)`
                : `calc(100vh - ${CustomBar}px)`,
              paddingBottom: '10rpx',
              backgroundImage: `url(${waterTemplate.templateComponent.prizeBottomUrl})`,
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'left 0% bottom 0%',
              backgroundSize: '750rpx 137rpx',
              overflow: 'auto',
            }"
            v-else
          >
            <view class="kong-box">
              <view class="da">暂无数据</view>
              <view class="xiao">完成{{waterTemplate.tip2}}任务即可得奖品哦~！</view>
              <image class="tu" :src="waterTemplate.templateComponent.runOutUrl" />
              <view class="btn-box" @click="goBack">
                <view class="wen">返回</view>
                <image class="btn" :src="iconPic.huang" />
              </view>
              <image class="bg" :src="waterTemplate.templateComponent.popNoneUrl" />
            </view>
          </view>
          <!-- 抽奖转盘 -->
          <pop-lottery
            v-if="showLotteryDialog"
            :lotteryId="marketPath"
            @close="lotteryClose"
          />
          <!-- 3:赠品中奖须知 -->
          <uni-popup
            ref="giftPopup"
            @maskClick="giftClose"
            maskBackgroundColor="rgba(0, 0, 0, 0.8)"
          >
            <view
              v-if="prizeInfo"
              class="zengpin-box"
              :style="{
                'padding-top': prizeInfo.prizeStartTime ? '280rpx' : '320rpx',
              }"
            >
              <view class="zp-box">
                <view class="zhu-box">
                  <view class="zou">
                    <image class="tuxiang" :src="prizeInfo.url" mode="aspectFit" />
                  </view>
                  <view class="you">
                    <view class="shang">{{ prizeInfo.name }}</view>
                    <block v-if="prizeInfo.prizeStartTime">
                      <view class="xia">请您及时兑奖哦~</view>
                    </block>
                    <block v-else>
                      <view class="xia">{{ prizeInfo.source }}</view>
                    </block>
                  </view>
                </view>
                <block v-if="prizeInfo.prizeStartTime" :class="prizeInfo.detail?'two-line-text':''">
                  <view class="hang-box">
                    <text>兑奖期限：</text>
                    <text>
                      {{ prizeInfo.prizeStartTime }}-{{ prizeInfo.prizeEndTime }}
                    </text>
                  </view>
                  <view class="hang-box"> 兑奖时段：{{ prizeInfo.prizeTime }} </view>
                  <view class="hang-box"> 兑奖地点：{{ prizeInfo.prizePlace }} </view>
                  <view class="hang-box">
                    兑奖联系人：{{ prizeInfo.prizePeople }}
                  </view>
                  <view class="hang-box"> 联系电话：{{ prizeInfo.prizePhone }} </view>
                  <view class="hang-box">
                    兑奖须知：{{ prizeInfo.detail }}
                  </view>
                </block>
                <block v-else>
                  <view class="hang2-box"> 该商品需要邮寄，请完善收货地址信息 </view>
                </block>
                <view class="btn-outbox">
                  <view class="btn-box" @click="giftClose">
                    <view class="wen">关闭</view>
                    <image class="btn" :src="iconPic.huang" />
                  </view>
                </view>
              </view>
              <image class="bg" :src="waterTemplate.templateComponent.prizePopupMiddleUrl" />
            </view>
          </uni-popup>
          <!-- 4:线下券核销 -->
          <block v-if="writeShow">
            <write-off :coupon-no="couponNo" @close="closeWriteDia" />
          </block>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import PopLottery from "@/components/lottery/pop-lottery.vue";
import WriteOff from "./components/WriteOff";
const app = getApp();
import { myWaterPrizeApi } from "@/pages/myPrize/api/myPrize";
import { receiveApi, openBlindBoxApi } from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  components: {
    PopLottery,
    WriteOff,
  },
  data() {
    return {
      CustomBar: this.CustomBar,

      waterInfoId: "",
      prizeList: [],

      showLotteryDialog: false,
      marketPath: "",

      prizeInfo: null,

      couponNo: "",
      writeShow: false,

      iconPic: {
        huang: "http://img.songlei.com/myPrize/huang.png",
      },
      waterTemplate:{},//游戏模版
    };
  },
  onLoad(options) {
    if (options.waterInfoId) {
      this.waterInfoId = options.waterInfoId;
    }
    
  },
  onShow() {
    // 登录控制
    app.initPage().then((res) => {
      this.getPrizeList();
    });

    this.waterTemplate = uni.getStorageSync('waterTemplate');
    console.log('this.waterTemplate',this.waterTemplate,this.waterTemplate.templateComponent.turntableUrl)
  },
  methods: {
    // 是否过期（0否 1是）
    checkExpire(info) {
      return info.isOverdue ? true : false;
    },
    // 盲盒之外的状态显示
    beyondBlindBox(info, type) {
      // 奖品类型 1:优惠券 2:盲盒 3:赠品 4:线下券
      const huang = "http://img.songlei.com/myPrize/huang.png";
      const hui = "http://img.songlei.com/myPrize/hui.png";

      if (1 == info.status) {
        if (1 == type) {
          info.btnVal = this.checkExpire(info) ? "已过期" : "去使用";
          info.btnUrl = this.checkExpire(info) ? hui : huang;
        }
        if (3 == type) {
          info.btnVal = this.checkExpire(info) ? "已过期" : "去查看";
          info.btnUrl = this.checkExpire(info) ? hui : huang;
        }
        if (4 == type) {
          if (1 == info.writeOffStatus) {
            info.btnVal = "已核销";
            info.btnUrl = hui;
          } else {
            info.btnVal = this.checkExpire(info) ? "已过期" : "去核销";
            info.btnUrl = this.checkExpire(info) ? hui : huang;
          }
        }
      } else {
        info.btnVal = this.checkExpire(info) ? "已过期" : "去领取";
        info.btnUrl = this.checkExpire(info) ? hui : huang;
      }
    },
    getPrizeList() {
      const params = {};
      if (this.waterInfoId) {
        params.waterInfoId = this.waterInfoId;
      }
      this.prizeList = [];
      myPrizeApi(params).then((res) => {
        let { marketPath, waterPrizeUsers } = { ...res.data };
      
        this.marketPath = marketPath;
        waterPrizeUsers.forEach((u) => {
          if (u.giftConditions) {
            u.source = `堆雪人达到${u.giftConditions}个得奖励`;
          } else {
            u.source = "完成堆雪人获得奖励";
          }
          // 奖品类型 1:优惠券 2:盲盒 3:赠品 4:线下券
          if (2 == u.drawGiftType) {
            if (u.blindBox) {
              this.beyondBlindBox(u, u.blindBox.prizeType);
            } else {
              u.btnVal = "拆盲盒";
              u.btnUrl = "http://img.songlei.com/myPrize/huang.png";
            }
          } else {
            this.beyondBlindBox(u, u.drawGiftType);
          }
        });
        this.prizeList = waterPrizeUsers;
      });
    },
    handlePrize(info) {
      if ("http://img.songlei.com/myPrize/hui.png" == info.btnUrl) {
        return;
      }
      if (2 == info.drawGiftType) {
        if (!info.blindBox) {
          this.openBlindBox(info);
        }
      } else {
        if (1 == info.status) {
          if (1 == info.drawGiftType) {
            // 优惠券去使用
            uni.navigateTo({
              url: `/pages/goods/goods-list/index?couponId=${info.waterPrize.prizeValue}`,
            });
          }
          if (3 == info.drawGiftType) {
            // 赠品去查看
            uni.navigateTo({
              url: `/pages/order/order-detail/index?id=${info.orderId}`,
            });
          }
          if (4 == info.drawGiftType) {
            if (0 == info.writeOffStatus) {
              // 线下券去核销
              this.couponNo = info.couponNo;
              this.writeShow = true;
            }
          }
        } else {
          if (3 == info.drawGiftType) {
            // 赠品去领取
            uni.navigateTo({
              url: `/pages/order/order-confirm/index?prizeId=${info.id}`,
            });
          } else {
            // 去领取
            this.receive(info.id);
          }
        }
      }
    },
    // 拆盲盒 奖品库存越多概率越大
    async openBlindBox(info) {
      let params = {
        waterInfoId: info.waterInfoId,
        prizeId: info.waterPrizeId,
      };
      try {
        const res = await openBlindBoxApi(params);
        let { data } = { ...res };
        if (data) {
          this.getPrizeList();
        }
      } catch (err) {
        // console.log(err);
      }
    },
    // 去领取
    receive(prizeUserId) {
      const params = {
        prizeUserId,
      };
      receiveApi(params).then(() => {
        uni.showToast({
          title: "领取成功",
          duration: 3000,
        });
        this.getPrizeList();
      });
    },
    // 抽奖转盘打开
    lotteryOpen() {
      this.showLotteryDialog = true;
    },
    // 抽奖转盘关闭
    lotteryClose() {
      this.showLotteryDialog = false;
    },
    // 3:赠品打开
    giftOpen(info) {
      if (info.blindBox) {
        this.prizeInfo = info.blindBox;
      } else {
        this.prizeInfo = info.waterPrize;
      }
      this.prizeInfo.source = info.source;
      this.$refs.giftPopup.open();
    },
    // 3:赠品关闭
    giftClose() {
      this.$refs.giftPopup.close();
    },
    closeWriteDia() {
      this.writeShow = false;
    },
    goBack() {
      uni.navigateBack();
    },
  },
};
</script>
<style scoped lang="scss">
.myPrize-page {
  margin-bottom: 20rpx;
  height: 100vh;
  .navbg {
    position: fixed;
    z-index: 10;
    width: 750rpx;
    top: 0;
  }
  .core-outbox {
    .core-box {
      width: 692rpx;
      min-height: 280rpx;
      background: #ffffff;
      border-radius: 24rpx;
      margin: auto;
      padding: 20rpx;
      display: flex;
      .zou-box {
        width: 209rpx;
        height: 209rpx;
        background: #fdf2eb;
        border-radius: 22rpx;
        .tupian {
          width: 209rpx;
          height: 209rpx;
          border-radius: 22rpx;
        }
      }
      .you-box {
        margin-left: 25rpx;
        font-family: PingFang SC;
        color: #000;
        .hang {
          font-size: 26rpx;
          font-weight: 500;
          &:nth-child(1) {
            width: 410rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 28rpx;
            font-weight: 800;
          }
        }
        .qita {
          display: flex;
        }
        .anniu-box {
          display: flex;
          .anniu {
            position: relative;
            margin-top: 10rpx;
            width: 221rpx;
            height: 73rpx;
            .wen {
              width: 221rpx;
              text-align: center;
              position: absolute;
              z-index: 1;
              top: 10rpx;
              font-size: 26rpx;
              font-family: PingFang SC;
              font-weight: 800;
              color: #953c22;
            }
            .btn {
              position: absolute;
              top: 0;
              left: 0;
              width: 221rpx;
              height: 73rpx;
            }
          }
        }
      }
    }
    .kong-box {
      position: relative;
      z-index: 10;
      width: 717rpx;
      height: 764rpx;
      margin: auto;
      padding-top: 120rpx;
      text-align: center;
      .da {
        font-size: 45rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: #994024;
      }
      .xiao {
        margin-top: 36rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #994124;
      }
      .tu {
        margin-top: 40rpx;
        width: 391rpx;
        height: 270rpx;
      }
      .btn-box {
        position: relative;
        margin: auto;
        width: 239rpx;
        height: 77rpx;
        .wen {
          width: 239rpx;
          height: 77rpx;
          line-height: 50rpx;
          background-color: transparent;
          padding-top: 10rpx;
          font-size: 30rpx;
          font-family: PingFang SC;
          font-weight: 800;
          color: #9f4227;
        }
        .btn {
          position: absolute;
          z-index: -1;
          top: 0;
          left: 0;
          width: 239rpx;
          height: 77rpx;
        }
      }
      .bg {
        z-index: -10;
        position: absolute;
        top: 40rpx;
        right: 0;
        width: 717rpx;
        height: 764rpx;
      }
    }
  }
  .zengpin-box {
    position: relative;
    width: 657rpx;
    height: 929rpx;
    padding-top: 280rpx;
    .zp-box {
      margin: auto;
      width: 556rpx;
      .zhu-box {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        .zou {
          width: 247rpx;
          height: 247rpx;
          background: #ffffff;
          border-radius: 16rpx;
          .tuxiang {
            width: 247rpx;
            height: 247rpx;
            border-radius: 16rpx;
          }
        }
        .you {
          margin-left: 16rpx;
          .shang {
            width: 273rpx;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 800;
            color: #000000;
          }
          .xia {
            margin-top: 90rpx;
            font-size: 28rpx;
            font-family: zihun100hao-fangfangxianfengti;
            font-weight: 400;
            color: #a1786e;
          }
        }
      }
      .hang-box {
        font-size: 22rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #000000;
        line-height: 35rpx;
      }
      .hang2-box {
        margin-top: 40rpx;
        margin-bottom: 36rpx;
        text-align: center;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #000000;
      }
      .btn-outbox {
        margin-top: 10rpx;
        display: flex;
        align-items: center;
        .btn-box {
          position: relative;
          margin: auto;
          width: 221rpx;
          height: 73rpx;
          text-align: center;
          .wen {
            padding-top: 8rpx;
            font-size: 30rpx;
            font-family: PingFang SC;
            font-weight: 800;
          }
          .btn {
            position: absolute;
            z-index: -1;
            top: 0;
            left: 0;
            width: 221rpx;
            height: 73rpx;
          }
        }
      }
    }
    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 657rpx;
      height: 929rpx;
    }
  }
}
</style>
<style lang="scss">
.WriteOff-page {
  .uni-popup {
    .uni-popup__wrapper {
      margin-top: 98rpx;
    }
  }
}
</style>
