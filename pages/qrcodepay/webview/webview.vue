<template>
	<view>
		<!-- #ifndef MP -->
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="content">{{ title }}</block>
		</cu-custom>
		<!-- #endif -->
		<view class="margin-top-xl">
			<web-view :src="url" :webview-styles="webviewStyles" @message="onMessage"></web-view>
		</view>
	</view>
</template>

<script>
  const app = getApp();
  export default {
    data() {
      return {
        theme: app.globalData.theme, //全局颜色变量
        title: "浏览",
        url: '',
        webviewStyles: {
        	progress: {
        		color: "#FF3333",
        	},
        },
      }
    },
    onLoad(options) {
      this.url = decodeURIComponent(options.url);
    },
    methods: {
      onMessage(e) {
        console.log(111, e);
      }
    }
  }
</script>

<style>
</style>