import { requestApi as request } from '@/utils/api.js'

// 一码付保存身份信息
export const identity = (data) => {
  return request({
    url: '/mallapi/unionpay/identity',
    method: 'post',
    data
  })
}

// 获取当前是否有签约
export const querySign = (data) => {
  return request({
    url: '/mallapi/unionpay/querySign',
    method: 'get',
    showLoading: false
  })
}

// 获取卡管理的url
export const cardManage = (data) => {
  return request({
    url: '/mallapi/unionpay/cardManage',
    method: 'post',
    data
  })
}

