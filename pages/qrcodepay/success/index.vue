<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">一码付</block>
		</cu-custom>

		<view class="result-box">
			<view class="padding flex flex-direction" style="padding-bottom:10rpx;padding-top:40rpx">
				<view class="text-sxl text-center text-success flex align-center">
					<image style="width: 40rpx; height: 40rpx;" src="https://img.songlei.com/live/ok.png"></image>
					<text style="color: #FF4E00; ">支付成功</text>
				</view>

				<view v-if="orderInfo" class="flex margin-top-sm lines-black text-center">
					<view class="flex-twice text-price" style="flex: 5;font-size: 80rpx">{{PayAmount}}
					</view>
				</view>

				<view v-if="orderInfo.discountAmount&&orderInfo.discountAmount>0"
					class="margin-sm flex color-white text-center" style="margin-top: 0rpx;">
					<view class="flex-twice text-xl text-price" style="flex: 5;text-decoration: line-through">
						{{txnAmt}}
					</view>
				</view>

				<navigator open-type="navigateBack" class="result-btn" delta="1">完成
				</navigator>
			</view>

			<view class="user-line"></view>

			<view v-if="orderInfo.discountAmount&&orderInfo.discountAmount>0"
				class="margin-left-sm margin-top-xl flex align-center text-red">
				<text class="margin-left flex-sub text-df ">优惠金额:</text>
				<view class="flex-twice text-df"
					style="text-align: right; padding-right: 40rpx;display:flex;flex-direction: row;justify-content: flex-end;align-items: center;">
					-
					<format-price color="red" styleProps="font-weight:bold" signFontSize="20rpx" smallFontSize="20rpx"
						priceFontSize="28rpx" :price="discountAmount"></format-price>
				</view>
			</view>

			<view class="margin-left-sm margin-top-lg flex align-center">
				<text class="margin-left flex-sub text-df text-gray">订单号:</text>
				<view class="flex-twice text-df text-black"
					style="text-align: right; padding-right: 40rpx;display:flex;flex-direction: row;justify-content: flex-end;align-items: center;">
					{{orderInfo.orderId}}
				</view>
			</view>

			<view class="margin-left-sm margin-top-lg flex align-center">
				<text class="margin-left flex-sub text-df text-gray">订单时间:</text>
				<view class="flex-twice text-df text-black"
					style="text-align: right; padding-right: 40rpx;display:flex;flex-direction: row;justify-content: flex-end;align-items: center;">
					{{orderInfo.createTime}}
				</view>
			</view>

			<view class="margin-left-sm margin-top-lg flex align-center">
				<text class="margin-left flex-sub text-df text-gray">支付方式:</text>
				<view class="flex-twice text-df text-black"
					style="text-align: right; padding-right: 40rpx;display:flex;flex-direction: row;justify-content: flex-end;align-items: center;">
					会员一码付
				</view>
			</view>

			<navigator v-if="payment" hover-class="none" class="result-img" :url="payment.linkUrl">
				<image class="img" mode="widthFix" :src="payment.imgUrl"></image>
			</navigator>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api'
	import util from 'utils/util'
	import formatPrice from "@/components/format-price/index.vue"

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderInfo: {
					// "bankCardNo": "439225******6884",
					// "cashNo": "110910",
					// "chlOrderId": "1686557414079332352",
					// "cid": "**********",
					// "createTime": "2023-08-02 09:59:56",
					// "hospitalId": "YHFH009300002",
					// "id": "1686557414081630209",
					// "orderId": "**************",
					// "payStatus": "1",
					// "queryId": "51230802359984563342",
					// "shopCode": "201",
					// "smallNo": "541",
					// "termId": "********",
					// "timeTamp": "**********",
					// "traceNo": "456334",
					// "traceTime": "**********",
					// "tradeNo": "YHF202308020959570000111",
					// "transactionId": "20111090889**************",
					// "txnAmt": "15.52",
					// "updateTime": "2023-08-02 09:59:56",
					// "userId": "1554373680561254401",
					// "discountAmount":'1.53'
				},
				paymentPrice: "", //实付金额
				payment: {}, //广告
				//有数统计使用
				page_title: '一码付结果页',
			};
		},

		components: {
			formatPrice
		},

		props: {},

		computed: {
			PayAmount() {
				if (this.orderInfo) {
					if (this.orderInfo.discountAmount && this.orderInfo.discountAmount > 0) {
						// let prc =  this.orderInfo.txnAmt - this.orderInfo.discountAmount
						let prc = (util.floatObj.subtract(this.orderInfo.txnAmt, this.orderInfo.discountAmount) / 100)
							.toFixed(2)
						return prc
						// return prc.toFixed(2);
					}
					return (this.orderInfo.txnAmt / 100).toFixed(2)
				}
				return 0
			},
			discountAmount() {
				if (this.orderInfo) {
					return (this.orderInfo.discountAmount / 100).toFixed(2)
				}
				return 0
			},
			
			txnAmt() {
				if (this.orderInfo) {
					return (this.orderInfo.txnAmt / 100).toFixed(2)
				}
				return 0
			},
			
			
		},

		onShow() {
			if (uni.getStorageSync('qrPayNotifyContent')) {
				let orderInfo = uni.getStorageSync('qrPayNotifyContent')
				console.log("=======qrPayNotifyContent=====", orderInfo);
				this.orderInfo = JSON.parse(orderInfo);
				console.log("======= this.orderInfo=====", this.orderInfo);
				uni.setStorageSync('qrPayNotifyContent', null)
			}
		},

		filters: {},

		onLoad(options) {
			app.initPage().then(res => {
				this.advertisement("QRCODE_PAY_SUCCESS")

			});
		},

		methods: {
			//获取广告
			advertisement(id) {
				let that = this;
				api.advertisementinfo({
					bigClass: id
				}).then(res => {
					console.log("res", res.data);
					this.payment = res.data;
				});
			},
		}
	};
</script>
<style scoped>
	.result-box {
		width: 750rpx;
		background: white;
	}

	.result-btn {
		width: 60%;
		height: 80rpx;
		border: 1px solid #ffffff;
		background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
		border-radius: 31px;
		margin: 20rpx auto;
		text-align: center;
		line-height: 80rpx;
		color: #fff;
		font-size: 34rpx;
	}

	.result-img {
		width: 95%;
		margin: 0 auto;
		margin-top: 50rpx;
		padding-bottom: 60rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
	}

	.img {
		/* height: 296rpx; */
		width: 100%;
		height: auto;
		border-radius: 20rpx;
	}

	.user-line {
		margin: 14rpx auto;
		width: 699rpx;
		border-bottom: 2rpx dashed #999;
	}

	.color-white {
		color: #999999;
	}

	.color-white .text-sm {
		font-size: 30rpx !important;
	}

	.text-success {
		display: flex;
		margin: 0 auto;
	}

	.text-success image {
		width: 70rpx;
		height: 70rpx;
		margin-right: 20rpx;
	}
</style>