<template>
  <view>
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
    	<block slot="backText">返回</block>
    	<block slot="content">一码付管理</block>
    </cu-custom>
    <view class="margin">
      <view class="show-box padding">
        <view class="flex align-start text-df padding-bottom-sm text-lg solid-bottom">
          <view class="name text-bold">2023年2月<text class="cuIcon-right" style="transform: rotate(90deg);"></text></view>
        </view>
        <view class="padding-tb solid-bottom">
          <view class="flex margin-bottom-sm align-start">
            <view class="flex-sub text-xdf text-bold margin-right-sm" style="line-height: 40rpx">松雷XX店线下支付XXX专柜</view>
            <view class="text-xsm" style="color: #999999;line-height: 40rpx;">2023-03-01 23:04:06</view>
          </view>
          <view class="flex align-end text-df">
            <view class="flex-sub">订单号: SC12345667890</view>
            <view>
              <view class="text-bold text-xdf" style="color: #FF0000;">-￥119.00</view>
              <view class="text-right">付款成功</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
export default {
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      hidden: true
    }
  },
  methods: {
    sumitBtn() {
      this.hidden = false;
      uni.showModal({
        title: '提示',
        content: '解除成功，如需使用可再次签约',
        showCancel: false,
        success() {
          
        }
      })
    }
  }
}
</script>

<style>
.show-box {
  margin: 25rpx 0;
  background-color: #fff;
  border-radius: 38rpx;
}
.input-flex {
  margin-left: 40rpx;
  margin-right: 40rpx;
}
</style>