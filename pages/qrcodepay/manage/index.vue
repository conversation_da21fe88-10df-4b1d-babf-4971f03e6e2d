<template>
  <view class="">
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
    	<block slot="backText">返回</block>
    	<block slot="content">一码付管理</block>
    </cu-custom>
    <view class="margin">
      <view class="show-box">
        <view class="input-flex flex align-start text-df margin-bottom">
          <view class="name text-bold">开通名称</view>
          <view class="input flex-sub padding-bottom" style="border-bottom: 1rpx solid #f1f1f1;">
            <input :disabled="true" type="text" placeholder="请输入开通名称">
          </view>
        </view>
        <view class="input-flex flex align-start text-df margin-bottom">
          <view class="name text-bold">开通时间</view>
          <view class="input flex-sub padding-bottom" style="border-bottom: 1rpx solid #f1f1f1;">
            <input :disabled="true" type="text" placeholder="请输入开通时间">
          </view>
        </view>
        <view class="input-flex flex align-start text-df margin-bottom">
          <view class="name text-bold">交易记录</view>
          <view class="input flex-sub padding-bottom" style="border-bottom: 1rpx solid #f1f1f1;text-align: right;">
            <text style="color: #000;">查看</text>
            <text class="cuIcon-right text-col"></text>
          </view>
        </view>
        <view class="input-flex flex align-start text-df">
          <view class="name text-bold">接触授权</view>
          <view class="input flex-sub padding-bottom flex">
            <input class="flex-sub" :disabled="true" type="text" placeholder="关闭后, 用户将接触授权">
            <!-- <switch color="#000" checked></switch> -->
            <switch checked class="brown switch-empty"/>
          </view>
        </view>
      </view>
    </view>
    <view class="ZZC" @click="close" v-if="hidden"></view>
    <view v-if="hidden" class="show-box position-fixed-center" style="width: 700rpx;padding: 0;">
      <image src="https://img.songlei.com/-1/material/d9f4081e-274c-494d-b753-7bc22be40828.png" style="width: 100%;height: 90rpx;" mode="widthFix"></image>
      <view class="margin-lr-lg margin-bottom-sm padding-bottom-lg padding-top text-df text-center" style="border-bottom: 1rpx solid #EBD5C1;">
        是否解除当前会员一码付服务？
      </view>
      <view class="flex align-center justify-between margin-lg">
        <view class="btn text-df" @click="sumitBtn">确认解除</view>
        <view class="btn text-df" style="background: linear-gradient(180deg, #EEDAC4 0%, #EF8C6D 0%, #EC6352 100%);color: #ffffff;" @click="close">我在想想</view>
      </view>
    </view>
  </view>
</template>

<script>
  const app = getApp();
  export default {
  	data() {
  		return {
  			theme: app.globalData.theme, //全局颜色变量
            hidden: true
  		}
  	},
  	methods: {
      sumitBtn() {
        this.hidden = false;
        uni.showModal({
          title: '提示',
          content: '解除成功，如需使用可再次签约',
          showCancel: false,
          success() {
            
          }
        })
      }
  	}
  }
</script>

<style>
.show-box {
  margin: 25rpx 0;
  background-color: #fff;
  border-radius: 38rpx;
  padding: 40rpx 0 0;
}
.input-flex {
  margin-left: 40rpx;
  margin-right: 40rpx;
}
.input-flex .name {
  width: 150rpx;
  text-align: left;
}
.ZZC {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, .5);
  top: 0;
  left: 0;
}
.position-fixed-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
.btn {
  width: 305rpx;
  height: 80rpx;
  background: linear-gradient(180deg, #D0B29A 0%, #EEDAC4 0%, #E3CAB6 100%);
  text-align: center;
  line-height: 80rpx;
  border-radius: 40rpx;
}
</style>