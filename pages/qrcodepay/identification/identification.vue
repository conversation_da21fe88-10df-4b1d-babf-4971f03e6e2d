<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">松雷商业一码付</block>
		</cu-custom>
		<view class="margin">
			<view class="flex justify-end text-black text-xsm" @click="hidden = true;">使用规则 ></view>
			<view class="show-box">
				<image src="https://img.songlei.com/-1/material/d9f4081e-274c-494d-b753-7bc22be40828.png"
					style="width: 100%;height: 90rpx;" mode="widthFix"></image>
				<view class="input-flex flex align-center text-df">
					<view class="name">姓名</view>
					<view class="input flex-sub">
						<input :value="form.username" @input="changeInput($event, 'username')" type="text"
							class="text-xsm" placeholder="请输入姓名">
					</view>
				</view>
				<view class="input-flex flex align-center text-df">
					<view class="name">身份证号</view>
					<view class="input flex-sub">
						<input :value="form.cardID" @input="changeInput($event, 'cardID')" type="text" class="text-xsm"
							placeholder="请输入身份证号">
					</view>
				</view>
				<view class="btn text-df" @click="nextBtn">下一步</view>
				<view class="text-xsm flex align-center justify-center" style="line-height: 40rpx;">
					<checkbox-group @change="checkChange">
						<label class="flex align-center">
							<checkbox value="1" :checked="check" class="round scarlet red"
								style="transform:scale(0.6);" />
							<text>我已阅读并同意<text class="cur-hover" style="color: #D0A088;">《会员一码付信息授权》</text>
							</text>
						</label>
					</checkbox-group>
				</view>
			</view>
			<view class="show-box" style="padding: 0;background-color: transparent;">
				<image src="https://img.songlei.com/-1/material/93e2aa03-2794-49c0-a972-6176b7430b3a.png"
					style="width: 100%;min-height: 650rpx;" mode="widthFix"></image>
			</view>
		</view>
		<view class="ZZC" @click="close" v-if="hidden"></view>
		<view v-if="hidden" class="show-box position-fixed-center" style="width: 700rpx;">
			<image src="https://img.songlei.com/-1/material/d9f4081e-274c-494d-b753-7bc22be40828.png"
				style="width: 100%;min-height: 90rpx;" mode="widthFix"></image>
			<view class="margin-lr-lg margin-bottom-lg padding-bottom-lg text-df"
				style="border-bottom: 1rpx solid #EBD5C1;">
				1.使用规则的关键就是使用规则，在使用规则内主要规则的关键；
			</view>
			<view class="margin-lr-lg margin-bottom-lg padding-bottom-lg text-df"
				style="border-bottom: 1rpx solid #EBD5C1;">
				2.第二项要注意的就是第一条的使用规则涉及到了那些关键点；
			</view>
			<view class="margin-lr-lg margin-bottom-lg text-df">
				3.要熟知使用规则的关键点，就一定要谨记前两条的关键点，这就是问题的关键。要熟知使用规则的关键点，就一定要谨记前两条的关键点，这就是问题的关键。
			</view>
			<view class="btn text-df" @click="close">知道了</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import {
		identity
	} from '@/pages/qrcodepay/api/qrcodepay.js'
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				hidden: false,
				check: false,
				form: {}
			}
		},
		onLoad() {
			const that = this;
			// const systemInfo = wx.getSystemInfoSync();
			// console.log(systemInfo);
			uni.getSystemInfo({
				success: function(e) {
					const {
						deviceType,
						model,
						osName,
						deviceId
					} = e;
					// const deviceID = (osName === 'ios') ? 'IDFV' : 'IMEI';
					const deviceID = deviceId;
					const deviceName = model;
					const deviceSystem = (osName === 'ios') ? 2 : 1
					that.form = {
						deviceName,
						deviceSystem,
						deviceID,
						deviceType: 1
					}
				},
			})
		},
		methods: {
			nextBtn() {
				const {
					username,
					cardID
				} = this.form;
				const {
					check
				} = this;
				let text = ''
				const idcard =
					/(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/;
				if (!check) {
					text = '请先同意协议'
				}
				if (!text && !username) {
					text = '请先输入姓名'
				}
				if (!text && !cardID) {
					text = '请输入身份证号'
				}
				if (!text && !idcard.test(cardID)) {
					text = '身份证号不正确'
				}
				if (text) {
					uni.showToast({
						icon: "none",
						title: text
					})
					return;
				}
				identity({
					...this.form,
					userName: username,
					cardID
				}).then(res => {
					const {
						code,
						data,
						msg
					} = res;
					if (+code === 0) {
						const urlString = encodeURIComponent(data);
						uni.navigateTo({
							url: '/pages/public/webview/webview?url=' + urlString
						})
					} else {
						uni.showToast({
							icon: 'none',
							title: msg
						})
					}
				})
			},
			changeInput(e, type) {
				const {
					value
				} = e.detail;
				this.form[type] = value;
			},
			checkChange(e) {
				const {
					value
				} = e.detail;
				this.check = !!value[0]
			},
			close() {
				this.hidden = false;
			}
		}
	}
</script>

<style>
	.show-box {
		margin: 25rpx 0;
		background-color: #fff;
		border-radius: 38rpx;
		padding-bottom: 40rpx;
	}

	.title {
		text-align: center;
	}

	.input-flex {
		margin: 0 40rpx;
		padding: 20rpx 0rpx 30rpx;
		border-bottom: 1rpx solid #eee;
	}

	.input-flex .name {
		width: 150rpx;
		text-align: left;
	}

	.btn {
		width: 450rpx;
		height: 80rpx;
		background: linear-gradient(180deg, #D0B29A 0%, #EEDAC4 0%, #E3CAB6 100%);
		text-align: center;
		line-height: 80rpx;
		border-radius: 40rpx;
		margin: 40rpx auto 30rpx;
	}

	.ZZC {
		position: fixed;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, .5);
		top: 0;
		left: 0;
	}

	.position-fixed-center {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 1;
	}
</style>