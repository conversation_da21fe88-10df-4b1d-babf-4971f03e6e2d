<template>
	<view style="min-height: 100vh;" @click="clearDel">
		<view>
			<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">消息中心</block>
			</cu-custom>
      <view class="cu-list">
        <block v-for="item in list">
          <messageItem :item="item" @update="update" @del="delBtn" ref="message" />
        </block>
      </view>
			<view class="cu-list menu-avatar" v-if="false">
				<block v-for="(conte,index) in customInform" :key="item.id">
					<navigator :url="'/pages/message/typeList/index?type='+conte.msgType" hover-class="none"
						class="cu-item">
						<!-- 自定义消息icon -->
						<view class="cu-avatar round lg">
							<image :src="magInfo[conte.msgType].imgUrl" alt="" mode="aspectFill"
								style="height: 100%;" />
						</view>
						<view class="content">
							<view class="text-gray">
								<view class="text-cut" style="color: #010101;">{{magInfo[conte.msgType].title}}
								</view>
							</view>

							<view class="text-gray bg-white " style="font-size: 24rpx;">
								{{conte.description}}
							</view>
						</view>
						<view class="action">
							<view class=" text-sm" style="color: #D3D3D3">{{conte.releaseTime | timestr	}}
							</view>
						</view>
					</navigator>
				</block>
			</view>
			<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
		</view>

	</view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import {
		mapState
	} from 'vuex'
	import api from 'utils/api';
	import {
		mapGetters
	} from "vuex";
  import messageItem from "../components/message-item/index.vue"
  import { getList, update, getWxTemplate } from "@/api/message.js"
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				Conversation: null,
				loadmore: false,
				Time_now: { // 现在时间
					Date: '',
					Y: '',
					M: '',
					D: '',
					h: '',
					m: '',
					s: ''
				},
				specifyTime: "", // 指定时间戳,
				customInform: [], // 自定义通知数据
        params: {
          current: 1,
          size: 10,
          descs: 'create_time',
          type: 1
        },
        list: []
			};
		},

		filters: {
			timestr: function(value) {
				let _time = value.split(" ")[1].split(":")
				return _time[0] + "-" + _time[1]
			}
		},
    components: {
      messageItem
    },
    onLoad(options) {
      this.params.type = options.type;
      this.initData();
    },

		onPullDownRefresh() {
			this.initData();
		},
    onReachBottom() {
      if(this.loadmore) {
        this.params.current++;
        this.getList()
      }
    },

		methods: {
			initData() {
				uni.stopPullDownRefresh();
        this.params.current = 1;
        this.list = [];
        this.getList()
			},

      // 站内信分类列表
      getList() {
        getList(this.params).then(res => {
          this.list = [...this.list, ...res.data.records]
          this.loadmore = res.data.total > (this.params.current * this.params.size)
        })
      },
      update(item) {
        update({ id: item.id, status: '1' }).then(res => {
          item.status = '1'
        })
      },
      delBtn(item) {
        const ins = this.list.findIndex(i => i.id === item.id);
        update({ id: item.id, status: '2' }).then(res => {
          uni.showToast({
            title: '删除成功',
            icon: 'none'
          })
          this.list.splice(ins, 1);
        })
      },
      clearDel() {
        if(this.$refs.message) {
          const message = [...this.$refs.message];
          message.forEach(item => {
            item.isLongPress();
          })
        }
      },
      wxTemplate() {
        getWxTemplate({ type: 1 }).then(res => {
          console.log(res.data);
          uni.requestSubscribeMessage({
            tmplIds: res.data,
          })
        })
      }
		}
	};
</script>
<style>
	/* #ifndef APP-PLUS-NVUE */
	@import "@/public/colorui/main.css";
	/* #endif */
</style>
<style scoped lang="scss">
	.top-container {
		height: 260rpx;
		position: relative;
	}

	.top-content-bg {
		height: 180rpx;
	}

	.top-content {
		position: absolute;
		top: 60rpx;
		height: 200rpx;
		width: 690rpx;
		margin-left: 30rpx;
		display: flex;
		justify-content: space-between;
		background-color: white;
		border-radius: 20rpx;
		box-shadow: 6rpx 6rpx 6rpx rgba(204, 204, 204, 0.35);
	}
	
	.top-item {
		display: flex;
        flex-direction: column;
		align-items: center;
		justify-content: center;
		flex: 1;
		.top-icon {
			width: 90rpx;
			height: 90rpx;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 50%;
			image {
				width: 90rpx;
				height: 90rpx;
			}
		}
		.top-txt {
			margin-top: 16rpx;
			font-size: 24rpx;
			color: #888888;
		}
		
	}

	.error-tip {
		display: flex;
		position: fixed;
		justify-content: center;
		font-size: 30rpx;
		padding: 10rpx;
		color: #ff0000;
		width: 100%;
		z-index: 2;
	}

	.cu-list {
	}
  .cu-item {
    background-color: #fff;
    min-height: 100rpx;
    border-radius: 10rpx;
    margin: 20rpx;
    box-shadow: 0 0 10rpx #ddd;
  }
  .message-radis {
    width: 10rpx;
    height: 10rpx;
    background-color: #c00;
    border-radius: 50%;
    margin-right: 10rpx;
  }

	.cu-list+.cu-list {
		margin-top: 20rpx;
	}
</style>