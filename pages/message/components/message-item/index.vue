<template>
  <view class="cu-item" @longpress="longpress">
    <view class="item-zzc" v-if="showDel">
      <view class="item-box-del" @click="del">删除</view>
    </view>
    <view class="flex align-center padding-sm" style="border-bottom: 1rpx solid #ddd;">
      <image :src="icorImg" class="margin-right-xs" style="width: 40rpx;height: 40rpx;border-radius: 50%;"></image>
      <view class="flex-sub text-sm">{{ item.title }}</view>
      <view class="text-xs" style="color: #D3D3D3;">{{ getUpdate }}</view>
    </view>
    <view @click="messageClick">
      <!-- 文字+图片 -->
      <view class="padding-lr-sm padding-tb flex align-center" v-if="item.contentType == '0'">
        <view class="flex-sub text-xsm">
          <view class="padding-bottom-xs" style="line-height: 40rpx;">{{ item.notice }}</view>
          <!-- <view style="line-height: 40rpx;">【SK-II神仙水230ml】已发货</view> -->
        </view>
        <view class="">
          <image :src="item.noticeUrl" mode="aspectFill" style="width: 100rpx;height: 70rpx;"></image>
        </view>
      </view>
      <!-- 纯图片 -->
      <view class="" @click.stop="imageClick" v-if="item.contentType == '1'">
        <image :src="item.imageUrl" mode="widthFix" style="width: 100%;display: block;"></image>
      </view>
    </view>
    <view class="flex align-center padding-sm" style="border-top: 1rpx solid #ddd;" @click="messageClick">
      <view class="flex-sub text-sm">{{ item.jumpText ||  '点击查看详情' }}</view>
      <view class="message-radis" v-if="item.status == '0'"></view>
      <view class="text-xs cuIcon-right" style="color: #D3D3D3;"></view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    
  },
  data() {
    return {
      showDel: false
    }
  },
  computed: {
    icorImg() {
      const imgobj = {
        '1': 'logistics',
        '2': 'message',
        '3': 'account',
        '4': 'notice'
      }
      const { icorType = 0, icorUrl, type } = this.item;
      if(+icorType === 0 && +type !== 0) {
        // 默认图片
        return `http://slshop-file.oss-cn-beijing.aliyuncs.com/client/mp/messages/${imgobj[type + '']}.png` 
      }
      return icorUrl
    },
    getUpdate() {
      let createTime = this.item.createTime;
      if(createTime) {
        let stamp = new Date(createTime).getTime();
        let newStamp = new Date().getTime();
        if(newStamp - stamp >  7 * 24 * 60 * 60 * 1000) {
          // 一周前的数据
          return '一周前'
        } else if(+(new Date().getDate()) == +createTime.split(" ")[0].slice(-2)) {
          // 填写时间
          return createTime.split(" ")[1].slice(0, 5)
        } else {
          // 填写日期
          return createTime.split(" ")[0].slice(-5)
        }
      }
      return '';
    }
  },
  methods: {
    imageClick() {
      this.$emit('update', this.item)
      uni.navigateTo({
        url: this.item.imageClickPath
      })
    },
    messageClick() {
      this.$emit('update', this.item)
      this.item.status = '1'; 
      if(this.item.clickPath) {
        uni.navigateTo({
          url: this.item.clickPath
        })
      }
    },
    longpress() {
      this.showDel = true;
    },
    isLongPress() {
      this.showDel = false;
    },
    del() {
      this.$emit('del', this.item)
    }
  }
}
</script>

<style>
.cu-item {
  background-color: #fff;
  min-height: 100rpx;
  border-radius: 10rpx;
  margin: 20rpx;
  box-shadow: 0 0 10rpx #ddd;
  position: relative;
}
.message-radis {
  width: 10rpx;
  height: 10rpx;
  background-color: #c00;
  border-radius: 50%;
  margin-right: 10rpx;
}
.item-zzc {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, .5);
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.item-box-del {
  width: 100rpx;
  height: 100rpx;
  background-color: #c00;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 28rpx;
}
</style>