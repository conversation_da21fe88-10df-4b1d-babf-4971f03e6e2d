<template>
  <view>
    <view>
      <cu-custom
        :bgColor="'bg-'+theme.backgroundColor"
        :isBack="true"
        :hideMarchContent="true"
      >
        <block slot="backText">返回</block>
        <block slot="content">{{title}}</block>
      </cu-custom>
      <view
        class="jz-list"
        :key="i"
      >
        <div-base-navigator
          :pageUrl="item.activitySmallPageLink"
          hover-class="none"
          v-for="(item,index) in activitiesList"
          :key="index"
          class="jz-item"
        >
          <view
            class="item-left"
            v-if="msgType=='0'"
          >
            <image
              src="https://img.songlei.com/live/shop/recommendation.png"
              mode="widthFix"
            ></image>
          </view>
          <view class="item-rig">
            <view
              class="item-title"
              v-if="msgType=='0'"
            >每日优选</view>
            <view class="item-name-box">
              <view class="item-name">
                <view class="item-name-box1">
                  {{item.title}}
                </view>
              </view>
              <view class="item-time">{{item.timess}}</view>
            </view>
            <view class="item-goods">
              <view class="item-goods-img">
                <image
                  :src="item.activityImgUrl"
                  mode="widthFix"
                ></image>
                <!-- <image src="../../../static/public/img/p-default.png" mode="widthFix"></image> -->
              </view>
              <view class="item-goods-describe">
                <view class="item-goods-describe-text">
                  {{item.description}}
                </view>
              </view>
            </view>
          </view>
        </div-base-navigator>
      </view>
      <view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
    </view>

  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'

import api from 'utils/api';

export default {
  components: {
    divBaseNavigator
  },
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      activitiesList: [], // 数据列表
      loadmore: true,
      Time_now: { // 现在时间
        Date: '',
        Y: '',
        M: '',
        D: '',
        h: '',
        m: '',
        s: ''
      },
      msgType: "", // 活动类型 0：活动优惠；1：服务通知;2:物流通知
      page: {
        current: 1,
        size: 10,
      },
	  //有数统计使用
	  page_title:''

    };
  },
  computed: {
    title: function () {
      let _name = ""
      if (this.msgType == "0") {
        _name = "活动优惠"
		this.page_title =  "活动优惠列表"
      } else if (this.msgType == "0") {
        _name = "服务通知"
		this.page_title =  "服务通知"
      } else {
        _name = "物流通知"
		this.page_title =  "物流通知"
      }
      return _name;
    }
  },
  props: {},

  onShow () {
    if (!util.isUserLogin()){
		uni.reLaunch({
		  url: '/pages/login/index'
		});
		return
	}
    // app.initJIM().then(res => {

    // app.getJIMUnreadMsgCnt()
    // });
    // }


  },
  filters: {

    timestr: function (value) {
      let _time = value.split('-')
      console.log("value", _time);
      if (_time[1]) {
        return _time[0] + "-" + _time[1]
      } else {
        return _time[0]

      }


    }
  },
  onLoad (options) {
    this.msgType = options.type || '';
    this.Times_now();
    this.getActivityList();

  },

  methods: {
    getActivityList () {
      let that = this;
      // this.loadmore = true
      api.getActivityList(Object.assign({}, this.page, {
        msgType: that.msgType
      })).then(res => {
        let _list = res.data.records;
        that.activitiesList = [...this.activitiesList, ..._list];
        console.log("that.activitiesList", that.activitiesList);
        if (_list.length < this.page.size) {
          this.loadmore = false;
        }
        _list.forEach((item, i) => {
          that.get_message_time(item.releaseTime, i)
        })

      })
      uni.hideLoading()
    },
    get_message_time (timestamp, msg_ids) {
      let that = this;
      let Time_now = that.Time_now;
      // let timess="";
      var date = new Date(timestamp);
      console.log(date, "=================")
      var Y = date.getFullYear() + '-';
      var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      var D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
      var h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
      var m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes()) + '';
      var s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
      if (Y == Time_now.Y && M == Time_now.M && D == Time_now.D) {
        that.activitiesList[msg_ids].timess = h + m;
        // timess = h + m;
      } else {
        that.activitiesList[msg_ids].timess = M + D;
        // timess = M + D;
      }
      // return Y + M + D + h + m + s;
      // return timess;
    },
    Times_now () {
      let that = this;
      let date = new Date();
      let Y = date.getFullYear() + '-';
      let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      let D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
      let h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
      let m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes()) + ':';
      let s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
      // console.log(date)
      that.Time_now.Date = date;
      that.Time_now.Y = Y;
      that.Time_now.M = M;
      that.Time_now.D = D;
      that.Time_now.h = h;
      that.Time_now.m = m;
      that.Time_now.s = s;
    },


  },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.getActivityList();
    }
  },
};
</script>
<style scoped lang="scss">
.jz-list {
  padding: 20rpx;
}

.jz-item {
  background: #ffffff;
  border-radius: 22rpx;
  display: flex;
  padding: 26rpx;

  .item-left {
    width: 46rpx;
    padding-right: 16rpx;
  }

  .item-rig {
    flex: 1;
    // width: calc(100% - 46rpx - 16rpx);
  }
}

.jz-item + .jz-item {
  margin-top: 20rpx;
}

.item-rig {
  .item-title {
    line-height: 46rpx;
    font-size: 26rpx;
    font-weight: bold;
    color: #777777;
  }

  .item-name-box {
    width: 100%;
    display: flex;
    height: 50rpx;
    align-items: center;
    // justify-content: center;

    .item-name {
      flex: 1;
      width: 0;

      .item-name-box1 {
        font-size: 28rpx;
        color: #000000;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .item-time {
      font-size: 24rpx;
      font-weight: bold;
      color: #bbbbbb;
      overflow: hidden;
    }
  }

  .item-goods {
    padding-top: 11rpx;
    width: 100%;
    display: flex;

    .item-goods-img {
      width: 120rpx;
      height: 120rpx;
      margin-right: 14rpx;

      image {
        width: 120rpx;
        height: 120rpx;
      }
    }

    .item-goods-describe {
      width: calc(100% - 120rpx - 14rpx);
      // background: #f8f8f8;
      border-radius: 13rpx;
      height: 120rpx;
      display: flex;
      padding: 0 22rpx;
      align-items: center;

      .item-goods-describe-text {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 26rpx;
        color: #777777;
      }
    }
  }
}
</style>
