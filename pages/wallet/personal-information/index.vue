<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">开通钱包</block>
    </cu-custom>

    <image
      style="width: 100%; height: 490rpx;"
      mode="widthFix"
      src="https://img.songlei.com/wallet/wallet-open3.png"
    ></image>
    <view class="explain">
      填写个人信息
    </view>

        <open-bank-card ref="formData"></open-bank-card>

        <view
          class="cu-form-group phone_margin"
          style="padding:16rpx 0rpx 30rpx;border-top: none;"
        >
          <radio-group
            @change="changeCheck"
            style="margin: auto;padding: 0 30rpx;"
          >
            <radio
              style="transform:scale(0.7);"
              class="green"
              :checked="checked"
              value="1"
            ></radio>
            <text class="text-sm">申请松鼠钱包意味着您同意开通Ⅲ类电子账户和</text>
            <text
              class="text-sm text-green bord-btm"
              @tap="goMinSheng(4)"
            >中国民生银行结算账户管理协议、</text>
          
              <text
                class="text-sm text-green bord-btm"
                @tap="goMinSheng(3)"
              >个人税收居民身份声明文件、</text>
              <text
                class="text-sm text-green bord-btm"
                @tap="goMinSheng(1)"
              >“账户信息即时通”服务协议、</text>
              <text
                class="text-sm text-green bord-btm"
                @tap="goMinSheng(2)"
              >中国民生银行客户第三方平台授权书、</text>
              <text
                class="text-sm text-green bord-btm"
                @tap="goMinSheng(5)"
              >中国民生银行“跨行通”业务实时归集协议</text>
          </radio-group>
        </view>

        <view class="login_padding flex flex-direction">
          <button
            class="cu-btn margin-tb-sm lg"
            :class="'bg-'+theme.backgroundColor"
            @click="regSub"
          >下一步</button>
        </view>
  </view>
</template>

<script>
const app = getApp();

import openBankCard from "@/components/open-bank-card/index.vue";

export default {
  components: {
    openBankCard
	},
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      form:{},
	  checked: false
    };
  },

  methods: {
	  changeCheck(){
		this.checked = !this.checked;
	  },
    //去H5协议
    goMinSheng (id) {
      uni.navigateTo({
        url: `/pages/wallet/webview/index?type=${id}`
      });
    },

    //提交
    regSub (e) {
      console.log("下一步", e);
      let that = this
      that.form = this.$refs.formData.form;
      if (!that.form.cifName) {
        uni.showToast({
          title: '请输入中文姓名',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.idNo) {
        uni.showToast({
          title: '请输入身份证号',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.tAcNo) {
        uni.showToast({
          title: '请输入银行卡号',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.mobilePhone) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.messageCode) {
        uni.showToast({
          title: '请输入手机号码验证码',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      let usreWalletInfo = that.form
      console.log("用户钱包详情", usreWalletInfo);
      uni.setStorageSync('USER_WALLET_INFO', usreWalletInfo);
      uni.navigateTo({
        url: '/pages/wallet/wallet-password/index?type=1'
      });
    },
  }
}
</script>

<style>
page {
  background: #ffffff;
}
</style>

<style scoped>
.bord-btm {
  border-bottom: 1px solid #00b62a;
}

.login_padding {
  padding: 0rpx 30rpx 0rpx;
}

.phone_margin {
  margin: 15rpx 0rpx 0rpx;
  border-radius: 10rpx;
}

.explain {
  text-align: center;
  width: 750rpx;
  height: 62rpx;
  line-height: 70rpx;
  font-size: 32rpx;
  color: #000;
  padding-left: 46rpx;
  background: #ffffff;
}
</style>

