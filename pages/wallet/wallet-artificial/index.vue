<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">升级账户</block>
    </cu-custom>

    <view class="explain">
      <view class="content">
        <view>· 请上传身份证正反面</view>
        <view>· 该证件上传权限仅限银行验证审核使用</view>
        <!-- <view>· 按要求上传证件信息后1-2个工作日内完成审核</view> -->
      </view>
    </view>

    <form @submit="regSub">
      <view style="background: #FFFFFF;">
        <view
          class="cu-form-group phone_margin"
          style="margin-top: 0rpx;"
        >
          <view class="title">姓 名</view>
          <input
            placeholder="请输入中文姓名"
            style="padding-left: 50rpx;color:#cccccc"
            type="text"
            disabled
            name="cifName"
            v-model="form.cifName"
          />
        </view>

        <view class="cu-form-group password_margin">
          <view class="title">身份证号</view>
          <input
            placeholder="请输入身份证号"
            style="color:#cccccc"
            name="idNo"
            type="idcard"
            disabled
            v-model="form.idNo"
          />
        </view>

        <!-- <view
          v-if="!idAPath&&!idBPath"
          class="cu-form-group phone_margin"
        >
          <view
            class="title"
            style="font-size: 30rpx;color: #000000;margin: 0rpx auto;"
          >请上传身份证正反面</view>
        </view> -->

        <view
          v-if="idAPath"
          class="text-center"
        >
          <view class="idAPath-text">
            <view class="head-text">
              身份证头像面
            </view>

            <view
              class="modify"
              @tap="delImg(1)"
            >
              修改
            </view>
          </view>
          <image
            @click="handlePreviewImage(1)"
            :src="idAPath"
            style="height: 333rpx;width: 550rpx;border-radius: 15rpx;"
          ></image>
        </view>

        <view
          class="headId"
          v-if="!idAPath"
        >
          <view class="flex">
            <view @tap="chooseImage(1)">
              <view class="text">头像面</view>
              <view class="headId-explain">上传您的身份证头像面</view>
              <view class="text-center">
                <image
                  class="Icon"
                  src="https://img.songlei.com/wallet/Icon.png"
                ></image>
              </view>
            </view>

            <view>
              <image
                class="head"
                src="https://img.songlei.com/wallet/back.png"
              ></image>
            </view>
          </view>
        </view>

        <view
          v-if="idBPath"
          class="text-center"
        >
          <view
            class="idAPath-text"
            style=" padding-top: 30rpx;"
          >
            <view class="head-text">
              身份证国徽面
            </view>

            <view
              class="modify"
              @tap="delImg(2)"
            >
              修改
            </view>
          </view>
          <image
            @click="handlePreviewImage(2)"
            :src="idBPath"
            style="height: 333rpx;width: 550rpx;border-radius: 15rpx;"
          ></image>
        </view>

        <view
          class="headId"
          style="margin-top:25rpx"
          v-if="!idBPath"
        >
          <view></view>
          <view class="flex">
            <view @tap="chooseImage(2)">
              <view class="text">国徽面</view>
              <view class="headId-explain">上传您的身份证国徽面</view>
              <view class="text-center">
                <image
                  class="Icon"
                  src="https://img.songlei.com/wallet/Icon.png"
                ></image>
              </view>
            </view>

            <view>
              <image
                class="head"
                src="https://img.songlei.com/wallet/head.png"
              ></image>
            </view>
          </view>
        </view>

        <view class="login_padding flex flex-direction">
          <button
            class="cu-btn margin-tb-sm lg"
            :class="'bg-'+theme.backgroundColor"
            form-type="submit"
          >上传证件</button>
        </view>
      </view>
    </form>

    <!-- 认证弹框 -->
    <authentication-dialog
      :showModal="hideDialog"
      :pageType="1"
      :code="code"
      :msg="msg"
      @changeModal="hideDialog = $event"
    ></authentication-dialog>

    <w-compress ref='wCompress' />

    <canvas
      class="canvas"
      canvas-id="canvas"
      :style="{width:cWidth+'px',height:cHeight+'px', visibility: 'hidden', 'position':'absolute', 'z-index': '-1', left: '-10000rpx',top:'-10000rpx'}"
    ></canvas>
  </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import __config from 'config/env';
import util from 'utils/util';
// import formData from '@/public/common/formData.js';
import authenticationDialog from "../components/authentication-dialog/index.vue";
import WCompress from '../components/w-compress/w-compress.vue'
import { getLessLimitSizeImage, imageSizeIsLessLimitSize, getCanvasImage, getBase64 } from '../components/w-compress/common.js'

export default {
  components: {
    authenticationDialog,
    WCompress
  },

  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      form: {
        idNo: '',
        cifName: ''
      },
      hideDialog: false,//弹框控制
      idAPath: '',// 身份证人头正面
      idBPath: '',// 身份证国徽背面
      code: 2,//认证码: 1是失败 2是成功
      msg: '',//认证返回信息

      idAPathImg: '',
      idBPathImg: '',

      cWidth: 2000,
      cHeight: 2000,

      imgFiles: [],
      compressImgs: [],
    }; getCurrentInstance
  },
  onLoad (options) {
    if (uni.getStorageSync('Account') != null && uni.getStorageSync('Account').length != 0) {
      let data = uni.getStorageSync('Account')
      console.log("onLoad====data===>", data);
      this.form.idNo = data.idNo
      this.form.cifName = data.cifName
    }
  },
  filters: {
    getBase64ImgUrl (res) {
      //拿到后端给的base64字符串
      var shareQrImg = util.getBase64ImageUrl(res)
      return shareQrImg
    }

  },

  methods: {
    //图片预览
    handlePreviewImage (index) {
      // 预览图片
      uni.previewImage({
        urls: index == '1' ? this.idAPath.split(',') : this.idBPath.split(',')
      });
    },

    //选择图片上传
    chooseImage (type) {
      let maxSize = 300;
      let that = this
      let dWidth = uni.getSystemInfoSync().windowWidth;
      uni.chooseImage({
        count: 1,//默认9
        success: (chooseImageRes) => {
          const tempFilePaths = chooseImageRes.tempFilePaths;

          uni.showLoading({
            title: '上传中'
          });

          console.log("tempFilePaths", tempFilePaths);
          console.log('size', chooseImageRes.tempFiles[0].size / 1024, "dWidth", dWidth);

          // // 使用common.js压缩
          // // 返回选定图片的本地文件列表，tempFilePaths可以作为img标签的src列表
          // // 当一次选择多张图片的情况
          // chooseImageRes.tempFilePaths.forEach(v => {
          //   that.imgFiles.push(v);
          //   uni.getFileInfo({
          //     filePath: v,
          //     success: function (res) {
          //       var cW = res.width, cH = res.height, rat = 1.1;
          //       that.cWidth = cW;
          //       that.cHeight = cH;
          //     }
          //   })

          //   getLessLimitSizeImage('canvas', v, maxSize, dWidth, function (img) {
          //     that.compressImgs.push(img);
          //     uni.getFileInfo({
          //       filePath: img,
          //       success: function (res) {
          //         console.log('压缩后：' + res.size / 1024 + 'kb')
          //         console.log("==========", img);
          //         that.onload(img).then(res => {
          //           console.log("onload", res);
          //           // let link = res.link
          //           if (type == '1') {
          //             // that.idAPath = link
          //             that.idAPath = img.toString()
          //           } else {
          //             // that.idBPath = link
          //             that.idBPath = img.toString()
          //           }
          //         })
          //         uni.hideLoading()
          //       }
          //     })
          //   })
          // });

          console.log("tempFilePaths.toString()", tempFilePaths.toString(), type);

          //使用组件压缩
          this.$refs.wCompress.start(chooseImageRes.tempFilePaths[0], {
            // pixels: 4000000,  // 最大分辨率，默认二百万
            quality: 0.8,     // 压缩质量，默认0.8
            // type: 'png',      // 图片类型，默认jpg
            base64: false,     // 是否返回base64，默认false，非H5有效
          })
            .then(res => {
              console.log("压缩结果", res)
              this.onload(res).then(ress => {
                console.log("tempFilePaths.toString()", tempFilePaths.toString(), type, ress);
                // let link = res.link
                if (type == '1') {
                  // that.idAPath = link
                  // that.idAPathImg = tempFilePaths.toString()
                  that.idAPath = res.toString()
                } else {
                  // that.idBPath = link
                  // that.idBPathImg = tempFilePaths.toString()
                  that.idBPath = res.toString()
                }
              })
              uni.hideLoading()
            })
            .catch(e => {
              console.log(e)
              uni.hideLoading()
            })

        }
      });
    },

    onload (tempFilePaths) {
      let _url = __config.basePath + '/mallapi/file/upload';
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          header: {
            'client-type': 'MA', //客户端类型小程序
            'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
            'third-session': uni.getStorageSync('third_session') ? uni
              .getStorageSync('third_session') : '',
          },
          url: _url,
          filePath: tempFilePaths,
          name: 'file',
          formData: {
            'fileType': 'image',
            'dir': 'goods/appraises/'
          },
          success: (uploadFileRes) => {
            if (uploadFileRes.statusCode == '200') {
              console.log("uploadFileRes---", uploadFileRes);
              resolve(JSON.parse(uploadFileRes.data))
            } else {
              uni.showModal({
                title: '提示',
                content: '上传失败：' + uploadFileRes.data,
                success (res) { }
              });
            }
          },
          complete: () => {
            uni.hideLoading()
          }
        });
      })
    },

    //删除图片
    delImg (index) {
      let that = this
      if (index == "1") {
        that.idAPath = ''
      } else {
        that.idBPath = ''
      }

    },

    //提交
    regSub (e) {
      console.log(e);
      let that = this
      // if (!that.form.cifName) {
      //   uni.showToast({
      //     title: '请输入中文姓名',
      //     icon: 'none',
      //     duration: 3000
      //   });
      //   return;
      // }
      // if (!that.form.idNo) {
      //   uni.showToast({
      //     title: '请输入身份证号',
      //     icon: 'none',
      //     duration: 3000
      //   });
      //   return;
      // }
      if (!that.idAPath) {
        uni.showToast({
          title: '请上传您的身份证头像面',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.idBPath) {
        uni.showToast({
          title: '请上传您的身份证国徽面',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      const FormData = require('@/public/common/formData.js')
      let fd = new FormData()

      //改成不上送用户名和身份证号
      // let form = JSON.stringify({ cifName: encodeURIComponent(that.form.cifName), idNo: that.form.idNo })
      // console.log("dto", form);
      // fd.append('dto', form)
      fd.appendFile('idAPath', that.idAPath)
      fd.appendFile('idBPath', that.idBPath)
      let data = fd.getData();
      uni.showLoading({
        title: '提交认证审核中...'
      });
      uni.request({
        url: __config.basePath + '/mallapi/purseUser/authentication',
        method: 'POST',
        header: {
          'client-type': 'MA', //客户端类型小程序
          'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
          'third-session': uni.getStorageSync('third_session') ? uni
            .getStorageSync('third_session') : '',
          'Content-type': data.contentType
        },
        data: data.buffer,
        success: (res) => {
          uni.hideLoading();
          console.log(res.data);
          if (res.data) {
            if (res.data.code == '1') {
              that.code = 1
              that.msg = res.data.msg
            } else {
              that.code = 2
              uni.setStorageSync('Artificial', that.form);
            }
            that.hideDialog = true
          }
        }
      });

      // api.UploadAuthentication(data.buffer).then(res => {
      //   console.log("res", res);
      // });

    },
  }

}
</script>

<style>
page {
  background-color: #ffffff;
}
</style>

<style scoped>
.head-text {
  font-size: 30rpx;
  color: #000;
}
.modify {
  width: 110rpx;
  height: 44rpx;
  background: #ffffff;
  border: 1rpx solid #dadada;
  border-radius: 22rpx;
  font-size: 26rpx;
  color: #5c5c5c;
}

.idAPath-text {
  display: flex;
  justify-content: space-between;
  padding: 0rpx 30rpx 30rpx;
}
.text {
  font-weight: bold;
  color: #000000;
}
.headId-explain {
  width: 265rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #777777;
  margin: 15rpx 0rpx 25rpx;
}
.headId {
  margin: auto;
  padding: 30rpx;
  display: flex;
  width: 686rpx;
  height: 300rpx;
  background: #f7f8fa;
  border-radius: 20rpx;
}
.head {
  width: 370rpx;
  height: 241rpx;
}
.Icon {
  width: 127rpx;
  height: 127rpx;
}
.input-border {
  border: 1px solid #bfbfbf;
  border-radius: 10rpx;
}
.text-Offline {
  border-bottom: 1px solid black;
}
.code_radius {
  border-radius: 10rpx;
}
.login_padding {
  padding: 30rpx 30rpx 0rpx;
}
.phone_margin {
  margin: 15rpx 30rpx 0rpx;
  border-radius: 10rpx;
}
.password_margin {
  margin: 0rpx 30rpx;
  border-radius: 10rpx;
}

.content {
  margin: 0rpx auto;
  width: 660rpx;
  height: 110rpx;
  padding: 15rpx 50rpx;
  font-size: 26rpx;
  border-radius: 15rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #575757;
  background-color: #ccc;
  line-height: 36rpx;
}
.explain {
  width: 750rpx;
  height: 125rpx;
  padding-top: 15rpx;
  background: #ffffff;
}
</style>

