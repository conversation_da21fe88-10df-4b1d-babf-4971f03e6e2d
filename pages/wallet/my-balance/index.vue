<template>
	<view style="background-color: #ffffff">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">我的余额</block>
		</cu-custom>

		<view class="text-center">
			<view class="Icon text-center">￥</view>
			<view style="font-size: 30rpx; padding-bottom: 60rpx">我的余额</view>
			<view>
				<text class="Icon2">￥</text>
				<text class="balance-num">{{ availableBalance || '' }}</text>
			</view>
			<view @tap="goMinSheng" class="text">免费开通民生信用卡 享300元现金券 ></view>
		</view>

		<view>
			<button
				class="recharge"
				:style="{
					background: '#C29B7E',
					marginTop: '240rpx',
					color: '#FFFFFF'
				}"
				@tap="goPay(1)"
			>
				充值
			</button>

			<button
				class="recharge"
				:style="{
					background: '#F1F1F1',
					marginTop: '32rpx',
					color: '#C29B7E'
				}"
				@tap="goPay(2)"
			>
				提现
			</button>
		</view>

		<view class="cu-list menu card-menu margin-top-xs all-orders">
			<view class="cu-list grid col-4 no-border" style="padding: 0rpx">
				<view class="cu-item" style="display: flex; justify-content: center; flex-direction: row">
					<view>
						<navigator url="/pages/wallet/upload-bank-card/index" hover-class="none">
							<view class="cuIcon-vipcard" style="color: #2b6fc2"></view>
							<text class="user-text-xl" style="color: #000000">升级账户</text>
						</navigator>
					</view>
				</view>

				<view class="cu-item">
					<navigator url="/pages/wallet/bill/index" hover-class="none">
						<view class="cuIcon-form" style="color: #c29b7e"></view>
						<text class="user-text-xl" style="color: #000000">账单</text>
					</navigator>
				</view>

				<view class="cu-item">
					<navigator :url="'/pages/wallet/wallet-settings/index?availableBalance=' + availableBalance" hover-class="none">
						<view class="cuIcon-settings" style="color: #2b6fc2"></view>
						<text class="user-text-xl" style="color: #000000">设置</text>
					</navigator>
				</view>

				<view class="cu-item">
					<view @tap="handleCall">
						<view class="cuIcon-service" style="color: #c29b7e"></view>
						<text class="user-text-xl" style="color: #000000">客服</text>
					</view>
				</view>
			</view>
		</view>

		<view class="text-center" style="position: absolute; justify-content: center; width: 100%; bottom: 0">
			<!-- <view
        class="phone"
        @tap="phone"
      >客服电话</view> -->
			<view style="font-size: 22rpx; color: #000000; padding-bottom: 33rpx">本服务由民生银行提供</view>
		</view>

		<!--组件与开通银行卡支付通用-->
		<verification-code-popup @change="mobileCodeChange($event)" :showModal="showModal" @HideModal="HideModal" :mobileNo="mobilePhone"></verification-code-popup>
		<uni-popup ref="perpopup" type="center" :mask-click="false">
			<view class="permissions_box">当您使用APP时，拨打电话咨询商家时候需要拨打电话权限，不授权上述权限，不影响APP其他功能使用。</view>
		</uni-popup>
	</view>
</template>

<script>
// import '../../../public/wallet/sip.min.js';
const app = getApp();

const util = require('utils/util.js');
import __config from 'config/env';
import api from 'utils/api';
import validate from 'utils/validate';

import verificationCodePopup from 'components/verification-code-popup/index.vue';

const MSGINIT = '发送',
	MSGSCUCCESS = '${time}秒后可重发',
	MSGTIME = 60;

export default {
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			CustomBar: this.CustomBar,
			availableBalance: '', //余额
			firstInnerFlag: '', //首次入金标志 0：否，1：是
			bankInnerPath: '', //签约状态 20就让用户输入验证码，00和70就没有验证码
			mobilePhone: '', //手机号
			messageTaskId: '', //验证码流水号
			showModal: false, //弹框控制
			payType: '', //1 充值 2 提现
			msgKey: false,
			msgText: MSGINIT,
			msgTime: MSGTIME,
			code: ''
		};
	},

	components: {
		verificationCodePopup
	},
	props: {},

	onLoad(options) {
		//获取余额
		this.getAccountQuery();
	},

	onShow() {},

	methods: {
		HideModal(val) {
			this.showModal = val;
		},

		mobileCodeChange(value) {
			console.log('value', value);
			this.code = value;
			this.getBankInnerpathSign();
		},
		//去民生信用卡页
		goMinSheng() {
			uni.navigateTo({
				url: '/pages/wallet/webview/index?type=0'
			});
		},

		handleCall() {
			// #ifdef MP-WEIXIN
			this.callFun();
			// #endif
			// #ifdef APP-PLUS
			let that = this;
			var platform = uni.getSystemInfoSync().platform;
			if (platform == 'android') {
				plus.android.checkPermission(
					'android.permission.CALL_PHONE',
					(granted) => {
						if (granted.checkResult == -1) {
							//弹出
							that.$refs.perpopup.open('top');
						} else {
							//执行你有权限后的方法
							that.callFun();
						}
					},
					(error) => {
						console.error('Error checking permission:', error.message);
					}
				);
				plus.android.requestPermissions(['android.permission.CALL_PHONE'], (e) => {
					//关闭
					that.$refs.perpopup.close();
					if (e.granted.length > 0) {
						//执行你有权限后的方法
						that.callFun();
					}
				});
			} else {
				//执行你有权限后的方法 ios
				that.callFun();
			}
			// #endif
		},

		//客服电话
		callFun() {
			uni.makePhoneCall({
				phoneNumber: '95568' //民生电话
			});
		},

		goPay(type) {
			/**
			 * 1.先查询是否首次入金 first_inner_flag  首次入金标志 0：否，1：是
			 * 2.首次入金需要查询看签约要不要验证码  bank_inner_path 20就让用户输入验证码，00和70就没有验证码
			 * 3.跳转的入金提现页
			 */

			//查询是否首次 获取用户手机号
			this.getPurse(type);
		},

		//
		hideModal() {
			this.showModal = false;
		},

		//获取手机号验证码
		getPhoneCode() {
			let that = this;
			if (that.msgKey) return;
			that.msgKey = true;
			api.getMobilePhoneCode({
				mobilePhone: that.mobilePhone,
				templateId: ''
			})
				.then((res) => {
					that.msgKey = false;
					console.log('res', res);
					if (res.code != '0') {
						uni.showToast({
							title: '验证码发送成功',
							icon: 'none',
							duration: 3000
						});
						that.messageTaskId = res.data.message_task_id;
						that.msgText = MSGSCUCCESS.replace('${time}', that.msgTime);
						that.msgKey = true;
						const time = setInterval(() => {
							that.msgTime--;
							that.msgText = MSGSCUCCESS.replace('${time}', that.msgTime);
							if (that.msgTime == 0) {
								that.msgTime = MSGTIME;
								that.msgText = MSGINIT;
								that.msgKey = false;
								clearInterval(time);
							}
						}, 1000);
					} else {
					}
				})
				.catch(() => {
					that.msgKey = false;
				});
		},

		//查询是否首次入金first_inner_flag  首次入金标志 0：否，1：是
		getPurse(type) {
			api.getPurse()
				.then((res) => {
					console.log('getPurse', res);
					if (res.data) {
						let firstInnerFlag = res.data.firstInnerFlag;
						this.firstInnerFlag = firstInnerFlag;
						this.payType = type;
						console.log('firstInnerFlag', this.firstInnerFlag);
						//首次入金需要查询看签约要不要验证码
						if (firstInnerFlag == '0') {
							console.log('22222');
							//判断用户是否需要签约
							this.getBankInnerPathQuery();
						} else {
							uni.navigateTo({
								url: `/pages/wallet/pay/index?type=${type}&availableBalance=${this.availableBalance}`
							});
						}
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},

		//如果需要验证码要传给后台
		getBankInnerpathSign() {
			api.getBankInnerpathSign({ bankInnerPath: this.bankInnerPath, messageCode: this.code ? this.code : null })
				.then((res) => {
					console.log('getBankInnerpathSign', res);
					if (res.code == '0') {
						uni.navigateTo({
							url: `/pages/wallet/pay/index?type=${this.payType}&availableBalance=${this.availableBalance}`
						});
					} else {
						//   uni.showToast({
						//   title: `${res.msg}`,
						//   icon: 'none',
						//   duration: 3000
						// });
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},

		//判断用户是否需要签约
		getBankInnerPathQuery() {
			api.getBankInnerPathQuery()
				.then((res) => {
					console.log('getBankInnerPathQuery', res);
					if (res.data) {
						//20就让用户输入验证码，00和70就没有验证码
						let bankInnerPath = res.data.best_path;
						this.bankInnerPath = bankInnerPath;
						console.log('bankInnerPath', bankInnerPath);
						if (bankInnerPath == '20') {
							//1.如果需要验证码则调验证码接口
							this.showModal = true;
							console.log('1111');
						} else {
							this.code = '';
						}
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},

		//获取电子账户信息
		getAccountQuery() {
			api.getAccountQuery()
				.then((res) => {
					console.log('getAccountQuery', res);
					if (res.data) {
						this.availableBalance = res.data.available_balance;
						this.mobilePhone = res.data.mobile_phone;
					}
				})
				.catch((err) => {
					console.log('err', err);
				});
		}
	}
};
</script>

<style>
page {
	background: #ffffff;
}
</style>
<style scoped>
.Icon {
	width: 100rpx;
	height: 100rpx;
	/* padding-top: 89rpx; */
	margin: 65rpx auto;
	line-height: 100rpx;
	font-size: 65rpx;
	color: #ffffff;
	background: #c29b7e;
	border-radius: 50%;
}
.balance-num {
	font-size: 91rpx;
	font-weight: bold;
	color: #171717;
}
.Icon2 {
	font-size: 56rpx;
	font-weight: 400;
	color: #000000;
}
.text {
	padding-top: 45rpx;
	font-size: 24rpx;
	color: #c0997c;
}
.recharge {
	border: none;
	width: 350rpx;
	height: 92rpx;
	border-radius: 46rpx;
}
.phone {
	margin-top: 130rpx;
	margin-bottom: 25rpx;
	font-size: 26rpx;
	color: #586387;
}
</style>
