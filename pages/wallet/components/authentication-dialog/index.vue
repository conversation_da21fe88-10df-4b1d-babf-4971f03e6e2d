<template>
  <view>
    <view
      class="cu-modal"
      :class="showModal ? 'show' : ''"
    >
      <view class="cu-dialog dialog-bg">
        <view class="bg-img">
          <view
            class="cu-bar text-white justify-center"
            :style="{
              backgroundColor:code=='2'?'#74D96F':'#C3C3C3'
              }"
          >
            <view style="">
              <text>实名认证</text>
            </view>
          </view>

          <view class="sign-succeed">
            <view class="padding">
              <image
                :src="code=='2'?$imgUrl('wallet/success.png'):$imgUrl('wallet/fail.png')"
                class="failImg"
              >
              </image>
            </view>

            <view class="margin-bottom">
              <text
                class=" text-lg text-bold "
                style="font-size: 35rpx; padding: 10rpx"
              >
                {{code=='2'?'认证成功':'认证失败'}}
              </text>
            </view>

            <view style="font-size: 24rpx;">
              <view style="color: #000000;">
                {{code=='2'?'恭喜您，提交的实名信息认证成功！':'您的实名认证审核没有通过'}}
              </view>
              <view style="color: #000000;margin-bottom:30rpx">
                {{code=='2'?'我们郑重承诺，对您的信息严格保密。':'请重新核对您的信息'}}
              </view>
              <view style="color: #9C9C9C;margin-bottom:30rpx">
                {{code=='2'?'您可以在“个人中心”→“我的证件”查看认证信息':msg}}
              </view>
            </view>
          </view>

          <view
            class="action"
            :class="code=='2'?'':'margin-top-lg'"
          >
            <button
              class="cu-btn bg-red round flex succeed-btn"
              :class="code=='2'?'bg-green':'bg-red margin-top-lg'"
              style="color: #FFFFFF;font-size: 24rpx;"
              @tap="hideModal"
            >
              {{code=='2'?'确认':'重新认证'}}
            </button>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script>

export default {

  data () {
    return {
    };
  },

  props: {
    //控制弹框显示隐藏
    showModal: {
      type: Boolean,
      default: true
    },
    //失败是1  成功是2 
    code: {
      type: Number,
      default: 2
    },
    //认证返回信息
    msg: {
      type: String,
      default: ''
    },
    //1是账户升级上传身份证页   
    pageType: {
      type: Number,
      default: 1
    },

  },

  methods: {
    hideModal () {
      this.$emit('changeModal', false);
      if (this.pageType == '1') {
        if (this.code == '2') {
          //升级账号填写个人信息
          uni.navigateTo({
            url: '/pages/wallet/cust-info/index'
          });
        }
        return
        //认证失败后重新认证
      } else {
        //认证成功后返回个人中心
        uni.switchTab({
          url: '/pages/tab-personal/index'
        });
      }
    }
  }


}

</script>

<style scoped>
.dialog-bg {
  background-color: unset;
  width: 574rpx;
}

.bg-img {
  background-color: #fff;
  height: 724rpx;
}

.failImg {
  width: 159rpx;
  height: 159rpx;
}
.sign-succeed {
  margin-top: 10rpx;
}

.succeed-btn {
  width: 50%;
  margin: 10rpx auto 0 auto;
}

.cu-steps.steps-bottom .cu-item::before,
.cu-steps.steps-bottom .cu-item::after {
  left: calc(0px - (100% - 80rpx) / 2);
}
</style>