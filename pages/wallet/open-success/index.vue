<template>
  <view>

    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">{{type |filterTypetTitle}}</block>
    </cu-custom>

    <view class="box">
      <view>
        <text
          v-if="walletStatus"
          class="check"
          :class="walletStatus=='0'?'cuIcon-check':'cuIcon-close'"
          :style="walletStatus=='0'?'background: #4cb80b;':'background: red;'"
        ></text>
        <text
          v-if="walletStatus=='0'"
          class="open-success"
        >{{type|filterTypetText}}</text>
        <text
          v-if="walletStatus=='1'"
          style="color: red;"
          class="open-success"
        >{{type=='0'?'开通失败':type=='3'?'升级失败':type=='1'?'充值失败':'提现失败'}}</text>
        <view
          v-if="msg"
          style="color: red;"
        >{{msg}}</view>
      </view>

      <view>
        <image
          class="img"
          src="https://img.songlei.com/wallet/open-success.png"
        ></image>

        <view class="login_padding">
          <button
            class="cu-btn margin-tb-sm wallet-btn"
            @tap="btnGo"
          >确认</button>
        </view>
      </view>
    </view>

    <view
      v-if="payment.imgUrl"
      class="text-center"
      @tap="goMinSheng"
    >
      <image
        class="banner"
        mode="widthFix"
        :src="payment.imgUrl | formatImg750"
      ></image>
    </view>

  </view>
</template>

<script>
const app = getApp();

const util = require("utils/util.js");
import __config from 'config/env';
import api from 'utils/api';
import validate from 'utils/validate';

export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,
      type: '0',//0是开户1是充值2是提现 4.销户
      date: '',
      walletStatus: '',//0是成功 1是失败
      msg: '',//失败原因
      payment: {},//广告内容
      toLink: '',//跳转链接
    };
  },

  components: {

  },
  props: {},
  filters: {
    filterTypetTitle (val) {
      let text = ''
      // type=='0'?'开通钱包':type=='3'?'钱包升级':type=='1'?'充值':'提现'
      if (val == '0') {
        text = '开通钱包'
        return text
      }
      if (val == '3') {
        text = '钱包升级'
        return text
      }

      if (val == '4') {
        text = '注销账户'
        return text
      }

      if (val == '1') {
        text = '充值'
      } else {
        text = '提现'
      }
      return text
    },

    filterTypetText (val) {

      let text = ''
      // type=='0'?'开通成功':type=='3'?'升级成功':type=='1'?'充值成功':'提现成功'
      if (val == '0') {
        text = '开通成功'
        return text
      }
      if (val == '3') {
        text = '升级成功'
        return text
      }

      if (val == '4') {
        text = '注销成功'
        return text
      }

      if (val == '1') {
        text = '充值成功'
      } else {
        text = '提现成功'
      }
      return text
    }


  },

  onLoad (options) {
    if (options && options.type) {
      this.type = options.type
      this.walletStatus = options.status
      this.msg = options.msg
    }

  },

  onShow () {
    //广告
    this.advertisement("WALLET_SUCCESS")
  },

  methods: {
    //广告
    advertisement (id) {
      api.advertisementinfo({
        bigClass: id
      }).then(res => {
        console.log("payment===>", res.data);
        this.payment = res.data;
        this.toLink = res.data.linkUrl
      });
    },
    //去民生信用卡页
    goMinSheng () {
      console.log("this.toLink", this.toLink);
      if (this.toLink) {
        uni.navigateTo({
          url: `${this.toLink}`
        });
        return
      }
      uni.navigateTo({
        url: "/pages/wallet/webview/index?type=0"
      });
    },
    //完成去钱包页
    btnGo () {
      // uni.navigateTo({
      //   url: "/pages/wallet/wallet-pages/index"
      // });
      if (this.type == '0') {
        uni.switchTab({
          url: '/pages/tab-personal/index'
        });

        return
      }
      if (this.type == '4') {
        uni.setStorageSync('WALLET_CLOSE', 'close');
        uni.switchTab({
          url: '/pages/tab-personal/index'
        });
        return
      }

      uni.navigateTo({
        url: "/pages/wallet/my-balance/index"
      });
    }
  }
};
</script>
<style scoped>
.banner {
  width: 721rpx;
  height: 172rpx;
  margin-top: 24rpx;
}
.wallet-btn {
  width: 421rpx;
  height: 76rpx;
  background: #4cb80b;
  border-radius: 38rpx;
  color: #ffffff;
}
.img {
  width: 482rpx;
  height: 398rpx;
}
.check {
  width: 67rpx;
  height: 68rpx;
  display: inline-block;
  line-height: 68rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  color: #ffffff;
  /* background: #4cb80b; */
}
.open-success {
  font-size: 45rpx;
  padding-top: 10rpx;
  display: inline-block;
  font-weight: bold;
  color: #4cb80b;
}
.box {
  padding-top: 56rpx;
  width: 710rpx;
  height: 681rpx;
  margin: auto;
  margin-top: 20rpx;
  text-align: center;
  background: #ffffff;
  border-radius: 30rpx;
}
</style>
