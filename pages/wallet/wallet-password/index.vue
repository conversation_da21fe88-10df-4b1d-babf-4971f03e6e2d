<template>
  <view style="background-color: #FFFFFF;">
    <!-- #ifndef MP -->
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">开通钱包</block>
    </cu-custom>
    <!-- #endif -->

    <view class="margin-top-xl">
      <web-view
        :src="url"
        :webview-styles="webviewStyles"
      ></web-view>
    </view>
  </view>
</template>

<script>
// import '../../../public/wallet/sip.min.js';
const app = getApp();

const util = require("utils/util.js");
import __config from 'config/env';
import api from 'utils/api';
import validate from 'utils/validate';

const clientType = "MA"; //客户端类型小程序
// const appId = uni.getAccountInfoSync().miniProgram.appId; //小程序appId
// const thirdSession = uni.getStorageSync("third_session") ?
//   uni.getStorageSync("third_session") :
//   "";
export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,
      type: '2',
      typeName: '注册',
      form: {
        // cifName: ''
      },
      randJnlNo: '',//随机数流水号
      random: '',//随机数
      newRandJnlNo: '',//随机数流水号
      newRandom: '',//随机数
      url: "",//H5地址链接
      status: '',//1：设置密码  2：修改密码  3 账户解锁  4 注销账户 5 账户升级
      upgradeData: '',//升级数据
      webviewStyles: {
        progress: {
          color: "#FF3333",
        },
      },
      appId: uni.getAccountInfoSync().miniProgram.appId,//小程序appId
      thirdSession: uni.getStorageSync("third_session") ?
        uni.getStorageSync("third_session") :
        "",
    };
  },

  components: {

  },
  props: {},

  onLoad (options) {
    if (options.data) {
      console.log("options.data", options.data);
      this.upgradeData = JSON.parse(decodeURIComponent(options.data))
    }
    let usreWalletInfo = uni.getStorageSync('USER_WALLET_INFO') ? uni.getStorageSync('USER_WALLET_INFO') : this.upgradeData;

    let user = usreWalletInfo
    // this.form.cifName = usreWalletInfo.cifName

    //获取随机数
    this.getRandomNumber(1)
    //顶部tabBar样式
    uni.setNavigationBarColor({
      frontColor: "#000000", // 必写项
      backgroundColor: "#ffffff", // 必写项
    });

    if (options) {
      if (options.type) {
        this.status = options.type
        if (this.status == '1') {
          //顶部tabBar
          uni.setNavigationBarTitle({
            title: '开通钱包',
          });
        } else if (this.status == '2') {
          this.getRandomNumber(2)
          //顶部tabBar
          uni.setNavigationBarTitle({
            title: '修改密码',
          });
        } else if (this.status == '3') {
          //顶部tabBar
          uni.setNavigationBarTitle({
            title: '账户解锁',
          });
        } else if (this.status == '4') {
          //顶部tabBar
          uni.setNavigationBarTitle({
            title: '注销账户',
          });
        } else if (this.status == '5') {
          //顶部tabBar
          uni.setNavigationBarTitle({
            title: '账户升级',
          });
        }
      }
    }

    //设置个人信息
    console.log("user", user);
    if (user) {
      if (this.status == "5") {
        this.form = user
      } else {
        this.form = JSON.stringify(user)
      }
    }

  },

  onShow () {
  },

  methods: {

    //向H5传参
    postUrl () {
      // let fromUrl = "http://192.168.50.186:8000/";
      let fromUrl = __config.walletPwdUrl
      console.log("============", fromUrl);
      if (!this.thirdSession) {
        util.backLoginPage();
        return;
      }
      if (JSON.stringify(this.form) == "{}" || this.status == '5') {
        console.log("********");
        this.getAccountQuery()
      } else {

        console.log("fromUrl========222222==========", fromUrl)
        if (this.status == '2') {
          fromUrl += `?data=${JSON.stringify(this.form)}&randJnlNo=${this.randJnlNo}&newRandom=${this.newRandom}&newRandJnlNo=${this.newRandJnlNo}&random=${this.random}&client_type=${clientType}&app_id=${this.appId}&third_session=${this.thirdSession}&type=${this.status}&url=${__config.basePath}`;
        } else {
          fromUrl += `?data=${JSON.stringify(this.form)}&randJnlNo=${this.randJnlNo}&random=${this.random}&client_type=${clientType}&app_id=${this.appId}&third_session=${this.thirdSession}&type=${this.status}&url=${__config.basePath}`;
        }
        console.log("===fromUrl========", fromUrl);
        this.url = fromUrl;
      }
    },

    //获取随机数 需要通过随机数生成加密 密码加密密文传给后端
    getRandomNumber (type) {
      api.getRandomNumber().then((res) => {
        console.log("getRandomNumber", res);
        if (res.code == '0') {
          let data = res.data
          if (type == '2') {
            this.newRandJnlNo = data.rand_jnl_no
            this.newRandom = data.random
          } else {
            this.randJnlNo = data.rand_jnl_no
            this.random = data.random
          }
          this.postUrl()
        } else {
        }
      }).catch(() => {
      })
    },
    //获取电子账户信息
    getAccountQuery () {
      // let fromUrl = "http://192.168.50.186:8000/";
      let fromUrl = __config.walletPwdUrl
      // if (this.status == '5') {
      //   fromUrl += `?data=${JSON.stringify(this.form)}&randJnlNo=${this.randJnlNo}&random=${this.random}&client_type=${clientType}&app_id=${this.appId}&third_session=${this.thirdSession}&type=${this.status}&url=${__config.basePath}`;
      //   this.url = fromUrl;

      // } else {
      api.getAccountQuery().then((res) => {
        console.log("getAccountQuery", res);
        if (res.data) {
          this.form.availableBalance = res.data.available_balance
          this.form.mobilePhone = res.data.mobile_phone
          this.form.tAcNo = res.data.t_ac_no
          this.form.cifName = res.data.cif_name
          if (!this.thirdSession) {
            util.backLoginPage();
            return;
          }
          if (this.status == '2') {
            fromUrl += `?data=${JSON.stringify(this.form)}&randJnlNo=${this.randJnlNo}&newRandom=${this.newRandom}&newRandJnlNo=${this.newRandJnlNo}&random=${this.random}&client_type=${clientType}&app_id=${this.appId}&third_session=${this.thirdSession}&type=${this.status}&url=${__config.basePath}`;
          } else {
            fromUrl += `?data=${JSON.stringify(this.form)}&randJnlNo=${this.randJnlNo}&random=${this.random}&client_type=${clientType}&app_id=${this.appId}&third_session=${this.thirdSession}&type=${this.status}&url=${__config.basePath}`;
          }
          console.log("===fromUrl====1111111====", fromUrl);
          this.url = fromUrl;
        }
      }).catch((err) => {
        console.log("err", err);
      })
      // }
    },
  }
};
</script>
<style scoped>
/* @import '../../../public/wallet/Keyboard/keyboard.css'; */
.explain {
  width: 750rpx;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 40rpx;
  color: #000;
  padding-left: 46rpx;
  background: #ffffff;
}

.text-Offline {
  border-bottom: 1px solid black;
}
.code_radius {
  border-radius: 10rpx;
}
.login_padding {
  padding: 50rpx 30rpx 0rpx;
}
.phone_margin {
  margin: 15rpx 30rpx 0rpx;
  border-radius: 10rpx;
}
.password_margin {
  margin: 12rpx 30rpx;
  border-radius: 10rpx;
}
</style>
