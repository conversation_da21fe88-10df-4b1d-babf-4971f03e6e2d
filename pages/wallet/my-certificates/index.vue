<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">我的证件</block>
    </cu-custom>

    <view class="explain">
      我的证件信息
      <!-- <view style="font-size: 30rpx;color: #999999;">
        开通钱包添加证件信息
      </view> -->
    </view>

    <form @submit="toWallet">
      <view style="background: #FFFFFF;">

        <!-- <view
          v-if="card"
          class="text-center"
        >
          <view class="idAPath-text">
            <view class="head-text">
              银行卡正面图片

            </view>

            <view
              class="modify"
              @tap="delImg(1)"
            >
              修改
            </view>
          </view>
          <image
            @click="handlePreviewImage(1)"
            :src="card"
            style="height: 333rpx;width: 550rpx;border-radius: 15rpx;"
          ></image>
        </view> -->

        <view
          class="headId"
          v-if="!card"
        >
          <view class="padding-top">
            <view @tap="toWallet()">
              <view class="text-center">
                <image
                  class="Icon"
                  src="https://img.songlei.com/wallet/Icon.png"
                ></image>
              </view>

              <view class="headId-explain">开通钱包添加证件信息</view>
            </view>
          </view>
        </view>

        <view class="login_padding flex flex-direction">
          <button
            class="cu-btn margin-tb-sm lg"
            :class="'bg-'+theme.backgroundColor"
            form-type="submit"
          >确定</button>
        </view>
      </view>
    </form>

  </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import __config from 'config/env';
import util from 'utils/util';
// import formData from '@/public/common/formData.js';


export default {
  components: {

  },

  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      form: {},
      card: '',// 银行卡
      code: 2,//认证码: 1是失败 2是成功
      msg: ''//认证返回信息
    };
  },
  onLoad (options) {
    const Artificial = uni.getStorageSync('Artificial');
    //是否开户
    this.isAccount()
    if (Artificial) {
      console.log("Artificial", Artificial);
      this.form.idNo = Artificial.idNo
    }
  },

  methods: {
    //钱包
    toWallet () {
      //1判断用户是否开通 没有开通去开通
      if (uni.getStorageSync('Account').length == 0) {
        //填写个人信息
        uni.navigateTo({
          url: "/pages/wallet/personal-information/index"
        });
        return
      } else {
        //2.如果开通请求获取用户证件信息
        // uni.navigateTo({
        //   url: "/pages/wallet/wallet-pages/index"
        // });
      }
    },

    //是否开户的，有数据代表开户，没数据代表没开户
    isAccount () {
      api.isAccount().then((res) => {
        // console.log("isAccount", res);
        let Account = res.data
        uni.setStorageSync('Account', Account);
        console.log("开通钱包", uni.getStorageSync('Account').length);
      })
    },
  }

}
</script>

<style scoped>
.head-text {
  font-size: 30rpx;
  color: #000;
}
.modify {
  width: 110rpx;
  height: 44rpx;
  background: #ffffff;
  border: 1rpx solid #dadada;
  border-radius: 22rpx;
  font-size: 26rpx;
  color: #5c5c5c;
}

.idAPath-text {
  display: flex;
  justify-content: space-between;
  padding: 0rpx 30rpx 30rpx;
}

.headId-explain {
  width: 265rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #777777;
  margin: 15rpx auto;
  text-align: center;
}
.headId {
  margin: auto;
  padding: 30rpx;
  width: 650rpx;
  height: 308rpx;
  background: #f7f8fa;
  border-radius: 20rpx;
}
.head {
  width: 370rpx;
  height: 241rpx;
}
.Icon {
  width: 127rpx;
  height: 127rpx;
}

.login_padding {
  padding: 300rpx 30rpx 0rpx;
}

.content {
  margin: 0rpx auto;
  width: 660rpx;
  height: 110rpx;
  padding: 15rpx 50rpx;
  font-size: 26rpx;
  border-radius: 15rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #575757;
  background-color: #ccc;
  line-height: 36rpx;
}
.explain {
  width: 750rpx;
  font-size: 40rpx;
  color: #000;
  padding: 25rpx 46rpx;
  background: #ffffff;
}
</style>