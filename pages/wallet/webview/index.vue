  <template>
  <view v-if="show">
    <view>
      <web-view :src="url">
      </web-view>
    </view>
  </view>
</template>
<script>
import __config from 'config/env';
export default {
  data () {
    return {
      url: "",
      title: '',
      show: true
    }
  },
  onLoad (val) {
    // #ifdef APP
    this.show = false;
    // #endif
    // this.url = val.url
    //顶部tabBar样式
    uni.setNavigationBarColor({
      frontColor: "#000000", // 必写项
      backgroundColor: "#F2F2F2", // 必写项
    });
    // 设置当前的title 如果外链中有的话将被覆盖
    if (this.isNotEmpty(val.title)) {
      this.setTitle(val.title);
    };
    //type 1 2 3 4 5对应相关协议
    if (val.type) {
      let type = val.type
      let agreement = __config.walletAgreement
      console.log("agreement", agreement);
      if (type == "1") {
        //账户信息即时通
        this.url = agreement
      } else if (type == "2") {
        //授权书
        this.url = agreement.replace("wallet", "wallet2")
      } else if (type == "3") {
        //声明文件
        this.url = agreement.replace("wallet", "wallet3")
      } else if (type == "4") {
        //管理协议
        this.url = agreement.replace("wallet", "wallet4")
      } else if (type == "5") {
        //跨行通协议
        this.url = agreement.replace("wallet", "wallet5")
      }else {
        //民生信用卡
        this.url = "https://creditcard.cmbc.com.cn/wsv2/?enstr=AwOU43eOa%2Fbazpx2nyXAhoqExR2vVcjpBiEqJDQYG5VhkG5WkTQvm8WacjE6GnHWFDDuGdbQfI%2B0UypAOltbv2iSo9FSlJD9DXVL36HKhgxYPDIEN3amysCnEU3ZMotNLxqyVwKmfvqc%2BTJ6%2FZX5Z2Zt6%2BIW6EQH7iu9PDqcoGM%3D"
      }
      console.log("获取支付的外部链接", this.url);
      // #ifdef APP
      uni.redirectTo({
        url: `/pages/public/webview/webview?url=${this.url}&title=零钱`
      })
      // #endif
    }
  },
  methods: {
    isNotEmpty (obj) {
      if (typeof obj == undefined || obj == null || obj == "" || obj == "undefined" || obj.length == 0) {
        return false;
      } else {
        return true;
      }
    },
    // 设置title
    setTitle (title) {
      uni.setNavigationBarTitle({
        title: title
      })
    },
  }
}
</script>
