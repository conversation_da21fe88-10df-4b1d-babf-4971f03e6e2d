<template>
  <view style="background-color: #FFFFFF;">

    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">账单</block>
    </cu-custom>

    <view class="content-head">
      <picker
        mode="date"
        :value="date"
        fields="month"
        @change="bindDateChange"
      >
        <view class="">

          <view class="top-date padding-left-sm padding-right-sm">
            <!-- <view style="margin-left:15rpx;margin-right:15rpx;font-size:30rpx;color:#000000">
            本月
          </view> -->
            {{date}}
            <text class="cuIcon-unfold"></text>
          </view>
          <!-- <view class="total">
            <text>支出￥12015.4</text>
            <text>收入￥755.20</text>
          </view> -->
        </view>
      </picker>
    </view>

    <view style="margin-top:100rpx">
      <view
        class="flex bill"
        v-for="(item,index) in billList"
        :key="index"
      >
        <view style="width: 82%;">
          <view style="font-size: 30rpx;color: #000000;margin-bottom: 21rpx;">{{item.payee_op_bank_no}}</view>
          <view
            class="text-sm"
            style="color: #A2A2A2;padding-bottom: 35rpx;"
          >{{item.trans_time |format}}</view>
        </view>

        <view style="font-size: 30rpx;">
          {{item.amount}}
        </view>
      </view>
      <view :class="'cu-load ' + (loadmoreRecommendList ?'loading':'over')"></view>
    </view>

  </view>
</template>

<script>
const app = getApp();

const util = require("utils/util.js");
import __config from 'config/env';
import api from 'utils/api';
import validate from 'utils/validate';

export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,
      date: '',
      billPage: {
        yearMonth: '',//年月
        beginNumber: 1,//页码
        queryNumber: 20,//最多20条
      },
      billList: [
        // {
        //   payee_op_bank_no: '1民生银行',
        //   trans_time: '2022-11-12',
        //   amount: '1',
        // }, {
        //   payee_op_bank_no: '2民生银行',
        //   trans_time: '2022-11-1',
        //   amount: '10',
        // }, {
        //   payee_op_bank_no: '3民生银行',
        //   trans_time: '2022-11-2',
        //   amount: '11',
        // }, {
        //   payee_op_bank_no: '4民生银行',
        //   trans_time: '2022-01-12',
        //   amount: '12',
        // }, {
        //   payee_op_bank_no: '5民生银行',
        //   trans_time: '2022-01-02',
        //   amount: '100',
        // },
      ],
      loadDataTimes: 0,
      loadmoreRecommendList: true,

    };
  },

  components: {
  },

  props: {},

  onLoad (options) {
    //获取当前月份
    this.getInitDate()
    //获取电子账单明细
    this.getAcnoTrsJnlQuery(true)

  },

  onShow () {
  },

  // 页面滚动到底部的事件
  onReachBottom () {
    if (this.loadmoreRecommendList) {
      this.billPage.beginNumber = this.billPage.beginNumber + 20;
      this.getAcnoTrsJnlQuery(false);
    }
  },
  // 监听用户下拉动作，一般用于下拉刷新
  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框

    uni.hideNavigationBarLoading(); // 停止下拉动作

    uni.stopPullDownRefresh();//停止当前页面下拉刷新。
  },

  filters: {
    format (date) {
      let dateStr = date.toString();
      var hour = dateStr.substring(8, 10);

      var min = dateStr.substring(10, 12);

      var sec = dateStr.substring(12, 14);

      var yy = dateStr.substring(0, 4);

      var dd = dateStr.substring(6, 8);

      var mm = dateStr.substring(4, 6);

      var time = yy + "-" + mm + "-" + dd + " " + hour + ":" + min + ":" + sec;
      return time
    }
  },

  methods: {
    //获取当前月份
    getInitDate () {
      var date = new Date()
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      if (month / 10 < 1) month = '0' + month
      this.date = year + '-' + month
      this.billPage.yearMonth = `${year}${month}`;
    },

    bindDateChange (e) {
      this.date = e.target.value;
      this.billPage.yearMonth = e.target.value.replace("-", '');
      console.log("date", this.date, this.billPage.yearMonth);
      this.loadmoreRecommendList = true
      this.getAcnoTrsJnlQuery(true);
    },

    //获取电子账单明细  
    getAcnoTrsJnlQuery (fresh) {
      if (this.loadmoreRecommendList) {
        api.getAcnoTrsJnlQuery(JSON.stringify(this.billPage)).then((res) => {
          console.log("getAcnoTrsJnlQuery", res);
          if (res.data.total_count == '0') {
            this.billList = []
            this.loadmoreRecommendList = false;
            return;
          }
          this.loadDataTimes++;
          if (res.data.list) {
            let list = res.data.list
            if (list || list.length > 0) {
              if (fresh) this.billList = [...res.data.list];
              else this.billList = [...this.billList, ...res.data.list];
            }
            console.log("billList", this.billList);
            if (this.billList.length == res.data.total_count) {
              this.loadmoreRecommendList = false;
            }
          }
        }).catch((err) => {
          this.loadmoreRecommendList = false;
          console.log(err);
        })
      }
    },

    //重置
    refresh () {
      this.loadmoreRecommendList = true;
      this.billList = [];
      this.billPage.beginNumber = 1;
      this.getAcnoTrsJnlQuery(true);
    }

  }
};
</script>
<style scoped>
.content-head {
  position: fixed;
  width: 100%;
}
.bill {
  padding-top: 23rpx;
  padding-left: 60rpx;
  border-bottom: 1rpx solid #ededed;
}
.top-date {
  display: flex;
  /* width: 40%; */
  align-items: center;
  height: 100rpx;
  background: #efefef;
}
.total {
  width: 60%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 24rpx;
  color: #a2a2a2;
  background: #efefef;
}
</style>
