<template>
  <view>
    <cu-custom
      :bgColor="'#558927'"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">开通钱包</block>
    </cu-custom>

    <view style="position: relative;">
      <image
        style="width: 100%; height: 890rpx;"
        mode="widthFix"
        src="https://img.songlei.com/wallet/wallet-open.png"
      ></image>

      <view class="text-img">
        <image
          mode="aspectFit"
          src="https://img.songlei.com/wallet/wallet.png"
        ></image>
      </view>

      <view class="cu-list menu card-menu margin-top-xs shadow-lg radius mine">
        <view class="cu-item">
          <view
            class="text-center"
            style="margin: auto;"
          >

            <view class="text-grey user-text-df">两项权益</view>
            <view class="line"></view>

          </view>
        </view>

        <navigator
          class="cu-item "
          url=""
          hover-class="none"
        >
          <view
            class="content flex"
            style="padding: 15rpx 0rpx 15rpx 30rpx;"
          >
            <view class="margin-right">
              <image
                class="alipay"
                src="https://img.songlei.com/wallet/exempt.png"
              ></image>
            </view>

            <view>
              <view class="text-grey user-text-dd">无手续费</view>
              <view class="text-grey user-text-dc">
                充值提现等均无额外费用
              </view>
            </view>
          </view>
        </navigator>

        <navigator
          class="cu-item "
          url=""
          hover-class="none"
        >
          <view
            class="content flex"
            style="padding: 15rpx 0rpx 35rpx 30rpx;"
          >
            <view class="margin-right">
              <image
                class="alipay"
                src="https://img.songlei.com/wallet/click.png"
              ></image>
            </view>

            <view>
              <view class="text-grey user-text-dd">便携支付</view>
              <view class="text-grey user-text-dc">
                畅行卡/接送机等均可使用余额支付
              </view>
            </view>
          </view>
        </navigator>

        <view class="login_padding flex flex-direction">
          <button
            class="cu-btn btn lg"
            @tap="open"
          >立即开通</button>
        </view>
        </navigator>

      </view>

    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {

    };
  },

  methods: {
    //去开通
    open () {
      //填写个人信息
      uni.navigateTo({
        url: "/pages/wallet/personal-information/index"
      });
    }
  }

}
</script>


<style scoped>
.btn {
  margin: 30rpx 80rpx 20rpx;
  background-color: #42b24e;
  color: #ffffff;
}
.line {
  margin: auto;
  width: 60rpx;
  border-bottom: 4rpx solid #f2b52b;
}
.text-img {
  position: absolute;
  top: 30rpx;
  left: 152rpx;
  width: 445rpx;
  height: 137rpx;
}

.alipay {
  height: 97rpx;
  width: 89rpx;
}

.user-text-df {
  height: 90rpx;
  color: #258d76;
  font-size: 34rpx;
  line-height: 90rpx;
}

.user-text-dc {
  color: #9b9b9b;
  font-size: 26rpx;
}

.user-text-dd {
  color: #000000;
  font-size: 32rpx;
}

.mine {
  position: absolute;
  top: 705rpx;
  left: 25rpx;
  width: 94% !important;
  margin: auto !important;
  border-radius: 10rpx !important;
}
</style>