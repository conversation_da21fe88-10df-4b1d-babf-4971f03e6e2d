<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">选择认证方式</block>
    </cu-custom>

    <view class="cu-list menu card-menu margin-top-xs shadow-lg radius mine">
      <view class="cu-item">
        <view class="content">
          <text class="line">|</text>
          <text class="text-grey user-text-df">身份证</text>
        </view>
      </view>

      <navigator
        class="cu-item arrow"
        url=""
        hover-class="none"
      >
        <view class="content">
          <image
            class="alipay"
            src="https://img.songlei.com/wallet/alipay.png"
          ></image>
          <text class="text-grey user-text-df">支付宝</text>
        </view>
      </navigator>

      <navigator
        class="cu-item arrow"
        url=""
        hover-class="none"
      >
        <view class="content">
          <image
            class="alipay"
            src="https://img.songlei.com/wallet/eID.png"
          ></image>
          <text class="text-grey user-text-df">elD身份证电子证照</text>
        </view>
      </navigator>
    </view>

    <view
      class="cu-list menu margin-top shadow-lg radius"
      style="width:100%"
    >
      <view class="cu-item">
        <view class="content">
          <text class="line">|</text>
          <text class="text-grey user-text-df">其他</text>
        </view>
      </view>

      <navigator
        class="cu-item arrow"
        url="/pages/wallet/wallet-artificial/index"
        hover-class="none"
      >
        <view class="content">
          <image
            class="alipay"
            src="https://img.songlei.com/wallet/artificial.png"
          ></image>
          <text class="text-grey user-text-df">人工审核</text>
        </view>
      </navigator>
    </view>

  </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import util from 'utils/util'

export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
    };
  },

  components: {},
  props: {},

  onShow () {

  },

  onLoad (option) {
  },

  methods: {
  }
};
</script>

<style scoped>
.alipay {
  height: 66rpx;
  width: 66rpx;
}
.line {
  width: 8px;
  height: 34px;
  background: #6da750;
  color: #6da750;
  margin-right: 20rpx;
}
.user-text-df {
  color: #000000;
  font-size: 28rpx;
}
.mine {
  /* width: 94% !important; */
  margin: auto !important;
  border-radius: 10rpx !important;
}
</style>
