<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      bgImage="https://img.songlei.com/wallet/head-img.png"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">松鼠钱包</block>
    </cu-custom>

    <view style="position: relative; height: 435rpx;">
      <image
        src="https://img.songlei.com/wallet/bimg.png"
        mode="widthFix"
        style="width:100%"
      >
      </image>

      <view style="position: absolute;width: 100%;top: 15rpx;">
        <view class="cu-list menu card-menu margin-top-xs all-orders">
          <view
            class="cu-list grid col-5 no-border"
            style="padding: 0rpx"
          >
            <view class="cu-item">
              <navigator
                url="/pages/wallet/my-balance/index"
                hover-class="none"
              >
                <view>
                  <image
                    src="https://img.songlei.com/wallet/balance.png"
                    style="width:82rpx;height: 82rpx;"
                  >
                  </image>
                </view>

                <view>
                  <text style="font-size: 24rpx;color: #000000;margin-top: 0rpx;">余额{{availableBalance ||'0'}}</text>
                </view>

                <view style="font-size: 20rpx;color: #999999;">我的资产</view>
              </navigator>
            </view>

            <view class="cu-item">
              <navigator
                url="/pages/coupon/coupon-user-list/index"
                hover-class="none"
              >
                <view>
                  <image
                    src="https://img.songlei.com/wallet/coupon.png"
                    style="width:82rpx;height: 82rpx;"
                  >
                  </image>
                </view>

                <view>
                  <text style="font-size: 24rpx;color: #000000;margin-top: 0rpx;">优惠券{{ userInfo.couponNum ||'0' }}</text>
                </view>

                <view style="font-size: 20rpx;color: #999999;">下单立省</view>
              </navigator>
            </view>

            <view class="cu-item">
              <navigator
                url="/pages/user/user-integral-shop/index"
                hover-class="none"
              >
                <view>
                  <image
                    src="https://img.songlei.com/wallet/integral.png"
                    style="width:82rpx;height: 82rpx;"
                  >
                  </image>
                </view>

                <view>
                  <text style="font-size: 24rpx;color: #000000;margin-top: 0rpx;">积分{{ userInfo.pointsCurrent||'0' }}</text>
                </view>

                <view style="font-size: 20rpx;color: #999999;">签到领积分</view>
              </navigator>
            </view>

            <view class="cu-item">
              <navigator
                url="/pages/wallet/bill/index"
                hover-class="none"
              >
                <view>
                  <image
                    src="https://img.songlei.com/wallet/bill.png"
                    style="width:82rpx;height: 82rpx;"
                  >
                  </image>
                </view>

                <view>
                  <text style="font-size: 24rpx;color: #000000;margin-top: 0rpx;">账单</text>
                </view>

                <view style="font-size: 20rpx;color: #999999;">消费明细</view>
              </navigator>
            </view>

            <view class="cu-item">
              <navigator
                url="/pages/wallet/upload-bank-card/index"
                hover-class="none"
              >
                <view>
                  <image
                    src="https://img.songlei.com/wallet/bank.png"
                    style="width:82rpx;height: 82rpx;"
                  >
                  </image>
                </view>

                <view>
                  <text style="font-size: 24rpx;color: #000000;margin-top: 0rpx;">银行卡1张</text>
                </view>

                <view style="font-size: 20rpx;color: #999999;">绑定的银行卡</view>
              </navigator>
            </view>
          </view>
        </view>
      </view>

      <view
        class="text-center"
        style="position: absolute;width: 100%;top: 230rpx;"
        v-if="wallet && wallet.linkUrl"
      >
        <navigator
          :url="wallet.linkUrl"
          hover-class="none"
        >
          <image
            :src="wallet.imgUrl | formatImg750"
            style="width: 710rpx;height: 219rpx;"
          >
          </image>
        </navigator>
      </view>
    </view>

    <!-- <view
      class="cu-list menu card-menu margin-top-xs shadow-lg radius mine"
      style="margin-top:40rpx"
    >

      <view
        class="margin-bottom-sm"
        style="font-size: 31rpx;font-weight: 400;color: #000000;"
      >
        能省
      </view>

      <navigator
        class="cu-item arrow"
        style="border-top-left-radius: 20rpx;border-top-right-radius: 20rpx;"
        url="/"
        hover-class="none"
      >
        <view
          class="content"
          style="flex: none;"
        >
          <image
            src="https://img.songlei.com/wallet/lcon-2.png"
            style="width: 27rpx;height: 27rpx;"
          >
          </image>

          <text
            class="user-text-df margin-left-xs"
            style="font-size: 31rpx;color: #000000;"
          >288元见面礼
          </text>

          <text class="lcon-1">
            免费领
          </text>
        </view>

        <view
          class="content"
          style="text-align: right;"
        >
          <text
            class="text-grey user-text-df"
            style="font-size: 24rpx;"
          >新人专属福利</text>
        </view>
      </navigator>

      <navigator
        class="cu-item arrow"
        url="/"
        hover-class="none"
      >
        <view
          class="content"
          style="flex: none;"
        >

          <image
            src="https://img.songlei.com/wallet/lcon-1.png"
            style="width: 27rpx;height: 27rpx;"
          >
          </image>

          <text
            class="user-text-df margin-left-xs"
            style="font-size: 31rpx;color: #000000;"
          >购物立减26元
          </text>

          <text class="lcon-1">
            速领
          </text>

        </view>

        <view
          class="content"
          style="text-align: right;"
        >
          <text
            class="text-grey user-text-df"
            style="font-size: 24rpx;"
          >财富甄选好礼物
          </text>
        </view>
      </navigator>
    </view> -->

    <recommendComponents canLoad />

  </view>
</template>

<script>
const app = getApp();
import recommendComponents from "components/recommend-components/index";
import api from 'utils/api'
import util from 'utils/util'

export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      availableBalance: '',//余额
      userInfo: {},//用户信息
      wallet: {},//广告地址和跳转
    };
  },

  components: {
    recommendComponents
  },

  props: {},

  onShow () {

  },

  onLoad (option) {
    //获取余额
    this.getAccountQuery()
    //获取用户信息
    this.userInfoGet()
    //广告
    this.advertisement("WALLET_PAGES")
  },

  methods: {
    //广告
    advertisement (id) {
      //个人中心banner
      api.advertisementinfo({
        bigClass: id
      }).then(res => {
        console.log("wallet===>", res.data);
        this.wallet = res.data;
      });

    },

    //获取电子账户信息
    getAccountQuery () {
      api.getAccountQuery().then((res) => {
        console.log("res", res);
        if (res.data) {
          this.availableBalance = res.data.available_balance
        }

      }).catch((err) => {
        console.log("err", err);
      })
    },

    //获取用户积分和劵
    userInfoGet () {
      api.userInfoGet().then(res => {
        this.userInfo = res.data;
        console.log("this.userInfo", this.userInfo);
      });
    }

  }
};
</script>

<style scoped>
.lcon-1 {
  width: 72rpx;
  height: 28rpx;
  font-size: 18rpx;
  line-height: 28rpx;
  margin-left: 10rpx;
  text-align: center;
  color: #ffffff;
  background-color: red;
  display: inline-block;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 15rpx;
  border-bottom-right-radius: 15rpx;
}
</style>
