<template>
  <view style="background-color: #FFFFFF;">
    <!-- #ifndef MP -->
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">{{type=='1'?"充值":"提现"}}</block>
    </cu-custom>
    <!-- #endif -->

    <view class="margin-top-xl">
      <web-view
        :src="url"
        :webview-styles="webviewStyles"
      ></web-view>
    </view>

  </view>
</template>

<script>

const app = getApp();

const util = require("utils/util.js");
import __config from 'config/env';
import api from 'utils/api';
import validate from 'utils/validate';


const clientType = "MA"; //客户端类型小程序
// const appId = uni.getAccountInfoSync().miniProgram.appId; //小程序appId
// const thirdSession = uni.getStorageSync("third_session") ?
//   uni.getStorageSync("third_session") :
//   "";

export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,
      randJnlNo: '',//随机数流水号
      random: '',//随机数
      type: '1',//1是充值 2是提现
      url: "",//H5地址链接
      form: {},//存用户信息
      webviewStyles: {
        progress: {
          color: "#FF3333",
        },
      },
      appId: uni.getAccountInfoSync().miniProgram.appId, //小程序appId
      thirdSession: uni.getStorageSync("third_session") ?
        uni.getStorageSync("third_session") :
        "",
    };
  },

  components: {

  },
  props: {},

  onLoad (options) {
    if (uni.getStorageSync('USER_WALLET_INFO') != null && uni.getStorageSync('USER_WALLET_INFO').length == 0) {

    }
    let usreWalletInfo = uni.getStorageSync('USER_WALLET_INFO');

    let user = usreWalletInfo
    // this.form.cifName = usreWalletInfo.cifName

    //设置个人信息
    console.log("user", user);
    if (user) {
      this.form = JSON.stringify(user)
    }

    //获取随机数
    this.getRandomNumber()

    if (options) {
      if (options.type) {
        this.type = options.type
      }
    }


    //顶部tabBar样式
    uni.setNavigationBarColor({
      frontColor: "#000000", // 必写项
      backgroundColor: "#F2F2F2", // 必写项
    });
    //顶部tabBar
    uni.setNavigationBarTitle({
      title: this.type == '1' ? "充值" : "提现",
    });
  },

  onShow () {
  },

  methods: {
    toLogin () {
      uni.reLaunch({
        url: '/pages/login/index'
      });
      return
    },

    //向H5传参
    postUrl () {
      // let fromUrl = "http://192.168.50.186:8002/";
      let fromUrl = __config.walletPayUrl
      console.log("============", fromUrl);
      if (!this.thirdSession) {
        util.backLoginPage();
        return;
      }
      if (JSON.stringify(this.form) == "{}") {
        console.log("********");
        this.getAccountQuery()
      } else {
        console.log("fromUrl========222222==========", fromUrl)
        fromUrl += `?type=${this.type}&data=${JSON.stringify(this.form)}&randJnlNo=${this.randJnlNo}&random=${this.random}&client_type=${clientType}&app_id=${this.appId}&third_session=${this.thirdSession}&url=${__config.basePath}`;
        console.log("===fromUrl========", fromUrl);
        this.url = fromUrl;
      }

    },

    //获取随机数 需要通过随机数生成加密 密码加密密文传给后端
    getRandomNumber () {
      api.getRandomNumber().then((res) => {
        console.log("getRandomNumber", res);
        if (res.code == '0') {
          let data = res.data
          this.randJnlNo = data.rand_jnl_no
          this.random = data.random
          this.postUrl()
        } else {

        }
      }).catch(() => {

      })
    },
    //获取电子账户信息
    getAccountQuery () {
      // let fromUrl = "http://192.168.50.186:8002/";
      let fromUrl = __config.walletPayUrl
      api.getAccountQuery().then((res) => {
        console.log("getAccountQuery", res);
        if (res.data) {
          this.form.availableBalance = res.data.available_balance
          this.form.mobilePhone = res.data.mobile_phone
          this.form.tAcNo = res.data.t_ac_no
          this.form.cifName = res.data.cif_name
          if (!this.thirdSession) {
            util.backLoginPage();
            return;
          }
          fromUrl += `?type=${this.type}&data=${JSON.stringify(this.form)}&randJnlNo=${this.randJnlNo}&random=${this.random}&client_type=${clientType}&app_id=${this.appId}&third_session=${this.thirdSession}&url=${__config.basePath}`;
          console.log("===fromUrl====1111111====", fromUrl);
          this.url = fromUrl;
        }
      }).catch((err) => {
        console.log("err", err);
      })
    },

  }
};
</script>
<style scoped>
</style>
