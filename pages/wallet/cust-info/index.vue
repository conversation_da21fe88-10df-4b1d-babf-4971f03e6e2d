<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">账户升级</block>
    </cu-custom>

    <view class="explain">
      <view> 补充个人信息</view>
      <view style="font-size: 22rpx;">应监管部门要求，请完善反洗钱信息</view>
    </view>

    <form @submit="regSub">
      <view style="background: #FFFFFF;">
        <view
          class="cu-form-group phone_margin"
          style="margin-top: 0rpx;"
        >
          <view
            class="title"
            style="width: 24%;"
          >区/县代码</view>
          <!-- #ifndef H5 || APP-PLUS || MP-ALIPAY -->
          <picker
            mode="multiSelector"
            @change="change"
            @columnchange="columnChange"
            :value="idx"
            :range="list"
            range-key='addr_name'
            style="padding-right: 0rpx;"
          >
            <view
              class="picker input-border"
              style="text-align: left;line-height: 70rpx;"
            >
              {{ list[0][idx[0]].addr_name||''}}，
              {{ list[1][idx[1]].addr_name||''}}，
              {{ list[2][idx[2]].addr_name||''}}
            </view>
          </picker>
          <!-- #endif -->
        </view>

        <view
          class="cu-form-group password_margin border-none"
          style="height: 210rpx;"
        >
          <view
            class="title"
            style="width: 24%;"
          >
            详细居住
            地址
          </view>
          <textarea
            class="addr"
            placeholder="请输入详细居住地址"
            maxlength="100"
            name="street"
            v-model="form.street"
          ></textarea>
        </view>

        <view class="cu-form-group password_margin border-none">
          <view
            class="title"
            style="width: 24%;"
          >职 业</view>

          <!-- #ifndef H5 || APP-PLUS || MP-ALIPAY -->
          <picker
            mode="multiSelector"
            @change="change2"
            @columnchange="columnChange2"
            :value="ido"
            :range="octList"
            range-key='occupation'
            style="padding-right: 0rpx;"
          >
            <view
              class="picker input-border"
              style="text-align: left;line-height: 70rpx;"
            >
              {{ octList[0][ido[0]].occupation||''}}，
              {{ octList[1][ido[1]].occupation||''}}

            </view>
          </picker>
          <!-- #endif -->

        </view>

        <view class="cu-form-group phone_margin border-none">
          <view
            class="title"
            style="width: 24%;"
          >工作单位</view>
          <input
            class="input-border"
            placeholder="请输入工作单位"
            style="padding-left: 30rpx;"
            name="company"
            v-model="form.company"
          ></input>
        </view>

        <view class="login_padding flex flex-direction">
          <button
            class="cu-btn margin-tb-sm lg"
            :class="'bg-'+theme.backgroundColor"
            form-type="submit"
          >下一步</button>
        </view>
      </view>
    </form>
  </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import util from 'utils/util'

export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      form: {},
      addrList: [],//地址集合
      occupationList: [],//职业集合
      filterData: {//过滤后的职业
        country: [],//国家
        province: [],
        city: [],
        area: [],
        category: [],//大类
        middle: [],//中类
      },
      type: '1',//1地址，2职业
      ido: [0, 0],//职业多列选择结果的索引号列表
      octList: [],//用于渲染职业名称
      newOtcList: [
        [
          {
            occupation: "请选择大类职业",
            oct_id: "0",
            type: "0",
          }
        ], [
          {
            occupation: "请选择中类职业",
            oct_id: "",
            type: "1",
            parent_oct_id: "0"
          }
        ]
      ],//集成后的职业数据列表
      idx: [0, 0, 0],  // 地址多列选择结果的索引号列表
      list: [],       // 用于渲染地址名称
      newAddrList: [//集成后的地址数据列表
        [
          {
            addr_code: "0",
            addr_level: "2",
            addr_name: "请选择省",
            parent_addr_code: "100000",
          }
        ], [
          {
            addr_code: "0",
            addr_level: "3",
            addr_name: "请选择市",
            parent_addr_code: "0",
          }],
        [
          {
            addr_code: "",
            addr_level: "4",
            addr_name: "请选择区",
            parent_addr_code: "0",
          }]]
    };
  },

  onLoad (options) {
    this.getAddrList()
    this.getOccupationList()
    // this.updatelist(0, 0)  // 初始化渲染

  },
  onShow () {
  },

  methods: {

    // 确定picker选择结果时
    change (e) {
      let idx = e.target.value
      let arr = this.list
      this.idx = idx
      this.form.districtCode = arr[2][idx[2]].addr_code
      console.log('你选择的结果地址是：', arr[0][idx[0]].addr_name, arr[1][idx[1]].addr_name, arr[2][idx[2]].addr_name)
      console.log('你选择的结果地址addr_code是：', arr[0][idx[0]].addr_code, arr[1][idx[1]].addr_code, arr[2][idx[2]].addr_code)
    },
    // 确定picker选择结果时
    change2 (e) {
      let idx = e.target.value
      let arr = this.octList
      this.ido = idx
      this.form.occupation = arr[1][idx[1]].oct_id
      console.log('你选择的结果职业oct_id是：', arr[0][idx[0]].oct_id, arr[1][idx[1]].oct_id)
      console.log('你选择的结果职业是：', arr[0][idx[0]].occupation, arr[1][idx[1]].occupation)

    },
    // picker的列发生变化时
    columnChange (e) {
      // column列索引（0-第一列）  value是列中数组索引
      this.updatelist(parseInt(e.target.column), parseInt(e.target.value), '1')
    },

    // picker的列发生变化时
    columnChange2 (e) {
      // column列索引（0-第一列）  value是列中数组索引
      this.updatelist(parseInt(e.target.column), parseInt(e.target.value), '2')
    },

    // 用于更新picker视图的方法封装
    updatelist (col, idx, type) {
      //1是地址 2是职业
      if (type == '1') {
        console.log("this.newAddrList", this.newAddrList[0], this.newAddrList[1], this.newAddrList[2]);
        let list = this.list  // 视图渲染
        if (this.newAddrList[0] != 'undefined' && this.newAddrList[1] != 'undefined' && this.newAddrList[2] != 'undefined') {
          list[0] = this.newAddrList[0]      // picker的第一列数据
          // 当第一列变化时
          if (col == 0) {
            // 更新第二列的数据
            list[1] = this.newAddrList[1].filter(ele => ele.parent_addr_code == list[0][idx].addr_code)
            // 更新第三列的数据
            list[2] = this.newAddrList[2].filter(ele => ele.parent_addr_code == list[1][0].addr_code)
            this.idx = [idx, 0, 0]
          }
          // 当第二列变化时
          if (col == 1) {
            // 只用更新第三列数据
            list[2] = this.newAddrList[2].filter(ele => ele.parent_addr_code == list[1][idx].addr_code)
            this.idx = [this.idx[0], idx, 0]
          }
          // 当第三列变化时，不用考虑
          // 更新list，更新picker视图
          this.list = list
        }
      }

      if (type == '2') {
        let newList = this.octList  // 视图渲染
        if (this.newOtcList[0] != 'undefined' && this.newOtcList[1] != 'undefined') {
          newList[0] = this.newOtcList[0]      // picker的第一列数据
          // newList[1] = this.newOtcList[1]      // picker的第er列数据
          // 当第一列变化时
          if (col == 0) {
            // 更新第二列的数据
            console.log("this.newOtcList[1]", this.newOtcList[1], newList[0]);
            newList[1] = this.newOtcList[1].filter(ele => ele.parent_oct_id == newList[0][idx].oct_id)
            console.log("=22==第一列变化", col, idx, newList[1]);
            // 更新第三列的数据
            // newList[2] = this.newOtcList[2].filter(ele => ele.parent_addr_code == newList[1][0].addr_code)
            this.ido = [idx, 0, 0]
          }
          // 当第二列变化时
          // if (col == 1) {
          //   // 只用更新第三列数据
          //   newList[0] = this.newOtcList[0].filter(ele => ele.oct_id == newList[1][idx].parent_oct_id)
          //   console.log("=22==第二列变化", col, idx, newList[0]);

          //   this.ido = [this.ido[0], idx, 0]
          // }
          // 当第三列变化时，不用考虑
          // 更新list，更新picker视图
          this.octList = newList
        }
      }
    },

    //过滤数据
    filterList (arr, type) {
      // let country,province,city,area
      //1 地址 2 职业
      if (type == '1') {
        arr.forEach((item, index) => {
          // console.log("item", item, index);

          //1 国家 2 省份 3 市 4 区县
          if (item.addr_level == '1') {
            this.filterData.country.push(item)
          } else if (item.addr_level == '2') {
            this.filterData.province.push(item)
          } else if (item.addr_level == '3') {
            this.filterData.city.push(item)
          } else if (item.addr_level == '4') {
            this.filterData.area.push(item)
          }
        });
        this.newAddrList[0].push(...this.filterData.province);
        this.newAddrList[1].push(...this.filterData.city)
        this.newAddrList[2].push(...this.filterData.area)
        console.log("1111111");
        console.log("newAddrList==>", this.newAddrList);
      }

      if (type == '2') {
        arr.forEach((item, index) => {
          //0 大类 1 中类
          if (item.type == '0') {
            this.filterData.category.push(item)
          } else if (item.type == '1') {
            this.filterData.middle.push(item)
          }
        });
        this.newOtcList[0].push(...this.filterData.category);
        this.newOtcList[1].push(...this.filterData.middle)
        console.log("2222");
        console.log("newOtcList==>", this.newOtcList);
      }
      console.log("========>", type, this.filterData);
      this.updatelist(0, 0, type)  // 初始化渲染
    },

    //查詢職業列表
    getOccupationList () {
      api.getOccupationList().then((res) => {
        console.log("getOccupationList==>", res, res.data.list);
        if (res.code == '0') {
          this.occupationList = res.data.list
          if (this.occupationList.length > 0) {
            this.filterList(this.occupationList, '2')
          }
        }
      }).catch((err) => { })
    },

    //查詢地址列表
    getAddrList () {
      api.getAddrList().then((res) => {
        // console.log("getAddrList==>", res, res.data.list);
        if (res.code == '0') {
          this.addrList = res.data.list
          if (this.addrList.length > 0) {
            this.filterList(this.addrList, '1')
          }
        }
      }).catch((err) => {
      })
    },

    //提交
    regSub (e) {
      console.log(e);
      let that = this
      if (!that.form.districtCode) {
        uni.showToast({
          title: '请选择区/县',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.street) {
        uni.showToast({
          title: '请输入详细居住地址',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.occupation) {
        uni.showToast({
          title: '请选择职业',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.company) {
        uni.showToast({
          title: '请输入工作单位',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      api.custInfoMainten(this.form).then((res) => {
        console.log("res", res);
        if (res.code == '0') {
          uni.navigateTo({
            url: `/pages/wallet/wallet-password/index?type=5`
          });
        }
      }).catch((err) => {
        console.log(err);
      })

    },

  }

}
</script>
<style>
page {
  background: #ffffff;
}
</style>
<style scoped>
.cu-form-group picker::after {
  right: 0rpx;
  line-height: 70rpx;
}
.addr {
  width: 545rpx;
  height: 165rpx;
  background: #f9f9f9;
  border: 1rpx solid #eaeaea;
  border-radius: 9rpx;
}
.input-border {
  padding-left: 15px;
  background: #f9f9f9;
  border: 1rpx solid #eaeaea;
  border-radius: 9rpx;
  width: 545rpx;
  height: 70rpx;
}
.border-none {
  border: none;
}
.text {
  font-weight: bold;
  color: #000000;
}

.login_padding {
  padding: 80rpx 30rpx 80rpx;
}
.phone_margin {
  margin: 15rpx 30rpx 0rpx;
  border-radius: 10rpx;
}
.password_margin {
  margin: 0rpx 30rpx;
  border-radius: 10rpx;
}

.explain {
  margin: 15rpx 0rpx 0rpx 50rpx;
  /* margin-left: 50rpx; */
  width: 450rpx;
  height: 100rpx;
  padding: 15rpx 50rpx;
  /* font-size: 26rpx; */
  border-radius: 15rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #575757;
  background-color: #ccc;
  line-height: 36rpx;
}
</style>

