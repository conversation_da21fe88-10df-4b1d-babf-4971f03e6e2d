<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">银行卡</block>
    </cu-custom>

    <view class="text-center card-box">
      <image
        src="https://img.songlei.com/wallet/cardbg.png"
        style="height: 195rpx;width: 681rpx;border-radius: 15rpx;"
      ></image>

      <view
        class="card-text"
        style="font-size: 32rpx;color: #FFFFFF;"
      >
        {{bankName||''}}
      </view>

      <view
        class="card-type"
        style="font-size: 28rpx;color: #FFFFFF;opacity: 0.5;"
      >
        {{cardType}}
      </view>

      <view class="caed-num">
        {{tAcNo}}
      </view>
    </view>

    <view
      class="cu-form-group item"
      style="background:none ;"
      @tap="toUpgrade()"
    >
      <view
        class="title"
        style="font-size: 24rpx;font-weight: 700;"
      >去升级账户</view>
      <view
        class="text-right"
        style="color: #ACACAC;"
      ><text class="cuIcon-right"></text></view>
    </view>

    <!-- <view class="explain">
      填写个人信息
      <view style="font-size: 30rpx;color: #999999;">
        上传银行卡
      </view>
    </view> -->

    <!-- <form @submit="regSub">
      <view style="background: #FFFFFF;">

        <view
          v-if="card"
          class="text-center"
        >
          <view class="idAPath-text">
            <view class="head-text">
              银行卡正面图片

            </view>

            <view
              class="modify"
              @tap="delImg(1)"
            >
              修改
            </view>
          </view>
          <image
            @click="handlePreviewImage(1)"
            :src="card"
            style="height: 333rpx;width: 550rpx;border-radius: 15rpx;"
          ></image>
        </view>

        <view
          class="headId"
          v-if="!card"
        >
          <view class="padding-top">
            <view @tap="chooseImage(1)">
              <view class="text-center">
                <image
                  class="Icon"
                  src="https://img.songlei.com/wallet/Icon.png"
                ></image>
              </view>

              <view class="headId-explain">上传您的银行卡照片</view>
            </view>
          </view>
        </view>

        <view class="login_padding flex flex-direction">
          <button
            class="cu-btn margin-tb-sm lg"
            :class="'bg-'+theme.backgroundColor"
            form-type="submit"
          >下一步</button>
        </view>
      </view>
    </form> -->

  </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import __config from 'config/env';
import util from 'utils/util';
// import formData from '@/public/common/formData.js';


export default {
  components: {

  },

  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      form: {},
      cardUrl: '',// 银行卡地址
      code: 2,//认证码: 1是失败 2是成功
      msg: '',//认证返回信息
      bankName: '',//银行卡名称
      cardType: '',//银行卡类别
      tAcNo: '',//卡号
      custInfoCheck: false//true 需要上传信息
    };
  },

  onShow () {
    //查询是否需要补充个人信息
    this.getCustInfoCheck()
  },
  onLoad (options) {
    const userWalletInfo = uni.getStorageSync('USER_WALLET_INFO');

    //认证
    this.isAuthentication();

    if (userWalletInfo) {
      console.log("userWalletInfo", userWalletInfo);
      this.form.idNo = userWalletInfo.idNo
      this.form.tAcNo = userWalletInfo.tAcNo
      if (this.form.tAcNo) {
        let reg = /^(\d{4})\d+(\d{4})$/;
        this.tAcNo = this.form.tAcNo.replace(reg, "$1 **** **** $2")
        this.getCardBin(this.form.tAcNo)
      }
    } else {
      this.getAccountQuery()
    }
  },

  methods: {
    //去升级账户
    toUpgrade () {
      let user = uni.getStorageSync('Account')
      // console.log("user", user);
      // 1：三类户 2：二类户
      if (user.openType == '1') {
        //1.判断用户是否认证 没有认证去认证
        if (uni.getStorageSync('Authentication') == null) {
          uni.navigateTo({
            url: "/pages/wallet/wallet-artificial/index"
          });
        } else if (this.custInfoCheck) {
          //认证完成和已补充信息
          uni.navigateTo({
            url: `/pages/wallet/wallet-password/index?type=5`
          });
        } else {
          uni.navigateTo({
            url: "/pages/wallet/cust-info/index"
          });
        }
      } else {
        uni.showToast({
          title: '当前账户等级已是最高等级，无法继续升级！',
          icon: 'none',
          duration: 3000
        });
        return;
      }
    },
    //用户是否认证 返回有数据代表已经认证，没数据代表没认证
    isAuthentication () {
      api.isAuthentication().then((res) => {
        console.log("isAuthentication", res);
        let Authentication = res.data
        uni.setStorageSync('Authentication', Authentication);
      })
    },

    //2.查询判断是否上传银行卡或者不需要上传 再查询是否需要补全个人职业信息，没有则跳转到补全个人职业信息页面
    //用户职业：occupation_ flag Y:需补充 N:无需补充 
    //工作单位：company_flag Y:需补充 N:无需补充 
    //联系地址：addr_flag Y:需补充 N:无需补充
    getCustInfoCheck () {
      api.getCustInfoCheck().then(res => {
        console.log("getCustInfoCheck", res);
        let userInfo = res.data
        if (userInfo) {
          //信息需要补充的，跳转到信息页 只展示需要补充填写的
          if (userInfo.occupation_flag != 'N' && userInfo.company_flag != 'N' && userInfo.addr_flag != 'N') {
            this.custInfoCheck = false
          } else {
            this.custInfoCheck = true
            // //客户信息页
            // uni.navigateTo({
            //   url: '/pages/wallet/cust-info/index'
            // });
          }
          console.log("this.custInfoCheck", this.custInfoCheck);
        }
      }).catch(e => {

      });
    },

    //获取电子账户信息 余额
    getAccountQuery () {
      api.getAccountQuery().then((res) => {
        console.log("res", res);
        if (res.data) {
          this.form.tAcNo = res.data.t_ac_no
          this.getCardBin(this.form.tAcNo)
          if (this.form.tAcNo) {
            let reg = /^(\d{4})\d+(\d{4})$/;
            this.tAcNo = this.form.tAcNo.replace(reg, "$1 **** **** $2")
          }
        }
      }).catch((err) => {
        console.log("err", err);
      })
    },
    //银行卡名称查询
    getCardBin (tAcNo) {
      api.getCardBin(tAcNo).then((res) => {
        console.log('getCardBin', res);
        if (res.code == '0') {
          this.bankName = res.data.bank_name
          let cardType = res.data.card_type
          if (cardType == '1') {
            this.cardType = "民生信用卡"
          } else if (cardType == '2') {
            this.cardType = "他行借记卡"
          } else if (cardType == '3') {
            this.cardType = "他行信用卡"
          } else if (cardType == '0') {
            this.cardType = "民生借记卡"
          } else {
            this.cardType = "储蓄卡"
          }
        }
      }).catch((err) => {
        console.log("err", err);
      })
    },

    //图片预览
    handlePreviewImage (index) {
      // 预览图片
      uni.previewImage({
        urls: this.cardUrl.split(',')
      });
    },

    //选择图片上传
    chooseImage (index) {
      uni.chooseImage({
        success: (chooseImageRes) => {
          const tempFilePaths = chooseImageRes.tempFilePaths;
          let _url = __config.basePath + '/mallapi/file/upload';
          let that = this
          uni.showLoading({
            title: '上传中'
          });
          console.log("tempFilePaths", tempFilePaths);
          this.onload(tempFilePaths[0]).then(res => {
            // let link = res.link
            that.cardUrl = tempFilePaths.toString()
          })
        }
      });
    },

    onload (tempFilePaths) {
      let _url = __config.basePath + '/mallapi/file/upload';
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          header: {
            'client-type': 'MA', //客户端类型小程序
            'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
            'third-session': uni.getStorageSync('third_session') ? uni
              .getStorageSync('third_session') : '',
          },
          url: _url,
          filePath: tempFilePaths,
          name: 'file',
          formData: {
            'fileType': 'image',
            'dir': 'goods/appraises/'
          },
          success: (uploadFileRes) => {
            if (uploadFileRes.statusCode == '200') {
              console.log("uploadFileRes---", uploadFileRes);
              resolve(JSON.parse(uploadFileRes.data))
            } else {
              uni.showModal({
                title: '提示',
                content: '上传失败：' + uploadFileRes.data,
                success (res) { }
              });
            }

          },
          complete: () => {
            uni.hideLoading()
          }
        });
      })
    },

    //删除图片
    delImg (index) {
      let that = this
      that.cardUrl = ''
    },

    //提交
    regSub (e) {
      let that = this
      if (!that.cardUrl) {
        uni.showToast({
          title: '请上传您的银行卡照片',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      const FormData = require('@/public/common/formData.js')
      let fd = new FormData()
      let form = JSON.stringify(that.form.idNo)
      //需要传递的参数
      // fd.append('TAcNo', form)
      //模拟银行卡
      fd.append('tAcNo', '6215169697304881756')
      fd.appendFile('cardUrl', that.cardUrl)

      let data = fd.getData();
      uni.showLoading({
        title: '提交银行卡照片中...'
      });
      uni.request({
        url: __config.basePath + '/mallapi/purse/purse-cardImgUpload',
        method: 'POST',
        header: {
          'client-type': 'MA', //客户端类型小程序
          'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
          'third-session': uni.getStorageSync('third_session') ? uni
            .getStorageSync('third_session') : '',
          'Content-type': data.contentType
        },
        data: data.buffer,
        success: (res) => {
          uni.hideLoading();
          console.log(res.data);
          if (res.data) {
            if (res.data.code == '1') {
              //错误提示
              uni.showModal({
                title: '提示',
                content: res.data.msg,
                success (res) {
                }
              });
            } else {
              //提交银行卡照片成功 查询用户信息是否完整，是否需要填写用户职业

            }
            that.hideDialog = true
          }
        }
      });

    },


  }

}
</script>

<style scoped>
page {
  background: #ffffff;
}
.item {
  padding: 26rpx 0;
  min-height: 114rpx;
  margin: 0 26rpx;
  border-bottom: 1rpx solid #ccc;
}
.caed-num {
  position: absolute;
  top: 157rpx;
  left: 150rpx;
  font-weight: bold;
  color: #ffffff;
  font-size: 55rpx;
}
.card-type {
  position: absolute;
  top: 108rpx;
  left: 81rpx;
}
.card-text {
  position: absolute;
  top: 67rpx;
  left: 83rpx;
}
.card-box {
  position: relative;
  padding-top: 35rpx;
}

.head-text {
  font-size: 30rpx;
  color: #000;
}
.modify {
  width: 110rpx;
  height: 44rpx;
  background: #ffffff;
  border: 1rpx solid #dadada;
  border-radius: 22rpx;
  font-size: 26rpx;
  color: #5c5c5c;
}

.idAPath-text {
  display: flex;
  justify-content: space-between;
  padding: 0rpx 30rpx 30rpx;
}

.headId-explain {
  width: 265rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #777777;
  margin: 15rpx auto;
  text-align: center;
}
.headId {
  margin: auto;
  padding: 30rpx;
  width: 539rpx;
  height: 319rpx;
  background: #f7f8fa;
  border-radius: 20rpx;
}
.head {
  width: 370rpx;
  height: 241rpx;
}
.Icon {
  width: 127rpx;
  height: 127rpx;
}

.login_padding {
  padding: 300rpx 30rpx 0rpx;
}

.content {
  margin: 0rpx auto;
  width: 660rpx;
  height: 110rpx;
  padding: 15rpx 50rpx;
  font-size: 26rpx;
  border-radius: 15rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #575757;
  background-color: #ccc;
  line-height: 36rpx;
}
.explain {
  width: 750rpx;
  font-size: 40rpx;
  color: #000;
  padding: 50rpx 46rpx;
  background: #ffffff;
}
</style>

