<template>
  <view>

    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">手机号验证</block>
    </cu-custom>

    <view class="explain">
      输入验证码
    </view>

    <form @submit="loginSub">
      <view class="cu-form-group">
        <view class="title">手机号</view>
        <input
          placeholder="请输入手机号"
          name="phone"
          v-model="form.phone"
        ></input>
      </view>

      <view class="cu-form-group">
        <view class="title">验证码</view>
        <input
          placeholder="请输入验证码"
          name="code"
          maxlength=4
          v-model="form.code"
        ></input>
        <span
          @click="getPhoneCode"
          class="cu-btn bg-gray"
          :class="'display:' + msgKey"
        >{{msgText}}</span>
      </view>

      <view class="padding flex flex-direction">
        <button
          class="cu-btn margin-tb-sm lg"
          :class="'bg-'+theme.backgroundColor"
          form-type="submit"
        >下一步</button>
      </view>
    </form>
  </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import validate from 'utils/validate'
import __config from '@/config/env';// 配置文件

const MSGINIT = "发送验证码",
  MSGSCUCCESS = "${time}秒后可重发",
  MSGTIME = 60;
export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,
      form: {},
      msgText: MSGINIT,
      msgTime: MSGTIME,
      msgKey: false,
      showPrivacyPolicy: __config.showPrivacyPolicy,
      privacyPolicyUrl: __config.privacyPolicyUrl,
      protocolUrl: __config.protocolUrl,
      checkedRead: true
    };
  },

  components: {

  },
  props: {
    reUrl: {//重定向页面
      type: String,
      default: '/pages/home/<USER>'
    }
  },

  onLoad () {

  },

  onShow () {
  },

  methods: {

    getPhoneCode () {
      if (this.msgKey) return
      if (!validate.validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      this.msgKey = true
      // api.getPhoneCode({
      //   type: '1',
      //   phone: this.form.phone
      // }).then(res => {
      //   this.msgKey = false
      //   if (res.code == '0') {
      //     uni.showToast({
      //       title: '验证码发送成功',
      //       icon: 'none',
      //       duration: 3000
      //     });
      //     this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
      //     this.msgKey = true
      //     const time = setInterval(() => {
      //       this.msgTime--
      //       this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
      //       if (this.msgTime == 0) {
      //         this.msgTime = MSGTIME
      //         this.msgText = MSGINIT
      //         this.msgKey = false
      //         clearInterval(time)
      //       }
      //     }, 1000)
      //   } else {

      //   }
      // }).catch(() => {
      //   this.msgKey = false
      // });
    },
    loginSub (e) {
      if (!validate.validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      if (!this.form.code) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      // api.loginByPhone(this.form).then(res => {
      //   let userInfo = res.data;
      //   uni.setStorageSync('third_session', userInfo.thirdSession);
      //   uni.setStorageSync('user_info', userInfo);
      //   //登录完成跳到首页
      //   uni.reLaunch({
      //     url: this.reUrl ? decodeURIComponent(this.reUrl) : '/pages/home/<USER>'
      //   });
      //   //获取购物车数量
      //   app.shoppingCartCount()
      // });
    }
  }
};
</script>
<style scoped>
.explain {
  width: 750rpx;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 40rpx;
  color: #000;
  padding-left: 46rpx;
  background: #ffffff;
}
.text_top {
  margin-top: 80rpx;
}
.btn_padding {
  padding: 70rpx 30rpx 0rpx;
}
</style>
