<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">设置</block>
    </cu-custom>

    <view style="background: #FFFFFF;">
      <view
        class="cu-form-group item"
        @tap.stop="toMyQrcode(3)"
      >
        <view
          class="title"
          style="font-size: 24rpx;"
        >账户解锁</view>
        <view
          class="text-right"
          style="color: #ACACAC;"
        ><text class="cuIcon-right"></text></view>
      </view>

      <view
        class="cu-form-group item"
        @tap.stop="toMyQrcode(2)"
      >
        <view
          class="title item-title"
          style="font-size: 24rpx;"
        >修改密码</view>
        <view class="text-right">
          <text
            class="cuIcon-right"
            style="color: #ACACAC; margin-left: 20rpx;"
          ></text>
        </view>
      </view>

      <view
        class="cu-form-group item"
        @tap.stop="toMyQrcode(4)"
      >
        <view
          class="title"
          style="font-size: 24rpx;"
        >注销账户</view>
        <view
          class="text-right"
          style="color: #ACACAC;"
        ><text class="cuIcon-right"></text></view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
const util = require("utils/util.js");
import api from 'utils/api'
import validate from 'utils/validate'
import __config from 'config/env';
export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      availableBalance: ''//余额
    };
  },

  onLoad (options) {
    // availableBalance 余额
    if (options.availableBalance) {
      this.availableBalance = options.availableBalance
    }
  },

  methods: {

    toMyQrcode (type) {
      if (type == "4") {
        if (this.availableBalance != '0.00') {
          uni.showToast({
            title: '当前账户余额大于0，请提现后重试',
            icon: 'none',
            duration: 3000
          });
          return;
        }
        uni.navigateTo({
          url: `/pages/wallet/wallet-password/index?type=${type}`
        });
      } else {
        uni.navigateTo({
          url: `/pages/wallet/wallet-password/index?type=${type}`
        });
      }


    }
  }
};
</script>

<style>
.item {
  padding: 26rpx 0;
  min-height: 114rpx;
  margin: 0 26rpx;
}
</style>
