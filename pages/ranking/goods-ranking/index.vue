<template>
  <view>
    <cu-custom
      bgColor="#f30c0a"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">{{scrollY>99?rankingList.name:''}}</block>
    </cu-custom>

    <scroll-view
      scroll-y
      :style="{height: `calc(100vh - ${CustomBar}px)`}"
      @scroll="handleScroll"
    
    >
      <!-- 排行榜标题 -->
      <view style="position: relative">
        <image
          mode="widthFix"
          style="width: 750rpx"
          :src="rankBgimg.imgUrl"
        >
        </image>

        <view class="ranlk-title">
          <view class="ranlk-title1">{{rankingList.name}}</view>
          <view class="ranlk-title2">{{rankingList.summary}}</view>
        </view>
      </view>
      <!-- 排行榜标题 -->

      <!-- 商品列表 -->

      <view v-if="goodsList&&goodsList.length>0">
          <goods-row :goodsListType="type" :goodsList="goodsList"></goods-row>
      <!-- 商品列表 -->
      </view>
    </scroll-view>
    
  </view>
</template>

<script>

const util = require("utils/util.js");
const app = getApp();
import api from '@/utils/api';
import goodsRow from "components/goods-row/index";
export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      userInfo: {},
      shareUser: {},//分享
      rankBgimg:{},//排行榜背景图片
      //有数统计使用
      page_title: '排行榜',
      goodsList:{},
      type:'rank',
      scrollY: 0,
      id:'',
      rankingList:{}
        }
  },

  onLoad (option) {
    if (option) {
     this.id = option.id;
     this.getGoods(option.id)
    }
   
    //分享朋友
    this.advertisement("SHARE_SIGNRECORD_INFO")
    this.advertisement("GOODS_RANK")
    // this.getUserCustAccntTot()
   
  },

  //分享朋友
  onShareAppMessage: function () {

    let shareUser = this.shareUser;
    let title = shareUser.name;
    let linkUrl = shareUser.linkUrl
    let imageUrl = shareUser.imgUrl + '-jpg_w360_q90' || '';
    const userInfo = uni.getStorageSync('user_info')
    let userCode = userInfo ? '&type=1&sharer_user_code=' + userInfo.userCode : ''
    let path = `${linkUrl}?userId=` + this.userInfo.id + userCode;
    console.log("onShareAppMessagepath", path);

    return {
      title: title ? title : null,
      path: linkUrl ? path : null,
      imageUrl: imageUrl ? imageUrl : null,
      success: function (res) {
        console.log(res.errMsg);
        uni.showToast({
          title: '分享成功'
        })
      },
      fail: function (res) { // 转发失败
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        })
      }
    };
  },

  components: {
    goodsRow
  },

  onShow () {
    // app.initPage().then(res => {
      
    // });
  },

  methods: {

     //滚动显示
     handleScroll (e) {
      this.scrollY = parseInt(e.detail.scrollTop);
      //如果距顶部小于20，就直接等于0
      if (this.scrollY < 50) {
        this.scrollY = 0
      }
      // console.log("scrollY==>", this.scrollY, "topHeight==>", this.topHeight, "CustomBar==>", this.CustomBar, this.scrollY > this.topHeight)
    },

    //分享
    advertisement (id) {
      if (id==='SHARE_SIGNRECORD_INFO') {
        api.advertisementinfo({
        bigClass: id
      }).then(res => {
        console.log("shareUser===>", res.data);
        this.shareUser = res.data;
      });
      }
      if (id==='GOODS_RANK') {
        api.advertisementinfo({
        bigClass: id
      }).then(res => {
        console.log("GOODS_RANK===>", res.data);
        this.rankBgimg = res.data;
      });
      }
      
    },

    getGoods(id) {
      api.getRanking(
        id
				).then(res => {
          console.log("goodsList===",res.data)
          this.rankingList=res.data
					let goodsList = res.data.rankSpuList;
          let Info = goodsList.filter(item=>{
            return item.goodsSpu
          })
          this.goodsList=Info.map(item => item.goodsSpu)
					
          console.log("goodsList==>",this.goodsList);
				});
			}

  }
}
</script>

<style scoped>
.ranlk-title{
  position: absolute;
  top:0;
  right: 0;
  bottom: 0;
  left: 0;
  top: 20rpx;
  text-align: center;
}
.ranlk-title1{
  font-size: 52rpx;
  color: #ffffff;
}
.ranlk-title2{
  font-size: 28rpx;
  color: #ffffff;
}
</style>
