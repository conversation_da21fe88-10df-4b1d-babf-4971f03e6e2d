import { requestApi as request } from '@/utils/api.js'
import { request as requestUpload } from '@/utils/uploadApi.js'


export const searchUpload = (cameraPic) => {
  return requestUpload({
    url: '/mallapi/imageSearch/searchImageByPic',
    filePath: cameraPic,
    name: 'img'
  })
}

export const searchUploads = (data) => {
  return request({
    url: '/mallapi/imageSearch/searchImageByUrl',
    method: 'post',
    data
  })
}

export const deletePic = (data) => {
  return request({
    url: '/mallapi/imageSearch/remove',
    method: 'post',
    data
  })
}


export const userFoot = (data) => {
  return request({
    url: '/mallapi/imageSearch/userFoot',
    method: 'get',
    data
  })
}


export const photoRecord = (data) => {
  return request({
    url: '/mallapi/imageSearch/photoRecord',
    method: 'get',
    data
  })
}

