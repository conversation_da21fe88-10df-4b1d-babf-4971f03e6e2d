<template>
  <view class="flex flex-direction camera-page">
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
    	<block slot="content">拍照</block>
    </cu-custom>
    <view class="flex-sub flex flex-direction" style="background-color: rgba(0, 0, 0, 0.5);">
      <view class="flex justify-between camera-box align-center">
        <view class="camera-aspect-bg">
          <image :src="cameraPic" mode="aspectFit" style="width: 100%;height: 100%;"></image>
        </view>
        <!-- <i class="cuIcon-close text-xxxl" style="color: #fff;"></i> -->
        <view></view>
      </view>
      <view class="flex-sub bg-white flex flex-direction">
        <scroll-view class="flex-sub" :scroll-y="true" style="background-color: #f1f1f1;">
          <template v-if="viewType == 'list' && goodsList.length > 0">
          	<goods-card ref="goodscard" :goodsList="goodsList" :otherShopInfos="otherShopInfos"></goods-card>
          </template>
          <template v-if="viewType == 'cascades' && goodsList.length > 0">
          	<goods-row @onCaetAnimation="caetShake" :showCart="true" ref="goodscard" :goodsList="goodsList" :otherShopInfos="otherShopInfos"></goods-row>
          </template>
          <recommendComponents v-if="isOver" canLoad />
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { searchUpload, searchUploads } from '@/pages/camera/api/camera.js'
import { mapState } from 'vuex'
import goodsRow from "components/goods-row/index";
import recommendComponents from "components/recommend-components/index";


export default {
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      cameraPic: '',
      list: [],
      scrollY: 0,
      topHeight: 0,
      inventoryState: '', //仅看有货||上架商品  0仅看有货  字段不传查看所有
      title: '口红',
      viewType: 'list',
      goodsList: [],
      otherShopInfos: '', // 品牌数据
      isOver: false
    }
  },
  computed: {
  	...mapState([
  		'windowWidth',
  		'HeightBar',
  		'CustomBar',
  		'menuWidth',
      'menuHeight',
  		'leftMenuWidth',
  		'pixelRatio'
  	])
  },
  components: {
  	goodsRow,
    recommendComponents
  },
  onLoad() {
    if(app.globalData.cameraPic) {
      this.cameraPic = app.globalData.cameraPic;
      this.uploadImg();
    }
  },
  methods: {
    uploadImg() {
      uni.showLoading({
        title: '查询中'
      })
      if(this.cameraPic.startsWith('https')) {
        searchUploads({ 
          picUrl: this.cameraPic
        }).then(res => {
          this.goodsList = res.data;
          this.isOver = true;
        })
      } else {
        searchUpload(this.cameraPic).then(res => {
          const dataParse = JSON.parse(res.data);
          const { code, msg, data } = dataParse;
          if(+code === 0) {
            this.goodsList = data;
          } else {
            uni.showToast({
              title: msg,
              icon: 'none'
            })
          }
          this.isOver = true;
        })
      }
      
    },
  }
}
</script>

<style scoped>
.camera-page {
  width: 100vw;
  min-height: 100vh;
}
.camera-box {
  height: 160rpx;
  padding: 0 60rpx;
}
.camera-aspect-bg {
  width: 96rpx;
  height: 106rpx;
  background-image: url('http://img.songlei.com/client/mp/camera/camera_bg.png');
  background-size: 100% 100%;
  padding: 10rpx 10rpx 20rpx;
}
.cuIcon-triangleupfill {
  height: 15rpx;
}
</style>