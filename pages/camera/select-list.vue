<template>
  <view class="flex flex-direction select-list">
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
    	<block slot="content">历史记录</block>
    </cu-custom>
    <view class="flex" style="line-height: 80rpx;background-color: #fff;margin-bottom: 20rpx;">
      <view @click="currentIndex = '0'" class="nav-item" :class="currentIndex == '0' ? 'active' : ''">我浏览过</view>
      <view @click="currentIndex = '1'" class="nav-item" :class="currentIndex == '1' ? 'active' : ''">我拍过的</view>
    </view>
    <view class="flex-sub">
      <swiper
        :current="currentIndex"
        style="height: 100%;"
        :autoplay="false"
        :interval="3000"
        :duration="600"
        @change="changeCurrent"
      >
        <swiper-item>
          <scroll-view 
            class="swiper-item" 
            :scroll-y="true" 
            :refresher-enabled="true" 
            :refresher-triggered="triggered"
            @refresherrefresh="refresherrefresh"
          >
            <view v-for="(item, name) in listObj" :key="name">
              <view style="padding: 10rpx 0;">{{ name }}</view>
              <view
                class="flex flex-wrap justify-between"
              >
                <view
                  class="image-box"
                  v-for="(items, index) in item"
                  :key="index"
                  @click="preview(items)"
                >
                  <image
                    :src="items"
                    mode="aspectFill"
                    style="width: 210rpx;height: 210rpx;"
                  ></image>
                </view>
               
                <view style="width: 210rpx;height: 0;"></view>
              </view>
            </view>
            <view class=""></view>
          </scroll-view>
        </swiper-item>
        <swiper-item>
          <scroll-view
            class="swiper-item"
            :scroll-y="true"
            :refresher-enabled="true" 
            :refresher-triggered="triggered"
            @refresherrefresh="refresherrefresh"
          >
            <template v-for="(item, name) in cameraObj">
              <view style="padding: 10rpx 0;">{{ name }}</view>
              <view class="flex flex-wrap justify-between">
                <view
                  class="image-box"
                  v-for="(items, index) in item"
                  :key="index"
                  @click="preview(items.picUrl)"
                  @longpress="longpress(items)"
                >
                  <image
                    :src="items.picUrl"
                    mode="aspectFill"
                    style="width: 210rpx;height: 210rpx;"
                  ></image>
                  
                </view>
                <view style="width: 210rpx;height: 0;"></view>
              </view>
            </template>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
    <view class="preview-img" v-if="isPreview">
      <cu-custom bgColor="#000" :hideMarchContent="true">
      	<block slot="content">预览</block>
      </cu-custom>
      <view class="flex-sub">
        <img :src="perviewImg" mode="aspectFit" alt="预览" style="width: 100%;height: 100%;">
      </view>
      <view class="footer-box">
        <view class="footer-bottom" @click="isPreview = false;">取消</view>
        <view class="footer-bottom" @click="submits">确定</view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { userFoot, photoRecord, deletePic } from '@/pages/camera/api/camera.js'
export default {
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      listObj: {},
      cameraObj: {},
      currentIndex: 0,
      isPreview: false,
      perviewImg: '',
      oneParams: {
        current: 1,
        size: 1000
      },
      twoParams: {
        current: 1,
        size: 1000
      },
      triggered: false
    }
  },
  onLoad() {
    this.userFoot();
    this.photoRecord();
  },
  methods: {
    userFoot() {
      userFoot(this.oneParams).then(res => {
        let obj = {};
        res.data.records.forEach(item => {
          if(obj[item.date]) {
            obj[item.date].push(item.picUrl);
          } else {
            obj[item.date] = [item.picUrl]
          }
        })
        this.listObj = obj;
      })
    },
    photoRecord() {
      photoRecord(this.twoParams).then(res => {
        let obj = {};
        res.data.records.forEach(item => {
          if(obj[item.date]) {
            obj[item.date].push(item);
          } else {
            obj[item.date] = [item]
          }
        })
        this.cameraObj = obj;
      })
    },
    changeCurrent(e) {
      this.currentIndex = e.detail.current;
    },
    preview(item) {
      this.perviewImg = item;
      this.isPreview = true;
    },
    submits() {
      app.globalData.cameraPic = this.perviewImg;
      uni.navigateTo({
        url: '/pages/camera/index'
      })
    },
    longpress(item) {
      uni.showModal({
        title: '提示',
        content: '确定删除该图片记录?',
        success: (res) => {
          if (res.confirm) {
            deletePic({ ids: [item.id] }).then(res => {
              uni.showToast({
                title: '删除成功'
              })
              this.photoRecord();
            })
          }
        }
      })
    },
    refresherrefresh() {
      this.triggered = true;
      this.userFoot();
      this.photoRecord();
      setTimeout(() => {
        this.triggered = false;
      }, 100)
    }
  }
}
</script>

<style scoped>
.select-list {
  width: 100vw;
  height: 100vh;
}
.nav-item {
  width: 50vw;
  text-align: center;
  position: relative;
}
.nav-item.active {
  color: #e00;
}
.nav-item.active::after {
  content: '';
  position: absolute;
  width: 60%;
  height: 5rpx;
  background-color: #e00;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.swiper-item {
  padding: 0rpx 30rpx 100rpx;
  overflow: auto;
  height: 100%;
  box-sizing: border-box;
}
.image-box {
  border: 4rpx solid #ddd;
  margin-bottom: 10rpx;
  position: relative;
}
.preview-img {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: #000;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}
.footer-box {
  box-sizing: content-box;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx 30rpx 50rpx;
  border: 1rpx solid #999;
}
.footer-bottom {
  width: 200rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30rpx;
  background-color: #e00;
  color: #fff;
  margin-left: 30rpx;
}
</style>