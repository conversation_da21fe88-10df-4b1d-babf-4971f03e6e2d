<template>

	<view  catchtouchmove='return' :style="{
		position: 'absolute',
		top: heightBar+'px',
		left: 0,
		bottom: 0,
		width: '100vw',
		height: '100vh',
		background: ' #333333'
	}">
		<template v-if="!hasBargain">
			<gal-css-animate animateName="zoomIn" duration="0.5s" timingFunction="ease-in">
				<view class="container-bg" :style="{
					height: windowHeight+'px',
					marginTop: '30%'
				}">
					<view class="help-txt-layout">
						<view class="help-txt">
							<image class="help-image" :src="bargainInfo.bargainUser.headimgUrl" mode="aspectFit">
							</image>
						</view>

						<text>求求你帮我砍一刀</text> 
					</view>
					<gal-css-animate ref="priceanimate" animateName="rubberBand" duration="0.5s"
						timingFunction="ease-in">
						<view class="help-tip">这个商品我想{{bargainInfo.bargainPrice}}元拿</view>
					</gal-css-animate>
					<view class="help-pro-bg">
						<image :src="bargainInfo.picUrl" mode="aspectFit" style="width: 460rpx; height: 460rpx;">
						</image>
					</view>
					<view class="help-action">
						<button v-if="bargainInfo.bargainUser&&bargainInfo.bargainUser.bargainCut"
							class="cu-btn bargain-btn radius lg" style="background-color: #CBCBCB; color: #fff"><text
								style="font-size: 40rpx;">砍价次数已达上限</text></button>

						<gal-css-animate   @click="bargainCutSave" v-else-if="bargainInfo.enable == '1' && bargainInfo.bargainUser.status == '0'"
							ref="actionanimate" animateName="heartBeat" duration="6s" timingFunction="ease-in">
							<button :loading="loading" class="cu-btn bargain-btn radius lg"
								style="background-color: #E11E19; color: #fff"><text style="font-size: 46rpx;"
									:disabled="disabled" @click="bargainCutSave">帮他砍一刀</text></button>
						</gal-css-animate>

						<button v-else class="cu-btn bargain-btn radius lg"
							style="background-color: #CBCBCB; color: #fff"><text
								style="font-size: 40rpx;">该砍价活动已结束</text></button>

					</view>
				</view>
			</gal-css-animate>
		</template>
		<view v-else>
			<gal-css-animate animateName="zoomIn" duration="0.5s" timingFunction="ease-in">
				<view class="container-bg">
					<view class="help-txt-layout">
						<view class="help-txt">
							<image class="help-image" :src="bargainInfo.picUrl" mode="aspectFit"></image>
						</view>

						<view style="font-size: 36rpx;font-weight: bold; ">帮我砍了<text
								style="font-size: 50rpx;font-weight: bold;color: #E11E19;">￥{{bargainCut.cutPrice||'0'}}</text>元
						</view>
					</view>
					<gal-css-animate ref="moreanimate" animateName="rubberBand" duration="0.5s"
						timingFunction="ease-in">
						<view class="help-tip" style="margin-top: 40rpx;">更多好物低价抢</view>
					</gal-css-animate>
					
					<view class="choose-layout" v-if="bargainCut && bargainCut.bargainInfos && bargainCut.bargainInfos.length>0">
						
						<template v-for="(item,index) in bargainCut.bargainInfos">
							<view class="choose-item" :key="index">
								<view class="choose-item-bg">
									<image :src="item.picUrl|| item.goodsSpu.picUrls[0]" mode="aspectFit"
										style="max-width: 194rpx; max-height: 186rpx;">
									</image>
									<view class="choose-item-txt  overflow-1">
										{{item.name}}
									</view>
								</view>
								<view class="choose-item-action " @click="chooseMe(item)">选 我</view>
							</view>
						</template>
					</view>
					<view class="help-action">
						<!-- <gal-css-animate ref="actionanimate" animateName="heartBeat" duration="6s"
							timingFunction="ease-in">
							<button class="cu-btn bargain-btn radius lg"
								style="background-color: #E11E19; color: #fff"><text
									style="font-size: 46rpx;">帮他砍一刀</text></button>
						</gal-css-animate> -->

						<!-- <button class="cu-btn bargain-btn radius lg" style="background-color: #CBCBCB; color: #fff"><text
							style="font-size: 40rpx;">今日砍价次数已达上线</text></button> -->
					</view>
				</view>
			</gal-css-animate>
		</view>


	</view>
</template>

<script>
	import galCssAnimate from '../anim/gal-css-animate.vue'
	import api from 'utils/api'
	export default {
		props: {
			bargainInfo: { // 分享参数
				type: Object,
				default: () => {
					return {}
				}
			},
			bargainHallId: {
				type: Number | String,
				default: ''
			},
			windowHeight:{
				type: Number | String,
				default: 800
			}
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				hasBargain: false,
				showPrice: false,
				disabled: false,
				loading: false,
				bargainCut: {
					bargainInfos : [
					]
				},
				
			}
		},
		components: {
			galCssAnimate
		},
		computed:{
		   heightBar () {
		     // #ifdef MP
		     return this.CustomBar > 50 ? this.CustomBar : 60
		     // #endif
		     return 40
		   }	
		},
		mounted: function() {
			
			console.log("====mounted=");
			// 0.5秒之后
			setTimeout(() => {
				this.$refs.priceanimate && this.$refs.priceanimate.play();
				this.$refs.actionanimate && this.$refs.actionanimate.loop(6000);
			}, 500)
			
			console.log("this.bargainCut.bargainInfos =",this.bargainCut.bargainInfos )
		},
		
		

		methods: {
			chooseMe(item){
				console.log(item)
				this.$emit('freshPro', {id: item.id})
			},
			//砍一刀
			bargainCutSave() {
				this.disabled = true;
				this.loading = true;
				let bargainUserId = this.bargainInfo.bargainUser.id;
				if (bargainUserId) {
					getApp().globalData.sf = 'bargainUserId:' + bargainUserId;
				}
				api.bargainCutSave({
					bargainUserId: bargainUserId,
					bargainHallId: this.bargainHallId
				}).then(res => {
					this.bargainCut = res.data;
					let that = this;
					this.hasBargain = true; 
                    this.loading = false;

					// uni.showModal({
					// 	content: '恭喜为好友砍下' + bargainCut.cutPrice.toFixed(2) + '元',
					// 	confirmColor: '#ff0000',
					// 	success(res) {
					// 		// that.bargainInfoGet({
					// 		// 	bargainId: that.id,
					// 		// 	id: bargainUserId
					// 		// });
					// 	}
					// });
				}).catch(e=>{
					uni.showModal({
						title: '提示',
						content: e,
						success(res) {}
					});
				});
			},
		}
	}
</script>

<style>
	@keyframes frame_ani_show {
		0% {
			-webkit-transform: scale(0);
			opacity: 0
		}

		100% {
			-webkit-transform: scale(1);
			opacity: 1
		}
	}

	.ani_show {
		-webkit-animation: frame_ani_show 0.5s linear;
	}

	.container-bg {
		width: 100%;
		height: 1086rpx;
		background-repeat: no-repeat;
		background-size: 100%;
		margin-top: 40%;
		background-image: url(https://img.songlei.com/live/bargain/help-bargain-bg.png);
	}

	.help-txt-layout {
		display: flex;

		color: #fff;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
	}

	.help-tip {
		margin-top: 20rpx;
		font-size: 42rpx;
		font-family: PingFang SC;
		font-weight: 800;
		color: #FFE6B3;
		justify-content: center;
		margin-left: 42rpx;
		align-items: center;
		width: 666rpx;
		height: 68rpx;
		display: flex;
		background-size: 100%;
		background-repeat: no-repeat;
		background-image: url(https://img.songlei.com/live/bargain/help-txt-bg.png);
	}

	.help-pro-bg {
		margin-top: 70rpx;
		width: 550rpx;
		height: 550rpx;
		display: flex;
		justify-content: center;
		margin-left: 100rpx;
		align-items: center;
		background-size: 100%;
		background-repeat: no-repeat;
		background-image: url(https://img.songlei.com/live/bargain/bargain-goods-bg.png);
	}

	.help-action {
		justify-content: center;
		align-items: center;
		display: flex;
		margin-top: 78rpx;
	}

	.help-txt {
		width: 74rpx;
		height: 74rpx;
		background: linear-gradient(0deg, #EA804B 0%, #FEEFBA 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}

	.help-image {
		width: 58rpx;
		height: 58rpx;
		border-radius: 50%;
	}

	.choose-layout {
		display: flex;
		/* justify-content: space-between; */
		margin-top: 120rpx;
		margin-left: 10rpx;
		margin-right: 10rpx;
		justify-content: center;
	}

	.choose-item {
		margin: 8rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		align-items: center;
	}

	.choose-item-bg {
		width: 230rpx;
		height: 330rpx;
		background-image: url(https://img.songlei.com/live/bargain/more-pro-bg.png);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-size: 100%;
		background-repeat: no-repeat;
	}

	.choose-item-txt {
		font-size: 22rpx;
		
		font-weight: bold;
		color: #000000;
		margin-top: 20rpx;
	}

	.choose-item-action {
		background: #E11E19;
		border-radius: 21px;
		width: 138rpx;
		height: 42rpx;
		line-height: 42rpx;
		font-weight: 400;
		color: #FFFFFF;
		font-size: 24rpx;
		margin-top: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
</style>
