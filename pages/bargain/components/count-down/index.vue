<template>
	<view v-if="outTime>0" style="display: initial;" :style="{
		color:textColor,
		fontSize: fontSize,
		fontWeight:fontWeight
	  }"
		class=" margin-left-xs margin-right-xs">
		<text v-if="day>0"
		:style="{'background-color': backgroundColor, 'padding':' 0 6rpx', 'border-radius': '6rpx'}"
		>{{day}}天</text>
		<text
			:style="{'background-color': backgroundColor, 'border-radius': '6rpx'}">{{hour>=0?hour:''}}</text>
		<text :style="{'color': connectorColor}">:</text>
		<text :style="{'background-color': backgroundColor, 'border-radius': '6rpx'}">{{minute>=0?minute:''}}</text>
		<text :style="{'color': connectorColor}">:</text>
		<text
			:style="{'background-color': backgroundColor,  'border-radius': '6rpx'}">{{second>=0?second:''}}</text>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				day: null,
				hour: null,
				minute: null,
				second: null,
				timer: null,
				totalTime: 0,
			};
		},
		components: {},
		watch: {
			outTime(val, oldVal) {
				if (val != oldVal) {}
			}
		},
		props: {
			// 这里定义了innerText属性，属性值可以在组件使用时指定，毫秒
			outTime: {
				type: Number,
				default: 0
			},
			textColor: {
				type: String,
				default: '#fff'
			},
			connectorColor: {
				type: String,
				default: '#fff'
			},
			index: {
				type: Number,
				default: 0
			},
			backgroundColor: {
				type: String,
				default: 'red'
			},
			fontSize: {
				type: String,
				default: '26rpx'
			},
			fontWeight: {
				type: Number,
				default: 400
			}
		},
		mounted: function() {
			this.CaculateDate();
		},
		destroyed: function() {
			if (this.timer) {
				clearInterval(this.timer);
			}
		},
		methods: {
			CaculateDate: function() {
				var that = this;
				if (this.totalTime == 0) {
					this.totalTime = this.outTime;
				}
                
				var leftTime = that.totalTime - 1000;
				var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10);
				var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10);
				var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);
				var seconds = parseInt(leftTime / 1000 % 60, 10);

				if (leftTime > 0) {
					that.totalTime = leftTime;
					that.day = days > 0 ? that.timeFormat(days) : 0;
					that.hour = hours > 0 ? that.timeFormat(hours) : 0;
					that.minute = minutes > 0 ? that.timeFormat(minutes) : 0;
					that.second = seconds > 0 ? that.timeFormat(seconds) : 0;
				}
				// console.log(leftTime, seconds, that.second, "======")
				this.timer = setInterval(function() {
					var leftTime = that.totalTime - 1000;
					var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10);
					var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10);
					var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);
					var seconds = parseInt(leftTime / 1000 % 60, 10);
					if (leftTime > 0) {
						that.totalTime = leftTime;
						that.day = days > 0 ? that.timeFormat(days) : 0;
						that.hour = hours > 0 ? that.timeFormat(hours) : 0;
						that.minute = minutes > 0 ? that.timeFormat(minutes) : 0;
						that.second = seconds > 0 ? that.timeFormat(seconds) : 0;
					} else {
						//结束
						clearInterval(that.timer);
						setTimeout(function() {
							that.$emit('countDownDone', that.index);
						}, 2000);
					}
				}, 1000);
			},

			timeFormat(param) {
				//小于10的格式化函数
				return param < 10 ? '0' + param : param;
			}

		}
	};
</script>
<style>
</style>
