<template>
  <view style="background-color: #fbdca0" v-if="scene != '1154'">
    <cu-custom :hideMarchContent="true" :isBack="true" :bgColor="bgColor">
      <block slot="backText">返回</block>
      <block slot="content">{{ title }}</block>
    </cu-custom>
    <!-- {{ bgColor }}, {{ title }} -->
    <!-- <image mode="widthFix"
		   :style="{height:'60px',width:'750rpx',overflow:'hidden',display:'block',position:'fixed', top:'0px',z-index:'999'}"></image>
		 -->
    <image
      mode="widthFix"
      src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/b7056cd4-06ac-48fb-8364-e1a4c2565e35.jpg"
      :style="{
        'z-index': '999',
        height: `60px`,
        width: '750rpx',
        position: 'absolute',
        top: '0px',
      }"
    ></image>
    <view>
      <view class="cu-card article bargin-bg" style="margin-top: -125rpx">
        <view style="position: relative" v-if="!showHelp">
          <!-- 总价值 -->
          <view class="price-bg">
            <view
              style="
                text-align: center;
                font-size: 30rpx;
                font-weight: 400;
                color: #ffffff;
                line-height: 58rpx;
              "
            >
              价值￥{{ bargainInfo.cutGoodsValue || "" }}
            </view>
          </view>
        </view>
        <view
          style="
            width: 100%;
            height: 280rpx;
            margin-top: 202rpx;
            margin-left: -4rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
          "
        >
          <image
            :src="bargainInfo.picUrl"
            mode="aspectFit"
            style="width: 280rpx; height: 200rpx"
          ></image>
        </view>
        <view
          v-if="bargainInfo.bargainPrice || bargainInfo.bargainPrice == '0'"
          style="
            display: flex;
            justify-content: center;
            color: white;
            font-size: 45rpx;
            margin-top: -24rpx;
            margin-left: -18rpx;
          "
        >
          低至 <text style="color: red">￥{{ bargainInfo.bargainPrice }}</text
          >元
        </view>
        <!-- <view v-if="spuName"
					style="font-size: 36rpx;font-weight: 800;color: #E32821;width: 100%; margin-top: 60rpx; display: flex; justify-content: center; align-items: center;margin-left: 10rpx;">
						{{bargainInfo.name}}
				</view> -->

        <!-- 砍价状态（0：未开始；1：活动中；2：已过期） -->
        <view
          style="
            border-radius: 32rpx;
            padding-bottom: 60rpx;
            display: block;
            background-color: #f9f6ec;
            overflow: hidden;
            margin: 30rpx;
            margin-top: 60rpx;
          "
          v-if="bargainInfo"
        >
          <!-- 活动倒计时 -->
          <view
            class="content"
            style="display: flex; justify-content: center"
            v-if="
              bargainInfo &&
              bargainInfo.status != '2' &&
              dateUtil.getOutTime(
                bargainInfo.bargainUser && bargainInfo.bargainUser.validEndTime
                  ? bargainInfo.bargainUser.validEndTime
                  : bargainInfo.validEndTime,
              ) > 0
            "
          >
            <view
              style="
                text-align: center;
                height: 56rpx;
                background-color: white;
                padding-right: 20rpx;
                padding-top: 10rpx;
              "
            >
              <count-down
                class="text-red"
                connectorColor="#E32821"
                backgroundColor="rgb(0,0,0,0)"
                textColor="#E32821"
                :outTime="
                  dateUtil.getOutTime(
                    bargainInfo.bargainUser &&
                      bargainInfo.bargainUser.validEndTime
                      ? bargainInfo.bargainUser.validEndTime
                      : bargainInfo.validEndTime,
                  )
                "
                @countDownDone="countDownDone"
                fontSize="26rpx"
              ></count-down>
              <text style="font-size: 26rpx; color: #e32821">后过期</text>
            </view>
          </view>

          <!-- 没参与 -->
          <!-- 价值文字描述 还没有砍价记录的时候-->
          <view
            v-if="!(bargainInfo.bargainUser && hasBargainUser)"
            style="
              height: 60rpx;
              line-height: 60rpx;
              font-weight: 800;
              margin-top: 60rpx;
              display: flex;
              justify-content: center;
              font-size: 36rpx;
              color: black;
            "
          >
            价值
            <text style="color: #e11e19; height: 60rpx; line-height: 60rpx">{{
              bargainInfo.goodsSku.salesPrice
            }}</text>
            元，可
            <text style="color: #e11e19; height: 60rpx; line-height: 60rpx">{{
              bargainInfo.bargainPrice
            }}</text>
            元获得
          </view>

          <!-- 已参加 -->
          <view v-if="bargainInfo.bargainUser && hasBargainUser">
            <view
              style="
                font-weight: 800;
                margin-top: 50rpx;
                font-size: 36rpx;
                color: black;
                display: flex;
                justify-content: center;
                align-items: center;
              "
              v-if="numberUtil.numberSubtract(canCutPrice, havCutPrice) > 0"
            >
              已砍
              <text class="text-red">{{ havCutPrice }}</text
              >元,
              <text style="margin-left: 10rpx">距离砍至</text>
              <text style="color: #e11e19">最低价</text>仅差
            </view>
            <view
              v-if="numberUtil.numberSubtract(canCutPrice, havCutPrice) > 0"
              style="display: flex; justify-content: center; margin-top: 14rpx"
            >
              <view style="display: flex; align-items: center">
                <view
                  style="
                    color: #e22e28;
                    font-size: 46rpx;
                    margin-top: 22rpx;
                    margin-right: 4rpx;
                    font-weight: 600;
                  "
                >
                  ¥</view
                >
                <view
                  class="text-red"
                  style="font-size: 74rpx; font-weight: bold"
                >
                  {{ numberUtil.numberSubtract(canCutPrice, havCutPrice) }}
                </view>
                <view style="height: 100rpx; position: relative; left: 30rpx">
                  <image
                    style="width: 214rpx; height: 100rpx"
                    src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/b8e7d060-a7c5-4b1c-a3de-34a04344c632.png"
                    mode=""
                  ></image>
                  <view
                    style="
                      position: absolute;
                      top: 10rpx;
                      left: 20rpx;
                      color: white;
                      letter-spacing: 3;
                    "
                  >
                    <view> 已完成{{ cutPercent }} </view>
                    <view> 继续加油 </view>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="cut-done">
              <image
                src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/63e06173-b562-45f5-9bab-35898b1bef90.png "
              >
              </image>
              <view class="cut-done-txt">恭喜砍价成功</view>
            </view>
          </view>

          <!-- 进度条 -->
          <view class="padding">
            <view class="cu-progress round" style="background-color: #fee6d9">
              <view
                style="background: linear-gradient(-90deg, #e22618, #f2a90e)"
                :style="'width: ' + cutPercent"
              >
                <!-- {{cutPercent}} -->
              </view>
            </view>
          </view>
          <view
            class="padding"
            style="
              display: flex;
              justify-content: space-between;
              margin-top: -96rpx;
            "
          >
            <view
              v-for="(item, index) in prolist"
              :key="index"
              :style="{
                display: 'flex',
                flexDirection: 'column',
                alignItems:
                  index == 0
                    ? 'flex-start'
                    : index == prolist.length - 1
                    ? 'flex-end'
                    : 'center',
              }"
            >
              <view>
                <img
                  :src="item.src"
                  alt=""
                  style="margin-top: 1rpx; width: 30rpx; height: 30rpx"
                />
              </view>
              <view>
                {{ item.name }}
              </view>
            </view>
          </view>

          <!-- 未参加 -->
          <view
            style="display: flex; justify-content: center"
            v-if="!(bargainInfo.bargainUser && hasBargainUser)"
          >
            <!-- && bargainInfo.bargainUser.status == '0' -->
            <button
              class="cu-btn bargain-btn round lg"
              :class="'bg-' + theme.themeColor"
              style="font-weight: 800; color: #ffffff"
              v-if="bargainInfo.enable == '1' && bargainInfo.status == '1'"
              @click="bargainUserSave"
            >
              预计邀请{{ bargainInfo.needPeopleNum }}人帮砍，可{{
                bargainInfo.bargainPrice
              }}元购得
            </button>
            <button
              class="cu-btn bargain-btn radius lg bg-gray"
              v-if="bargainInfo.enable == '0'"
            >
              <text>该砍价已关闭</text>
            </button>
            <button
              class="cu-btn bargain-btn round bg-gray lg"
              v-if="bargainInfo.status == '0'"
            >
              <text>活动未开始</text>
            </button>
            <button
              class="cu-btn bargain-btn round bg-gray lg"
              v-if="bargainInfo.status == '2'"
            >
              <text>活动已过期</text>
            </button>
          </view>
          <view
            class="padding-lr text-center margin-top-sm"
            style="display: flex; justify-content: center"
          >
            <!-- 已经参与 -->
            <view v-if="bargainInfo.bargainUser && hasBargainUser">
              <view
                class="cu-btn bargain-btn round lg"
                style="
                  height: 80rpx;
                  display: flex;
                  font-weight: 800;
                  color: #ffffff;
                  background-image: url(https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/5404046f-1e6f-4888-8121-9b85ae26e679.png);
                  background-repeat: no-repeat;
                  background-size: 100%;
                "
                v-if="
                  bargainInfo.enable == '1' &&
                  bargainInfo.bargainUser.status == '0' &&
                  bargainInfo.floorBuy == '0'
                "
              >
                <view style="flex: 4" @tap.stop="toBuy">
                  <!-- <view style="text-align: left;">
										<text style="font-size: 26rpx;">{{bargainInfo.goodsSku.salesPrice}}</text>
										<text style="font-size: 20rpx;">总价</text>-
										<text style="font-size: 26rpx;">{{havCutPrice}}</text>
										<text style="font-size: 20rpx;">已砍</text>
									</view> -->
                  <view
                    style="
                      text-align: left;
                      margin-top: 8rpx;
                      margin-left: 26rpx;
                      margin-top: -2rpx;
                    "
                  >
                    支付￥{{
                      numberUtil.numberSubtract(
                        bargainInfo.goodsSku.salesPrice,
                        havCutPrice,
                      )
                    }}元
                  </view>
                </view>
                <text
                  @tap.stop="shareShowFun"
                  style="flex: 3; margin-left: 14rpx"
                  >继续砍价</text
                >
              </view>
              <view
                v-else-if="
                  bargainInfo.enable == '1' &&
                  bargainInfo.bargainUser.status == '0' &&
                  bargainInfo.floorBuy == '1'
                "
              >
                <button
                  class="cu-btn bargain-btn round lg"
                  :class="'bg-' + theme.themeColor"
                  style="font-weight: 800; color: #ffffff"
                  v-if="bargainInfo.enable == '1' && bargainInfo.status == '1'"
                  @click="shareShowFun"
                >
                  预计邀请{{
                    bargainInfo.bargainUser.needPeopleNum
                  }}人帮砍，可{{ bargainInfo.bargainPrice }}元购得
                </button>
              </view>
              <view
                class="cu-btn bargain-btn round lg"
                v-if="
                  bargainInfo.enable == '1' &&
                  bargainInfo.bargainUser.status == '0' &&
                  bargainInfo.floorBuy == '3'
                "
              >
                <view
                  style="
                    height: 90rpx;
                    display: flex;
                    font-weight: 800;
                    color: #ffffff;
                    background-image: url(https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/5404046f-1e6f-4888-8121-9b85ae26e679.png);
                    background-repeat: no-repeat;
                    background-size: 100%;
                  "
                  v-if="
                    bargainInfo.bargainUser.cutNum >= bargainInfo.cutPeopleNum
                  "
                >
                  <view style="flex: 4" @tap.stop="toBuy">
                    <!-- 	<view style="text-align: left;">
											<text style="font-size: 26rpx;">{{bargainInfo.goodsSku.salesPrice}}</text>
											<text style="font-size: 20rpx;">总价</text>-
											<text style="font-size: 26rpx;">{{havCutPrice}}</text>
											<text style="font-size: 20rpx;">已砍</text>
										</view> -->
                    <view style="text-align: left; margin-top: 10rpx">
                      支付￥{{
                        numberUtil.numberSubtract(
                          bargainInfo.goodsSku.salesPrice,
                          havCutPrice,
                        )
                      }}元
                    </view>
                  </view>
                  <text
                    @tap.stop="shareShowFun"
                    style="flex: 3; margin-left: 14rpx"
                    >砍至{{ bargainInfo.bargainPrice }}元拿走</text
                  >
                </view>
                <view v-else>
                  <button
                    class="cu-btn bargain-btn round lg"
                    :class="'bg-' + theme.themeColor"
                    style="font-weight: 800; color: #ffffff"
                    v-if="
                      bargainInfo.enable == '1' && bargainInfo.status == '1'
                    "
                    @click="shareShowFun"
                  >
                    {{ bargainInfo.cutPeopleNum }}人帮砍即可购买，最低{{
                      bargainInfo.bargainPrice
                    }}元购得
                  </button>
                </view>
              </view>
              <button
                class="cu-btn bargain-btn radius lg bg-gray"
                v-if="bargainInfo.enable == '0'"
              >
                <text class="cuIcon-cardboardforbid">该砍价已关闭</text>
              </button>
              <view>
                <button
                  class="cu-btn bargain-btn round lg"
                  style="
                    background-color: #e11e19;
                    font-weight: bold;
                    color: #ffffff;
                    font-size: 26rpx;
                  "
                  v-if="bargainInfo.bargainUser.status == '1'"
                  @click="toBuy"
                >
                  <text>已完成砍价</text>
                  <text
                    v-if="bargainInfo.bargainUser.isBuy == '0'"
                    style="padding-left: 10rpx"
                  >
                    支付{{
                      numberUtil.numberSubtract(
                        bargainInfo.goodsSku.salesPrice,
                        havCutPrice,
                      )
                    }}元</text
                  >
                  <text v-else style="padding-left: 10rpx"> 已购买</text>
                </button>
              </view>
              <button
                class="cu-btn bargain-btn round bg-gray lg"
                v-if="bargainInfo.bargainUser.status == '2'"
              >
                <text class="cuIcon-close">活动已过期</text>
              </button>
            </view>
          </view>
          <!-- 砍价大按钮 -->
          <!-- <view v-if="userInfo && bargainInfo.bargainUser && hasBargainUser">
						<view class="padding text-center" v-if="bargainInfo.bargainUser.userId != userInfo.id">
							<button class="cu-btn round bg-orange cuIcon-cardboardforbid lg" :class="'bg-'+theme.themeColor"
								:disabled="disabled" @tap="bargainCutSave"
								v-if="bargainInfo.enable == '1' && bargainInfo.bargainUser.status == '0' && !bargainInfo.bargainUser.bargainCut">帮砍一刀</button>
							<button class="cu-btn round bg-gray cuIcon-check lg"
								v-if="bargainInfo.bargainUser.status == '0' && bargainInfo.bargainUser.bargainCut">已经砍过了</button>
							<navigator class="cu-btn round bg-cyan lg margin-left" hover-class="none"
								:url="'/pages/bargain/bargain-detail/index?id=' + bargainInfo.id">发起新砍价</navigator>
						</view>
						<view class="padding text-center"
							v-if="(bargainInfo.bargainUser.status == '1' || bargainInfo.bargainUser.floorBuy == '0') && (bargainInfo.bargainUser.userId == userInfo.id)">
							<button class="cu-btn round bg-green.light lg" @tap="toBuy"
								v-if="(bargainInfo.bargainUser.status == '1' || bargainInfo.bargainUser.floorBuy == '0') &&  bargainInfo.bargainUser.isBuy == '0'">¥
								{{numberUtil.numberSubtract(bargainInfo.goodsSku.salesPrice,havCutPrice)}} 砍后价购买</button>
							<navigator hover-class="none"
								:url="'/pages/order/order-detail/index?id=' + bargainInfo.bargainUser.orderId"
								class="cu-btn round bg-green.light lg" v-if="bargainInfo.bargainUser.isBuy == '1'">已经砍后价购买
							</navigator>
						</view>
					</view> -->
        </view>
      </view>

      <!-- 砍价记录 -->
      <view class="cu-card mar-top-30">
        <view class="cu-item" style="border-radius: 30rpx">
          <view
            style="
              font-size: 36rpx;
              font-family: PingFang SC;
              font-weight: 500;
              background-color: #fbeccf;
              padding: 20rpx;
              text-align: center;
              color: #000000;
            "
          >
            砍价记录
          </view>
          <view class="cu-list menu-avatar" v-if="bargainCutList.length > 0">
            <view
              class="cu-item"
              v-for="(item, index) in bargainCutList"
              :key="index"
            >
              <view
                class="cu-avatar round lg"
                :style="'background-image:url(' + item.headimgUrl + ');'"
              >
              </view>
              <view class="content">
                <view
                  class="text-grey"
                  style="font-weight: 800; color: #000000; font-size: 32rpx"
                >
                  {{ item.nickName }}
                </view>
                <view class="text-grey" style="color: #000000; font-size: 26rpx"
                  >{{ item.createTime }}
                </view>
              </view>
              <view class="margin-right text-red">
                <text
                  style="font-size: 30rpx; font-weight: bold; color: #e01512"
                  >砍掉{{ item.cutPrice }}元</text
                >
              </view>
            </view>
            <view class="cu-load bg-white" v-if="loadmore" @tap="loadMore"
              >加载更多</view
            >
          </view>
          <template v-else>
            <view
              style="
                width: 100%;
                height: 300rpx;
                font-size: 30rpx;
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              暂无数据</view
            >
          </template>
        </view>
      </view>
      <!-- 分享组件 -->
      <share-component
        v-model="showShare"
        :shareParams="shareParams"
      ></share-component>
      <r-canvas ref="rCanvas"></r-canvas>
    </view>
    <help-bargain-component
      :windowHeight="windowHeight"
      v-if="showHelp"
      :bargainInfo="bargainInfo"
      :bargainHallId="bargainHallId"
      @freshPro="freshPro"
    />
    <!-- 分享朋友圈 -->
  </view>
  <view v-else>
    <share-single-page />
  </view>
</template>

<script>
import rCanvas from "../components/r-canvas/r-canvas.vue";
import shopInfo from "components/shop-info/index";
const util = require("@/utils/util.js");
const { base64src } = require("utils/base64src.js");
const app = getApp();
import api from "utils/api";
import numberUtil from "utils/numberUtil.js";
import dateUtil from "utils/dateUtil.js";
import jweixin from "utils/jweixin";
import countDown from "../components/count-down/index";
import shareComponent from "@/components/share-component/index";
import helpBargainComponent from "../components/help-bargain/index";
import shareSinglePage from "components/share-single-page/index";

import { getCurrentTitle } from "@/public/js_sdk/sensors/utils.js";
import { senTrack } from "@/public/js_sdk/sensors/utils.js";

export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      dateUtil: dateUtil,
      numberUtil: numberUtil,
      bargainInfo: {
        shopInfo: {},
        name: "",
        bargainUser: {
          launchNum: "",
          validBeginTime: "",
          validEndTime: "",
        },
        goodsSpu: {
          freightTemplat: {},
        },
        goodsSku: {
          specs: [],
        },
      },
      disabled: false,
      page: {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: "",
        //升序字段
        descs: "create_time",
      },
      parameter: {},
      loadmore: true,
      bargainCutList: [],
      shareShow: "",
      curLocalUrl: "",
      userInfo: null,
      modalRule: "",
      id: "",
      // 会场id
      bargainHallId: "",
      specInfo: "",
      cutPercent: "",
      canCutPrice: "",
      havCutPrice: 0,
      posterUrl: "",
      posterShow: false,
      posterConfig: "",
      article_description: "",
      hasBargainUser: false, //是否存在砍价数据
      bargainUserId: "",
      showShare: false,
      shareParams: {},
      scene: "",
      Image: "",
      prolist: [
        {
          name: "",
          src: "https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/07cf122a-e076-40a6-911f-1c17802c1a55.png",
        },
        {
          name: "20%",
          src: "https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/07cf122a-e076-40a6-911f-1c17802c1a55.png",
        },
        {
          name: "40%",
          src: "https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/07cf122a-e076-40a6-911f-1c17802c1a55.png",
        },
        {
          name: "60%",
          src: "https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/07cf122a-e076-40a6-911f-1c17802c1a55.png",
        },
        {
          name: "80%",
          src: "https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/07cf122a-e076-40a6-911f-1c17802c1a55.png",
        },
        {
          name: "100%",
          src: "https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/07cf122a-e076-40a6-911f-1c17802c1a55.png",
        },
      ],
      windowHeight: 800,
      title: "砍价详情",
      enterTime: 0,
    };
  },
  components: {
    shareComponent,
    countDown,
    shareSinglePage,
    helpBargainComponent,
    rCanvas,
  },
  props: {},
  computed: {
    spuName: function () {
      return this.bargainInfo &&
        this.bargainInfo.goodsSpu &&
        this.bargainInfo.goodsSpu.name &&
        this.bargainInfo.goodsSpu.name.toString().length > 1
        ? this.bargainInfo.goodsSpu.name.substring(0, 9) + "..."
        : this.bargainInfo.goodsSpu.name;
    },

    showHelp: function () {
      return (
        this.userInfo &&
        this.bargainInfo.bargainUser &&
        this.bargainInfo.bargainUser.userId > 0 &&
        this.bargainInfo.bargainUser.userId != this.userInfo.id
      );
    },
  },
  onReady() {},
  onPageScroll(e) {
    this.pageScrollTop = e.scrollTop;
    if (this.pageScrollTop > 30) {
      this.title = "";
    } else {
      this.title = "砍价详情";
    }
  },
  onShow() {
    this.enterTime = Date.now();
    this.getData();
    uni.getSystemInfo({
      success: (res) => {
        this.windowHeight = res.windowHeight;
      },
    });
    this.userInfo = uni.getStorageSync("user_info");
  },
  onLoad(options) {
    this.dateUtil = dateUtil;
    this.numberUtil = numberUtil;
    let id;
    let bargainUserId, bargainHallId;
    let getLaunchOptions = uni.getLaunchOptionsSync();
    console.error(getLaunchOptions, "getLaunchOptions");
    this.scene = getLaunchOptions.scene;
    if (options.scene) {
      //接受二维码中参数
      let scenes = decodeURIComponent(options.scene).split("&");
      bargainUserId = scenes[0];
    } else if (options.bargainUserId) {
      //分享
      bargainUserId = options.bargainUserId;
    } else {
      //从砍价列表和我的砍价列表进入传入的会场id
      bargainHallId = options.conferencehall;
      id = options.id;
    }
    this.bargainUserId = bargainUserId;
    if (bargainHallId) {
      getApp().globalData.sf = "bargainUserId:" + bargainUserId;
    }
    this.id = id;
    this.bargainHallId = bargainHallId;
    app.initPage().then((res) => {});
  },

  onShareAppMessage: function () {
    let bargainInfo = this.bargainInfo;

    let title = bargainInfo.shareTitle;
    let imageUrl = bargainInfo.picUrl + "-jpg_w360_q90";
    let path = "";

    if (bargainInfo.bargainUser && this.hasBargainUser) {
      path =
        "/pages/bargain/bargain-detail/index?bargainUserId=" +
        bargainInfo.bargainUser.id;
    } else {
      path =
        "/pages/bargain/bargain-detail/index?id=" +
        bargainInfo.id +
        "&conferencehall=" +
        this.bargainHallId;
    }
    const userInfo = uni.getStorageSync("user_info");
    const userCode = userInfo ? "&sharer_user_code=" + userInfo.userCode : "";
    path = path + userCode;

    senTrack("ActivityPageShareOrCollect", {
      page_name: getCurrentTitle(0),
      page_level: "二级",
      activity_id: bargainInfo.id,
      activity_name: bargainInfo.name,
      activity_type_first: "营销活动",
      activity_type_second: "砍价",
      activity_type: "分享",
    });
    return {
      title: `我想${this.bargainInfo.bargainPrice}元拿,就差你一刀了,快来帮帮我`,
      path: path,
      imageUrl: this.Image,
      success: function (res) {
        if (res.errMsg == "shareAppMessage:ok") {
          console.log(res.errMsg);
        }
      },
      fail: function (res) {
        // 转发失败
      },
    };
  },

  onUnload() {
    const leaveTime = new Date().getTime();
    const stayDuration = Math.floor((leaveTime - this.enterTime) / 1000);

    senTrack("ActivityPageLeave", {
      page_name: getCurrentTitle(0),
      forward_source: getCurrentTitle(0),
      page_level: "二级",
      activity_id: this.bargainInfo.id,
      activity_name: this.bargainInfo.name,
      activity_type_first: "营销活动",
      activity_type_second: "砍价",
      stay_duration: stayDuration,
    });
  },
  methods: {
    darwimage() {
      if (this.bargainInfo.picUrl) {
        this.$nextTick(async () => {
          // 初始化
          console.log(this.bargainInfo.picUrl, "this.bargainInfo.picUrl");
          await this.$refs.rCanvas.init({
            canvas_id: "rCanvas",
            // canvas_width:200,
            canvas_height: 350,
            hidden: true,
          });
          // 画图
          await this.$refs.rCanvas
            .drawImage({
              url: this.bargainInfo.picUrl,
              x: 0,
              y: 0,
              w: 375,
              h: 300,
            })
            .catch((err_msg) => {
              uni.showToast({
                title: err_msg,
                icon: "none",
              });
            });
          // 画图
          await this.$refs.rCanvas
            .drawImage({
              url: "https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/2e85ba5f-bf35-4245-aaee-7c282329b092.png",
              x: 280,
              y: 20,
              w: 70,
              h: 76,
            })
            .catch((err_msg) => {
              uni.showToast({
                title: err_msg,
                icon: "none",
              });
            });
          await this.$refs.rCanvas
            .drawImage({
              url: "https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/b11b9c04-3362-46a5-979f-4b90bdbd3a5b.png",
              x: 0,
              y: 250,
              w: 375,
              h: 50,
            })
            .catch((err_msg) => {
              uni.showToast({
                title: err_msg,
                icon: "none",
              });
            });
          await this.$refs.rCanvas
            .drawText({
              x: 296,
              y: 50,
              text: "立减",
              font_color: "red",
              font_size: 20,
              font_family: "PingFang SC",
              font_weight: 700,
            })
            .catch((err) => {});
          await this.$refs.rCanvas
            .drawText({
              x: 300,
              y: 76,
              text:
                Number(this.bargainInfo.cutGoodsValue) -
                Number(this.bargainInfo.bargainPrice),
              font_color: "red",
              font_size: 20,
              font_family: "PingFang SC",
              font_weight: 700,
            })
            .catch((err) => {
              this.error(err);
            });
          await this.$refs.rCanvas
            .drawText({
              x: 287,
              y: 76,
              text: "￥",
              font_color: "red",
              font_size: 16,
              font_family: "PingFang SC",
              font_weight: 700,
            })
            .catch((err) => {
              this.error(err);
            });

          await this.$refs.rCanvas
            .drawText({
              x: 50,
              y: 280,
              text: `原价 ￥ ${this.bargainInfo.cutGoodsValue}`,
              font_color: "red",
              font_size: 16,
              font_family: "PingFang SC",
              font_weight: 700,
            })
            .catch((err) => {
              this.error(err);
            });

          await this.$refs.rCanvas
            .drawText({
              x: 260,
              y: 280,
              text: `${this.bargainInfo.bargainPrice}元拿走`,
              font_color: "#FBF1EF",
              font_size: 16,
              font_family: "PingFang SC",
              font_weight: 700,
            })
            .catch((err) => {
              this.error(err);
            });
          // 生成海报
          await this.$refs.rCanvas.draw((res) => {
            //res.tempFilePath：生成成功，返回base64图片
            // 保存图片
            console.log(res.tempFilePath, " res.tempFilePath");
            this.Image = res.tempFilePath;
            // this.$refs.rCanvas.saveImage(res.tempFilePath)
          });
        });
      }
    },
    // 分享朋友圈方法
    onShareTimeline() {
      if (this.Image) {
        console.log(this.Image);
      }
      let bargainInfo = this.bargainInfo;
      let title = bargainInfo.shareTitle;
      let imageUrl = bargainInfo.picUrl + "-jpg_w360_q90";
      let path = "";

      if (bargainInfo.bargainUser && this.hasBargainUser) {
        path =
          "/pages/bargain/bargain-detail/index?bargainUserId=" +
          bargainInfo.bargainUser.id;
      } else {
        path =
          "/pages/bargain/bargain-detail/index?id=" +
          bargainInfo.id +
          "&conferencehall=" +
          this.bargainHallId;
      }
      const userInfo = uni.getStorageSync("user_info");
      const userCode = userInfo ? "&sharer_user_code=" + userInfo.userCode : "";
      path = path + userCode;

      return {
        path: path,
        imageUrl: this.Image,
        success: function (res) {
          if (res.errMsg == "shareAppMessage:ok") {
            console.log(res.errMsg);
          }
        },
        fail: function (res) {
          // 转发失败
        },
      };
    },
    //查询砍价信息
    bargainInfoGet(data, isShowShareFun, flag) {
      api.bargainInfoGet(data).then((res) => {
        this.hasBargainUser = true;
        console.log("===1111==");
        if (!res.data.bargainUser) {
          console.log("===222==");
          res.data.bargainUser = {
            validBeginTime: "",
            validEndTime: "",
          };
          this.hasBargainUser = false;
        }
        let bargainInfo = res.data;

        // 判断是否是第一次请求接口，添加页面预览埋点
        if (flag == 1) {
          senTrack("ActivityPageView", {
            page_name: getCurrentTitle(0),
            forward_source: getCurrentTitle(0),
            page_level: "二级",
            activity_id: bargainInfo.id,
            activity_name: bargainInfo.name,
            activity_type_first: "营销活动",
            activity_type_second: "砍价",
          });
        }

        let goodsSku = bargainInfo.goodsSku;
        let specInfo = "";
        if (goodsSku && goodsSku.specs && goodsSku.specs.length > 0) {
          goodsSku.specs.forEach(function (specItem, index) {
            specInfo = specInfo + specItem.specValueName;
            if (goodsSku.specs.length != index + 1) {
              specInfo = specInfo + ";";
            }
          });
        }
        this.bargainInfo = bargainInfo;
        this.darwimage();
        console.log("======bargainInfo=======", bargainInfo);
        this.specInfo = specInfo;
        if (this.hasBargainUser) {
          let canCutPrice =
            bargainInfo.goodsSku.salesPrice - bargainInfo.bargainPrice; //可砍
          let havCutPrice = bargainInfo.bargainUser.havBargainAmount; //已砍
          let cutPercent =
            Number((havCutPrice / canCutPrice) * 100).toFixed(2) + "%";
          this.bargainInfo = bargainInfo;
          this.parameter.bargainUserId = bargainInfo.bargainUser.id;
          this.cutPercent = cutPercent;
          this.canCutPrice = canCutPrice;
          this.havCutPrice = havCutPrice;
          this.bargainCutList = [];
          this.bargainCutPage();
        }
        setTimeout(() => {
          this.article_description = bargainInfo.goodsSpu
            ? bargainInfo.goodsSpu.description
            : "";
        }, 300);
        this.initShareUrl();
        if (isShowShareFun) {
          //砍价没完成的时候弹出分享框
          this.$nextTick(() => {
            if (this.bargainInfo.bargainUser.status != "1") {
              this.shareShowFun();
            }
          });
        }
      });
    },

    //帮砍记录列表
    bargainCutPage() {
      api
        .bargainCutPage(
          Object.assign({}, this.page, util.filterForm(this.parameter)),
        )
        .then((res) => {
          let bargainCutList = res.data.records;
          console.log(res.data.records, "res.data.records");
          this.bargainCutList = [...this.bargainCutList, ...bargainCutList];
          if (bargainCutList.length < this.page.size) {
            this.loadmore = false;
          }
        });
    },

    //发起砍价
    bargainUserSave() {
      this.disabled = true;
      let that = this;

      senTrack("ActivityClick", {
        page_name: getCurrentTitle(0),
        page_level: "二级",
        activity_id: this.bargainInfo.id,
        activity_name: this.bargainInfo.name,
        activity_type_first: "营销活动",
        activity_type_second: "砍价",
      });
      // uni.requestSubscribeMessage({
      // 	tmplIds: ['1qiMopN2G5vt7m5T1E0QI40b8dl1DUrw6pZe1O7_OAY'],
      // 	success(e) { // 订阅成功
      // 		console.log(e)
      // 	},
      // 	fail(e) {
      // 		console.log(e)
      // 	},
      // 	complete(e) { // 方法执行成功回调（不管是否成功都会执行）
      // console.log(e)
      that.handleDealBargain();
      // }
      // })
    },

    handleDealBargain() {
      api
        .bargainUserSave({
          bargainId: this.bargainInfo.id,
          bargainHallId: this.bargainHallId,
        })
        .then((res) => {
          this.bargainInfoGet(
            {
              bargainId: this.id,
              bargainHallId: this.bargainHallId,
            },
            true,
          );
        });
    },

    loadMore() {
      this.page.current = this.page.current + 1;
      this.bargainCutPage();
    },
    initShareUrl() {
      // #ifdef H5
      this.curLocalUrl = util.setH5ShareUrl();
      if (!(this.curLocalUrl.indexOf("bargainUserId") != -1)) {
        if (this.bargainUserId) {
          //先判断是否有传过来的砍价ID，如果有就用原有的
          this.curLocalUrl =
            this.curLocalUrl + "&bargainUserId=" + this.bargainUserId;
        } else if (this.bargainInfo.bargainUser && this.hasBargainUser) {
          this.curLocalUrl =
            this.curLocalUrl +
            "&bargainUserId=" +
            this.bargainInfo.bargainUser.id;
        }
      }
      // 目前测试出的问题是：微信jsSDK分享的路径url必须 和 当前的页面路径一致，否则分享的链接会乱码，所以替换一下当前路径
      // 安卓有效，iOS微信浏览器有问题无法替换
      history.replaceState(history.state, null, this.curLocalUrl);
      // #endif
    },
    shareShowFun() {
      senTrack("ActivityClick", {
        page_name: getCurrentTitle(0),
        page_level: "二级",
        activity_id: this.bargainInfo.id,
        activity_name: this.bargainInfo.name,
        activity_type_first: "营销活动",
        activity_type_second: "砍价",
      });
      // #ifdef APP-PLUS
      this.curLocalUrl = util.setAppPlusShareUrl();
      if (this.bargainUserId) {
        //先判断是否有传过来的砍价ID，如果有就用原有的
        this.curLocalUrl =
          this.curLocalUrl + "&bargainUserId=" + this.bargainUserId;
      } else if (
        this.bargainInfo.bargainUser &&
        this.hasBargainUser &&
        !(this.curLocalUrl.indexOf("bargainUserId") != -1)
      ) {
        this.curLocalUrl =
          this.curLocalUrl +
          "&bargainUserId=" +
          this.bargainInfo.bargainUser.id;
      }
      // #endif

      let desc = "长按识别小程序码";
      let shareImg = this.bargainInfo.picUrl;
      // #ifdef H5 || APP-PLUS
      desc = "长按识别二维码";
      // #endif
      //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
      let posterConfig = {
        width: 750,
        height: 1700,
        backgroundColor: "#fff",
        debug: false,
        texts: [
          {
            x: 150,
            y: 160,
            baseLine: "middle",
            text: `邀请好友帮砍价￥${this.bargainInfo.bargainPrice}购商品`,
            fontSize: 40,
            color: "#080808",
            fontFamily: "STSong",
            // borderRadius: 34
          },
          // {
          //        x: 30,
          //        y: 113,
          //        baseLine: 'top',
          //        text:  '分享标题', //this.bargainInfo.shareTitle,
          //        fontSize: 38,
          //        color: '#080808'
          //      },
          {
            x: 59,
            y: 1100,
            fontSize: 38,
            baseLine: "middle",
            text: this.bargainInfo.goodsSpu.name,
            width: 540,
            lineNum: 1,
            color: "#080808",
            zIndex: 200,
          },
          {
            x: 59,
            y: 1150,
            baseLine: "middle",
            text: "原价: ¥" + this.bargainInfo.goodsSku.salesPrice,
            fontSize: 38,
            color: "#080808",
          },
          {
            x: 59,
            y: 1250,
            baseLine: "middle",
            text: [
              {
                text: "砍价成功仅需",
                fontSize: 38,
                color: "#ec1731",
              },
              {
                text: "¥" + this.bargainInfo.bargainPrice,
                fontSize: 36,
                color: "#ec1731",
                marginLeft: 38,
              },
            ],
          },
          {
            x: 59,
            y: 1300,
            baseLine: "middle",
            text: "价格具有时效性",
            fontSize: 28,
            color: "#929292",
          },
          // {
          // 	x: 59,
          // 	y: 945,
          // 	baseLine: 'middle',
          // 	text: [{
          // 		text: this.bargainInfo.goodsSpu.sellPoint,
          // 		fontSize: 28,
          // 		color: '#929292',
          // 		width: 570,
          // 		lineNum: 1
          // 	}]
          // },
          {
            x: 360,
            y: 1550,
            baseLine: "top",
            text: desc,
            fontSize: 38,
            color: "#080808",
          },
          {
            x: 360,
            y: 1600,
            baseLine: "top",
            text: "快来帮好友砍一刀",
            fontSize: 28,
            color: "#929292",
          },
        ],
        images: [
          {
            width: 668,
            height: 720,
            x: 59,
            y: 250,
            url: shareImg,
          },
          {
            width: 200,
            height: 230,
            x: 92,
            y: 1410,
            url: null,
            qrCodeName: "qrCodeName", // 二维码唯一区分标识
          },
        ],
      };
      let userInfo = uni.getStorageSync("user_info");

      if (userInfo && userInfo.headimgUrl) {
        //如果有头像则显示
        posterConfig.images.push({
          width: 62,
          height: 62,
          x: 30,
          y: 30,
          borderRadius: 62,
          url: userInfo.headimgUrl,
        });
        posterConfig.texts.push({
          x: 113,
          y: 61,
          baseLine: "middle",
          text: userInfo.nickName,
          fontSize: 32,
          color: "#8d8d8d",
        });
      }

      this.shareParams = {
        title: this.bargainInfo.shareTitle,
        desc: this.bargainInfo.goodsSpu.name,
        imgUrl: this.bargainInfo.picUrl,
        url: this.curLocalUrl,
        scene: this.bargainInfo.bargainUser.id,
        bargainHallId: this.bargainHallId,
        page: "pages/bargain/bargain-detail/index",
        posterConfig: posterConfig,
        trackParams: {
          page_name: getCurrentTitle(0),
          page_level: "二级",
          activity_id: this.bargainInfo.id,
          activity_name: this.bargainInfo.name,
          activity_type_first: "营销活动",
          activity_type_second: "砍价",
          activity_type: "分享",
        },
      };
      this.showShare = true;
    },

    ruleShow() {
      this.modalRule = "show";
    },

    ruleHide() {
      this.modalRule = "";
    },

    //前去购买
    toBuy(e) {
      if (this.bargainInfo.bargainUser.isBuy != "0") {
        uni.showToast({
          title: "该商品您已下单，请去订单里查看",
          icon: "npne",
          duration: 2000,
        });
        return;
      }
      let bargainInfo = this.bargainInfo;
      let bargainUser = bargainInfo.bargainUser;
      let goodsSpu = bargainInfo.goodsSpu;
      let goodsSku = bargainInfo.goodsSku;

      /* 把参数信息异步存储到缓存当中 */
      uni.setStorage({
        key: "param-orderConfirm",
        data: {
          source: 1, //source 1 砍价
          bargainId: this.id, //砍价Id
          relationId: bargainUser.id,
          bargainHallId: this.bargainHallId, //会场id
          shoppingCarItemIds: {
            spuId: goodsSpu.id,
            skuId: goodsSku.id,
            quantity: 1,
            specInfo: "",
          },
        },
      });
      uni.navigateTo({
        url: "/pages/order/order-confirm/index",
      });
    },

    countDownDone() {
      this.onLoad();
    },

    freshPro(bargin) {
      this.id = bargin.id;
      this.bargainUserId = null;
      this.getData();
    },

    async getData() {
      //数据重置
      this.disabled = false;
      this.page = {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: "",
        //升序字段
        descs: "create_time",
      };
      this.parameter = {};
      this.loadmore = true;
      this.bargainCutList = [];
      this.shareShow = "";
      this.curLocalUrl = "";
      this.userInfo = null;
      this.modalRule = "";
      this.specInfo = "";
      this.cutPercent = "";
      this.canCutPrice = "";
      this.havCutPrice = 0;
      this.posterUrl = "";
      this.posterShow = false;
      this.posterConfig = "";
      this.article_description = "";
      this.hasBargainUser = false; //是否存在砍价数据
      this.showShare = false;
      this.shareParams = {};
      this.Image = "";

      if (this.bargainUserId) {
        api.bargainUserGet(this.bargainUserId).then(async (res) => {
          let bargainUser = res.data;
          this.id = bargainUser.bargainId;
          this.bargainHallId = bargainUser.bargainHallId;
          this.bargainInfoGet(
            {
              bargainId: this.id,
              bargainHallId: this.bargainHallId,
              id: bargainUser.id,
            },
            false,
            1,
          );
        });
      } else {
        await this.bargainInfoGet(
          {
            bargainId: this.id,
            bargainHallId: this.bargainHallId,
          },
          false,
          1,
        );
      }
      console.log(this.$refs.rCanvas, "that.$refs.rCanvas");
    },
  },
};
</script>
<style>
.bargin-bg {
  background-image: url("https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/f392392e-029d-45dc-92de-1cc4874cc1fa.jpg");
  background-size: 100% auto;
  background-repeat: no-repeat;
}

.price-bg {
  background-image: url("https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/3c83e8f4-3f36-49b9-a0ef-a6c08b82115e.png");
  background-size: 100% auto;
  position: absolute;
  left: 150rpx;
  top: 162rpx;
  width: 444rpx;
  height: 58rpx;
  z-index: 3;
  font-weight: 400;
  color: #ffffff;
  font-size: 26rpx;
}

.row-img {
  width: 200rpx !important;
  height: 200rpx !important;
  border-radius: 10rpx;
}

.bargain-btn {
  width: 580rpx;
  height: 76rpx;
}

.show-bg {
  height: 84%;
  margin-top: 120rpx;
}

.image-box {
  height: 90%;
}

.show-btn {
  margin-top: -130rpx;
}

page {
  background-color: #fbdca0;
}

.cut-done {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16rpx;
}

.cut-done image {
  width: 296rpx;
  height: 170rpx;
}

.cut-done-txt {
  font-size: 44rpx;
  font-weight: 500;
  color: #f51e08;
}
</style>
