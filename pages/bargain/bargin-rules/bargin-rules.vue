<template>
	<view>
		<cu-custom :hideMarchContent="true" :isBack="true" :bgColor="showBg?'bg-'+theme.backgroundColor:''">
			<block slot="backText">返回</block>
			<block slot="content">砍价规则</block>
		</cu-custom>
		<view class="" style="padding: 20rpx;" v-if="article">
		 <jyf-parser :html="article"></jyf-parser></view>
	</view>
</template>

<script>	
    import api from 'utils/api'
	const util = require("utils/util.js")
	const app = getApp()
	export default {
	components: {
    },
		data() {
			return {
				showBg: true, //是否显示背景色
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //:
				article:""
			}
		},
		methods: {
			bargainInfoPage1() {
				api.bargainInfoPage1('234').then(res => {
				    this.article = res.data.description
					console.log(res.data.description,'获取富文本名称')
				})
			},
		},
			
		options(){
		},
		onShow() {
		this.bargainInfoPage1() 
		}
	}
</script>

<style>

</style>
