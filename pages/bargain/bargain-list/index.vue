<template>
  <view style="background-color: #f30d0c;">
    <cu-custom :hideMarchContent="true" :isBack="true" :bgColor="bgColor">
      <block slot="backText">返回</block>
      <block slot="content">全民来砍价</block>
    </cu-custom>
    <template v-if="bargaindata.bargainInfos" style="height: 100%;">
      <!-- <image mode="widthFix"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/05903eab-acb4-47bb-b7d1-7beb1c0e5ecc.jpg"
				:style="{
			  height:`${(CustomBar>0?CustomBar:60)}px`,
			   width:'750rpx',
			   overflow:'hidden',
			   display:'block',
			       position: 'relative',
			       top: '-60px',
			}"></image> -->
      <view
        :style="{ backgroundImage: 'url(' + bargaindata.picUrl[0] + ')', backgroundSize: 'contain', backgroundColor: bargaindata.bgColor || '#f30d0c' }"
        class="bground">
        <view style="position: relative;top:-7px;left:0;right:0;">
          <view :style="{ background: bargaindata.cutRuleBgColor || '#E83A1C' }" @click="Bargainingrules" style="border-bottom-left-radius: 18rpx;
                          border-top-left-radius: 20rpx;;font-size: 20rpx;display: flex;
						  justify-content: center;align-items: center; position: absolute;
						  top:270rpx;right:-4rpx;padding:8rpx;
						  color: white;">砍价规则</view>
        </view>
        <view style="margin-top: 229rpx;margin-bottom: 100rpx;">
          <template v-if="fnishiing.length || wating.length">
            <view v-if="tabNum == '1'">
              <view style="margin-left: 20rpx;margin-right: 20rpx;position: relative;top: 100rpx;">
                <image style="width: 100%;height: 357rpx;"
                  src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/09c59c5c-0664-42a2-a927-847ac0aa9f8f.png"
                  mode="aspectFit"></image>
                <view style="display: flex;">
                  <view @click="getbarginng"
                    style="font-size: 36rpx;font-weight: bold;color: #000000;position: absolute;top: 22rpx;left: 64rpx;width: 35%;height: 67rpx;">
                    正在砍价中</view>
                  <view v-if="fnishiing && fnishiing.length"
                    style="position: absolute;top: 29rpx;left: 250rpx;background-color: #E11E19;color: #FFFFFF;border-radius: 50%; height:30rpx; width:30rpx; line-height:30rpx; text-align:center">
                    {{ fnishiing.length }}</view>
                  <view @click="fullbargin"
                    style="position: absolute; top: 26rpx;right: 108rpx;font-size: 26rpx;font-family: PingFang SC;font-weight: bold; ">
                    砍价已完成</view>
                  <view v-if="wating && wating.length"
                    style="position: absolute;top: 29rpx;right: 66rpx;background-color: #E11E19;color: #FFFFFF;border-radius: 50%; height:30rpx; width:30rpx; line-height:30rpx; text-align:center">
                    {{ wating.length }}</view>
                </view>
                <view class="uni-margin-wrap" style="position: absolute;top: 52rpx;" v-if="fnishiing.length">
                  <swiper autoplay='true' interval='2000'>
                    <swiper-item v-for="(item, index) in fnishiing" :key='index' class="swiper">
                      <view @click="detailshop(item)">
                        <view style="display:flex;margin-top: 30rpx;padding: 20rpx 20rpx  20rpx  20rpx;;">
                          <view style="">
                            <image style="width: 235rpx;height: 215rpx;" :src="item.picUrl" mode="aspectFit">
                            </image>
                          </view>
                          <view v-for="(key, val, ind) in item.bargainUser" :key='ind'
                            style="display: flex;flex: 1;flex-direction: column;padding-left: 20rpx;">
                            <view style="font-size: 25rpx;font-family: CenturyGothic;font-weight: 400;color: #000000">
                              <view style="position: relative;" v-if="item.bargainUser">
                                <unicountdown :show-day="false" :font-size="10"
                                  :hour="toHHmmss(dateUtil(item.bargainUser['validEndTime'])).split(':')[0]"
                                  :minute="toHHmmss(dateUtil(item.bargainUser['validEndTime'])).split(':')[1]"
                                  :second="toHHmmss(dateUtil(item.bargainUser['validEndTime'])).split(':')[2]"
                                  color="#FFFFFF" background-color="#000000" />
                              </view>
                            </view>
                            <view
                              style='width: 372px;font-size: 29rpx;font-weight: bold;color: #000000;margin-top: 12rpx;'>
                              {{ item.name }}
                            </view>
                            <view style="margin-top: 10rpx;width: 400rpx;">
                              <progress border-radius="8" :percent="item.cutPercent" activeColor="#E75115"
                                stroke-width="8" backgroundColor='#FEE6D9' active='true' />
                            </view>
                            <view
                              style="width: 370rpx;height: 68rpx;background: #E11E19;border-radius: 32px;display: flex;justify-content: center;align-items: center;color: white;margin-top:20rpx;">
                              再邀{{ item.bargainUser['needPeopleNum'] }}人帮砍 ¥{{ item.bargainPrice }}拿走
                            </view>
                          </view>
                        </view>
                      </view>
                    </swiper-item>
                  </swiper>
                </view>
                <view v-else style="position: absolute;top: 98rpx;height: 328rpx;width: 100%;">
                  <image style="height: 226rpx;width: 100%;"
                    src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/8185728e-6e30-4c0f-b04d-71ba630bab1e.jpg"
                    mode=""></image>
                </view>
              </view>
            </view>
            <view v-else>
              <view style="margin-left: 20rpx;margin-right: 20rpx;position: relative;top: 100rpx;">
                <image style="width: 100%;height: 357rpx;"
                  src='https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/f69cfb62-3498-43af-af0e-b6c3d327b87e.png'
                  mode="aspectFit"></image>
                <view style="display: flex;">
                  <view @click="getbarginng" style="font-size: 26rpx;font-weight: bold;color: #000000;position: absolute;top: 26rpx;left: 114rpx;
									width: 45%;
										height: 54rpx;
										margin-left: -90rpx;
										text-align: center;
										height: 54rpx;
										margin-top: -8px;
										padding-top: 18rpx;">
                    正在砍价中</view>
                  <view v-if="fnishiing && fnishiing.length"
                    style="position: absolute;top: 30rpx;left: 256rpx;background-color: #E11E19;color: #FFFFFF;border-radius: 50%; height:30rpx; width:30rpx; line-height:30rpx; text-align:center">
                    {{ fnishiing.length }}</view>

                  <view @click="fullbargin"
                    style="position: absolute; top: 24rpx;right: 108rpx;font-size: 32rpx;font-family: PingFang SC;font-weight: bold;width: 226rpx;height: 67rpx;margin-right: -39px;">
                    砍价已完成</view>
                  <view v-if="wating && wating.length"
                    style="position: absolute;top: 30rpx;right: 60rpx;background-color: #E11E19;color: #FFFFFF;border-radius: 50%; height:30rpx; width:30rpx; line-height:30rpx; text-align:center">
                    {{ wating.length }}</view>

                  <view class="uni-margin-wrap" style="position: absolute;top: 52rpx;height: 143px;"
                    v-if="wating.length">
                    <swiper class="swiper" autoplay='true' interval='2000'>
                      <swiper-item v-for="(item, index) in wating" :key='index'>
                        <view style="display:flex;margin-top: 30rpx;padding: 20rpx 20rpx  20rpx  20rpx;">
                          <view style="">
                            <image style="width: 235rpx;height: 215rpx;" :src="item.picUrl" mode="aspectFit"></image>
                          </view>
                          <view v-for="(key, val, index) in item.bargainUser" :key='index'
                            style="display: flex;flex: 1;flex-direction: column;padding-left: 20rpx;">
                            <view style="font-size: 25rpx;font-family: CenturyGothic;font-weight: 400;color: #000000">
                            </view>
                            <view
                              style='width: 372px;font-size: 29rpx;font-weight: bold;color: #000000;margin-top: 12rpx;'>
                              {{ item.name }}
                            </view>
                            <!-- <view 
													style="font-size: 20rpx;font-family: PingFang SC;font-weight: 500;text-decoration: line-through;color: #A5A5A5;">
													价值 : ¥{{item.cutGoodsValue}}
												</view> -->
                            <view style="margin-top: 10rpx;width: 400rpx;">
                              <progress border-radius="8" percent="100" activeColor="#E75115" duration='5'
                                stroke-width="8" backgroundColor='#FEE6D9' active='true' />
                            </view>
                            <view @click="toBuy(item)" v-if="item.bargainUser['isBuy'] != 1"
                              style="width: 370rpx;height: 68rpx;background: #E11E19;border-radius: 32px;display: flex;justify-content: center;align-items: center;color: white;margin-top:20rpx;">
                              砍价完成支付{{ item.bargainPrice }}元拿走
                            </view>
                            <view v-else
                              style="width: 370rpx;height: 68rpx;background: #575454;border-radius: 32px;display: flex;justify-content: center;align-items: center;color: white;margin-top:20rpx;">
                              砍价完成支付{{ item.bargainPrice }}元拿走
                            </view>
                          </view>
                        </view>
                      </swiper-item>
                    </swiper>
                  </view>
                  <view v-else style="position: absolute;top: 98rpx;height: 328rpx;width: 100%;">
                    <image style="height: 226rpx;width: 100%;"
                      src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/d4f7019a-a9e0-4a3d-975a-6dcbd1651b6e.jpg"
                      mode=""></image>
                  </view>
                </view>
              </view>
            </view>
          </template>
        </view>
        <view :class="[fnishiing.length || wating.length ? 'barginshopList' : 'barginshopListleng']">
          <view class="shoplis" id="shoplis" style="margin-left:15rpx;margin-right:15rpx;">
            <view
              style="margin-bottom: 24rpx;padding-top: 35rpx;display: flex;align-items: center;justify-content: center;">
              <view style="width: 30rpx;margin-right: 20rpx;">
                <image style="height: 8rpx;"
                  src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/efe16d14-9925-4fe6-a339-fc11e8fa5d4a.png">
                </image>
              </view>
              <view v-if="bargaindata.hallName" style="font-size: 34rpx;font-weight: bold;text-align: center;
							font-family: Source Han Sans CN;
							color: #FFFFFF;
							">
                {{ bargaindata.hallName }}
              </view>
              <view style="width: 30rpx;margin-left: 20rpx;">
                <image style="height: 8rpx;"
                  src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/f7cecced-5d94-47df-b6c0-486a59fa5859.png">
                </image>
              </view>
            </view>
            <scroll-view scroll-y="true" :style="{
              height: scrollHeight,
              overflow: 'scroll',
              borderRadius: ' 20rpx'
            }">
              <view v-if="bargaindata.bargainInfos" style="background: white;padding-bottom: 20rpx;">
                <!-- 商品循环 -->
                <view v-for="(item, indewx) in bargaindata.bargainInfos" :key='indewx'>
                  <view @click="detailshop(item)"
                    style="display: flex;color: black;padding: 10rpx 20rpx 10rpx 10rpx;margin-bottom: 20rpx;">
                    <view>
                      <image style="width: 230rpx;height: 230rpx; border-radius: 10rpx;" :src="item.picUrl"
                        mode="aspectFit">
                      </image>
                    </view>
                    <view style="padding-left:10rpx;padding-top:10rpx;display:flex;flex-direction:column;margin-left:10rpx;flex: 1;
											    padding-bottom: 15px;border-bottom: 1px solid #E9E9E9;">
                      <view style="font-size: 34rpx;font-weight: 600;color: #000000;">
                        {{ item.name }}
                      </view>
                      <view class="progress-box"
                        style="width:60%;position: relative; margin-bottom: 8rpx;   margin-top: 20rpx;">
                        <progress border-radius="8" :percent="item.percent" activeColor="#E75115" stroke-width="8"
                          backgroundColor='#FEE6D9' active='true' />
                        <view
                          style="position: absolute; top: -6rpx;right: -164rpx;font-size: 22rpx;font-family: PingFang SC;  font-family: PingFang SC;  font-weight: 600;color: #A5A5A5;">
                          剩余库存: {{ item.limitNum }}
                        </view>
                      </view>
                      <view style="
												font-size: 24rpx;
												font-family: Source Han Sans CN;
												font-weight: 400;
												text-decoration: line-through;
												color: #9E9B9D;">
                        价值 : ¥{{ item.cutGoodsValue }}</view>
                      <view v-if="bargaindata.endTime"
                        style="margin-top: 10rpx;margin-top:10rpx;font-size: 27rpx;font-family: PingFang SC;font-weight: 600;color: #000000;">
                        {{ bargaindata.endTime.slice(0, 10) }} <span style='color:#E33E3D'>{{
                          bargaindata.endTime.slice(10, 19)
                        }}</span> <span style="margin-left:8rpx">结束</span>
                      </view>
                      <view style="width: 450rpx; height:76rpx; position: relative;" v-if="item.floorBuy == '1'">
                        <image
                          src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/5404046f-1e6f-4888-8121-9b85ae26e679.png"
                          style="height:66rpx;width: 450rpx;margin-top: 10rpx;"></image>
                        <view
                          style=" display: flex;position: absolute;top: 26rpx;    color: white;    font-size: 25rpx;    left: 22rpx;">
                          最多邀请<view
                            style="width: 41rpx;  display: flex;justify-content: center;align-items: center;    ">
                            {{ item.needPeopleNum }}
                          </view>位用户
                        </view>
                        <view style="position: absolute;right: 20rpx; top: 26rpx; color: white;font-size: 25rpx;">
                          砍至{{ item.bargainPrice }}元拿走 </view>
                      </view>
                      <view style="width: 450rpx; height:76rpx; position: relative;" v-if="item.floorBuy == '0'">
                        <image
                          src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/5404046f-1e6f-4888-8121-9b85ae26e679.png"
                          style="height:66rpx;width: 450rpx;margin-top: 10rpx;"></image>
                        <view
                          style=" display: flex;   position: absolute;    top: 26rpx;    color: white;    font-size: 25rpx;    left: 22rpx;">
                          最多邀请<view
                            style="width: 41rpx;  display: flex;justify-content: center;align-items: center;    ">
                            {{ item.needPeopleNum }}
                          </view>位用户
                        </view>
                        <view style="position: absolute;right: 20rpx; top: 26rpx; color: white;font-size: 25rpx;">
                          最低{{ item.bargainPrice }}元拿走
                        </view>
                      </view>
                      <view style="width: 450rpx; height:76rpx; position: relative;" v-if="item.floorBuy == '3'">
                        <image
                          src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/5404046f-1e6f-4888-8121-9b85ae26e679.png"
                          style="height:66rpx;width: 450rpx;margin-top: 10rpx;"></image>
                        <view
                          style=" display: flex;   position: absolute;    top: 26rpx;    color: white;    font-size: 25rpx;    left: 22rpx;">
                          最多邀请<view
                            style="width: 41rpx;  display: flex;justify-content: center;align-items: center;    ">
                            {{ item.needPeopleNum }}
                          </view>位用户
                        </view>
                        <view style="position: absolute;right: 20rpx; top: 26rpx; color: white;font-size: 25rpx;">
                          最低{{ item.bargainPrice }}元拿走
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </template>
    <base-nodata v-else-if="!loading" tip="暂无进行中的砍价会场" />
  </view>

</template>
<script>
const util = require("utils/util.js")
const app = getApp()
import api from 'utils/api'
import baseNodata from "components/base-nodata/index"
import unicountdown from "../components/uni-countdown/uni-countdown"
import dateUtil from 'utils/dateUtil.js'

import { getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'
import { senTrack } from '@/public/js_sdk/sensors/utils.js'

export default {
  components: {
    unicountdown,
    baseNodata
  },
  data () {
    return {
      loading: true,
      showBg: false,
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      page: {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: 'sort',
        //升序字段
        descs: ''
      },
      parameter: {},
      loadmore: true,
      bargainInfoList: [],
      // ====LIWEIDONG====/	
      bargaindata: '',
      images: [
        'https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/09c59c5c-0664-42a2-a927-847ac0aa9f8f.png',
        'https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/f69cfb62-3498-43af-af0e-b6c3d327b87e.png'
      ],
      background: ['color1', 'color2', 'color3'],
      indicatorDots: true,
      autoplay: true,
      interval: 2000,
      duration: 500,
      tabNum: 1,
      // testHour: 1,
      // testMinute: 0,
      // testSecond: 0,
      // start: false,
      // timeupSecond: 10,
      fnishiing: [],
      CustomBar: this.CustomBar,
      wating: [],
      setHght: '',
      windowHeight: '800',
      enterTime: 0
    }
  },
  props: {},
  //页面滑动 监听事件
  computed: {
    bgColor () {
      if (this.bargaindata.bargainInfos) {
        return "rgba(255, 255, 255, 0)"
      } else {
        return 'bg-' + this.theme.backgroundColor
      }
    },

    scrollHeight () {
      if (this.fnishiing.length || this.wating.length) {
        return (this.windowHeight - 420) + 'px'
      } else {
        return (this.windowHeight - 180) + 'px'
      }
    }
  },

  onPageScroll (e) {
    // this.pageScrollTop = e.scrollTop;
    // console.log(this.pageScrollTop, 'pageScrollTop')
    // if (this.pageScrollTop > 10) {
    // 	this.bgColor = "rgba(186, 156, 128, " + ((this.pageScrollTop + 30) / 100) + ')'
    // } else {
    // 	this.bgColor = "rgba(186, 156, 128, 0)"
    // }
  },
  onShow () {
    this.enterTime = new Date().getTime()
    this.bargainInfoPage1()
    this.$nextTick(() => {
      //          const query = uni.createSelectorQuery().in(this);
      // query.select('#shoplis').boundingClientRect(data => {
      //   console.log("得到布局位置信息" + JSON.stringify(data));
      //   this.setHght = data.height-50
      // }).exec();
    })
    uni.getSystemInfo({
      success: res => {
        this.windowHeight = res.windowHeight
      }
    })
  },
  onUnload () {
    const leaveTime = new Date().getTime()
    const stayDuration = Math.floor((leaveTime - this.enterTime) / 1000)

    senTrack('ActivityPageLeave', {
      'page_name': getCurrentTitle(0),
      'forward_source': getCurrentTitle(0),
      'page_level': '一级',
      'activity_id': this.bargaindata.bargainHallId,
      'activity_name': this.bargaindata.hallName,
      'activity_type_first': '营销活动',
      'activity_type_second': '砍价',
      'stay_duration': stayDuration
    })
  },

  onLoad: function (options) {
    let that = this
    if (options.shopId) {
      this.parameter.shopId = options.shopId
    }
    app.initPage().then(res => {
      // this.bargainInfoPage1();
    })
  },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1
    }
  },

  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading()
    this.refresh() // 隐藏导航栏加载框

    uni.hideNavigationBarLoading() // 停止下拉动作

    uni.stopPullDownRefresh()
  },
  onReady: function () {

  },
  methods: {
    scrollTopS (e) {
      console.log(e, 'scrollTopS')
    },
    percents (item) {
      console.log(Math.floor(Number(item.launchNum) / (Number(item.launchNum) + Number(item.limitNum))))
      return Math.floor(Number(item.launchNum) / (Number(item.launchNum) + Number(item.limitNum)))
    },
    dateUtil (date) {
      if (date) {
        //获取某个时间点到当前时间的差（秒）
        var nowDate = new Date()
        var endDate = new Date(date.replace(new RegExp('-', 'g'), '/'))
        return endDate.getTime() - nowDate.getTime()
      }
      return ''
    },
    toBuy (val) {
      let that = this
      let arr = this.bargaindata.bargainInfos.filter(function (item) {
        return item.id == val.id
      })
      console.log(this.bargaindata, 'ARR')
      if (arr[0].bargainUser.isBuy != '0') {
        uni.showToast({
          title: '该商品您已下单，请去订单里查看',
          icon: 'npne',
          duration: 2000
        })
        return
      }
      let bargainInfo = arr[0]
      let bargainUser = bargainInfo.bargainUser
      let goodsSpu = bargainInfo.goodsSpu
      let goodsSku = bargainInfo.goodsSku

      /* 把参数信息异步存储到缓存当中 */
      uni.setStorage({
        key: 'param-orderConfirm',
        data: {
          source: 1, //source 1 砍价
          bargainId: arr[0].id, //砍价Id
          relationId: bargainUser.id,
          bargainHallId: that.bargaindata.bargainHallId, //会场id
          // shoppingCarItemIds: {
          // 	spuId: goodsSpu.id,
          // 	skuId: goodsSku.id,
          // 	quantity: 1,
          // 	specInfo: '',
          // },
        }
      })
      uni.navigateTo({
        url: '/pages/order/order-confirm/index'
      })
    },

    formatTime (date) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hour = date.getHours()
      const minute = date.getMinutes()
      const second = date.getSeconds()
      return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber)
        .join(
          ':')
    },
    detailshop (val) {

      senTrack('ActivityClick', {
        'page_name': getCurrentTitle(0),
        'page_level': '一级',
        'activity_id': this.bargaindata.bargainHallId,
        'activity_name': this.bargaindata.hallName,
        'activity_type_first': '营销活动',
        'activity_type_second': '砍价'
      })

      uni.navigateTo({
        url: `/pages/bargain/bargain-detail/index?id=${val.id}&conferencehall=${this.bargaindata.id}`
      })
    },
    Bargainingrules () {
      uni.navigateTo({
        url: '/pages/bargain/bargin-rules/bargin-rules'
      })
    },
    percent (item) {
      console.log(Math.floor(item.launchNum / item.limitNum), 'Math.floor(item.launchNum/item.limitNum)')
      return Math.floor(item.launchNum / item.limitNum)
    },
    getbarginng () {
      this.tabNum = 1
      // this.bargainInfoPage1();
    },

    fullbargin () {
      this.tabNum = 2
      // this.bargainInfoPage1();
    },
    toHHmmss (data) {
      var time
      var hours = parseInt((data % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      var minutes = parseInt((data % (1000 * 60 * 60)) / (1000 * 60))
      var seconds = (data % (1000 * 60)) / 1000
      time = (hours < 10 ? ('0' + hours) : hours) + ':' + (minutes < 10 ? ('0' + minutes) : minutes) + ':' + (
        seconds < 10 ? ('0' + seconds) : seconds)
      return time
    },
    bargainInfoPage1 () {
      let that = this
      this.fnishiing = []
      this.wating = []
      api.bargainInfoPage1().then(res => {
        if (res.data) {
          this.bargaindata = res.data

          senTrack('ActivityPageView', {
            'page_name': getCurrentTitle(0),
            'forward_source': getCurrentTitle(0),
            'page_level': '一级',
            'activity_id': this.bargaindata.bargainHallId,
            'activity_name': this.bargaindata.hallName,
            'activity_type_first': '营销活动',
            'activity_type_second': '砍价'
          })

          this.bargaindata.bargainInfos.forEach(function (item) {
            item.percent = (Number(item.launchNum) / (Number(item.launchNum) + Number(item
              .limitNum))) * 100
          })
          res.data.bargainInfos.forEach(function (item) {
            if (item.bargainUser != null && item.bargainUser.status == '1') {
              that.wating.push(item)
            } else if (item.bargainUser != null && item.bargainUser.status == '0') {
              console.log(item.bargainUser.havBargainAmount, '未完成')
              let canCutPrice = item.goodsSku.salesPrice - item.bargainPrice //可砍
              let havCutPrice = item.bargainUser.havBargainAmount //已砍
              let cutPercent = Number(havCutPrice / canCutPrice * 100).toFixed(2)
              item.cutPercent = cutPercent
              that.fnishiing.push(item)
            }
          })

        }
        this.loading = false
      }).catch(e => {
        this.loading = false
      })

    },
    refresh () {
      this.loadmore = true
      this.bargainInfoList = []
      this.page.current = 1
      this.bargainInfoPage1()
    }

  }
}
</script>
<style>
.row-img {
  width: 200rpx !important;
  height: 200rpx !important;
  border-radius: 10rpx;
}

.bargain {
  min-height: 1450rpx;
}

.bargain-item {
  background-color: #ffffff;
  width: 90%;
  margin: auto;
  border-radius: 10rpx;
  margin-bottom: 40rpx;
}

.bargain-bg {
  width: 100%;
  height: 680rpx;
  margin-top: -160rpx;
}

.bargain-information {
  width: 460rpx;
}

.bargain-list {
  margin-top: -50rpx;
}

.btn-enter {
  width: 580rpx;
  height: 76rpx;
}

.uni-icon {
  line-height: 1.5;
}

.progress-cancel {
  margin-left: 40rpx;
}

.progress-control button {
  margin-top: 20rpx;
}

.wx-progress-inner-bar {
  border-radius: 6px !important;
  background: linear-gradient(-90deg, #e11e19, #f4b50d);
}

.uni-margin-wrap {
  width: 690rpx;
  width: 100%;
}

.swiper {
  height: 300rpx;
}

.swiper-item {
  display: block;
  height: 300rpx;
  line-height: 300rpx;
  text-align: center;
}

.swiper-list {
  margin-top: 40rpx;
  margin-bottom: 0;
}

.uni-common-mt {
  margin-top: 60rpx;
  position: relative;
}

.info {
  position: absolute;
  right: 20rpx;
}

.uni-padding-wrap {
  width: 550rpx;
  padding: 0 100rpx;
}

.bground {
  position: fixed;
  background-size: 100% auto;
  background-repeat: no-repeat;
  height: 100%;
  right: 0;
  top: 0;
}

.barginshopList {
  width: 100%;
  border-radius: 20rpx;
  margin-bottom: 120rpx;
  /* padding-bottom: 50rpx; */
}

.barginshopListleng {
  width: 100%;
  border-radius: 20rpx;
  /* margin-top: 100rpx; */
  margin-bottom: 120rpx;
  /* padding-bottom: 50rpx; */
}
</style>
