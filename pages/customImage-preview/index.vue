<template>
  <view class="full-screen-preview" >
    <view class="rotated-image">
      <image @click="closePreview" :src="imageUrl" mode="widthFix" style="margin-bottom: 210rpx;"/>
      <view>
        <canvas 
          style="
            position: absolute; 
            left: -9999px;
            width: 345px;
            height: 65px; 
            background-color: #ffffff;"
          class="bar_code" canvas-id="Brcode">
        </canvas>
      </view>

      <view @click.stop="refreshBarcode" class="text-center">刷新会员二维码</view>
    </view>
  </view>
</template>

<script>
import { getEvtscdDetails } from "@/pages/coupon/api/coupon.js"
const brCode = require("utils/barcode.js");
import api from 'utils/api'

export default {
  data() {
    return {
      id:'',
      imageUrl: ''
    };
  },

  onLoad: function (options) {
    if(options.id){
      this.id=options.id
      this.getCouponInfo( this.id )
    }else{
      this.id = ''
      this.cardInfoGet()
    }
  },

  methods: {
    closePreview() {
      uni.navigateBack();
    },

    refreshBarcode(){
      if (this.id) {
        this.getCouponInfo( this.id)
      }else{
        this.cardInfoGet()
      }
    },

      // 获取劵详情
    getCouponInfo ( id ) {
      getEvtscdDetails({ couponNo: id }).then(res => {
        console.log("res", res);
        let couponInfo = res.data;
        const ctx = wx.createCanvasContext('Brcode');
    
        // // 保存canvas状态
        // ctx.save();
        // // 旋转canvas
        // ctx.translate(65, 518); // 将原点移动到旋转中心
        // ctx.rotate(-Math.PI / 2); //逆时针旋转90度
        // 绘制条形码
        brCode.code128(ctx, couponInfo.custQrcode, 345, 100); // 注意参数顺序可能需要调整
        // // 恢复canvas状态
        // ctx.restore();
        // // 绘制完成
        // ctx.draw();

        // 将canvas转换为图片
        setTimeout(()=>{
          uni.canvasToTempFilePath({
            canvasId: 'Brcode',
            width: 345,
            height: 100,
            success: (res) => {
            // 图片路径
            const tempFilePath = res.tempFilePath;
            console.log('条码图片路径:1', tempFilePath);
            this.imageUrl = tempFilePath
            },
            fail: (err) => {
            console.error('转换失败:', err);
            }
          }, this);
        },3000)
      }).catch(e => {})
    },

    cardInfoGet() {
				api.getCardList().then(res => {
					res.data = JSON.parse(res.data);
          console.log("res.data",res.data);
					if (res.data && res.data.vipCardMessageList && res.data.vipCardMessageList.length > 0) {
						const carInfo = res.data.vipCardMessageList[0];
						if (!carInfo) {
							let data = JSON.stringify(res.data)
							uni.showModal({
								title: '提示',
								content: `${data}`,
								success: function(res) {
									if (res.confirm) {
										console.log('用户点击确定');
									} else if (res.cancel) {
										console.log('用户点击取消');
									}
								}
							});
							return;
						}
            const ctx = wx.createCanvasContext('Brcode');
            // // 保存canvas状态
            // ctx.save();
            // // 旋转canvas
            // ctx.translate(65, 518); // 将原点移动到旋转中心
            // ctx.rotate(-Math.PI / 2); //逆时针旋转90度
            // 绘制条形码
            brCode.code128(ctx, carInfo.barcode, 345, 100); // 注意参数顺序可能需要调整
            // // 恢复canvas状态
            // ctx.restore();
            // // 绘制完成
            // ctx.draw();
            // 将canvas转换为图片
						 setTimeout(()=>{
							 uni.canvasToTempFilePath({
							   canvasId: 'Brcode',
							   width: 345,
							   height: 100,
							   success: (res) => {
							 	// 图片路径
							 	const tempFilePath = res.tempFilePath;
							 	console.log('条码图片路径:2', tempFilePath);
                 this.imageUrl = tempFilePath
							   },
							   fail: (err) => {
							 	console.error('转换失败:', err);
							   }
							 }, this);
						 },3000)
					}
				});
			},
  },

};
</script>

<style>
.full-screen-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: '#FFFFFF';
  display: flex;
  justify-content: center;
  align-items: center;
}

.rotated-image {
  transform: rotate(90deg); /* 将图片旋转90度 */
}
</style>
