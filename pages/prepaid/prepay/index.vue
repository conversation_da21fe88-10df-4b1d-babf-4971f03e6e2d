<template>
  <view>
    <!-- 页面标题 -->
    <view>
      <cu-custom 
        :bgColor="'bg-' + theme.backgroundColor" 
        :isBack="true" 
        :hideMarchContent="true"
      >
        <block slot="backText">返回</block>
        <block slot="content">帮我付</block>
      </cu-custom>
    </view>
    <view>
      <!-- 价格展示 -->
      <view class="text-center margin-tb">
        <format-price 
          signFontSize="38rpx" 
          smallFontSize="50rpx" 
          priceFontSize="50rpx"
          :price="totalPrice" 
        />
      </view>

      <view v-if="listOrderInfo.length" class="padding bg-white" style="width: 100%; height: 100%;">
        <view class="text-black">
          帮付订单信息
        </view>
        <view class="margin-top">
          <view 
            class="flex padding-bottom margin-top-sm" 
            style="border-bottom: 1rpx solid #f1f1f1;"
            v-for="item in listOrderInfo"
            :key="item.id"
            @tap="goDetail(item)"
          >
            <view style="width: 240rpx;">
              <image 
                :src="item.picUrl" 
                style="display: block; width: 240rpx; height: 240rpx; border: 1rpx solid #e3e3e3; border-radius: 20rpx;" 
                mode="aspectFill"
              />
            </view>
            <view class="margin-left-sm" style="width: 430rpx;">
              <view class="text-black text-cut" style="width: 100%;">
                {{ item.spuName}}
              </view>
              <view class="ellipsis">
                 {{  item.specInfo ? item.specInfo : '' }} x{{ item.quantity }}
              </view>
              <view style="width: 100%; margin-top: 74rpx;" class="text-right">
                <format-price 
                  color="#000"  
                  signFontSize="28rpx" 
                  smallFontSize="28rpx" 
                  priceFontSize="28rpx"
                  :price="item.paymentPrice" 
                />
              </view>
            </view>
          </view>
        </view>
      </view>

      <button 
        v-if="(tatus == '0' || status == null) && currentUserId != orderUserId" 
        class="payMe-btn" 
        @tap="goPay"
      >
        好友帮付
      </button>
      <!-- 不需要展示支付方式，但需要唤起支付 -->
      <payComponents ref="pay" v-show="false" :callBack="true" @success="successBack"
      @fail="failBack"/>
    </view>
  </view>
</template>

<script>
const app = getApp();

import formatPrice from "@/components/format-price/index.vue";
import util from 'utils/util'
import { getByOrderMainId } from "@/pages/prepaid/api/prepaid.js"
import payComponents from '@/components/pay-components/pay-components.vue';

export default{
  components: { formatPrice, payComponents },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      orderOn: '',
      listOrderInfo: [],
      totalPrice: 0,
      status: '',
      currentUserId: uni.getStorageSync('user_info').id,
      orderUserId: ''
    }
  },
  onLoad(options) {
    this.listOrderInfo = []
    this.orderOn = options.orderOn
  },
  onShow() {
    app.initPage().then(res => {
      if (util.isUserLogin()) {
        this.getByOrderMain()
      } else {
        const pages = getCurrentPages()
        const url = pages[pages.length - 1]['$page']['fullPath']
        uni.reLaunch({
          url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
        })
      }
    });
  },
  methods: {
    async getByOrderMain() {
      try {
        this.listOrderInfo = []
        this.totalPrice = 0
        this.status = ''
        const res = await getByOrderMainId(this.orderOn);
        res.data.length && res.data.forEach(item => {
          this.listOrderInfo.push(...item.listOrderItem)
        })
        this.listOrderInfo.length && this.listOrderInfo.forEach(item => {
          this.totalPrice += Number(item.paymentPrice)
        })
        this.status = res.data[0].status
        this.orderUserId = res.data[0].userId
      } catch (error) {

      }
    },
    goDetail(item) {
      uni.navigateTo({ url: `/pages/goods/goods-detail/index?id=${item.spuId}&source_module=${encodeURIComponent('预付卡')}` });
    },
    goPay() {
      const params = {
        listOrderInfo: this.listOrderInfo
      }
      this.$refs.pay?.payment(params, true)
    },
    successBack() {
      uni.showToast({ title: `支付成功`, icon: 'none' });
    },
    failBack() {
      uni.showToast({ title: `支付失败`, icon: 'none' });
    }
  }
}

</script>

<style lang="scss" scoped>
.payMe-btn{
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #FF4444;
  border-radius: 40rpx;
  font-weight: 800;
  font-size: 28rpx;
  color: #FFFFFF;
  display: block;
  margin: 40rpx auto 20rpx;
}
.ellipsis{
  display: -webkit-box;
  color: #777777; 
  max-height: 70rpx; 
  width: 100%; 
  -webkit-box-orient: vertical;
  text-wrap: wrap; 
  -webkit-line-clamp: 2; 
  overflow: hidden; 
  text-overflow: ellipsis;
  font-size: 24rpx;
  margin-top: 10rpx;
}
</style>