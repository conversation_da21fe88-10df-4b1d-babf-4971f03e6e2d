import { requestApi as request } from "@/utils/api.js";

// 获取帮付联系人
export function getHelpPayUserList(data) {
  return request({
    url: "/mallapi/orderinfo/getHelpPayUserList",
    method: "get",
    data,
  });
}

// 获取排行榜商品数据
export function rankGoods(data) {
  return request({
    url: "/mallapi/rankInfo/rankByCateId",
    method: "get",
    data,
  });
}

// 通过主订单号获取好友帮付的订单信息
// mallapi/orderinfo/getByOrderMainId/
export function getByOrderMainId(id) {
  return request({
    url: `/mallapi/orderinfo/getByOrderMainId/${id}`,
    method: "get",
  });
}