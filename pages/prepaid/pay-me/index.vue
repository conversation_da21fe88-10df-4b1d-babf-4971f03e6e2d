<template>
  <view>
    <!-- 页面标题 -->
    <view>
      <cu-custom 
        :bgColor="'bg-' + theme.backgroundColor" 
        :isBack="true" 
        :hideMarchContent="true"
      >
        <block slot="backText">返回</block>
        <block slot="content">帮我付</block>
      </cu-custom>
    </view>
    <view>
      <!-- 价格展示 -->
      <view class="text-center margin-tb">
        <format-price 
          signFontSize="38rpx" 
          smallFontSize="50rpx" 
          priceFontSize="50rpx"
          :price="price" 
        />
      </view>

      <!-- 帮付人员 -->
      <view class="padding bg-white" style="width: 100%; height: 100%;">
        <!-- <view>
          <view class="text-black">
            手机号查找帮付人
          </view>
          <view class="flex align-center margin-tb">
            <image 
              src="https://img.songlei.com/rank/pay-person.png" 
              style="width: 74rpx; height: 74rpx;" 
            />
            <input 
              class="margin-left-sm" 
              style="width: 585rpx; height: 84rpx; border-bottom: 1rpx solid #f3f3f3" 
              placeholder="输入对方手机号码，找帮付好友" 
              v-model="phone"
            />
          </view>
          <button class="send-btn" @tap="handlePayMe(phone)">发送</button>
        </view> -->
        <!-- <view class="margin-top" v-if="payUserList.length">
          <view class="text-black">
            快速选择帮付人
          </view>
          <view 
            v-for="item in payUserList" 
            :key="item.id" 
            class="flex align-center margin-tb justify-between"
          >
            <image 
              :src="item.headimgUrl || 'https://img.songlei.com/rank/pay-person.png'" 
              style="width: 74rpx; height: 74rpx; border-radius: 50%;" 
            />
            <view 
              class="flex align-center justify-between margin-left" 
              style="border-bottom: 1rpx solid #f3f3f3; flex: 1; height: 94rpx;"
            >
              <view style="flex: 1;">{{ item.phone }}</view>
              <button class="payMe-btn" @tap="handlePayMe(item.phone)">帮我付</button>
            </view>
          </view>
        </view> -->
        <view class="margin-top">
          <view class="text-black">
            通过微信找朋友帮忙付
          </view>
          <view>
            <view class="flex justify-around">
              <view class="padding-tb-sm" @tap="goShare">
                <image 
                  src="https://img.songlei.com/rank/wechat.png" 
                  style="width: 102rpx; height: 102rpx;" 
                />
                <view class="text-black text-sm text-center">微信</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 发送成功弹框 -->
    <uni-popup ref="perpopup" type="center">
			<view class="success-poppup padding">
        <image 
          src="https://img.songlei.com/rank/pay-success.png" 
          style="display: block; width: 113rpx; height: 113rpx; margin: 0 auto ;" 
        />
        <view class="text-black text-center margin-tb">发起成功，待付款</view>
        <view class="text-center margin-tb" style="color: #BDBDBD;">本笔订单自发起后 30 分钟内有效</view>

        <view class="flex buttons">
          <view @tap="goHome">回首页逛逛</view>
          <view @tap="handlePage('/pages/order/order-list/index')">查看订单</view>
        </view>
			</view>
      
		</uni-popup>

    <share-component 
      v-if="showShare"
      v-model="showShare" 
      :shareParams="shareParams" 
      :showSharePoster="false" 
    />

  </view>
</template>

<script>
const app = getApp();

import formatPrice from "@/components/format-price/index.vue";
import util from 'utils/util'
import { getHelpPayUserList } from "@/pages/prepaid/api/prepaid.js"
import shareComponent from "@/components/share-component/index"

export default{
  components: { formatPrice, shareComponent },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      price: '',
      orderOn: '',
      phone: '',
      payUserList: [],
      shareParams: {},
      showShare: false,
      isSharing: false, // 标记用户是否触发了分享操作
    }
  },
  onLoad(options) {
    this.price = options.price
    this.orderOn = options.orderOn
    this.shareParams = {
      title: `拜托您帮我支付一笔${this.price}元的订单`,
      desc: `拜托您帮我支付一笔${this.price}元的订单`,
      imgUrl: 'https://img.songlei.com/rank/pay-demand-ban.png',
      page: 'pages/prepaid/prepay/index?orderOn='+this.orderOn,
	  url: 'pages/prepaid/prepay/index?orderOn='+this.orderOn,
    }
  },
  onShow() {
    app.initPage().then(res => {
      if (util.isUserLogin()) {
        this.getHelpPayUserList()
      } else {
        const pages = getCurrentPages()
        const url = pages[pages.length - 1]['$page']['fullPath']
        uni.reLaunch({
          url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
        })
      }
    });
    // 检查是否可能完成了分享
    if (this.isSharing) {
      console.log("用户可能完成了分享");
      // 执行相应的后续操作
      this.isSharing = false; // 重置分享状态
      this.showShare = false;
      this.$refs.perpopup.open();
    }
  },
  methods: {
    async getHelpPayUserList() {
      try {
        const res = await getHelpPayUserList();
        this.payUserList = res.data || []
      } catch (error) {

      }
    },
    handlePayMe(phone) {
      if (!phone) {
        uni.showToast({ title: `请输入手机号或选帮付人`, icon: 'none' });
        return
      }
      uni.showModal({
        title: '提示',
        content: '确认发送给' + phone,
        success: (res) => {
          if (res.confirm) {
            this.$refs.perpopup.open();
          } else {
            
          }
        },
        complete: () => {}
      });
    },
    handlePage(url) {
      uni.navigateTo({ url })
    },
    goHome() {
      uni.switchTab({ url: '/pages/home/<USER>' });
    },
    goShare() {
      this.isSharing = true;
      this.showShare = true;
    }
  },
  onShareAppMessage() {
    // 自定义分享内容
    return {
      title: this.shareParams.title,
      desc: this.shareParams.desc,
      path: this.shareParams.url,
      imageUrl: this.shareParams.imgUrl
    };
  }
}

</script>

<style lang="scss" scoped>
.send-btn{
  width: 471rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #F2B187;
  border-radius: 40rpx;
  font-weight: 500;
  font-size: 26rpx;
  color: #FFFFFF;
}
.payMe-btn{
  width: 165rpx;
  height: 60rpx;
  background: #E74D45;
  border-radius: 30rpx;
  font-weight: 500;
  font-size: 26rpx;
  color: #FFFFFF;
}
.success-poppup{
  width: 616rpx;
  height: 442rpx;
  background: #FFFFFF;
  border-radius: 39rpx;
  .buttons{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 109rpx;
    border-top: 2rpx solid #dfdfdf;
    view{
      font-weight: 800;
      font-size: 28rpx;
      width: 50%;
      text-align: center;
      height: 109rpx;
      line-height: 109rpx;
      background: #fff;
      &:nth-child(1) {
        border-radius: 0 0 0 39rpx; 
        border-right: 2rpx solid #dfdfdf;
      }
      &:nth-child(2) {
        color: #E74D45; border-radius: 0 0 39rpx 0;
      }
    }
  }
}
</style>