<template>
	<view>
		<view v-if="scene != 1154" style="padding-bottom: 110rpx">
			<divComponents ref="divComponents" :microId="id" divType="micro" v-if="id && scene != 1154" :showPage="showPage" :isBack="false" :pageSelectPoi="pageSelect" />
		</view>
		<share-single-page v-if="scene == 1154" />
	</view>
</template>

<script>
import divComponents from '@/components/div-components/index.vue';
import shareSinglePage from '@/components/share-single-page/index.vue';
const util = require('utils/util.js');
const app = getApp();
import { EventBus } from '@/utils/eventBus.js';
export default {
	components: {
		divComponents,
		shareSinglePage
	},
	data() {
		return {
			id: '',
			showPage: true,
			scene: '',
			//微页面分类，获取默认选中第几个分类。主要是直播跳转到逛逛直播
			pageSelect: {
				poi: -1,
				other: ''
			}
		};
	},
	onLoad(options) {
		// #ifdef MP-WEIXIN
		let getLaunchOptions = uni.getLaunchOptionsSync();
		this.scene = getLaunchOptions.scene;
		//场景值等于 1154 分享单页模式
		if (this.scene && this.scene == 1154) return;
		// #endif
		util.saveSharerUserCode(options);
		const tabBar = uni.getStorageSync('tabBar');
		if (options.id) {
			this.id = options.id;
		} else {
			if (tabBar[2].pagePathId) {
				// #ifdef APP
				if (tabBar[2].appPagePathId) {
					this.id = tabBar[2].appPagePathId;
				} else {
					this.id = tabBar[2].pagePathId;
				}
				// #endif
				// #ifdef MP
				this.id = tabBar[2].pagePathId;
				// #endif
				console.log('看看微页面的id', this.id);
			}
		}
	},
	onShow() {
		app.shoppingCartCount();
		// 打开一次逛逛,在加进来要重新触发
		const pageSelectPoi = uni.getStorageSync('tab-selectPoi');
		if (pageSelectPoi > 0) {
			console.log('==pageSelectPoi===', pageSelectPoi);
			this.pageSelect.poi = pageSelectPoi;
			this.pageSelect.other = new Date().getTime();
			uni.removeStorage({
				key: 'tab-selectPoi'
			});
		}
		// 再次进入页面时，刷新新人专享数据
		EventBus.$emit('refreshNewCustomerDialog');
	},

	// onHide() {
	// 	setTimeout(()=>{
	// 		this.showPage = false;
	// 	},1000)
	// },

	onShareAppMessage: function () {
		let share = this.$refs.divComponents.shares;
		return share;
	},

	// #ifdef MP-WEIXIN
	//朋友圈
	onShareTimeline(res) {
		let share = this.$refs.divComponents.shares;
		return share;
	},
	// #endif

	onPullDownRefresh() {
		this.$refs.divComponents.pullDownRefresh();
		EventBus.$emit('pageFresh', 'true');
	},

	onPageScroll(res) {
		uni.$emit('vonPageScroll', res);
		uni.$emit('vonVideoPageScroll', res);
	},

	onReachBottom() {
		// if (
		// 	this.$refs.divComponents.reachBottom &&
		// 	typeof this.$refs.divComponents.reachBottom === "function"
		// )
		// 	this.$refs.divComponents.reachBottom();
		EventBus.$emit('divGoodsGroupsReachBottom', 'true');
	},
	onUnload() {
		EventBus.$off('divGoodsGroupsReachBottom');
		// 新人专享弹框
		EventBus.$off('refreshNewCustomerDialog');
	}
};
</script>
