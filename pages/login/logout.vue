<template>
  <view style="background-color: #FFFFFF;">
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">注销账户</block>
    </cu-custom>
    <view class="cu-form-group margin-top phone_margin  group-item">
      <view class="title">手机号</view>
      <view class="flex-sub">
        {{ form.phone }}
      </view>
    </view>
    <view class="cu-form-group password_margin">
      <view class="title">验证码</view>
      <input
        placeholder="请输入验证码"
        name="code"
        maxlength=4
        v-model="form.code"
      ></input>
      <span
        @click="getPhoneCode"
        class="cu-btn bg-gray code_radius"
        :class="'display:' + msgKey"
      >{{msgText}}</span>
    </view>
    <view class="login_padding flex flex-direction">
      <button
        class="cu-btn margin-tb-sm lg"
        :class="'bg-'+theme.backgroundColor"
        @click="btnSure"
      >确认注销</button>
    </view>
  </view>
</template>

<script>
const app = getApp();
const util = require("utils/util.js");
import api from 'utils/api';
import validate from 'utils/validate'
import { loginout } from '@/pages/login/api/loginPhoneCode'

const MSGINIT = "发送验证码",
  MSGSCUCCESS = "${time}秒后可重发",
  MSGTIME = 60;

export default {
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,
      type: '1',
      typeName: '注册',
      form: {
        phone: '',
        code: ''
      },
      msgText: MSGINIT,
      msgTime: MSGTIME,
      msgKey: false,
    };
  },

  components: {

  },
  props: {},

  onLoad (options) {
    const userInfo = uni.getStorageSync('user_info')
    console.log("1111111", userInfo.phone);
    this.form.phone = userInfo.phone;
  },

  onShow () {
  },

  methods: {
    toLogin () {
      if(this.typeName === '修改') {
        uni.navigateBack({
          delta: 1
        })
      } else {
        uni.reLaunch({
          url: '/pages/login/index'
        });
      }
      return
    },
    btnSure() {
      const that = this;
      if (!this.form.code) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      uni.showModal({
        title: '提示',
        content: '请确认是否注销，注销后将不可恢复',
        success(res) {
          if (res.confirm) {
            console.log('用户点击确定');
            that.regSub();
          }
        }
      })
    },
    regSub () {
      let that = this
      loginout(this.form).then(res => {
        uni.clearStorageSync();
        app.resloveOnLaunch();
        uni.showModal({
          title: '提示',
          content: '账号注销成功',
          showCancel: false,
          success (res) {
            uni.reLaunch({
              url: '/pages/home/<USER>'
            })
          }
        });
      });
    },
    getPhoneCode () {
      if (this.msgKey) return
      if (!validate.validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      this.msgKey = true
      api.getPhoneCode({
        type: this.type,
        phone: this.form.phone
      }).then(res => {
        this.msgKey = false
        if (res.code == '0') {
          uni.showToast({
            title: '验证码发送成功',
            icon: 'none',
            duration: 3000
          });
          this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
          this.msgKey = true
          const time = setInterval(() => {
            this.msgTime--
            this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
            if (this.msgTime == 0) {
              this.msgTime = MSGTIME
              this.msgText = MSGINIT
              this.msgKey = false
              clearInterval(time)
            }
          }, 1000)
        } else {

        }
      }).catch(() => {
        this.msgKey = false
      });
    }
  }
};
</script>
<style scoped>
.text-Offline {
  border-bottom: 1px solid black;
}
.code_radius {
  border-radius: 10rpx;
}
.login_padding {
  padding: 80rpx 30rpx 80rpx;
}
.phone_margin {
  margin: 15rpx 30rpx 0rpx;
  border-radius: 10rpx;
}
.password_margin {
  margin: 12rpx 30rpx;
  border-radius: 10rpx;
}
</style>
