<template>
	<view class="flex flex-direction align-center">
		<view style="display: flex; justify-content: center;">
			<image class="login-logo" mode="widthFix" src="https://img.songlei.com/live/login-logo.jpg"></image>
		</view>
		<form @submit="loginSub" :style="{
			width: isPhone? '750rpx':'60vh'
		}">
			<view class="cu-form-group phone_margin">
				<view class="title title-lable  text-xdf">号码</view>
				<input :placeholder-style="{
						'font-size':  isPhone? '28rpx':'18rpx'
					}" placeholder="请输入手机号" name="phone" v-model="form.phone"></input>
			</view>

			<view class="cu-form-group password_margin">
				<view class="title title-lable  text-xdf">密码</view>
				<input :placeholder-style="{
						'font-size':  isPhone? '28rpx':'18rpx'
					}" placeholder="请输入密码" password name="password" v-model="form.password"></input>
			</view>

			<view class="text-right text-gray margin-right margin-top-sm cuIcon-question text-xdf">
				<text @click="toReset">忘记密码</text>
			</view>

			<view class="padding flex flex-direction login_padding">
				<button class="cu-btn margin-btn lg text-xdf" :class="'bg-'+theme.backgroundColor"
					form-type="submit">立即登录</button>
			</view>

			<view class="text-center text-xdf ">
				没有账号？<text class="text-Offline" style="color: #CDAD90;border-color: #CDAD90;"
					@click="toRegister">立即注册</text>
			</view>
			<view class="flex justify-center text-sm align-center margin-top-lg text-xdf" @click="handleChangeRead"
				v-show="showPrivacyPolicy">
				<text v-if="checkedRead" style="font-size:36rpx;font-weight: bold;color:  #CDAD90;"
					class="cuIcon-roundcheckfill text-lg"></text>
				<text v-else class="unchecked">
				</text>
				<view class="flex justify-center align-center" style="margin-left: 8rpx;">
					<text>我已阅读</text>
					<navigator class="text-blue " style="color: #CDAD90;"
						@click.stop="navTo('/pages/public/webview/webview?title=用户协议&url=' + protocolUrl)">用户协议
					</navigator>和<navigator class="text-blue" style="color: #CDAD90;"
						@click.stop="navTo('/pages/public/webview/webview?title=隐私政策&url=' + privacyPolicyUrl)">隐私政策
					</navigator>
				</view>
			</view>
		</form>
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api'
	import validate from 'utils/validate'
	import __config from '@/config/env'; // 配置文件
	import {
		mapState
	} from 'vuex';
	// #ifdef APP-PLUS
	import {
		startPush,
		setPushAlias,
	} from '@/api/push';
	// #endif
	import {
			senLogin
	} from "@/public/js_sdk/sensors/utils.js";
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				form: {},
				showPrivacyPolicy: __config.showPrivacyPolicy,
				privacyPolicyUrl: __config.privacyPolicyUrl,
				protocolUrl: __config.protocolUrl,
				checkedRead: false,

			};
		},

		components: {

		},
		props: {
			reUrl: { //重定向页面
				type: String,
				default: '/pages/home/<USER>'
			}
		},

		computed: {
			...mapState(['isPhone']),
		},

		methods: {
			toReset() {
				uni.redirectTo({
					url: '/pages/login/register?type=reset'
				});
			},
			toRegister() {
				uni.redirectTo({
					url: '/pages/login/register'
				});
			},
			loginSub(e) {

					if (!validate.validateMobile(this.form.phone)) {
						uni.showToast({
							title: '请输入正确的手机号码',
							icon: 'none',
							duration: 3000
						});
						return;
					}
					if (!this.form.password) {
						uni.showToast({
							title: '请输入密码',
							icon: 'none',
							duration: 3000
						});
						return;
					}
					if (!this.checkedRead) {
						uni.showToast({
							title: '请先勾选用户协议',
							icon: 'none',
							duration: 3000
						});
						return;
					}
					api.login(this.form).then(res => {
						let userInfo = res.data;
						uni.setStorageSync('third_session', userInfo.thirdSession);
						uni.setStorageSync('user_info', userInfo);
						// #ifdef APP-PLUS
						if(uni.getStorageSync('push_status')==null ||uni.getStorageSync('push_status')== ""|| uni.getStorageSync('push_status')){
							setPushAlias(userInfo.id);
							uni.setStorageSync('push_status', true);
						}
						// #endif
						senLogin(userInfo);
						//登录完成跳到首页
						uni.reLaunch({
							url: this.reUrl &&  this.reUrl!='business-circle-plugin' ? decodeURIComponent(this.reUrl) : '/pages/home/<USER>'
						});
						//获取购物车数量
						app.shoppingCartCount()
					});
				
			},

			handleChangeRead() {
				this.checkedRead = !this.checkedRead;
				// #ifdef APP-PLUS
				if (uni.getStorageSync('push_status') == null || uni.getStorageSync('push_status') == "" || uni
					.getStorageSync('push_status')) {
					startPush();
					uni.setStorageSync('push_status', true);
				}
				// #endif
			},
			navTo(url) {
				uni.navigateTo({
					url
				})
			}
		}
	};
</script>
<style scoped>
	.text-Offline {
		border-bottom: 1px solid black;
	}

	.margin-btn {
		margin-top: 50rpx;
		margin-bottom: 20rpx;
	}

	.login_padding {
		padding: 30rpx 30rpx 30rpx;
	}

	.phone_margin {
		margin: -20rpx 30rpx 0rpx;
		border-radius: 10rpx;
	}

	.password_margin {
		margin: 12rpx 30rpx;
		border-radius: 10rpx;
	}

	.login-logo {
		width: 300rpx;
		margin-top: 8vh;
	}

	.title-lable {
		width: 220rpx;
	}

	.unchecked {
		width: 36rpx;
		height: 36rpx;
		background: #FFFFFF;
		border: 1px solid #999999;
		border-radius: 50%;
	}

	@media (min-width: 549px) {
		.login-logo {
			width: 240rpx;
			margin-top: 2vh;
		}

		.title-lable {
			width: 130rpx;
		}

		.cu-btn {
			height: 40rpx;
		}

		.unchecked {
			width: 22rpx;
			height: 22rpx;
		}

		.login_padding {
			padding: 10rpx 30rpx 10rpx;
		}

		.phone_margin {
			margin: -20rpx 18rpx 0rpx;
			border-radius: 10rpx;
		}

		.password_margin {
			margin: 7rpx 18rpx;
			border-radius: 6rpx;
		}
	}
</style>