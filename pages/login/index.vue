<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true"></cu-custom>
		<view class="nav text-center login padding-bottom-lg" :class="'bg-' + theme.backgroundColor">
			<view :class="'cu-item ' + (1 == TabCur ? 'text-white cur' : '')" @click="tabSelect(1)" class="login-type">
				<text class="text-xdf">快速登录</text>
			</view>
			<view :class="'cu-item ' + (0 == TabCur ? 'text-white cur' : '')" @click="tabSelect(0)" class="login-type">
				<text class="text-xdf">账号登录</text>
			</view>
		</view>
		<!-- #ifdef APP -->
		<phoneCode v-if="TabCur === 1" :reUrl="reUrl" :fromChannel="fromChannel" :loginStyle="loginStyle"  @receivelogin="receivelogin"
			@univerify="handleLogin"></phoneCode>
		<!-- #endif -->
		<!-- #ifdef MP -->
		<authorization :reUrl="reUrl" v-if="TabCur === 1" :fromChannel="fromChannel" @receivelogin="receivelogin"
			@methonChange="methonChange"></authorization>
		<!-- <phoneCode v-if="TabCur === 1" :reUrl="reUrl" :fromChannel="fromChannel" @receivelogin='receivelogin'></phoneCode> -->
		<!-- #endif -->
		<userLogin v-if="TabCur == 0" :reUrl="reUrl"></userLogin>
	</view>
</template>
<script>
	const app = getApp();
	const util = require('utils/util.js');
	import api from 'utils/api';
	import userLogin from './userlogin';
	import authorization from './authorization';
	//手机号验证码登录
	import phoneCode from './components/phoneCodes';
	import register from './register';

	// #ifdef APP-PLUS
	const CryptoJS = require('crypto-js');
	import {
		setPushAlias
	} from '@/api/push';
	// #endif
	import {
			senLogin
	} from "@/public/js_sdk/sensors/utils.js";
	const version = require('utils/version.js');
	export default {
		data() {
			return {
				maLoginType: false, //判断是否手机验证码登录
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				TabCur: 1,
				reUrl: '',
				//有数统计使用
				page_title: '登录',
				fromChannel: '', //到登录页面的来源
				loginconfirm: true,
				loginStyle: 1, //1一键登录  2.其他方式登录
			};
		},
		components: {
			userLogin,
			register,
			phoneCode,
			authorization
		},
		props: {},
		onLoad(options) {
			const {
				fromChannel
			} = options;
			this.fromChannel = fromChannel;
			let reUrl = options.reUrl;
			this.reUrl = reUrl;
			
			let that = this;
			// api.getLivingTag().then(res => {
			// 	if (res && res.data) {
			// 		if(res.data.maLoginType=='2'){
			// 			that.maLoginType =  true
			// 		} else{
			// 			that.maLoginType  =  false
			// 			if(version.getversion()=='1'){
			// 				that.TabCur=2
			// 			}else {
			// 				that.TabCur=2
			// 			}
			// 		}
			// 	}
			// });
		},
		
		methods: {
			handleLogin() {
				console.log("====handleLogin==");
				let that = this;
				uni.preLogin({
					provider: "univerify",
					success() {
						//预登录成功
						uni.login({
							provider: "univerify",
							univerifyStyle: {
								// 自定义登录框样式
								//参考`univerifyStyle 数据结构`
								fullScreen: true, // 是否全屏显示，默认值： false
								title: "快速登录",
								backgroundColor: "#ffffff", // 授权页面背景颜色，默认值：#ffffff
								icon: {
									path: "../../static/my/头像.png", // 自定义显示在授权框中的logo，仅支持本地图片 默认显示App logo
								},
								phoneNum: {
									color: "#000000", // 手机号文字颜色 默认值：#000000
									fontSize: "18", // 手机号字体大小 默认值：18
								},
								slogan: {
									color: "#8a8b90", //  slogan 字体颜色 默认值：#8a8b90
									fontSize: "12", // slogan 字体大小 默认值：12
								},
								// 一键登录
								authButton: {
									normalColor: "#3479f5", // 授权按钮正常状态背景颜色 默认值：#3479f5
									highlightColor: "#2861c5", // 授权按钮按下状态背景颜色 默认值：#2861c5（仅ios支持）
									disabledColor: "#73aaf5", // 授权按钮不可点击时背景颜色 默认值：#73aaf5（仅ios支持）
									textColor: "#ffffff", // 授权按钮文字颜色 默认值：#ffffff
									title: "本机号码一键登录", // 授权按钮文案 默认值：“本机号码一键登录”
								},
								// 其他登录方式
								otherLoginButton: {
									visible: "true", // 是否显示其他登录按钮，默认值：true
									normalColor: "#f8f8f8", // 其他登录按钮正常状态背景颜色 默认值：#f8f8f8
									highlightColor: "#dedede", // 其他登录按钮按下状态背景颜色 默认值：#dedede
									textColor: "#000000", // 其他登录按钮文字颜色 默认值：#000000
									title: "其他号码登录/注册", // 其他登录方式按钮文字 默认值：“其他登录方式”
									borderWidth: "1px", // 边框宽度 默认值：1px（仅ios支持）
									borderColor: "#c5c5c5", //边框颜色 默认值： #c5c5c5（仅ios支持）
								},
								// 自定义按钮登录方式
								buttons: {
									// 仅全屏模式生效，配置页面下方按钮  （3.1.14+ 版本支持）
									iconWidth: "45px", // 图标宽度（高度等比例缩放） 默认值：45px
									list: [{
											provider: "apple",
											iconPath: "/static/test.jpg", // 图标路径仅支持本地图片
										},
										{
											provider: "weixin",
											iconPath: "/static/test.jpg",
										},
									],
								},
								privacyTerms: {
									defaultCheckBoxState: "true", // 条款勾选框初始状态 默认值： true
									textColor: "#8a8b90", // 文字颜色 默认值：#8a8b90
									termsColor: "#1d4788", //  协议文字颜色 默认值： #1d4788
									prefix: "我已阅读并同意", // 条款前的文案 默认值：“我已阅读并同意”
									suffix: "并使用本机号码登录", // 条款后的文案 默认值：“并使用本机号码登录”
									fontSize: "12", // 字体大小 默认值：12,
									privacyItems: [
										// 自定义协议条款，最大支持2个，需要同时设置url和title. 否则不生效
										{
											url: "https://", // 点击跳转的协议详情页面
											title: "用户服务协议", // 协议名称
										},
									],
								},
							},
							success(res) {
								let params = {
									name: "getPhoneNumber",
									data: {
										openid: res.authResult.openid,
										access_token: res.authResult.access_token
									}
								}
								// console.log('------------params-------------',params);
								//在得到access_token和openid后，通过callfunction调用云函数
								uniCloud.callFunction(params).then(res2 => {
									// 有用的信息只返回一个手机号
									that.encryptPhone(res2.result
									.phoneNumber); //加密手机号               
								}).catch((err) => {
									console.error(err);
									console.log(err.errCode);
									console.log(err.errMsg);
									uni.showToast({
										title: err.errMsg,
										icon: "none"
									})
									setTimeout(() => {
										uni.closeAuthView() //关闭一键登录弹出窗口
									}, 500)
								})
							},
							// 当用户点击自定义按钮时，会触发uni.login的fail回调[点击其他登录方式，可以跳转页面，或执行事件]
							fail(res) {
								// 登录失败
								console.log(res.code);
								if (res.code == "30002") {
									//用户点击了其他登录方式
									uni.closeAuthView() //关闭一键登录弹出窗口
									that.loginStyle=2;
								}
								if (res.code == "30003") {
									//用户关闭验证界面
									that.goBack();
								}
								if (res.code == "30006") {
									//一键登录失败 安卓底部返回键会触发这里
									that.goBack();
								}
							},
						});
					},
					fail(res) {
						// 预登录失败
						// 不显示一键登录选项（或置灰）
						// 根据错误信息判断失败原因，如有需要可将错误提交给统计服务器
						// console.error("=====res=====5", res);
						// uni.showModal({
						//   title: "提示",
						//   content: JSON.stringify(res),
						//   showCancel: true,
						// });
						if (res.code == "20202") {
							// 终端未开启SIM流量
							uni.showModal({
								title: "提示",
								content: "一键登录请先开启数据流量",
								showCancel: false,
							});
						}
						if (res.code == "30005") {
							// 预登录失败
							uni.showModal({
								title: "提示",
								content: "一键登录请先开启数据流量/检查设备是否支持",
								showCancel: false,
							});
						}
						if (res.code == "5000") {
							// 取号失败，请检查SIM卡是否停机欠费
							uni.showToast({
								title: "取号失败,请检查SIM卡是否停机欠费!",
								icon: "none"
							});
						}
						if (res.code == "40101") {
							// 移动-源IP鉴权失败
							uni.showToast({
								title: "检查是否正常运营商手机卡，重新尝试!!",
								icon: "none"
							});
						}
						if (res.code == "40201") {
							// 联通-源IP鉴权失败
							uni.showToast({
								title: "检查是否正常运营商手机卡，重新尝试!",
								icon: "none"
							});
						}
						if (res.code == "40301") {
							// 电信-源IP鉴权失败
							uni.showToast({
								title: "取号失败,请检查SIM卡是否停机欠费!",
								icon: "none"
							});
						}
					},
				});
			},
			goBack() {
				//有上一级返回上一级，如果是扫码就跳转首页
				const pages = getCurrentPages();
				const pageUrl = pages[pages.length - 2]; // 上一个页面
				if (pageUrl) {
					uni.navigateBack({
						delta: 1,
					});
				} else {
					uni.closeAuthView(); //关闭一键登录弹出窗口
				}
			},
			encryptPhone(phoneNumber) {
				const secret = 'songleijituan016' // 密钥，前后端要一致
				const secretKey = CryptoJS.SHA256(secret).toString().substring(0, 32)
				const iv = CryptoJS.lib.WordArray.random(16);
				const cipher = CryptoJS.AES.encrypt(
					CryptoJS.enc.Utf8.parse(phoneNumber),
					CryptoJS.enc.Utf8.parse(secretKey), {
						iv: iv,
						mode: CryptoJS.mode.CBC, // 明确指定CBC模式
						padding: CryptoJS.pad.Pkcs7
					}
				).toString();
				const encryptedData = `${iv.toString(CryptoJS.enc.Base64)}:${cipher}`;
				this.autologin(encryptedData);
			},
			autologin(encryptedData) {
				let params = {
					encryptedData,
				}
				api.autologinApi(params).then(res => {
					let userInfo = res.data;
					console.error("======autologin=======", userInfo);
					// #ifdef APP-PLUS
					setPushAlias(userInfo.id);
					// #endif
					uni.setStorageSync('third_session', userInfo.thirdSession);
					uni.setStorageSync('user_info', userInfo);
					uni.closeAuthView(); //关闭一键登录弹出窗口
					senLogin(userInfo);
					//登录完成跳到首页
					uni.reLaunch({
						url: this.reUrl ? decodeURIComponent(this.reUrl) : '/pages/home/<USER>'
					});
					//获取购物车数量
					app.shoppingCartCount()
				});
			},
			tabSelect(index) {
				this.TabCur = index;
			},
			//    接收组件的方法
			receivelogin(type) {
				console.log('------', type);
				this.loginconfirm = type;
			},
			methonChange() {
				this.maLoginType = true;
			}
		},
		onUnload() {
			// console.log('出发了')
			if (this.loginconfirm === false) {
				// 判断跳转页面如果是false
				uni.switchTab({
					url: '/pages/home/<USER>'
				});
			} else {
				//否则的是  true
			}
		}
	};
</script>
<style>
	page {
		background-color: #ffffff;
	}
</style>
<style scoped>
	.login {
		margin-left: -20rpx;
	}

	.login-type {
		line-height: 60rpx;
		height: 60rpx;
		margin: 0 30rpx;
		padding: 0;
	}

	@media (min-width: 549px) {
		.login-type {
			line-height: 36rpx;
			height: 36rpx;
			margin: 0 18rpx;
			padding: 0;
		}
	}
</style>