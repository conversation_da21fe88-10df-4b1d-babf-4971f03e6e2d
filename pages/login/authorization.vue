<template>
	<view style="background-color: #FFFFFF; height: 100vh;">
		<view style="display: flex; justify-content: center; padding-top:18vh;padding-bottom:10vh;">
			<image style="width: 400rpx;" mode="widthFix" src="https://img.songlei.com/live/login-logo.jpg"></image>
		</view>
		<view class="btn_padding flex flex-direction">
			<button v-if="checkedRead" class="cu-btn margin-tb-sm lg" style="margin: 0 60rpx; font-size: 30rpx;"
				:class="'bg-'+theme.backgroundColor" open-type="getPhoneNumber"
				@getphonenumber="getPhoneNumber">手机号快捷登录</button>

			<button v-else class="cu-btn margin-tb-sm lg" style="margin: 0 60rpx; font-size: 30rpx;"
				:class="'bg-'+theme.backgroundColor" @click="handleLogin">手机号快捷登录</button>
		</view>

		<view class=" flex justify-center align-center text-sm " style="font-size: 28rpx;margin-top: 35rpx;"
			v-show="showPrivacyPolicy" @click="handleChangeRead">
			<text v-if="checkedRead" style="font-size:36rpx;font-weight: bold;color:  #CDAD90;"
				class="cuIcon-roundcheckfill"></text>
			<view v-else
				style="width: 36rpx;height: 36rpx;background: #FFFFFF;border: 1px solid #999999;border-radius: 50%;">
			</view>
			<text style="margin-left: 8rpx;">我已阅读</text>
			<text style="color: #CDAD90;"
				@click.stop="navTo('/pages/public/webview/webview?title=用户协议&url='+protocolUrl)">用户协议</text>
			和<text class="text-blue text-sm" style="color: #CDAD90;"
				@click.stop="navTo('/pages/public/webview/webview?title=隐私政策&url=' + privacyPolicyUrl)">隐私政策</text>
		</view>
		<view class="">

			<mp-half-screen-dialog bindbuttontap="buttontap" :show="show" :maskClosable="false" @close='closemessage'>
				<view slot="title">授权用户头像和昵称</view>
				<view slot="desc" style="margin-bottom: -50rpx;">
					<form @submit="confirm">
						<view class="">
							<button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
								<image :src="avatarUrl" style="width: 56px;height: 56px;"></image>
							</button>
						</view>
						<view class="" style="margin-left: 30rpx;height: 63rpx;display: flex;align-items: center;">
							<view style="display: block;color: var(--weui-FG-0);font-weight: 700;font-size: 30rpx;">
								用户昵称:
							</view>
							<input
								style="margin-left:22rpx;display: flex; align-items: center;height: 60rpx;font-size: 29rpx;"
								type="nickname" name="nickName" v-model="unserName" placeholder="请输入昵称"
								@change="getunserName" />
						</view>
						<view slot="footer" style="display: flex;margin-top: 40rpx;">
							<button type="primary" plain="true" style="width: 200rpx;height: 80rpx;line-height: 80rpx;"
								@click="skip">取消</button>
							<button type="primary" formType="submit"
								style="width: 200rpx;height: 80rpx;line-height: 80rpx;">确认</button>
						</view>
					</form>
				</view>
			</mp-half-screen-dialog>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api'
	import validate from 'utils/validate'
	import __config from '@/config/env'; // 配置文件
	// #ifdef APP-PLUS
	import {
		startPush,
		setPushAlias,
	} from '@/api/push';
	// #endif
	// #ifdef MP-WEIXIN
	const businessCirclePlugin = requirePlugin('business-circle-plugin');
	// #endif
	import {
		getWxTemplate
	} from "@/api/message.js";
	import {
			senLogin
	} from "@/public/js_sdk/sensors/utils.js";
	const MSGINIT = "发送验证码",
		MSGSCUCCESS = "${time}秒后可重发",
		MSGTIME = 60;
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				form: {},
				msgText: MSGINIT,
				msgTime: MSGTIME,
				msgKey: false,
				showPrivacyPolicy: __config.showPrivacyPolicy,
				privacyPolicyUrl: __config.privacyPolicyUrl,
				protocolUrl: __config.protocolUrl,
				checkedRead: false,
				userinfo: {},
				rawData: '',
				phonenumdata: '',
				avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
				unserName: "",
				show: false,
				buttons: [{
						type: 'default',
						className: '',
						text: '跳过',
						value: 0
					},
					{
						type: 'primary',
						className: '',
						text: '确认',
						value: 1
					}
				],
				// 微信授权登录流程日志上报
				// #ifdef MP-WEIXIN
				logs: [],
				startTime: new Date().getTime()
				// #endif
			};
		},
		components: {},
		props: {
			reUrl: { //重定向页面
				type: String,
				default: '/pages/home/<USER>'
			},
			fromChannel: {
				type: String,
			}
		},
		destroyed() {
			// #ifdef MP-WEIXIN
			wx.reportEvent('wxdata_perf_monitor', {
				wxdata_perf_monitor_id: 'login-authorization',
				wxdata_perf_monitor_level: 1,
				wxdata_perf_error_code: 0,
				wxdata_perf_error_msg: 'success',
				wxdata_perf_cost_time: new Date().getTime() - this.startTime,
				wxdata_perf_extra_info1: JSON.stringify(this.logs)
			});
			// #endif
		},
		methods: {
			getlogin() {
				uni.setStorageSync('third_session', this.userInfo.thirdSession);
				uni.setStorageSync('user_info', this.userInfo);
				const openId = this.userInfo.openId;
				let reUrl = this.reUrl;
				const decodeUrl = decodeURIComponent(reUrl);
				if (reUrl == 'business-circle-plugin') {
					// #ifdef MP-WEIXIN
					api.erpOpenIdBind({
						channelId: 'SMART'
					}).then(res => {
						businessCirclePlugin.getLocation(openId).then(
							res => {
								if (res.return_code == 0) {

									businessCirclePlugin.getAuthStatus(openId, __config.mchId)
										.then(
											authRes => {
												reUrl =
													`plugin://business-circle-plugin/index?mch_id=${__config.mchId}&openid=${openId}&member_status=${authRes.status}`;
												uni.navigateTo({
													url: reUrl
												});
											})
								}
							})
					}).catch(e => {
						console.error(e);
					});
					// #endif

					// #ifndef MP-WEIXIN
					uni.redirectTo({
						url: reUrl ? decodeURIComponent(reUrl) : '/pages/home/<USER>'
					});
					// #endif
				} else if (decodeUrl && decodeUrl.startsWith('/pages/user/user-address/form/index')) {
					//register true表示是新客户
					if (this.userInfo.register) {
						uni.reLaunch({
							url: decodeUrl
						});
					} else {
						uni.reLaunch({
							url: '/pages/home/<USER>'
						});
					}
				} else {
					uni.reLaunch({
						url: reUrl ? decodeUrl : '/pages/home/<USER>'
					});
				}
				app.shoppingCartCount()
			},
			skip() {
				this.show = false
				this.getlogin()
				this.userInfoGet()
			},
			confirm(e) {
				// #ifdef MP
				getWxTemplate({
					type: 8
				}).then(res => {
					uni.requestSubscribeMessage({
						tmplIds: res.data,
						complete: () => {
							this.confirmNext(e);
						}
					})
				})
				// #endif
				// #ifndef MP
				this.confirmNext(e);
				// #endif
			},
			confirmNext(e) {
				// #ifdef MP
				this.logs.push({
					wxdata_perf_step_id: '登录步骤3:获取头像昵称之后的逻辑',
					wxdata_perf_error_msg: 'success',
					wxdata_perf_cost_time: new Date().getTime() - this.startTime,
					wxdata_perf_extra_info1: "关闭获取头像昵称的弹框，更新用户资料" + JSON.stringify({
						id: this.userInfo.id,
						nickName: e.detail.value.nickName,
						sex: this.userInfo.sex,
						headimgUrl: this.userInfo.headimgUrl
					})
				});
				// #endif
				api.userInfoUpdate({
					id: this.userInfo.id,
					nickName: e.detail.value.nickName,
					sex: this.userInfo.sex,
					headimgUrl: this.userInfo.headimgUrl
				});
				this.show = false
				this.getlogin()
				this.userInfoGet()
			},
			async userInfoGet() {
				const res = await api.userInfoGet();
				this.userInfo = res.data;
				uni.setStorageSync('user_info', this.userInfo);
				console.log("==this.userInfo==========", this.userInfo);
				senLogin(this.userInfo);
			},
			closemessage(e) {
				this.show = false
				this.getlogin()
			},
			open() {
				this.show = true
			},
			buttontap(e) {
				console.log(e.detail)
			},
			getuser() {
				let that = this
				wx.getUserProfile({
					desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: (detail) => {
						// 如果先获取的手机号  (如果先获取的手机号并且拿到手机号)
						if (that.phonenumdata) {
							that.phonenumdata.rawData = detail.rawData
							that.login(that.phonenumdata)
						} else {
							//获取用户信息并存储用户信息 
							if (detail.rawData) {
								that.rawData = detail.rawData
							}
						}
					}
				})
			},
			handleChangeRead() {
				this.checkedRead = !this.checkedRead;
				console.log(this.checkedRead, 'this.checkedRead');
				// #ifdef APP-PLUS
				startPush();
				// #endif
			},
			getPhoneNumber(e) {
				let that = this
				if (+e.detail.errno === 1400001) {
					// if(true) {
					uni.showToast({
						title: '获取手机号失败，切换为验证码登录',
						icon: 'none'
					})
					setTimeout(() => {
						this.$emit('methonChange');
					}, 1500)
					return;
				}

				if (this.rawData) {
					if (e.detail.encryptedData) {
						const data = e.detail;
						data.rawData = this.rawData
						that.login(data)
					}
				} else {
					if (e.detail.encryptedData) {
						this.phonenumdata = e.detail
						console.log(this.phonenumdata, 'phonenumdata')
						this.login(this.phonenumdata)
					}
				}

			},
			async login(data) {
				if (this.fromChannel) {
					data.channelId = this.fromChannel
				}
				if (app.globalData.sf) {
					data.channelId = getApp().globalData.sf
				} else if (uni.getStorageSync('cabinetGroup')) {
					console.log(uni.getStorageSync('cabinetGroup'), ' uni.getStorageSync1')
					data.channelId = uni.getStorageSync('cabinetGroup')
				}
				data.channelId = data.channelId || 'SONGSHU'; //没有场景值，默认传SONGSHU

				// #ifdef MP
				this.logs.push({
					wxdata_perf_monitor_id: '登录步骤1:准备调loginma接口',
					wxdata_perf_error_msg: 'success',
					wxdata_perf_cost_time: new Date().getTime() - this.startTime,
					wxdata_perf_extra_info1: "调loginma接口传的参数:" + JSON.stringify(data)
				});
				// #endif

				try {
					const res = await api.loginByPhoneMa(data);
					this.$emit("receivelogin", true);
					this.phonenumdata = ''
					this.rawData = ''
					this.userInfo = res.data;
					this.show = true
					this.avatarUrl = res.data.headimgUrl
					this.unserName = res.data.nickName;
					// #ifdef MP
					this.logs.push({
						wxdata_perf_monitor_id: '登录步骤2:调loginma接口成功',
						wxdata_perf_error_msg: 'success',
						wxdata_perf_cost_time: new Date().getTime() - this.startTime,
						wxdata_perf_extra_info1: "调loginma接口返回值:" + JSON.stringify(res.data)
					});
					// #endif
					// #ifdef APP-PLUS
					setPushAlias(this.userInfo.id);
					// #endif
					
				} catch (e) {
					console.error(e);
					// #ifdef MP
					this.logs.push({
						wxdata_perf_monitor_id: '登录步骤2:调loginma接口有报错',
						wxdata_perf_error_msg: 'erroe',
						wxdata_perf_cost_time: new Date().getTime() - this.startTime,
						wxdata_perf_extra_info1: "调loginma接口错误信息:" + JSON.stringify(e)
					});
					// #endif
				}

			},
			handleLogin() {
				if (!this.checkedRead) {
					uni.showToast({
						title: '请先勾选用户协议',
						icon: 'none',
						duration: 3000
					});
				}
			},
			getPhoneCode() {
				if (this.msgKey) return
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.msgKey = true
				api.getPhoneCode({
					type: '1',
					phone: this.form.phone
				}).then(res => {
					this.msgKey = false
					if (res.code == '0') {
						uni.showToast({
							title: '验证码发送成功',
							icon: 'none',
							duration: 3000
						});
						this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						this.msgKey = true
						const time = setInterval(() => {
							this.msgTime--
							this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
							if (this.msgTime == 0) {
								this.msgTime = MSGTIME
								this.msgText = MSGINIT
								this.msgKey = false
								clearInterval(time)
							}
						}, 1000)
					} else {

					}
				}).catch(() => {
					this.msgKey = false
				});
			},
			loginSub(e) {
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (!this.form.code) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				api.loginByPhone(this.form).then(res => {
					let userInfo = res.data;
					console.error("======loginByPhone=======", userInfo);
					// #ifdef APP-PLUS
					setPushAlias(userInfo.id);
					// #endif
					uni.setStorageSync('third_session', userInfo.thirdSession);
					uni.setStorageSync('user_info', userInfo);
					senLogin(this.userInfo);
					//登录完成跳到首页
					uni.reLaunch({
						url: this.reUrl ? decodeURIComponent(this.reUrl) : '/pages/home/<USER>'
					});
					//获取购物车数量
					app.shoppingCartCount()
				});
			},
			onChooseAvatar(e) {
				const {
					avatarUrl
				} = e.detail
				console.log(avatarUrl, 'avatarUrlavatarUrl')
				this.avatarUrl = avatarUrl
				this.uploadAvatar(avatarUrl)
			},
			getunserName() {
				// if(this.unserName!=this.unserName){
				console.log(this.unserName, 'this.unserName')
				api.userInfoUpdate({
					id: this.userInfo.id,
					nickName: this.unserName,
					sex: this.userInfo.sex,
					headimgUrl: this.userInfo.headimgUrl
				});
				// }

			},
			uploadAvatar(filePath) { // 上传头像
				let _url = '/mallapi/file/upload';
				//#ifndef H5
				_url = __config.basePath + _url
				//#endif
				let that = this
				uni.showLoading({
					title: '上传中'
				});
				uni.uploadFile({
					header: {
						//#ifdef H5
						'client-type': util.isWeiXinBrowser() ? 'H5-WX' : 'H5', //客户端类型普通H5或微信H5
						'tenant-id': getApp().globalData.tenantId,
						'app-id': getApp().globalData.appId ? getApp().globalData.appId : '', //微信h5有appId
						//#endif
						//#ifdef MP-WEIXIN
						'client-type': 'MA', //客户端类型小程序
						'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
						//#endif
						//#ifdef APP-PLUS
						'client-type': 'APP', //客户端类型APP
						'tenant-id': getApp().globalData.tenantId,
						//#endif
						'third-session': uni.getStorageSync('third_session') ? uni
							.getStorageSync('third_session') : '',
					},
					url: _url,
					filePath: filePath,
					name: 'file',
					formData: {
						'fileType': 'image',
						'dir': 'headimg/'
					},
					success: (uploadFileRes) => {
						if (uploadFileRes.statusCode == '200') {
							that.userInfo.headimgUrl = JSON.parse(uploadFileRes.data).link
							api.userInfoUpdate({
								id: this.userInfo.id,
								nickName: this.userInfo.nickName,
								sex: this.userInfo.sex,
								headimgUrl: JSON.parse(uploadFileRes.data).link
							}).then(res => {
								// uni.navigateBack({
								// 	delta: 1
								// });
							});
						} else {
							uni.showModal({
								title: '提示',
								content: '上传失败：' + uploadFileRes.data,
								success(res) {}
							});
						}
					},
					fail: (err) => {
						console.log(err)
					},
					complete: () => {
						uni.hideLoading()
					}
				});
			},

			navTo(url) {
				uni.navigateTo({
					url
				})
			}
		}
	};
</script>
<style scoped>
	.text_top {
		margin-top: 80rpx;
	}

	.btn_padding {
		padding: 70rpx 30rpx 0rpx;
	}

	.avatar-wrapper {
		padding: 0;
		width: 56px !important;
		height: 56px !important;
		/* margin-top: 40px; */
		margin-bottom: 30px;
		border: none;
	}

	.items {
		display: flex;
		align-items: center;
		justify-content: center;

	}
</style>