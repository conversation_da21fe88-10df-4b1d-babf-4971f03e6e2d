<template>
	<view class="flex flex-direction align-center">
		<view style="display: flex; justify-content: center;">
			<image class="login-logo" mode="widthFix" src="https://img.songlei.com/live/login-logo.jpg"></image>
		</view>
		<view :style="{
			width: isPhone? '750rpx':'60vh'
		}">
			<view v-if="loginStyle==1"  class="padding flex flex-direction">
				<button class="cu-btn mlr-xxxxl lg text-xdf" :class="'bg-'+theme.backgroundColor"
				  @click="toUniverify">一键登录</button>
			</view>
			
			<form v-else-if="loginStyle==2" @submit="loginSub">
				<view class="cu-form-group margin-top">
					<view class="title title-lable  text-xdf">手机号码</view>
					<input :placeholder-style="{
						'font-size':  isPhone? '28rpx':'18rpx'
					}" placeholder="请输入手机号" name="phone" v-model="form.phone"></input>
				</view>
				<view class="cu-form-group">
					<view class="title title-lable text-xdf">验证码</view>
					<input :placeholder-style="{
						'font-size':  isPhone? '28rpx':'18rpx'
					}" placeholder="请输入验证码" name="code" maxlength=4 v-model="form.code"></input>
					<span @click="getPhoneCode" class="cu-btn bg-gray text-xdf"
						:class="'display:' + msgKey">{{msgText}}</span>
				</view>
				<view class="padding flex flex-direction">
					<button class="cu-btn margin-tb-sm lg text-xdf" :class="'bg-'+theme.backgroundColor"
						form-type="submit">立即登录</button>
				</view>
				<view class="text-center text-xdf">
					其他方式？
					<text class="text-Offline" style="color: #CDAD90;border-color: #CDAD90" @click="toUniverify">
						一键登录
					</text>
				</view>
			</form>
			<view class="flex justify-center align-center margin-top-lg text-xdf" v-show="showPrivacyPolicy"
				@click="handleChangeRead">
				<text v-if="checkedRead" style="font-weight: bold;color: #CDAD90;"
					class="cuIcon-roundcheckfill text-lg"></text>
				<view v-else class="unchecked">
				</view>
				<text style="margin-left: 8rpx;">我已阅读</text>
				<text style="color: #CDAD90;"
					@click.stop="navTo('/pages/public/webview/webview?title=用户协议&url='+protocolUrl)">用户协议</text>
				和<text class="text-blue" style="color: #CDAD90;"
					@click.stop="navTo('/pages/public/webview/webview?title=隐私政策&url=' + privacyPolicyUrl)">隐私政策</text>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import {
		mapState
	} from 'vuex';
	const util = require("utils/util.js");
	import validate from 'utils/validate'
	import __config from '@/config/env'; // 配置文件
	import api from 'utils/api';
	// #ifdef APP-PLUS
	import {
		startPush,
		setPushAlias,
	} from '@/api/push';
	// #endif
	// #ifdef MP-WEIXIN
	const businessCirclePlugin = requirePlugin('business-circle-plugin');
	// #endif
	const MSGINIT = "发送验证码",
		MSGSCUCCESS = "${time}秒后可重发",
		MSGTIME = 60;

	import {
			senLogin
	} from "@/public/js_sdk/sensors/utils.js";
	const version = require("utils/version.js");
	import {
		loginPhoneCode,
		loginByPhone
	} from "@/pages/login/api/loginPhoneCode"

	export default {
		props: {
			reUrl: { //重定向页面
				type: String,
				default: '/pages/home/<USER>'
			},
			fromChannel: {
				type: String,
			},
			loginStyle: {
				type: Number,
			}
		},
		data() {
			return {
				msgText: MSGINIT,
				msgTime: MSGTIME,
				msgKey: false,
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				showPrivacyPolicy: true,
				showPrivacyPolicy: __config.showPrivacyPolicy,
				checkedRead: false,
				privacyPolicyUrl: __config.privacyPolicyUrl,
				protocolUrl: __config.protocolUrl,
				form: {},
			}
		},

		computed: {
			...mapState(['isPhone']),
		},
		methods: {
			handleChangeRead() {
				this.checkedRead = !this.checkedRead;
				// #ifdef APP-PLUS
				if (uni.getStorageSync('push_status') == null || uni.getStorageSync('push_status')) {
					startPush();
					uni.setStorageSync('push_status', true);
				}
				// #endif
			},
			loginSub(e) {
				if (this.checkedRead === false) {
					uni.showToast({
						title: '请先勾选用户协议',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (!this.form.code) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (this.fromChannel) {
					this.form.channelId = this.fromChannel
				}
				if (app.globalData.sf) {
					this.form.channelId = getApp().globalData.sf
				} else if (uni.getStorageSync('cabinetGroup')) {
					console.log(uni.getStorageSync('cabinetGroup'), ' uni.getStorageSync1')
					this.form.channelId = uni.getStorageSync('cabinetGroup')
				}
				this.form.channelId = this.form.channelId || 'SONGSHU'; //没有场景值，默认传SONGSHU
				console.log("app注册信息, " + this.form);
				loginByPhone(this.form).then(res => {
					this.$emit("receivelogin", true);
					let userInfo = res.data;
					uni.setStorageSync('third_session', userInfo.thirdSession);
					uni.setStorageSync('user_info', userInfo);
					// #ifdef APP-PLUS
					setPushAlias(userInfo.id);
					// #endif
					senLogin(userInfo);
					//登录完成跳到首页
					let reUrl = this.reUrl;
					const openId = userInfo.openId;
					const decodeUrl = decodeURIComponent(reUrl);
					if (reUrl == 'business-circle-plugin') {
						// #ifdef MP-WEIXIN
						api.erpOpenIdBind({
							channelId: 'SMART'
						}).then(res => {
							businessCirclePlugin.getLocation(openId).then(
								res => {
									if (res.return_code == 0) {
										businessCirclePlugin.getAuthStatus(openId, __config.mchId)
											.then(
												authRes => {
													reUrl =
														`plugin://business-circle-plugin/index?mch_id=${__config.mchId}&openid=${openId}&member_status=${authRes.status}`;
													uni.navigateTo({
														url: reUrl
													});
												})
									}
								})
						}).catch(e => {
							console.error(e);
						});
						// #endif

						// #ifndef MP-WEIXIN
						uni.redirectTo({
							url: reUrl ? decodeURIComponent(reUrl) : '/pages/home/<USER>'
						});
						// #endif
					} else if (decodeUrl && decodeUrl.startsWith('/pages/user/user-address/form/index')) {
						//register true表示是新客户
						if (userInfo.register) {
							uni.reLaunch({
								url: decodeUrl
							});
						} else {
							uni.reLaunch({
								url: '/pages/home/<USER>'
							});
						}
					} else {
						uni.reLaunch({
							url: reUrl ? decodeUrl : '/pages/home/<USER>'
						});
					}
					//获取购物车数量
					app.shoppingCartCount()
				});
			},
			getPhoneCode() {
				if (this.checkedRead === false) {
					uni.showToast({
						title: '请先勾选用户协议',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (this.msgKey) return
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.msgKey = true
				api.getPhoneCode({
					type: '1',
					phone: this.form.phone
				}).then(res => {
					this.msgKey = false
					if (res.code == '0') {
						uni.showToast({
							title: '验证码发送成功',
							icon: 'none',
							duration: 3000
						});
						this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						this.msgKey = true
						const time = setInterval(() => {
							this.msgTime--
							this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
							if (this.msgTime == 0) {
								this.msgTime = MSGTIME
								this.msgText = MSGINIT
								this.msgKey = false
								clearInterval(time)
							}
						}, 1000)
					} else {

					}
				}).catch(() => {
					this.msgKey = false
				});
			},
			// 一键登录
			toUniverify() {
				if (this.checkedRead === false) {
					uni.showToast({
						title: '请先勾选用户协议',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.$emit('univerify');
			},
			navTo(url) {
				uni.navigateTo({
					url
				})
			}
		}
	}
</script>

<style scoped>
	.text-Offline {
		border-bottom: 1px solid black;
	}

	.text_top {
		margin-top: 80rpx;
	}

	.btn_padding {
		padding: 70rpx 30rpx 0rpx;
	}

	.login {
		margin-left: -20rpx;
	}

	.login-text {
		font-size: 30rpx;
	}

	.icon-settings {
		width: 40rpx;
		height: 40rpx;
	}

	.member {
		height: 88rpx;
		line-height: 45rpx;
		justify-content: space-between;
		background: linear-gradient(to right, #d58158, #d59471);
		font-size: 26rpx;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 15rpx;
	}

	.collect-layout {
		background-color: #F3F3F3;
		border-top-left-radius: 28rpx;
		border-top-right-radius: 28rpx;
	}

	.collect-item {
		flex: 1;
		height: 70rpx;
	}

	.order-item {
		font-size: 64rpx;
		margin-top: 0
	}

	.icon-num {
		right: 17%;
		left: auto;
		margin-left: 20rpx;
		background-color: #dd514c;
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		font-size: 30rpx;
		line-height: 40rpx;
		color: #ffffff;
		vertical-align: middle;
		position: absolute;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;
		padding: 0rpx 16rpx;
		font-family: Helvetica Neue, Helvetica, sans-serif;
		black-space: nowrap;
	}

	.pay-num {
		position: relative;
		display: block;
		margin-top: 16rpx;
		width: 100%;
		font-size: 44rpx;
		font-weight: bold;
		color: #000;
		line-height: 64rpx;
		height: 64rpx;
	}

	.result-img {
		margin: 0 auto;
		margin-top: 15rpx;
		margin-bottom: 15rpx;
		overflow: hidden;
	}

	.img {
		/* height: 296rpx; */
		width: 100%;
		height: auto;
		border-radius: 20rpx;
		border: 1px solid red;
	}

	.user-text-phone {
		font-size: 26rpx !important;
	}

	.user-text-xl {
		color: #000000 !important;
		margin: 0rpx !important;
	}

	.user-text-df {
		color: #000000;
	}

	.user-padding {
		padding: 10rpx;
		margin-bottom: 12rpx;
	}

	.user-btn-sm {
		font-size: 22rpx;
		height: 36rpx;
		margin-left: 10rpx;
	}

	.user-line {
		margin: 14rpx auto;
		width: 699rpx;
		border-bottom: 2rpx dashed #eee;
	}

	.personal-information {
		flex: 1;
		display: flex;
	}

	.user-nickName {
		margin-left: 12rpx;
	}

	.head {
		border: #ffffff 2rpx solid;
		/*  #ifdef  H5 || APP-PLUS */
		margin-top: 70rpx;
		/*  #endif  */
	}

	.all-orders {
		width: 94% !important;
		margin: auto !important;
		margin-top: 15rpx !important;
		border-radius: 20rpx !important;
	}

	.mine {
		width: 94% !important;
		margin: auto !important;
		border-radius: 10rpx !important;
	}

	.font-24 {
		font-size: 25rpx;
	}

	.login-logo {
		width: 300rpx;
		margin-top: 8vh;
	}

	.title-lable {
		width: 220rpx;
	}


	.unchecked {
		width: 36rpx;
		height: 36rpx;
		background: #FFFFFF;
		border: 1px solid #999999;
		border-radius: 50%;
	}

	@media (min-width: 549px) {
		.login-logo {
			width: 240rpx;
			margin-top: 2vh;
		}

		.title-lable {
			width: 130rpx;
		}

		.cu-btn {
			height: 40rpx;
		}

		.unchecked {
			width: 22rpx;
			height: 22rpx;
		}
	}
</style>