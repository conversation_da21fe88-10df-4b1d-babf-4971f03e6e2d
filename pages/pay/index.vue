<template>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">订单支付</block>
    </cu-custom>
    <view
      class="padding text-center margin-top"
      v-if="orderInfo != null"
    >
      <view class="text-red text-bold text-xxl">¥<text class="margin-left-xs payment_price">{{orderInfo.paymentPrice}}</text></view>
      <view
        class="text-sm margin-top-xs"
        v-if="orderInfo.isPay == '0' && !orderInfo.status && canPay"
      >请在
        <count-down
          :outTime="1000 * orderInfo.outTime"
          @countDownDone="countDownDone"
        ></count-down>
        内付款，超时订单将自动取消
      </view>
      <view
        class="text-sm margin-top-xs"
        v-if="!canPay"
      >订单已超时取消</view>
      <view class="text-sm margin-top-sm">{{orderInfo.name}}</view>
    </view>
    <view class="cu-card">
      <radio-group
        @change="radioChange"
        class="cu-item cu-list menu-avatar block"
      >
        <view
          class="cu-item"
          v-for="(item, index) in paymentTypes"
          :key="item.value"
          :class="item.disabled != false ? 'grayscale' : ''"
        >
          <view
            class="cu-avatar lg bg-white"
            :style="'background-image:url('+item.icon+')'"
          ></view>
          <view class="content">
            <view class="text-black">{{item.name}}</view>
            <view class="text-gray text-sm flex">
              <text
                class="text-cut text-xs"
                v-if="item.disabled != false"
              >
                <text class="cuIcon-infofill margin-right-xs"></text> {{item.tip}}
              </text>
            </view>
          </view>
          <view class="action">
            <radio
              class="red"
              :value="item.value"
              :checked="index === current"
              :disabled="item.disabled != false ? true : false"
            ></radio>
          </view>
        </view>
      </radio-group>
    </view>
    <view
      class="cu-bar bg-white tabbar border pay-div"
      v-if="orderInfo"
    >
      <button
        class="cu-btn block shadow-blur round pay-btn"
        :class="'bg-'+theme.themeColor"
        @tap="unifiedOrder"
        :loading="loading"
        :disabled="loading"
        type
        v-if="orderInfo.isPay == '0' && !orderInfo.status && canPay"
      >立即付款 ¥{{orderInfo.paymentPrice}}</button>
    </view>
  </view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import countDown from "components/count-down/index";
	import jweixin from '@/utils/jweixin'

	export default {
		components: {
			countDown
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderInfo: null,
				orderId: null,
				canPay: false,
				paymentTypes: [{
						value: '1',
						name: '微信',
						icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAMAAABHPGVmAAAAY1BMVEVHcEwHuwYIfQcqSSoIvwYJxwcIgQcKMwkCNwEHoAUDQwIJxgcJlAgMigsLawsIuAYCPwIJxwcFiQQGiAUGvgUIuQYJxwcHnQUEjgPC7sHC7sEGpQUJuwcJxAcJvgcJyAcAuAC5OlB6AAAAHHRSTlMA0FAG2+5fEB6hKeN8bz7CM/Wei+qw+re2IjqL0KEARgAABDJJREFUaN7tWdmWojAUJBtZ2BHRnibB///KiYjdJARIEF9mrLfuoxZ3rUqIog8++OCDfw/o68+Ar6/3/D4r4/oKmtuArsHXU1bSYxkgT6RGd+tG3JT+M7nE7CAGmt8ZOhdkTzhEB1DEYIFh5JEgezVtmkJ1G9A0r0TDzu0mxUBT5Ls5YOJFoaGq753BpL0vxxCM2DN4p74LgWrCU4bqMI47CwwluYZy3BEYS93u4OhwGVbzbhdwwJrJzTiUTHDiGEpJMK6MdaAK705GQJnfhAyxGFssKkkFpSU3/i0z72QZTyf54+lYYnLgcTC48WniOS6CmHke9x8yuVU8fryszEfyI+FGRWT9s2XMR37WmJp5bL36mJmBeJCYaTyHV6RTALlL9ZxvUZkNUflUpbDXxWksvNVeYAyFW13n02BWtoYFC6lWSNzNWljTCK7s1i48RGQutnoYC+zQYEkAIPLnQ1KSBGOAwfbYp04RUevSop1LcYIlozqzyGPoaxm8sRThD2+EWJnDGMK8ZOtMxS2QQg610c4pvegN17e9hmwAXzF/CIRGkerfovCS6KKoSXpl3wO+MJhfOCgSedZTQTO3cVKyLZzeDzVBYcR3CrxcRiUBfC1dCugwIJAewVoAAamiEfpuPeKd7YCrt6ErqLaYXjrdn9b340quWMSA54fbs9nOuWckifDneIQ9XZCJ17d6iCgIWA6tycJ9vnpX2XPQAlKGLsce31XaYGVhS0728fRs5ZGvPotEaw4dxq3JKhtMpKE/UwnweETtYMysYmhbMJkyyrKpAvZ82du5Qk+j0giElHY5xxLAqQFoy3VxNKGdihHIs6gTT/E0M4Va8mR8fVfIq+UE5DjR7JckGTv22rpslMOvzXo+s1rw6R3iiaBAhykzjUxerUZS2sMkB9sEybTJ7wUQhWkOLub5fW3x6VTg2Vk+TS9mdAnPuHV8Vg3zPQbp5IjKYVjUTLBmDXSz9DhbZNHuOA+3NI9Rie0bj2qh+roAcOdxr5vJV77QY1qC4n2RqD51XK0odyTRcZFY8/o7JvX2TliqyR+Hw8cLhS/bfSS3HPk4/DvJBdFkH0fjuCH9GS9zCPQwFmpX4R3H/Kfcy4rXBRluOse1gtJ9RUkdd4NypBBInwryrL6AhFQV0eZ03zQS5s6WpphIDWJMCMGYh7BteokxW41FEWo3fAKJ+0WKCG1IzoJoz3/nzNeur4JD+b0QmMrj6qEfodAuJntuQllYwmZb3vPqrQrhSPfesbfescxOKAEsvTfHC7f5WaXeHMdDPbc7WbVZ9BoY33r3sePm21EYvEEDDnhXNNwWrNHIPDoCND6TGc9df9Tj6u+oF3giOw8v8H6AL2n+eFmlDsnXswfyOD3VV406hYI+M9l1VRkdCYeEp1q3s+jdEN+yiN6Pkov3k6CIRh988MH/ib/ezc/hBNArqAAAAABJRU5ErkJggg==',
						tip: 'PC、APP、微信小程序、公众号H5可使用',
						// #ifdef APP-PLUS || MP-WEIXIN
						disabled: false,
						// #endif
						// 公众号H5 由于微信不支持个体户申请普通浏览器H5收款，所以只做了公众号H5的对接
						// #ifdef H5
						disabled: util.isWeiXinBrowser() ? false : true,
						// #endif
					},
					{
						value: '2',
						name: '支付宝',
						icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAMAAABHPGVmAAAAXVBMVEVHcEwBf7UBg7sCtf8MMEABsfwHPFTA6vxBv/YALEABcaICsPwCs/4CsvwAZ5IBea0Bi8cBqPABjssBpu8Gg7sBqPPA6/1BwPc2r+QCqfIApfACsf0Br/oCqfACrfc//Qm0AAAAGXRSTlMAnov+CeUvQL8YWtTz3UVtnrqt0HvzJZ1guT/6QwAABARJREFUaN7tmduSoyAQhiOnWcCzJpkO6Ps/5oKORo2CaHZrayu/MxdTjvmkafqUy+Wjjz766P8Rud2+QnW7BRHSvLrCI1RXWmZ8LyOtlAZooAmTwYCO610YgnDoxz8FoCTbwSi0+dfmuCAWXkjRNmcQ5gUBpx4G0ucYFtMmbouluDkNMZ9QuI3l9qpYm0uZX/ebABaehTielWyQ9KylINuQSDvfUI7/KD02fWyfFlK5n5XD+xHp2/vvTcjt+tgL8a3kvg1xMwJW4oB8vQvSPH79UxCtFkpGD0nw8p4+BmnLaCE0ej9a3ory6yGIYiQg0XEKRyCQXoIgf2El5BgEohrVqFeEZntCRGZUZ7VRf//onjRat7qTstfCuzr30uP9o97lOCcmQEKXo7cS9VGISTQBsevoSmCyksRTbARBrnSqMRWRQkqZyMSKJubOGYjO+UxPh+3E7WUl9AmIivYdklqdgLSLlSw0blGp37cnCxXrIeWQd0F/DRr/UmgsctQbk9Ziw8YAiv4YBBI+Bhn4UxBdDo8xdQpiDL+aXTtriUmRfibHJ0VU15nIalQmhjQLJdRhrZ0Q02VQlE6KTc5EVMVqtIzKhztCHY1dqhIr5SwXqIh70+GxB1mrbfdAIK43Uy8TJVW6HYNlig9CaH8ECGNCiJQtGyee5ol4tjTNkT2BOO3yeC5jha2oLLNFVTHGrXS1IfJDOucUFbab/BNLjGtJxFab5RaOJK3WBr5oYunec0HhQrzsFEtWjtAOiI1JJhrBSmuDq5eBAzH788rxQYCSzd7R2I0ifvFzfBAbk0q9XbKo+BXzwvFBVGbKKmdoXG+g+ZTjhYgLT+BQk244tOd4IPYznBCcOetu0XF2mMtRIE7XsTFB4Zbjg9TbGw9An01LhiuxEeF49u2BmFpLbLiwqtiEYdLBJoZ4zklFLly2q9uRP523xr31qiykVRpPvJ2JsccrRVMxm4sN56baP918QnRug3y1yHezM0iemwY22IRgfqJw50C8Nr36kNS1ovnEl3g1q08MJql5IKTtt5ebpE5jo0SWs1ws6IstH7raixmSlh7GroQzxvj8aZar5rEWoVdCp7uQSLZ6eIbirTPU4hixEAionK2N18sYO8NanLP9EOtN+bwo4qy2GdnVK9o87cXMKkgwLlUi0Q82BTJ1kALwzL77cqB0Ym7XZR7UWrdtG9tquAH/dB26H41Lx1jmRh/rT4ZKOwadtlBbbbSCR9xXh8Wy9vwMvfkJspti8VsYXU5yfOmA37IU6jz7XL4B4v4+wBoMn4e0d18eE2cNBm3hz5UpPUdRxZ6JJS9OLEbRbO8XjcX1cUxyf4YkhH/f779Cdf827Vhg7fLRRx999M/qN1ayvMw+OVKQAAAAAElFTkSuQmCC',
						tip: 'PC、APP、普通H5可使用',
						// #ifdef APP-PLUS
						disabled: false,
						// #endif
						// #ifdef H5
						disabled: util.isWeiXinBrowser() ? true : false,
						// #endif
					}
				],
				current: null,
				loading: false,
				//有数统计使用
				page_title:'订单支付'
			}
		},
		onLoad(options) {
			let orderId = options.orderId
			this.orderId = orderId
			// app.initPage().then(res => {
			// 	this.orderGet(orderId);
			// });
		},

  onShow () {
    app.initPage().then(res => {
      this.orderGet(this.orderId);
    });
  },

  methods: {
    orderGet (id) {
      let that = this;
      api.orderGet(id).then(res => {
        let orderInfo = res.data;
        this.orderInfo = orderInfo;
        if (orderInfo.outTime > 0) {
          this.canPay = true
        }
        if (orderInfo.isPay === '1') {
          //支付成功跳转到支付结果页面
          setTimeout(() => {
            uni.redirectTo({
              url: '/pages/order/order-pay-result/index?orderId=' + that.orderId
            });
          }, 1000);
        }
      });
    },
    countDownDone () {
      this.canPay = false
    },
    radioChange (evt) {
      for (let i = 0; i < this.paymentTypes.length; i++) {
        if (this.paymentTypes[i].value === evt.detail.value) {
          this.current = i;
          break;
        }
      }
    },
    unifiedOrder () {
      this.loading = true;
      var that = this;
      let orderInfo = this.orderInfo;
      if (this.current == null) {
        this.loading = false;
        uni.showToast({
          title: '请选择支付工具',
          icon: 'none',
          duration: 2000
        });
      } else {
        let paymentType = this.paymentTypes[this.current].value
        api.unifiedOrder({
          paymentType: paymentType,
          id: orderInfo.id,
          // #ifdef MP-WEIXIN
          tradeType: 'JSAPI',
          // #endif
          // #ifdef H5
          tradeType: util.isWeiXinBrowser() ? 'JSAPI' : 'MWEB',
          returnUrl: window.location.protocol + '//' + window.location.host +
            '/pages/order/order-pay-result/index?orderId=' + orderInfo.id + '&tenant_id=' +
            orderInfo.tenantId,
          quitUrl: window.location.protocol + '//' + window.location.host +
            '/pages/order/order-detail/index?id=' + orderInfo.id + '&tenant_id=' + orderInfo
              .tenantId
						// #endif
						// #ifdef APP-PLUS
						tradeType: 'APP',
          // #endif
        }).then(res => {
          this.loading = false;
          if (orderInfo.paymentPrice <= 0) {
            //0元付款
            //支付成功跳转到支付结果页面
            uni.redirectTo({
              url: '/pages/order/order-pay-result/index?orderId=' + that.orderId
            });
          } else {
            // let payData = res.data;
            let payData = res.data.barCodePay;
            // #ifdef MP-WEIXIN
            //微信小程序
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: payData.timeStamp,
              nonceStr: payData.nonceStr,
              package: payData.package1,
              signType: payData.signType,
              paySign: payData.paySign,
              success: function (res) {
                //支付成功跳转到支付结果页面
                uni.redirectTo({
                  url: '/pages/order/order-pay-result/index?orderId=' +
                    that.orderId
                });
              },
              fail: function (res) {
                console.log('fail:' + JSON.stringify(res));
              },
              complete: function (res) {
                console.log(res);
              }
            });
            // #endif
            // #ifdef APP-PLUS
            //app微信支付
            if (paymentType === '1') {
              let orderInfo = {
                "appid": payData.appId,
                "noncestr": payData.nonceStr,
                "package": payData.packageValue,
                "partnerid": payData.partnerId,
                "prepayid": payData.prepayId,
                "timestamp": payData.timeStamp,
                "sign": payData.sign
              }
              uni.requestPayment({
                provider: 'wxpay',
                orderInfo: orderInfo,
                success: function (res) {
                  //支付成功跳转到支付结果页面
                  uni.redirectTo({
                    url: '/pages/order/order-pay-result/index?orderId=' +
                      that.orderId
                  });
                },
                fail: function (res) {
                  console.log('fail:' + JSON.stringify(res));
                },
                complete: function (res) {
                  console.log(res);
                }
              });
            }
            //app支付宝
            if (paymentType === '2') {
              let orderInfo = payData.body
              uni.requestPayment({
                provider: 'alipay',
                orderInfo: orderInfo,
                success: function (res) {
                  //支付成功跳转到支付结果页面
                  uni.redirectTo({
                    url: '/pages/order/order-pay-result/index?orderId=' +
                      that.orderId
                  });
                },
                fail: function (res) {
                  console.log('fail:' + JSON.stringify(res));
                },
                complete: function (res) {
                  console.log(res);
                }
              });
            }
            // #endif
            // #ifdef H5
            //微信公众号H5
            if (util.isWeiXinBrowser()) {
              jweixin.payRequest(payData, res => {
                //支付成功跳转到支付结果页面
                uni.redirectTo({
                  url: '/pages/order/order-pay-result/index?orderId=' + that
                    .orderId
                });
              }, e => {

              })
            } else {
              //H5支付宝
              if (paymentType === '2') {
                let form = payData.body
                //创建容器将返回的表单放进去然后新开一个页面
                const div = document.createElement('div')
                div.setAttribute("id", "alipay-div");
                div.innerHTML = form //此处form就是后台返回接收到的数据
                document.body.appendChild(div)
                document.forms[0].setAttribute('target',
                  '_blank'); //或者 myForm.target='_parent';
                document.forms[0].submit()
                document.getElementById("alipay-div").parentNode.removeChild(document
                  .getElementById("alipay-div"));
              }
            }
            // #endif
          }
        }).catch(() => {
          this.loading = false;
        });
      }
    },
  }
}
</script>

<style>
.pay-div {
  width: 100%;
  height: 140rpx;
  position: absolute;
  bottom: 0;
}

.pay-btn {
  margin: auto;
  width: 96%;
  height: 88rpx;
}

.payment_price {
  font-size: 80rpx;
}
</style>
