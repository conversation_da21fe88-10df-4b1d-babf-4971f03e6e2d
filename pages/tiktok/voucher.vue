<template>
  <view>
    <cu-custom :isBack="true" :bgColor="'bg-'+theme.backgroundColor" :hideMarchContent="true">
      <!-- <block slot="backText">返回</block> -->
			<block slot="content">抖音兑换券</block>
		</cu-custom>
    <view class="wrapper" v-if="!tiktokCoupon">
      <image src="https://img.songlei.com/storagelocker/no-data.png" class="no-data" />
      <view class="btn" @click="toHome">去逛逛</view>
    </view>
    <view class="wrapper" v-if="tiktokCoupon">
      <view class="title">
        <view class="text-lg text-black text-center">
          <text class="text-bold">{{ tiktokCoupon.couponTypeName }}</text>
          <text class="text-xdf margin-left-sm" style="color: #999999;">×{{ tiktokCoupon.qty }}</text>
        </view>
        <view class="text-xdf text-center margin-top-sm" style="color: #C6A389">可兑换松鼠美淘券</view>
        <view class="left-bottom-circle"></view>
        <view class="right-bottom-circle"></view>
      </view>
      <view class="coupon-list" v-if="tiktokCoupon.details.length">
        <view class="coupon-item" v-for="(item, index) in tiktokCoupon.details" :key="index">
          <view class="flex justify-between align-center">
            <view class="text-xdf text-black">{{ item.typeName }}</view>
            <view class="text-xdf" style="color: #999999;">×1</view>
          </view>
          <view class="text-xsm margin-top-sm" style="color: #C6A389;" @click="showCouponRule(item)">
            <text>使用须知</text>
            <text class="cuIcon-right"></text>
          </view>
        </view>
      </view>

      <view class="btn" @click="exchangeTiktokCoupon">确认兑换</view>
    </view>

    <!-- 使用须知弹框 -->
    <view
      class="cu-modal"
      :class="modalRule ? 'show' : ''"
      catchtouchmove="touchMove"
      @click="closeRule"
    >
      <view
        class="dialog-tips cu-dialog bg-white"
        @click.stop
      >
        <view class="text-xl text-center text-black text-bold">
          松鼠券使用须知
          <text 
            @click.stop="closeRule" 
            class="close cuIcon-roundclose" 
            style="color: #e5e5e5; font-size: 50rpx;"
          ></text>
        </view>
        <view class="margin-top-sm text-bold text-left">{{ couponRule.typeName }}</view>
        <view class="margin-top-sm text-left" style="white-space: pre-wrap;" v-html="couponRule.detailUsageDesc">
         </view>
      </view>
    </view>

    <!-- 兑换成功弹框 -->
    <view
      class="cu-modal"
      :class="modalSuccess ? 'show' : ''"
      catchtouchmove="touchMove"
      @click="closeSuccess"
    >
      <view
        class="dialog-tips cu-dialog bg-white"
        @click.stop
      >
        <view class="text-lg text-center text-black">
          温馨提示
          <text 
            @click.stop="closeSuccess" 
            class="close cuIcon-roundclose" 
            style="color: #e5e5e5; font-size: 50rpx;"
          ></text>
        </view>
        <view class="text-xl margin-top-sm text-black text-bold text-center">兑换成功！</view>
        <view class="btn" @click="toPage('/pages/coupon/coupon-user-list/index')">去使用</view>
      </view>
    </view>
  </view>
</template>
<script>
import { getTiktokCoupon, exchangeTiktokCoupon } from './api.js';
const app = getApp();

export default {
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      tiktokCoupon: null,
      couponRule: {
        detailUsageDesc: ''
      },
      modalRule: false,
      modalSuccess: false,
      store: ''

    };
  },

  onLoad(options) {
    this.store = options.store;
    this.coupon = options.coupon;
  },

  onShow(){
    app.initPage().then(res => {
      this.getTiktokCoupon();
    });
  },

  methods: {
    // 获取抖音兑换券
    getTiktokCoupon(){
      getTiktokCoupon({
        store: this.store,
        qrcode: this.coupon
      }).then(res => {
        this.tiktokCoupon = res.data;
      });
    },
    // 显示优惠券使用规则
    showCouponRule(item){
      this.couponRule = item;
      this.modalRule = true
    },
    // 关闭优惠券使用规则
    closeRule(){
      this.modalRule = false
    },
    // 关闭成功弹框
    closeSuccess(){
      this.modalSuccess = false
    },
    // 跳转页面
    toPage(url){
      this.closeSuccess()
      uni.navigateTo({
        url: url
      })
    },
    // 兑换券
    exchangeTiktokCoupon(){
      exchangeTiktokCoupon({
        store: this.store,
        couponTypeCode: this.tiktokCoupon.couponTypeCode,
        couponNo: this.tiktokCoupon.couponNo,
      }).then(res => {
        this.modalSuccess = true
      });
    },
    toHome() {
      uni.reLaunch({
        url: '/pages/home/<USER>'
      });
    }
  },
}
</script>
<style lang="scss" scoped>
.wrapper{
  width: 100%;
  height: 100%;
  padding: 40rpx;
  .no-data{
    display: block;
    width: 200rpx;
    height: 204rpx;
    margin: 0 auto;
  }
}

.title{
  padding: 40rpx;
  border-radius: 30rpx 30rpx 0 0;
  background-color: #fff;
  border-bottom: 1px solid #E5E5E5;
  position: relative;
}

.left-bottom-circle{
  width: 40rpx;
  height: 40rpx;
  background-color: #efefef;
  position: absolute;
  bottom: -20rpx;
  left: -20rpx;
  border-radius: 50%;
}
.right-bottom-circle{
  width: 40rpx;
  height: 40rpx;
  background-color: #efefef;
  position: absolute;
  bottom: -20rpx;
  right: -20rpx;
  border-radius: 50%;
}

.coupon-list{
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 0 0 30rpx 30rpx;
  padding: 0 50rpx;
  .coupon-item{
    border-bottom: 1px dotted #E5E5E5;
    padding: 40rpx 0;
  }
}

.btn{
  width: 365rpx;
  height: 89rpx;
  background: linear-gradient(88deg, #C7AE93 0%, #C5A288 100%);
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 89rpx;
  margin: 73rpx auto 0;
}

.dialog-tips{
  padding: 60rpx 40rpx;
  .btn-tips{
    width: 213rpx;
    height: 84rpx;
    background: #4A71FE;
    border-radius: 42rpx;
    font-size: 28rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 84rpx;
    margin: 53rpx auto 0;
    position: relative;
  }
  .close{
    position: absolute;
    right: 36rpx;
    top: 36rpx;
  }
  .input-points{
    width: 200rpx;
    height: 64rpx;
    border: 1rpx solid #E6E6E6;
    border-radius: 12rpx;
    padding: 0 20rpx;
  }
}
</style>
