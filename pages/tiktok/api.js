import {
  requestApi as request
} from '@/utils/api.js'

const userInfo = uni.getStorageSync('user_info');
const { erpCid } = userInfo;

// 查询抖音券
export const getTiktokCoupon = (data) => {
  return request({
    url: '/sco-accnt-api/rest?method=sco.coupon.checkdouyin',
    method: 'post',
	  data: {
      channel: "SONGSHU",
      custId: erpCid,
      ...data
    }
  })
}
// 兑换抖音券
export const exchangeTiktokCoupon = (data) => {
  return request({
    url: '/sco-accnt-api/rest?method=sco.coupon.exchangedouyin',
    method: 'post',
	  data: {
      channel: "SONGSHU",
      custId: erpCid,
      ...data
    }
  })
}