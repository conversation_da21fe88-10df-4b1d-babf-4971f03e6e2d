<template>
  <view
    class="padding-bottom-xs;"
    style="background-color: #f4f4f4; position: relative; width: 100%"
  >
    <!-- #ifdef MP -->
    <div-live :shopId="parameter.shopId"></div-live>
    <!-- #endif -->
    <view
      v-if="jionmumberdatas && jionmumberdatas.brandVipConfig"
      style="width: 100%"
    >
      <view
        style="height: 222rpx; width: 100%"
        v-if="
          jionmumberdatas &&
          jionmumberdatas.brandVipConfig &&
          jionmumberdatas.brandVipMember
        "
      >
        <image
          :src="jionmumberdatas.brandVipConfig.joinPic"
          mode="aspectFill"
          style="width: 100%; height: 222rpx"
          @click="putcur"
        ></image>
      </view>
      <view
        v-else-if="jionmumberdatas && jionmumberdatas.brandVipConfig"
        style="width: 100%; height: 222rpx"
      >
        <image
          :src="jionmumberdatas.brandVipConfig.notJoinPic"
          mode="aspectFill"
          style="width: 100%; height: 222rpx"
          @click="putcur"
        ></image>
      </view>
    </view>
    <view
      class="shop-content"
      v-if="
        !showDefaultPage &&
        pageDivData &&
        pageDivData.pageComponent &&
        pageDivData.pageComponent.componentsList &&
        pageDivData.pageComponent.componentsList.length > 0
      "
    >
      <!-- 自定义页面组件 -->
      <view
        v-for="(temp, index) in pageDivData.pageComponent.componentsList"
        :key="index"
      >
        <!-- <template v-if="temp.componentName === 'liveComponent'">
					<view>
						<div-live v-model="temp.data" :shopId="parameter.shopId"></div-live>
					</view>
				</template> -->
        <template v-if="temp.componentName === 'imageComponent'">
          <view>
            <div-image
              v-model="temp.data"
              :isVideo="shopDetailShowVideo"
            ></div-image>
          </view>
        </template>
        <template v-if="temp.componentName === 'imageListComponent'">
          <view>
            <div-image-list v-model="temp.data"></div-image-list>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'swiperComponent'">
          <view>
            <div-swiper v-model="temp.data"></div-swiper>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'navButtonComponent'">
          <view>
            <div-nav-button v-model="temp.data"></div-nav-button>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'noticeComponent'">
          <view>
            <div-notice v-model="temp.data"></div-notice>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'titleTextComponent'">
          <view>
            <div-title-text v-model="temp.data"></div-title-text>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'goodsComponent'">
          <view style="background-color: #ffffff">
            <div-goods
              v-model="temp.data"
              :shopId="parameter.shopId"
            ></div-goods>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'goodsRowComponent'">
          <view>
            <div-goods-row
              @onMoreShopGoods="NavChangeCur2()"
              v-model="temp.data"
              :shopId="parameter.shopId"
            ></div-goods-row>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'shopComponent'">
          <view>
            <div-shop v-model="temp.data" :shopId="parameter.shopId"></div-shop>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'goodsCategoryComponent'">
          <view>
            <div-goods-category v-model="temp.data"></div-goods-category>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'couponComponent'">
          <view>
            <div-coupon
              v-model="temp.data"
              :shopId="parameter.shopId"
            ></div-coupon>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'bargainComponent'">
          <view>
            <div-bargain
              v-model="temp.data"
              :shopId="parameter.shopId"
            ></div-bargain>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'grouponComponent'">
          <view>
            <div-groupon
              v-model="temp.data"
              :shopId="parameter.shopId"
            ></div-groupon>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'seckillComponent'">
          <view>
            <div-seckill
              v-model="temp.data"
              :shopId="parameter.shopId"
            ></div-seckill>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'searchComponent'">
          <view>
            <div-search
              v-model="temp.data"
              :shopId="parameter.shopId"
              :collectId="shopInfo.collectId"
              @userCollect="userCollect"
            ></div-search>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'shopInfoComponent'">
          <view>
            <div-shop-info
              @shareShowFun="shareShowFun"
              v-model="temp.data"
              :shopId="parameter.shopId"
            >
            </div-shop-info>
          </view>
        </template>
        <template v-else-if="temp.componentName === 'advertComponent'">
          <div-advert
            v-model="temp.data"
            :shopId="parameter.shopId"
          ></div-advert>
        </template>
        <template v-else-if="temp.componentName === 'goodsGroupingComponent'">
          <div-goods-groups v-model="temp.data" ref="qwe"></div-goods-groups>
        </template>
        <template v-else-if="temp.componentName === 'couponShopComponent'">
          <coupon-shop v-model="temp.data" :shopInfo="shopInfo" />
        </template>
        <template
          v-else-if="
            temp.componentName === 'shopGiftCardComponent' &&
            shopInfo != null &&
            shopInfo.cabinetGroup
          "
        >
          <shop-gift-card
            v-model="temp.data"
            :shopInfo="shopInfo"
            :shopId="parameter.shopId"
          />
        </template>
      </view>
    </view>
    <view v-if="showDefaultPage" class="shop-content">
      <!-- 默认的显示布局 -->
      <!-- 领取优惠券/秒杀/拼团/砍价 -->
      <!-- <view class="left-item bg-white padding-xs padding-top-sm">
				<view class="flex align-center">
					<navigator class="radius econds-kill" hover-class="none"
						:url="'/pages/seckill/seckill-list/index?shopId='+id">
						<image
							src="https://jiuzhuokeji.oss-cn-beijing.aliyuncs.com/1/material/36508116-317e-44d8-a964-bca0cf102619.png">
						</image>
					</navigator>
					<navigator class="radius margin-left-xs spellGroup" hover-class="none"
						:url="'/pages/groupon/groupon-list/index?shopId='+id">
						<image
							src="https://jiuzhuokeji.oss-cn-beijing.aliyuncs.com/1/material/17352afd-7491-420f-8bd9-e7b4fedcf450.png">
						</image>
					</navigator>
				</view>
				<view class="flex align-center margin-top-xs">
					<navigator class="radius bargain" hover-class="none"
						:url="'/pages/bargain/bargain-list/index?shopId='+id">
						<image
							src="https://jiuzhuokeji.oss-cn-beijing.aliyuncs.com/1/material/3ad8c0fc-65bd-4ef0-ac01-1e863f1f8481.png">
						</image>
					</navigator>
					<navigator class="radius margin-left-xs coupons" @tap="showModalCoupon">
						<image
							src="https://jiuzhuokeji.oss-cn-beijing.aliyuncs.com/1/material/466d5b21-e82f-47bd-a6ed-6c4809acff2a.png">
						</image>
					</navigator>
				</view>
			</view> -->

      <view style="background-color: #ffffff">
        <goods-card :goodsList="goodsListHot"></goods-card>
      </view>

      <coupon-receive
        :couponInfoList="couponInfoList"
        :modalCoupon="modalCoupon"
        @changeModalCoupon="modalCoupon = $event"
        @receiveCouponChange="receiveCouponChange($event)"
      >
      </coupon-receive>
    </view>
    <!-- 分享组件 -->
    <share-component
      v-model="showShare"
      :shareParams="shareParams"
    ></share-component>
    <shop-pop :shopInfo="shopInfo" />
  </view>
</template>

<script>
import couponReceive from "components/coupon-receive/index";
import goodsRow from "components/goods-row/index";
import divLive from "@/components/div-components/div-live/div-live.vue";
import divImage from "../div-components-shop/div-image/div-image.vue";
import divImageList from "@/components/div-components/div-image-list/div-image-list.vue";

import divSwiper from "@/components/div-components/div-swiper/div-swiper.vue";
import divAdvert from "@/components/div-components/div-advert/index.vue";

import divNavButton from "@/components/div-components/div-nav-button/div-nav-button.vue";
import divNotice from "../div-components-shop/div-notice/div-notice.vue";
import divTitleText from "../div-components-shop/div-title-text/div-title-text.vue";
import divGoods from "@/components/div-components/div-goods/index.vue";
import divGoodsRow from "@/components/div-components/div-goods-row/div-goods-row.vue";
import divCoupon from "../div-components-shop/div-coupon/div-coupon.vue";
import divBargain from "../div-components-shop/div-bargain/div-bargain.vue";
import divGroupon from "../div-components-shop/div-grouponinfo/div-grouponinfo.vue";
import divSeckill from "@/components/div-components/div-seckill/div-seckill.vue";
import divSearch from "../div-components-shop/div-search/div-search.vue";
import divShopInfo from "../div-components-shop/div-shopinfo/div-shopinfo.vue";
import shareComponent from "@/components/share-component/index";
import divGoodsGroups from "@/components/div-components/div-goods-groups/div-goods-groups.vue";
import shopPop from "../shop-pop/index.vue";
import couponShop from "../couponShop/index.vue";
import shopGiftCard from "../shop-gift-card/shop-gift-card.vue";

import api from "utils/api";
const app = getApp();
export default {
  components: {
    couponReceive,
    goodsRow,
    divLive,
    divImage,
    divImageList,
    // divAdvertDialog,
    divSwiper,
    divNavButton,
    divNotice,
    divTitleText,
    divGoods,
    divGoodsRow,
    divCoupon,
    divBargain,
    divGroupon,
    divSeckill,
    divSearch,
    divShopInfo,
    divAdvert,
    shareComponent,
    divGoodsGroups,
    shopPop,
    couponShop,
    shopGiftCard,
  },
  props: {
    parameter: {
      type: Object,
      default: {},
    },
    shopInfo: {
      type: Object,
      default: () => {},
    },
    heightHead: {
      type: Number,
      default: 100,
    },
    scrollTopCut: {
      type: Number,
      default: 100,
    },
    shopDetailShowVideo: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    parameter: {
      handler(val) {
        if (val && val.shopId) {
          app.initPage().then((res) => {
            this.loadDataDivPage();
            this.getshopinfo();
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      // CustomBar: this.CustomBar,
      modalCoupon: false,
      showDefaultPage: false,
      pageDivLoadmore: true,
      pageDivData: {
        pageComponent: {
          componentsList: [],
        },
      }, //首页自定义配置组件的数据
      //默认情况下的数据
      couponInfoList: [],
      goodsListHot: [],
      showShare: false,
      shareParams: {},
      scrollTop: 0,
      isSetScrollTop: 0, //0 初始值  1 不需要设置滑动， 2 需要设置
      jionmumberdatas: null,
    };
  },
  methods: {
    loadDataDivPage() {
      this.pageDivLoadmore = true;
      api.pagedeviseShop(this.parameter.shopId).then((res) => {
        let pageDivData = res.data;
        if (pageDivData) {
          this.pageDivData = pageDivData;
          // console.log(pageDivData,'pageDivData')
          this.$emit("homeData", {
            showDefaultPage: this.showDefaultPage,
            pageDivData: {
              ...this.pageDivData,
              componentsList: null,
            },
          }); //componentsList不需要
          if (
            !pageDivData ||
            !pageDivData.pageComponent ||
            pageDivData.pageComponent.componentsList.length == 0
          ) {
            // 如果没有设置自定义页面数据，那就显示默认原始页面
            this.getDefaultPageData();
          } else {
            this.showDefaultPage = false;
            this.pageDivLoadmore = false;
          }
        } else {
          // 如果没有设置自定义页面数据，那就显示默认原始页面
          // this.shopInfoGet();
          this.getDefaultPageData();
        }
      });
    },
    getDefaultPageData() {
      this.showDefaultPage = true;
      this.couponInfoPage();
      this.goodsHot();
    },

    //查询店铺可用电子券
    couponInfoPage() {
      api
        .couponInfoPage({
          current: 1,
          size: 50,
          descs: "create_time",
          shopId: this.parameter.shopId,
        })
        .then((res) => {
          this.couponInfoList = res.data.records;
        });
    },

    //热销单品
    goodsHot() {
      api
        .goodsPage({
          searchCount: false,
          current: 1,
          size: 20,
          descs: "sale_num",
          shopId: this.parameter.shopId,
        })
        .then((res) => {
          this.goodsListHot = res.data.records;
        });
    },

    showModalCoupon() {
      this.modalCoupon = true;
    },

    hideModalCoupon() {
      this.modalCoupon = false;
    },

    receiveCouponChange(obj) {
      //更新单条数据
      this.couponInfoList[obj.index] = obj.item;
      this.couponInfoList.splice(); //确保页面刷新成功
    },
    async shareShowFun() {
      // 分享海报需要配置的参数
      let desc = "长按识别小程序码";
      let shareImg = this.shopInfo.imgUrl;
      // #ifdef H5 || APP-PLUS
      desc = "长按识别二维码";
      // h5的海报分享的图片有的有跨域问题，所以统一转成base64的
      // 之所以不在组件里面转换是因为无法区分哪张image图片需要处理，一般处理主图
      shareImg = await util.imgUrlToBase64(shareImg);
      // #endif
      //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
      let posterConfig = {
        width: 750,
        height: 1280,
        backgroundColor: "#fff",
        debug: false,
        blocks: [
          {
            width: 690,
            height: 808,
            x: 30,
            y: 183,
            borderWidth: 2,
            borderColor: "#f0c2a0",
            borderRadius: 20,
          },
          {
            width: 634,
            height: 74,
            x: 59,
            y: 770,
            backgroundColor: "#fff",
            opacity: 0.5,
            zIndex: 100,
          },
        ],
        texts: [
          {
            x: 40,
            y: 113,
            baseLine: "top",
            text: "发现一个好店铺，推荐给你呀",
            fontSize: 38,
            color: "#080808",
          },
          {
            x: 92,
            y: 810,
            fontSize: 38,
            baseLine: "middle",
            text: this.shopInfo.name,
            width: 570,
            lineNum: 1,
            color: "#080808",
            zIndex: 200,
          },
          {
            x: 82,
            y: 900,
            fontSize: 28,
            baseLine: "middle",
            text: this.shopInfo.address,
            width: 570,
            lineNum: 1,
            color: "#666666",
            zIndex: 200,
          },
          {
            x: 82,
            y: 950,
            fontSize: 28,
            baseLine: "middle",
            text: this.shopInfo.phone,
            width: 570,
            lineNum: 1,
            color: "#666666",
            zIndex: 200,
          },
          {
            x: 540,
            y: 950,
            fontSize: 28,
            baseLine: "middle",
            text: this.shopInfo.collectCount + "人已收藏",
            width: 570,
            lineNum: 1,
            color: "#666666",
            zIndex: 200,
          },
          {
            x: 360,
            y: 1065,
            baseLine: "top",
            text: desc,
            fontSize: 38,
            color: "#080808",
          },
          {
            x: 360,
            y: 1123,
            baseLine: "top",
            text: "超值好货快来购买",
            fontSize: 28,
            color: "#929292",
          },
        ],
        images: [
          {
            width: 634,
            height: 634,
            x: 59,
            y: 210,
            url: shareImg,
          },
          {
            width: 230,
            height: 230,
            x: 92,
            y: 1020,
            url: null,
            qrCodeName: "qrCodeName", // 二维码、小程序码唯一区分标识
          },
        ],
      };
      let userInfo = uni.getStorageSync("user_info");
      if (userInfo && userInfo.headimgUrl) {
        //如果有头像则显示
        posterConfig.images.push({
          width: 62,
          height: 62,
          x: 40,
          y: 30,
          borderRadius: 62,
          url: userInfo.headimgUrl,
        });
        posterConfig.texts.push({
          x: 123,
          y: 61,
          baseLine: "middle",
          text: userInfo.nickName,
          fontSize: 32,
          color: "#8d8d8d",
        });
      }
      this.shareParams = {
        title: "发现一个好店铺，推荐给你呀",
        desc: this.shopInfo.name,
        imgUrl: this.shopInfo.imgUrl,
        scene: this.shopInfo.id,
        page: "pages/shop/shop-detail/index",
        posterConfig: posterConfig,
      };
      this.showShare = true;
    },
    getshopinfo() {
      api.shopInfoGet(this.parameter.shopId).then((res) => {
        // let shopInfo = res.data;
        // this.shopInfo = shopInfo;
        // console.log(shopInfo,'shopInfo')
        if (res.data.brandId) {
          api.getBrandVipMember(res.data.brandId).then((res) => {
            this.jionmumberdatas = res.data;
            console.log(this.jionmumberdatas, "res.data-==============");
          });
        }
      });
    },
    putcur() {
      this.$parent.getcur();
    },
  },
};
</script>

<style>
.econds-kill {
  width: 40%;
  height: 100rpx;
}

.econds-kill image {
  width: 100%;
  height: 100rpx;
}

.spellGroup {
  width: 700rpx;
  height: 100rpx;
}

.spellGroup image {
  width: 100%;
  height: 100rpx;
}

.bargain {
  width: 700rpx;
  height: 100rpx;
}

.bargain image {
  width: 100%;
  height: 100rpx;
}

.coupons {
  width: 40%;
  height: 100rpx;
}

.coupons image {
  width: 100%;
  height: 100rpx;
}
</style>
