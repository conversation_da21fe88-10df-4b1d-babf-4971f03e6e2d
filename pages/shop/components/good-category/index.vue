<template>
  <view
    class="padding-bottom-xs"
    style="background-color: #f0eeef; position: relative"
  >
    <template v-if="goodsCategoryShop && goodsCategoryShop.length > 0">
      <!-- 简单分类 -->
      <view
        class="cu-card bg-white padding radius"
        v-for="(item, index) in goodsCategoryShop"
        :key="index"
      >
        <navigator
          hover-class="none"
          :url="
            '/pages/shop/shop-goods-classify/index?shopId=' +
            parameter.shopId +
            '&brandCategoryShopFirst=' +
            item.id +
            '&title=' +
            item.name
          "
          class="flex justify-between"
        >
          <view><text
              class="cuIcon-titles margin-left-xs"
              :class="'text-' + theme.themeColor"
            ></text>
            <text class="text-bold text-black">{{ item.name }}</text>
          </view>
          <view class="cuIcon-right"></view>
        </navigator>

        <view class="content margin-top-xs">
          <view class="grid margin-right-xs">
            <navigator
              hover-class="none"
              :url="
                '/pages/shop/shop-goods-classify/index?shopId=' +
                parameter.shopId +
                '&brandCategoryShopSecond=' +
                item2.id +
                '&title=' +
                item2.name
              "
              class="cu-item bg-gray padding-sm text-sm"
              style="
                width: 46%;
                border-radius: 40rpx;
                margin-top: 10rpx;
                margin-left: 20rpx;
              "
              v-for="(item2, index) in item.children"
              :key="index"
            ><text style="padding-left: 14rpx">{{
                item2.name
              }}</text></navigator>
          </view>
        </view>
      </view>

      <!-- 图片分类 -->
      <!-- <view class="verticalbox flex">
        <scroll-view
          class="VerticalNav nav"
          scroll-y
          scroll-with-animation
          :scroll-top="VerticalNavTop"
          style="height: calc(100vh - 100rpx)"
        >
          <view
            class="set-text"
            :class="
              'cu-item ' +
              (index == TabCur ? 'cur text-' + theme.themeColor : '')
            "
            v-for="(item, index) in goodsCategoryShop"
            :key="index"
            @tap="tabSelect"
            :data-id="index"
            >{{ item.name }}
          </view>
        </scroll-view>

        <scroll-view
          class="verticalmain"
          scroll-y
          scroll-with-animation
          style="height: calc(100vh - 100rpx)"
          :scroll-into-view="'main-' + MainCur"
          @scroll="VerticalMain"
        >
          <view
            class="padding-tb-xs padding-lr-sm"
            v-for="(item, index) in goodsCategoryShop"
            :key="index"
            :id="'main-' + index"
          >
            <view class="cu-bar bg-white border-top-radius">
              <view class="action">
                <text
                  class="cuIcon-titles"
                  :class="'text-' + theme.themeColor"
                ></text
                ><text class="text-df">{{ item.name }}</text>
              </view>
            </view>

            <view class="cu-bar bg-white solid-bottom border-bottom-radius">
              <view class="cate-list flex flex-wrap">
                <image
                  v-if="item.picUrl"
                  class="img-banner radius"
                  :src="item.picUrl"
                  @click="jumpPage(item.page)"
                ></image>
                <view
                  v-if="item.children.length > 0"
                  v-for="(item2, index2) in item.children"
                  :key="index2"
                  class="cate text-xs text-center"
                >
                  <navigator
                    hover-class="none"
                    :url="
                      '/pages/goods/goods-list/index?shopId=' +
                      parameter.shopId +
                      '&categoryShopSecond=' +
                      item2.id +
                      '&title=' +
                      item2.name
                    "
                  >
                    <image
                      class="cate-img"
                      :src="
                        item2.picUrl
                          ? item2.picUrl
                          : 'https://img.songlei.com/live/img/no_pic.png'
                      "
                    ></image>
                    <view class="cate-type text-sm">{{ item2.name }}</view>
                  </navigator>
                </view>
                <view class="padding response text-center" v-if="!item.children"
                  >暂无数据</view
                >
              </view>
            </view>
          </view>
        </scroll-view>
      </view> -->
    </template>
    <no-date v-else></no-date>
  </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import noDate from "components/base-nodata/index.vue";
export default {
  components: {
    noDate
  },
  props: {
    parameter: {
      type: Object,
      default: {}
    },
    theme: {
      type: Object,
      default: {
        themeColor: "#EA0E32"
      }
    },
    heightHead: {
      type: Number,
      default: 100
    },
    scrollTopCut: {
      type: Number,
      default: 100
    }
  },
  watch: {
    parameter: {
      handler (val) {
        if (val && val.brandId > 0) {
          app.initPage().then(res => {
            this.goodsCategoryShopTree();
          });   
        }
      },
      deep: true,
      immediate: true
    }
  },

  created () {
    if (this.parameter && this.parameter.brandId > 0) {
      app.initPage().then(res => {
        this.goodsCategoryShopTree();
      });   
    }
  },

  mounted () {

    this.VerticalMain()

  },

  data () {
    return {
      CustomBar: this.CustomBar,
      goodsCategoryShop: [],
      load: true,
      parentId: 0,
      TabCur: 0,
      MainCur: 0,
      VerticalNavTop: 0,
    }
  },
  methods: {
    jumpPage (url) {
      if (url) {
        uni.navigateTo({
          url: url
        });
      }
    },
    //商品分类
    goodsCategoryShopTree () {
      console.log('准备发送数据了')
      api.brandCategoryShopTree({
        brandId: this.parameter.brandId
      }).then(res => {
        console.log('获取商品分类数据', res.data);
        this.goodsCategoryShop = res.data
      });
    },


    tabSelect (e) {
      this.TabCur = e.currentTarget.dataset.id;
      this.MainCur = e.currentTarget.dataset.id;
      this.VerticalNavTop = (e.currentTarget.dataset.id - 1) * 50;
      console.log("VerticalNavTop====>", this.VerticalNavTop);
    },

    VerticalMain (e) {
      let that = this;
      let list = this.goodsCategoryShop;
      let tabHeight = 0;

      if (this.load) {
        for (let i = 0; i < list.length; i++) {
          let view = uni.createSelectorQuery().select("#main-" + i);
          view.fields({
            size: true
          }, data => {
            list[i].top = tabHeight;
            if (data && data.height > 0) {
              tabHeight = tabHeight + data.height;
            }
            list[i].bottom = tabHeight;
          }).exec();
        }

        that.load = false;
        that.goodsCategoryShop = list;
      }
      // console.log("e", e, typeof (e) == "undefined");
      if (typeof (e) == "undefined") {
        return
      }
      let scrollTop = e.detail.scrollTop + 20;

      for (let i = 0; i < list.length; i++) {
        if (scrollTop > list[i].top && scrollTop < list[i].bottom) {

          that.VerticalNavTop = (i - 1) * 50;
          that.TabCur = i;
          return false;
        }

      }


    }
  }
}
</script>

<style scoped>
.border-top-radius {
  border-radius: 15rpx 15rpx 0rpx 0rpx;
}

.border-bottom-radius {
  border-radius: 0rpx 0rpx 15rpx 15rpx;
}
</style>
<style>
.set-text {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.VerticalNav.nav {
  width: 176rpx;
  white-space: initial;
  padding-top: 10rpx;
}

.VerticalNav.nav .cu-item {
  width: 100%;
  text-align: center;
  background-color: #fff;
  margin: 0;
  border: none;
  height: 50px;
  border-radius: 12rpx;
  position: relative;
}

.VerticalNav.nav .cu-item.cur {
  background-color: #f1f1f1;
}

.VerticalNav.nav .cu-item.cur::after {
  content: '';
  width: 8rpx;
  height: 30rpx;
  border-radius: 10rpx 0 0 10rpx;
  position: absolute;
  background-color: currentColor;
  top: 0;
  right: 0rpx;
  bottom: 0;
  margin: auto;
}

.verticalbox {
  /* margin-top: 100rpx; */
}

.search {
  top: unset !important;
}

.img-banner {
  width: 94%;
  height: 148rpx;
  margin: auto;
}

.cate-list {
  width: 100%;
}

.cate {
  width: 150rpx;
  margin: 15rpx;
}

.cate-img {
  width: 140rpx;
  height: 140rpx;
}
</style>

