<template>
    <view class="activeCard">
        <view class="left">
        	<!-- <view v-if="actInfo.isTop" class="isTop">
        		<class-label text='置顶' value='lightDarkGrey' :marginRight='8' :padding="[4,12]" :borderRadius='24'></class-label>
        	</view> -->
			<view class="dateBox">
				<view class="day">
					{{day}}
				</view>
				<view class="month">
					{{month}}月
				</view>
			</view>
        </view>
        <view class="right">
        	<view class="text margin-bottom-sm">
        			<class-label :text='actInfo.dyLabel' value='zy' :marginBottom='0' :marginRight='16' :padding="[4,6]"></class-label>
					{{actInfo.dyExplain}}
        	</view>
			<view class="card">
				<view class="img">
                    <lazy-load v-for="(item,index) in actInfo.picUrlsList" :key='index' :borderRadius='20' :image="item ? item : 'https://img.songlei.com/live/img/no_pic.png'" mode="aspectFill">
                    </lazy-load>
				</view>
				<!-- <view class="imgBtm">
					<view class="label">
                        <view v-for="(item,index) in actInfo.label" :key="index">
                            <class-label :text='item.text' value='whiteGrey' :borderRadius='8' :fontSize='24' :marginRight='16' :padding="[4,6]"></class-label>
                        </view>
					</view>
					<view class="btn">
						去参与
					</view>
				</view> -->
			</view>
        </view>

    </view>
</template>
<script>
import lazyLoad from "components/lazy-load/index";
export default {
    props:{
		actInfo:{
            type:Object,
            default(){
                return {}
            }
        }
	},
    components:{
        lazyLoad
    },
	data(){
		return{
			
		}
	},
    computed:{
        day(){
            let dateArr = this.actInfo.createTime.split('-');
            return dateArr[2].substring(0,2);
        },
        month(){
            let dateArr = this.actInfo.createTime.split('-');
            return dateArr[1];
        },
    },
	created() {
	}
}
</script>
<style scoped>
.activeCard{
    margin-bottom: 40rpx;
    display: flex;
}
/* 左侧 */
.left{
    flex-shrink: 0;
    margin-right: 10rpx;
}
/* 日期 */
.dateBox{
    display: flex;
    justify-content: space-between;
    width: 100rpx;
}
.day{
    font-size: 40rpx;
    color: #080808;
    font-weight: 600;
}
.month{
    font-size: 24rpx;
    color: #8F8F8F;
}

/* 右侧 */
.right{
	width: 80%;
}
.card{
    background-color: #F5F5F5;
    border-radius: 20rpx;
}
.imgBtm{
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.btn{
    background: linear-gradient(to right,#FF8E08,#FE5E00);
    color: #fff;
    padding: 20rpx 40rpx;
    border-radius: 40rpx;
}
</style>