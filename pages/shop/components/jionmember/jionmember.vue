<template>
  <view v-if="jionmumberdata&&jionmumberdata.brandId" id="jionmember" :style="{ 'margin-top': jionmumber === 1 ? '0rpx' : '-84rpx'}">
    <view
      v-if="jionmumber === 1"
      class="shopActive"
      :style="{
        position: 'relative',
        top: '-10px',
      }"
    >
      <image
        :src="jionmumberdatas.brandVipConfig && jionmumberdatas.brandVipConfig.logo"
        style="
          height: 224rpx;
          position: absolute;
          border-top-left-radius: 29rpx;
          border-top-right-radius: 29rpx;
          top: -68rpx;
          left: 20rpx;
          width: 94%;
        "
      ></image>
      <view
        style="
          font-size: 32rpx;
          font-family: OPPOSans M;
          font-weight: 400;
          position: absolute;
          left: 40rpx;
          top: -28rpx;
          color: #ffffff;
        "
      >
      </view>
      <view
        style="
          height: 344rpx;
          background: #ffffff;
          border-radius: 32rpx;
          position: absolute;
          width: 707rpx;
          display: flex;
          flex-direction: column;
          top: 120rpx;
        "
      >
        <view
          style="
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 20rpx;
          "
        >
          <view
            style="
              border-bottom: 1px solid #f7f7f7;
              display: flex;
              width: 100%;
              height: 60rpx;
              align-items: center;
              font-size: 26rpx;
              font-family: PingFang SC;
              font-weight: 500;
            "
          >
            <text style="color: red; margin-right: 10rpx">*</text> 手机号码 :
            <input type="text" v-model="phone" style="margin-left: 20rpx" />
          </view>
          <!-- 				<view style="width:30rpx;height: 30rpx;margin-right: 16rpx;margin-top: 16rpx;" @click="editmumber">
					<image style="width: 28rpx;height: 28rpx"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/pen.png" alt="" />
					</view> -->
        </view>
        <view
          style="display: flex; align-content: center; justify-content: center"
        >
          <view style="width: 30rpx; height: 30rpx">
            <image
              style="width: 30rpx; height: 30rpx"
              src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/checkout.png"
              mode=""
            ></image>
          </view>
          <view
            style="
              display: flex;
              flex-direction: column;
              font-size: 24rpx;
              font-family: PingFang SC;
              margin-left: 21rpx;
              font-weight: 500;
              color: #878787;
            "
          >
            <view style="width: 480rpx;">
              确认授权即同意
              <text style="color: #0070c6" @click="licenseAgreement"
                >《会员授权协议》</text
              >
              <template v-if="cardNotice">
                及本店
                <text style="color: #0070c6" @click="activecard"
                  >《开卡须知》</text
                >
              </template>
             并同意接受该店铺或松鼠美淘发动的商业信息 </view>
          </view>
        </view>
        <view style="display: flex; justify-content: center; margin-top: 60rpx" @click="submitjoin">
          <view
            style="
              width: 448rpx;
              height: 74rpx;
              background: #f2170f;
              border-radius: 37rpx;
              display: flex;
              justify-content: center;
              align-items: center;
              color: #ffffff;
            "
          >
            确认授权并加入店铺会员
          </view>
        </view>

        <view
          style="
            background: white;
            border-radius: 20rpx;
            margin-top: 80px;
            padding: 20rpx;
            width: 707rpx;
          "
        >
          <view
            style="
              font-size: 30rpx;
              font-family: PingFang SC;
              font-weight: bold;
              color: #282828;
              text-align: center;
              margin-bottom: 30rpx;
              margin-top: 11rpx;
            "
          >
            入会享受特权
          </view>
          <view
            style="
              display: flex;
              width: 100%;
              justify-content: space-between;
              margin-bottom: 22rpx;
            "
          >
            <view
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                margin-left: 32rpx;
              "
            >
              <image
                src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/joinfirst.png"
                style="width: 92rpx; height: 100rpx"
              ></image>
              <view
                style="
                  font-size: 26rpx;
                  font-family: PingFang SC;
                  font-weight: 500;
                  color: #000000;
                  margin-top: 14rpx;
                "
              >
                会员加赠礼
              </view>
            </view>
            <view
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              "
            >
              <image
                src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/jointwo.png"
                style="width: 90rpx; height: 100rpx"
              ></image>
              <view
                style="
                  font-size: 26rpx;
                  font-family: PingFang SC;
                  font-weight: 500;
                  color: #000000;
                  margin-top: 14rpx;
                "
              >
                会员专属商品
              </view>
            </view>
            <!-- 第三表图片在链接 -->
            <view
              @click="getmin"
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                margin-right: 51rpx;
              "
            >
              <image
                src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/jointhress.png"
                style="width: 94rpx; height: 100rpx"
              ></image>
              <view
                style="
                  font-size: 26rpx;
                  font-family: PingFang SC;
                  font-weight: 500;
                  color: #000000;
                  margin-top: 14rpx;
                "
              >
                会员权益
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view
      v-else-if="jionmumber === 2"
      class="shopActive"
    >
      <view 
        style="height: 224rpx;position: relative;overflow: hidden;border-radius: 20rpx;"
      >
        <image :src="jionmumberdatas.brandVipConfig.vipPic" mode="aspectFill" style="width: 100%;height: 100%;border-radius: 20rpx;"></image>
        <view
          style="
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            color: #fff;
          "
          class="flex flex-direction justify-between"
        >
          <view class="flex justify-between align-center" style="padding: 20rpx;">
            <view
              style="
                font-size: 32rpx;
                font-family: OPPOSans M;
                font-weight: 400;
                border-radius: 14rpx;
                color: #ffffff;
                position: relative;
                top: -8rpx;
                left: 8rpx;
              "
            >
              <text v-if="jionmumberdatas">{{ jionmumberdatas.brand.name }}</text>
              <text v-if="jionmumberdatas&&jionmumberdatas.brand&&jionmumberdatas.brand.nameEn&& (!jionmumberdatas.brand.name || jionmumberdatas.brand.name.toUpperCase() != jionmumberdatas.brand.nameEn.toUpperCase()) " style="margin-left: 6rpx">{{
                jionmumberdatas.brand.nameEn
              }}</text>
              <image
                style="width: 60rpx;
                       margin-left: 5px;
                       height: 23rpx; margin-left: 10rpx"
                src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/7a1fad01-7439-470b-a7e3-b0387c79b394.png"
              ></image>
            </view>
            <view class="flex justify-between" style="width: 280rpx;">
              <view
                @click="getmin"
                style="
                  width: 130rpx;
                  height: 46rpx;
                  background: #000000;
                  opacity: 0.5;
                  border-radius: 46rpx;
                  display: flex;
                  justify-content: center;
                  font-size: 24rpx;
                  align-items: center;
                "
              >
                会员权益
              </view>
              <view
                @click="editmumber"
                style="
                  width: 130rpx;
                  height: 46rpx;
                  background: #000000;
                  opacity: 0.5;
                  border-radius: 46rpx;
                  display: flex;
                  justify-content: center;
                  font-size: 24rpx;
                  align-items: center;
                "
              >
                修改信息
              </view>
            </view>
            
            
          </view>
          <view style="position: relative;height: 72rpx;">
            <image
              style="height: 100%;width: 100%;"
              src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/mc.png"
              mode=""
            ></image>
            <view style="position: absolute;top: 0;left: 0;color: #fff;" class="flex align-center justify-between">
              <view style="width: 352rpx; text-align: center" @click="pointsDetails" v-if="jionmumberdatas.brandVipMember">
                <text style="margin-right: 4rpx">积分</text
                >{{
                  jionmumberdatas.brandVipMember.brandPoints === null? 0: jionmumberdatas.brandVipMember.brandPoints
                }}
              </view>
              <image
                style="width: 1rpx; height: 70rpx"
                src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/tcline.png"
              >
              </image>
              <view style="width: 352rpx; text-align: center" @click="getmin(1)">
                <text style="margin-right: 4rpx">优惠券</text>{{ couponNum }}
              </view>
            </view>
          </view>          
        </view>
      </view>
      <!-- 会员推荐 -->
      <view :style="{ height: `calc(100vh - ${ topHeight }px - 224rpx - 50px - 10rpx)`, overflow: 'auto' }">
        <shopmber ref="shopmber" v-if="shopmbers"></shopmber>
      </view>
      <!-- <image
        class="hyimg"
        :src="jionmumberdatas.brandVipConfig.vipPic"
        style="
          height: 224rpx;
          position: absolute;
          top: -68rpx;
          left: 20rpx;
          width: 94%;
          border-radius: 20rpx;
        "
      >
      </image> -->
      <view
        style="
          position: absolute;
          bottom: -156rpx;
          height: 72rpx;
          width: 704rpx;
        "
        v-if="false"
      >
        <!-- <image
          style="height: 72rpx; width: 100%"
          src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/mc.png"
          mode=""
        ></image> -->
        <!--  -->
        <view
          style="
            display: flex;
            position: absolute;
            top: 26rpx;
            color: white;
            line-height: 72rpx;
            color: white;
            top: 2rpx;
          "
        >
          <view style="width: 352rpx; text-align: center" @click="pointsDetails" v-if="jionmumberdatas.brandVipMember">
            <text style="margin-right: 4rpx">积分</text
            >{{
              jionmumberdatas.brandVipMember.brandPoints === null? 0: jionmumberdatas.brandVipMember.brandPoints
            }}
          </view>
          <image
            style="width: 1rpx; height: 70rpx"
            src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/tcline.png"
          >
          </image>
          <view style="width: 352rpx; text-align: center" @click="getmin(1)">
            <text style="margin-right: 4rpx">优惠券</text>{{ couponNum }}
          </view>
          <view
            style="
              font-size: 32rpx;
              font-family: OPPOSans M;
              font-weight: 400;
              position: absolute;
              left: 14rpx;
              top: -130rpx;
              padding: 14rpx;
              top: -161rpx;
              border-radius: 14rpx;
              color: #ffffff;
            "
          >
            <text v-if="jionmumberdatas">{{ jionmumberdatas.brand.name }}</text>
            <text v-if="jionmumberdatas&&jionmumberdatas.brand&&jionmumberdatas.brand.nameEn&& (!jionmumberdatas.brand.name || jionmumberdatas.brand.name.toUpperCase() != jionmumberdatas.brand.nameEn.toUpperCase()) " style="margin-left: 6rpx">{{
              jionmumberdatas.brand.nameEn
            }}</text>
            <image
              style="width: 60rpx;
                     margin-left: 5px;
                     height: 23rpx; margin-left: 10rpx"
              src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/7a1fad01-7439-470b-a7e3-b0387c79b394.png"
            ></image>
          </view>
          <view
            @click="getmin"
            style="
              background: #000000;
              opacity: 0.5;
              border-radius: 46rpx;
              position: absolute;
              display: flex;
              right: 31rpx;
              top: -131rpx;
              justify-content: center;
              height: 46rpx;
              padding-left: 16rpx;
              font-size: 24rpx;
              padding-right: 16rpx;
              align-items: center;
            "
          >
            会员权益
          </view>
          <view
            @click="editmumber"
            style="
              background: #000000;
              opacity: 0.5;
              border-radius: 46rpx;
              position: absolute;
              display: flex;
              right: 170rpx;
              top: -131rpx;
              justify-content: center;
              height: 46rpx;
              padding-left: 16rpx;
              font-size: 24rpx;
              padding-right: 16rpx;
              align-items: center;
            "
          >
            修改信息
          </view>
        </view>
        <!-- 会员推荐 -->
        <shopmber ref="shopmber" v-if="shopmbers"></shopmber>
      </view>
    </view>
    <view
      v-else-if="jionmumber === 3"
      class="shopActive"
      :style="{ position: 'relative', top: '-10px' }"
    >
      <view
        style="
          border-radius: 30rpx;
          background: white;
          width: 707rpx;
          position: absolute;
          height: 100vw;
          top: -60rpx;
          padding-left: 40rpx;
          padding-right: 40rpx;
        "
      >
        <view
          style="
            font-size: 30rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #000000;
            margin-top: 30rpx;
          "
        >
          请完善以下会员信息
        </view>
        <view style="display: flex; margin-top: 40rpx; align-items: center">
          <view
            style="
              font-size: 26rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #656565;
            "
          >
            姓名
          </view>
          <view style="margin-left: 20rpx">
            <input
              type="text"
              value=""
              placeholder="姓名"
              v-model="unsername"
            />
          </view>
        </view>
        <view class="">
          <image
            style="height: 2rpx"
            src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/tcline.png"
            mode=""
          ></image>
        </view>
        <view style="display: flex; margin-top: 30rpx; align-items: center">
          <view
            style="
              font-size: 26rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #656565;
            "
          >
            电话
          </view>
          <view style="margin-left: 20rpx">
            <input
              disabled="false"
              v-model="phones"
              type="text"
              value=""
              placeholder="请输入电话"
            />
          </view>
        </view>
        <view class="">
          <image
            style="height: 2rpx"
            src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/tcline.png"
            mode=""
          ></image>
        </view>
        <!-- 	<view style="display: flex;margin-top: 30rpx;align-items: center;">
				<view style="font-size: 26rpx;font-family: PingFang SC;font-weight: 500;color: #656565;">
					地址
				</view>
				<view style="margin-left: 20rpx;">
					<input value="" placeholder="请输入地址"></input>
				</view>
			</view> -->
        <view style="margin-top: 50rpx; display: flex; justify-content: center;flex-direction: column;">
          <view
            @click="submit"
            style="
              width: 448rpx;
              height: 74rpx;
              background: #f2170f;
              border-radius: 37px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              margin: auto;
            "
          >
            提交
          </view>
          <view
            @click="goback"
            style="
              width: 448rpx;
              height: 74rpx;
              background: #f2170f;
              border-radius: 37px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              margin: auto;
              margin-top: 22rpx;
            "
          >
            返回
          </view>
        </view>
      </view>
    </view>
  </view>
  <view v-else style="margin-top: 200px; text-align: center">
    店铺会员功能，期待中
  </view>
</template>
<script>
const util = require("utils/util.js");
const app = getApp();
import api from "utils/api";
import activeCard from "../active-card/index";
import noDate from "components/base-nodata/index.vue";
import shopmber from "../shoprecommendation/shoprecommendation.vue";
import { debounce } from "@/utils/util.js";
import { getEvtscdList } from "@/pages/coupon/api/coupon.js";
import {
		mapState,
	} from 'vuex' 
export default {
  name: "jionmember",
  components: {
    activeCard,
    noDate,
    shopmber,
  },
  computed: {
    ...mapState([
    	'CustomBar',
    ]),
  },
  props: {
    heightHead: {
      type: Number,
      default: 100,
    },
    scrollTopCut: {
      type: Number,
      default: 100,
    },
    parameter: {
      type: Object,
      default: {},
    },
    jionmumberdata: {
      type: Object,
      default: {},
    },
    vipmber: {
      type: [Object, String],
      default: {},
    },
    shopInfo: {
      type: Object,
      default: {},
    },
  },
  watch: {
    parameter: {
      handler(val) {
        if (val && val.shopId) {
          // this.shopId = val.shopId
          uni.setStorageSync("shopId", val.shopId);
          app.initPage().then((res) => {
            this.relod();
          });
        }
      },
      deep: true,
      immediate: true
    },
    jionmumberdata: {
      handler(val) {
        if (val && val.brandId) {
          api.getBrandVipMember(val.brandId).then((res) => {
            this.jionmumberdatas = res.data;
            this.phone = res.data.userInfo.phone;
            this.phones = res.data.userInfo.phone;
            this.unsername = res.data.userInfo.nickName;
            if (res.data.brandVipMember === null) {
              this.jionmumber = 1;
            } else if (res.data.brandVipMember) {
              this.jionmumber = 2;
            }
            this.getHeight()
          });
        }
      },
      immediate: true
    },
    vipmber: {
      handler(val) {
        const { brandVipConfig = {} } = val;
        const { cardNotice = "" } = brandVipConfig || {};
        this.cardNotice = cardNotice;
      },
    },
    deep: true,
  },
  data() {
    return {
      scrollTop: 0,
      jionmumberdatas: "",
      CustomBar: this.CustomBar,
      submitData: {
        shopId: null,
        current: 1,
        size: 10,
        descs: "create_time", //升序字段
      },
      actData: [],
      isSetScrollTop: 0,
      jionmumber: "", // 加入会员
      phone: "",
      phones: "",
      unsername: "",
      shopId: "",
      shopmbers: true,
      couponNum: 0,
      cardNotice: '',
      topHeight: 0,
    };
  },
  created() {
    if (this.parameter && this.parameter.shopId) {
      app.initPage().then((res) => {
        // this.shopId =  安营扎寨
        // console.log(this.shopId,'11111111111111')
        this.relod();
      });
    }
  },
  mounted() {},
  methods: {
    getHeight() {
      setTimeout(() => {
        uni
        .createSelectorQuery()
        .in(this)
        .select("#jionmember")
        .boundingClientRect((res) => {
          this.topHeight = res.top;
        }).exec();
      }, 0)
    },
    goback(){
     this.jionmumber  =2 
    },
    loopUser() {
      let that =this
      const user_info = uni.getStorageSync("user_info");
      console.log("循环的获取的用户信息", user_info)
      if (!user_info) {
        setTimeout(() => {
          this.loopUser();
        }, 0);
        return;
      }
      const { id } = user_info;
      if (!id) {
        uni.navigateTo({
          url:
            "/pages/login/index?reUrl=" +
            encodeURIComponent(
              `/pages/shop/shop-detail/index?cur=6&id=`+this.parameter.shopId
            ),
        });
      } else {
        // this.relod();
        if (this.phone == ""||this.phone ===null) {
        uni.showModal({
          content: "手机号不能为空",
        });
      } else if (this.phone.length == "11") {
        let regExp = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
        if (regExp.test(this.phone)) {
          debounce(that.jionvip(), 2000);
        } else {
          uni.showModal({
            content: "手机号格式错误",
          });
        }
      } else {
        uni.showModal({
          content: "手机号格式错误",
        });
      }
      }
    },
    //授权协议
    licenseAgreement() {
      uni.navigateTo({
        url: `/pages/shop/memberAuthorization/memberAuthorization?brandId=${this.jionmumberdata.brandId}`,
      });
    },
    // 开卡须知
    activecard() {
      uni.navigateTo({
        url: `/pages/shop/InstructionsCard/InstructionsCard?brandId=${this.jionmumberdata.brandId}`,
      });
    },
    // 修改
    submit() {
      console.log(this.jionmumberdatas, "this.jionmumberdatas");
      api
        .brandvipmembers({
          brandId: this.jionmumberdata.brandId,
          name: this.unsername,
          phone: this.phones,
          id: this.jionmumberdatas.brandVipMember.id,
          brandMemberId: this.jionmumberdatas.brandVipMember.brandMemberId,
        })
        .then((res) => {
          if (res.data === false) {
            uni.showModal({
              content: "修改失败",
            });
            this.jionmumber = 2;
          } else if (res.data === true) {
            this.jionmumber = 2;
          }
        });
    },
    editmumber() {
      this.shopInfoGet();
      this.jionmumber = 3;
    },
    pointsDetails() {
      // console.log(1);/ 
      uni.navigateTo({
        url:`/pages/shop/point/index?brandId=${this.jionmumberdata.brandId}&shopId=${this.parameter.shopId}&brandPoints=${this.jionmumberdatas.brandVipMember.brandPoints}`,
      })
    },
    getmin(type) {
      if (+type === 1) {
        uni.navigateTo({
          url:
            "/pages/coupon/coupon-shop-list/index?counter=" +
            this.shopInfo.cabinetGroup,
        });
        return;
      }
      uni.navigateTo({
        url: `/pages/shop/Myrights/Myrights?brandId=${this.jionmumberdata.brandId}`,
      });
    },
    jionvip() {
      api
        .brandvipmember({
          brandId: this.jionmumberdata&&this.jionmumberdata.brandId?this.jionmumberdata.brandId:'',
          shopId: this.parameter.shopId,
          phone: this.phone,
        })
        .then((res) => {
          uni.showToast({
            title: "恭喜加入店铺会员成功",
            duration: 2000,
          });
	     	  this.getmessage()
          this.jionmumber = 2;
		  
        });
        
        this.getEvtscdList()  // 请优优惠劵接口
    },
    // 确认加入会员
    submitjoin() {
      console.log("加入会员看看")
      this.loopUser()
    },
    getmessage() {
      if (this.jionmumberdata&&this.jionmumber == "1") {
        api.getBrandVipMember(this.jionmumberdata.brandId).then((res) => {
          this.jionmumberdatas = res.data;
          this.phone = res.data.userInfo.phone;
          this.phones = res.data.userInfo.phone;
          this.unsername = res.data.userInfo.nickName;
          if (res.data.brandVipMember === null) {
            this.jionmumber = 1;
          } else if (res.data.brandVipMember) {
            this.jionmumber = 2;
          }
        });
      } else if (this.jionmumber == "2") {
        this.shopmbers = false;
        this.$nextTick(function () {
          this.shopmbers = true;
        });
        this.shopInfoGet();
      }
    },
    reachBottom() {
      if (this.loadmore) {
        this.submitData.current = this.submitData.current + 1;
        this.getShopDynamic();
      }
    },
    relod() {
      this.loadmore = true;
      this.actData = [];
      this.submitData.current = 1;
      this.getShopDynamic();
      // this.getEvtscdList();
    },
    getEvtscdList() {
      getEvtscdList({
        current: 1,
        size: 10,
        status: "01",
        tenantCode: this.shopInfo.cabinetGroup,
      }).then((res) => {
        this.couponNum = res.data.rowsCount;
      });
    },
    getShopDynamic() {
      api
        .getShopDynamic(
          Object.assign({}, this.submitData, util.filterForm(this.parameter))
        )
        .then((res) => {
          let actData = res.data;
          this.actData = [...this.actData, ...actData];
          if (actData.length < this.submitData.size) {
            this.loadmore = false;
          }
        });
    },
    shopInfoGet() {
      console.log(this.jionmumberdata, "this.jionmumberdata");
      api.getBrandVipMember(this.jionmumberdata.brandId).then((res) => {
        this.jionmumberdatas = res.data;
        this.phone = res.data.userInfo.phone;
        this.phones = res.data.userInfo.phone;
        this.unsername = res.data.brandVipMember.name;
        console.log(res.data, "res.data");
        if (res.data.brandVipMember === null) {
          this.jionmumber = 1;
        }
      });
      // 	this.$nextTick(() => {
      // 		uni.getSystemInfo({
      // 			success: function(res) { // res - 各种参数
      // 				console.log(res.windowHeight); // 屏幕的宽度
      // 				let info = uni.createSelectorQuery().select(
      // 					".shop-head"); // 获取某个元素
      // 				info.boundingClientRect(function(data) { //data - 各种参数
      // 					if (data) {
      // 						console.log("获取的高度", data.height) // 获取元素宽度
      // 						that.heightHead = data.height +
      // 							10; //有10px的底部margin
      // 					} else {
      // 						console.error("没有获取到shop-head高度")
      // 					}

      // 				}).exec()
      // 			}
      // 		});
      // 	})
    },
  },
};
</script>
<style scoped>
.shopActive {
  padding: 0 20rpx 0 20rpx;
  background: #f6f6f6;
}
</style>
