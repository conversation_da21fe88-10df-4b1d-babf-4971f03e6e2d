<template>
	<!-- 秒杀 -->
	<view class="bg-white">
		<view :style="{marginBottom: `${newData.pageSpacing}px`}" class="seckill-bg">
			<image v-if="newData.backgroundImg" :src="newData.backgroundImg" class="seckill-image"></image>
			<view class="wrapper-list-goods radius" :style="{backgroundColor: newData.background,}" :class="newData.background? newData.background.indexOf('bg-') != -1 ? newData.background : '': '' ">
				<view class="cu-bar padding-left-sm padding-right-sm">
					<view class="goods-selection text-df margin-top">
						<view class="margin-left-xs text-bold" :style="{color: `${newData.titleColor}`}">{{newData.title}}</view>
						<view class="margin-left-xs text-sm" :style="{color: `${newData.subtitleColor}`}">{{newData.subtitle}}</view>
					</view>
					<navigator :url="'/pages/seckill/seckill-list/index?shopId='+ shopId" class="bg-white round margin-top-sm text-sm margin-right-xs seckill-more">更多</navigator>
				</view>
				<view class="seckill-list">
					<scroll-view v-if="listSeckillGoodsInfo&&listSeckillGoodsInfo.length>0" class="scroll-view_x goods-detail margin-top-xs" scroll-x>
						<block v-for="(item, index) in listSeckillGoodsInfo" :key="index">
							<navigator hover-class="none" :url="'/pages/seckill/seckill-detail/index?seckillHallInfoId=' + item.seckillHallInfo.id+'&source_module=秒杀组件'"
							 class="item margin-left-sm margin-right-sm">
								<view class="img-box">
									<image class="bg-white" :src="item.picUrl ? item.picUrl : 'https://img.songlei.com/live/img/no_pic.png'"></image>
								</view>
								<view :style="{color: `${newData.titleColor}`}">
									<view class="text-cut text-sm margin-top-xs">{{item.name}}</view>
									<view class="sm margin-top-xs cu-tag line-white radius">限量 {{item.limitNum}}</view>
									<view class="ext-price text-white text-bold margin-top-xs"><text class="text-price">{{item.seckillPrice}}</text></view>
								</view>
							</navigator>
						</block>
					</scroll-view>
					<view v-else class="goods-detail text-center" :style="{color: `${newData.titleColor}`}">
						<view class="text-sm padding margin-top-lg">暂无商品信息</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	const util = require("utils/util.js");

	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
            shopId: {
                type: String
            }
		},
		mounted() {
			this.parameter.shopId = this.shopId;
			this.getData();
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				curSeckillHall: {}, //当前会场
				hasSeckill: false,
				listSeckillGoodsInfo: [], //当前时间段 秒杀商品
				parameter: {},
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: 'sort',
					descs: '' //升序字段
				},
			};
		},
		methods: {
			getData() {
				let curDate = this.$moment().format("YYYY-MM-DD");
				let that = this;

				let curHour = this.$moment().format("H");
				api.seckillhallList(curDate).then(res => {
					let seckillList = res.data;
					if (seckillList && seckillList.length > 0) {
						this.hasSeckill = true;
						let hasSeckill = false;
						seckillList.forEach((item, index) => {
							if (item.hallTime == curHour) { //默认设置当前小时的秒杀时间，如果没有就设置最近时间的秒杀时间
								this.curSeckillHall = item;
								hasSeckill = true;
								this.getSeckillGoodsData(item.id);
								return;
							} else if (!hasSeckill && item.hallTime > curHour) { //秒杀时间必须大于当前时间
								this.curSeckillHall = item;
								hasSeckill = true;
								this.getSeckillGoodsData(item.id);
								return;
							}
						})
						if (!hasSeckill) {
							this.curSeckillHall = seckillList[0];
							this.getSeckillGoodsData(this.curSeckillHall.id);
						}
					} else {
						this.hasSeckill = false;
					}
				});
			},
			getSeckillGoodsData(id) {
				this.page.size = this.newData.goodsQty;
				api.seckillinfoPage(Object.assign({
					seckillHallId: id
				}, this.page, util.filterForm(this.parameter))).then(res => {
					let listSeckillGoodsInfo = res.data.records;
					listSeckillGoodsInfo.forEach((item, index) => {
						item.progress = (item.seckillNum / item.limitNum).toFixed(2) * 100
					})
					this.listSeckillGoodsInfo = [...this.listSeckillGoodsInfo, ...listSeckillGoodsInfo];
				});
			},
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.seckill-bg {
		width: 96%;
		height: 498rpx;
		margin: auto;
	}

	.seckill-image {
		width: 96%;
		height: 498rpx;
		margin: auto;
		position: absolute;
	}

	.seckill-more {
		padding: 2px 10px;
		background: rgba(255, 255, 255, 0.71);
	}

	.seckill-list {
		padding-left: 5rpx;
		padding-right: 5rpx;
	}

	.goods-detail {
		width: auto;
		overflow: hidden;
	}

	.wrapper-list-goods {
		height: 498rpx;
		white-space: nowrap;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 200rpx;
		height: 200rpx;
	}

	.wrapper-list-goods .item .img-box {
		width: 100%;
		height: 200rpx;
		margin-top: 20rpx;
	}

	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx;
	}
</style>
