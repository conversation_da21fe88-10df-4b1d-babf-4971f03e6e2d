<template>
	<!-- 商品显示 组件 -->
	<view class="padding-tb-sm" :style="{marginBottom: `${newData.pageSpacing}px`}" >
		<view class="cu-bar justify-center">
			<view class="action text-bold text-sm" :style="{color: `${newData.titleColor}`}" >
				<text class="cuIcon-move "></text> <text class="text-sm" :class="newData.titleIcon"></text>{{newData.title}}<text class="cuIcon-move"></text>
			</view>
		</view>
		<view>
			<view v-if="newData.showType=='row'">
				<goods-row :goodsList="newData.goodsList"></goods-row>
			</view>
			<view v-else-if="newData.showType=='card'">
				<goods-card :goodsList="newData.goodsList"></goods-card>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import goodsRow from "components/goods-row/index";

    export default {
		components: {
			goodsRow
		},
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
						showType: 'row',
					    pageSpacing: 0,
					    goodsList: []
	                }
	            }
            }
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
    }
</script>

<style scoped lang="scss">


</style>
