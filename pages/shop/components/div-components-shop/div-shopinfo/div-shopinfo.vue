<template>
	<!-- 店铺信息组件 -->
	<view :style="{marginBottom: `${newData.pageSpacing}px`}">
		<view class="cu-card no-card">
			<view class="cu-item " :style="{ backgroundColor: newData.background }">
				<view class="flex content shop-detail align-center padding-sm" :class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : '' ">
					<img :src="shopInfo.imgUrl" mode="scaleToFill"></img>
					<view class="shop-text margin-left-sm" :style="{color: `${newData.textColor}`}">
						<view class="cuIcon-locationfill overflow-1 location"><text class=" text-sm margin-left-xs">{{shopInfo.address}}</text></view>
						<view class="flex collect overflow-1 margin-top-xs" :hover-stop-propagation="true" @click="callPhone(shopInfo.phone)">
							<text class="cuIcon-mobilefill"></text><text class="phone text-sm margin-left-xs">{{shopInfo.phone}}</text></view>
						<view class="flex justify-between margin-top-xs" v-if="shopInfo">
							<text class="margin-left-xs text-sm">{{shopInfo.collectCount}} 人已收藏</text>
							<view class="shop-share">
								<text @tap="shareShowFun" class="cuIcon-share"></text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import couponReceive from "@/components/coupon-receive/index";
	import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
	import api from "@/utils/api";
	import util from '@/utils/util'
	const app = getApp();
	export default {
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						rowNum: 4,
						textColor: '#333333',
						pageSpacing: 0,
						navButtons: []
					}
				}
			},
			shopId: {
				type: String
			}
		},
		components: {
			divBaseNavigator,
			couponReceive,
		},

		mounted() {
			this.shopInfoGet();
			this.couponInfoPage();
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				modalCoupon: false,
				showShare: false,
				couponInfoList: [],
				couponInfoListShow: [],
				shareParams: {},
				shopInfo: {},
			};
		},
		methods: {
			shopInfoGet() {
				if(this.shopId){
					api.shopInfoGet(this.shopId).then(res => {
						this.shopInfo = res.data
					});
				}
			},
			//查询店铺可用电子券
			couponInfoPage() {
				api.couponInfoPage({
					current: 1,
					size: 50,
					descs: 'create_time',
					shopId: this.shopId
				}).then(res => {
					this.couponInfoList = res.data.records;
				});
			},
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				});
			},
			//收藏
			userCollect() {
				let shopInfo = this.shopInfo;
				let collectId = shopInfo.collectId;
				if (collectId) {
					api.userCollectDel(collectId).then(res => {
						uni.showToast({
							title: '已取消收藏',
							icon: 'success',
							duration: 2000
						});
						shopInfo.collectId = null;
						shopInfo.collectCount = shopInfo.collectCount - 1
						this.shopInfo = shopInfo;
					});
				} else {
					api.userCollectAdd({
						type: '2',
						relationIds: [shopInfo.id]
					}).then(res => {
						uni.showToast({
							title: '收藏成功',
							icon: 'success',
							duration: 2000
						});
						shopInfo.collectId = res.data[0].id;
						shopInfo.collectCount = shopInfo.collectCount + 1
						this.shopInfo = shopInfo;
					});
				}
			},
			receiveCouponChange(obj) { //更新单条数据
				this.couponInfoList[obj.index] = obj.item;
				this.couponInfoList.splice(); //确保页面刷新成功
			},
			showModalCoupon() {
				this.modalCoupon = true;
			},

			hideModalCoupon() {
				this.modalCoupon = false;
			},
			shareShowFun() {
				this.$emit('shareShowFun')
			},
			jumpPage(page) {
				if (page) {
					window.open(page)
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.shop-detail img {
		width: 120rpx !important;
		height: 120rpx !important;
		border-radius: 6rpx;
		background-color: #ffffff;
	}
</style>
