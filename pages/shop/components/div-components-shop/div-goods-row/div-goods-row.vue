<template>
  <!-- 商品显示组件-横向排列方式显示 -->
  <view :style="{marginBottom: `${newData.pageSpacing}px`}">
    <view class="wrapper-list-goods padding-top-sm">
      <view class="flex padding-lr-sm align-center justify-between">
        <view
          class="text-df"
          :style="{color: `${newData.titleColor}`}"
        >
          <text
            class="text-bold"
            :class="newData.titleIcon"
          ></text>
          <text class="margin-left-xs">{{newData.title}}</text>
        </view>
        <view
          @click="onMoreShopGoods"
          class="text-sm"
        >更多<text class="cuIcon-right"></text></view>
      </view>
      <scroll-view
        class="scroll-view_x"
        scroll-x
        style="width:auto;overflow:hidden;"
      >
        <block
          v-for="(item, index) in newData.goodsList"
          :key="index"
        >
          <navigator
            hover-class="none"
            :url="'/pages/goods/goods-detail/index?id=' + item.id + '&source_module=' + encodeURIComponent('店铺商品行')"
            class="item flex goods-box radius"
          >
            <view class="img-box">
              <image :src="item.picUrls[0] ? item.picUrls[0] : 'https://img.songlei.com/live/img/no_pic.png'"></image>
            </view>
            <view class="text-cut margin-top text-df padding-left-sm">{{item.name}}</view>
            <view class="margin-top-xs text-sm text-gray padding-left-sm overflow-2">{{item.sellPoint}}</view>
            <view class="text-price text-red margin-left-sm margin-top-xs text-lg text-bold">{{item.priceDown}}
              <text
                v-if="item.estimatedPriceVo&&item.priceDown!=item.estimatedPriceVo.price"
                class="text-red text-xs"
                style="font-weight: 500;padding-left: 6rpx"
              >劵后价</text>

              <text
                v-if="item.inventoryState==1"
                class="text-gray text-xs"
                style="font-weight: 500;padding-left: 6rpx"
              >已售罄
              </text>

              <text
                v-if="item.estimatedPriceVo&&item.estimatedPriceVo.originalPrice!='0.0'&&item.priceDown!= item.estimatedPriceVo.originalPrice"
                class="price-original text-xs"
              >￥{{ item.estimatedPriceVo.originalPrice }}
              </text>
            </view>
          </navigator>
        </block>
      </scroll-view>
    </view>
  </view>
</template>

<script>
const app = getApp();
export default {
  components: {
  },
  props: {
    value: {
      type: Object,
      default: function () {
        return {
          title: '商品甄选',
          titleColor: 'red',
          titleIcon: 'cuIcon-message',
          pageSpacing: 0,
          goodsList: []
        }
      }
    }
  },
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
    };
  },
  methods: {
    onMoreShopGoods () {
      this.$emit('onMoreShopGoods');
    },
    jumpPage (page) {
      if (page) {
        uni.navigateTo({
          url: page
        });
      }
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper-list-goods {
  white-space: nowrap;
  padding: 10rpx;
}

.wrapper-list-goods .item {
  display: inline-block;
  width: 320rpx;
  height: 500rpx;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 10rpx;
  margin-right: 10rpx;
  background-color: #fff;
  box-shadow: 0px 0px 20px #e5e5e5;
}

.wrapper-list-goods .item .img-box {
  width: 100%;
  height: 320rpx;
}

.wrapper-list-goods .item .img-box image {
  width: 100%;
  height: 100%;
  border-radius: 5rpx 5rpx 0 0;
}

.goods-box {
  overflow: hidden;
}
</style>
