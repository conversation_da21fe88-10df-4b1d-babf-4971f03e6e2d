<template>
	<view class="shopActive padding-bottom-xs" :style="{
		backgroundColor: actData && actData.length>0?'#f4f4f4':'#ffffff',
		position:'relative'
	  }">
		<view v-if="shopName&&shopName.indexOf('何所有')>-1" style="padding: 20rpx;">
			<image v-for="(item,index) in hsypics" :src="item" style="width: 100%;" mode="widthFix" :key="index"
				@click="handlePreviewImage(index)"></image>
		</view>
		<template v-else-if="actData && actData.length>0">
			<view class="shop-content" style="padding-top: 20rpx;">
				<block v-for="(item,index) in actData" :key="index">
					<active-card v-if="item" :actInfo='item'></active-card>
				</block>
			</view>
		</template>
		<view v-else class="cu-bar justify-center">
			<no-date></no-date>
		</view>

	</view>
</template>
<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import activeCard from '../active-card/index'
	import noDate from "components/base-nodata/index.vue";
	export default {
		components: {
			activeCard,
			noDate
		},
		props: {
			heightHead: {
				type: Number,
				default: 100
			},
			scrollTopCut: {
				type: Number,
				default: 100
			},
			parameter: {
				type: Object,
				default: {}
			},
			shopName: {
				type: String,
				default: ''
			}

		},
		watch: {
			parameter: {
				handler(val) {
					if (val && val.shopId) {
						app.initPage().then(res => {
							this.relod();
						});
					}
				},
				deep: true
			}
		},
		data() {
			return {
				scrollTop: 0,
				CustomBar: this.CustomBar,
				submitData: {
					shopId: null,
					current: 1,
					size: 10,
					descs: 'create_time', //升序字段
				},
				loadmore: true,
				actData: [],
				isSetScrollTop: 0,
				hsypics: [
					'https://img.songlei.com/live/hesuoyou/hsy01.jpg',
					'https://img.songlei.com/live/hesuoyou/hsy02new.jpg'
				]
			}
		},
		created() {
			if (this.parameter && this.parameter.shopId) {
				app.initPage().then(res => {
					this.relod();
				});
			}
		},
		methods: {
			reachBottom() {
				if (this.loadmore) {
					this.submitData.current = this.submitData.current + 1;
					this.getShopDynamic();
				}
			},
			relod() {
				this.loadmore = true;
				this.actData = [];
				this.submitData.current = 1;
				this.getShopDynamic();
			},
			getShopDynamic() {
				api.getShopDynamic(Object.assign({}, this.submitData, util.filterForm(this.parameter))).then(res => {
					console.log("res", res)
					let actData = res.data;
					this.actData = [...this.actData, ...actData];
					if (actData.length < this.submitData.size) {
						this.loadmore = false;
					}
				});
			},

			handlePreviewImage(index) {
				// 预览图片
				uni.previewImage({
					urls: this.hsypics,
					current: index
				});
			},
		}
	}
</script>
<style scoped>
	.shopActive {
		padding: 0 20rpx 20rpx 20rpx;
	}
</style>