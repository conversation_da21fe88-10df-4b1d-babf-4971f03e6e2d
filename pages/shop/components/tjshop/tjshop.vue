<template>
	<view>
		<goods-card :goodsList="goodsListRecom"></goods-card>
		<view v-if="needLoadMore" :class="'cu-load ' + (loadmoreRecommendList ?'loading':'over')"></view>
	</view>
</template>

<script>
	const app = getApp();  
	import api from '@/utils/api'
	export default {
		props: {
			//接口参数
			itemId: {
				type: String,
				default: null
			},
			sceneId:{
				type: String,
				default: 'sy101'
			},
			needLoadMore: {
				type: Boolean,
				default: false
			},
			//由父组件控制什么时候可以加载推荐数据,如果需要传入itemId 就等获取到itemId再设置为true，如果不需要传入itemId直接设置为true
			canLoad: {
				type: Boolean,
				default: false
			},
			// shopId:{
			// 	type: String,
			// 	default: null
			// }
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				loadmoreRecommendList: true,
				goodsListRecom: [],
				loadDataTimes:0,
				shopId:""
			}
		},
		watch: {
			canLoad: {
				handler(newValue, oldValue) {
					this.getGoodsRecom(true,uni.getStorageSync('shopId'))		},
				immediate:true
			},
			// shopId: {
			// 	handler(val) {
			// 		if (val && val) {
			// 			// this.shopId = val
			// 			console.log(this.shopId,'999999999999999999999999999999')
   //                      this.getGoodsRecom(true,val);
			// 		}
			// 	},
			// 	deep: true   
			// },
		},
		
		methods: {
			//推荐商品
			/** 	
			 
		})
			 * @param Boolean fresh  是否刷新
			 */
			getGoodsRecom(fresh,val) {
				if (this.loadmoreRecommendList) {
					api.goodsspu({'brandSaleNumSort':true,
					'shopId':uni.getStorageSync('shopId')||val
					}).then(res => {
						this.loadDataTimes++; 	
						if (!res.data.records || res.data.records == 0 ) {
						    if(this.loadDataTimes<1){
								this.getGoodsRecom();
							}
						} else if (res.data.records || res.data.records.length>0  ){
							if(fresh) this.goodsListRecom = [ ...res.data.records];
							else this.goodsListRecom = [...this.goodsListRecom, ...res.data.records];
						}
						console.log(this.goodsListRecom,'this.goodsListRecom')
					}).catch(e => {
						this.loadmoreRecommendList = false;
					});
				}
				

			},

			loadMoreGoodsRecom() {
				if (this.needLoadMore) {
					this.getGoodsRecom(false)
				}
			}
		}
	}
</script>

<style>
	.recommend-action {
		    display: flex;
		    align-items: center;
		    height: 100%;
		    justify-content: center;
		    max-width: 100%;
	}
</style>
