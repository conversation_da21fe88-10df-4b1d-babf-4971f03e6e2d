<template>
	<view class="bg-white" @tap="goodDefaultStyle">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="isBack" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{ cartType && cartType == 3 ? '卡清单' : '购物车' }}</block>
		</cu-custom>

		<view class="cu-bar bg-white solid-bottom fixed security J-header" style="margin: 0 !important"
			v-if="shopGoodsList && shopGoodsList.effectiveData && shopGoodsList.effectiveData.length > 0">
			<view class="titleText" :class="!topTabkey ? 'active' : ''" @tap.stop="handleTopTab('', '')">
				全部
				<!-- <text class="titleTextNum">{{ goodsLength }}</text> -->
			</view>

			<scroll-view scroll-x class="nav" style="width: 66%">
				<view class="flex align-center" style="width: 500rpx">
					<view class="flex" v-if="
							shopGoodsList &&
							shopGoodsList.shoppingCartSelectVO &&
							shopGoodsList.shoppingCartSelectVO.apiCouponNameIdVOList &&
							shopGoodsList.shoppingCartSelectVO.apiCouponNameIdVOList.length
						">
						<view class="titleText"
							:class="topTabkey == 'showFullCouponId' && topTabkeyCouponId == item.couponId ? 'active' : ''"
							v-for="(item, index) in shopGoodsList.shoppingCartSelectVO.apiCouponNameIdVOList"
							:key="index" @tap.stop="handleTopTab('showFullCouponId', item.couponId)">
							{{ item.showMsg }}
						</view>
					</view>

					<view class="titleText" :class="topTabkey == 'showReducePrice' ? 'active' : ''"
						v-if="shopGoodsList && shopGoodsList.shoppingCartSelectVO && shopGoodsList.shoppingCartSelectVO.showReducePrice"
						@tap.stop="handleTopTab('showReducePrice', '1')">
						降价
					</view>
					<view class="titleText" :class="topTabkey == 'showOftenBuy' ? 'active' : ''"
						v-if="shopGoodsList && shopGoodsList.shoppingCartSelectVO && shopGoodsList.shoppingCartSelectVO.showOftenBuy"
						@tap.stop="handleTopTab('showOftenBuy', '1')">
						常购
					</view>
				</view>
			</scroll-view>

			<view class="titleText management">
				<text @tap="operationFun">{{ operation ? '管理' : '完成' }}</text>
			</view>
		</view>

		<view :class="[classObject ? 'noshopcart-card' : 'shopcart-card']" style="padding-bottom: 102rpx"
			@tap="goodDefaultStyle">
			<!-- 礼品卡 卡清单展示 没有优惠 -->
			<view v-if="giftCardType && cartType && cartType == 3">
				<view>
					<text v-if="giftCardType.includes('1')"
						class="text-black text-bold margin-left-sm shop-name">实体卡</text>
				</view>
				<checkbox-group v-if="giftCardType.includes('1')">
					<view class="cu-card" v-for="(shopInfo, shopIndex) in shopGoodsList.effectiveData"
						:key="shopInfo.shopId">
						<view class="solid-bottom radius cu-card-shop-name">
							<view class="margin-top-xs" v-for="(item, index) in shopInfo.shoppingCartData"
								:key="item.id">
								<view class="flex align-center swiper-content" v-if="item.goodsSpu.productType == '1'"
									@touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd"
									:style="{ left: slideShopIndex == shopIndex && messageIndex == index ? transformX : '0rpx' }"
									:data-shopindex="shopIndex" :data-index="index">
									<checkbox class="round margin-right-sm red"
										:class="item.checked ? theme.themeColor + ' checked ' : ''"
										@tap="checkboxChange(item, shopIndex, index)" :value="item.id"
										:disabled="(item.quantity == 0 || item.quantity > item.goodsSku.stock || item.goodsSpu.presaleType === '1') && operation"
										:checked="item.checked"></checkbox>
									<navigator hover-class="none" style="width: 100%"
										:url="'/pages/goods/goods-detail/index?id=' + item.spuId + '&source_module=' + encodeURIComponent('购物车')">
										<view class="content">
											<view class="flex flex-wrap">
												<!-- 产品图片 -->
												<image
													:src="item.goodsSku && item.goodsSku.picUrl ? item.goodsSku.picUrl : item.goodsSpu.picUrls[0]"
													mode="aspectFill" class="row-img margin-top-sm basis-3"></image>
												<view class="desc row-info padding-left-sm block basis-7">
													<!-- 产品名称 -->
													<view class="text-black margin-top-sm overflow-2">
														{{ item.goodsSpu.name }}
													</view>
													<!-- 产品规则 -->
													<view
														class="text-gray text-sm margin-top-xs cu-tag round specification"
														@tap.stop="changeSpecs(item)" :data-spuid="item.goodsSpu.id"
														:data-index="index" v-if="item.goodsSpu.specType == '1'">
														<text class="overflow-1">
															<text v-if="item.goodsSku && item.specs"
																v-for="(item2, index2) in item.specs" :key="index2">
																{{ item2.specValueName }}
																<text v-if="item.specs.length != index2 + 1">;</text>
															</text>
														</text>
														<text class="cuIcon-unfold"></text>
													</view>
													<view v-if="item.goodsSku">
														<view class="flex align-center justify-between margin-top-sm"
															@tap.stop>
															<format-price v-if="item.goodsSku.estimatedPriceVo" :color="
																	Number(item.goodsSku.estimatedPriceVo.price) != Number(item.goodsSku.estimatedPriceVo.estimatedPrice)
																		? 'black'
																		: '#e54d42'
																" signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx"
																:price="Number(item.goodsSku.estimatedPriceVo.price)" />
															<view class="margin-top-xs">
																<base-stepper :stNum="item.quantity"
																	:min="item.goodsSku.minBuyNum > 0 ? item.goodsSku.minBuyNum : 1"
																	:disabled="false" :max="item.goodsSku.stock"
																	@numChange="cartNumChang($event, item)"
																	:data-index="index"></base-stepper>
															</view>
														</view>
													</view>
												</view>
											</view>
										</view>
										<!-- 预售展示 1 预售前  2 预售中 -->
										<!-- <view v-if="item.goodsSpu.presaleType=='1'||item.goodsSpu.presaleType=='2'" class="presaleBox">
													<class-label
														:text='item.goodsSpu.presaleState'
														value='5'
														:marginBottom='0'
														:padding='[6,10]'
														:fontSize='22'
														:marginRight='12'
													></class-label>
												<text class="presale">
													{{item.goodsSpu.presaleTime}}
												</text>
											</view> -->
									</navigator>
								</view>
								<view class="swiper-btn">
									<view class="collection defaultStyles" :data-shopindex="shopIndex"
										:data-index="index" :data-id="item.id"
										@tap="userCollectAddRequest([item.spuId])">
										移入收藏
									</view>
									<view class="goodDelete defaultStyles"
										@tap="shoppingCartDelRequest([item.id], item)">删除</view>
								</view>
							</view>
						</view>
					</view>
				</checkbox-group>
				<view v-if="giftCardType.includes('2')">
					<text class="text-black text-bold margin-left-sm shop-name">电子卡</text>
				</view>
				<checkbox-group v-if="giftCardType.includes('2')">
					<view class="cu-card" v-for="(shopInfo, shopIndex) in shopGoodsList.effectiveData"
						:key="shopInfo.shopId">
						<view class="solid-bottom radius cu-card-shop-name">
							<view class="margin-top-xs" v-for="(item, index) in shopInfo.shoppingCartData"
								:key="item.id">
								<view class="flex align-center swiper-content" v-if="item.goodsSpu.productType == '2'"
									@touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd"
									:style="{ left: slideShopIndex == shopIndex && messageIndex == index ? transformX : '0rpx' }"
									:data-shopindex="shopIndex" :data-index="index">
									<checkbox class="round margin-right-sm red"
										:class="item.checked ? theme.themeColor + ' checked ' : ''"
										@tap="checkboxChange(item, shopIndex, index)" :value="item.id"
										:disabled="(item.quantity == 0 || item.quantity > item.goodsSku.stock || item.goodsSpu.presaleType === '1') && operation"
										:checked="item.checked"></checkbox>
									<navigator hover-class="none" style="width: 100%"
										:url="'/pages/goods/goods-detail/index?id=' + item.spuId + '&source_module=' + encodeURIComponent('购物车')">
										<view class="content">
											<view class="flex flex-wrap">
												<!-- 产品图片 -->
												<image
													:src="item.goodsSku && item.goodsSku.picUrl ? item.goodsSku.picUrl : item.goodsSpu.picUrls[0]"
													mode="aspectFill" class="row-img margin-top-sm basis-3"></image>
												<view class="desc row-info padding-left-sm block basis-7">
													<!-- 产品名称 -->
													<view class="text-black margin-top-sm overflow-2">
														{{ item.goodsSpu.name }}
													</view>
													<!-- 产品规则 -->
													<view
														class="text-gray text-sm margin-top-xs cu-tag round specification"
														@tap.stop="changeSpecs(item)" :data-spuid="item.goodsSpu.id"
														:data-index="index" v-if="item.goodsSpu.specType == '1'">
														<text class="overflow-1">
															<text v-if="item.goodsSku && item.specs"
																v-for="(item2, index2) in item.specs" :key="index2">
																{{ item2.specValueName }}
																<text v-if="item.specs.length != index2 + 1">;</text>
															</text>
														</text>
														<text class="cuIcon-unfold"></text>
													</view>
													<view v-if="item.goodsSku">
														<view class="flex align-center justify-between margin-top-sm"
															@tap.stop>
															<format-price v-if="item.goodsSku.estimatedPriceVo" :color="
																	Number(item.goodsSku.estimatedPriceVo.price) != Number(item.goodsSku.estimatedPriceVo.estimatedPrice)
																		? 'black'
																		: '#e54d42'
																" signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx"
																:price="Number(item.goodsSku.estimatedPriceVo.price)" />
															<view class="margin-top-xs">
																<base-stepper :stNum="item.quantity"
																	:min="item.goodsSku.minBuyNum > 0 ? item.goodsSku.minBuyNum : 1"
																	:disabled="false" :max="item.goodsSku.stock"
																	@numChange="cartNumChang($event, item)"
																	:data-index="index"></base-stepper>
															</view>
														</view>
													</view>
												</view>
											</view>
										</view>
										<!-- 预售展示 1 预售前  2 预售中 -->
										<!-- <view v-if="item.goodsSpu.presaleType=='1'||item.goodsSpu.presaleType=='2'" class="presaleBox">
													<class-label
														:text='item.goodsSpu.presaleState'
														value='5'
														:marginBottom='0'
														:padding='[6,10]'
														:fontSize='22'
														:marginRight='12'
													></class-label>
												<text class="presale">
													{{item.goodsSpu.presaleTime}}
												</text>
											</view> -->
									</navigator>
								</view>
								<view class="swiper-btn">
									<view class="collection defaultStyles" :data-shopindex="shopIndex"
										:data-index="index" :data-id="item.id"
										@tap="userCollectAddRequest([item.spuId])">
										移入收藏
									</view>
									<view class="goodDelete defaultStyles"
										@tap="shoppingCartDelRequest([item.id], item)">删除</view>
								</view>
							</view>
						</view>
					</view>
				</checkbox-group>
			</view>
			<!-- 正常商品 -->
			<checkbox-group
				v-else-if="shopGoodsList && shopGoodsList.effectiveData && shopGoodsList.effectiveData.length > 0">
				<view class="cu-card" v-for="(shopInfo, shopIndex) in shopGoodsList.effectiveData"
					:key="shopInfo.shopId">
					<view class="solid-bottom radius cu-card-shop-name">
						<navigator hover-class="none" :url="'/pages/shop/shop-detail/index?id=' + shopInfo.shopId">
							<!-- 店铺全选 -->
							<!-- <checkbox class="round" :class="shopInfo.checked?theme.themeColor+' checked':''"
								@tap.stop="shopInfoCheckboxChange(shopInfo)" :checked="shopInfo.checked"
								:disabled="shopInfo.quantity==0&&operation"></checkbox> -->
							<view class="cu-avatar sm radius margin-left-sm shop-icon cu-card-shop-name-box"
								:style="'background-image:url(' + shopInfo.imgUrl + ')'"></view>
							<text class="text-black text-bold margin-left-sm shop-name">{{ shopInfo.shopName }}</text>
							<text class="cuIcon-right text-sm"></text>
						</navigator>
						<!-- 优惠券、凑单 -->
						<view class="margin-top-xs flex justify-between align-center" style="padding: 0 10px"
							v-show="shopInfo.apiCouponNameIdVO">
							<view>
								<text v-show="shopInfo.apiCouponNameIdVO && shopInfo.apiCouponNameIdVO.showMsg"
									class="text-white text-sm margin-top-sm"
									style="background: #ff4444; padding: 2px 2px 1px; margin-right: 5px; border-radius: 3px">
									可用券
								</text>
								<text v-show="shopInfo.apiCouponNameIdVO && shopInfo.apiCouponNameIdVO.showMsg"
									class="text-sm text-black">
									{{ shopInfo.apiCouponNameIdVO && shopInfo.apiCouponNameIdVO.showMsg }}
								</text>
							</view>
							<view class="text-sm text-red" @click="goPageUrl(shopInfo.apiCouponNameIdVO.couponId)"
								v-show="!(shopInfo.apiCouponNameIdVO && shopInfo.apiCouponNameIdVO.isFullPremiseAmount)">
								凑单
								<text class="cuIcon-right text-xs"></text>
							</view>
						</view>
						<view class="margin-top-xs" v-for="(item, index) in shopInfo.shoppingCartData" :key="item.id">
							<view class="flex align-center swiper-content" @touchstart="touchStart"
								@touchmove="touchMove" @touchend="touchEnd"
								:style="{ left: slideShopIndex == shopIndex && messageIndex == index ? transformX : '0rpx' }"
								:data-shopindex="shopIndex" :data-index="index">
								<checkbox class="round margin-right-sm red"
									:class="item.checked ? theme.themeColor + ' checked ' : ''"
									@tap="checkboxChange(item, shopIndex, index)" :value="item.id"
									:disabled="(item.quantity == 0 || item.quantity > item.goodsSku.stock || item.goodsSpu.presaleType === '1') && operation"
									:checked="item.checked"></checkbox>
								<navigator hover-class="none" style="width: 100%"
									:url="'/pages/goods/goods-detail/index?id=' + item.spuId + '&source_module=' + encodeURIComponent('购物车')">
									<view class="content">
										<view class="flex flex-wrap">
											<!-- 产品图片 -->
											<view class="margin-top-sm basis-3"
												style="width: 30%; height: 6.4em; position: relative">
												<image
													:src="item.goodsSku && item.goodsSku.picUrl ? item.goodsSku.picUrl : item.goodsSpu.picUrls[0]"
													mode="aspectFill" class="row-img"
													style="width: 100% !important; height: 100% !important" />

												<view v-if="item.buyNum" class="text-center" style="
														position: absolute;
														bottom: 0;
														left: 0;
														height: 36rpx;
														line-height: 36rpx;
														background: rgba(255, 0, 0, 0.4);
														border-radius: 10rpx;
														width: 100%;
														color: #fff;
													">
													买过{{ item.buyNum }}次
												</view>
											</view>
											<view class="desc row-info padding-left-sm block basis-7">
												<!-- 产品名称 -->
												<view class="text-black margin-top-sm overflow-2">
													{{ item.goodsSpu.name }}
												</view>
												<!-- 产品规则 -->
												<view class="text-gray text-sm margin-top-xs cu-tag round specification"
													@tap.stop="changeSpecs(item)" :data-spuid="item.goodsSpu.id"
													:data-index="index" v-if="item.goodsSpu.specType == '1'">
													<text class="overflow-1">
														<text v-if="item.goodsSku && item.specs"
															v-for="(item2, index2) in item.specs" :key="index2">
															{{ item2.specValueName }}
															<text v-if="item.specs.length != index2 + 1">;</text>
														</text>
													</text>
													<text class="cuIcon-unfold"></text>
												</view>
												<!-- 产品优惠 -->
												<view class="text-red text-sm margin-top-xs" v-show="
														ticketAsync &&
														item.checked &&
														item.goodsSku.estimatedPriceVo.apiCouponNameIdVO &&
														item.goodsSku.estimatedPriceVo.apiCouponNameIdVO.showMsg
													">
													<text style="border: 1px dashed #fc3636; padding: 1px 3px">
														{{ item.goodsSku.estimatedPriceVo.apiCouponNameIdVO && item.goodsSku.estimatedPriceVo.apiCouponNameIdVO.showMsg }}
													</text>
												</view>
												<!-- <view class="flex justify-between" v-if="item.goodsSku"> -->
												<!-- <view>
														<view class="text-red text-sm margin-top-sm"
															v-if="item.goodsSku && (item.addPrice-item.goodsSku.salesPrice) > 0">
															比加入时降<view class="text-price display-ib">
																{{item.addPrice-item.goodsSku.salesPrice}}
															</view>
														</view>
													</view> -->
												<!-- <view
														:class="'text-' + (item.quantity > item.goodsSku.stock ? 'red' : 'gray') + ' margin-top-sm text-right text-sm'">
														库存{{item.goodsSku.stock}}</view> -->
												<!-- </view> -->
												<view v-if="item.goodsSku">
													<view class="flex align-center justify-between margin-top-xs"
														@tap.stop>
														<format-price v-if="item.goodsSku.estimatedPriceVo" :color="
																Number(item.goodsSku.estimatedPriceVo.price) != Number(item.goodsSku.estimatedPriceVo.estimatedPrice)
																	? 'black'
																	: '#e54d42'
															" signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx"
															:price="Number(item.goodsSku.estimatedPriceVo.price)" />
														<view class="margin-top-xs">
															<base-stepper :stNum="item.quantity"
																:min="item.goodsSku.minBuyNum > 0 ? item.goodsSku.minBuyNum : 1"
																:disabled="false" :max="item.goodsSku.stock"
																@numChange="cartNumChang($event, item)"
																:data-index="index"></base-stepper>
														</view>
													</view>
												</view>
												<view class="flex align-center margin-top-xs text-smx" v-if="
														item.goodsSku.estimatedPriceVo &&
														Number(item.goodsSku.estimatedPriceVo.estimatedPrice) !== Number(item.goodsSku.estimatedPriceVo.price)
													">
													<text class="text-red">预估到手价：</text>
													<format-price signFontSize="20rpx" smallFontSize="24rpx"
														priceFontSize="34rpx"
														:price="item.goodsSku.estimatedPriceVo.estimatedPrice" />
												</view>

												<!-- 价格波动 -->
												<view class="flex align-center text-sm" v-if="isPriceFluctuating(item)"
													@tap.stop="openPriceCheckModal(item, shopIndex, index)">
													<text>价格波动</text>
													<text class="cuIcon-right text-sm"></text>
												</view>
												<!-- 降价 -->
												<view class="text-red text-sm"
													v-else-if="item.goodsSku && item.addPrice - item.goodsSku.salesPrice > 0"
													@tap.stop="openPriceCheckModal(item, shopIndex, index)">
													降价
													<view class="margin-lr-sm text-price display-ib">
														{{ item.addPrice - item.goodsSku.salesPrice }}
														<text class="cuIcon-right text-sm"></text>
													</view>
												</view>
											</view>
										</view>
									</view>
									<!-- 预售展示 1 预售前  2 预售中 -->
									<view v-if="item.goodsSpu.presaleType == '1' || item.goodsSpu.presaleType == '2'"
										class="presaleBox">
										<class-label :text="item.goodsSpu.presaleState" value="5" :marginBottom="0"
											:padding="[6, 10]" :fontSize="22" :marginRight="12"></class-label>
										<text class="presale">
											{{ item.goodsSpu.presaleTime }}
										</text>
									</view>
								</navigator>
							</view>
							<view class="swiper-btn">
								<view class="collection defaultStyles" :data-shopindex="shopIndex" :data-index="index"
									:data-id="item.id" @tap="userCollectAddRequest([item.spuId])">
									移入收藏
								</view>
								<view class="goodDelete defaultStyles" @tap="shoppingCartDelRequest([item.id], item)">删除
								</view>
							</view>
						</view>
					</view>
				</view>
			</checkbox-group>
			<!-- 购物车没商品 -->
			<view class="text-center" style="display: flex; flex-direction: column"
				v-if="!shopGoodsList.effectiveData || (shopGoodsList.effectiveData.length <= 0 && !loadmore)">
				<view>购物车空空如也，赶快行动吧~~</view>
				<view style="display: flex; justify-content: center; align-items: center">
					<view class="margin-top">
						<image style="width: 210rpx; height: 208rpx"
							src="https://img.songlei.com/live/shopping-cart.png"></image>
					</view>
					<view style="margin-left: 20rpx">
						<view v-if="cartType && shopId"
							@click="toPage('/pages/shop/shop-detail/index?cartType=3&id=' + shopId)">
							<button class="cu-btn margin-top goShopping" :class="'bg-' + theme.themeColor">逛礼品卡</button>
						</view>
						<navigator v-else hover-class="none" url="/pages/seckill/seckill-list/index">
							<button class="cu-btn margin-top goShopping" :class="'bg-' + theme.themeColor">逛秒杀</button>
						</navigator>
						<navigator hover-class="none" url="/pages/goods/goods-list/index">
							<button class="cu-btn margin-top goSeckill" :class="'bg-' + theme.themeColor">去购物</button>
						</navigator>
					</view>
				</view>
			</view>
			<!-- 失效商品 -->
			<view class="cu-bar bg-white solid-bottom margin-top" v-if="shopGoodsList.invalidData != null">
				<view class="action">
					失效宝贝{{ shopGoodsList.invalidData == null ? 0 : getExpiredcommodity(shopGoodsList.invalidData) }}件
				</view>
				<view class="action">
					<button class="cu-btn line-red round sm" @tap="clearInvalid">清空失效宝贝</button>
				</view>
			</view>
			<view v-if="shopGoodsList.invalidData && shopGoodsList.invalidData.length > 0">
				<view class="cu-card article no-card" v-for="(shopInfo, shopIndex) in shopGoodsList.invalidData"
					:key="shopInfo.shopId">
					<view class="solid-bottom radius cu-card-shop-name">
						<navigator hover-class="none" :url="'/pages/shop/shop-detail/index?id=' + shopInfo.shopId">
							<view class="cu-avatar sm radius margin-left-sm shop-icon cu-card-shop-name-box"
								:style="'background-image:url(' + shopInfo.imgUrl + ')'"></view>
							<text class="text-black text-bold margin-left-sm shop-name">{{ shopInfo.shopName }}</text>
							<text class="cuIcon-right text-sm"></text>
						</navigator>
						<view class="margin-top-xs" v-for="(item, index) in shopInfo.shoppingCartData" :key="item.id">
							<view class="flex align-center swiper-content">
								<navigator hover-class="none" style="width: 100%"
									:url="'/pages/goods/goods-detail/index?id=' + item.spuId + '&source_module=' + encodeURIComponent('购物车')">
									<view class="content">
										<view class="flex flex-wrap">
											<image
												:src="item.goodsSku && item.goodsSku.picUrl ? item.goodsSku.picUrl : item.goodsSpu.picUrls[0]"
												mode="aspectFill" class="row-img margin-top-sm basis-3"></image>
											<view class="desc row-info padding-left-sm block basis-7">
												<view class="text-black margin-top-sm overflow-2">
													{{ item.goodsSpu.name }}
												</view>
												<!-- <view class="text-gray text-sm margin-top-xs cu-tag round specification"
													@tap.stop="changeSpecs(item)" :data-spuid="item.goodsSpu.id"
													:data-index="index" v-if="item.goodsSpu.specType == '1'">
													<text class="overflow-1">
														<text v-if="item.goodsSku" v-for="(item2, index2) in item.specs"
															:key="index2">{{item2.specValueName}}<text
																v-if="item.specs.length != (index2+1)">;</text>
														</text>
													</text>
													<text class="cuIcon-unfold"></text>
												</view> -->
												<view class="text-red"
													v-if="item.goodsSpu && item.goodsSpu.shelf == '1' && !item.goodsSku">
													请重新选择规格</view>
												<view class="margin-top-lg text-red"
													v-if="!item.goodsSpu || item.goodsSpu.shelf == '0'">已下架</view>
											</view>
										</view>
									</view>
								</navigator>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : '')"></view>
			<recommendComponents :itemId="computedRecommendId" sceneId="sy101" :canLoad="canLoad" />
		</view>
		<view v-if="shopGoodsList && shopGoodsList.effectiveData && shopGoodsList.effectiveData.length > 0"
			class="cu-bar bg-white tabbar border shop foot" style="background-color: #ffffff"
			:style="{ 'z-index': modalDiscounts ? '9999' : '1', bottom: '0' }">
			<view class="flex align-center">
				<checkbox-group @change="checkboxAllChange">
					<checkbox :disabled="isAllDisabled > 0" class="round margin-left red"
						:class="isAllSelect ? theme.themeColor + ' checked' : ''" value="all" :checked="isAllSelect">
					</checkbox>
				</checkbox-group>
				<view class="text-lg margin-left-xs" @click="checkboxAllClick">全选</view>
			</view>
			<view class="flex justify-between align-center bar-rt" v-if="operation">
				<view class="" style="flex: 1">
					<view class="flex align-center justify-end">
						<view class="text-xs" style="margin-right: 10px" v-if="selectValue && selectValue.length">
							已选购{{ selectValue.length }}件</view>
						<view class="text-xs text-bold">合计：</view>
						<format-price
							v-if="selectValue.length && totalEstimatedPriceVo && totalEstimatedPriceVo.estimatedPrice"
							signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx"
							:price="totalEstimatedPriceVo.estimatedPrice"></format-price>
						<format-price v-else signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx"
							:price="settlePrice"></format-price>
						<!-- <text class="text-xl text-bold text-price text-red">{{settlePrice}}</text> -->
					</view>
					<view class="flex align-center justify-end" v-if="selectValue && selectValue.length">
						<view class="flex align-center" style="margin-right: 10px" v-if="totalSale">
							<view class="text-xs text-red">共减：</view>
							<format-price signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="28rpx"
								:price="totalSale"></format-price>
						</view>
						<text class="text-xs text-red" @click="updataModalDiscounts">
							查看明细
							<!-- <text class="cuIcon-top text-sm"></text> -->
						</text>
					</view>
				</view>
				<button :disabled="showOrderConfirm"
					class="cu-btn round text-white shadow-blur lg margin-left-sm settle-bt" style="width: 210rpx"
					:class="'bg-' + theme.themeColor" type="" @tap="orderConfirm">
					{{ settleText }}
				</button>
			</view>
			<view class="action bar-rt" v-if="!operation">
				<button class="cu-btn round line-orange" :disabled="showUserCollectAdd"
					@tap="userCollectAdd">移入收藏夹</button>
				<button class="cu-btn round line-red margin-left-sm" :disabled="showShoppingCartDel"
					@tap="shoppingCartDel">删除</button>
			</view>
		</view>
		<goods-sku v-if="modalSku" :goodsSpu="goodsSpu" :cartNum="shoppingCartSelect.quantity"
			@numChange="shoppingCartSelect.quantity = $event" :goodsSku="goodsSku" :shopInfo="shopInfo"
			@changeGoodsSku="goodsSku = $event" :goodsSpecData="goodsSpecData" @changeSpec="goodsSpecData = $event"
			:shoppingCartId="shoppingCartSelect.id" :modalSku="modalSku" @changeModalSku="modalSku = $event"
			:modalSkuType="modalSkuType" @operateCartEvent="operateCartEvent" ref="goodsSkuRef"></goods-sku>

		<discounts-details v-if="modalDiscounts" :settlePrice="settlePrice" :modalDiscounts="modalDiscounts"
			@updataModalDiscounts="updataModalDiscounts" :discountsPrice="totalEstimatedPriceVo"
			:shoppingCartData="shopGoodsList.effectiveData" :operation="operation"
			@checkChange="checkDetailsChange"></discounts-details>

		<!-- 价格查价弹框 -->
		<PriceCheck v-if="isPriceCheckModalShow" :goodsObj="priceCheckGood" :isModalShow="isPriceCheckModalShow"
			@close="isPriceCheckModalShow = false" @checkboxChange="checkboxChange" @orderConfirm="orderConfirm" />
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api'
	import baseStepper from "@/components/base-stepper/index";
	import discountsDetails from "@/components/discounts-details/index"; // 优惠明细
	import recommendComponents from "components/recommend-components/index";
	import formatPrice from "@/components/format-price/index.vue"
	import goodsSku from "@/components/goods-sku/goods-sku";
	import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js";
	import PriceCheck from "./price-check.vue"
	import {
		senTrack
	} from '@/public/js_sdk/sensors/utils.js';

	export default {
		mixins: [navigateUtil],
		props: {
			isBack: {
				type: Boolean,
				default: false,
			},
			cartType: {
				type: String | Number,
				default: ''
			},
			shopId: {
				type: String | Number,
				default: ''
			},
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					current: 1,
					size: 50,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				showOrderConfirm: true, // 是否可以结算
				showUserCollectAdd: true, // 是否可以移入收藏夹
				showShoppingCartDel: true, // 是否可以删除
				loadmore: true, // 购物车数据加载提示
				operation: true, // 判断是显示 结算按钮 还是 管理按钮

				shopGoodsList: { // 购物车所有商品 有效 无效
					effectiveData: [],
					invalidData: []
				},
				shoppingCartData: [], // 选中商品
				isAllSelect: false, // 全选按钮 是否选中
				selectValue: [], // 选中商品id集合
				settlePrice: 0, // 结算金额
				goodsSpu: {}, // 更换规格-商品信息
				shopInfo: {},
				goodsSpecData: [], // 更换规格-商品规格查询
				modalSku: false,
				modalSkuType: '1',
				shoppingCartSelect: { // 更换规格选中的商品信息
					quantity: 1
				},
				goodsSku: {}, // 库存信息
				transformX: '0rpx',
				messageIndex: -1, // 当前店铺商品索引
				slideShopIndex: -1, // 当前店铺索引
				canLoad: false,
				isFirst: true, // 用于校验 回退到购物车 不刷新
				// 商品数量
				goodsLength: app.globalData.shoppingCartCount,
				totalSale: 0, // 共减价
				modalDiscounts: false, // 优惠明细
				totalEstimatedPriceVo: {}, // 总优惠
				backupsShopGoodsList: {}, // 购物车数据备份
				discountsDetailsLoad: false,
				numLoad: false, // 限制重复请求改变数量接口
				ticketAsync: false, // 判断是否显示优惠券
				topTabkey: '',
				topTabkeyCouponId: '',
				isPriceCheckModalShow: false, // 价格查询弹框
				priceCheckGood: {}, // 价格查询弹框商品信息
			};
		},

		components: {
			baseStepper,
			recommendComponents,
			formatPrice,
			discountsDetails, // 优惠明细
			goodsSku,
			PriceCheck
		},
		computed: {
			//处理礼品卡 实体还是电子显示
			giftCardType() {
				if (this.shopGoodsList && this.shopGoodsList.effectiveData && this.shopGoodsList.effectiveData.length >
					0 && this.cartType && this.cartType == '3') {
					let giftCrad = []
					if (this.shopGoodsList.effectiveData[0].shoppingCartData && this.shopGoodsList.effectiveData[0]
						.shoppingCartData.length > 0) {
						let goodsList = this.shopGoodsList.effectiveData[0].shoppingCartData
						// 实体卡1
						let entityList = goodsList.filter((item) => {
							return item.goodsSpu.productType == '1'
						})
						// 电子卡2
						let electronList = goodsList.filter((item) => {
							return item.goodsSpu.productType == '2'
						})
						if (entityList && entityList.length > 0 && electronList && electronList.length > 0) {
							return giftCrad = ['1', '2']
						}
						if (entityList && entityList.length > 0) {
							giftCrad = ['1']
						} else if (electronList && electronList.length > 0) {
							giftCrad = ['2']
						}
						return giftCrad
					}
					return []
				}
				return []
			},

			classObject() {
				if (!this.shopGoodsList.effectiveData || this.shopGoodsList.effectiveData.length <= 0) {
					return true;
				} else {
					return false;
				}
			},
			settleText() {
				if (this.selectValue.length && this.totalEstimatedPriceVo?.apiCouponNameIdVO) {
					return this.totalEstimatedPriceVo?.apiCouponNameIdVO?.couponUserId ? '结算' : '领券结算'
				} else {
					return '结算'
				}
			},
			isAllDisabled: function() {
				if (!this.shopGoodsList.effectiveData || this.shopGoodsList.effectiveData.length <= 0) {
					return true;
				} else {
					return false;
				}
			},

			computedRecommendId() {
				let result = ""
				if (this.shopGoodsList.effectiveData && this.shopGoodsList.effectiveData.length > 0) {
					this.shopGoodsList.effectiveData.forEach(function(shop, index) {
						if (shop && shop.shoppingCartData && shop.shoppingCartData.length > 0) {
							shop.shoppingCartData.forEach((good) => {
								if (result) result += ','
								result += good.spuId
							})
						}
					})
				}

				if (this.shopGoodsList.invalidData && this.shopGoodsList.invalidData.length > 0) {
					this.shopGoodsList.invalidData.forEach(function(shop, index) {
						if (shop && shop.shoppingCartData && shop.shoppingCartData.length > 0) {
							shop.shoppingCartData.forEach((good) => {
								if (result) result += ','
								result += good.spuId
							})
						}
					})
				}

				return result;
			},

		},
		watch: {
			shoplist() {
				if (this.shopGoodsList.effectiveData && this.shopGoodsList.effectiveData.length >= 0) {
					return true;
				} else {
					return false;
				}
			},
			showOrderConfirm() {},
			selectValue(val) {
				if (this.selectValue.length > 0) {
					this.showOrderConfirm = false;
					this.showUserCollectAdd = false;
					this.showShoppingCartDel = false;
				} else {
					this.showOrderConfirm = true;
					this.showUserCollectAdd = true;
					this.showShoppingCartDel = true;
					this.isAllSelect = false;
				}
			}
		},

		mounted() {
			// this.isFirst = true;
			// app.initPage().then(res => {
			// this.shoppingCartPage();
			// });
			this.shoppingCartPage();
		},

		created() {
			this.direction = ''
			this.startX = 0
			this.startY = 0
			this.btnGroupWidth = 254;
			this.isMoving = false;
			// this.shoppingCartPage();

		},

		methods: {
			//获取失效商品数量
			getExpiredcommodity(shopingitem) {
				let number = 0
				shopingitem.forEach(function(item) {
					if (item.shoppingCartData && item.shoppingCartData.length >= 0) {
						number += item.shoppingCartData.length
					}
				})
				return number
			},
			// 初始化
			info() {
				this.isAllSelect = false; // 全选默认未选择
				this.selectValue = []; // 选中商品id集合
				this.shoppingCartData = []; // 选中商品
				this.settlePrice = 0; // 结算金额
			},
			//管理按键事件
			operationFun() {
				this.operation = !this.operation;
				this.checkboxHandle(this.selectValue);
			},
			//加载数据
			shoppingCartPage(key, val) {
				const params = {
					...this.page
				}
				if (this.cartType == 3) {
					params.type = this.cartType
				}

				if (key && val) {
					params[key] = val
				}

				this.info();
				this.canLoad = false
				api.shoppingCartPage(params).then(res => {
					if (res.data && res.data.effectiveData && res.data.effectiveData.length > 0) {
						res.data.effectiveData.forEach((item, index) => {
							item.shoppingCartData.forEach(shopItem => {
								shopItem.checked = false;

								//添加预售时间 1 预售前  2 预售后
								if (shopItem.goodsSpu.presaleType === '1') {
									let time = shopItem.goodsSpu.goodsSpuInfoVo.presaleStartTime
										.substr(5, 11).replace('-', '月')
									let mouthDay = time.substr(0, 5); //月 日
									let ytd = time.substr(5, 9); //时间
									console.log("handlePresaleStartTime",
									`${mouthDay}日 ${ytd} 开售`);
									// return `${mouthDay}日 ${ytd} 开始发售`
									shopItem.goodsSpu.presaleTime = `${mouthDay}日 ${ytd} 开始发售`
								}
								if (shopItem.goodsSpu.presaleType === '2') {
									let time = shopItem.goodsSpu.goodsSpuInfoVo.presaleEndTime
										.substr(5, 11).replace('-', '月')
									let mouthDay = time.substr(0, 5); //月 日
									let ytd = time.substr(5, 9); //时间
									// return `${mouthDay}日\n${ytd} 活动结束`
									shopItem.goodsSpu.presaleTime = `${mouthDay}日 ${ytd} 活动结束`
								}
							})
						})
						//更新购物车数量
						let _shoppingCartCount = 0;
						res.data.effectiveData.forEach(item => {
							if (item.shoppingCartData && item.shoppingCartData.length >= 0) {
								_shoppingCartCount += item.shoppingCartData.length
							}
						})
						// app.globalData.shoppingCartCount = res.data.effectiveData.length + '';
						app.globalData.shoppingCartCount = _shoppingCartCount + '';
						this.goodsLength = _shoppingCartCount
						uni.$emit("updateCart");
					} else {
						app.globalData.shoppingCartCount = '0';
						uni.$emit("updateCart");
						this.goodsLength = 0;
					}
					this.shopGoodsList = JSON.parse(JSON.stringify(res.data));
					this.backupsShopGoodsList = JSON.parse(JSON.stringify(res.data));
					this.loadmore = false; // 隐藏加载框
					this.$nextTick(() => {
						//是否可以加载推荐数据
						this.canLoad = true
					})
				});
			},

			// 数量变化
			async cartNumChang(val, item) {
				try {
					if (this.numLoad) return
					this.numLoad = true
					let quantity = Number(val);
					item.quantity = quantity;
					await this.shoppingCartUpdate({
						id: item.id,
						quantity: quantity
					});
					await this.countSelect();

				} catch (e) {

				} finally {
					this.numLoad = false
				}

			},

			async shoppingCartUpdate(parm) {
				await api.shoppingCartUpdate(parm);
			},

			//收藏
			userCollectAdd() {
				let selectValue = this.selectValue;
				let shoppingCartData = this.shoppingCartData;
				let selectSpuValue = [];
				shoppingCartData.forEach(function(shoppingCart, index) {
					let selectValueIndex = selectValue.indexOf(shoppingCart.id);

					if (selectValueIndex > -1) {
						selectSpuValue.push(shoppingCart.spuId);
					}
				});
				this.userCollectAddRequest(selectSpuValue)
			},
			// userCollectAddSingle 单个收藏执行
			userCollectAddSingle(e) {
				let dataset = e.target.dataset;
				this.userCollectAddRequest([dataset.id])
			},
			// 收藏执行函数
			userCollectAddRequest(selectSpuValue) {
				let that = this;
				api.userCollectAdd({
					type: '1',
					relationIds: selectSpuValue
				}).then(res => {

					uni.showToast({
						title: '收藏成功',
						icon: 'success',
						duration: 2000
					});
					that.shoppingCartPage();
				});

			},
			// 恢复显示
			goodDefaultStyle() {
				this.transformX = 0;
			},

			// 删除
			shoppingCartDel() {
				let selectValue = this.selectValue;
				let that = this;
				if (selectValue && selectValue.length > 0) {
					uni.showModal({
						content: '确认将这' + selectValue.length + '个宝贝删除',
						cancelText: '我再想想',
						confirmColor: '#ff0000',
						success(res) {
							if (res.confirm) {
								that.shoppingCartDelRequest(selectValue);
							}
						}

					});
				}
			},
			shoppingCartDelRequest(selectValue, item) {
				let that = this;
				api.shoppingCartDel({
					ids: selectValue
				}).then(res => {
					that.selectValue = [];
					that.isAllSelect = false;
					that.settlePrice = 0;
					that.shoppingCartPage();
					app.shoppingCartCount()
				});

			},

			clearInvalid() {
				let selectValue = [];
				let that = this;
				this.shopGoodsList.invalidData.forEach(function(shoppingCart, index) {
					shoppingCart.shoppingCartData.forEach(good => {
						selectValue.push(good.id);
					})
				});
				uni.showModal({
					content: '确认清空失效的宝贝吗',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							api.shoppingCartDel({
								ids: selectValue
							}).then(res => {
								that.shopGoodsList.invalidData = [];
							});
						}
					}

				});
			},

			isCanBuy(shoppingCart) { //是否购买操作
				return shoppingCart.goodsSku && shoppingCart.quantity <= shoppingCart.goodsSku.stock;
			},
			//单个商品勾选点击
			checkboxChange(item, storeIndex, shopIndex) {

				item.checked = !item.checked;

				// 价格比对的弹框（价格波动和降价）里面的返回并勾选按钮更新数据
				this.shopGoodsList.effectiveData[storeIndex].shoppingCartData[shopIndex] = item

				let index = this.selectValue.indexOf(item.id);
				let shoppingCartData = this.shoppingCartData;
				if (item.checked === true) {
					if (index === -1) {
						this.selectValue.push(item.id);
						shoppingCartData.push(item)

					}
				} else {
					if (index !== -1) {
						this.selectValue.splice(index, 1);
						shoppingCartData.splice(index, 1)

						this.selectValue && this.selectValue.length && this.cancelCheck(storeIndex, shopIndex)
					}
				}
				this.shoppingCartData = shoppingCartData;

				this.checkboxHandle(this.selectValue);
			},

			// 判断全选按钮是否选中
			checkboxHandle(selectValue) {
				let that = this;

				// 需要一个默认值，没有默认值在没有商品得时候点击管理会出现null数据，导致forEach报错，不会执行后面得数据
				let shoppingCartData = this.shopGoodsList.effectiveData || [];

				// 	shoppingCartData =[];
				let isAllSelect = false;
				let _leng = 0;
				//预售商品
				let presalseLeng = []
				shoppingCartData.forEach((item, i) => {
					item.shoppingCartData.forEach(good => {
						// console.log("==good==",good);
						if (good.goodsSpu.presaleType === '1') {
							presalseLeng.push(good)
						}
					})
					if (item.shoppingCartData && item.shoppingCartData.length >= 0) {
						_leng += item.shoppingCartData.length;
					}
					//减掉预售商品
					if (presalseLeng.length > 0) {
						_leng = _leng - presalseLeng.length
					}
				})

				if (_leng == selectValue.length) {
					isAllSelect = true;

				}
				this.isAllSelect = isAllSelect;
				this.selectValue = selectValue;

				!this.selectValue.length && this.cancelCheck();
				this.countSelect(); // 计算总价
			},
			checkboxAllClick() {
				if (this.isAllDisabled) {
					this.isAllSelect = false
				} else {
					this.isAllSelect = !this.isAllSelect;
					this.setAllSelectValue(this.isAllSelect);
				}
			},
			checkboxAllChange(e) {
				this.updataModalDiscounts('close') // 关闭优惠详情弹框
				var value = e.detail.value;
				if (value && value.length > 0) {
					this.isAllSelect = true;
					this.setAllSelectValue(true);
				} else {
					this.isAllSelect = false;
					this.setAllSelectValue(false);
				}
			},

			setAllSelectValue(status) {
				let shoppingCartData = this.shopGoodsList.effectiveData;
				let selectValue = [];
				let that = this;
				let _shoppingCartList = []; // 选中的商品数组

				if (shoppingCartData && shoppingCartData.length > 0) {
					if (status) {
						shoppingCartData.forEach(function(shoppingCart, index) {
							shoppingCart.shoppingCartData.forEach(item => {

								//预售中选择
								if (item.goodsSpu.presaleType && item.goodsSpu.presaleType === '1') {
									item.checked = false;
								} else {
									item.checked = true;
									selectValue.push(item.id);
									_shoppingCartList.push(item);
								}

							})
						});
					} else {
						shoppingCartData.forEach(function(shoppingCart) {
							shoppingCart.shoppingCartData.forEach(item => {
								item.checked = false;
							})
						})
					}
					this.shoppingCartData = _shoppingCartList;
					this.checkboxHandle(selectValue);
				}
			},

			//计算结算值
			countSelect() {
				let selectValue = this.selectValue;
				let settlePrice = 0;
				if (selectValue && selectValue.length <= 0) {
					this.settlePrice = settlePrice;
				} else if (this.shoppingCartData) {
					this.shoppingCartData.forEach(function(shoppingCart, index) {
						if (selectValue.indexOf(shoppingCart.id) > -1 && shoppingCart.goodsSku && shoppingCart
							.quantity <= shoppingCart.goodsSku.stock) {
							settlePrice = Number(settlePrice) + Number(shoppingCart.quantity) * Number(shoppingCart
								.goodsSku.estimatedPriceVo.estimatedPrice);
						}
					});
					console.log(settlePrice, '=========计算结算值=======')
					this.settlePrice = settlePrice.toFixed(2)
					this.cartType != 3 && this.selectValue.length && this.getDiscountsDetails(); // 请求接口获取优惠详情
				}
			},
			//更换规格
			changeSpecs(item) {
				this.goodsSpu = {};
				this.goodsSpecData = [];
				this.shoppingCartSelect = item;
				this.modalSku = true;
				this.shopInfo = item.shopInfo;
				this.goodsGet(item.spuId);
			},

			goodsGet(id) {
				api.goodsGet(id).then(res => {
					this.goodsSpu = res.data;
					this.goodsSpecGet(id);
				});
			},

			goodsSpecGet(spuId) {
				api.goodsSpecGet({
					spuId: spuId
				}).then(res => {
					let goodsSpecData = res.data;
					let shoppingCartSelect = this.shoppingCartSelect;
					if (shoppingCartSelect.goodsSku) {
						//回显
						shoppingCartSelect.specs.forEach(function(spec, index) {
							goodsSpecData.forEach(function(goodsSpec, index) {
								if (spec.specId == goodsSpec.id) {
									goodsSpec.checked = spec.specValueId;
								}
							});
						});
					}
					this.goodsSpecData = goodsSpecData;
					this.goodsSku = shoppingCartSelect.goodsSku ? shoppingCartSelect.goodsSku : {};
					this.$refs.goodsSkuRef?.initSpecData(goodsSpecData); // 购物车这里加载商品信息加载完成后直接调用初始化规格方法
				});
			},

			operateCartEvent() {
				this.shoppingCartPage();
			},

			//结算
			async orderConfirm() {
				const apiCouponNameId = this.totalEstimatedPriceVo?.apiCouponNameIdVO
				let couponRes = null
				let couponIds = ''
				if (apiCouponNameId && !apiCouponNameId?.couponUserId) {
					const params = {
						couponId: apiCouponNameId.couponId,
						errModalHide: true // 不显示错误弹框
					}
					couponRes = await api.couponUserSave(params)
				}

				let params = [];
				let shoppingCartData = this.shoppingCartData;
				// console.error("shoppingCartData===>", shoppingCartData);
				let goodsIds = [],
					sku_id_list = [],
					sku_name_list = [],
					goods_amount = 0,
					goods_name_list = [],
					store_id_list = [],
					vender_id_list = [];
				shoppingCartData.forEach(function(shoppingCart, index) {
					if (shoppingCart.checked && shoppingCart.goodsSku && shoppingCart.goodsSku.enable == '1' &&
						shoppingCart.goodsSpu &&
						shoppingCart.goodsSpu.shelf == '1' && shoppingCart.quantity <= shoppingCart.goodsSku
						.stock
					) {
						goodsIds.push(shoppingCart.spuId);
						params.push(shoppingCart.id);
						goods_amount += shoppingCart.quantity;
						goods_name_list.push(shoppingCart.spuName);
						if (shoppingCart.goodsSpu.shopCode > 0) {
							store_id_list.push(shoppingCart.goodsSpu.shopCode);
						}
						if (shoppingCart.goodsSpu.shopId > 0) {
							vender_id_list.push(shoppingCart.goodsSpu.shopId);
						}
						sku_id_list.push(shoppingCart.skuId);
						if (shoppingCart.specInfo) {
							sku_name_list.push(shoppingCart.specInfo);
						}else {
							sku_name_list.push(shoppingCart.spuName+'-单规格');
						}
					}

					senTrack("ShoppingCartSettleDetail", {
						settle_method: '购物车结算',
						activity_id: '', 
						goods_id: shoppingCart.spuId,
						sku_id: shoppingCart.skuId,
						sku_name: shoppingCart.specInfo || (shoppingCart.spuName+'-单规格'),
						goods_amount: shoppingCart.quantity,
						goods_name: shoppingCart.spuName,
						// 商品划线价
						goods_crossed_price: shoppingCart.salesPrice,
						 // 实付金额
						paid_amount: shoppingCart.goodsSku.estimatedPriceVo.estimatedPrice,
						// 折扣金额
						discount_amount: shoppingCart.goodsSku.estimatedPriceVo.promotionsDiscountPrice,
						// 优惠券金额
						coupon_amount: shoppingCart.goodsSku.estimatedPriceVo.coupon,
						// 优惠券ID列表
						coupon_id_list: shoppingCart.goodsSku.estimatedPriceVo?.apiCouponNameIdVO?.couponId>0 ? [shoppingCart.goodsSku.estimatedPriceVo.apiCouponNameIdVO.couponId]:[],
						// 门店ID
						store_id: shoppingCart.goodsSpu.shopCode,
						// 楼层ID
						LCID: shoppingCart.goodsSpu.floor,
						// 品类ID
						PLID: shoppingCart.goodsSpu.categoryId,
						// 品牌ID
						goods_brand_id: shoppingCart.goodsSpu.brand,
						// 专柜ID
						shoppe_id:	shoppingCart.goodsSpu.cabinetGroup,					
						// 商家ID
						vender_id: shoppingCart.goodsSpu.shopId,
						business_format: shoppingCart.goodsSpu.formatType,
						is_pre_sale: false,
						is_flash_sale: false
					});
					
				});
				/* 把参数信息异步存储到缓存当中 */
				this.isFirst = false;
				uni.setStorage({
					key: 'param-orderConfirm',
					data: {
						source: this.cartType == 3 ? 7 : 1,
						shoppingCarItemIds: params,
						goodsIds: goodsIds
					}
				});

				if (apiCouponNameId?.couponUserId) {
					couponIds = apiCouponNameId.couponId
				}
				if (couponRes?.code === 0 && couponRes?.data) {
					couponIds = couponRes.data.couponId
				}
				const url = couponIds ?
					`/pages/order/order-confirm/index?couponId=${couponIds}` :
					'/pages/order/order-confirm/index'

				uni.navigateTo({
					url: url
				});

				//发送给神策 购物车结算
				senTrack("ShoppingCartSettle", {
					// 商品id列表
					goods_id_list: goodsIds,
					sku_id_list,
					sku_name_list,
					// 结算的商品数量总和
					goods_amount,
					goods_name_list,
					// 商品原价
					goods_crossed_price: Number(this.totalEstimatedPriceVo.price),
					// 支付价格
					paid_amount: Number(this.totalEstimatedPriceVo.estimatedPrice),
					// 结算商品折扣金额总和（不包含优惠券）
					discount_amount: Number(this.totalEstimatedPriceVo.promotionsDiscountPrice),
					coupon_amount: Number(this.totalEstimatedPriceVo.coupon),
					coupon_id_list: couponIds ? couponIds.split(",") : [],
					store_id_list,
					vender_id_list
				});


			},

			touchStart(event) {
				if (event.currentTarget.dataset.disabled === true) {
					return;
				}
				this.startX = event.touches[0].pageX;
				this.startY = event.touches[0].pageY;
			},
			// 手指触摸元素后移动。
			touchMove(event) {
				if (this.direction === 'Y' || event.currentTarget.dataset.disabled === true) {
					this.direction = '';
					return;
				}
				var moveY = event.touches[0].pageY - this.startY,
					moveX = event.touches[0].pageX - this.startX;
				if (!this.isMoving && Math.abs(moveY) > Math.abs(moveX) || Math.abs(moveY) > 100 || Math.abs(moveX) <
					50) { //纵向滑动//参数100与50可调节侧滑灵敏度
					this.direction = 'Y';
					return;
				}
				this.direction = moveX > 0 ? 'right' : 'left';
				this.messageIndex = moveX < 0 ? event.currentTarget.dataset.index : -1;
				this.slideShopIndex = moveX < 0 ? event.currentTarget.dataset.shopindex : -1;
				this.isMoving = true;

			},
			touchEnd(event) {
				this.isMoving = false;
				if (this.direction !== 'right' && this.direction !== 'left') {
					this.direction = '';
					return;
				}
				if (this.direction == 'right') {
					this.messageIndex = -1;
					this.slideShopIndex = -1;

				}
				this.endMove(event)
			},
			endMove(event) {
				if (this.direction === 'Y' || event.currentTarget.dataset.disabled === true) {
					this.direction = '';
					return;
				}
				if (this.messageIndex !== -1) {
					// this.transformX = `translateX(${-this.btnGroupWidth}px)`;
					this.transformX = `${-this.btnGroupWidth}rpx`;
				} else {
					// this.transformX = 'translateX(0px)';
					this.transformX = '0px';
				}
				this.direction = '';
			},

			// 获取优惠详情数据
			async getDiscountsDetails() {
				try {
					if (this.discountsDetailsLoad) return

					this.discountsDetailsLoad = true
					const params = {
						ids: this.selectValue.join(",")
					}
					const res = await api.cartEstimatePrice(params)

					this.totalEstimatedPriceVo = res.data.totalEstimatedPriceVo
					this.totalSale = (Number(res.data.totalEstimatedPriceVo.promotionsDiscountPrice) + Number(res.data
						.totalEstimatedPriceVo.coupon)).toFixed(2)
					const cartList = res.data.cartList
					cartList.forEach((item) => {
						item.checked = true
					})

					this.handleStoreData(cartList)
				} catch (e) {

				} finally {
					this.discountsDetailsLoad = false
				}
			},
			// 在购物车源数据里查找需要替换的数据位置
			async handleStoreData(cartList) {
				const _this = this
				await this.shopGoodsList.effectiveData.forEach((storeItem) => {
					cartList.forEach((goodsItem) => {
						if (storeItem.shopId === goodsItem.shopId) {
							storeItem.shoppingCartData.forEach((item, i) => {
								if (item.id === goodsItem.id) {
									_this.$set(storeItem.shoppingCartData[i].goodsSku,
										"estimatedPriceVo", goodsItem.goodsSku
										.estimatedPriceVo)
									_this.$set(storeItem.shoppingCartData[i].goodsSku
										.estimatedPriceVo, "apiCouponNameIdVO", goodsItem
										.goodsSku.estimatedPriceVo.apiCouponNameIdVO)
									_this.$set(storeItem.shoppingCartData[i], "quantity",
										goodsItem.goodsSku.estimatedPriceVo.quantity)
									// _this.$set(storeItem.shoppingCartData, i, goodsItem)
								}
							})
						}
					})
				})
				this.ticketAsync = true

			},
			// 取消勾选数据还原
			cancelCheck(sotreIndex, shopIndex) {
				if (sotreIndex >= 0 && shopIndex >= 0) {
					this.$set(this.shopGoodsList.effectiveData[sotreIndex].shoppingCartData[shopIndex].goodsSku,
						'estimatedPriceVo', this.backupsShopGoodsList?.effectiveData[sotreIndex].shoppingCartData[
							shopIndex].goodsSku.estimatedPriceVo)
				} else {
					this.totalEstimatedPriceVo = {}
					this.shoppingCartPage()
				}
				this.ticketAsync = false

			},

			// 凑单跳转页面
			goPageUrl(id) {
				uni.navigateTo({
					url: `/pages/goods/goods-list/index?couponId=${id}`
				});
			},

			// 更新优惠明细显示字段
			updataModalDiscounts(data) {
				if (data === 'close') {
					this.modalDiscounts = false
				} else {
					this.modalDiscounts = !this.modalDiscounts
				}
			},
			// 优惠明细选择
			checkDetailsChange(shop, storeIndex, shopIndex) {
				// if (flag) {
				this.$set(this.shopGoodsList.effectiveData[storeIndex].shoppingCartData, shopIndex, shop)
				// }
				this.checkboxChange(shop, storeIndex, shopIndex)
			},
			// 点击顶部 tab
			handleTopTab(key, val) {
				this.topTabkey = key || ''

				this.topTabkeyCouponId = (key == 'showFullCouponId') ? val : ''
				this.shoppingCartPage(key, val)
			},

			// 打开价格查询
			openPriceCheckModal(goods, shopIndex, goodsIndex) {
				this.priceCheckGood = {
					goods,
					shopIndex,
					goodsIndex
				}
				this.isPriceCheckModalShow = true
			},
			isPriceFluctuating(item) {
				if (!item?.buyNum || !item?.goodsSku) return false;
				return (
					item.addPrice != item.goodsSku.salesPrice ||
					item.goodsSku.salesPrice != item.minBuyPrice
				);
			},
		}
	};
</script>
<style>
	/* checkbox::before {
		right: 2px !important;
		top: 10px !important;
	} */

	checkbox .wx-checkbox-input,
	checkbox .uni-checkbox-input {
		width: 20px;
		height: 20px;
	}
</style>
<style lang="scss" scoped>
	.presaleBox {
		padding-top: 20rpx;
		color: #d7792d;
	}

	.bg-white {
		background-color: inherit;
	}

	.J-header {
		// width: 100%;
		// height: 55rpx;
		background: #ffffff;
	}

	.security {
		/* margin-top:80rpx; */
		min-height: 80rpx;
		box-shadow: unset !important;
		justify-content: flex-start;
	}

	.cu-bar .action:first-child {
		margin-left: 58rpx;
		font-size: 26rpx;
		font-weight: bold;
		color: #fc3636;
	}

	.management {
		font-size: 26rpx;
		color: #484848;
	}

	.cu-bar .action:last-child {
		margin-right: 68rpx;
	}

	.shopcart-card {
		// margin-top: 77rpx;
		margin-top: 102rpx;
		padding-bottom: calc(100upx + env(safe-area-inset-bottom) / 2);
	}

	.noshopcart-card {
		margin-top: 40rpx;
		padding-bottom: calc(100upx + env(safe-area-inset-bottom) / 2);
	}

	.cu-card {
		// width: calc(100% - 44rpx);
		background: #ffffff;
		border-radius: 14px;
		margin: 0 22rpx 22rpx;
		// margin-bottom: 22rpx;
		padding: 26rpx 0 22rpx;

		.cu-card-shop-name {
			.cu-card-shop-name-box {
				margin-left: 30rpx;
			}

			// display: flex;
			.shop-icon {
				width: 24rpx;
				height: 24rpx;
				margin-left: 14rpx;
				background-size: cover;
				background-position: center;
			}

			.shop-name {
				font-size: 26rpx;
				color: #000000;
				margin-left: 15rpx;
			}
		}
	}

	.foot {
		/*  #ifdef  H5 */
		bottom: calc(var(--window-bottom));
		/*  #endif  */
	}

	.without {
		margin: auto;
	}

	.without image {
		width: 300rpx;
		height: 305rpx;
	}

	.bar-rt {
		width: 570rpx !important;
		text-align: right !important;
		margin-right: 10rpx !important;
	}

	.settle-bt {
		width: 200rpx;
	}

	.row-img {
		width: 30% !important;
		border-radius: 10rpx;
		height: 6.4em;
	}

	.row-info {
		width: 60%;
	}

	.row-specs {
		width: 200px;
	}

	.specification {
		white-space: unset !important;
	}

	.cu-bar {
		top: unset !important;
	}

	.cu-avatar.sm {
		width: 24rpx;
		height: 24rpx;
		margin-left: 30rpx;
		padding-right: 14rpx;
	}

	.margin-top-xs {
		position: relative;

		.swiper-content {
			width: 100%;
			padding: 0 12rpx 0 18rpx;
			position: relative;
			z-index: 1;
			background-color: #fff;
			// left: -254rpx;
			transition: left 1s;
			box-sizing: border-box;

			.overflow-2 {
				font-size: 24rpx;
				color: #000000;
				line-height: 38rpx;
			}

			.text-black {
				color: #000000;
			}
		}

		// 滑动按钮
		.swiper-btn {
			position: absolute;
			// display: none;
			// position: relative;
			right: 0;
			top: 0;
			bottom: 0;
			display: flex;
			align-items: center;

			.defaultStyles {
				// padding: 0 38rpx;
				width: 127rpx;
				font-size: 24rpx;
				color: #ffffff;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.collection {
				background: linear-gradient(0deg, #ff8048 0%, #ff6223 100%);
			}

			.goodDelete {
				background: linear-gradient(0deg, #ff4c17 0%, #f22300 100%);
			}
		}
	}

	.cu-bar .titleText {
		// padding: 68rpx;
		padding: 0 36rpx;
		color: #484848;
		font-size: 26rpx;

		// .titleTextNum {
		// 	padding-left: 10rpx;
		// }
	}

	// .cu-bar > .titleText:first-child {
	// 	margin-left: 58rpx;
	// 	// font-size: 28rpx;
	// 	// font-weight: bold;
	// 	// color: #fc3636;
	// }
	.cu-bar .active {
		// margin-left: 58rpx;
		font-size: 28rpx;
		font-weight: bold;
		color: #fc3636;
	}

	.goShopping {
		width: 230rpx;
		height: 56rpx;
		background: linear-gradient(-90deg, #ff5147 0%, #ff936c 100%);
		border-radius: 28rpx;
		font-size: 28rpx;
		color: #ffffff;
	}

	.goSeckill {
		width: 230rpx;
		height: 56rpx;
		background: linear-gradient(90deg, #ba9879 0%, #c7ad93 0%, #c5a288 100%);
		border-radius: 28rpx;
		font-size: 28rpx;
		color: #ffffff;
	}
</style>