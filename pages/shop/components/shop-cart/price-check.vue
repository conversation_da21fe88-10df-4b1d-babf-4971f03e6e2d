<template>
  <view
    class="cu-modal bottom-modal"
    :class="isModalShow ? 'show' : ''"
    catchtouchmove="touchMove"
    @tap="closeModal"
  >
    <view
      class="cu-dialog dialo-sku bg-white"
      :class="isModalShow ? 'animation-slide-bottom' : ''"
      style="height: 75%;"
      @tap.stop
    >
      <view class="cu-card article no-card">
        <!-- 标题 -->
        <view class="flex justify-around align-center text-xl" style="height: 100rpx;">
          <view class="text-black text-center" style="width: 85%">购物车查价</view>
          <view class="text-xl close-icon">
            <text class="cuIcon-close" @tap="closeModal"></text>
          </view>
        </view>
        <!-- 商品信息 -->
        <view class="cu-item" style="padding: 0 30rpx;">
          <view class="content" style="padding: 0;">
            <!-- 图片展示 -->
            <image
              @click="handlePreviewImage(goodsObj.goods.picUrl)"
              v-if="goodsObj.goods.picUrl"
              :src="goodsObj.goods.picUrl || 'https://img.songlei.com/live/img/no_pic.png'"
              mode="aspectFill"
              class="row-img margin-top-xs"
            />
            <image
              @click="handlePreviewImage(0)"
              v-else-if="
                !goodsObj.goods.picUrl && goodsObj.goods.picUrls && goodsObj.goods.picUrls.length
              "
              :src="goodsObj.goods.picUrls[0] || 'https://img.songlei.com/live/img/no_pic.png'"
              mode="aspectFill"
              class="row-img margin-top-xs"
            />

            <!-- 商品信息 -->
            <view class="flex flex-direction justify-between" style="flex: 1; padding: 10rpx 0 0;">
              <view class="text-black text-left overflow-2 " style="flex:1;">
                {{goodsObj.goods.goodsSpu.name}}
              </view>
              <view class="flex flex-direction justify-between" style="flex: 2;" v-if="goodsObj.goods.goodsSku">
                <view class="flex align-center justify-between" @tap.stop>
                  <format-price 
                    v-if="goodsObj.goods.goodsSku.estimatedPriceVo"
                    :color="Number(goodsObj.goods.goodsSku.estimatedPriceVo.price) != (Number(goodsObj.goods.goodsSku.estimatedPriceVo.estimatedPrice)) ? 'black' : '#e54d42'"  
                    signFontSize="20rpx" 
                    smallFontSize="24rpx" 
                    priceFontSize="34rpx" 
                    :price="Number(goodsObj.goods.goodsSku.estimatedPriceVo.price)" 
                  />
                </view>
                <view 
                  class="flex align-center text-smx" 
                  v-if="goodsObj.goods.goodsSku.estimatedPriceVo && (Number(goodsObj.goods.goodsSku.estimatedPriceVo.estimatedPrice) !== Number(goodsObj.goods.goodsSku.estimatedPriceVo.price))"
                >
                  <text class="text-red">预估到手价：
                  </text>
                  <format-price  
                    signFontSize="20rpx" 
                    smallFontSize="24rpx" 
                    priceFontSize="34rpx" 
                    :price="goodsObj.goods.goodsSku.estimatedPriceVo.estimatedPrice" 
                  />
                </view>
              </view>
            </view>

            
          </view>
          <!-- 价格走势 -->
          <view class="text-black text-left margin-top-sm">价格走势</view>
          <!-- 价格波动  展示加购价，历史购买价，当前价-->
          <view v-if="goodsObj.goods.buyNum" class="flex justify-around align-center margin-tb-sm">
            <view class="text-black price-check-text text-sm" style="width: 100%;">
              历史价 
              <view class="text-price display-ib margin-lr-sm">
                {{goodsObj.goods.minBuyPrice ? goodsObj.goods.minBuyPrice : '0.00'}}
              </view> 
              加购价 
              <view class="text-price display-ib margin-lr-sm">
                {{goodsObj.goods.addPrice ? goodsObj.goods.addPrice : '0.00'}}
              </view> 
              当前价格
              <view class="text-price display-ib text-red margin-left-sm">
                {{goodsObj.goods.salesPrice ? goodsObj.goods.salesPrice : '0.00'}}
              </view> 
            </view>
          </view>
          <!--  加购价，当前价-->
          <view v-else class="flex justify-around align-center margin-tb-sm">
            <view class="text-black price-check-text text-sm">
              加入时价格 
              <view class="text-price display-ib margin-lr-sm">
                {{goodsObj.goods.addPrice ? goodsObj.goods.addPrice : '0.00'}}
              </view> 
              当前价格
              <view class="text-price display-ib text-red margin-left-sm">
                {{goodsObj.goods.salesPrice ? goodsObj.goods.salesPrice : '0.00'}}
              </view> 
            </view>
            <view 
              class="text-red text-sm"
              style="padding: 15rpx 0;"
              v-if="goodsObj.goods.addPrice - goodsObj.goods.goodsSku.salesPrice > 0"
            >
              距加入时降 
              <view class="text-price display-ib" style="margin-left: 10rpx">
                {{ goodsObj.goods.addPrice - goodsObj.goods.goodsSku.salesPrice}}
              </view>
            </view>
          </view>
          <!-- 柱状图 -->
          <view class="charts-box" :style="screenWidth && screenWidth <= 375 ? 'height:300rpx' : 'height:400rpx'">
            <qiun-data-charts 
              ref="charts"
              type="column"
              :opts="opts"
              :chartData="chartData"
            />
          </view>
        </view>
      </view>

      <!-- 按钮 -->
      <view 
        class="bg-white foot border margin-top-lg justify-between" 
        style="position: fixed; bottom: 20rpx; width: 100%;" 
      >
        <button
          class="cu-btn bg-white round shadow-blur lg return-check margin-right-sm"
          @tap="handleReturnCheck"
        > 返回并勾选 </button>
        <button
          class="cu-btn round shadow-blur lg settle"
          @tap="handleSettle"
        > 立即结算 </button>
      </view>
    </view>
  </view>
</template>
<script>
import qiunDataCharts from "../qiun-data-charts/components/qiun-data-charts/qiun-data-charts";
const app = getApp();
export default {
  components: {
	  qiunDataCharts
  },
  props: {
    isModalShow: {
      type: Boolean,
      default: false,
    },
    goodsObj: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      chartData: {},
      opts: {
        color: ["#FFADAD", "#FF0000"],
        padding: [15,15,0,5],
        enableScroll: false,
        dataPointShape: false,
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true, // 不绘制网格
          axisLineColor: '#f9f9f9'
        },
        yAxis: {
          data: [{ min: 0 }],
          disabled: true, // 不绘制 Y 轴
          axisLine: false, // 坐标线
          gridColor: "#f9f9f9"
        },
        extra: {
          column: {
            type: "group",
            width: 30,
            activeBgColor: "#000000",
            activeBgOpacity: 0.08,
            seriesGap: 5,
            barBorderRadius: [ 6, 6, 0, 0 ]
          },
        }
      },
      screenWidth: ''
    }
  },
  mounted() {
    this.getScreenSize()
    this.getServerData();
  },
  methods: {
    getScreenSize() {
      uni.getSystemInfo({
        success: (res) => {
          this.screenWidth = res.windowWidth; // 屏幕宽度，单位为px
          // const screenHeight = res.windowHeight; // 屏幕高度，单位为px
        },
      });
    },
    getServerData() {
      //模拟从服务器获取数据时的延时
      setTimeout(() => {
        //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let res
        if (this.goodsObj.goods.buyNum) {
          res = {
            categories: ["历史价","加购价","当前价"],
            series: [
              { 
                name: '',  
                data: [
                this.goodsObj.goods.minBuyPrice,
                this.goodsObj.goods.addPrice,
                {value: this.goodsObj.goods.salesPrice, color: '#FF0000',},
              ]}
            ]
          };
        } else {
          res = {
            categories: ["加购价","当前价"],
            series: [
              { 
                name: '',  
                data: [
                this.goodsObj.goods.addPrice,
                {value: this.goodsObj.goods.salesPrice, color: '#FF0000',}
              ]}
            ]
          };
        }
        
        this.chartData = JSON.parse(JSON.stringify(res));
      }, 500);
    },
    // 关闭弹框
    closeModal() {
      this.$emit("close", false);
    },
    // 图片预览
    handlePreviewImage(params) {
      if (typeof params === "number") {
        uni.previewImage({
          urls: this.goodsObj.goods.goodsSpu.picUrls,
          current: params,
        });
      } else {
        const imgArr = [params];
        uni.previewImage({
          urls: imgArr,
          current: 0,
        });
      }
    },
    // 返回并勾选
    handleReturnCheck() {
      this.$emit("checkboxChange", this.goodsObj.goods, this.goodsObj.shopIndex, this.goodsObj.goodsIndex);
      this.closeModal();
    },
    // 立即结算
    async handleSettle() {
      await this.$emit("checkboxChange", this.goodsObj.goods, this.goodsObj.shopIndex, this.goodsObj.goodsIndex);
      await this.$emit('orderConfirm');
      // this.closeModal();
    }
  }
}
</script>
<style lang="scss" scope>
.row-img {
  display: block;
  width: 240rpx !important;
  height: 240rpx !important;
  border-radius: 10rpx;
}
.price-check-text{
  width: 440rpx;
  padding: 15rpx 20rpx;
  text-align: center;
  background: #F5F5F5;
  border-radius: 33rpx;
}
.charts-box {
  width: 100%;
  height: 400rpx;
}
.return-check{
  width: 45%;
  background: #FFF;
  border-radius: 40rpx;
  border: 1px solid #C09979; 
  color: #C09979;
}
.settle{
  width: 45%;
  background: linear-gradient(-90deg, #CCAC92 0%, #C09979 0%, #D5B59C 100%);
  border-radius: 40rpx;
  color: #fff;
}
</style>