<template>
  <view 
    style="min-height: 104upx;background-color: #FEF8E6;" 
    class="coupon-flex"
    :style="{ height: `${ newData.height * 2 }rpx` }"
    v-if="couponData && couponData.length"
  >
    <scroll-view scroll-x enable-flex style="align-items: center;">
      <view class="coupons-list" :style="{ width: `${ 220 * (couponData && couponData.length) }rpx`, 'justifyContent': couponData.length > 1 ? 'space-between' : 'center' }">
        <template v-if="couponData.length === 1">
          <view class="coupon-item-one" v-for="item in couponData" :key="item.id" @click="submitInfo(`${item.eventId}`)">
            <view class="brand-one padding-left-lg">{{ shopInfo.brand }}</view>
            <view class="flex">
              <view class="flex price-one">
                <text style="height: 80upx;font-size: 22upx;">￥</text>
                <text class="text-bold" style="height: 80upx;line-height: 80upx;">{{ item.faceValue }}</text>
              </view>
              <view class="flex-sub padding-left-sm">
                <view class="text-xdf text-bold" style="padding-bottom: 5upx;">品牌券</view>
                <view class="text-sm">
                  <block v-if="item.condAmount">满{{item.condAmount || 0}}元可用</block>
                  <block v-else>无门槛</block>
                </view>
              </view>
            </view>
          </view>
        </template>
        <template v-if="couponData.length === 2">
          <view class="coupon-item-two" v-for="item in couponData" :key="item.id" @click="submitInfo(`${item.eventId}`)">
            <view class="brand-two">{{ shopInfo.brand }}</view>
            <view class="flex">
              <view class="flex price-two">
                <text style="height: 80upx;font-size: 22upx;">￥</text>
                <text class="text-bold" style="height: 80upx;line-height: 80upx;">{{ item.faceValue }}</text>
              </view>
              <view class="flex-sub padding-left-xs">
                <view class="text-xdf text-bold"style="padding-bottom: 5upx;">品牌券</view>
                <view class="text-sm">
                  <block v-if="item.condAmount">满{{item.condAmount || 0}}元可用</block>
                  <block v-else>无门槛</block>
                </view>
              </view>
            </view>
          </view>
        </template>
        <template v-if="couponData.length >= 3">
          <view class="coupon-item-more" v-for="item in couponData" :key="item.id" @click="submitInfo(`${item.eventId}`)">
            <view class="brand-more">{{ shopInfo.brand }}</view>
            <view class="text-xdf text-bold text-center">品牌券</view>
            <view class="flex justify-center text-bold text-center" style="font-size: 80upx;color: #632D00;">
              <text style="height: 80upx;font-size: 22upx;">￥</text>
              <text style="height: 80upx;line-height: 80upx;">{{ item.faceValue }}</text>
            </view>
            <view class="coupon-threshold text-center">
              <block v-if="item.condAmount">满{{item.condAmount || 0}}元可用</block>
              <block v-else>无门槛</block>
            </view>
          </view>
        </template>
      </view>
    </scroll-view>
    <view class="toast-coupon" v-if="toastCoupon">
      <view class="coupon-box">
        <view class="text-bold text-xl" style="margin: 20upx 0 40upx;">领取成功！</view>
        <view class="flex justify-around padding-bottom">
          <view @click="submit" class="coupon-buttom" style="color: #fff;background-color: rgb(144, 100, 37);">去查看</view>
          <view class="coupon-buttom" @click="close">取消</view>
        </view>
        <view class="coupon-icon text-xs" style="color: #999;">您可以点击【我的】查看优惠券详情</view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { getTypeDetail, submitInfo } from "@/pages/coupon/api/coupon.js"
export default {
  props: {
    value: {
      type: Object,
      default: function() {
        return {
          showType: 'row',
          pageSpacing: 0,
          goodsList: [],
        }
      }
    },
    shopInfo: {
      type: Object,
      default: ({})
    }
  },
  watch: {
    shopInfo: {
      handler(newData) {
        if(newData) {
          getTypeDetail({ merchant: newData.cabinetGroup, pageNo: 1, pageSize: 100, position: '04' }).then(res => {
            this.couponData = res.data.dataList
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      newData: this.value || {},
      couponData: [],
      toastCoupon: false
    }
  },
  created() {
    
  },
  methods: {
    submitInfo(eventId) {
      submitInfo({ eventId }).then(res => {
        this.toastCoupon = true;
      })
    },
    submit() {
      // var pages = getCurrentPages() //获取加载的页面
      // var currentPage = pages[pages.length - 1] //获取当前页面的对象
      // currentPage.$vm.shopCoupon && currentPage.$vm.shopCoupon()
      this.toastCoupon = false;
      uni.navigateTo({
        url: '/pages/coupon/coupon-shop-list/index?counter=' + this.shopInfo.cabinetGroup
      })
    },
    close() {
      this.toastCoupon = false;
    }
  }
}
</script>

<style scoped>
.coupon-flex {
  display: flex;
  align-items: center;
}
.coupons-list {
  display: flex;
  justify-content: center;
  min-width: 100vw;
  padding: 0 20rpx;
}
.coupon-item-one {
  width: 619upx;
  height: 138upx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url('https://img.songlei.com/-1/material/05192359-06bf-464b-9c1f-73f9d03c9935.png');
}
.coupon-item-one .brand-one {
  color: #fff;
  font-size: 22upx;
  line-height: 30upx;
}
.coupon-item-one .price-one {
  width: 200upx;
  height: 108upx;
  color: #632D00;
  font-size: 80upx;
  font-weight: bold;
  vertical-align: top;
  box-sizing: border-box;
  padding: 14upx 15upx;
}
.coupon-item-two {
  width: 348upx;
  height: 138upx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url('https://img.songlei.com/-1/material/e11b8ca5-d0e2-4774-8454-7488da32c19b.png');
}
.coupon-item-two .brand-two {
  color: #fff;
  font-size: 22upx;
  padding-left: 18upx;
  line-height: 30upx;
}
.coupon-item-two .price-two {
  width: 179upx;
  height: 108upx;
  color: #632D00;
  font-size: 80upx;
  font-weight: bold;
  vertical-align: top;
  box-sizing: border-box;
  padding: 14upx 15upx;
}
.coupon-item-more {
  width: 195upx;
  height: 218upx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url('https://img.songlei.com/-1/material/1a489c89-91da-407d-afa2-d968769d27ea.png');
}
.coupon-item-more .brand-more {
  color: #fff;
  font-size: 22upx;
  padding-left: 18upx;
  line-height: 30upx;
}
.coupon-item-more .coupon-threshold {
  height: 65upx;
  line-height: 65upx;
  font-size: 24upx;
  padding-top: .1rem;
}
.toast-coupon {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .5);
  top: 0 ;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1025;
}
.toast-coupon .coupon-box {
  width: 600upx;
  padding: 20upx;
  border-radius: 20upx;
  background-color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}
.toast-coupon .coupon-buttom {
  width: 250upx;
  line-height: 70upx;
  background-color: #ddd;
  border-radius: 30upx;
  font-size: 32upx;
}
</style>