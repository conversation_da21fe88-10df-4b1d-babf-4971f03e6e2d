<template>
	<!-- 商品显示组件-横向排列方式显示 -->
	<view :class="classCom" :style="{
	        marginBottom: `${Number(newData.marginBottomSpacing)*2}rpx`,
	        marginTop: `${newData.marginTopSpacing*2}rpx`,
	        marginLeft: `${newData.marginLeftSpacing*2}rpx`,
	        marginRight: `${newData.marginRightSpacing*2}rpx`,
	      }">
		<view class="wrapper-list-goods" :style="{
		   backgroundColor: newData.background,
		   backgroundImage: `url(${newData.bgImage})`,
			 backgroundSize: `contain`,
			 backgroundRepeat: `no-repeat`,
			 backgroundPosition: `center`,
		   overflow: `hidden`,
		   paddingBottom: `${newData.paddingBottomSpacing*2}rpx`,
		   paddingTop: `${newData.paddingTopSpacing*2}rpx`,
		   paddingLeft: `${newData.paddingLeftSpacing*2}rpx`,
		   paddingRight: `${newData.paddingRightSpacing*2}rpx`,
		}">
			<view class="flex justify-between" style="height:166rpx;width: 100%;align-items: flex-end;">
				<view>
					<view class="text-bold">{{newData.title}}</view>
					<view class="margin-top-xs margin-bottom text-black text-xs">{{newData.subtitle}}</view>
					<view class="flex justify-between align-center" style="width: 445rpx;">
						<format-price :styleProps="priceStyle" style="height: 40rpx;line-height: 40rpx;"
							signFontSize="24rpx" color="#000000" :price="validAmt" priceFontSize="32rpx"
							smallFontSize="24rpx" />
						<view class="text-bold">{{totCnt}} <text class="text-xs">张</text></view>
						<view @click="navShopTo()" class="text-center" :style="{
							    fontSize: '24rpx',
								width: `140rpx`,
								background: `${newData.btnBgColor}`,
								color:`${newData.btnColor}`,
								borderRadius: `18rpx`}">我要购卡
							<text style="font-size: 24rpx;">></text>
						</view>
					</view>
				</view>

				<view @click="handleGiftPassword" class="text-center"
					:style="{width:'100rpx',display:newData.showQrCode=='1'?'block':'none', overflow:'hidden'}">
					<view style="position: relative;width:100rpx;height:166rpx;">
						<view class="cuIcon-qr_code text-bold" :style="{
						   	position: 'absolute', top:'0rpx','font-size':'100rpx',color:`${newData.qrCodeColor}`,
						}"></view>
						<view :style="{position: 'absolute',  bottom:'0rpx',color:`${newData.qrCodeTextColor}` }"
							class="text-xs">礼品卡支付</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import lazyLoad from "components/lazy-load/index";
	import {
		myGiftCardList
	} from '@/pages/gift/api/gift'
	import formatPrice from "@/components/format-price/index.vue";
	import {
		getIsPassword
	} from '@/pages/gift/api/gift'

	const app = getApp();
	import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"

	export default {
		mixins: [navigateUtil],
		components: {
			lazyLoad,
			formatPrice
		},
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {
						title: '我的何所有礼品卡',
						subtitle: '美好生活值得拥有，美好生活应有尽有',
						showQrCode: '1',
						qrCodeColor: '#000000',
						qrCodeTextColor: '#000000',
						pageUrl: '',
						background: '',
						btnColor: '#FFFFFF',
						btnBgColor: '#CE151B',
						bgImage: '',
						paddingLeftSpacing: '10',
						paddingRightSpacing: '10',
						paddingTopSpacing: '10',
						paddingBottomSpacing: '10'
					}
				}
			},
			shopInfo: {
				type: Object,
				default: ({})
			}
		},

		computed: {
			classCom() {
				return this.newData.background && this.newData.background.indexOf('bg-') != -1 ?
					this.newData.background :
					''
			},
		},

		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				validAmt: 0, //总余额
				totCnt: 0, //礼品卡总数量
				priceStyle: "display: flex; justify-content: center; font-weight: bold; align-items: baseline;",
				shopId: '' //礼品卡店铺id
			};
		},

		created() {
			console.log(this.newData, '=========积分商品============')
			this.myGiftCardList()
			this.getIsPassword()
		},

		methods: {
			//查询购卡店铺
			getIsPassword() {
				getIsPassword().then((res => {
					if (res.data) {
						// this.userpassword=res.data
						// isActive 0：未开通 1：已开通支付密码
						this.shopId = res.data.shopId;
						 uni.setStorageSync('paypaymentuserpassword',res.data)
					}
				}))
			},

			//购卡到店铺,没有店铺则提示
			navShopTo() {
				if (!this.shopId) return uni.showToast({
					icon: 'none',
					title: '礼品卡店铺不存在！',
					duration: 2000
				});
				uni.navigateTo({
					url: `/pages/shop/shop-detail/index?id=${this.shopId}`
				})
			},

			//我的礼品卡列表
			myGiftCardList() {
				// cabinetGroup 柜组编码
				myGiftCardList(Object.assign({}, {
					cabinetGroup: this.shopInfo.cabinetGroup
				})).then((res) => {
					if (res.data) {
						let {
							validAmt,
							totCnt
						} = {
							...res.data
						}
						this.totCnt = totCnt || 0;
						this.validAmt = validAmt || 0
					}
				})
			},

			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},

			
			handleGiftPassword(){
			  let isActive = uni.getStorageSync('paypaymentuserpassword')?.isActive
			  // isActive 0 没有开通去开通
			    if (isActive =='0') {
			        uni.navigateTo({
			        url: `/pages/gift/set-password/code/index`
			      })
			      return
			    } else {
					this.toPage('/pages/gift/payment-code/index')
				}	
			}
		}
	}
</script>

<style scoped lang="scss">
	.wrapper-list-goods {
		white-space: nowrap;
		// padding: 10rpx;
	}
</style>
