<template>
  <!-- :style="{marginTop: `${scrollTop>scrollTopCut?0:(heightHead+4)}px`}" -->
  <view style="backgroundColor': '#f0eeef; margin-bottom: 120rpx;">
    <view
      class="cu-bar justify-center  shop-card"
      :class="pageScrollTop>=heightHead?'fixed-tab':''"
      :style="{
				'backgroundColor': '#ffffff',
				'minHeight':'auto',
				'padding':'3px 0',
				'top': pageScrollTop>=heightHead?(CustomBar+'px'):'',
			}"
    >

      <view class="grid response text-center align-center">
        <view class="flex-sub padding-xs radius">
          <view
            class="tab-item"
            :class="multiple=='desc' ? 'text-bold text-'+theme.themeColor : 'tab-item-color'"
            @tap="sortHandle"
            data-type="multiple"
          >综合</view>
        </view>

        <view class="flex-sub padding-xs  radius  ">
          <view
            class="grid text-center justify-center tab-item"
            @tap="sortHandle"
            data-type="sales"
            :class="sales=='asc' ||sales=='desc' ? 'text-bold text-'+theme.themeColor : 'tab-item-color'"
          >销量
            <view :style="{'font-size': '30rpx', 'line-height': pageScrollTop>=heightHead?'10rpx':'30rpx','margin':pageScrollTop>=heightHead?'auto':''} ">
              <view
                :class="'cuIcon-triangleupfill ' + (sales=='asc' ? 'text-'+theme.themeColor : 'tab-item-color')"
                data-type="sales"
              ></view>
              <view class="basis-df"></view>
              <view
                :class="'cuIcon-triangledownfill ' + (sales=='desc' ? 'text-'+theme.themeColor : 'tab-item-color')"
                data-type="sales"
              ></view>
            </view>
          </view>
        </view>

        <view class="flex-sub padding-xs radius ">
          <view
            class="tab-item"
            :class="createTime=='desc' ? 'text-bold text-'+theme.themeColor : 'tab-item-color'"
            @tap="sortHandle"
            data-type="createTime"
          >新品</view>
        </view>

        <view class="flex-sub padding-xs  radius ">
          <view
            class="grid text-center justify-center tab-item"
            @tap="sortHandle"
            data-type="price"
            :class="price=='asc' ||price=='desc' ? 'text-bold text-'+theme.themeColor : 'tab-item-color'"
          >价格
            <view :style="{'font-size': '30rpx', 'line-height': pageScrollTop>=heightHead?'10rpx':'30rpx','margin':pageScrollTop>=heightHead?'auto':''} ">
              <view
                :class="'cuIcon-triangleupfill ' + (price=='asc' ? 'text-'+theme.themeColor : 'tab-item-color')"
                data-type="price"
              ></view>
              <view class="basis-df"></view>
              <view
                :class="'cuIcon-triangledownfill ' + (price=='desc' ? 'text-'+theme.themeColor : 'tab-item-color')"
                data-type="price"
              ></view>
            </view>
          </view>
        </view>

        <view class="flex-sub margin-xs radius">
          <view
            :class="inventoryState=='0' ? 'text-bold text-'+theme.themeColor : ''"
            @tap="sortHandle"
            data-type="goods"
          >仅看有货
          </view>
        </view>
      </view>

      <view class="action margin-left">
        <view class="text-xxl tab-item">
          <text
            class="tab-item"
            style="font-size:30rpx;"
            :class="'cuIcon-' + (viewType ? 'list' : 'cascades')"
            @tap="viewTypeEdit"
          ></text>
        </view>
      </view>
    </view>

    <template v-if="goodsList&&goodsList.length>0">
      <view
        class="shop-content"
        style="background-color: #f4f4f4;position: relative;"
      >
        <view
          style="padding: 10rpx;"
          v-if="viewType"
        >
          <goods-card :goodsList="goodsList"></goods-card>
        </view>

        <view v-if="!viewType">
          <goods-row :goodsList="goodsList"></goods-row>
        </view>

        <view
          style="width: 750rpx;"
          :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"
        ></view>
      </view>
    </template>
    <no-date v-else></no-date>
     <shop-pop />
  </view>
</template>

<script>
import util from '@/utils/util'
import goodsRow from "components/goods-row/index";
import shopPop from "../shop-pop/index.vue";
import noDate from "components/base-nodata/index.vue";

import api from '@/utils/api'
const app = getApp();
export default {
  components: {
	shopPop,
    goodsRow,
    noDate
  },
  props: {
    parameter: {
      type: Object,
      default: {}
    },
    heightHead: {
      type: Number,
      default: 100
    },
    pageScrollTop: {
      type: Number,
      default: 0
    }
  },
  watch: {
    parameter: {
      handler (val) {
        if (val && val.shopId) {
         app.initPage().then(res => {
          this.relod();
         });    
        }
      },
      deep: true
    }
  },
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,
      loadmore: true,
      goodsList: [],
      viewType: false,
      page: {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: '',
        //升序字段
        descs: ''
      },
      multiple: 'desc',
      price: '',
      sales: '',
      createTime: '',
      goods: '',
      inventoryState: '',//仅看有货||上架商品  0仅看有货  字段不传查看所有
    }
  },

  created () {
    if (this.parameter && this.parameter.shopId) {
		app.initPage().then(res => {
		 this.relod();
		});    
    }
  },

  methods: {
    goodsPage () {
      if (this.inventoryState == '0') {
        api.goodsPage(Object.assign({}, this.page, { inventoryState: '0' }, util.filterForm(this.parameter))).then(res => {
          let goodsList = res.data.records;
          this.goodsList = [...this.goodsList, ...goodsList];
          if (goodsList.length < this.page.size) {
            this.loadmore = false;
          }
        });

      } else {
        api.goodsPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
          let goodsList = res.data.records;
          this.goodsList = [...this.goodsList, ...goodsList];
          if (goodsList.length < this.page.size) {
            this.loadmore = false;
          }
        });
      }
    },

    reachBottom () {
      if (this.loadmore) {
        this.page.current = this.page.current + 1;
        this.goodsPage();
      }
    },

    viewTypeEdit () {
      this.viewType = !this.viewType;
    },

    relod () {
      this.loadmore = true;
      this.goodsList = [];
      this.page.current = 1;
      this.goodsPage();
    },

    needLoad () {
      if (this.goodsList.length <= 0) {
        this.goodsPage();
      }
    },

    sortHandle (e) {
      let type = e.target.dataset.type;
      switch (type) {
        case 'price':
          if (this.price == '') {

            this.price = 'asc';
            this.page.descs = '';
            this.page.ascs = 'price_down';
          } else if (this.price == 'asc') {
            this.price = 'desc';
            this.page.descs = 'price_down';
            this.page.ascs = '';
          } else if (this.price == 'desc') {
            this.price = '';
            this.page.descs = '';
            this.page.ascs = '';
          }
          this.sales = '';
          this.createTime = '';
          this.multiple = '';
          this.inventoryState = ''
          break;

        case 'sales':
          if (this.sales == '') {
            this.sales = 'desc';
            this.page.descs = 'sale_num';
            this.page.ascs = '';

          } else if (this.sales == 'desc') {
            this.sales = 'asc';
            this.page.descs = '';
            this.page.ascs = 'sale_num';

          } else if (this.sales == 'asc') {
            this.sales = '';
            this.page.descs = '';
            this.page.ascs = '';

          }
          this.price = '';
          this.createTime = '';
          this.multiple = '';
          this.inventoryState = ''
          break;
        //新品
        case 'createTime':
          if (this.createTime == '') {
            this.createTime = 'desc';
            this.inventoryState == ''
            this.page.descs = 'create_time';
            this.page.ascs = '';
          } else if (this.createTime == 'desc') {
            this.createTime = '';
            this.page.descs = '';
            this.page.ascs = '';

          }
          this.price = '';
          this.sales = '';
          this.multiple = '';
          this.inventoryState = ''
          break;

        //仅看有货
        case 'goods':
          if (this.inventoryState == '') {
            this.inventoryState = '0';
            this.page.descs = 'create_time';
            this.page.ascs = '';
            this.multiple = 'desc';
          } else if (this.inventoryState == '0') {
            this.multiple = '';
            this.inventoryState = '';
            this.page.descs = '';
            this.page.ascs = '';
          }
          this.price = '';
          this.createTime = '';
          this.sales = '';
          this.multiple = '';
          break;
        default:
          this.page.descs = '';
          this.page.ascs = '';
          this.createTime = '';
          this.price = '';
          this.sales = '';
          this.inventoryState = ''
          this.multiple = 'desc';
          break;
      }

      this.relod();
    },

  },


}
</script>

<style scoped>
.fixed-tab {
  position: fixed;
  left: 0;
  width: 100%;
  z-index: 9999;
}

.tab-item {
  font-size: 24rpx;
}

.tab-item-color {
  color: #525252;
}

.triangle {
  color: #a5a5a5;
}

.location {
  width: 480rpx;
}

.shop-detail {
  height: 160rpx;
}

.shop-detail image {
  width: 120rpx !important;
  height: 120rpx !important;
}

.show-bg {
  height: 84%;
  margin-top: 120rpx;
}

.image-box {
  height: 90%;
}

.show-btn {
  margin-top: -130rpx;
}

.econds-kill {
  width: 40%;
  height: 100rpx;
}

.econds-kill image {
  width: 100%;
  height: 100rpx;
}

.spellGroup {
  width: 700rpx;
  height: 100rpx;
}

.spellGroup image {
  width: 100%;
  height: 100rpx;
}

.bargain {
  width: 700rpx;
  height: 100rpx;
}

.bargain image {
  width: 100%;
  height: 100rpx;
}

.coupons {
  width: 40%;
  height: 100rpx;
}

.coupons image {
  width: 100%;
  height: 100rpx;
}
</style>
