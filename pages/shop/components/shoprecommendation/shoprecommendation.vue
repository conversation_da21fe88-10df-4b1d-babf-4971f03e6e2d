<template>
	<view>
		<view>
			<view
				style="display: flex;align-items: center;padding: 16rpx;background: white;margin-top: 10rpx;border-top-left-radius: 10rpx;border-top-right-radius: 10rpx;">
				<image style="width: 28rpx;height: 30rpx;"
					src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/lovex.png" mode=""></image>
				<text
					style="font-size:30rpx;font-family: PingFang SC;font-weight: bold;color: #282828;margin-left: 20rpx;">商品优选</text>
			</view>
			<recommendComponents canLoad/>
		</view>
	</view>
</template>
<script>
	const app = getApp();
	import api from '@/utils/api'
	import recommendComponents from "../tjshop/tjshop.vue";
	export default {
		props: {
			//接口参数
			itemId: {
				type: String,
				default: null
			},
			sceneId: {
				type: String,
				default: 'sy101'
			},
			needLoadMore: {
				type: Boolean,
				default: false
			},
			//由父组件控制什么时候可以加载推荐数据,如果需要传入itemId 就等获取到itemId再设置为true，如果不需要传入itemId直接设置为true
			canLoad: {
				type: Boolean,
				default: false
			},
			// shopId: {
			// 	type: String,
			// 	default: ''
			// },
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				loadmoreRecommendList: true,
				goodsListRecom: [],
				loadDataTimes: 0,
				shopId:""
			}
		},
		components: {
			recommendComponents
		},
		watch: {
			canLoad: {
				handler(newValue, oldValue) {
					if (newValue) {
						this.getGoodsRecom(true);
					}
				},
				immediate: true
			},
			// shopId: {
			// 	handler(val) {
			// 		if (val && val) {
			// 			console.log(val, 'val.shopid22222222222222222222')
			// 			this.shopId = val

			// 		}
			// 	},
			// 	deep: true
			// },
		},

		methods: {
			//推荐商品
			/**
			 * @param Boolean fresh  是否刷新
			 */
			getGoodsRecom(fresh) {
				if (this.loadmoreRecommendList) {
					api.getRecommendList({
						sceneId: this.sceneId,
						itemId: this.itemId,
						returnCount: 40
					}).then(res => {
						this.loadDataTimes++;
						if (!res.data || res.data.length == 0) {
							if (this.loadDataTimes < 1) {
								this.getGoodsRecom();
							}
						} else if (res.data || res.data.length > 0) {
							if (fresh) this.goodsListRecom = [...res.data];
							else this.goodsListRecom = [...this.goodsListRecom, ...res.data];
						}
					}).catch(e => {
						this.loadmoreRecommendList = false;
					});
				}

			},

			loadMoreGoodsRecom() {
				if (this.needLoadMore) {
					this.getGoodsRecom(false)
				}
			}
		}
	}
</script>

<style>
	.recommend-action {
		display: flex;
		align-items: center;
		height: 100%;
		justify-content: center;
		max-width: 100%;
	}
</style>
