<template>
	<view class="pop-component">
		<template v-if="shopSetting && shopSetting.showPop == 1">
			<div-base-navigator v-for="(item, index) in shopSetting.navButtons" v-bind:key="index" :pageUrl="item.pageUrl">
				<image
					:src="item.imageUrl"
					:style="{
						position: 'fixed',
						width: (item.width * 2 || 160) + 'rpx',
						height: (item.height * 2 || 160) + 'rpx',
						right: item.rightSpacing > 0 ? Number(item.rightSpacing) + 'px' : '',
						top: item.topSpacing > 0 ? Number(item.topSpacing) + CustomBar + 'px' : '',
						bottom: item.bottomSpacing > 0 ? Number(item.bottomSpacing) * 2 + 'rpx' : ''
					}"
				/>
			</div-base-navigator>
		</template>

		<template v-if="shopSetting && shopSetting.cusService && shopSetting.cusService.show == 1">
			<!-- <div-base-navigator :pageUrl="cusServicePageUrl"> -->
			<image
				@click="handleCusServiceClick"
				:src="shopSetting.cusService.imageUrl"
				:style="{
					position: 'fixed',
					width: (shopSetting.cusService.width * 2 || 90) + 'rpx',
					height: (shopSetting.cusService.height * 2 || 90) + 'rpx',
					right: shopSetting.cusService.rightSpacing > 0 ? Number(shopSetting.cusService.rightSpacing) + 'px' : '',
					top: shopSetting.cusService.topSpacing > 0 ? Number(shopSetting.cusService.topSpacing) + CustomBar + 'px' : '',
					bottom: shopSetting.cusService.bottomSpacing > 0 ? Number(shopSetting.cusService.bottomSpacing) * 2 + 'rpx' : ''
				}"
			/>
		</template>
	</view>
</template>

<script>
import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue';
const app = getApp();
import api from 'utils/api';
import __config from '@/config/env';

export default {
	name: 'shop-pop',
	props: {
		shopInfo: {
			type: Object,
			default: () => {}
		}
	},

	data() {
		return {
			StatusBar: this.StatusBar,
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			cusServicePageUrl: ''
		};
	},
	components: {
		divBaseNavigator
	},
	computed: {
		//主题设置里面 店铺设置
		shopSetting: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.shopSetting || {};
			}
			return {};
		}
	},
	created() {
		this.getCustomerServiceInfo();
	},
	methods: {
		handleCusServiceClick() {
			// #ifdef MP
			wx.openCustomerServiceChat({
				extInfo: {
					url: this.shopInfo ? this.shopInfo.wxCustomerUrl : this.cusServicePageUrl
				},
				corpId: __config.chatId,
				success(res) {}
			});
			// #endif
			// #ifdef APP
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				openCustomerServiceChat: true,
				corpid: __config.chatId,
				customerUrl: this.shopInfo ? this.shopInfo.wxCustomerUrl : this.cusServicePageUrl,
				fail(err) {
					console.log('打开客服错误', err);
					// uni.makePhoneCall({
					//           phoneNumber: '**********'
					//          })
				}
			});
			// #endif
		},

		getCustomerServiceInfo() {
			let that = this;
			api.getLivingTag().then((res) => {
				if (res && res.data) {
					that.cusServicePageUrl = res.data.platWxCustomerUrl;
				}
			});
		}
	}
};
</script>

<style scoped lang="scss"></style>