<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">会员授权协议</block>
		</cu-custom>
	   <view class="" style="margin:20rpx">
		   <jyf-parser :html="datas.brandVipConfig && datas.brandVipConfig.warrantAgreement"></jyf-parser>
	   </view>
	</view>
</template>

<script>
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				datas: {}
			};
		},

		components: {},
		props: {},

		onLoad(options) {
		  this.id = options.brandId
		  api.getBrandVipMember(this.id||options.brandId).then(res => {
		  	this.datas = res.data
		  })
		},

		onShow() {
			app.initPage().then(res => {
			});
		},

		methods: {
		 

		}
	};
</script>
<style>
	page {
		background-color: #fff;
	}
</style>
