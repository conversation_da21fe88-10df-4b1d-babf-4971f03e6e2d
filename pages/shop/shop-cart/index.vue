<template>
	<shopcart :isBack="true" ref="shopcart" :cartType="cartType" :shopId="shopId" />
</template>
<script>
	const app = getApp();
    import shopcart from "../components/shop-cart/shop-cart";
	export default {
		components:{
			shopcart
		},
		data(){
		  return {
			  isFirst: true,
			  //有数统计使用
			  page_title:'购物车',
				cartType: '',
				//店铺Id
				shopId:''
		  }	
		},
		onLoad(options) {
			this.cartType = options.cartType || '';
			this.shopId = options.shopId||'';
		},

		onShow() {
			if(!this.isFirst){
				if(this.$refs.shopcart)
				this.$refs.shopcart.shoppingCartPage();
			}
			this.isFirst = false;
		},

	};
</script>
