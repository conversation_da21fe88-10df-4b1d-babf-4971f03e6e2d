<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">何所有</block>
		</cu-custom>

		<view style="display: flex;align-items: center;justify-content: center; height: 300px;">何所有商品扫码的专属页面</view>

	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
			}
		},
		methods: {

		}
	}
</script>

<style>

</style>