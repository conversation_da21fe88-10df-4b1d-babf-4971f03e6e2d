<template>
	<view>
     <!-- 带搜索框 -->
		<!-- <cu-custom :bgColor="'#F9F9F9'" simple :iconColor="'#000'" :boderColor="'#ccc'" :isBack="true"> -->

      <!-- 不带搜素框 -->
      <cu-custom
      bgColor="#f30c0a"
      :isBack="true"
      :hideMarchContent="true"
    >
			<!-- 带搜索框 -->
      <!-- <view slot="marchContent">
				<view class="search-form round" :style="{
			  height: `${searchHeight}px`,
			  lineHeight:'100%',
			  marginRight:' 20rpx',
			  marginLeft:' 0',
			  border: '1rpx solid #CDA488'
		  }"
        >
          <text
            class="cuIcon-search"
            style="font-size: 30rpx;color: #A1A1A1"
          ></text>
          <navigator
            class="response"
						style="padding-right: 100rpx;"
            hover-class="none"
            :url="'/pages/base/search/index?shopId='+parameter.shopId"
          >
            <input
              placeholder-style="color: #a1a1a1; font-size:26rpx;"
              v-model="parameter.name"
              type="text"
              :placeholder="parameter.name"
              confirm-type="search"
			  			style="width:130%"
            >
          </navigator>
        </view>
      </view> -->

      <!-- 不带搜素框 -->
      <block slot="backText">返回</block>
      <block slot="content">{{title}}</block>
    </cu-custom>

    <!-- 全部商品 -->
    <view style="margin-top: 80rpx;">
      <totalGoodsList :parameter="parameter" ref="goodlist" :theme="theme" :pageScrollTop="pageScrollTop" :heightHead="heightHead"  />
    </view>
	</view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();

	import api from 'utils/api'
    import totalGoodsList from "../components/good-list/index.vue";
	export default {

		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				parameter: {
				},
				title: '',
				//有数统计使用
				page_title: '店铺搜索过度页',
        heightHead: 0,
				searchHeight: 30,
        pageScrollTop: 0,
			};
		},

		components: {
      totalGoodsList
		},

		props: {},

		onLoad(options) {
			// #ifdef MP-WEIXIN
			let {
				height
			} = uni.getMenuButtonBoundingClientRect()
			this.searchHeight = height;
			// #endif

			let title = options.title ? decodeURI(options.title) : '店铺分类';

			this.title = title;
      console.log("this.title",this.title);

			//店铺商品一级分类
			if (options.categoryShopFirst) {
				this.parameter.categoryShopFirst = options.categoryShopFirst;
			}
			//店铺商品二级分类
			if (options.categoryShopSecond) {
				this.parameter.categoryShopSecond = options.categoryShopSecond;
			}
			if (options.brandCategoryShopFirst) {
				this.parameter.brandCategoryShopFirst = options.brandCategoryShopFirst;
			}
			if (options.brandCategoryShopSecond) {
				this.parameter.brandCategoryShopSecond = options.brandCategoryShopSecond;
			}

			if (options.name) {
				let parameter = decodeURIComponent(options.name)
				this.parameter.name = decodeURI(parameter);
			}

			if (options.couponUserId) {
				this.parameter.couponUserId = options.couponUserId;
			}

			if (options.shopId) {
				this.parameter.shopId = options.shopId
			}
			if(options.categoryType=='2'){
				this.parameter.categorySecond = options.categorySecond;
				this.parameter.shopBrandId = options.shopBrandId;
				this.categoryType = '2'
			    	
			}

		},

    onReachBottom() {
				this.$refs.goodlist && this.$refs.goodlist.reachBottom();
		},

		methods: {

		}
	};
</script>
<style scoped lang="scss">
	.fixed {
		position: fixed;
		top: 100px;
	}

	.excessive-cu-btn {
		position: relative;
		border: 0rpx;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;
		// padding: 0 30rpx;
		font-size: 28rpx;
		height: 44rpx;
		line-height: 44rpx;
		text-align: center;
		text-decoration: none;
		overflow: visible;
		margin-left: initial;
	}

	.excessive-follow {
		font-size: 17rpx;
		height: 25rpx;
	}

	.excessive-sm {
		padding: 0 6rpx;
		margin-right: 6rpx;
		font-size: 17rpx;
		height: 22rpx;
		// line-height: 22rpx;
		// text-align: center;
		color: #ffffff;
	}

	.excessive-btn {
		font-size: 26rpx;
		
		font-weight: 400;

		margin-top: 15rpx;
		// background: #ffffff;
		// border: 1px solid #93040d;
		color: #1a1a1a;
	}

	.excessive {
		position: relative;
		background: #ffffff;
		width: 750rpx;
		height: 96rpx;
		// border-radius: 22rpx;
		top: 0rpx;
		// left: 12rpx;
	}

	.topfixed-active {
		width: 100%;
		position: fixed;
		top: 0rpx;
		left: 0rpx;
		background: #fff;
		z-index: 9999;
		box-sizing: border-box;
	}

	.excessive-banner-icon {
		width: 152rpx;
		height: 76rpx;
		border-radius: 12rpx;
		border: solid 1px #ededed;
		margin-left: 12rpx;
	}

	.excessive-banner-text {
		display: block;
		flex-direction: row;
		width: 440rpx; // 第一种样式标签和店铺并齐
		//  width: 365rpx;  //第二种样式标签在店铺下面
	}

	.excessive-title {
		font-size: 28rpx;
		
		font-weight: 400;
		/**单行显示超出省略 */
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		width: 255rpx; // 第一种样式标签和店铺并齐
		width: 365rpx; //第二种样式标签在店铺下面
		color: #000;
	}

	.goods-search {
		top: unset !important;
		min-height: 100rpx !important;
	}

	.goods-nav {
		top: unset !important;
		margin-top: 100rpx;
	}

	.cuIcon-triangledownfill {
		margin-top: -22rpx;
	}

	.coupon-top {
		background-image: url(https://img.songlei.com/1/material/5eb12e53-a620-4913-a704-fd34178fa64f.png);
		background-size: contain;
		padding: 30rpx 20rpx;
	}

</style>
