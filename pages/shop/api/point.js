import {
	requestApi as request
} from '@/utils/api.js'

// 查看积分 
export const getTransactionstHistory = (data) => {
  return request({
    url: '/mallapi/brandvipmember/getTransactionstHistory',
    method: 'get',
    data
  })
}


//  分档接口  
export const pointsGrading = (data) => {
  return request({
    url: '/mallapi/goodsspu/pointsGrading',
    method: 'get',
    data
  })
}
//
export const getpointlevel = (data) => {
  return request({
    url: '/sco-activity-api/rest?method=sco.activity.api.info.getpointlevel',
    method: 'POST',
    data:{
		...data
	}
  })
}