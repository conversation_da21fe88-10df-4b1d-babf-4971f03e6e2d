<template>
	<view>
		<cu-custom :hideMarchContent="true" :isBack="true" :bgColor="bgColor">
			<block slot="backText">返回</block>
			<block slot="content">我的权益</block>
		</cu-custom>
		<image :src="data.brandVipConfig.rightsNavigatePic"
			style="position: absolute;top: 0;left: 0;height: 120rpx;width: 750rpx;"></image>
		<image :src="data.brandVipConfig.rightsPic" mode="widthFix"
			style="position:absolute;width: 100%;height: 100%;top: 120rpx;"></image>
	</view>
</template>

<script>
	import api from 'utils/api'
		
		export default {
		data() {
			return {
                    id:'',
					data:''
			}
		},
		methods: {

		},
		onLoad(options) {
			console.log(options,'options')
			 this.id = options.brandId
			 api.getBrandVipMember(this.id||options.brandId).then(res => {
			 	this.data = res.data
			 })
		}
	}
</script>

<style>

</style>