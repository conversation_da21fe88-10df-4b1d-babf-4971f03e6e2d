<template>
	<view>
		<view v-if="scene!='1154'">
			<view :style="{
			 position: 'fixed',
			 width: '100%',
			 top:0
		 }">
				<view :style="{
			   minHeight:`${(CustomBar>0?CustomBar:60)+140}px`,
			   width:' 100%',
			   backgroundImage: `url(${shopInfo.headImg})`,
			   backgroundRepeat:'no-repeat',
			   backgroundSize:'100% auto'
		   } "></view>
				<view
					style="position: absolute;left: 0; right: 0; bottom: 0; top: 0;background-color: rgba(0, 0, 0, 0.7)">
				</view>
			</view>
			<cu-custom class='custom' simple :bgColor="bgColor" :textColor="pageDivData.pageComponent.textColor">
				<view slot="marchContent">
					<view class="search-form round" :style="{
            height: `${HeightBar}px`,
            lineHeight: `${HeightBar}px`,
            marginRight:' 20rpx',
            marginLeft: 0,
            backgroudColor:'rgba(255,255,255,0.8)',
            color: '#FFFFFF'
          }">
						<text class="cuIcon-search" style="font-size: 30rpx;color: #000000;"></text>
						<navigator class="response" hover-class="none"
							:url="'/pages/base/search/index?shopId='+parameter.shopId">
							<input style="height: 100%;line-height:100%;" type="text" placeholder="请输入关键字"
								placeholder-style="color: #000000; font-size:26rpx;"
								:style="{ width: `${(750-menuWidth*pixelRatio-leftMenuWidth/2*pixelRatio)- 100}rpx`}">
						</navigator>
					</view>
				</view>
			</cu-custom>

			<view :class="'cu-modal ' + (shopInfo.enable=='0'?'show':'')">
				<view class="cu-dialog">
					<view class="cu-bar bg-white justify-end">
						<view class="content">提示</view>
					</view>
					<view class="padding-xl">抱歉，该店铺不存在或已下架</view>
				</view>
			</view>

			<!-- 店铺顶部 -->
			<!-- :style="{top: `${CustomBar}px`}"
			:class="scrollTop>scrollTopCut?'hidden_header':'show_header'" -->
			<view>
				<view class="shop-head" ref="shop-head">
					<view class="shop-top">
						<!--店铺信息-->
						<view class="shop-information">
							<view>
								<image mode="aspectFit" class="excessive-banner-icon" :src="shopInfo.imgUrl" alt=""
									srcset="">
								</image>
							</view>

							<view class="left">
								<view class="name  overflow-1">
									{{shopInfo.name||''}}
								</view>

								<view class="info">
									<text class="tag" style="width:auto" v-if="shopInfo.selfSupport=='1'">
										松鼠自营
									</text>

									<view class="tag experience" style="width: 65rpx;border-radius: 20rpx;">
										上新
									</view>
									<view class="fans">{{shopInfo.collectCount}}人关注</view>
								</view>
							</view>

							<view class="right" @click="wxTemplate()">
								<text class="cuIcon-favor margin-right-xs" style="font-size: 28rpx;"></text>
								<text>{{shopInfo.collectId ? '已收藏' : '收藏'}}</text>
							</view>
						</view>

						<!-- 店铺顶部tab -->
						<view class="cu-bar  top-home fixed shop-card" style="position: unset;min-height: 90rpx;">
							<view class="tab">
								<!--  #ifdef MP-WEIXIN -->
								<view class="item" :class="topTabCur==1?'select':''" @click="handleTopTab('1')">推荐
								</view>
								<!--  #endif -->

								<!--  #ifndef MP-WEIXIN -->
								<view class="item" :class="topTabCur==1?'select':''" @click="handleTopTab('1')">优选
								</view>
								<!--  #endif -->

								<view class="item" :class="topTabCur==2?'select':''" style="margin-left: 70rpx;"
									@click="handleTopTab('2')">宝贝</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 店铺会员优惠券 -->
				<!-- <view class="member-box flex align-center justify-between" v-show="PageCur=='1'">
          <view class="flex-sub flex align-center justify-center">
            <image style="width: 72upx;height: 72upx;border-radius: 50%;background-color: #ddd;"></image>
            <view class="text-sm" style="margin-left: 20upx;width: 220upx;">
              <view>L1: <text>###!</text></view>
              <view>积分：<text class="text-xdf text-bold">38888</text></view>
            </view>
          </view>
          <view style="width: 2upx;height:67upx;background-color: #eaeaea;"></view>
          <view class="flex-sub text-center text-sm">
            <image src="" style="width: 38upx;height: 32upx;background-color: #ddd;"></image>
            <view>会员专享券</view>
          </view>
        </view> -->
				<view class="main-container">

					<shopHome v-show="PageCur=='1'" @homeData="getHomeData" :parameter="parameter" :theme="theme"
						ref='shophome' :shopInfo="shopInfo" :heightHead="heightHead"
						:shopDetailShowVideo="assistanceShow" />
					<!-- 全部商品 -->
					<totalGoodsList v-show="PageCur=='2'" :parameter="parameter" ref="goodlist" :theme="theme"
						:pageScrollTop="pageScrollTop" :heightHead="heightHead" />
					<!-- 分类 -->
					<goodCategory v-if="PageCur=='3'" :parameter="parameter" :theme="theme" :heightHead="heightHead" />
					<!-- 商铺动态 -->
					<shopActive v-show="PageCur=='4'" :parameter="parameter" :shopName="shopInfo.name" ref="shopActive"
						:heightHead="heightHead">
					</shopActive>
					<!-- 店铺会员 -->
					<jionmumber v-if="PageCur=='6'" :parameter="parameter" :jionmumberdata="jionmumberdata"
						:shopInfo="shopInfo" ref="jionmumber" :heightHead="heightHead" :vipmber="vipmber"></jionmumber>
				</view>
				<view class="cu-bar tabbar bg-white shadow foot">
					<view class="action" @click="NavChange" data-cur="1" :class="PageCur=='1'?'text-shop':'text-gray'"
						:style="[{color: PageCur=='1'? pageDivData.pageComponent.tabTextColor :  pageDivData.pageComponent.tabDefaultTextColor}]">
						<image :src="PageCur=='1'?tabSelectImage(0):tabDefaultImage(0)" class="tab-image"
							:class="tabText(0)?'':'tab-lg'"></image>
						<view v-if="tabText(0)" class="tabbar-text">{{tabText(0)}}</view>
					</view>

					<view class="action" @click="NavChange" data-cur="2" :class="PageCur=='2'?'text-shop':'text-gray'"
						:style="[{color: PageCur=='2'? pageDivData.pageComponent.tabTextColor :  pageDivData.pageComponent.tabDefaultTextColor}]">
						<image :src="PageCur=='2'?tabSelectImage(1):tabDefaultImage(1)" class="tab-image"
							:class="tabText(1)?'':'tab-lg'">
						</image>
						<view class="tabbar-text">{{tabText(1)}}</view>
					</view>

					<view class="action" @click="NavChange" data-cur="4" :class="PageCur=='4'?'text-shop':'text-gray'"
						:style="[{color: PageCur=='4'? pageDivData.pageComponent.tabTextColor :  pageDivData.pageComponent.tabDefaultTextColor}]">
						<image :src="PageCur=='4'?tabSelectImage(3):tabDefaultImage(3)" class="tab-image"
							:class="tabText(3)?'':'tab-lg'">
						</image>
						<view class="tabbar-text">{{tabText(3)}}</view>
					</view>

					<view class="action" @click="NavChange" data-cur="3" :class="PageCur=='3'?'text-shop':'text-gray'"
						:style="[{color: PageCur=='3'? pageDivData.pageComponent.tabTextColor :  pageDivData.pageComponent.tabDefaultTextColor}]">
						<image :src="PageCur=='3'?tabSelectImage(2):tabDefaultImage(2)" class="tab-image"
							:class="tabText(2)?'':'tab-lg'">
						</image>
						<view class="tabbar-text">{{tabText(2)}}</view>
					</view>

					<!-- 购物车展示不显示在底部了 -->
					<!-- <view class="action" @click="NavChange" data-cur="5"
						:class="PageCur=='5'?'text-'+theme.themeColor:'text-gray'"
						:style="[{color: PageCur=='5'? pageDivData.pageComponent.tabTextColor :  pageDivData.pageComponent.tabDefaultTextColor}]">
						<image :src="PageCur=='5'?tabSelectImage(4):tabDefaultImage(4)" class="tab-image"
							:class="tabText(4)?'':'tab-lg'"></image>
						<view class="tabbar-text">{{tabText(4)}}</view>
						<view v-if="shoppingCartCount>0" style="position: absolute;
						right: 24px;
						top: -5px;
						background-color: #ff0000;
						color: #ffffff;
						border-radius: 50%;
						width: 26rpx;
						height: 26rpx;
						line-height: 26rpx;">{{shoppingCartCount||0}}</view>
					</view> -->

					<view v-if="shopInfo.brandId&&jionmumberdata&&jionmumberdata.brandId&&vipmber.brandVipConfig"
						class="action" @click="NavChange" data-cur="6" :class="PageCur=='6'?'text-shop':'text-gray'"
						:style="[{color: PageCur=='6'? pageDivData.pageComponent.tabTextColor :  pageDivData.pageComponent.tabDefaultTextColor}]">
						<image :src="PageCur=='6'?tabSelectImage(5):tabDefaultImage(5)" class="tab-image"
							:class="tabText(5)?'':'tab-lg'"></image>
						<view class="tabbar-text">{{tabText(5)}}</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 分享组件 -->
		<view v-if="scene&&scene=='1154'">
			<share-single-page />
		</view>
		<!-- 浇树游戏品牌助力任务浏览倒计时 -->
		<view class="daojishi-box" v-if="taskId">
			浏览剩
			<text v-if="0 != countdown[0]"><text style="color:#FF0000">{{countdown[0]}}</text>分</text>
			<text style="color:#FF0000">{{countdown[1]}}</text>秒
		</view>
		<!-- 浇树游戏品牌助力任务完成弹框 -->
		<view v-if="assistanceShow">
			<brand-assistance :task-id="taskId" @close="closeAssistanceDia" />
		</view>
	</view>
</template>

<script>
	import util from '@/utils/util'
	const app = getApp();
	import api from 'utils/api'
	import goodCategory from "../components/good-category/index.vue";
	import shopHome from "../components/shop-home/index.vue";
	import totalGoodsList from "../components/good-list/index.vue";
	import shopActive from "../components/shop-active/index.vue";
	import shareSinglePage from "components/share-single-page/index";
	import jionmumber from "../components/jionmember/jionmember.vue"
	import BrandAssistance from "./components/BrandAssistance";
	import {
		getWxTemplate
	} from "@/api/message.js";
	import {
		senTrack
	} from "@/public/js_sdk/sensors/utils.js";

	import {
		EventBus
	} from '@/utils/eventBus.js'
	import {
		mapState,
	} from 'vuex'
	export default {
		components: {
			shopHome,
			totalGoodsList,
			goodCategory,
			shopActive,
			shareSinglePage,
			jionmumber,
			BrandAssistance,
		},
		data() {
			return {
				jionmumberdata: {},
				topTabCur: 1,
				theme: app.globalData.theme, //全局颜色变量
				showDefaultPage: false,
				PageCur: '1',
				pageDivData: {
					pageComponent: {}
				},
				id: '',
				shopInfo: {
					collectCount: 0
				},
				parameter: {},
				title: '',
				posterUrl: "",
				posterShow: false,
				posterConfig: "",
				shareShow: '',
				curLocalUrl: '',
				heightHead: 0,
				pageScrollTop: 0,
				shoppingCartCount: 0,
				height: 0,
				searchHeight: 30,
				bgColor: "rgba(255, 255, 255, 0)",
				iconColor: '#fff',
				loading: true,
				scene: '', //场景值
				//有数统计使用
				page_title: '店铺页',
				jionmumber: true,
				vipmber: "",

				intervalId: null,
				secondVal: "",
				browseTime: "",
				taskId: "", //浇树活动品牌助力任务
				assistanceShow: false, //品牌助力任务结果弹框

				// 统计品牌店铺页面停留时长
				startTimeStamp: 0,
				isSendSenTrack: 0  // 0 初始化状态   1 已发送  再次onShow 可再次发送
			};
		},

		props: {},
		computed: {
			...mapState([
				'windowWidth',
				'HeightBar',
				'CustomBar',
				'menuWidth',
				'leftMenuWidth',
				'pixelRatio'
			]),
			tabDefaultImage() {
				return function(index) {
					// if (this.pageDivData.pageComponent.tabbarItems && this.pageDivData.pageComponent.tabbarItems[
					//   index] && this.pageDivData.pageComponent.tabbarItems[index].iconPath) {
					//   return this.pageDivData.pageComponent.tabbarItems[index].iconPath.startsWith("http") ? this
					//     .pageDivData.pageComponent.tabbarItems[index].iconPath :
					//     `/static/public/img/shop/thememobile/${index + 1}-001.png`;
					// }
					return `https://img.songlei.com/live/shop/thememobile/${index + 1}-001.png`;
				}
			},

			tabSelectImage() {
				return function(index) {
					// 第一个可以后台配置
					if (index == 0) {
						if (this.pageDivData.pageComponent && this.pageDivData.pageComponent.tabbarItems && this
							.pageDivData.pageComponent.tabbarItems[
								0] && this.pageDivData.pageComponent.tabbarItems[0].selectedIconPath) {
							return this.pageDivData.pageComponent.tabbarItems[0].selectedIconPath.startsWith("http") ?
								this.pageDivData.pageComponent.tabbarItems[0].selectedIconPath :
								`https://img.songlei.com/live/shop/thememobile/${index + 1}-002.png`;
						}
					}
					return `https://img.songlei.com/live/shop/thememobile/${index + 1}-002.png`;
				}
			},

			tabText() {
				return function(index) {
					// if (this.pageDivData.pageComponent.tabbarItems && this.pageDivData.pageComponent.tabbarItems[
					//   index]) {
					//   return this.pageDivData.pageComponent.tabbarItems[index].text
					// } else if (!this.pageDivData.pageComponent.tabbarItems || !this.pageDivData.pageComponent
					//   .tabbarItems[index]) {
					if (index == 0) {
						if (this.pageDivData.pageComponent && this.pageDivData.pageComponent.tabbarItems && this
							.pageDivData.pageComponent.tabbarItems[0] && this.pageDivData.pageComponent.tabbarItems[0]
							.text) {
							return this.pageDivData.pageComponent.tabbarItems[0].text
						} else {
							return '首页';
						}
					} else if (index == 1) return '商品';
					else if (index == 2) return '分类';
					else if (index == 3) return this.shopInfo.name && this.shopInfo.name.indexOf('何所有') > -1 ? '品牌故事' :
						'品牌秀秀';
					else if (index == 4) return '购物车';
					else if (index == 5) return '会员';
				}
			},

			countdown() {
				if ("" !== this.secondVal) {
					if (this.secondVal < 0) {
						clearInterval(this.intervalId);
						this.assistanceShow = true;
						return [0];
					} else {
						let minute = Math.floor(this.secondVal / 60);
						let second = this.secondVal - minute * 60;
						return [minute, second];
					}
				}
			},
		},
		onLoad(options) {
			uni.showLoading();
			//获取屏幕滚动距离
			let res = uni.getSystemInfoSync()
			this.height = res.windowHeight;

			// --------小程序分享朋友圈开始------
			// #ifdef MP-WEIXIN
			uni.showShareMenu({
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			});
			// --------小程序分享朋友圈结束------
			let getLaunchOptions = uni.getLaunchOptionsSync()
			this.scene = getLaunchOptions.scene
			let {
				height
			} = uni.getMenuButtonBoundingClientRect()
			this.searchHeight = height;
			// #endif
			// 保存别人分享来的 userCode
			//接受二维码中参数 参数有两种情况  scene=id=XXX  或者直接 scene=XXX  XXX代表店铺id
			let id = options.scene ? (decodeURIComponent(options.scene).includes('id') ? util.UrlParamHash(
					decodeURIComponent(options.scene), 'id') : decodeURIComponent(options.scene).split('&')[0]) : options
				.id;
			if (options.cur === '6') {
				this.topTabCur = 5;
				this.PageCur = 6;
			}
			this.id = id;

			this.parameter.shopId = id
			//场景值等于 1154 分享单页模式
			if (this.scene && this.scene == 1154) return
			app.initPage().then(res => {
				this.shopInfoGet();
			});
			// 浇树活动品牌助力任务
			if (options.taskId) {
				this.taskId = options.taskId;
				this.browseTime = options.browseTime;
				this.timing();
			}
			// setTimeout(() => {
			//   var pages = getCurrentPages() //获取加载的页面
			//   var currentPage = pages[pages.length - 1] //获取当前页面的对象
			//   currentPage.$vm.shopCoupon()
			// }, 5000)
		},
		onShow() {
			if(this.isSendSenTrack==1){
				senTrack('BrandPageView', {
					page_name: '店铺详情',
					goods_brand_id: this.shopInfo.brandId,
					goods_brand_name: this.shopInfo.brand,
				});
			}
			this.shoppingCartCount = app.globalData.shoppingCartCount;
			this.startTimeStamp = new Date().getTime();
		},
		//分享朋友
		onShareAppMessage: function() {
			let pageDivData = this.pageDivData.pageComponent
			let shareShow, shareTitle, shareImageUrl;
			shareShow = pageDivData.shareShow ? pageDivData.shareShow : ''
			if (shareShow && shareShow == '1') {
				shareTitle = pageDivData.shareTitle ? pageDivData.shareTitle : this.shopInfo.name
				shareImageUrl = pageDivData.shareImageUrl ? pageDivData.shareImageUrl + '-jpg_w360_q90' : ''
				//图片压缩
				// if (shareImageUrl) shareImageUrl + '-jpg_w360_q90'
			}
			console.log("shareShow, shareTitle, shareImageUrl", shareShow, shareTitle, shareImageUrl);
			const userInfo = uni.getStorageSync('user_info')
			let userCode = userInfo ? '&sharer_user_code=' + userInfo.userCode : ''
			let path = '/pages/shop/shop-detail/index?id=' + this.shopInfo.id + userCode;
			// imageUrl: this.shopInfo.imgUrl,
			return {
				title: shareTitle,
				path: path,
				imageUrl: shareImageUrl,
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log(res.errMsg);
					}
				},
				fail: function(res) { // 转发失败
				}
			};
		},

		// 分享朋友圈
		onShareTimeline: function() {
			let pageDivData = this.pageDivData.pageComponent
			let shareShow, shareTitle, shareImageUrl;
			shareShow = pageDivData.shareShow ? pageDivData.shareShow : ''
			if (shareShow && shareShow == '1') {
				shareTitle = pageDivData.shareTitle ? pageDivData.shareTitle : this.shopInfo.name
				// //图片压缩
				// if (shareImageUrl) shareImageUrl + '-jpg_w360_q90'
				shareImageUrl = pageDivData.shareImageUrl ? pageDivData.shareImageUrl : ''
			}
			// console.log("shareImageUrl", shareImageUrl);
			// console.log("shareShow, shareTitle, shareImageUrl", shareShow, shareTitle, shareImageUrl);
			const userInfo = uni.getStorageSync('user_info')
			let userCode = userInfo ? '&sharer_user_code=' + userInfo.userCode : ''
			let path = 'pages/shop/shop-detail/index?id=' + this.shopInfo.id + userCode;

			this.dataController({
				bhv_type: "share",
				item_id: this.goodsSpu.id
			});
			return {
				title: shareTitle,
				query: path,
				imageUrl: shareImageUrl,
				success: function(res) {
					console.log(res.errMsg);
					uni.showToast({
						title: '分享成功'
					})

				},
				fail: function(res) { // 转发失败
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					})
				}

			};
		},

		onPageScroll(e) {
			// if (e.scrollTop > 60 && e.scrollTop <250) {
			this.pageScrollTop = e.scrollTop;
			// }
			if (this.pageScrollTop > 10) {
				this.bgColor = "rgba(186, 156, 128, " + ((this.pageScrollTop + 30) / 100) + ')'
				// this.iconColor = "#000"
			} else {
				this.bgColor = "rgba(186, 156, 128, 0)"
			}

		},
		onReachBottom() {
			if (this.PageCur == '2') {
				this.$refs.goodlist && this.$refs.goodlist.reachBottom();
			} else if (this.PageCur == '4') {
				this.$refs.shopActive && this.$refs.shopActive.reachBottom();
			}
			//装修组件的上滑加载下一页
			EventBus.$emit("divGoodsGroupsReachBottom", "true")
		},
		onPullDownRefresh() {
			this.shopInfoGet();
			if (this.PageCur == '6') {
				this.$nextTick(function() {
					this.$refs.jionmumber.getmessage()
				})
			}
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		onHide() {
			this.handleLeave();
		},
		onUnload() {
			clearInterval(this.intervalId);
			this.handleLeave();
		},
		methods: {
			handleLeave(){
				//上传神策 品牌店铺页离开
				var endTimeStamp = new Date().getTime();
				senTrack('BrandPageLeave', {
					page_name: '店铺详情',
					goods_brand_id: this.shopInfo.brandId,
					goods_brand_name: this.shopInfo.brand,
					stay_duration: (endTimeStamp - this.startTimeStamp) / 1000
				})
			},
			getcur() {
				this.topTabCur = 5;
				this.PageCur = 6;
			},
			getHomeData(data) {
				this.showDefaultPage = data.showDefaultPage;
				this.pageDivData = data.pageDivData;
			},
			handleTopTab(tab) {
				this.topTabCur = tab;
				this.PageCur = tab;
			},

			shopInfoGet() {
				let that = this
				api.shopInfoGet(this.parameter.shopId).then(res => {
					let shopInfo = res.data;
					this.shopInfo = shopInfo;
					//向神策发送浏览品牌店铺页面事件
					if (this.isSendSenTrack==0) {
						senTrack('BrandPageView', {
							page_name: '店铺详情',
							goods_brand_id: this.shopInfo.brandId,
							goods_brand_name: this.shopInfo.brand,
						});
						this.isSendSenTrack = 1;
					}
					
					this.parameter.brandId = shopInfo.brandId
					this.jionmumberdata = shopInfo
					api.getBrandVipMember(shopInfo.brandId).then(res => {
						this.vipmber = res.data
					})
					uni.hideLoading();
					that.loading = false
					this.$nextTick(() => {
						uni.getSystemInfo({
							success: function(res) { // res - 各种参数
								console.log(res.windowHeight); // 屏幕的宽度
								let info = uni.createSelectorQuery().select(
									".shop-head"); // 获取某个元素
								info.boundingClientRect(function(data) { //data - 各种参数
									if (data) {
										console.log("获取的高度", data.height) // 获取元素宽度
										that.heightHead = data.height +
											10; //有10px的底部margin
									} else {
										console.error("没有获取到shop-head高度")
									}

								}).exec()
							}
						});
					})
				});
			},
			NavChange: function(e) {
				let cur = e.currentTarget.dataset.cur
				if (cur == '5') {
					uni.navigateTo({
						url: '/pages/shop/shop-cart/index'
					});
				} else {
					wx.pageScrollTo({
						scrollTop: 0
					})
					if (cur != this.PageCur) {
						this.PageCur = cur;
					}
					this.showShare = true;
				}
				if (cur == '1') {
					this.topTabCur = 1
					this.$refs.shophome.getshopinfo()
				} else if (cur == '2') {
					this.topTabCur = 2
				}
			},
			NavChangeCur2() {
				this.PageCur = 2
				if (this.goodsList.length <= 0) {
					this.goodsPage();
				}
			},

			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				});
			},

			//收藏
			userCollect() {
				let shopInfo = this.shopInfo;
				let collectId = shopInfo.collectId;

				if (collectId) {
					api.userCollectDel(collectId).then(res => {
						// uni.showToast({
						// 	title: '已取消订阅',
						// 	icon: 'success',
						// 	duration: 2000
						// });
						shopInfo.collectId = null;
						shopInfo.collectCount = shopInfo.collectCount - 1
						this.shopInfo = shopInfo;
					});
				} else {
					api.userCollectAdd({
						type: '2',
						relationIds: [shopInfo.id]
					}).then(res => {
						// uni.showToast({
						// 	title: '收藏成功',
						// 	icon: 'success',
						// 	duration: 2000
						// });
						shopInfo.collectId = res.data[0].id;
						shopInfo.collectCount = shopInfo.collectCount + 1
						this.shopInfo = shopInfo;
					});
				}
			},
			// 店铺优惠券
			shopCoupon() {
				// this.PageCur = '6';
				// this.$refs?.jionmumber?.getEvtscdList()
				uni.navigateTo({
					url: '/pages/coupon/coupon-shop-list/index?counter=' + this.shopInfo.cabinetGroup
				})
				// console.log()
			},
			// 品牌助力任务浏览倒计时
			timing() {
				this.secondVal = Number(this.browseTime);
				this.intervalId = setInterval(() => {
					this.secondVal--;
				}, 1000);
			},
			// 关闭品牌助力任务完成弹框
			closeAssistanceDia() {
				this.assistanceShow = false;
				uni.navigateBack()
			},
			wxTemplate(type) {
				// #ifdef MP
				getWxTemplate({
					type: 12
				}).then(res => {
					uni.requestSubscribeMessage({
						tmplIds: res.data,
						complete: () => {
							this.userCollect()
						}
					})
				})
				// #endif
				// #ifndef MP
				this.userCollect()
				// #endif
			},
		}
	};
</script>
<style lang="less">
	.text-shop {
		color: #ff4f00;
	}

	.excessive-banner-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 15rpx;
		margin-right: 20rpx;
		background: #ffffff;
	}

	page {
		background-color: #ffffff;
	}

	.main-container {
		padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
	}

	.tab {
		display: flex;
		align-items: center;
		height: 100%;
		margin-left: 30rpx;

		.item {
			font-size: 30rpx;
			font-weight: 500;
			color: #ffffff;
			padding-bottom: 16rpx;
		}

		.select {
			border-bottom: solid 6rpx #fff;
			font-weight: bold;
		}
	}

	.shop-information {
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		width: 750rpx;
		margin-right: 12rpx;
		padding: 19rpx;

		.left {
			flex: 1;
			margin-top: -6rpx;

			.name {
				font-weight: bold;

				color: #ffffff;
				font-size: 30rpx;
			}

			.info {
				margin-top: 10rpx;
				display: flex;

				.tag {
					width: 85rpx;
					height: 30rpx;
					background: linear-gradient(90deg, #fe3915 0%, #ff651a 100%);
					// border: 1rpx solid #ff2727;
					border-radius: 4rpx;
					font-size: 20rpx;
					color: #ffffff;
					text-align: center;
					border-radius: 8rpx;
				}

				.experience {
					background: #ff9d2e;
					color: #ffffff;
					margin-left: 12rpx;
				}

				.fans {
					font-size: 20rpx;
					margin-left: 12rpx;
					color: #fff;
				}
			}
		}

		.right {
			width: 124rpx;
			height: 52rpx;
			background: linear-gradient(90deg, #ff1700 0%, #fc5a13 100%);
			border-radius: 27rpx;
			// border: 1rpx solid #fff;
			color: #ffffff;
			font-size: 22rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.nav-pretty {
		height: 60rpx;
		// width: 100%;
		background-image: url('https://img.songlei.com/live/shop/pretty-bg.png');
		background-size: 100% 100%;
		display: flex;
		justify-content: center;
		margin-top: 20rpx;
	}

	.shop-card {
		box-shadow: unset !important;
	}

	.cuIcon-triangledownfill {
		margin-top: -22rpx;
	}

	.top-home {
		top: unset !important;
	}

	.shop-head {
		// position: fixed;
		// top: 50px;
		position: relative;
	}

	.shop-top {
		// position: absolute;
		// z-index: 9999;
		// top: 190rpx;
	}

	.show_header {
		animation: showLayer 0.05s linear both;
	}

	.hidden_header {
		animation: hideLayer 0.05s linear both;
	}

	@keyframes showLayer {
		0% {
			display: block;
			transform: translateY(-100%);
		}

		100% {
			transform: translateY(0); //这里可以通过变大变小调整偏移量
		}
	}

	@keyframes hideLayer {
		0% {
			transform: translateY(0);
		}

		100% {
			transform: translateY(-100%);
			display: none;
		}
	}

	.tab-image {
		width: 36rpx;
		height: 36rpx;
		margin: 0 auto 12rpx;
		text-align: center;
	}

	.tab-lg {
		width: 60rpx;
	}

	.tabbar-text {
		font-size: 20rpx;
	}

	page {
		background: #F6F6F6
	}

	.member-box {
		width: 750upx;
		height: 110upx;
		background-color: #fff;
		border-radius: 25upx 25upx 0 0;
		position: relative;
		z-index: 1;
	}

	.daojishi-box {
		position: fixed;
		top: 350rpx;
		left: 20rpx;
		width: 230rpx;
		height: 63rpx;
		line-height: 63rpx;
		text-align: center;
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: rgba(255, 255, 255, 1);
		background: rgba(0, 0, 0, 0.8);
		border-radius: 32rpx;
	}
</style>