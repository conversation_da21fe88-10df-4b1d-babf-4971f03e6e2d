<template>
  <view class="BrandAssistance-page">
    <uni-popup
      ref="popup"
      @maskClick="close"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="core-box" v-if="waterTemplate">
        <view class="one-box">
          <view class="yin">品牌送您{{waterTemplate.tip2}}</view>
          <image v-if="waterTemplate.templateComponent" class="tupian" :src="waterTemplate.templateComponent.codeResPrizeUrl" />
          <view class="wen">x {{ giveWaterNum }}</view>
        </view>
        <view class="two-box">
          明日再来品牌助力，还可得{{ tomorrowWaterNum }}{{waterTemplate.tip2}}哦
        </view>
        <view class="three-box" @click="close">
          <view class="wen">关闭</view>
          <image class="btn" :src="iconPic.huang2" />
        </view>
        <image v-if="waterTemplate.templateComponent" class="bg" :src="waterTemplate.templateComponent.taskCompletedUrl" />
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年9月6日
 **/
import { doneApi } from "@/pages/squirrelParadise/api/squirrelParadise";

export default {
  props: {
    taskId: {
      type: String,
      require: true,
      default: "",
    },
  },
  data() {
    return {
      giveWaterNum: "",
      tomorrowWaterNum: "",
      iconPic: {
        // ppzlbg: "http://img.songlei.com/paradise/task/ppzlbg.png",
        // snowmanPpzlbg: "http://img.songlei.com/paradise/task/snowman-ppzlbg.png",

        // shuibg: "http://img.songlei.com/paradise/task/shuibg.png",
        // snowmanShuidi: "http://img.songlei.com/paradise/task/snowman-shuidi.png",
        // nianShuidi: "http://img.songlei.com/paradise/task/nian-shuidi.png",

        huang2: "http://img.songlei.com/paradise/task/huang2.png",
      },
      waterTemplate:{},//游戏模版
    };
  },
  mounted() {
    this.waterTemplate = uni.getStorageSync('waterTemplate');
    console.log("this.waterTemplate===>",this.waterTemplate);
    this.done();
    this.$refs.popup.open("center");
  },
  methods: {
    done() {
      const params = {
        taskId: this.taskId,
      };
      doneApi(params).then((res) => {
        const { giveWaterNum, tomorrowWaterNum } = res.data;
        this.giveWaterNum = giveWaterNum;
        this.tomorrowWaterNum = tomorrowWaterNum;
      });
    },
    close() {
      this.$refs.popup.close();
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.BrandAssistance-page {
  .core-box {
    position: relative;
    width: 648rpx;
    height: 549rpx;
    text-align: center;
    // padding-top: 150rpx;
    padding-top: 165rpx;
    .one-box {
      display: flex;
      justify-content: center;
      align-items: center;
      .yin {
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #953c22;
        margin-right: -50rpx;
      }
      .tupian {
        width: 226rpx;
        // height: 225rpx;
        height: 175rpx;
      }
      .wen {
        font-size: 40rpx;
        
        font-weight: bold;
        color: #3288fe;
        margin-left: -60rpx;
      }
    }
    .two-box {
      // margin-top: -40rpx;
      margin-top: 0rpx;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #953c22;
    }
    .three-box {
      position: relative;
      margin: auto;
      margin-top: 10rpx;
      width: 239rpx;
      height: 77rpx;
      .wen {
        padding-top: 10rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #9f4227;
      }
      .btn {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        width: 239rpx;
        height: 77rpx;
      }
    }

    .bg {
      z-index: -10;
      position: absolute;
      top: 0;
      right: 0;
      width: 648rpx;
      height: 549rpx;
    }
  }
}
</style>
