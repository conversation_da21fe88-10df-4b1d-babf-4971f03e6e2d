<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">加入松雷商业</block>
		</cu-custom>
		<view>
			<image src="https://img.songlei.com/live/shop/enter/tc01.png" alt="" style="height:325rpx;width:100%;">
			</image>
			<view style="padding-left: 26rpx;padding-right: 26rpx;position: relative;top: -120rpx;">
				<view v-if="!cabinetGroup || (cabinetGroup&&!cabinetGroup.startsWith('203'))">
					<!-- <image src="https://img.songlei.com/live/shop/enter/gzwx.png" alt="" style="height:200rpx;width:100%;"> </image> -->
					<official-account style='width: 100%;'></official-account>
				</view>
				<view style="display: flex;margin-top: 26rpx;">
					<view style="height:180rpx;width: 48%;" @click="shopgo">
						<image src="https://img.songlei.com/live/shop/enter/slshop01.png" alt="" style="height:180rpx">
						</image>
					</view>
					<view style="height:180rpx;width: 48%;margin-left: 22rpx;" @click="Flagshipstore">
						<image src="https://img.songlei.com/live/shop/enter/ppqj01.png" alt="" style="height:180rpx">
						</image>
					</view>
				</view>
				<view class="flex" style="margin-top: 26rpx;">
					<view style="height:180rpx;width: 48%;" @click="member">
						<image src="https://img.songlei.com/live/shop/enter/slshop01.png" alt="" style="height:180rpx">
						</image>
					</view>
					<view style="height:180rpx;width: 48%;margin-left: 22rpx;" @click="downloadApp">
						<image src="https://img.songlei.com/live/shop/enter/downloadapp.png" alt="" style="height:180rpx">
						</image>
					</view>
				</view>
			</view>
			<view
				style="width: 608rpx;height: 123px;font-size: 22rpx;font-family: PingFang SC;color: #4F4F4F;font-weight: 500;margin: 0 auto;">
				<view style="margin-top:  -102rpx;">1、会员卡仅限本人使用，同一微信号、同一手机号限申请一张 </view>
				<view style="padding-left: 36rpx;margin-top: 10rpx;">会员卡及相关优惠不可转让。 </view>
				<view style="margin-top: 10rpx;"> 2、会员卡享有会员活动和相应优惠。</view>
				<view style="margin-top: 10rpx;">3、松雷商业保留随时修订或更改会员条款及权益的权利。</view>
			</view>
		</view>
	</view>
</template>

<script>
	const util = require("utils/util.js");
	import __config from '@/config/env';
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				theme: app.globalData.theme,
				CustomBar: this.CustomBar,
				page_title: '加入松雷商业',
				cabinetGroup: '',
				id: ''
			};
		},
		props: {},
		onLoad(options) {
			console.log("===options========", options)
			//options.scene又两种情况 1 scene里面直接是店铺id  2 scene: `id=${this.shopId}&sf=${this.cabinetGroup}`,
			if (options.scene) {
				const qrCodeScene = decodeURIComponent(options.scene);
				if (qrCodeScene.indexOf('=') != -1) {
					const qrCodeSceneArray = qrCodeScene.split('&');
					const qrCodeSf = util.UrlParamHash(qrCodeScene, 'sf');
					if (qrCodeSf) {
						app.globalData.sf = qrCodeSf;
					}
					const id = util.UrlParamHash(qrCodeScene, 'id');
					if (id) {
						this.id = id;
					}
				} else {
					this.id = options.scene
				}
			}
			if (!this.id) {
				this.id = options.id
			}
			if (this.id) {
				api.shopInfoGet(this.id).then(res => {
					let shopInfoList = res.data;
					this.cabinetGroup = shopInfoList.cabinetGroup
					if (shopInfoList.cabinetGroup && !util.isUserLogin()) {
						console.log(this.cabinetGroup, 'this.cabinetGroup1')
						uni.setStorageSync('cabinetGroup', this.cabinetGroup)
					}
				})
			}
			console.log(this.cabinetGroup, 'this.cabinetGroup')
			app.initPage().then(res => {

			})
		},
		methods: {
			shopgo() {
				uni.reLaunch({
					url: '/pages/home/<USER>'
				});
			},
			Flagshipstore() {
				uni.navigateTo({
					url: `/pages/shop/shop-detail/index?id=${this.id}`
				});
			},
			member() {
				if (util.isUserLogin()) {
					uni.navigateTo({
						url: `/pages/user/user-qrcode/index`
					});
				} else {
					uni.navigateTo({
						url: `/pages/login/index`
					});
				}
			},
			
			// 去客服那里快捷引导下载
			downloadApp() {
				const wxCustomerUrl = "https://work.weixin.qq.com/kfid/kfce7573262066e336c";
				// #ifdef MP
				wx.openCustomerServiceChat({
				  extInfo: {
				    url: wxCustomerUrl,
				  },
				  corpId: __config.chatId,
				  showMessageCard: true,
				  sendMessageTitle: "app下载",
				  sendMessageImg: "",
				  success(res) {},
				});
				// #endif
				// #ifdef APP
				uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					openCustomerServiceChat: true,
					corpid: __config.chatId,
					customerUrl: wxCustomerUrl,
					fail(err) {
						console.log("打开客服错误", err);
				    uni.makePhoneCall({
				      phoneNumber: '**********'
				    })
					}
				})
				// #endif
			}
		}
	};
</script>
<style>

</style>
