<template>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      simple
      :isBack="true"
      :hideMarchContent="false"
    >
      <block slot="backText">返回</block>
      <!-- <block slot="content">店铺列表</block> -->
      <blok slot="marchContent">
        <!-- <view
          class="cu-bar search fixed shop-search"
          :class="'bg-'+theme.backgroundColor"
        > -->
        <view
          class="search-form round"
          style="height:100%; line-height:100%; margin-right: 20rpx;border: 1rpx solid #CDA488;"
        >
          <text class="cuIcon-search"></text>
          <navigator
            class="response"
            hover-class="none"
            :url="`/pages/base/search/index?shopId=${ parameter.shopId}&shopType=1`"
          >
            <input
              type="text"
              placeholder="请输入关键字"
              :value="parameter.keyword"
            ></input>
          </navigator>
          <!-- </view> -->
        </view>
      </blok>
    </cu-custom>

    <!-- <view
      class="cu-bar search fixed shop-search"
      :class="'bg-'+theme.backgroundColor"
    >
      <view class="search-form round">
        <text class="cuIcon-search"></text>
        <navigator
          class="response"
          hover-class="none"
          :url="`/pages/base/search/index?shopId=${ parameter.shopId}&shopType=1`"
        >
          <input
            type="text"
            placeholder="请输入关键字"
            :value="parameter.keyword"
          ></input>
        </navigator>
      </view>
    </view> -->

    <view class="justify-center bg-white solid-bottom ">
      <view
        v-if="parameter.keyword"
        class="bg-white nav"
      >
        <view class="flex text-center">
          <navigator
            class="cu-item flex-sub"
            hover-class="none"
            open-type="redirect"
            :url="'/pages/goods/goods-list/index?name='+parameter.keyword"
          >
            商品
          </navigator>
          <view
            class="cu-item flex-sub cur"
            :class="'text-'+theme.themeColor"
          >
            店铺
          </view>
        </view>
      </view>
    </view>

    <view class="cu-card bg-white">
      <view
        class="bg-white radius shop-card"
        v-for="(item, index) in shopInfoList"
        :key="index"
      >
        <navigator
          class="flex padding-top-sm margin-left-sm justify-between"
          hover-class="none"
          :url="'/pages/shop/shop-detail/index?id=' + item.shopId"
        >
          <view class="round flex">
            <image
              mode="aspectFit"
              :src="item.imgUrl"
              class="round head-image"
            ></image>
            <view
              class="margin-left-sm text-sm text-bold padding-top-xs overflow-1 "
              style="width: 400rpx;"
            >{{item.name}}</view>
          </view>
          <view>
            <text
              class="round enter-store"
              :class="'bg-'+theme.themeColor"
            >进入店铺</text>
          </view>
        </navigator>

        <view class="grid margin-left-sm margin-top-xs">
          <block
            v-for="(item2, index) in item.goodsList"
            :key="index"
          >
            <navigator
              hover-class="none"
              :url="'/pages/goods/goods-detail/index?id=' + item2.id + '&source_module=' + encodeURIComponent('店铺列表')"
              class="goods-item"
            >
              <image
                class="top radius img-box"
                :src="item2.picUrls[0] |formatImg360"
              ></image>
              <text class="overflow-1 text-sm ">{{item2.name}}</text>
              <text
                v-if="item2.saleNum&&item2.saleNum>0"
                class="text-gray text-xs"
              >已售{{item2.saleNum}}</text>
              <text class="text-bold text-red flex">￥{{item2.priceDown}}</text>
              <text
                v-if="item2.estimatedPriceVo&&item2.priceDown!=item2.estimatedPriceVo.price"
                class="text-red text-xs"
                style="font-weight: 500;padding-left: 6rpx"
              >劵后价</text>
              <text
                v-if="item2.inventoryState==1"
                class="text-gray text-xs"
                style="font-weight: 500;padding-left: 6rpx"
              >已售罄
              </text>

              <text
                v-if="
                 item2.estimatedPriceVo&&item2.estimatedPriceVo.originalPrice!='0.0'&&item2.priceDown!= item2.estimatedPriceVo.originalPrice"
                class="price-original text-xs"
              >￥{{ item2.estimatedPriceVo.originalPrice }}
              </text>
            </navigator>
          </block>

          <view
            v-if="item.goodsList.length <= 0"
            class="no-goods"
          >
            <image
              src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/54d0e94c-b490-433f-98e4-09d9c27cedfd.png"
              class="no-item margin-top"
            ></image>
            <view class="text-sm text-gray text-center">店主还没有上架商品哦～</view>
          </view>
        </view>
      </view>
    </view>
    <view :class="'cu-load bg-white ' + (loadmore?'loading':'over')"></view>
  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'
import { senTrack } from "@/public/js_sdk/sensors/utils.js"
export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      page: {
        // searchCount: false,
        current: 1,
        size: 10,
        // ascs: '',
        // //升序字段
        // descs: ''
      },

      parameter: {},
      loadmore: true,
      shopInfoList: [],
      //有数统计使用
      page_title: '店铺列表',
	  searchType: ''
    };
  },

  //收藏
  userCollect () {
    let shopInfo = this.shopInfo;
    let collectId = shopInfo.collectId;

    if (collectId) {
      api.userCollectDel(collectId).then(res => {
        uni.showToast({
          title: '已取消收藏',
          icon: 'success',
          duration: 2000
        });
        shopInfo.collectId = null;
        shopInfo.collectCount = shopInfo.collectCount - 1
        this.shopInfo = shopInfo;
      });
    } else {
      api.userCollectAdd({
        type: '2',
        relationIds: [shopInfo.id]
      }).then(res => {
        uni.showToast({
          title: '收藏成功',
          icon: 'success',
          duration: 2000
        });
        shopInfo.collectId = res.data[0].id;
        shopInfo.collectCount = shopInfo.collectCount + 1
        this.shopInfo = shopInfo;
      });
    }
  },

  components: {

  },
  props: {},

  onShow () { },

  onLoad: function (options) {
    if (options.name) {
      this.parameter.keyword = decodeURI(decodeURI(options.name));
    }
	
	if (options.searchType) {
		this.searchType = options.searchType;
	}
	
    app.initPage().then(res => {
      this.shopInfoPage();
    });
  },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.shopInfoPage();
    }
  },

  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框

    uni.hideNavigationBarLoading(); // 停止下拉动作

    uni.stopPullDownRefresh();
  },

  methods: {
    shopInfoPage () {
      // api.shopInfoPageWithSpu(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
      //   let shopInfoList = res.data.records;
      //   console.log("店铺搜索结果==>", shopInfoList);
      //   this.shopInfoList = [...this.shopInfoList, ...shopInfoList];
      //   console.log("2222222", this.shopInfoList);

      //   if (shopInfoList.length < this.page.size) {
      //     this.loadmore = false;
      //   }
      // });
      //接口测试数据
      // let data = {
      //   current: 1,
      //   keyword: this.parameter,
      //   size: 10
      // }
      api.shopInfoGetShopList(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
        let shopInfoList = res.data;
        console.log("店铺搜索结果==>", shopInfoList);
        this.shopInfoList = [...this.shopInfoList, ...shopInfoList];
        if (shopInfoList.length < this.page.size) {
          this.loadmore = false;
        }
      })
    },

    refresh () {
      this.loadmore = true;
      this.shopInfoList = [];
      this.page.current = 1;
      this.shopInfoPage();
    }
  },
  
  
  onUnload() {
  	// 给神策发送 搜索结果页离开的事件
  	if (this.searchType) {
  		senTrack('SearchResultPageLeave', {
  			search_keyword: this.parameter.name,
  			keyword_type: this.searchType
  		})
  	}
  }
  
  
};
</script>

<style>
.shop-search {
  box-shadow: unset !important;
  top: unset !important;
  min-height: 100rpx !important;
}

.shop-nav {
  top: unset !important;
  margin-top: 100rpx;
}

.shop-card {
  width: 94%;
  height: 460rpx;
  margin: 20rpx auto;
  box-shadow: 0px 5px 10px #e5e5e5;
}

.head-image {
  width: 110rpx;
  height: 60rpx;
}

.enter-store {
  font-size: 24rpx;
  padding: 10rpx 20rpx 10rpx 20rpx;
  line-height: 50rpx;
}

.img-box {
  height: 200rpx;
}

.goods-item {
  padding: 10rpx;
  width: 220rpx;
  height: 220rpx;
}

.goods-name {
  height: 65rpx;
}

.no-goods {
  margin: 30rpx auto;
}

.no-item {
  width: 400rpx;
  height: 200rpx;
}
</style>
