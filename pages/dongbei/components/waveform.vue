<template>
	<view class="waveform-container">
		<canvas canvas-id="waveCanvas" id="waveCanvas" class="wave-canvas"></canvas>
	</view>
</template>

<script>
	export default {
		props: {
			audioSrc: String,
			isPlaying: Boolean
		},

		data() {
			return {
				audioContext: null,
				analyser: null,
				dataArray: null,
				canvasWidth: 300,
				canvasHeight: 100,
				animationId: null
			}
		},

		watch: {
			isPlaying(newValue, oldValue) {
				if (newValue) {
					this.drawWaveform();
				} else {
					this.resetWaveform();
					// cancelAnimationFrame(this.animationId)
				}
			}
		},

		created() {
			this.initCanvas()
			this.initAudio()
		},

		beforeDestroy() {
			cancelAnimationFrame(this.animationId)
			if (this.audioContext) {
				this.audioContext.destroy()
			}
		},
		methods: {
			initCanvas() {
				const sysInfo = uni.getSystemInfoSync()
				this.canvasWidth = sysInfo.windowWidth * 0.9
				this.ctx = uni.createCanvasContext('waveCanvas', this)
			},

			initAudio() {
				this.audioContext = uni.createInnerAudioContext()
				this.audioContext.src = this.audioSrc

				// H5端使用Web Audio API
				if (process.env.VUE_APP_PLATFORM === 'h5') {
					const audioCtx = new(window.AudioContext || window.webkitAudioContext)()
					const source = audioCtx.createMediaElementSource(this.audioContext._audio)
					this.analyser = audioCtx.createAnalyser()
					this.analyser.fftSize = 256
					source.connect(this.analyser)
					this.analyser.connect(audioCtx.destination)
					this.dataArray = new Uint8Array(this.analyser.frequencyBinCount)
				}

				// this.audioContext.onPlay(() => {
				// 	console.log("========onPlay=====");
				// 	this.drawWaveform()
				// })

				// this.audioContext.onEnded(() => {
				// 	console.log("========onEnded=====");
				// 	cancelAnimationFrame(this.animationId)
				// })
			},

			drawWaveform() {
				// 获取音频数据
				if (process.env.VUE_APP_PLATFORM === 'h5' && this.analyser) {
					this.analyser.getByteFrequencyData(this.dataArray)
				} else {
					// 非H5端模拟数据
					this.dataArray = new Uint8Array(64)
					for (let i = 0; i < this.dataArray.length; i++) {
						this.dataArray[i] = Math.random() * 100 + 30
					}
				}

				// 清除画布
				this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

				// 绘制波形
				const barWidth = (this.canvasWidth / this.dataArray.length) * 1.2
				let x = 0

				for (let i = 0; i < this.dataArray.length; i++) {
					const barHeight = this.dataArray[i] / 2

					// 动态渐变色
					const hue = (i * 3 + Date.now() / 50) % 360
					const color = this.hslaToRgba(hue, 100, 60, 0.7)
					this.ctx.setFillStyle(color)

					// 绘制柱状图
					this.ctx.fillRect(
						x,
						this.canvasHeight / 2 - barHeight / 2,
						barWidth,
						barHeight
					)

					// 添加圆角（上半部分）
					this.ctx.beginPath()
					this.ctx.arc(
						x + barWidth / 2,
						this.canvasHeight / 2 - barHeight / 2,
						barWidth / 2,
						Math.PI,
						0,
						false
					)
					this.ctx.fill()

					// 添加圆角（下半部分）
					this.ctx.beginPath()
					this.ctx.arc(
						x + barWidth / 2,
						this.canvasHeight / 2 + barHeight / 2,
						barWidth / 2,
						0,
						Math.PI,
						false
					)
					this.ctx.fill()

					x += barWidth + 1
				}

				this.ctx.draw(false) // 注意：canvas draw 是异步的

				// 使用 setTimeout 替代 requestAnimationFrame
				this.animationId = setTimeout(() => {
					this.drawWaveform()
				}, 1000 / 60) // 每帧间隔约 16ms
			},

			// drawWaveform() {
			// 	// 获取音频数据
			// 	if (process.env.VUE_APP_PLATFORM === 'h5' && this.analyser) {
			// 		this.analyser.getByteFrequencyData(this.dataArray)
			// 	} else {
			// 		// 非H5端模拟数据
			// 		this.dataArray = new Uint8Array(64)
			// 		for (let i = 0; i < this.dataArray.length; i++) {
			// 			this.dataArray[i] = Math.random() * 100 + 30
			// 		}
			// 	}

			// 	// 清除画布
			// 	this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

			// 	// 绘制波形
			// 	const barWidth = (this.canvasWidth / this.dataArray.length) * 1.2
			// 	let x = 0

			// 	for (let i = 0; i < this.dataArray.length; i++) {
			// 		const barHeight = this.dataArray[i] / 2

			// 		// 动态渐变色
			// 		const hue = (i * 3 + Date.now() / 50) % 360
			// 		// this.ctx.setFillStyle(`hsla(${hue}, 100%, 60%, 0.7)`)

			//                  const color = this.hslaToRgba(hue, 100, 60, 0.7); // h=动态色相, s=100%, l=60%, a=0.7
			//                  this.ctx.setFillStyle(color);

			// 		// 绘制波形柱
			// 		this.ctx.fillRect(
			// 			x,
			// 			this.canvasHeight / 2 - barHeight / 2,
			// 			barWidth,
			// 			barHeight
			// 		)

			// 		// 添加圆角
			// 		this.ctx.beginPath()
			// 		this.ctx.arc(
			// 			x + barWidth / 2,
			// 			this.canvasHeight / 2 - barHeight / 2,
			// 			barWidth / 2,
			// 			Math.PI,
			// 			0,
			// 			false
			// 		)
			// 		this.ctx.arc(
			// 			x + barWidth / 2,
			// 			this.canvasHeight / 2 + barHeight / 2,
			// 			barWidth / 2,
			// 			0,
			// 			Math.PI,
			// 			false
			// 		)
			// 		this.ctx.fill()

			// 		x += barWidth + 1
			// 	}

			// 	this.ctx.draw()

			// 	// 继续动画
			// 	this.animationId = requestAnimationFrame(this.drawWaveform.bind(this))
			// },

			resetWaveform() {
				clearTimeout(this.animationId)
				this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
				this.ctx.draw()
			},

			// resetWaveform() {
			// 	cancelAnimationFrame(this.animationId)
			// 	this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
			// 	this.ctx.draw()
			// },

			hslaToRgba(h, s, l, a) {
				// h: 0-360, s: 0-100, l: 0-100, a: 0-1

				s /= 100;
				l /= 100;

				const k = n => (n + h / 30) % 12;
				const aCalc = s * Math.min(l, 1 - l);
				const f = n =>
					l - aCalc * Math.max(-1, Math.min(k(n) - 3, Math.min(9 - k(n), 1)));

				const r = Math.round(255 * f(0));
				const g = Math.round(255 * f(8));
				const b = Math.round(255 * f(4));

				return `rgba(${r}, ${g}, ${b}, ${a})`;
			}
		}
	}
</script>

<style>
	.waveform-container {
		width: 100%;
		height: 120px;
		margin: 20px 0;
		/* background-color: #1a1a2e; */
		border-radius: 10px;
		overflow: hidden;
	}

	.wave-canvas {
		width: 100%;
		height: 100%;
	}
</style>