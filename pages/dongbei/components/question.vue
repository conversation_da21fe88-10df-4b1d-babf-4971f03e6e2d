<template>
	<view class="container">
		<!-- 当前题目 -->
		<view v-if="currentQuestion" class="question-box">
			<!-- 播放按钮 -->
			<button @click="playAudio">播放听力</button>

			<!-- 波形动画 -->
			<view v-if="isPlaying" class="waveform">
				<view v-for="(bar, index) in waveBars" :key="index" class="wave-bar"
					:style="{ height: bar.height + 'px', backgroundColor: bar.color }"></view>
			</view>

			<!-- 波形动画组件 -->
			<waveform :audio-src="currentQuestion.audioUrl" ref="waveform" :is-playing="isPlaying" />

			<!-- 题目 -->
			<view class="question">{{ currentQuestion.question }}</view>

			<!-- 选项 -->
			<view class="options">
				<view v-for="(option, index) in currentQuestion.options" :key="index" :class="[
            'option',
            selected !== null && selected === index ? 'selected' : '',
            isSubmitted && currentQuestion.correctIndex === index ? 'correct' : '',
            isSubmitted && selected === index && selected !== currentQuestion.correctIndex ? 'wrong' : ''
          ]" @click="selectOption(index)">
					{{ option.label }}. {{ option.text }}
				</view>
			</view>
		</view>

		<!-- 完成页面 -->
		<view v-else class="complete">
			<text>答题结束！你的得分：{{ score }} 分</text>
			<text class="badge">获得称号：{{ badge }}</text>
		</view>
	</view>
</template>

<script>
	import waveform from './waveform.vue';
	export default {
		components: {
			waveform
		},
		data() {
			return {
				questions: [], // 所有题目数据
				currentIndex: 0, // 当前题目索引
				currentQuestion: null, // 当前题目对象
				selected: null, // 用户选择的索引
				isSubmitted: false, // 是否已提交答案
				score: 0, // 总分
				badge: '', // 获得的称号
				audioContext: null, // 音频播放器
				correctSound: null, // 正确音效
				wrongSound: null, // 错误音效
				isPlaying: false, // 是否正在播放音频
				waveBars: Array(8).fill({
					height: 10,
					color: '#999'
				}), // 波形条数组
				waveInterval: null // 动画定时器
			};
		},

		created() {
			this.audioContext = uni.createInnerAudioContext();
			this.correctSound = uni.createInnerAudioContext();
			this.correctSound.src = 'https://img.songlei.com/live/dongbei/correct.wav';
			this.wrongSound = uni.createInnerAudioContext();
			this.wrongSound.src = 'https://img.songlei.com/live/dongbei/wrong.wav';
			this.fetchQuestions(); // 加载题目
		},
		methods: {
			fetchQuestions() {
				// 模拟从接口获取题目数据
				setTimeout(() => {
					this.questions = [{
							question: "What does the woman want to do?",
							options: [{
									label: "A",
									text: "Go to the park."
								},
								{
									label: "B",
									text: "Buy some food."
								},
								{
									label: "C",
									text: "Visit her friend."
								},
								{
									label: "D",
									text: "Stay at home."
								}
							],
							correctIndex: 0,
							audioUrl: "https://img.songlei.com/live/dongbei/correct.wav",
							score: 10
						},
						{
							question: "Where did they go last weekend?",
							options: [{
									label: "A",
									text: "To the beach."
								},
								{
									label: "B",
									text: "To the cinema."
								},
								{
									label: "C",
									text: "To the library."
								},
								{
									label: "D",
									text: "To the museum."
								}
							],
							correctIndex: 3,
							audioUrl: "https://img.songlei.com/live/dongbei/correct.wav",
							score: 10
						}
					];
					this.currentQuestion = this.questions[0];
				}, 500);
			},

			playAudio() {
				if (!this.currentQuestion || this.isPlaying) return;
                this.$refs.waveform.resetWaveform();
				this.isPlaying = true;
				this.animateWave();

				this.audioContext.src = this.currentQuestion.audioUrl;
				this.audioContext.play();

				this.audioContext.onEnded(() => {
					this.isPlaying = false;
					clearInterval(this.waveInterval);
					this.waveBars = this.waveBars.map(bar => ({
						...bar,
						height: 10,
						color: '#999'
					}));
				});
			},

			animateWave() {
				const colors = ['#55aaff', '#77bbff', '#99ccff'];
				this.waveInterval = setInterval(() => {
					this.waveBars = this.waveBars.map((_, i) => ({
						height: Math.floor(Math.random() * 40) + 10,
						color: colors[i % colors.length]
					}));
				}, 200);
			},

			selectOption(index) {
				if (this.isSubmitted) return;
				this.selected = index;
				this.submitAnswer(); // 立即提交
			},

			submitAnswer() {
				this.isSubmitted = true;

				if (this.selected === this.currentQuestion.correctIndex) {
					this.score += this.currentQuestion.score;
					this.correctSound.play();
				} else {
					this.wrongSound.play();
				}

				setTimeout(() => {
					this.nextQuestion();
				}, 1000); // 延迟1秒后切换下一题
			},

			nextQuestion() {
				this.$refs.waveform.resetWaveform();
				this.currentIndex++;
				if (this.currentIndex < this.questions.length) {
					this.currentQuestion = this.questions[this.currentIndex];
					this.selected = null;
					this.isSubmitted = false;
				} else {
					this.currentQuestion = null;
					this.calculateBadge(); // 计算称号
				}
			},

			calculateBadge() {
				if (this.score >= 20) {
					this.badge = "王者";
				} else if (this.score >= 15) {
					this.badge = "黄金";
				} else if (this.score >= 10) {
					this.badge = "白银";
				} else {
					this.badge = "青铜";
				}
			}
		},

		beforeDestroy() {
			this.audioContext.destroy();
			this.correctSound.destroy();
			this.wrongSound.destroy();
			if (this.waveInterval) clearInterval(this.waveInterval);
		}
	};
</script>

<style scoped>
	.container {
		padding: 20px;
	}

	.question-box {
		margin-bottom: 40px;
	}

	.question {
		font-size: 18px;
		margin: 20px 0;
	}

	.options {
		margin-top: 10px;
	}

	.option {
		border: 1px solid #ccc;
		padding: 15px;
		margin-bottom: 10px;
		border-radius: 5px;
	}

	.option.selected {
		background-color: #d0eaff;
		border-color: #007AFF;
	}

	.option.correct {
		background-color: #c8f7c5;
		border-color: green;
	}

	.option.wrong {
		background-color: #ffe6e6;
		border-color: red;
	}

	.complete {
		font-size: 18px;
		text-align: center;
		margin-top: 50px;
		line-height: 2;
	}

	.badge {
		display: block;
		font-weight: bold;
		font-size: 20px;
		color: gold;
		margin-top: 10px;
	}

	/* 音频波形动画 */
	.waveform {
		display: flex;
		justify-content: center;
		align-items: flex-end;
		height: 60px;
		margin: 20px 0;
	}

	.wave-bar {
		width: 6px;
		margin: 0 2px;
		border-radius: 2px;
		transition: height 0.2s ease;
	}
</style>