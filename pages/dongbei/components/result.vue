<template>
  <view class="container">
    <view class="result-card">
      <text class="score">你的得分: {{score}}</text>
      
      <view class="badge-container">
        <text class="badge" :class="getBadgeClass(score)">{{getBadgeText(score)}}</text>
      </view>
      
      <view class="feedback">
        <text>{{getFeedback(score)}}</text>
      </view>
      
      <button class="restart-btn" @click="restart">再来一次</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      score: 90
    }
  },
  // onLoad(options) {
  //   this.score = parseInt(options.score) || 0
  // },
  methods: {
    getBadgeText(score) {
      if (score >= 90) return '英语大师'
      if (score >= 70) return '听力达人'
      if (score >= 50) return '进步之星'
      return '继续努力'
    },
    
    getBadgeClass(score) {
      if (score >= 90) return 'gold'
      if (score >= 70) return 'silver'
      if (score >= 50) return 'bronze'
      return 'normal'
    },
    
    getFeedback(score) {
      if (score >= 90) return '太棒了！你的英语听力水平非常出色！'
      if (score >= 70) return '做得不错！继续保持练习会更上一层楼！'
      if (score >= 50) return '还不错，但还有提升空间，加油！'
      return '听力需要更多练习，相信下次会更好！'
    },
    
    restart() {
      uni.redirectTo({
        url: '/pages/question/question'
      })
    }
  }
}
</script>

<style>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  padding: 20px;
}

.result-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  text-align: center;
}

.score {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  display: block;
}

.badge {
  font-size: 22px;
  padding: 10px 20px;
  border-radius: 25px;
  display: inline-block;
  margin: 15px 0;
}

.badge.gold {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
}

.badge.silver {
  background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
  color: #333;
}

.badge.bronze {
  background: linear-gradient(135deg, #CD7F32, #A0522D);
  color: white;
}

.badge.normal {
  background: #f5f5f5;
  color: #333;
}

.feedback {
  margin: 20px 0;
  font-size: 16px;
  line-height: 1.5;
}

.restart-btn {
  margin-top: 30px;
  background: linear-gradient(90deg, #4CAF50, #2E7D32);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 25px;
}
</style>