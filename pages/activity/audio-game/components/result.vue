<template>
  <view class="result-card">
    <view class="result-head">
      <!-- no-quiz-total -->
      <view class="quiz-total-num" :class="{ 'no-quiz-total': quizNum === 0 }">
        <view v-if="quizNum" class="num-text">{{ quizNum }}</view>
      </view>
      <!-- <image class="close-btn" :src="assets.close" @tap.stop="close" /> -->
    </view>
    <view class="prizes-section">
      <view class="prizes-title-container">
        <image
          v-if="allPrizes.length > 0"
          class="tips"
          :src="assets.restulTitle"
        ></image>
        <image
          class="my-prizes-enter"
          :src="assets.myPrizes"
          @tap.stop="myPrizesEnter"
        ></image>
      </view>
      <scroll-view
        v-if="allPrizes.length > 0"
        class="prize-item-wrapper"
        scroll-y="true"
        @touchmove.stop
      >
        <view
          v-for="(prize, index) in allPrizes"
          :key="index"
          class="prize-item"
        >
          <image :src="prize.prizeImg" class="prize-image" mode="aspectFit" />
          <view class="prize-content">
            <view class="prize-name">{{ prize.prizeName }}</view>
            <image
              class="go-use"
              :src="prize.prizeType == 2 ? assets.goLotteryGray : assets.goUse"
              @tap.stop="goUse(prize)"
            />
          </view>
        </view>
      </scroll-view>
      <view
        v-else
        class="no-prize"
        :class="{ 'exhausted-icon': quizNum > 0 }"
      ></view>
      <view class="btn-continue" @tap.stop="continueQuiz"
        >剩{{ remainingAnswersNum || 0 }}次</view
      >
      <!-- #ifdef MP -->
      <button class="btn-share" open-type="share">分享好友</button>
      <!-- #endif -->
      <!-- #ifdef APP-PLUS -->
      <button class="btn-share" @tap="shareWxByApp">分享微信好友</button>
      <!-- #endif -->
    </view>
  </view>
</template>

<script>
import { submitDongBeiHuaAnswer } from "./api/index.js";
import { senTrack } from "@/public/js_sdk/sensors/utils.js";
import __config from "@/config/env";
export default {
  props: ["quizNum", "participationNum", "activityInfo"],
  components: {},
  data() {
    return {
      score: this.testScore,
      allPrizes: [],
      assets: {
        close: this.$imgUrl("live/dbh/dbh-cosle.png"),
        continue: this.$imgUrl("live/dbh/btn_continue.png"), // 继续按钮图标/背景图
        share: this.$imgUrl("live/dbh/btn_share.png"), // 分享按钮图标/背景图
        goUse: this.$imgUrl("live/dbh/btn_go_use.png"), // 去使用按钮图标/背景图
        goLotteryGray: this.$imgUrl("live/dbh/go-lottery-gray.png"),
        goLottery: this.$imgUrl("live/dbh/go-lottery.png"),
        myPrizes: this.$imgUrl("live/dbh/prize_my.png"), // 我的奖品入口/展示图
        listBg: this.$imgUrl("live/dbh/prize_list_bg.png"), // 奖品列表背景图
        regretTip: this.$imgUrl("live/dbh/prize_regret_tip.png"), // 遗憾提示图（如未中奖）
        quizTotalNum: this.$imgUrl("live/dbh/quiz_total_num.png"),
        restulTitle: this.$imgUrl("live/dbh/restul_title.png"),
      },
      remainingAnswersNum: "",

      shareObjTemp: {},

      showModal: false,
      isInitShare: false,
    };
  },

  mounted() {
    submitDongBeiHuaAnswer({
      id: this.activityInfo.id,
      winsNum: this.quizNum,
    }).then(({ data }) => {
      if (data) {
        const { winUserPrizes, remainingAnswersNum } = data;
        this.remainingAnswersNum = remainingAnswersNum;
        this.allPrizes = winUserPrizes ?? [];
        const index = this.allPrizes.findIndex((item) => item.prizeType == 2);
        if (index !== -1) {
          setTimeout(() => {
            this.$emit("lottery", this.allPrizes[index].relatedActivityId);
          }, 300);
        }
        this.$emit("getPrizes", this.allPrizes);
      }
    });
  },

  methods: {
    // 关闭弹窗
    close() {
      this.$emit("close");
    },

    continueQuiz() {
      if (this.remainingAnswersNum < 1) {
        uni.showToast({
          title: "没有答题次数了",
          icon: "error",
        });
        return;
      }
      this.$emit("restart");
    },

    myPrizesEnter() {
      this.$emit("myPrizes");
    },
    goUse(val) {
      const { prizeType, couponNo, prizeValue } = val;
      let url;
      switch (+prizeType) {
        case 3:
          url = `/pages/goods/goods-list/index?couponId=${prizeValue}`;
          break;
        case 4:
          url = `/pages/signrecord/signrecord-info/index`;
          break;
        case 6:
          url = `/pages/coupon/coupon-offline-detail-plus/index?id=${couponNo}`;
          break;
        case 2:
          break;
      }
      uni.navigateTo({
        url,
      });
    },

    // #ifdef APP-PLUS
    shareWxByApp() {
      // #ifdef APP-PLUS
      this.shareObjTemp.imgUrl = this.activityInfo.sharePicUrl;
      this.shareObjTemp.url = `pages/home/<USER>
      this.shareObjTemp.title = "松鼠商城";
      this.shareObjTemp.desc = "听声答题";
      // #endif
      uni.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 5, //分享形式 5:小程序
        href: this.shareObjTemp.url,
        title: this.shareObjTemp.title,
        summary: this.shareObjTemp.desc,
        imageUrl: this.shareObjTemp.imgUrl,
        miniProgram: {
          id: __config.originAppid,
          path: this.shareObjTemp.url,
          type: 0,
          webUrl: "https://shopapi.songlei.com/slshop-h5/ma/",
        },
        success: function (res) {},
        fail: function (err) {
          uni.showModal({
            title: "提示",
            content: "正在开发中",
            showCancel: false,
          });
        },
      });
      senTrack("ActivityPageShareOrCollect", {
        ...this.activityInfo.trackParams,
        action_type: "分享",
        activity_id: this.activityInfo.id,
      });
    },
    // #endif
  },
};
</script>

<style scoped lang="scss">
.result-card {
  width: calc(100vw - 30rpx);
  margin-left: 15rpx;
}

.result-head {
  position: relative;
  width: 100%;
  .quiz-total-num {
    width: 450rpx;
    height: 68rpx;
    margin-left: calc((100% - 450rpx) / 2);
    background-image: url("https://img.songlei.com/live/dbh/quiz_total_num.png");
    background-size: 450rpx 68rpx;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    &.no-quiz-total {
      background-image: url("https://img.songlei.com/live/dbh/prize_regret_tip.png");
      height: 124rpx !important;
      background-size: 440rpx 124rpx !important;
      width: 440rpx;
      margin-left: calc((100% - 440rpx) / 2);
    }
    .num-text {
      position: absolute;
      line-height: 68rpx;
      right: 82rpx;
      font-size: 68rpx;
      color: yellow;
      font-weight: bold;
    }
  }
  .close-btn {
    position: absolute;
    width: 50rpx;
    height: 50rpx;
    right: 40rpx;
    top: -20rpx;
  }
}

.prizes-section {
  margin-top: 30rpx;
  padding-top: 1rpx;
  width: 720rpx;
  height: 900rpx;
  background-image: url("https://img.songlei.com/live/dbh/prize_list_bg.png");
  background-size: 720rpx 900rpx;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  .prizes-title-container {
    margin-top: 27rpx;
    margin-left: 132rpx;
    margin-right: 58rpx;
    width: calc(100% - 132rpx - 58rpx);
    display: flex;
    align-items: center;
    position: relative;
    height: 76rpx;
    .tips {
      width: 300rpx;
      height: 52rpx;
    }
    .my-prizes-enter {
      position: absolute;
      height: 76rpx;
      width: 200rpx;
      right: 0;
    }
  }

  .btn-continue {
    position: absolute;
    width: 310rpx;
    height: 146rpx;
    background-image: url("https://img.songlei.com/live/dbh/btn_continue.png");
    background-size: 310rpx 146rpx;
    background-position: center;
    background-repeat: no-repeat;
    bottom: 75rpx;
    left: 27rpx;
    text-align: right;
    line-height: 146rpx;
    font-size: 28rpx;
    padding-right: 32rpx;
  }

  .btn-share {
    position: absolute;
    width: 310rpx;
    height: 146rpx;
    background-image: url("https://img.songlei.com/live/dbh/btn_share.png");
    background-size: 310rpx 146rpx;
    background-position: center;
    background-repeat: no-repeat;
    bottom: 75rpx;
    right: 58rpx;
    text-align: center;
    line-height: 146rpx;
    font-size: 40rpx;
    color: #ffffff;
  }

  button {
    background-color: transparent;
    border: none;
    outline: none;
    position: relative;
  }

  button::after {
    border: none;
  }

  .no-prize {
    position: absolute;
    top: 70rpx;
    margin-left: calc((100% - 468rpx) / 2);
    width: 468rpx;
    height: 497rpx;
    background-image: url("https://img.songlei.com/live/dbh/prize_no_icon.png");
    background-size: 468rpx 497rpx;
    background-position: center;
    background-repeat: no-repeat;
    &.exhausted-icon {
      background-image: url("https://img.songlei.com/live/dbh/prize_exhausted_icon.png");
    }
  }
}

.prize-item-wrapper {
  width: calc(100% - 115rpx - 115rpx);
  height: 400rpx;
  overflow-y: scroll;
  margin-left: 115rpx;
}

.prize-item {
  width: 100%;
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #efefef;
  .prize-image {
    width: 160rpx;
    height: 160rpx;
  }

  .prize-content {
    flex: 1;
    height: 160rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    .prize-name {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
    }

    .go-use {
      margin-top: auto;
      width: 220rpx;
      height: 76rpx;
    }
  }
}
</style>
