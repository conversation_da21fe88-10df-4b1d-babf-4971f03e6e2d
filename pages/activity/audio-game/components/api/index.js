import { requestApi as request } from "@/utils/api.js";

// 首页东北话弹窗
export function getDongbeiPop() {
  return request({
    url: "/mallmarket/audioquizactivity/game/getList",
    method: "get",
  });
}

// 获取东北话题目
export function getDongbeiQuestions(data) {
  return request({
    url: "/mallmarket/dongbeihua/game/questions",
    method: "get",
    showLoading: false,
    data,
  });
}

export function submitDongBeiHuaAnswer(data) {
  return request({
    url: "/mallmarket/audioquizactivity/game/lotteries",
    method: "get",
    data,
  });
}
export function shareEnterDongBeiHua(data) {
  return request({
    url: "/mallmarket/audioquizactivity/game/getOne",
    method: "get",
    data,
  });
}

export function myPrizeList(actType) {
  return request({
    url: "/mallmarket/actuserprize/myPrize",
    method: "get",
    data: {
      current: 1,
      size: 50,
      actType,
    },
  });
}

// 领取奖品
export function getPrize(data) {
  return request({
    url: "/mallmarket/actuserprize/getPrize/" + data,
    method: "put",
  });
}

// 拆盲盒
export function openBlindBoxApi(data) {
  return request({
    url: "/mallmarket/water/blindBox",
    method: "get",
    data,
  });
}
