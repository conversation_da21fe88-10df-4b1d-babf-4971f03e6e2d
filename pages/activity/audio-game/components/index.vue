<template>
  <view class="cu-modal modal show" catchtouchmove="true">
    <view class="content" :class="{ 'content-top': currentStep !== 'testing' }">
      <!-- 开始页面 -->
      <view v-if="currentStep === 'start'" class="quiz-page">
        <image class="bg-image" :src="images.bg" />
        <image class="logo" :src="images.logo" />
        <image class="title" :src="images.title" />
        <image class="pen" :src="images.pen" />
        <image class="start-btn" :src="images.startBtn" @tap.stop="startTest" />
        <image
          class="my-prize-btn"
          style="bottom: 60rpx"
          :src="images.myPrizeBtn"
          @tap.stop="showMyPrizeList"
        ></image>
      </view>
      <!-- 答题页面 -->
      <view v-if="currentStep === 'testing'" class="quiz-page">
        <image
          class="bg-image"
          style="height: 911rpx"
          :src="quizImageAssets.background.quiz"
        />
        <image class="character" :src="getAnswerStatusImage()" />
        <image class="title" :src="images.title" />
        <image class="pen" :src="images.pen" />
        <image class="close-btn" :src="images.close" @tap.stop="close" />
        <view class="quiz-container">
          <view class="play-bar-container">
            <view class="play-bar" :class="{ 'play-bar-stop': !isPlaying }" />
            <view class="play-btn-wrapper">
              <image
                class="play-icon"
                :src="quizImageAssets.feedback.playBtn"
                @tap.stop="playQuestionAudio"
              />
              <text>重新播放</text>
            </view>
          </view>
          <view class="quiz-sub-title">
            {{ currentQuestion.name }}
          </view>
          <view
            v-for="(option, index) in currentQuestion.options"
            :key="index"
            class="quiz-item"
            :class="[`option-${index}`, getOptionClass(index)]"
            @tap="selectOption(index)"
          >
            {{ option }}
            <view
              v-if="
                selectedOption === index ||
                (showCorrectAnswer && index === currentQuestion.correctOption)
              "
              class="result-icon"
            ></view>
          </view>
        </view>
      </view>
      <result
        v-if="currentStep === 'result'"
        :quizNum="quizTotalNum"
        :activityInfo="activityInfo"
        @close="close"
        @restart="restart"
        @lottery="lotteryEvent"
        @getPrizes="getPrizes"
        @myPrizes="showPrize = true"
      />
      <view v-if="currentStep === 'end'" class="quiz-page">
        <image class="activity-end" :src="getActivityStatusImage()" />
        <image
          class="my-prize-btn"
          :src="images.myPrizeBtn"
          @tap.stop="showPrize = true"
        ></image>
      </view>
    </view>

    <view v-if="showPrize" class="prize-msk">
      <view class="quiz-page" style="margin-top: 200rpx">
        <image class="bg-image" :src="images.bg" />
        <image class="logo" :src="images.logo" />
        <image class="title" :src="images.title" />
        <image class="pen" :src="images.pen" />
        <image
          class="close-btn"
          :src="images.close"
          @tap.stop="showPrize = false"
        />
        <view class="prize-list-border">
          <scroll-view
            class="prize-item-scroll"
            scroll-y="true"
            @touchmove.stop
          >
            <view
              v-for="(prize, index) in prizes"
              :key="index"
              class="prize-item"
            >
              <image
                :src="prize.prizeImg"
                class="prize-image"
                mode="aspectFit"
              />
              <view class="prize-content">
                <view class="prize-name">{{ prize.prizeName }}</view>
                <view class="time">{{ prize.drawTime }}</view>
                <view
                  class="use-btn-container"
                  :class="{
                    huang: prize.btnUrl === 'huang',
                    hui: prize.btnUrl === 'hui',
                  }"
                  @click="handlePrize(prize)"
                >
                  {{ prize.btnVal }}
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <view v-show="showContinueDialog" class="continue-dialog">
      <view class="border-continue">
        <view>
          本题正确答案是 “{{ optionMap[currentQuestion.correctOption] }}”</view
        >
        <image :src="images.continueQuizBtn" @click="clickContinue" />
      </view>
    </view>

    <pop-lottery
      v-if="showPopLottery"
      :lotteryId="lotteryId"
      :isPop="true"
      :showPrizeListProp="false"
      @close="showPopLottery = false"
    />
  </view>
</template>

<script>
import result from "./result.vue";
import PopLottery from "@/components/lottery/pop-lottery.vue";
import { senTrack } from "@/public/js_sdk/sensors/utils.js";
import {
  getDongbeiPop,
  shareEnterDongBeiHua,
  myPrizeList,
  openBlindBoxApi,
  getPrize,
} from "./api/index.js";

const app = getApp();
// 步骤枚举
const STEP = {
  START: "start",
  TESTING: "testing",
  RESULT: "result",
  END: "end",
};
// 活动状态枚举
const ACTIVITY_STATUS = {
  INIT: -1,
  NOT_STARTED: 0, // 活动尚未开始
  TODAY_ENDED: 1, // 今日活动已结束
  ACTIVITY_ENDED: 2, // 活动已经结束
  NO_ATTEMPTS: 3, // 游戏次数已用完
  NO_ACTIVITY: 4, // 暂无活动
};

export default {
  name: "DongbeiDialog",
  components: { result, PopLottery },
  props: {
    value: {
      type: [String, Object, Array],
      default: () => null,
    },
  },

  data() {
    return {
      currentStep: STEP.START,
      activityStatus: ACTIVITY_STATUS.INIT,
      questions: [],
      currentIndex: 0,
      currentQuestion: {},
      selectedOption: null,
      isSubmitted: false,
      score: 0,
      showCorrectAnswer: false, // 新增：是否显示正确答案
      // [修改] audioContext 初始化为 null，不再持有长期实例
      audioContext: null,
      // [删除] feedbackAudio 也可以直接删除，因为我们将使用局部变量
      // feedbackAudio: null,
      isPlaying: false,
      waveTimer: null,
      localValue: null, // 本地副本，避免直接修改 prop
      images: {
        bg: this.$imgUrl("live/dbh/dbh-main-bg.png"),
        pen: this.$imgUrl("live/dbh/pencil.gif"),
        logo: this.$imgUrl("live/dbh/squirrel.gif"),
        startBtn: this.$imgUrl("live/dbh/start-btn.gif"),
        close: this.$imgUrl("live/dbh/dbh-cosle.png"),
        title: this.$imgUrl("live/dbh/listening.gif"),
        share: this.$imgUrl("live/dbh/btn_share.png"), // 分享按钮图标/背景图
        continueQuizBtn: this.$imgUrl("live/dbh/continue-quiz.png"),

        goUse: this.$imgUrl("live/dbh/btn_go_use.png"), // 去使用按钮图标/背景图
        goLotteryGray: this.$imgUrl("live/dbh/go-lottery-gray.png"),

        myPrizeBtn: this.$imgUrl("live/dbh/prize_my_entry.png"),
      },
      quizImageAssets: {
        // ... (内容不变，已折叠)
        background: {
          quiz: this.$imgUrl("live/dbh/quiz_bg.png"),
        },
        character: {
          correct: this.$imgUrl("live/dbh/quiz_character_correct.png"),
          wrong: this.$imgUrl("live/dbh/quiz_character_wrong.png"),
          waiting: this.$imgUrl("live/dbh/quiz_character_waiting.png"),
        },
        title: {
          title1: this.$imgUrl("live/dbh/quiz_title1.png"),
          title2: this.$imgUrl("live/dbh/quiz_title2.png"),
          title3: this.$imgUrl("live/dbh/quiz_title3.png"),
        },
        activity: {
          notStarted: this.$imgUrl("live/dbh/activity_not_started.png"),
          todayEnded: this.$imgUrl("live/dbh/activity_today_ended.png"),
          ended: this.$imgUrl("live/dbh/activity_ended.png"),
          timesExhausted: this.$imgUrl("live/dbh/activity_times_exhausted.png"),
          noActivity: this.$imgUrl("live/dbh/activity_no_current.png"),
        },
        feedback: {
          correct: this.$imgUrl("live/dbh/failed-music.mp3"),
          wrong: this.$imgUrl("live/dbh/success-music.mp3"),
          playBtn: this.$imgUrl("live/dbh/play_btn_icon.png"),
        },
      },
      quizTotalNum: 0,
      activityInfo: {},
      lotteryId: "",
      showPopLottery: false,
      trackParams: {
        page_name: "东北话小游戏活动页",
        page_level: "一级",
        activity_id: "",
        activity_name: "东北话小游戏",
        activity_type_first: "玩法活动",
        activity_type_second: "东北话竞猜",
      },
      startTime: 0,
      hasRequestedData: false, // 标记是否已经请求过数据
      isLogin: false,
      optionMap: { 0: "A", 1: "B", 2: "C" },
      showContinueDialog: false,
      showPrize: false,
      prizes: [],
    };
  },

  watch: {
    showPrize(val) {
      if (val) {
        this.requestMyPrizeList();
      }
    },
  },
  beforeCreate() {
    this.startTime = Date.now();
    app.globalData.tabBarHide++;
    uni.hideTabBar();
  },
  mounted() {
    const userInfo = uni.getStorageSync("user_info");
    this.isLogin = userInfo && userInfo.id > 0;
  },
  methods: {
    handleValueType() {
      if (typeof this.localValue === "string") {
        if (this.localValue === "暂无活动") {
          this.activityStatus = ACTIVITY_STATUS.NO_ACTIVITY;
        } else if (this.localValue === "游戏次数") {
          this.activityStatus = ACTIVITY_STATUS.NO_ATTEMPTS;
        } else if (this.localValue === "今日活动已结束") {
          this.activityStatus = ACTIVITY_STATUS.TODAY_ENDED;
        } else if (this.localValue === "活动已结束") {
          this.activityStatus = ACTIVITY_STATUS.ACTIVITY_ENDED;
        } else if (this.localValue === "活动尚未开始") {
          this.activityStatus = ACTIVITY_STATUS.NOT_STARTED;
        }
      } else if (
        typeof this.localValue === "object" &&
        this.localValue !== null &&
        !Array.isArray(this.localValue)
      ) {
        this.activityStatus = this.localValue.status;
        this.localValue = [{ ...this.localValue }];
      }
    },
    // [修改] 开始测试，根据标志变量控制数据请求
    async startTest() {
      if (!this.isLogin) {
        const restPath = `/pages/activity/audio-game/index?id=${this.value.shareId}&share_type=dbh`;
        uni.navigateTo({ url: "/pages/login/index?reUrl=" + restPath });
        return;
      }
      // 如果没有请求过数据，则根据shareId判断请求哪个接口
      if (!this.hasRequestedData) {
        try {
          if (this.value.shareId) {
            // 分享进入，调用分享接口
            const { data } = await shareEnterDongBeiHua({
              id: this.value.shareId,
            });
            this.localValue = data;
          } else {
            // 正常进入，调用活动接口
            const { data } = await getDongbeiPop();
            this.localValue = data;
          }
          this.hasRequestedData = true; // 标记已请求过数据
          this.handleValueType();
          // 加载题目数据
          await this.loadQuestions();
        } catch (error) {
          console.error("获取东北话活动数据失败:", error);
          this.activityStatus = ACTIVITY_STATUS.NO_ACTIVITY;
          this.currentStep = STEP.END;
          return;
        }
      }
      if (this.hasRequestedData && this.currentStep !== STEP.END) {
        this.currentStep = STEP.TESTING;
        this.playQuestionAudio();
      }
    },

    async loadQuestions() {
      if (Array.isArray(this.localValue) && this.localValue.length > 0) {
        this.activityInfo = this.localValue.shift();
        const optionMap = { A: 0, B: 1, C: 2 };
        if (this.activityInfo?.audioQuestionBankList) {
          this.questions = this.activityInfo?.audioQuestionBankList.map(
            (item) => ({
              options: [item.optionA, item.optionB, item.optionC],
              correctOption: optionMap[item.correctOption],
              audioResource: item.audioResource,
              name: item.name,
            }),
          );
          this.currentQuestion = this.questions[0];
          this.currentStep = STEP.START;
          senTrack("ActivityPageView", {
            ...this.trackParams,
            activity_id: this.activityInfo.id,
          });
        } else {
          this.currentStep = STEP.END;
        }
      } else {
        this.currentStep = STEP.END;
      }
    },

    // 播放题目音频的核心方法
    playQuestionAudio() {
      // 如果当前仍有实例存在（异常情况），先销毁，确保环境干净
      if (this.audioContext) {
        this.audioContext.destroy();
      }
      console.log("[Audio] 创建新的题目音频实例...");
      this.audioContext = uni.createInnerAudioContext();

      // 监听：当音频开始播放时，更新播放状态
      this.audioContext.onPlay(() => {
        console.log("[Audio] 题目音频开始播放");
        this.isPlaying = true;
      });

      // 监听：当音频自然播放结束后，销毁自己
      this.audioContext.onEnded(() => {
        console.log("[Audio] 题目音频自然播放结束");
        this.isPlaying = false;
      });
      // 监听：播放出错时，也要销毁自己
      this.audioContext.onError((err) => {
        console.error("[Audio] 题目音频播放错误，销毁实例。", err);
        this.stopQuestionAudio();
      });
      // 监听：准备好之后再播放
      this.audioContext.onCanplay(() => {
        console.log("[Audio] 题目音频准备就绪，即将播放。");
        // 再次检查实例是否存在，防止在准备过程中被销毁
      });
      // 设置src，启动整个加载和播放流程
      this.audioContext.src = this.currentQuestion.audioResource;
      this.audioContext.play();
    },

    // [新增] 停止并销毁题目音频的核心方法
    stopQuestionAudio() {
      if (this.audioContext) {
        console.log("[Audio] 手动停止并销毁题目音频实例。");
        this.audioContext.destroy();
        this.audioContext = null;
      }
      this.isPlaying = false;
    },

    // [修改] getOptionClass，支持显示正确答案
    getOptionClass(index) {
      if (this.selectedOption !== index && !this.showCorrectAnswer) return "";

      // 如果是用户选择的选项
      if (this.selectedOption === index) {
        const isCorrect = index === this.currentQuestion.correctOption;
        return isCorrect ? "correct" : "wrong";
      }

      // 如果需要显示正确答案，且当前选项是正确答案
      if (
        this.showCorrectAnswer &&
        index === this.currentQuestion.correctOption
      ) {
        return "correct-answer";
      }

      return "";
    },

    getAnswerStatusImage() {
      if (this.selectedOption === null)
        return this.quizImageAssets.character.waiting;
      const isCorrect =
        this.selectedOption === this.currentQuestion.correctOption;
      return isCorrect
        ? this.quizImageAssets.character.correct
        : this.quizImageAssets.character.wrong;
    },
    getActivityStatusImage() {
      switch (this.activityStatus) {
        case ACTIVITY_STATUS.NOT_STARTED:
          return this.quizImageAssets.activity.notStarted;
        case ACTIVITY_STATUS.TODAY_ENDED:
          return this.quizImageAssets.activity.todayEnded;
        case ACTIVITY_STATUS.ACTIVITY_ENDED:
          return this.quizImageAssets.activity.ended;
        case ACTIVITY_STATUS.NO_ATTEMPTS:
          return this.quizImageAssets.activity.timesExhausted;
        case ACTIVITY_STATUS.NO_ACTIVITY:
          return this.quizImageAssets.activity.noActivity;
        default:
          return this.quizImageAssets.activity.noActivity;
      }
    },

    // [修改] 选择选项的核心逻辑
    selectOption(index) {
      if (this.isSubmitted) return;
      this.selectedOption = index;
      this.isSubmitted = true;

      // 1. 立刻调用销毁方法，停止题目音频
      this.stopQuestionAudio();

      const isCorrect = index === this.currentQuestion.correctOption;
      if (isCorrect) this.quizTotalNum++;

      // 2. "即用即毁"模式处理反馈音效
      const feedbackAudio = uni.createInnerAudioContext();
      feedbackAudio.src = isCorrect
        ? this.quizImageAssets.feedback.correct
        : this.quizImageAssets.feedback.wrong;

      const cleanup = () => {
        console.log("[Audio] 反馈音效处理完毕，销毁实例。");
        feedbackAudio.destroy();
      };
      feedbackAudio.onEnded(cleanup);
      feedbackAudio.onError(cleanup);
      feedbackAudio.play();
      // 3. 如果答错了，显示正确答案
      if (!isCorrect) {
        this.showCorrectAnswer = true;
        setTimeout(() => {
          this.showContinueDialog = true;
        }, 1500);
      } else {
        setTimeout(() => {
          this.clickContinue();
        }, 1500);
      }
      senTrack("ActivityClick", {
        ...this.trackParams,
        activity_id: this.activityInfo.id,
      });
    },

    clickContinue() {
      this.showContinueDialog = false;
      this.showCorrectAnswer = false;
      this.nextQuestion();
    },

    // [修改] 下一题
    nextQuestion() {
      this.currentIndex++;
      if (this.currentIndex < this.questions.length) {
        this.currentQuestion = this.questions[this.currentIndex];
        this.selectedOption = null;
        this.showCorrectAnswer = false; // 重置正确答案显示状态
        this.playQuestionAudio(); // 调用新方法播放
        this.isSubmitted = false;
      } else {
        this.activityInfo = {
          ...this.activityInfo,
          trackParams: { ...this.trackParams },
        };
        this.$emit("updataShare", this.activityInfo);

        this.currentStep = STEP.RESULT;
        this.stopQuestionAudio(); // 答题全部结束，确保销毁
      }
    },

    // [修改] 关闭弹窗
    close() {
      senTrack("ActivityPageLeave", {
        ...this.trackParams,
        activity_id: this.activityInfo.id,
        stay_duration: Math.floor(Date.now() - this.startTime / 1000),
      });
      if (Array.isArray(this.localValue) && this.localValue.length > 0) {
        this.currentIndex = 0;
        this.selectedOption = null;
        this.showCorrectAnswer = false; // 重置状态
        this.isSubmitted = false;
        this.quizTotalNum = 0;
        this.currentStep = STEP.START;
        this.loadQuestions();
        this.stopQuestionAudio(); // 调用销毁方法
        return;
      }
      this.$emit("close");
    },

    // [修改] 重新开始
    restart() {
      this.currentIndex = 0;
      this.selectedOption = null;
      this.showCorrectAnswer = false; // 重置正确答案显示状态
      this.isSubmitted = false;
      this.quizTotalNum = 0;
      if (!this.localValue[0]) this.localValue.push(this.activityInfo);
      this.stopQuestionAudio(); // 重新开始前，确保销毁旧实例
      this.loadQuestions();
      this.currentStep = STEP.START;
    },

    lotteryEvent(id) {
      this.lotteryId = id;
      this.showPopLottery = true;
    },

    getPrizes(list) {
      // 遍历奖品列表，为每个奖品单独上传埋点
      if (Array.isArray(list) && list.length > 0) {
        list.forEach((prize) => {
          const { prizeType, couponNo, prizeValue, relatedActivityId } = prize;
          let prizeId = "";
          // 根据奖品类型选择对应的ID字段
          switch (+prizeType) {
            case 2:
              prizeId = relatedActivityId || "";
              break;
            case 3:
              prizeId = prizeValue || "";
              break;
            case 6:
              prizeId = couponNo || "";
              break;
            default:
              prizeId = prize.id || prize.prizeId || "";
          }
          senTrack("ActivityResult", {
            ...this.trackParams,
            activity_id: this.activityInfo.id,
            prize_name: prize.prizeName || "",
            prize_id: prizeId,
            is_obtain_prize: true,
          });
        });
      } else {
        senTrack("ActivityResult", {
          ...this.trackParams,
          activity_id: this.activityInfo.id,
          is_obtain_prize: false,
        });
      }
    },

    restPlay() {
      this.playQuestionAudio();
    },
    async requestMyPrizeList() {
      const { data } = await myPrizeList(3);
      const huang = "huang";
      const hui = "hui";
      if (data?.records?.length > 0) {
        data.records.forEach((item) => {
          const { status, prizeType: type } = item;
          if (status === "1") {
            switch (type) {
              case "1": // 实物
                item.btnVal = "去查看";
                item.btnUrl = huang;
                break;
              case "2": // 虚拟物品
                item.btnVal = "去使用";
                item.btnUrl = huang;
                break;
              case "3": // 优惠券
                item.btnVal = "去使用";
                item.btnUrl = huang;
                break;
              case "4": // 积分
                item.btnVal = "去使用";
                item.btnUrl = huang;
                break;
              case "6": // 线下券
                if (item.writeOffStatus === "1") {
                  item.btnVal = "已核销";
                  item.btnUrl = hui;
                } else {
                  item.btnVal = "去核销";
                  item.btnUrl = huang;
                }
                break;
              case "7": // 礼品卡
                item.btnVal = "去查看";
                item.btnUrl = huang;
                break;
              default:
                break;
            }
          } else {
            item.btnVal = "去领取";
            item.btnUrl = huang;
          }
        });
      }
      this.prizes = data?.records ?? [];

      console.log("this.prizes", this.prizes);
    },
    handlePrize(info) {
      // 按钮为灰色不可操作时直接返回
      if (info.btnUrl === "http://img.songlei.com/myPrize/hui.png") return;
      if (info.prizeType === "2") {
        if (info.relatedActivityId) {
          this.lotteryId = info.relatedActivityId;
          this.showPopLottery = true;
          return;
        }
        // 盲盒奖品，未拆开时拆盲盒
        if (!info.blindBox) {
          this.openBlindBox(info);
        }
      } else {
        if (info.status === "1") {
          //0:未领取 1:已领取 2:未中奖
          // 已领取状态1:实物 2:虚拟物品 3:优惠券 4:积分 5:谢谢参与 6:线下券 7:礼品卡
          switch (info.prizeType) {
            case "1":
              // 实物去查看订单详情
              uni.navigateTo({
                url: `/pages/order/order-detail/index?id=${info.id}`,
              });
              break;
            case "2":
              // 虚拟物品去使用
              uni.navigateTo({
                url: `/pages/order/order-detail/index?id=${info.id}`,
              });
              break;
            case "3":
              // 优惠券去使用
              uni.navigateTo({
                url: `/pages/goods/goods-list/index?couponId=${info.prizeValue}`,
              });
              break;
            case "4":
              // 积分 去使用 跳积分签到页
              uni.navigateTo({
                url: `/pages/signrecord/signrecord-info/index`,
              });
              break;
            case "6":
              // 线下券去核销
              if (info.writeOffStatus === "0") {
                this.couponNo = info.couponNo;
                uni.navigateTo({
                  url: `/pages/coupon/coupon-offline-detail-plus/index?id=${info.couponNo}`,
                });
              }
              break;
            case "7":
              // 礼品卡 去查看 跳我的红包和礼品卡页面
              uni.navigateTo({
                url: `/pages/gift/gift-card/index`,
              });
              break;
            default:
              break;
          }
        } else {
          // 未领取状态
          if (info.prizeType === "1") {
            // 实物去领取页面
            uni.navigateTo({
              url: `/pages/order/order-confirm/index?prizeId=${info.id}`,
            });
          } else {
            // 其他奖品直接领取
            this.receive(info.id);
          }
        }
      }
    },

    /**
     * 拆盲盒接口调用，奖品库存越多概率越大
     * @param {Object} info 奖品信息
     */
    async openBlindBox(info) {
      const params = {
        actId: info.actId,
        prizeId: info.waterPrizeId,
      };
      try {
        const res = await openBlindBoxApi(params);
        if (res.data) {
          this.getPrizeList();
        }
      } catch (error) {
        // 处理异常或提示
        console.error("拆盲盒失败", error);
      }
    },
    /**
     * 领取奖品接口调用
     * @param {String} actUserPrizeId 奖品用户ID
     */
    receive(actUserPrizeId) {
      getPrize(actUserPrizeId).then(() => {
        uni.showToast({
          title: "领取成功",
          duration: 3000,
        });
        this.getPrizeList();
      });
    },

    showMyPrizeList() {
      if (!this.isLogin) {
        const restPath = `/pages/activity/audio-game/index?id=${this.value.shareId}&share_type=dbh`;
        uni.navigateTo({ url: "/pages/login/index?reUrl=" + restPath });
        return;
      }
      this.showPrize = true;
    },
  },
  // [修改] 组件销毁时，执行最终的清理
  beforeDestroy() {
    app.isBottomTabBar();
    this.stopQuestionAudio();
  },
};
</script>

<style scoped lang="scss">
.modal {
  width: 100vw;
  height: 100vh;
  background-image: url("https://img.songlei.com/live/dbh/audio-game-bg.png");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
.content {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  top: 240rpx;
  &.content-top {
    transform: translate(-50%, -50%);
    top: 50%;
  }
}

// 开始页面
.quiz-page {
  width: calc(100vw - 30rpx);
  position: relative;
  margin-left: 15rpx;
  .bg-image {
    width: 100%;
    height: 1017rpx;
  }
  .logo {
    position: absolute;
    left: 0;
    top: 25rpx;
    width: 300rpx;
    height: 400rpx;
  }
  .title {
    position: absolute;
    left: 219rpx;
    top: 118rpx;
    width: 400rpx;
    height: 260rpx;
  }
  .pen {
    position: absolute;
    right: 0;
    top: 212rpx;
    width: 148rpx;
    height: 200rpx;
  }
  .start-btn {
    position: absolute;
    left: 50%;
    bottom: 190rpx;
    width: 502rpx;
    height: 173rpx;
    transform: translateX(-50%);
  }
  .close-btn {
    position: absolute;
    right: 50rpx;
    top: 65rpx;
    width: 50rpx;
    height: 50rpx;
  }

  .character {
    position: absolute;
    left: 16rpx;
    top: 40rpx;
    width: 300rpx;
    height: 400rpx;
  }

  .quiz-container {
    left: 50%;
    transform: translateX(-50%);
    top: 494rpx;
    position: absolute;
    width: 560rpx;
    min-height: 500rpx;
    padding-bottom: 40rpx;
    background-image: url("https://img.songlei.com/live/dbh/option_list_border.png");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;

    .play-bar-container {
      margin-top: 32rpx;
      display: flex;
      align-items: center;
      width: 100%;
      padding: 0 30rpx;
    }

    .play-bar {
      width: 417rpx;
      height: 51rpx;
      background-image: url("https://img.songlei.com/live/dbh/pay-icon.gif");
      background-size: 100% 100%; /* 保持纵横比并覆盖整个容器 */
      background-position: center; /* 图片居中显示 */
      background-repeat: no-repeat; /* 不重复图片 */

      &.play-bar-stop {
        background-image: url("https://img.songlei.com/live/dbh/pay-icon-stop.png");
      }
    }

    .play-btn-wrapper {
      display: flex;
      justify-content: center;
      flex-direction: column;
      margin-left: auto;
      align-items: center;
      .play-icon {
        width: 60rpx;
        height: 60rpx;
      }
      text {
        margin-top: 7rpx;
        font-size: 20rpx;
        color: #3d3a30;
      }
    }

    .quiz-sub-title {
      margin-top: 12rpx;
      width: calc(100% - 64rpx);
      margin-left: 32rpx;
      font-size: 26rpx;
    }

    .quiz-item {
      padding: 0 24rpx;
      margin-top: 36rpx;
      display: flex;
      width: 408rpx;
      height: 118rpx;
      margin-left: 110rpx;
      position: relative;
      background-image: url("https://img.songlei.com/live/dbh/quiz-item-border.png");
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      font-size: 26rpx;
      color: #3d382f;
      display: flex;
      justify-content: center;
      align-items: center;

      &::after {
        content: "";
        position: absolute;
        top: 8rpx;
        left: 8rpx;
        right: 8rpx;
        bottom: 8rpx;
        background-color: #ffe100;
        border-radius: 20rpx;
        z-index: -1;
      }

      &::before {
        content: "";
        position: absolute;
        left: -80rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 66rpx;
        height: 78rpx;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 1;
      }

      &.option-0::before {
        background-image: url("https://img.songlei.com/live/dbh/option-a.png");
      }

      &.option-1::before {
        background-image: url("https://img.songlei.com/live/dbh/option-b.png");
      }

      &.option-2::before {
        background-image: url("https://img.songlei.com/live/dbh/option-c.png");
      }

      &.correct {
        color: #fff;
        &::after {
          background-color: #21b779;
        }
        .result-icon {
          position: absolute;
          right: -30rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 164rpx;
          height: 164rpx;
          background-image: url("https://img.songlei.com/live/dbh/icon_correct.png");
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          z-index: 2;
        }
      }
      &.wrong {
        color: #fff;
        &::after {
          background-color: #cc162f;
        }
        .result-icon {
          position: absolute;
          right: -30rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 164rpx;
          height: 164rpx;
          background-image: url("https://img.songlei.com/live/dbh/icon_wrong.png");
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          z-index: 2;
        }
      }

      // 新增：显示正确答案的样式
      &.correct-answer {
        color: #fff;
        animation: correctAnswerPulse 0.6s ease-in-out;

        &::after {
          background-color: #21b779;
          box-shadow: 0 0 20rpx rgba(33, 183, 121, 0.6);
        }

        .result-icon {
          position: absolute;
          right: -30rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 164rpx;
          height: 164rpx;
          background-image: url("https://img.songlei.com/live/dbh/icon_correct.png");
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          z-index: 2;
          animation: correctAnswerIconShow 0.3s ease-in-out 0.3s both;
        }
      }
    }

    // 正确答案提示文字样式
    .correct-answer-tip {
      margin-top: 30rpx;
      text-align: center;
      font-size: 28rpx;
      color: #21b779;
      font-weight: bold;
      animation: tipFadeIn 0.5s ease-in-out;
    }
  }
  .activity-end {
    width: 720rpx;
    height: 620rpx;
    margin-top: 100rpx;
  }

  .my-prize-btn {
    bottom: 100rpx;
    position: absolute;
    width: 304rpx;
    height: 110rpx;
    left: 221rpx;
  }

  .prize-list-border {
    width: 655rpx;
    height: 711rpx;
    background-image: url("https://img.songlei.com/live/dbh/prize_list_border.png");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    top: 400rpx;
    left: 31rpx;

    .prize-item-scroll {
      width: calc(655rpx - 80rpx);
      height: calc(711rpx - 80rpx);
      margin-top: 40rpx;
      margin-left: 40rpx;
    }
  }
}

// 正确答案高亮动画
@keyframes correctAnswerPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 正确答案图标显示动画
@keyframes correctAnswerIconShow {
  0% {
    opacity: 0;
    transform: translateY(-50%) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}

// 提示文字淡入动画
@keyframes tipFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.prize-msk {
  width: 100vw;
  height: 100vh;
  background: rgba($color: #000000, $alpha: 0.8);
  z-index: 1000;
  position: absolute;
  top: 0;
  left: 0;
}
.continue-dialog {
  width: 100vw;
  height: 100vh;
  background: rgba($color: #000000, $alpha: 0.8);
  z-index: 1000;
  position: absolute;
  top: 0;
  left: 0;

  .border-continue {
    width: 512rpx;
    height: 340rpx;
    background-image: url("https://img.songlei.com/live/dbh/border_continue_quiz.png");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    margin-top: calc((100vh - 340rpx) / 2);
    margin-left: calc((100vw - 512rpx) / 2);

    view {
      margin-top: 40rpx;
      margin-left: 38rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #3d3a30;
      line-height: 34rpx;
    }
    image {
      margin-left: calc((512rpx - 340rpx) / 2);
      bottom: 62rpx;
      position: absolute;
      width: 340rpx;
      height: 86rpx;
    }
  }
}

.prize-item {
  width: 100%;
  display: flex;
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #efefef;
  .prize-image {
    width: 160rpx;
    height: 160rpx;
  }
  .prize-content {
    margin-left: 32rpx;
    flex: 1;
    height: 160rpx;
    display: flex;
    flex-direction: column;
    position: relative;
    .prize-name {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      text-align: left;
    }
    .time {
      font-weight: 500;
      font-size: 26rpx;
      color: #7b7b7b;
      line-height: 34rpx;
      text-align: left;
    }
    .use-btn-container {
      position: absolute;
      width: 221rpx;
      height: 76rpx;
      text-align: center;
      font-size: 26rpx;
      font-weight: 800;
      color: #333;
      line-height: 76rpx;
      border-radius: 38rpx;
      bottom: 0;
      right: 0;
      &.huang {
        background: #ffe100;
      }

      &.hui {
        background: #efefef;
      }

      // .wen {
      //   z-index: 1;

      // }
      // .btn {
      //   position: absolute;
      //   top: 0;
      //   left: 0;
      //   width: 221rpx;
      //   height: 73rpx;
      // }
    }
  }
}
</style>
