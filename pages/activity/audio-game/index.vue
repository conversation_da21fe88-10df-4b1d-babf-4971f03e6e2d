<template>
  <view class="page-main">
    <audio-game-dialog
      v-if="showDialog"
      v-model="dialogData"
      @close="showDialog = false"
      @updataShare="updataShareInfo"
    />
  </view>
</template>

<script>
import AudioGameDialog from "./components/index.vue";
import { senTrack } from "@/public/js_sdk/sensors/utils.js";
export default {
  components: { AudioGameDialog },
  data() {
    return {
      showDialog: false,
      dialogData: {},
      shares: {
        imageUrl: "",
        path: "",
      },
      activityInfo: {},
    };
  },
  mounted() {
    let query = {};
    // #ifdef MP-WEIXIN
    let getLaunchOptions = uni.getLaunchOptionsSync();
    query = getLaunchOptions.query;
    // #endif
    let { id, scene, share_type } = query ?? {};
    if (scene) {
      const decodedScene = decodeURIComponent(scene);
      const regex = /^([^&]+)&([^&]+)&$/;
      const matches = decodedScene.match(regex);
      if (matches) {
        id = matches[1];
        share_type = matches[2];
      }
    }
    if (id && share_type === "dbh") {
      this.dialogData = { shareId: id };
    } else {
      this.dialogData = { shareId: "" };
    }
    this.showDialog = true;
  },
  methods: {
    updataShareInfo(data) {
      this.shares.imageUrl = data.sharePicUrl;
      this.shares.path = `/pages/activity/audio-game/index?id=${data.id}&share_type=dbh`;
      this.activityInfo = data;
    },
  },
  onShareAppMessage: function () {
    senTrack("ActivityPageShareOrCollect", {
      ...this.activityInfo.trackParams,
      action_type: "分享",
      activity_id: this.activityInfo.id,
    });
    return this.shares;
  },
};
</script>

<style lang="scss">
.page-main {
  width: 100%;
  height: 100%;
}
</style>
