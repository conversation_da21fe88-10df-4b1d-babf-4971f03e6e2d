<template>
	<view style="background-color: #A5C0D7; width: 100%; height: 100%; position: absolute;">
		<cu-custom :bgColor="'#2b81b9'" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content"><text class=" overflow-1">冰雪大世界</text></block>
		</cu-custom>
    <view class="prize-box">
      <!-- <image class="bg" mode="aspectFit" src="https://img.songlei.com/iceWorld/prize-bg.png" /> -->
      <view class="prize">
        <view class="prize-item"></view>
        <view class="prize-item"></view>
        <view class="prize-item"></view>
        <view class="prize-item"></view>
        <!-- <view class="prize-item"></view>
        <view class="prize-item"></view> -->
      </view>
    </view>
	</view>
</template>
<script>
	const app = getApp();
	// import { getIceInfo, registerIce } from '@/pages/activity/api/iceWorld.js';
	export default {
		data() {
			return {
			};
		},
		components: {},
		props: {},
		watch: {},
		computed: {},
		async onLoad(options) {
			if (!app.isLogin(true)) {
				return;
			}
			try {
				await app.initPage();

			} catch (e) {
				console.error(e, '================冰雪世界获取信息==================')
			}
		},
		created() {},
		mounted() {},
		methods: {

		},
	};
</script>
<style scoped lang="scss">
.prize-box{
  position: relative;
  width: 100%;
  height: 1282rpx;
  padding-top: 430rpx;
  box-sizing: border-box;
  background: url('https://img.songlei.com/iceWorld/prize-bg.png') center center no-repeat;
  background-size: 100% auto;
  .bg{
    width: 100%;
    height: 100%;
    position: absolute;
    top: -180rpx;
    left: 0;
    z-index: 0;
  }
  .prize{
    width: 643rpx;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    margin: 0 auto;
    .prize-item{
      width: 317rpx;
      height: 317rpx;
      background: #FFFFFF;
      border-radius: 24rpx;
      margin-top: 9rpx;
      &:nth-child(2n+1){
        margin-right: 9rpx;
      }
    }
  }
}

</style>