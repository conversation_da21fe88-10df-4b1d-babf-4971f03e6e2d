<template>
	<view style="background-color: #fff; width: 100%; height: 100%; position: absolute;">
		<cu-custom :bgColor="'#4340f8'" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content"><text class=" overflow-1">冰雪大世界</text></block>
		</cu-custom>
		<view class="title"> 
			<view class="Welcome">Welcome</view>
			<view class="SIGNUP">SIGN UP</view>
			<view class="Member-Register">会员注册</view>
			<image class="bg" mode="aspectFit" src="https://img.songlei.com/iceWorld/bg.png"  />
		</view>
		<view class="form">
			<view class="form-item">
				<view class="label">姓名</view>
				<view class="flex align-center">
					<input class="form-input" style="width: 419rpx;" v-model="form.userName" :disabled="isReady"  placeholder-style="color: #CDCDCD;" placeholder="请输入姓名" />
					<radio-group class="form-radio"  @change="sexChange" :disabled="isReady">
						<label v-for="item in sexArr" :key="item.value">
							<radio color="433FF8" activeBackgroundColor="433FF8" style="transform:scale(0.5);" :disabled="isReady" :value="item.value" :checked="item.value == sexCurrent" />
							<text>{{item.label}}</text>
						</label>
					</radio-group>
				</view>
			</view>
			<view class="form-item">
				<view class="label">手机号</view>
				<input  class="form-input" v-model="form.phone" disabled placeholder-style="color: #CDCDCD;" placeholder="请输入手机号" />
			</view>
			<view class="form-item">
				<view class="label">邮寄地址</view>
				<textarea class="form-textarea" v-model="form.address" :disabled="isReady" placeholder-style="color: #CDCDCD;" placeholder="请输入邮寄地址" />
			</view>

			<button size="default" type="default" class="submit-btn" v-if="isReady" @click="goPage">查看奖品</button>
			<button size="default" type="default" class="submit-btn" v-else @click="submit">提交</button>

			<view class="tips">
				您的信息将非常保密,请放心填写。我们承诺不会将您的信息 泄露给第三方。
			</view>
		</view>
		<!-- @tap="hideModalCoupon" -->
		<view class="cu-modal" :class="showClass" @tap.stop="hideModal" catchtouchmove="touchMove">
			<view class="cu-dialog bg-white success-box" @tap.stop>
				<image class="img" mode="aspectFit" src="https://img.songlei.com/iceWorld/success-icon.png"  />
				<view class="text">注册成功</view>
				<view class="tips">系统升级中更多精彩敬请期待...</view>
				<button size="default" type="default" class="btn" @click="goPage">确认</button>
			</view>
		</view>
	</view>
</template>
<script>
	const app = getApp();
	import { getIceInfo, registerIce } from '@/pages/activity/api/iceWorld.js';
	export default {
		data() {
			return {
				theme: app.globalData.theme,
				sexArr: [
					{value: 0, label: '女'},
					{value: 1, label: '男'}
				],
				form: {
					userName: '',
					phone: '',
					address: '',
					sex: ''
				},
				sexCurrent: -1,
				showClass: '',
				isReady: false
				
			};
		},
		components: {},
		props: {},
		watch: {},
		computed: {},
		async onLoad(options) {
			if (!app.isLogin(true)) {
				return;
			}
			try {
				await app.initPage();
				this.form.phone = uni.getStorageSync('user_info').phone
				const iceInfoData = await getIceInfo();
				console.log(iceInfoData, '=========================iceInfo==========================')
				if (iceInfoData.code == 0 && iceInfoData.data != null) {
					this.form = iceInfoData.data
					this.sexCurrent = iceInfoData.data.sex
					this.isReady = true
				}

			} catch (e) {
				console.error(e, '================冰雪世界获取信息==================')
			}
		},
		created() {},
		mounted() {
			// this.form.phone = uni.getStorageSync('user_info').phone
		},
		methods: {
			sexChange(e) {
				this.sexCurrent = e.detail.value
				this.form.sex = e.detail.value
			},
			hideModal() {
				this.showClass = ''
			},
			async submit() {
				try {
					if (!this.form.userName) {
						uni.showToast({ title: '请输入姓名', icon: 'none', duration: 3000 });
						return 
					}
					if (!this.form.sex) {
						uni.showToast({ title: '请选择性别', icon: 'none', duration: 3000 });
						return 
					}
					if (!this.form.phone) {
						uni.showToast({ title: '请输入手机号', icon: 'none', duration: 3000 });
						return 
					}
					if (!this.form.address) {
						uni.showToast({ title: '请输入邮寄地址', icon: 'none', duration: 3000 });
						return 
					}

					const res = await registerIce(this.form)
					if (res.code == 0) {
						this.showClass = 'show'
					}
					console.log(res, '===============================注册=============')
				} catch (e) {
					console.error(e, '==========================冰雪世界注册==============')
				}
			},
			goPage() {
				uni.navigateTo({
					url: '/pages/activity/iceWorld/prize'
				})
			}
		},
	};
</script>
<style scoped lang="scss">
.title {
	padding: 121rpx 0 80rpx 45rpx;
	position: relative;
	background: #4340f8;
	.Welcome{
		font-weight: bold;
		font-size: 31rpx;
		color: #8F93EA;
	}
	.SIGNUP{
		font-weight: bold;
		font-size: 92rpx;
		color: #FFFFFF;
		margin-top: -24rpx;
	}
	.Member-Register{
		font-weight: bold;
		font-size: 31rpx;
		color: #FFFFFF;
		margin-top: -24rpx;
	}
	.bg{
		position: absolute;
		width: 307rpx;
		height: 428rpx;
		right: 0;
		top: -121rpx;
	}
}
.form{
	width: 100%;
	background: #FFFFFF;
	border-radius: 79rpx 79rpx 0rpx 0rpx;
	padding: 61rpx 48rpx 48rpx;
	margin-top: -80rpx;
	position: relative;
	.form-item{
		margin-bottom: 28rpx;
	}
	.label{
		font-weight: 500;
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 23rpx;
	}
	.form-input{
		width: 100%;
		height: 97rpx;
		background: #F5F5F5;
		border-radius: 16rpx;
		font-size: 32rpx;
		padding: 0 33rpx;
	}
	.form-radio{
		margin-left: 42rpx;
	}
	.form-textarea{
		width: 100%;
		height: 203rpx;
		background: #F5F5F5;
		border-radius: 16rpx;
		padding: 33rpx;
	}
	.submit-btn{
		width: 654rpx;
		height: 111rpx;
		line-height: 111rpx;
		background: #433FF8;
		border-radius: 16rpx;
		margin: 113rpx auto 0;
		font-weight: 800;
		font-size: 30rpx;
		color: #FFFFFF;
	}
	.tips{
		width: 629rpx;
		font-weight: 500;
		font-size: 24rpx;
		color: #666666;
		margin: 57rpx auto 0;
	}
}
.success-box{
	width: 608rpx;
	height: 644rpx;
	background: #FFFFFF;
	border-radius: 120rpx;
	padding: 80rpx 73rpx;
	.img{
		width: 200rpx;
		height: 200rpx;
	}
	.text{
		font-weight: bold;
		font-size: 30rpx;
		color: #666666;
		text-align: center;
		margin: 31rpx auto 12rpx;
	}
	.tips{
		font-weight: 500;
		font-size: 24rpx;
		color: #838383;
		margin: 0 auto;
	}
	.btn{
		width: 462rpx;
		height: 111rpx;
		line-height: 111rpx;
		background: #433FF8;
		border-radius: 16rpx;
		margin: 30rpx auto 0;
		font-weight: 800;
		font-size: 30rpx;
		color: #FFFFFF;
	}
}
</style>