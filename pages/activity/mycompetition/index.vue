<template>
	<view style="background-color: #fff;">
		<cu-custom bgImage="https://img.songlei.com/live/competition/competition-topbg.png" :isBack="true"
			:hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content"><text class=" overflow-1">{{ articleInfo.articleTitle }}</text></block>
		</cu-custom>
		<image mode="aspectFill" style="width: 100%; height: 230rpx;"
			src="https://img.songlei.com/live/competition/competition-banner.png"></image>
		<view class="flex text-center">
			<view class="cu-item"
				:style="{flex: 1,margin: '0 20rpx', fontWeight:800, fontSize:'26rpx', paddingBottom:'10rpx',color: index == tabCur ? '#16B4EF':'#000000', borderBottom:  index == tabCur ?'solid 6rpx #16B4EF': 'solid 0rpx'}"
				v-for="(item, index) in competionTypes" :key="index" @tap="tabSelect" :data-index="index"
				:data-key="item.key">{{ item.value }}</view>
		</view>
		<view class="content">
			<template v-if="keyList&&keyList.length>0">
				<view v-for="(keyItem, keyIndex) in keyList" :key="keyItem">
					<view style="font-size: 22rpx;display: flex;justify-content: space-between; margin: 20rpx 0 10rpx;">
						<text>{{keyItem}}</text>
						<text
							v-if="myCompetitionList[keyItem]&&myCompetitionList[keyItem].length>0">{{myCompetitionList[keyItem].length}}场比赛</text>
					</view>
					<template v-if="myCompetitionList[keyItem]&&myCompetitionList[keyItem].length>0">
						<view class="competition-item"
							v-for="(competitionItem,competitionIndex) in myCompetitionList[keyItem]">
							<view style="width: 200rpx;max-width: 200rpx;">
								<view style="font-size: 22rpx;color: #000000;">{{competitionItem.matchStartTime}}
									{{competitionItem.matchName}}
								</view>
								<view style="font-size: 22rpx;color: #A4A4A4;margin-top: 26rpx;">
									{{competitionItem.isEnd==1?'完赛':'未完赛'}}
								</view>
								<view
									style="width: 200rpx;max-width: 200rpx; overflow: hidden; ;font-size: 22rpx;color: #000000;margin-top: 26rpx;;">
									我的竞猜：{{competitionItem.guessDetail}}</view>
							</view>
							<view
								style="width: 280rpx;max-width: 280rpx;display: flex;flex-direction: column;justify-content: space-between;">
								<view style="display: flex;align-items: center; justify-content: center;">
									<image mode="aspectFill" style="width: 68rpx; height: 58rpx;"
										:src="competitionItem.teamA.img"></image>
									<view style="width: 140rpx;margin-left: 20rpx;font-size: 22rpx;color: #000000;">
										{{competitionItem.teamA.name}}
									</view>
									<view style="min-width: 60rpx;font-weight: 800;font-size: 30rpx;color: #000000;">
										{{competitionItem.teamA.score||''}}
									</view>
								</view>
								<view style="display: flex;align-items: center; justify-content: center;">
									<image mode="aspectFill" style="width: 68rpx; height: 58rpx;"
										:src="competitionItem.teamB.img"></image>
									<view style="width: 130rpx;margin-left: 20rpx;font-size: 22rpx;color: #000000;">
										{{competitionItem.teamB.name}}
									</view>
									<view style="min-width: 60rpx;font-weight: 800;font-size: 30rpx;color: #000000;">
										{{competitionItem.teamB.score||''}}
									</view>
								</view>
							</view>
							<view
								:style="{ width:'127rpx',marginTop: '20rpx',display:'flex', flexDirection:'column', alignItems:'center', justifyContent:'center', color:  competitionItem.guessResult==1? '#FF1304':'#A4A4A4'}">
								<image mode="aspectFit" style="width: 66rpx; height: 80rpx;margin-bottom: 10rpx;"
									:src="competitionItem.guessResult==0? 'https://img.songlei.com/live/competition/no-result.png':competitionItem.guessResult==1?'https://img.songlei.com/live/competition/win-result.png':'https://img.songlei.com/live/competition/lose-result.png' ">
								</image>
								{{competitionItem.guessResult==0?'未出结果':competitionItem.guessResult==1?'您已猜中':'未猜中'}}
							</view>
							<view style="width: 100rpx; display: flex; justify-content: center;align-items: center;">
								<view v-if="competitionItem.guessResult==1" @click="(e)=>handleAction(competitionItem)"
									:style="{color:'#fff',display:'flex',alignItems:'center', justifyContent:'center',width:'96rpx', height:'96rpx',
								 backgroundImage: competitionItem.guessResult==1&&competitionItem.isRaffle == 1?'url(https://img.songlei.com/live/competition/join-btn.png)':'url(https://img.songlei.com/live/competition/select-btn.png)',
								 backgroundSize:'100%',
								 backgroundRepeat:'no-repeat',
								 padding: '0 22rpx',
								 lineHeight:'32rpx',
								 fontSize: '26rpx',
								 flexWrap:'wrap'
								 }">
									{{competitionItem.guessResult==1&&competitionItem.isRaffle == 1?'参与 抽奖':'查看 奖品'}}
								</view>
							</view>
						</view>
					</template>

				</view>
			</template>
			<view v-else style="height: 300rpx;display: flex; align-items: center; justify-content: center;">
				暂无数据
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getMyCompetitionList,
		getMyCompetitionType
	} from "@/api/activity.js";
	const util = require("utils/util.js");
	const app = getApp();
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				competionTypes: [{
						value: '小组赛',
						key: '0'
					},
					{
						value: '1/8决赛',
						key: '1'
					},
					{
						value: '1/4决赛',
						key: '2'
					},
					{
						value: '半决赛',
						key: '3'
					},
					{
						value: '决赛',
						key: '4'
					}
				],
				tabCur: 0,
				keyList: [],
				myCompetitionList: {}
			}
		},

		onLoad(options) {
			app.initPage().then(res => {
				this.getCompetionTypes()
			});
		},

		methods: {
			getCompetionTypes() {
				getMyCompetitionType().then(res => {
					if (res.data != null) {
						this.tabCur = res.data;
					}
					this.getData();
				})
			},
			tabSelect(e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.index != this.tabCur) {
					this.tabCur = dataset.index;
					this.getData();
				}
			},

			getData() {
				getMyCompetitionList({
					matchType: this.tabCur
				}).then(res => {
					if (res.data) {
						this.myCompetitionList = res.data;
						this.keyList = Object.keys(res.data);
					} else {
						this.myCompetitionList = {};
						this.keyList = [];
					}
				})
			},
			// guessResult 竞猜结果（0：未出结果 1：猜中 2：未猜中）
			// isRaffle 是否可以抽奖（0否1是）
			handleAction(competitionItem) {
				if (competitionItem.guessResult == 1) {
					if (competitionItem.isRaffle == 1) {
						uni.navigateTo({
							url: "/pages/activity/lottery/index?id=" + competitionItem.marketId
						})
					} else {
						uni.navigateTo({
							url: "/pages/activity/lottery/index?id=" + competitionItem.marketId + '&showPrize=1'
						})
					}
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		background: #F3F3F3;
		padding: 20rpx;

		.competition-item {
			display: flex;
			justify-content: space-between;
			padding: 14rpx;
			background-color: #fff;
			border-radius: 10rpx;
			margin-top: 20rpx;
		}
	}
</style>