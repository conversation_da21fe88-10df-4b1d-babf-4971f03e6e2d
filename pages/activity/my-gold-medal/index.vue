<template>
	<view style="background-color: #fff;">
		<cu-custom bgColor="#263989" :isBack="true"
			:hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content"><text class=" overflow-1">奥运会竞猜</text></block>
		</cu-custom>

		<view class="content">
			<template v-if="myCompetitionList&&myCompetitionList.length>0">
				<view
					v-for="(item,index) in myCompetitionList" :key="index">
					<view class="flex">
						<view 
							style="width: 265rpx;
								height: 50rpx;
								line-height: 50rpx;
								margin-right: 45rpx;
								text-align: center;"
						>
							{{dateStr(item.matchDate)}}
						</view>
					</view>

					<view 
						class="competition-item"
						:style="{
								backgroundSize: 'cover',
								width: '96%',
								margin: '0 auto',
								paddingTop: '10rpx',
								height: '290rpx'
						}"
					>
						<view class="padding-lr-sm padding-tb-sm" style="margin: 0 auto;">
							<view class="flex">
								<view>
									<view 
										class="flex text-black text-sm"
										style="width: 418rpx;height: 54rpx;line-height: 54rpx;background: #F4F4F4;border-radius: 19rpx; margin-bottom: 8rpx;"
										>
										<view class="padding-left-lg">比赛金牌数：</view>
										<view style="padding-left: 105rpx;">{{ item.goldNum || '— —' }}</view>
									</view>

									<view 
										class="flex text-black text-sm"
										style="width: 418rpx;height: 54rpx;line-height: 54rpx;background: #F4F4F4;border-radius: 19rpx;"
										>
										<view class="padding-left-lg">我的竞猜数：</view>
										<view style="padding-left: 105rpx;">
											<view v-if="item.myGuess">
												<view v-if="item.myGuess.type=='0'">{{ item.myGuess.min }}-{{ item.myGuess.max }}</view>
												<view v-if="item.myGuess.type=='1'">{{ item.myGuess.max }}以上</view>
											</view>
											<view v-else>{{ '— —' }}</view>
										</view>
									</view>
								</view>

								<view 
									style="
										width: 177rpx;
										height: 115rpx;
										line-height: 115rpx;
										text-align: center;
										background: #F4F4F4;
										margin-left: 8rpx;
										border-radius: 19rpx;"
									:style="{
										color:item.guessResult==1?'#FF0000':'#B3B3B3'
									}"
									>
									<view class="text-center">
										<view style="width: 52rpx; height: 40rpx;margin: 0 auto;">
											<image mode="widthFix" 
												:src="item.guessResult==0? 'https://img.songlei.com/live/competition/no-result.png':item.guessResult==1?'https://img.songlei.com/live/competition/win-result.png':'https://img.songlei.com/live/competition/lose-result.png' ">
											</image>
										</view>

										{{item.guessResult=='1'?'恭喜猜中':item.guessResult=='2'?'未猜中':'未出结果'}}
									</view>
								</view>
							</view>

							<view 
								class="text-center" 
								style="margin-top: 40rpx;"
							>
								<view class="flex justify-around">
									<button
										class="cu-btn"
										@click="(e)=>handleAction(item,'1')"
										style="
											width: 278rpx;
											height: 85rpx;
											color: #ffffff;
											border-radius: 27rpx;
											"
										:style="{
											background: item.guessResult==1?'linear-gradient(0deg, #F51100 0%, #FF5C5C 100%)':'linear-gradient(0deg, #A8A8A8 0%, #C3C2C2 100%)',
											'box-shadow': item.guessResult==1?'0rpx 5rpx 0rpx 0rpx #9D0C00, 0rpx 7rpx 0rpx 0rpx #6B0800':'0rpx 5rpx 0rpx 0rpx #696969, 0rpx 7rpx 0rpx 0rpx #4D4D4D',
											'border-image': item.guessResult==1?'linear-gradient(0deg, #720900, #F51606, #FFFFFF) 1 1':'linear-gradient(0deg, #505050, #A8A8A7, #FFFFFF) 1 1',
										}"
									>
										抽奖
									</button>

									<button
										class="cu-btn"
										@click="(e)=>handleAction(item,'2')"
										style="
											width: 278rpx;
											height: 85rpx;
											color: #ffffff;
											border-radius: 27rpx;"
										:style="{
											background: item.guessResult!=0?'linear-gradient(0deg, #F51100 0%, #FF5C5C 100%)':'linear-gradient(0deg, #A8A8A8 0%, #C3C2C2 100%)',
											'box-shadow': item.guessResult!=0?'0rpx 5rpx 0rpx 0rpx #9D0C00, 0rpx 7rpx 0rpx 0rpx #6B0800':'0rpx 5rpx 0rpx 0rpx #696969, 0rpx 7rpx 0rpx 0rpx #4D4D4D',
											'border-image': item.guessResult!=0?'linear-gradient(0deg, #720900, #F51606, #FFFFFF) 1 1':'linear-gradient(0deg, #505050, #A8A8A7, #FFFFFF) 1 1',
										}"
									>
										我的奖品
									</button>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>

			<view v-else style="height: 300rpx;display: flex; align-items: center; justify-content: center;">
				暂无数据
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getMyGoldList,
	} from "@/api/activity.js";
	const util = require("utils/util.js");
	const app = getApp();
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				myCompetitionList: {},
			}
		},

		onLoad(options) {
			app.initPage().then(res => {
				this.getData()
			});
		},

		methods: {
			// 时间格式化
			dateStr(dateStr){
				// 将字符串日期转换成Date对象
				if(dateStr){
					const date = new Date(dateStr);
					// 格式化输出日期
					// 获取年、月、日
					const year = date.getFullYear();
					const month = date.getMonth() + 1; // 月份从0开始，需要加1
					const day = date.getDate();
	
					// 获取星期几
					const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
					const dayOfWeek = days[date.getDay()];
	
					// 格式化输出日期
					const formattedDate = `${year}.${month}.${day} ${dayOfWeek}`;
					return formattedDate
				}
				return "— —"
			},

			// 获取我的竞猜列表
			getData() {
				getMyGoldList().then(res => {
					console.log("getData====>",res.data)
					if (res.data) {
						this.myCompetitionList = res.data;
					} else {
						this.myCompetitionList = {};
					}
				})
			},

			// guessResult 竞猜结果（0：未出结果 1：猜中 2：未猜中）
			// isRaffle 是否可以抽奖（0否1是）
			// num 1是抽奖 2 是我的奖品
			handleAction(competitionItem, num) {
				let url = "/pages/activity/lottery/index?id=" + competitionItem.marketId;
				if (num === '1' && competitionItem.guessResult == 1) {
						if (competitionItem.isRaffle == 1) {
								uni.navigateTo({
										url: url
								});
						} else {
								url += '&showPrize=1';
								uni.navigateTo({
										url: url
								});
						}
				} else if(competitionItem.guessResult != 0 && num === '2') {
						uni.navigateTo({
								url: url + '&showPrize=1'
						});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		background: #F3F3F3;
		padding: 20rpx;

		.competition-item {
			display: flex;
			justify-content: space-between;
			padding: 14rpx;
			background-color: #fff;
			border-radius: 35rpx;
			margin-top: 20rpx;
		}
	}
</style>