<template>
	<view>
		<popAssistance :userPrizeId="pid" />
	</view>
</template>

<script>
const app = getApp();
const util = require('utils/util.js');
import popAssistance from '../nineGrid/components/pop-assistance.vue';
export default {
	name: 'pop-assiatance',
	components: {
		popAssistance
	},

	data() {
		return {
			pid: ''
		};
	},
	onLoad(options) {
		if (options.scene) {
			//接受二维码中参数
			let scenes = decodeURIComponent(options.scene);
			this.pid = util.UrlParamHash(scenes, 'pid');
		} else {
			this.pid = options.pid;
		}
		console.log("=======");
	},
	methods: {}
};
</script>

<style></style>
