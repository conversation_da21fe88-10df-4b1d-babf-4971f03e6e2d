<template>
	<pop-lottery :lotteryId="lotteryId" ref="popupLottery" :isPop="false" :showPrizeListProp="showPrize" />
</template>

<script>
import PopLottery from './components/pop-lottery.vue'
const util = require("utils/util.js")
import { getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'
import { senTrack } from '@/public/js_sdk/sensors/utils.js'

export default {
	name: 'lotteryPage',
	components: {
		PopLottery,
	},
	data () {
		return {
			lotteryId: '',
			showPrize: false
		}
	},

	onLoad (options) {
		if (options.scene) {
			//接受二维码中参数
			let scenes = decodeURIComponent(options.scene)
			this.lotteryId = util.UrlParamHash(scenes, 'id')
		} else {
			this.lotteryId = options.id
			if (options.showPrize == 1) {
				this.showPrize = true
			}
		}
	},

	onShareAppMessage: function () {
		let share = this.$refs.popupLottery.shareMessage()

		senTrack('ActivityPageShareOrCollect', {
			'page_name': getCurrentTitle(0),
			'page_level': '一级',
			'activity_id': this.lotteryId,
			'activity_name': share.title,
			'activity_type_first': '玩法活动',
			'activity_type_second': '幸运九宫格',
			'activity_type': '分享'
		})

		console.log("==onShareAppMessage===", share)
		return {
			...share
		}
	},
}
</script>