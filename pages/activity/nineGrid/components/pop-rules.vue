<template>
	<!--助力弹框-->
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''">
		<view class="cu-dialog" style="width: 750rpx;background-color: transparent;">

			<view class="rule-layout">
				<view class="content">
					<view class="title">规则</view>
					<scroll-view scroll-y  style="height: 260px;" > <jyf-parser :html="rule"></jyf-parser></scroll-view>
				</view>
			</view>
			<image style="width: 30px; height: 30px; margin-top: 30rpx; "
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png"
				fit="cover" @tap.stop="handleClose" />
		</view>

	</view>
	</view>
</template>

<script>
	export default {
		name: 'pop-rules',
		props: {
			rule: {
				type: String,
				default: ''
			},
		},
		data() {
			return {
				showModal: true,
			}
		},

		methods: {
			handleClose() {
				this.showModal = false;
				this.$emit("close");
			},
		},

	}
</script>
<style lang="scss" scoped>
	.rule-layout {
		width: 100%; 
		display: flex;
		flex-direction: column; 
		width: 690rpx;
		height: 810rpx;
		margin: 0 auto;
		background-size: 100% 100%;
		background-image: url(https://img.songlei.com/live/activity/nineGrid/rule-bg.webp);
	}
	.content {
		margin: 30rpx auto;
		width: 470rpx;
		height: 440rpx;
		overflow-y: scroll;
		.title {
			font-weight: bold;
			font-size: 36rpx;
			color: #793636;
		}
	}
</style>