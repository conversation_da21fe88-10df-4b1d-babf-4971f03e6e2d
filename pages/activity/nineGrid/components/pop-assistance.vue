<template>
	<!-- 抽奖 popup -->
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''">
		<view class="cu-dialog" style="width: 750rpx;background-color: transparent;">
			<view class="assiatance-layout" v-if="step==1">
				<scroll-view scroll-y  style="height: 760rpx;transform: translateZ(0);"> 
				  <view class="content">
					  <view class="flex align-center">
						  <image :src="info.headImgUrl" class="head-image  align-center"></image>
						  <view class="user-info">{{info.nickName}}用户邀请您助力</view>
					  </view>
					  <view class="title">{{info.actName}}活动抽红包了</view>
					  <jyf-parser :html="info.actRule"></jyf-parser>
				  </view>
				</scroll-view>
				<!-- 助力状态0不可助力 1可以助力 2已助力 -->
				<view class="flex justify-center" style="margin: 120rpx 50rpx 20rpx;">
					<image v-if="info.status==1" class="assistant-btn"  style="margin-right: 30rpx;" src="https://img.songlei.com/live/activity/nineGrid/assistant-btn.webp" @click="handleAssistant"></image>
					<image v-else-if="info.status==2" class="assistant-btn" style="margin-right: 30rpx;" src="https://img.songlei.com/live/activity/nineGrid/assistant-done.webp"></image>
					<image class="assistant-btn"  src="https://img.songlei.com/live/activity/nineGrid/gotodraw.png" @click="handleDraw"></image>
				</view>
			</view>
			<view v-else class="assiatance-success">
				 <image  @click="handleDraw" src="https://img.songlei.com/live/activity/nineGrid/goto-draw_orange.png" class="goto-draw_orange"></image>
			</view>
			<!-- <image style="width: 30px; height: 30px; margin-top: 30rpx; "
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png"
				fit="cover" @tap.stop="handleClose" /> -->
		</view>
	</view>
	</view>
</template>

<script>
	const app = getApp();
	import { getAssistanceInfo, assistanceAction } from '@/api/activity.js';
	export default {
		name: 'pop-assiatance',
		props: {
			userPrizeId: {
				type: String,
				default: ''
			},
		},
		data() {
			return {
				showModal: true,
				info: null,
				step: 1 //  1 助力信息  2  助力成功信息
			}
		},
	watch: {
		userPrizeId: {
			handler(newVal, oldVal) {
				if (newVal != oldVal && newVal) {
					app.initPage().then((res) => {
						this.getInfo(newVal);
					});
				}
			},
			immediate: true
		}
	},

		methods: {
			async getInfo(userPrizeId){
				uni.showLoading({
					title: '加载中...'
				});
				let res = await getAssistanceInfo({
					userPrizeId
				});
				if(res.ok){
					this.info = res.data;
				}
				uni.hideLoading();
			},
			handleClose() {
				this.showModal = false;
				this.$emit("close");
			},
			handleDraw(){
				if(this.info.modelType=='2'){
					uni.navigateTo({
						url: "/pages/activity/lottery/index?id="+this.info.actId
					})
				}else {
					uni.navigateTo({
						url: "/pages/activity/nineGrid/index?id="+this.info.actId
					})
				}
				
			},
			
			async handleAssistant(){
				let that = this;
				uni.showLoading({
					title: '助力中...'
				});
				try{
					let res = await assistanceAction({
						userPrizeId: this.userPrizeId
					});
					uni.hideLoading();
					if(res.ok && res.data){
						this.step = 2;
						// uni.showToast({
						// 	title: "助力成功"
						// })
						// uni.navigateTo({
						// 	url: "/pages/activity/nineGrid/index?id="+res.data.actId
						// })
					}
				}catch(e){
				}
			}
		},

	}
</script>
<style lang="scss" scoped>
	.assiatance-layout {
		width: 100%; 
		display: flex;
		flex-direction: column; 
		width: 726rpx;
		height: 1000rpx;
		margin: 0 auto;
		background-size: 100% 100%;
		background-image: url(https://img.songlei.com/live/activity/nineGrid/pop-assiatancebg.webp);
	}
	.content {
		margin: 30rpx auto;
		width: 470rpx;
		height: 634rpx;
		overflow-y: scroll;
		
		.head-image {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
		}
		
		.user-info {
			font-weight: 500;
			font-size: 28rpx;
			color: #000000;
			margin-left: 20rpx;
		}
		.title {
			font-weight: bold;
			font-size: 36rpx;
			color: #793636;
			margin-top: 10rpx;
		}
	}
	
	.assistant-btn {
		width: 284rpx;
		height: 110rpx;
	}
	
	.assiatance-success {
		width: 100%;
		display: flex;
		flex-direction: column; 
		align-items: center;
		margin-bottom: 30rpx;
		width: 726rpx;
		height: 851rpx;
		margin: 0 auto;
		background-size: 100% 100%;
		background-image: url(https://img.songlei.com/live/activity/nineGrid/assiatance-success.webp);
	}
	
	.goto-draw_orange {
		width: 480rpx;
		height: 126rpx;
		margin-top: 700rpx;
	}
	
</style>