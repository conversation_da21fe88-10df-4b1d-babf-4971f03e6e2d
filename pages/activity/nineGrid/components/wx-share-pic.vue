<template>
	<view style="position: fixed; left: 100%; z-index: -9999; opacity: 0">
		<canvas :canvas-id="canvasID" :style="{ width: canvasW + 'px', height: canvasH + 'px' }"></canvas>
	</view>
</template>

<script>
var _this;
export default {
	name: 'wx-share-pic',
	props: {
		//canvasID 等同于 canvas-id
		canvasID: {
			Type: String,
			default: 'wxCanvas'
		},
		//背景图片
		bgImg: {
			type: String,
			default: ''
		},

		//奖品列表
		prizeList: {
			type: Array,
			default: []
		}
	},
	data() {
		return {
			canvasW: 0,
			canvasH: 0,
			ctx: null,
			width: 700
		};
	},
	methods: {
		async onCanvas() {
			let _this = this;
			const ctx = uni.createCanvasContext(_this.canvasID, _this);
			_this.canvasW = uni.upx2px(_this.width);
			_this.canvasH = uni.upx2px(_this.width * 0.8);
			ctx.setFillStyle('#FFFFFF'); //canvas背景颜色
			ctx.fillRect(0, 0, _this.canvasW, _this.canvasH); //canvas画布大小

			//背景图
			let bgimg = await _this.getImageInfo(_this.bgImg || 'https://img.songlei.com/live/activity/nineGrid/default-share.jpg');
			ctx.drawImage(bgimg, 0, 0, _this.canvasW, _this.canvasH);

			const prizeList = this.getValidPrize();
			if (prizeList && prizeList[0]) {
				// 第一个奖品背景白色
				_this.setRadius(ctx, 10, _this.canvasW * 0.02, _this.canvasH * 0.58, _this.canvasW * 0.3, _this.canvasH * 0.4); // 加圆角
				ctx.setFillStyle('#FFFFFF');
				ctx.fill();
				//画奖品图片
				let prize0 = await _this.getImageInfo(prizeList[0].url);
				ctx.drawImage(prize0, _this.canvasW * 0.04, _this.canvasH * 0.6, _this.canvasW * 0.26, _this.canvasH * 0.3);
				// 画奖品名称
				ctx.setFontSize(12);
				ctx.setTextAlign('center');
				ctx.setFillStyle('#000000');
				ctx.fillText(prizeList[0].prizeName, _this.canvasW * 0.16, _this.canvasH * 0.96, _this.canvasW * 0.26);
			}

			if (prizeList && prizeList[1]) {
				// 第二个奖品背景白色
				_this.setRadius(ctx, 10, _this.canvasW * 0.34, _this.canvasH * 0.58, _this.canvasW * 0.3, _this.canvasH * 0.4); // 加圆角
				ctx.setFillStyle('#FFFFFF');
				ctx.fill();

				//画奖品图片
				let prize0 = await _this.getImageInfo(prizeList[1].url);
				ctx.drawImage(prize0, _this.canvasW * 0.36, _this.canvasH * 0.6, _this.canvasW * 0.26, _this.canvasH * 0.3);
				// 画奖品名称
				ctx.setFontSize(12);
				ctx.setTextAlign('center');
				ctx.setFillStyle('#000000');
				ctx.fillText(prizeList[1].prizeName, _this.canvasW * 0.49, _this.canvasH * 0.96, _this.canvasW * 0.26);
			}

			if (prizeList && prizeList[2]) {
				// 第三个奖品背景白色
				_this.setRadius(ctx, 10, _this.canvasW * 0.67, _this.canvasH * 0.58, _this.canvasW * 0.3, _this.canvasH * 0.4); // 加圆角
				ctx.setFillStyle('#FFFFFF');
				ctx.fill();

				//画奖品图片
				let prize0 = await _this.getImageInfo(prizeList[2].url);
				ctx.drawImage(prize0, _this.canvasW * 0.69, _this.canvasH * 0.6, _this.canvasW * 0.26, _this.canvasH * 0.3);
				// 画奖品名称
				ctx.setFontSize(12);
				ctx.setTextAlign('center');
				ctx.setFillStyle('#000000');
				ctx.fillText(prizeList[2].prizeName, _this.canvasW * 0.82, _this.canvasH * 0.96, _this.canvasW * 0.26);
			}

			//延迟后渲染至canvas上
			let pic = await _this.setTime(ctx);
			_this.$emit('success', pic);
		},

		//彻底改成同步 防止拿到的图片地址为空
		setTime(ctx) {
			_this = this;
			return new Promise((resole, err) => {
				setTimeout(() => {
					ctx.draw(false, async () => {
						let pic = await _this.getNewPic();
						resole(pic);
					});
				}, 600);
			});
		},

		getNewPic() {
			return new Promise((resolve, errs) => {
				setTimeout(() => {
					uni.canvasToTempFilePath(
						{
							canvasId: _this.canvasID,
							quality: 1,
							complete: (res) => {
								// 在H5平台下，tempFilePath 为 base64
								// 关闭showLoading
								uni.hideLoading();
								//  储存海报地址  也是分享的地址
								resolve(res.tempFilePath);
							}
						},
						_this
					);
				}, 200);
			});
		},
		//获取图片的临时地址
		getImageInfo(imgSrc) {
			return new Promise((resolve, errs) => {
				uni.getImageInfo({
					src: imgSrc,
					success: (image) => {
						resolve(image.path);
					},
					fail: (err) => {
						console.error('getImageInfo:', err);
					}
				});
			});
		},

		/**
		 * 设置圆角矩形
		 *
		 * @param ctx 绘图上下文
		 * @param cornerRadius 圆角半径
		 * @param width 矩形宽度
		 * @param height 矩形高度
		 * @param x 矩形左上角的 x 坐标
		 * @param y 矩形左上角的 y 坐标
		 * @returns 无返回值
		 */
		setRadius(ctx, cornerRadius, x, y, width, height) {
			// 开始绘制路径
			ctx.beginPath();
			// 绘制最左侧的圆角
			ctx.arc(x + cornerRadius, y + cornerRadius, cornerRadius, Math.PI, Math.PI * 1.5);
			// 绘制顶部边缘
			ctx.moveTo(x + cornerRadius, y);
			ctx.lineTo(x + width - cornerRadius, y);
			ctx.lineTo(x + width, y + cornerRadius);
			// 绘制最右侧的圆角
			ctx.arc(x + width - cornerRadius, y + cornerRadius, cornerRadius, Math.PI * 1.5, Math.PI * 2);
			// 绘制右侧边缘
			ctx.lineTo(x + width, y + height - cornerRadius);
			ctx.lineTo(x + width - cornerRadius, y + height);
			// 绘制最下侧的圆角
			ctx.arc(x + width - cornerRadius, y + height - cornerRadius, cornerRadius, 0, Math.PI * 0.5);
			// 绘制底部边缘
			ctx.lineTo(x + cornerRadius, y + height);
			ctx.lineTo(x, y + height - cornerRadius);
			// 绘制最左侧的圆角
			ctx.arc(x + cornerRadius, y + height - cornerRadius, cornerRadius, Math.PI * 0.5, Math.PI);
			// 绘制左侧边缘
			ctx.lineTo(x, y + cornerRadius);
			ctx.lineTo(x + cornerRadius, y);
			// 闭合路径
			ctx.closePath();
		},

		// 获取有效的奖品
		getValidPrize() {
			let result = [];
			if (this.prizeList && this.prizeList.length > 0) {
				result = this.prizeList.filter(function (item) {
					return item.objType != 5;
				});
			}
			return result;
		}
	},

	mounted() {
		_this = this;
	}
};
</script>

<style lang="scss"></style>
