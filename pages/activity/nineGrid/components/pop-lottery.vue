<template>
	<view :style="{ width: '100vw', height: '100%', background: bgColor }">
		<view
			:style="{ background: isPop ? 'rgba(0, 0, 0, 0.6)' : 'transparent', backgroundSize: '100% 100%', backgroundImage: isPop ? '' : `url(${bgImg})` }"
			catchtouchmove="true"
			v-if="showModal"
			:class="showModal ? 'show' : ''"
		>
			<cu-custom :hideMarchContent="true" :isBack="true"></cu-custom>
			<lottery
				ref="lottery"
				:lotteryId="lotteryId"
				@close="handleClose"
				:isPop="isPop"
				:showPrizeListProp="showPrizeListProp"
				@setBgImg="handleSetBgImg"
				@setBgColor="handleSetBgColor"
			/>
		</view>
	</view>
</template>

<script>
import Lottery from './lottery.vue';
export default {
	name: 'pop-lottery',
	components: {
		Lottery
	},
	props: {
		lotteryId: {
			type: String,
			default: ''
		},
		isPop: {
			type: Boolean,
			default: false
		},
		showPrizeListProp: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			showModal: true,
			bgImg: '',
			bgColor: ''
		};
	},

	methods: {
		handleClose() {
			this.showModal = false;
			this.$emit('close', '');
		},
		handleSetBgImg(bgImg) {
			this.bgImg = bgImg;
		},

		handleSetBgColor(bgColor) {
			this.bgColor = bgColor;
		},

		shareMessage() {
			return this.$refs.lottery.shareMessage();
		}
	}
};
</script>

<style lang="scss" scoped>
.almost-lottery__popup-wrap {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;

	.almost-lottery {
		background: transparent;
	}
}
</style>