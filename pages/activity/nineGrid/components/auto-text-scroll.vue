<template>
	<view class="swiperC-text">
		<view v-for="(item, index) in textList" :key="index" class="text-item">{{ item }}</view>
	</view>
</template>

<script>
export default {
	name: 'auto-text-scroll',
	data() {
		return {
			textList: []
		};
	},
	methods: {
		init(items = []) {
			this.textList = items;
			if(this.textList&& this.textList.length<20){
				this.textList = this.textList.concat(this.textList);
			}
		}
	}
};
</script>

<style>
@-webkit-keyframes rowup {
	0% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}

	100% {
		-webkit-transform: translate3d(0, -250rpx, 0);
		transform: translate3d(0, -250rpx, 0);
	}
}
@keyframes rowup {
	0% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}

	100% {
		-webkit-transform: translate3d(0, -250rpx, 0);
		transform: translate3d(0, -250rpx, 0);
	}
}
.swiperC-text {
	font-size: 25rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #ffffff;
	width: 475rpx;
	line-height: 38rpx;
	-webkit-animation: 5s rowup linear infinite normal;
	animation: 5s rowup linear infinite normal;
	/* animation-delay: 1s; */
}
</style>
