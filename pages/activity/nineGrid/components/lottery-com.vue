<template>
	<!-- 转盘 -->
	<view class="luck-layout">
		<view class="luck-list">
			<view v-for="(item, index) in prizeList" :key="index">
				<view class="cell-item" :style=" {
					backgroundImage: index == 4? `url(${centerImg})` :  index === sel ? `url(https://img.songlei.com/live/activity/nineGrid/selected2.png)` :'' ,
				}">
					<view class="cell-content">
						<image :src="item.url" mode="widthFix" style="width: 80rpx; height: 80rpx" v-if="item.url">
						</image>
						<view style="margin-top: 5rpx" v-if="item.prizeName">
							<text style="font-size: 24rpx">{{ item.prizeName }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			// 列表
			prizeList: {
				type: Array,
				default: []
			},
			// 中奖索引
			prizeIndex: {
				type: Number,
				default: 0
			},
			// 剩余抽奖次数
			freeNum: {
				type: Number,
				default: 0
			},
			centerImg: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				sel: '',
				FastNums: 0,
				SlowNums: 0,
				time: 1000,
				LoopStatus: true,
				noClick: true,
				freeLoopTimer: null
			};
		},
		mounted() {
			this.freeLoop();
		},
		methods: {
			// 中奖后的逻辑
			updateMoney(prizeIndex) {
				this.$emit('handleLuckEnd', prizeIndex);
			},
			//老虎机空闲时候旋转
			freeLoop() {
				this.freeLoopTimer && clearTimeout(this.freeLoopTimer);
				if (!this.sel || this.sel < 9) {
					if (this.sel == 3) {
						this.sel = 0;
					} else if (this.sel === '') {
						this.sel = 0;
					} else if (this.sel == 2) {
						this.sel = 5;
					} else if (this.sel == 5) {
						this.sel = 8;
					} else if (this.sel == 8) {
						this.sel = 7;
					} else if (this.sel == 7) {
						this.sel = 6;
					} else if (this.sel == 6) {
						this.sel = 3;
					} else {
						this.sel++;
					}
					this.freeLoopTimer = setTimeout(() => {
						this.freeLoop();
					}, 1000);
				}
			},

			// 开始抽奖
			handleActionStart(index) {
				if (!this.noClick) {
					uni.showToast({
						title: '请勿频繁点击',
						icon: 'none',
						position: 'bottom'
					});
					return;
				}
				this.noClick = false;
				this.FastNums = 0;
				this.SlowNums = 0;
				this.time = 100;
				this.LoopStatus = true;
				this.freeLoopTimer && clearTimeout(this.freeLoopTimer);
				this.loop();
			},
			// 抽奖过程 控制
			loop() {
				let prizeIndex = this.prizeIndex;
				if (!this.sel || this.sel < 9) {
					if (this.sel == 3) {
						this.sel = 0;
					} else if (this.sel === '') {
						this.sel = 0;
					} else if (this.sel == 2) {
						this.sel = 5;
					} else if (this.sel == 5) {
						this.sel = 8;
					} else if (this.sel == 8) {
						this.sel = 7;
					} else if (this.sel == 7) {
						this.sel = 6;
					} else if (this.sel == 6) {
						this.sel = 3;
					} else {
						this.sel++;
					}
					this.FastNums++;
					if (this.FastNums == 4) {
						this.FastNums = 0;
						this.time = 50;
						this.SlowNums++;
					}
					if (this.SlowNums == 8) {
						this.SlowNums = 0;
						this.time = 300;
						this.FastNums = 5;
					}
					if (this.FastNums > 5) {
						if (this.sel == prizeIndex) {
							this.noClick = true;
							this.LoopStatus = false;
							// 成功的逻辑
							this.updateMoney(prizeIndex);
						}
					}
					if (this.LoopStatus) {
						setTimeout(() => {
							this.loop();
						}, this.time);
					}
				}
			}
		}
	};
</script>

<style scoped>
	.luck-layout {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 抽奖 */
	.luck-list {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cell-item {
		width: 195rpx;
		height: 185rpx;
		border-radius: 30rpx;
		margin: 3rpx;
		background-size: 100% 100%;
	}

	.cell-content {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.select {
		background-image: url('https://img.songlei.com/live/activity/nineGrid/selected2.png');
	}

	.center-layout {
		background-image: url('https://img.songlei.com/live/activity/nineGrid/c.png');
	}
</style>