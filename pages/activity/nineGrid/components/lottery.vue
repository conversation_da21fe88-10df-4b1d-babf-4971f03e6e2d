<template>
	<view class="almost-lottery"
		:style="{ height: '1460rpx', backgroundSize: '100% 100%', backgroundImage: `url(${lotteryInfo.dialImg})` }">
		<!-- lottery -->
		<view class="almost-lottery__head">
			<view class="rules-btn" @click="handleShowRule">规则</view>
			<view class="prize-btn" @click="handleShowPrizeList">中奖明细</view>
			<view v-if="sharePopInfo.isCanInvite == 1" class="share-btn" @click="handleShowSharePop">邀请好友</view>
			<view class="start-time" v-if="lotteryInfo.startTime">
				活动时间：{{ lotteryInfo.startTime.substring(6, 10).replace('-', '.') }} ~ {{ lotteryInfo.endTime.substring(6,
					10).replace('-', '.') }}
			</view>
		</view>
		<view class="almost-lottery__wheel">
			<lottery-com ref="lotteryCom" :freeNum="freeNum" :prize-list="prizeList" :prize-index="prizeIndex"
				@handleLuckEnd="handleLuckEnd"
				:centerImg="lotteryInfo.centerImg || 'https://img.songlei.com/live/activity/nineGrid/c.png'" />
		</view>
		<view class="barrage">
			<image style="width: 48rpx; height: 38rpx; margin-right: 20rpx"
				src="https://img.songlei.com/live/activity/nineGrid/trumpet.png" @click="handleActionStart"></image>
			<auto-text-scroll ref="barrage" width="750rpx" height="140rpx"></auto-text-scroll>
		</view>
		<view class="lottery-base">
			<image class="lottery-btn"
				:src="lotteryInfo.drawImg || 'https://img.songlei.com/live/activity/nineGrid/click-start2.webp'"
				@click="handleActionStart"></image>
			<view v-if="freeNum > -1" class="almost-lottery__count">抽奖 {{ freeNum }} 次</view>
			<view v-else class="almost-lottery__count">请先登录</view>
		</view>

		<pop-rules v-if="showRules" :rule="lotteryInfo.detail" @close="handleCloseRule" />
		<pop-prize-list v-if="showPrizeList" :lotteryId="lotteryId" @close="handleClosePrizeList" />
		<pop-prize v-if="showPrize" :showType="showType" :prize="showType == 1 ? prizeInfo : sharePopInfo"
			@close="handleClosePrize" @handleShowAssistanceList="showAssistanceList = true"
			@handleShowPost="handleShowPost" />
		<pop-status v-if="showStatus" :startTime="lotteryInfo.startTime" :endTime="lotteryInfo.endTime"
			:status="lotteryInfo.status" />
		<PopAssistanceList v-if="showAssistanceList" :list="prizeInfo.actInfoHelpVO.helpUsers"
			@close="showAssistanceList = false" />
		<!-- 送券通知 -->
		<coupon-notification ref="topMessage"></coupon-notification>
		<wxSharePic ref="wxSharePic" :bgImg="lotteryInfo.shareImg" :prizeList="prizeList" @success="shareSuccess" />
		<postSharePic v-if="showPostShare" v-model="showPostShare" :shareParams="sharePostParams" />
	</view>
</template>

<script>
const app = getApp()
import LotteryCom from './lottery-com.vue'
import PopRules from './pop-rules.vue'
import PopPrizeList from './pop-prizelist.vue'
import PopAssistanceList from './pop-assistancelist.vue'
import PopPrize from './pop-prize.vue'
import PopStatus from './pop-status.vue'
import AutoTextScroll from './auto-text-scroll.vue'
import wxSharePic from './wx-share-pic.vue'
import couponNotification from '@/components/coupon-notification/index'
import postSharePic from './post-share-pic.vue'
import { getActivityInfo, getActivityUser, getPrizeInfo, getHistoryUsers, getInviteInfo } from '@/api/activity.js'
import { clearCacheFile, clearStore } from '@/utils/lottery-utils.js'
import util from 'utils/util'
import { getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'
import { senTrack } from '@/public/js_sdk/sensors/utils.js'

export default {
	name: 'lotteryIndex',
	components: {
		LotteryCom,
		AutoTextScroll,
		PopRules,
		PopPrizeList,
		PopAssistanceList,
		PopPrize,
		PopStatus,
		couponNotification,
		wxSharePic,
		postSharePic
	},
	props: {
		lotteryId: {
			type: String,
			default: ''
		},
		//是否弹框的方式展示
		isPop: {
			type: Boolean,
			default: false
		},
		showPrizeListProp: {
			type: Boolean,
			default: false
		}
	},
	data () {
		return {
			// 以下是转盘配置相关数据
			lotteryConfig: {
				// 抽奖转盘的整体尺寸，单位rpx
				lotterySize: 630,
				// 抽奖按钮的尺寸，单位rpx
				actionSize: 196
			},

			// 以下是转盘 UI 配置
			// 转盘外环图，如有需要，请参考替换为自己的设计稿
			// lotteryBg: require('@/static/lottery-bg.png'),
			// 抽奖按钮图
			// actionBg: require('@/static/action-bg.png'),

			// 以下是奖品配置数据
			// 奖品数据
			lotteryInfo: {},
			prizeList: [],
			// 奖品是否设有库存
			onStock: true,
			// 中奖下标
			prizeIndex: -1,
			// 中奖奖品
			prizeInfo: null,
			// 是否正在抽奖中，避免重复触发
			prizeing: false,
			// 权重随机数的最大值
			prizeWeightMax: 0,
			// 权重数组
			prizeWeightArr: [],
			// 以下为业务需求有关示例数据
			// 金币余额
			goldCoin: 20,
			// 当日免费抽奖次数余额
			freeNum: 0,
			// 每次消耗的金币数
			goldNum: 20,
			// 每天免费抽奖次数
			freeNumDay: 1,
			tabIndex: 0,
			historyList: [
				'187****6531中10元优惠券',
				'135****1235中1元优惠券',
				'176****9863中66元优惠券',
				'132****2136中33元优惠券',
				'185****9036中88元优惠券',
				'189****4765中88元优惠券'
			],
			isDrawFinish: false, //是否在请求数据绘制中
			showRules: false, // 规则
			showPrizeList: false, // 中奖明细弹框
			showPrize: false, // 恭喜中奖了弹框
			showStatus: false, // 活动已开始或者已结束弹框
			showAssistanceList: false,
			shares: {},
			shareImgSrc: '',
			showPostShare: false,
			sharePostParams: {},
			assistancelist: [],
			sharePopInfo: {
				isCanInvite: false,
				userPrizeId: ''
			},
			showType: '1', //1显示奖品   2  只显示下面的分享
			enterTime: ''
		}
	},

	watch: {
		lotteryId: {
			handler (newVal, oldVal) {
				if (newVal != oldVal && newVal) {
					app.initPage().then((res) => {
						this.handleInitCanvas()
					})
				}
			},
			immediate: true
		}
	},

	created () {
		this.enterTime = new Date().getTime()
		this.prizeList = []
	},

	destroyed () {
		const leaveTime = new Date().getTime()
		const stayDuration = Math.floor((leaveTime - this.enterTime) / 1000)

		senTrack('ActivityPageLeave', {
			'page_name': getCurrentTitle(0),
			'forward_source': getCurrentTitle(0),
			'page_level': '一级',
			'activity_id': this.lotteryId,
			'activity_name': this.lotteryInfo.name,
			'activity_type_first': '玩法活动',
			'activity_type_second': '幸运九宫格',
			'stay_duration': stayDuration
		})
	},

	methods: {
		shareSuccess (e) {
			this.shareImgSrc = e
		},
		// 重新生成
		handleInitCanvas () {
			this.prizeList = []
			this.getPrizeList()
			this.$nextTick(() => {
				if (this.showPrizeListProp) {
					this.handleShowPrizeList()
				}
			})
		},

		// 获取奖品列表
		async getPrizeList () {
			uni.showLoading({
				title: '奖品准备中...'
			})
			let res = await getActivityInfo({
				id: this.lotteryId
			})
			if (res.ok) {
				let data = res.data
				if (data) {
					this.lotteryInfo = data.actInfo || {}

					senTrack('ActivityPageView', {
						'page_name': getCurrentTitle(0),
						'forward_source': getCurrentTitle(0),
						'page_level': '一级',
						'activity_id': this.lotteryId,
						'activity_name': this.lotteryInfo.name,
						'activity_type_first': '玩法活动',
						'activity_type_second': '幸运九宫格'
					})

					// 1 未开始  3 已结束
					if (this.isPop && (this.lotteryInfo.status == 1 || this.lotteryInfo.status == 3)) {
						this.$emit('close')
						return
					}
					//如果是页面展示大转盘，需要展示背景图片
					this.$emit('setBgImg', this.lotteryInfo.bgImg)
					this.$emit('setBgColor', this.lotteryInfo.bgColor)
					//1待开始  3已结束 弹框展示
					if (this.lotteryInfo.status == 1 || this.lotteryInfo.status == 3) {
						this.showStatus = true
					}

					if (this.lotteryInfo.bgColor) {
						this.lotteryInfo.bgColor = this.lotteryInfo.bgColor.split(';')
					}
					this.lotteryInfo.drawImg = this.lotteryInfo.drawImg || 'https://img.songlei.com/live/activity/nineGrid/drawImg.webp'
					this.lotteryInfo.dialImg = this.lotteryInfo.dialImg || 'https://img.songlei.com/live/activity/nineGrid/red-dialImg.svg'
					this.lotteryInfo.detail = this.lotteryInfo.detail || '暂无规则信息'
					// 在索引为4的位置添加空元素占位置
					if (data.prizeList && data.prizeList.length > 4) {
						data.prizeList.splice(4, 0, {})
						this.prizeList = data.prizeList
					}
					this.$nextTick(() => {
						this.$refs.wxSharePic.onCanvas()
						this.handlePostShare()
					})
				}
				//获取历史中奖用户
				this.getHistoryUsers()
				if (util.isUserLogin()) {
					//获取用户可转次数
					this.getActivityUserInfo()
					this.getShareInfo()
				} else {
					//负数表示 需要登录查看
					this.freeNum = -1
				}
			} else {
				uni.hideLoading()
				uni.showToast({
					title: '获取奖品失败',
					mask: true,
					icon: 'none'
				})
			}
		},

		async getHistoryUsers () {
			let res = await getHistoryUsers({
				actId: this.lotteryId
			})
			if (res && res.data && res.data.length > 0) {
				let pat = /(\d{3})\d*(\d{4})/
				res.data.forEach((item) => {
					this.historyList.push(`${item.phone.replace(pat, '$1****$2')}中了${item.prizeName}`)
				})
			}
			this.$refs.barrage.init(this.historyList)
		},

		// 获取该用户剩余抽奖次数
		async getActivityUserInfo () {
			let res = await getActivityUser({
				actId: this.lotteryId
			})
			if (res.ok) {
				let data = res.data
				if (data) {
					this.freeNum = data.num
				}
			} else {
				uni.hideLoading()
				uni.showToast({
					title: '获取剩余抽奖次数失败',
					mask: true,
					icon: 'none'
				})
			}
		},

		async getShareInfo () {
			let res = await getInviteInfo({
				actId: this.lotteryId
			})
			if (res.ok) {
				if (res.data) {
					this.sharePopInfo = {
						...res.data,
						actInfoHelpVO: res.data.helpVo
					}
				}
			}
		},

		// 抽奖开始之前
		handleDrawBefore () {
			console.log('抽奖开始之前')
			let flag = false
			// 还有免费数次或者剩余金币足够抽一次
			if (this.freeNum > 0) {
				flag = true
			} else {
				flag = false
				uni.showToast({
					title: '抽奖次数已用完',
					icon: 'none',
					duration: 2000
				})
			}
			return flag
		},

		// 本次抽奖开始
		handleDrawStart () {
			let that = this
			if (this.prizeing) return
			if (this.lotteryInfo && this.lotteryInfo.attendLimit) {
				const attendLimit = JSON.parse(this.lotteryInfo.attendLimit)
				if (attendLimit && attendLimit.sale > 0) {
					uni.showModal({
						title: '温馨提示',
						content: '每次抽奖需要消耗' + attendLimit.sale + '积分，是否继续?',
						showCancel: true,
						success (res) {
							if (res.confirm) {
								that.dealDrawStart()
							}
						}
					})
				} else {
					this.dealDrawStart()
				}
			} else {
				this.dealDrawStart()
			}
		},

		dealDrawStart () {
			this.prizeing = true
			// 更新免费次数
			this.freeNum--
			this.tryLotteryDraw()
		},

		// 尝试发起抽奖
		tryLotteryDraw () {
			console.log('旋转开始，获取中奖下标......')
			this.$refs.lotteryCom.handleActionStart()

			senTrack('ActivityClick', {
				'page_name': getCurrentTitle(0),
				'page_level': '一级',
				'activity_id': this.lotteryId,
				'activity_name': this.lotteryInfo.name,
				'activity_type_first': '玩法活动',
				'activity_type_second': '幸运九宫格'
			})
			//从后端获取中奖下标
			this.remoteGetPrizeIndex()
		},

		// 远程请求接口获取中奖下标
		remoteGetPrizeIndex () {
			getPrizeInfo(this.lotteryId)
				.then((res) => {
					if (res && res.data) {
						this.prizeInfo = res.data
						//因为第4个中心点不算入奖品
						this.prizeIndex = this.prizeInfo.sort < 4 ? this.prizeInfo.sort : this.prizeInfo.sort + 1
						console.log('本次抽中奖品 =>', this.prizeInfo.prizeName)
						// 场景类型(1:大转盘 2:订单支付 3: 商品预览)
						this.$refs.topMessage.setMessage(1, 5000) // 将数据传递给顶部信息提示组件，并设置持续时间为0ms
						if (res.data && res.data.actInfoHelpVO && res.data.actInfoHelpVO.helpUsers) {
							this.assistancelist = res.data.actInfoHelpVO.helpUsers || []
						}
						this.getShareInfo()

						senTrack('ActivityResult', {
							'page_name': getCurrentTitle(0),
							'page_level': '一级',
							'activity_id': this.lotteryId,
							'activity_name': this.lotteryInfo.name,
							'activity_type_first': '玩法活动',
							'activity_type_second': '幸运九宫格',
							'is_obtain_prize': this.prizeInfo.id ? true : false,
							'prize_name': this.prizeInfo.prizeName,
							'prize_id': this.prizeInfo.id,
						})
					}
				})
				.catch((error) => {
					//接口报错，旋转状态重置
					this.prizeing = false
				})
		},

		// 本次抽奖结束
		handleLuckEnd () {
			this.prizeing = false
			this.showPrize = true
			this.showType = 1
		},

		handleShowSharePop () {
			this.showPrize = true
			this.showType = 2
		},

		handleShowRule () {
			this.showRules = true
		},
		handleCloseRule () {
			this.showRules = false
		},
		handleShowPrizeList () {
			this.showPrizeList = true
		},

		handleClosePrizeList () {
			this.showPrizeList = false
		},

		handleClosePrize () {
			this.showPrize = false
			this.$refs.lotteryCom.freeLoop()
		},

		handleActionStart () {
			if (this.handleDrawBefore()) {
				this.handleDrawStart()
			}
		},

		shareMessage () {
			this.shares.title = this.lotteryInfo.name || '抽一次，有惊喜'
			this.shares.imageUrl = this.lotteryInfo.shareImg || this.shareImgSrc
			if (this.sharePopInfo && this.sharePopInfo.userPrizeId > 0 && this.sharePopInfo.actInfoHelpVO.isCanHelp == 1) {
				this.shares.path = 'pages/activity/assiatance/index?id=' + this.lotteryId
				this.shares.path += '&pid=' + this.sharePopInfo.userPrizeId
			} else {
				this.shares.path = '/pages/activity/nineGrid/index?id=' + this.lotteryId
			}
			return this.shares
		},

		handleShowPost () {
			this.showPostShare = true
		},

		handlePostShare () {
			const prizeList = this.getValidPrize()
			let posterConfig = {
				width: 750,
				height: 1030,
				backgroundColor: '#fff',
				debug: false,
				texts: [
					{
						x: 10,
						y: 780,
						text: '扫码参与瓜分亿元红包',
						fontSize: 40,
						color: '#f00'
					},
					{
						x: 10,
						y: 840,
						text: '邀请您来帮忙助力!',
						fontSize: 40,
						color: '#000'
					},
					{
						x: 500,
						y: 850,
						text: '扫描/长按识别!',
						fontSize: 30,
						color: '#B3B3B3'
					}
				],
				blocks: [],
				images: [
					{
						width: 730,
						height: 584,
						x: 10,
						y: 10,
						url: 'https://img.songlei.com/live/activity/nineGrid/default-share.jpg'
					},
					{
						width: 200,
						height: 200,
						x: 490,
						y: 614,
						url: null,
						qrCodeName: 'qrCodeName' // 二维码唯一区分标识
					}
				]
			}
			if (this.prizeList && this.prizeList[0]) {
				posterConfig.blocks.push({
					width: 232,
					height: 274,
					x: 20,
					y: 300,
					borderRadius: 30,
					backgroundColor: '#ffffff'
				})
				posterConfig.images.push({
					width: 232,
					height: 260,
					x: 20,
					y: 310,
					borderRadius: 30,
					url: this.prizeList[0].url
				})
			}
			if (this.prizeList && this.prizeList[1]) {
				posterConfig.blocks.push({
					width: 232,
					height: 274,
					x: 262,
					y: 300,
					borderRadius: 30,
					backgroundColor: '#ffffff'
				})
				posterConfig.images.push({
					width: 232,
					height: 260,
					x: 272,
					y: 310,
					borderRadius: 30,
					url: this.prizeList[1].url
				})
			}
			if (this.prizeList && this.prizeList[2]) {
				posterConfig.blocks.push({
					width: 232,
					height: 274,
					x: 502,
					y: 300,
					borderRadius: 30,
					backgroundColor: '#ffffff'
				})
				posterConfig.images.push({
					width: 232,
					height: 260,
					x: 512,
					y: 310,
					borderRadius: 30,
					url: this.prizeList[2].url
				})
			}
			let userInfo = uni.getStorageSync('user_info')
			console.log('userInfo==>', userInfo)
			if (userInfo && userInfo.headimgUrl) {
				//如果有头像则显示
				posterConfig.images.push({
					width: 121,
					height: 121,
					x: 10,
					y: 614,
					borderRadius: 120,
					url: userInfo.headimgUrl
				})
				posterConfig.texts.push({
					x: 150,
					y: 664,
					text: userInfo.nickName,
					fontSize: 32,
					color: '#000'
				})
				posterConfig.texts.push({
					x: 150,
					y: 710,
					text: userInfo.phone,
					fontSize: 32,
					color: '#000'
				})
			}

			if (this.sharePopInfo && this.sharePopInfo.userPrizeId > 0 && this.sharePopInfo.actInfoHelpVO.isCanHelp == 1) {
				this.sharePostParams = {
					scene: 'pid=' + this.sharePopInfo.userPrizeId,
					page: 'pages/activity/assiatance/index',
					posterConfig: posterConfig
				}
			} else {
				this.sharePostParams = {
					scene: 'id=' + this.lotteryId,
					page: 'pages/activity/nineGrid/index',
					posterConfig: posterConfig
				}
			}
		},

		// 获取有效的奖品
		getValidPrize () {
			let result = []
			if (this.prizeList && this.prizeList.length > 0) {
				result = this.prizeList.filter(function (item) {
					return item.objType != 5
				})
			}
			return result
		}
	}
}
</script>

<style lang="scss" scoped>
.almost-lottery {
	flex: 1;
}

.barrage {
	height: 50rpx;
	overflow: hidden;
	display: flex;
	align-items: center;
	margin-top: 61rpx;
	margin-left: 100rpx;
}

.almost-lottery__head {
	position: relative;
	width: 100%;
	height: 290rpx;

	.start-time {
		font-size: 30rpx;
		font-weight: 700;
		color: #620053;
		position: absolute;
		top: 260rpx;
		left: 50%;
		transform: translateX(-50%);
		text-align: center;
		border-radius: 27rpx;
		height: 54rpx;
		line-height: 54rpx;
		padding: 0 30rpx;
	}

	.prize-btn,
	.rules-btn,
	.share-btn {
		background: rgba(0, 0, 0, 0.2);
		border-top-left-radius: 20rpx;
		border-bottom-left-radius: 20rpx;
		position: absolute;
		right: 0;
		top: 136rpx;
		color: #ffffff;
		font-size: 26rpx;
		padding: 4rpx 20rpx;
		z-index: 1;
	}

	.prize-btn {
		top: 198rpx;
	}

	.rules-btn {
		top: 238rpx;
	}

	.share-btn {
		top: 256rpx;
	}
}

.lottery-base {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 77rpx;
}

.lottery-btn {
	width: 432rpx;
	height: 132rpx;
}

.almost-lottery__count {
	color: #ffffff;
	font-size: 24rpx;
	margin-top: 16rpx;
}

.almost-lottery__wheel {
	text-align: center;
	margin-top: 47rpx;
}
</style>
