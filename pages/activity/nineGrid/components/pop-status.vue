<template>
	<!-- 抽奖 popup -->
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''">
		<view class="cu-dialog" style="width: 750rpx;background-color: transparent;">
			<view style="justify-content: center;display: flex;flex-direction: column;align-items: center;">
				<view class="content">
					<image style="width: 160rpx; "
						:src="status==1?'https://img.songlei.com/live/activity/lottery/no-start.png':'https://img.songlei.com/live/activity/lottery/end.png'"
						mode="widthFix" />
					<view v-if="status==1"
						style="justify-content: center;display: flex;flex-direction: column;align-items: center;">
						<view class="time-label">活动时间</view>
						<view class="time-label">开始时间：{{startTime||''}}</view>
						<view class="time-label">结束时间：{{endTime||''}}</view>
					</view>
					<image style="width: 318rpx; height: 90rpx; margin-top: 30rpx; "
						src="https://img.songlei.com/live/activity/lottery/go-home.png" fit="cover"
						@tap.stop="goHome" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'pop-rules',
		props: {
			startTime: {
				type: String,
				default: ''
			},
			endTime: {
				type: String,
				default: ''
			},
			// 1 未开始  3 已结束
			status: {
				type: String | Number,
				default: ''
			}

		},
		data() {
			return {
				showModal: true,
			}
		},

		methods: {
			goHome() {
				uni.reLaunch({
					url: "/pages/home/<USER>"
				})
			},
		},

	}
</script>

<style lang="scss" scoped>
	.content {
		width: 584rpx;
		background: #FFFFFF;
		border-radius: 38rpx;
		padding: 34rpx;
		justify-content: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.time-label {
			font-size: 28rpx;
			font-weight: bold;
			color: #000000;
			line-height: 43rpx;
			padding-top: 20rpx;
		}
	}
</style>