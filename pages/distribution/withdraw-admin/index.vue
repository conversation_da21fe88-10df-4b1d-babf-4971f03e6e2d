<template>
  <view class="">
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">提现管理</block>
    </cu-custom>

    <!--提现状态框-->
    <view v-if="bankInfo&&bankInfo.userWithdrawStatus" class="margin-top">
      <view
        class="padding-xs margin-top-xs text-center"
        style="width:95%;height:571rpx;background: #FFFFFF;margin:20rpx auto;border-radius:15rpx;"
      >
        <view style="margin:0 auto; margin-top:76rpx">
          <view style="width: 161upx; height: 161upx;margin:0 auto;">
            <image
              style="width:100%;height: 100%;border-radius:15rpx;"
              :src="bankInfo.userWithdrawStatus=='0'?'https://img.songlei.com/distribution/withdrawal.png':(bankInfo.userWithdrawStatus=='2'?'https://img.songlei.com/distribution/error.png':'https://img.songlei.com/gift/answer.png')" 
              mode="aspectFill" 
              >
            </image>
          </view>

          <view v-if="bankInfo.userWithdrawStatus=='1'" class="text-xl text-bold text-center padding-sm" style="width: 100%;">
            {{bankInfo.applyAmount}}元已提现到您的银行卡
          </view>
          <view v-if="bankInfo.userWithdrawStatus=='0'||bankInfo.userWithdrawStatus=='2'" class="text-xl text-bold text-center padding-sm" style="width: 100%;">
            {{bankInfo.userWithdrawStatus=='0'?'提现中':'提现失败'}}
          </view>
          <view v-if="bankInfo.userWithdrawStatus!=='1'&&bankInfo.reason" class="text-xsm text-center" style="width: 100%;">
            {{bankInfo.reason}}
          </view>

          <view class="margin" style="width: 475rpx;height: 1rpx;border-bottom: 4rpx dashed #F5F5F7;margin:20rpx auto;"></view>
        </view>  
        <view class="flex justify-center" @click="toPage('/pages/distribution/distribution-center/index')">
          <view style="margin-top: 20rpx;color: #FFFFFF;" class="cu-btn round login_padding">
            返回
          </view>
        </view>
      </view>
    </view>

    <!-- 填写提现内容 -->
    <view v-else>
      <view class="padding-tb padding-lr-sm box-center">
        <view class="text-bold text-df">提现至银行卡：</view>
        <view v-if="bankInfo.userWithdrawBankAccount">
          <text class="text-bold text-xml">{{ bankInfo.userWithdrawBankAccount.bankCardNo | onlyFourBank}}</text>
          <text class="text-df">（{{bankInfo.userWithdrawBankAccount.bankName || 'XXXX' }}）</text>
        </view>
      </view>
  
      <view class="margin-top-sm bg-white box-center padding-bottom-lg">
        <view class="padding-tb-sm withdrawable">
          <text class="text-bold text-df" style="color: #000000 !important;">可提现金额：</text>
          <text class="text-price text-df"></text>{{bankInfo.commissionTotalAmount || '0'}}
        </view>
  
        <view class="cu-form-group margin-top-sm font-weight flex justify-start">
          <text class="text-black text-df" style="color: #000000 !important;">提现金额：</text>
  
          <view class="flex withdrawable-input padding-left-sm">
            <view class="text-price"></view>
            <input
              v-model="userwithdrawrecord.applyAmount"
              :min="0"
              type="number"
              style=" height: 80rpx;line-height: 80rpx;"
              :max="withdrawAmount"
              placeholder="请输入提现金额"
              placeholder-class="input-text"
              name="input"
            >
          </view>
  
          <view @click="whole()" class="whole margin-left-xs">
            全部提现
          </view>
        </view>
  
        <view class="lines-light-grey text-center text-xsm">最低提现金额:￥{{bankInfo.commissionMinAmount || '0'}}</view>
  
        <view class="flex justify-center">
          <button
            class="cu-btn margin-tb-sm lg login_padding"
            :class="'bg-'+theme.themeColor"
            @click="submit"
          >提现</button>
        </view>
  
        <view class="text-center text-xsm ">
          提示：2-3个工作日到账
        </view>
      </view>
    </view>

    <!-- 提现计划 -->
    <view v-if="WithdrawalPlanList&&WithdrawalPlanList.length>0" class="margin-tb bg-white box-center" >
      <view class="font-weight padding-top margin-left" style="color: #000000 !important;">提现计划:</view>
      <scroll-view scroll-y="true" style="height:506rpx;" @scrolltolower="getReachBottom()">
        <view v-for="(item,index) in WithdrawalPlanList" :key="index" 
        class=" flex justify-between margin-top-xs text-sm padding-lr ">
					<text class="text-sm">{{item.month}}</text>
					<text class="text-sm">可提现￥{{item.monthlyCommission}}</text>
				</view>
        <view :class="'cu-load bg-white-black ' + (loadmore?'loading':'over')"></view>
      </scroll-view>
    </view>

    <!-- 礼品卡验密弹框 -->
    <view v-if="modalGiftPwd" :class="'cu-modal bottom-modal ' + modalGiftPwd" >
      <view class="cu-dialog bg-white-black" @tap.stop :style="{height: '65%'}">
				<view class="flex margin margin-bottom-xl margin-top-sm">
          <view class="text-xsm text-left" style="width: 100%;">输入提现密码</view>
          <view class="cuIcon-close line-light-grey" @tap.stop="hideModalGiftPwd()"></view>
        </view>

        <view class="padding-top">
          <code-page @change="code($event)" :passwordShow="true"/>
        </view>

        <view @tap="navTo()" class="flex justify-end margin margin-top-lg">
          <view class="text-xsm line-light-grey">忘记密码</view>
          <view class="cuIcon-right line-light-grey"></view>
        </view>

        <view class="flex justify-center">
          <button
            class="cu-btn margin-tb-sm lg login_padding"
            :class="'bg-'+theme.themeColor"
            @click="confirm()"
          >确认</button>
        </view>
			</view>
		</view>

    <!--绑定银行卡弹框倒计时  -->
    <view :class="'cu-modal ' + modalDialog" @tap.stop>
      <view class="cu-dialog">
        <view style="margin: 0rpx 87rpx 0 87rpx;background-color: #ffffff;border-radius: 20rpx;">
          <view style="display: flex; flex-direction: column; align-items: center">
            <image class="margin-top-xl margin-bottom" style="width: 161upx; height: 161upx" mode="aspectFill"
                src="https://img.songlei.com/gift/answer.png">
            </image>
          
            <view class="text-center">
              <view class="text-xsm text-black text-center">银行卡绑定成功</view>
              
              <view class="margin">
                <button @click="confirmModalDialog()" style="width: 317rpx;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%); color: #FFF;" class="cu-btn round">
                  关闭({{msgTime}})
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'
const numberUtil = require("utils/numberUtil.js");
import codePage from "@/components/verification-code/index.vue";
import {getBankDistributionTotalAmount,userwithdrawrecord,withdrawalPlanPage} from "@/pages/distribution/api/distribution.js"
import {passwordCheck} from '@/api/gift';
import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"
  const MSGTIME = 5;
export default {
  mixins: [navigateUtil],
  components: {
    codePage
  },

  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      bankInfo:{},//银行信息
      userwithdrawrecord:{//提现输入
        withdrawType:'1',
        applyAmount:'',
      },
      modalGiftPwd:'',//验密弹框
      pwd:'',//密码
      loadmore: true,
      page: {
          current: 1,
          size:10,
        },
      WithdrawalPlanList:[],//提现计划列表
      modalDialog: '',//绑定银行卡成功弹框
      msgTime: MSGTIME,//弹框倒计
      time: null  // 定时器名称 
    }
  },

  onLoad (e) {
    if (e) {
      if (e.show) {
        this.modalDialog = e.show;
        this.countdown()
      }
    }
  },

  onShow() {
    this.getBankInfo()
    this.refresh()
  },

  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框
    uni.hideNavigationBarLoading(); // 停止下拉动作
    uni.stopPullDownRefresh();
  },

  methods: {
    //关闭绑定提示框
    confirmModalDialog(){
      this.msgTime = '';
      this.modalDialog= '';
    },

    //倒计时
    countdown(){
      let that = this
      this.msgTime= 5;
      clearInterval(this.time)
      this.time = setInterval(() => {
        that.msgTime--
        if (that.msgTime == 0) {
          that.msgTime = '';
          that.modalDialog= '';
          clearInterval(this.time)
          return
        }
      }, 1000)
    },

    //下拉加载更多
    getReachBottom () {
      if (this.loadmore) {
        this.page.current = this.page.current + 1;
        this.getWithdrawalPlanPage();
      }
    },

    //提现计划
    getWithdrawalPlanPage(){
      withdrawalPlanPage(Object.assign({},this.page)).then((res)=>{
        console.log("getWithdrawalPlanPage==res==>",res);
        if (res.data) {
          if (res.data.records) {
            let WithdrawalPlanList=res.data.records
          
            this.WithdrawalPlanList = [...this.WithdrawalPlanList, ...WithdrawalPlanList];
            if (WithdrawalPlanList.length < this.page.size) {
              this.loadmore = false;
            }
          }
        }
      })
    },

    //重置
    refresh () {
      this.loadmore = true;
      this.WithdrawalPlanList = [];
      this.page.current = 1;
      this.getWithdrawalPlanPage();
    },

    //忘记密码
		navTo(){
			// 忘记 
      uni.navigateTo({
        url: `/pages/distribution/password/index?source=admin`
      })
		},

    //确认密码
    confirm(){
      this.validatePwd()
    },

    //输入密码
    async	code(val){
			console.log("val",val);
      let that = this
			if (val) {
				that.pwd = val;
			}
		},

    //验密
    validatePwd(){
      passwordCheck(Object.assign({password:this.pwd})).then(res=>{
					console.log("passwordCheck=>",res);
					if (res.code == 0) {
            this.withdrawrecord()
					}
				}).catch((e)=>{
					console.log("ee",e);
				})
    },

    // 关闭密码支付弹框
    hideModalGiftPwd(){
      this.modalGiftPwd = '';
      // this.pwd = '';
    },

    //全部提现
    whole(){
      if (this.bankInfo) {
        this.userwithdrawrecord.applyAmount = this.bankInfo.commissionTotalAmount
      }
    },

    //查询银行卡可用余额 最低余额 信息
    async  getBankInfo(){
      try {
        const res = await getBankDistributionTotalAmount()
        console.log("res",res);
        if (res.data) {
          this.bankInfo = res.data
        }
      } catch (error) {
        console.log("error");
      }
    },

  //提现
    submit () {
      if (this.userwithdrawrecord.applyAmount < this.bankInfo.commissionMinAmount) {
        uni.showToast({
          title: '申请金额必须大于最低提现金额' + this.bankInfo.commissionMinAmount,
          icon: 'none',
          duration: 3000
        })
        return;
      }

      if (this.userwithdrawrecord.applyAmount < 0) {
        uni.showToast({
          title: '申请金额必须大于0',
          icon: 'none',
          duration: 3000
        })
        return;
      }

      if (this.userwithdrawrecord.applyAmount > this.bankInfo.commissionTotalAmount) {
        uni.showToast({
          title: '申请金额不能超过可提现金额',
          icon: 'none',
          duration: 3000
        })
        return;
      }
      this.modalGiftPwd = 'show'
      
    },

    // 申请提现
    withdrawrecord(){
      userwithdrawrecord(this.userwithdrawrecord).then(res => {
          if (res.data) {//是分销员
            // uni.showToast({
            //   icon: 'none',
            //   title: '申请成功，可在提现记录中查看进度哦~'
            // })
            this.hideModalGiftPwd();
            this.getBankInfo()

            // uni.redirectTo({
            //   url: '/pages/distribution/distribution-withdraw-list/index'
            // });
          }
        });
    }
  }
}
</script>

<style>
.box-center{
  width: 95%;
  margin: 0 auto;
  margin-top: 30rpx;
  border-radius: 15rpx;
  background-color: #FFFFFF;
}
.withdrawable{
  width: 95%;
  margin: 0 auto;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.font-weight {
  font-weight: 300;
}

.input-box {
  text-align: right;
  /* color: # !important; */
}

.input-text {
  /* height: 80rpx;
  line-height: 80rpx; */
  color: rgb(177, 183, 188);
}
.withdrawable-input{
  width: 318rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #FAFAFA;
  border-radius: 40rpx;
}
.whole{
  width: 178rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #FFF1E1;
  color: #AC785E;
  border-radius: 40rpx;
}
.login_padding {
  width: 343rpx;
  height: 80rpx;
  margin-top: 20rpx;
  background: linear-gradient(90deg, #FF8362 0%, #FF4F00 100%);
  /* border-radius: 40rpx; */
}
</style>
