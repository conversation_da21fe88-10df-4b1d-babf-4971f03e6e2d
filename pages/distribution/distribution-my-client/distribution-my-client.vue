<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{title}}</block>
		</cu-custom>


		<!-- 我的客户筛选列表 -->
		<view class="justify-center bg-white solid-bottom  padding-top-xs">
			<view class="flex justify-between">
				<view class="flex-sub margin-xs  flex justify-center">
					<view class="grid text-center text-xdf text-black flex align-center" @tap="sortHandle" data-type="sumSalePrice">消费金额
						<view class="margin-left-xs" style="font-size: 32rpx; line-height: 12rpx; ">
							<view
								:class="'cuIcon-triangleupfill ' + (ascs == 'sumSalePrice' ? 'text-'+theme.themeColor : '')"
								data-type="sumSalePrice"></view>
							<view
								:class="'cuIcon-triangledownfill ' + (descs == 'sumSalePrice' ? 'text-'+theme.themeColor : '')"
								data-type="sumSalePrice"></view>
						</view>
					</view>
				</view>

				<view class="flex-sub margin-xs  flex justify-center">
					<view class="grid text-center text-xdf text-black  flex align-center" @tap="sortHandle" data-type="sumQuantity">购买件数
						<view class="margin-left-xs" style="font-size: 32rpx; line-height: 12rpx;">
							<view
								:class="'cuIcon-triangleupfill ' + (ascs == 'sumQuantity' ? 'text-'+theme.themeColor : '')"
								data-type="sumQuantity"></view>
							<view
								:class="'cuIcon-triangledownfill ' + (descs == 'sumQuantity'? 'text-'+theme.themeColor : '')"
								data-type="sumQuantity"></view>
						</view>
					</view>
				</view>

				<view class="flex-sub margin-xs  flex justify-center">
					<view class="grid text-center text-xdf text-black  flex align-center" @tap="sortHandle" data-type="sumCommission">佣金金额
						<view class="margin-left-xs" style="font-size: 32rpx; line-height: 12rpx;">
							<view
								:class="'cuIcon-triangleupfill ' + (ascs == 'sumCommission' ? 'text-'+theme.themeColor : '')"
								data-type="sumCommission"></view>
							<view
								:class="'cuIcon-triangledownfill ' + (descs == 'sumCommission'? 'text-'+theme.themeColor : '')"
								data-type="sumCommission"></view>
						</view>
					</view>
				</view>

			</view>
		</view>

		<view class="myclient_list">
			<view class="myclient_item" v-for="(item,index) in list" :key="index" @click="handleItem(item)">
				<image v-if="index+1=='1'" src="https://slshop-file.oss-cn-beijing.aliyuncs.com/fenxiao/1.png" mode=""
					class="header_one"></image>
				<image v-if="index+1=='2'" src="https://slshop-file.oss-cn-beijing.aliyuncs.com/fenxiao/2.png" mode=""
					class="header_one"></image>
				<image v-if="index+1=='3'" src="https://slshop-file.oss-cn-beijing.aliyuncs.com/fenxiao/3.png" mode=""
					class="header_one"></image>

				<view class="">
					<image :src="item.headimgUrl " mode="aspectFit"
						style="width: 100rpx;height: 100rpx;"></image>
				</view>
				<view class="item_right">
					<view class="">
						昵称：{{ item.nickName }}
					</view>
					<view>
						累计消费: <text style="color:red">￥{{ item.sumSalePrice }}</text>
					</view>
					<view class="">
						购买件数: {{item.sumQuantity}}
					</view>
					<view class="">
						累计返佣: ￥{{ item.sumCommission }}
					</view>
				</view>
				<view class="">
					<image src="https://slshop-file.oss-cn-beijing.aliyuncs.com/fenxiao/jt.png" mode=""
						style="width: 14rpx;height: 22rpx;"></image>
				</view>
			</view>
			<view :class="'cu-load ' + (loadmore?'loading':'over')"></view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import {
		distributionCustom
	} from "@/pages/distribution/api/distribution.js";
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				title: "我的客户",
				page: {
					current: 1,
					size: 10,
				},
				list: [],
				loadmore: true,
				descs: 'sumSalePrice',
				ascs: '',
			};
		},
		onLoad() {
			app.initPage().then(res => {
				this.getData();
			})
		},
		methods: {
			handleItem(item){
				uni.navigateTo({
					url:"/pages/distribution/distribution-order-list/index?customerId="+item.customerId
				})
			},
			// tab筛选
			sortHandle(e) {
				let type = e.target.dataset.type;
				switch (type) {
					//价格
					case 'sumSalePrice':
						if (this.ascs == '' && this.descs == '' || this.descs == 'sumSalePrice') {
							this.ascs = 'sumSalePrice';
							this.descs = '';
						} else {
							this.descs = 'sumSalePrice';
							this.ascs = '';
						}
						break;
					case 'sumQuantity':
						if (this.ascs == '' && this.descs == '' || this.descs == 'sumQuantity') {
							this.ascs = 'sumQuantity';
							this.descs = '';
						} else {
							this.descs = 'sumQuantity';
							this.ascs = '';
						}
						break;
					case 'sumCommission':
						if (this.ascs == '' && this.descs == '' || this.descs == 'sumCommission') {
							this.ascs = 'sumCommission';
							this.descs = '';
						} else {
							this.descs = 'sumCommission';
							this.ascs = '';
						}
						break;
				}
				this.refresh();
			},

			refresh() {
				this.page.current = 1;
				this.getData();
			},

			getData() {
				const params = {
					...this.page
				};
				if (this.descs) {
					params.descs = this.descs;
				} else if (this.ascs) {
					params.ascs = this.ascs;
				}
				distributionCustom(params).then((res) => {
					this.loading = false;
					if (res.data && res.data.records) {
						if (this.page.current == 1) {
							this.list = [...res.data.records];
						} else {
							this.list = [...this.list, ...res.data.records];
						}
						if (this.list.length >= res.data.total) {
							this.loadmore = false;
						}
					}
				}).catch(e => {
					this.loadmore = false;
				});
			}
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.getData();
			}
		},
	};
</script>

<style scoped>
	.myclient_list {
		margin-top: 20rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
	}

	.myclient_item {
		display: flex;
		align-items: center;
		/* border: 1rpx solid red; */
		padding: 20rpx;
		/* border: ; */
		border-radius: 20rpx;
		position: relative;
		background: white;
		margin-bottom: 20rpx;
	}

	.item_right {
		margin-left: 20rpx;
		width: 530rpx;
	}

	.header_one {
		position: absolute;
		left: 4rpx;
		top: 0;
		width: 50rpx;
		height: 50rpx;
	}
</style>