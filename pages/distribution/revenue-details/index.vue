<template>
  <view class="">
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">收入明细</block>
    </cu-custom>

    <!-- 明细内容 -->
    <view v-if="dataList">
      <view v-for="(item, index) in dataList"
        :key="index" class="padding-tb padding-lr-sm box-center">
        <view class="flex justify-between">
          <view class="flex justify-start">
            <view class="text-black text-df text-bold">订单编号：</view>
            <view class="margin-left-xs text-smx">{{item.orderNo}}</view>
          </view>
          <view class="text-xs text-bold">
            {{item.refountStatus=='0'?'未退款':(item.refountStatus=='1'?'退款':'部分退款')}}
          </view>
        </view>

        <view class="flex justify-start">
          <view class="text-black text-df text-bold">商品名称：</view>
          <view class="margin-left-xs text-smx">{{item.spuName}}</view>
        </view>

        <view class="flex justify-start">
          <view class="text-black text-df text-bold">商品数量：</view>
          <view class="margin-left-xs text-smx">{{item.quantity}}</view>
        </view>

        <view class="flex justify-start">
          <view class="text-black text-df text-bold">下单时间：</view>
          <view class="margin-left-xs text-smx">{{item.createTime}}</view>
        </view>

        <view class="flex justify-between">
          <view class="flex justify-start">
            <view class="text-black text-df text-bold">下单金额：</view>
            <view class="margin-left-xs text-smx">
              <text class="text-price text-df"></text>
              {{item.paymentPrice}}
            </view>
          </view>
        
          <view class="flex justify-start">
            <view class="text-black text-df text-bold">分佣金额：</view>
            <view class="margin-left-xs text-red text-df text-bold">
              <text class="text-price text-df text-red"></text>
              {{item.commissionNoSettlement}}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'
const numberUtil = require("utils/numberUtil.js");
import codePage from "@/components/verification-code/index.vue";
import {getWithdrawableList} from "@/pages/distribution/api/distribution.js";
import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"
export default {
  mixins: [navigateUtil],
  components: {
    codePage
  },

  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      page: {
          current: 1,
          size:10,
        },
      dataList:[],//提现计划列表
    }
  },

  onLoad (e) {
  },

  onShow() {
    this.refresh()
  },

  //下拉加载更多
  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.getPage();
    }
  },

  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框
    uni.hideNavigationBarLoading(); // 停止下拉动作
    uni.stopPullDownRefresh();
  },

  methods: {
    //提现计划
    getPage(){
      getWithdrawableList(Object.assign({},this.page)).then((res)=>{
        console.log("getPage==res==>",res);
        if (res.data) {
          if (res.data.records) {
            let dataList=res.data.records
            this.dataList = [...this.dataList, ...dataList];
            if (dataList.length < this.page.size) {
              this.loadmore = false;
            }
          }
        }
      })
    },

    //重置
    refresh () {
      this.loadmore = true;
      this.dataList = [];
      this.page.current = 1;
      this.getPage();
    },
  }
}
</script>

<style>
.box-center{
  width: 95%;
  margin: 0 auto;
  margin-top: 20rpx;
  border-radius: 15rpx;
  background-color: #FFFFFF;
}
</style>
