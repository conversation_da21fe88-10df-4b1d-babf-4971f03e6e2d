<template>
	<view class="">
		<cu-custom bgColor="#bc8468" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">分销首页</block>
		</cu-custom>

		<!-- 如果没有成为分销员 -->
		<view style="position: absolute; width: 100%;" :style="{top:(CustomBar+StatusBar+menuHeight)+'rpx'}">
			<view v-if="!distribution">
				<!-- 指定分销 -->
				<view class="distribution-image-bg" v-if="distributionConfig.distributionModel == 1"
					:class="'bg-' + theme.themeColor">
					<image class="distribution-image" src="https://img.songlei.com/live/message/distribution-1.png">
					</image>

					<view class="text-center distribution-text">
						<view class="text-xl font-weight text-yellow">
							您当前还未获得分销权限
						</view>

						<view class="margin-top text-df font-weight text-yellow text-height">
							请联系商城管理员即可开启分销权限
							<br />
						</view>

						<navigator open-type="navigateBack" class="cu-item arrow margin-top-xl" hover-class="none">
							<button class="cu-btn back-home text-red">点击返回</button>
						</navigator>
					</view>
				</view>
				<!-- 人人分销 -->
				<view class="distribution-image-bg" v-else-if="distributionConfig.distributionModel == 2"
					:class="'bg-' + theme.themeColor">
					<image class="distribution-image" src="https://img.songlei.com/live/message/distribution-1.png">
					</image>

					<view class="text-center distribution-text">
						<view class="text-xl font-weight text-yellow">
							您当前还未获得分销权限
						</view>
						<view class="margin-top text-df font-weight text-yellow text-height">
							分享商品链接或活动海报给好友
							<br />
							好友注册并登录商城后成功支付订单
							<br />
							即可自动获得分销权限
						</view>

						<navigator open-type="navigateBack" class="cu-item arrow margin-top-xl" hover-class="none">
							<button class="cu-btn back-home text-red">点击返回</button>
						</navigator>
					</view>
				</view>
				<!-- 满额分销 -->
				<view class="distribution-image-bg" style="" v-else-if="distributionConfig.distributionModel == 3"
					:class="'bg-' + theme.themeColor">
					<image class="distribution-image" src="https://img.songlei.com/live/message/distribution-1.png">
					</image>

					<view class="text-center distribution-text">
						<view class="text-xl font-weight text-yellow">
							您当前还未获得分销权限
						</view>

						<view class="margin-top text-df font-weight text-yellow text-height">
							您当前在商城的消费金额为
							<text class="text-price">
								{{ userConsumptionRecord.totalAmount }}
							</text>
							元
							<br />
							当消费满
							<text class="text-price">
								{{ distributionConfig.fullAmount }}
							</text>
							元，即可获得分销权限
							<br />
							快去商城购置商品获取分销权限吧！
						</view>

						<navigator open-type="navigateBack" class="cu-item arrow margin-top-xl" hover-class="none">
							<button class="cu-btn back-home text-red">点击返回</button>
						</navigator>
					</view>
				</view>
			</view>
			<!-- 如果是分销员 -->
			<view v-else-if="distribution">
				<view class="money-bg">
					<view class="flex justify-between" style="padding: 40rpx 40rpx 0;">
						<view>
							<view style="font-size: 30rpx;">
								<text style="font-weight: bold;">可提现佣金</text>（元）
							</view>
							<view style="margin-top: 20rpx;line-height: 80rpx; color: #EE102B;">￥<text
									style="font-size: 90rpx;">{{distribution.commissionNoWithdrawable | filterInt }}</text>
								<text
									style="font-size: 36rpx;">{{distribution.commissionNoWithdrawable | filterSmall}}</text>
							</view>
						</view>
						<view>
							<view style="display: flex;justify-content: flex-end; align-items: center; padding-right: 10rpx;"
							@click="toPage('/pages/distribution/revenue-details/index')">
								收入明细
								<image class="arrow-right" src="https://img.songlei.com/distribution/arrow-right2.png">
								</image>
							</view>
							<!-- <navigator style="display: flex;justify-content: flex-end; align-items: center;"
								url="/pages/distribution/distribution-withdraw-list/index" hover-class="none">
								收入明细
								<image class="arrow-right" src="https://img.songlei.com/distribution/arrow-right2.png">
								</image>
							</navigator> -->
							<!-- <navigator class="cu-item" url="/pages/distribution/distribution-withdraw/index"
								hover-class="none" style="padding-top: 20rpx;line-height: 80rpx">
								<button class="cu-btn" 
									style="color: white;width: 215rpx;height: 66rpx;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);border-radius: 33rpx;">
									<text> 提现 </text>
								</button>
							</navigator> -->

							<view class="cu-item" style="padding-top: 20rpx;line-height: 80rpx" @click="withdrawal()">
								<button class="cu-btn"
									style="color: white;width: 215rpx;height: 66rpx;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);border-radius: 33rpx;">
									<text> 提现 </text>
								</button>
							</view>
						</view>
					</view>
					<view class="module-layout" style="margin: 0 26rpx 26rpx; padding:26rpx;">
						<view class="flex justify-between align-center module-line" style="font-size:28rpx;"
							@click="Commissionsettled(1)">
							<view class="module-head">
								<view>
									<view class="module-title">待结算佣金</view>
									<view class="module-tip">分销商品订单待结算</view>
								</view>

							</view>
							<view class="flex align-center">
								<view>
									<text class="price-signal">￥</text>
									<text class="price">{{distribution.commissionNoSettlement}}</text>
								</view>
								<image class="arrow-right" src="https://img.songlei.com/distribution/arrow-right2.png">
								</image>
							</view>
						</view>

						<view class="flex justify-between align-center module-line"
							style="margin-top: 30rpx;font-size:28rpx;" @click="Commissionsettled(2)">
							<view class="module-head">
								<view>
									<view class="module-title">当月预估佣金</view>
									<view class="module-tip">当前月份分销商品预计销售产生的佣金</view>
								</view>

							</view>
							<view class="flex align-center">
								<view>
									<text class="price-signal">￥</text>
									<text class="price">{{distribution.commissionMonthtEstimate}}</text>
								</view>
								<image class="arrow-right" src="https://img.songlei.com/distribution/arrow-right2.png">
								</image>
							</view>
						</view>

						<view class="flex justify-between align-center" style="margin-top: 30rpx;font-size: 24rpx;">
							<view class="flex flex-direction align-center">
								<view style="height: 40rpx; line-height: 40rpx; font-size: 28rpx;">累计佣金
								</view>
								<view class="price-margin">
									<text class="price-signal">￥</text>
									<text class="price">{{distribution.commissionTotal}}</text>
								</view>
							</view>
							<view class="line" style="height: 80rpx;"></view>
							<view class="flex flex-direction  align-center" @click="toPage('/pages/distribution/withdraw-list/index?type=1')">
								<view style="height: 40rpx; line-height: 40rpx; font-size: 28rpx;">
									已提现佣金
									<image class="arrow-right"
										src="https://img.songlei.com/distribution/arrow-right2.png">
									</image>
								</view>
								<view class="price-margin">
									<text class="price-signal">￥</text>
									<text class="price">{{distribution.commissionWithdrawal}}</text>
								</view>
							</view>
							<view class="line" style="height: 80rpx;"></view>
							<view class="flex flex-direction  align-center"  @click="toPage('/pages/distribution/withdraw-list/index?type=0')">
								<view style="height: 40rpx; line-height: 40rpx; font-size: 28rpx;">提现中佣金
									<image class="arrow-right"
										src="https://img.songlei.com/distribution/arrow-right2.png">
									</image>
								</view>
								<view class="price-margin">
									<text class="price-signal">￥</text>
									<text class="price">{{distribution.commissionWithDrawabling}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="module-layout" style="padding-bottom: 0rpx;">
					<view class="module-head module-line">
						<view class="module-title">常用工具</view>
					</view>
					<swiper class="swiper square-dot" style="height: 200rpx;width: 100%;" indicator-color="#FFC0CB"
						indicator-active-color="#F92C19" indicator-dots :autoplay="false">
						<swiper-item v-for="(item,index) in menuList" :key="index">
							<view class="flex align-center" style="padding:20rpx 10rpx  0 10rpx;">
								<view style="width: 145rpx;" v-for="(menuItem,menuIndex) in menuList[index]"
									:key="menuIndex">
									<navigator v-show="(menuItem.label!='我的银行卡' || (menuItem.label=='我的银行卡' && distribution.showBankCard))&&(menuItem.label!='交易密码' || (menuItem.label=='交易密码' && distribution.showPassWord))" 
										:url="menuItem.url"
										class="flex flex-direction align-center" 
										hover-class="none">
										<image :src="menuItem.icon" style="height: 70rpx; width:70rpx">
										</image>
										<view style="margin-top:16rpx">{{menuItem.label}}</view>
									</navigator>
								</view>
							</view>
						</swiper-item>
					</swiper>
				</view>
				
				  <!-- 优先级低，先不用 -->
				<view class="module-layout" style="margin-top: 30rpx;">
					<view class="module-head module-line">
						<view class="module-title">分销数据</view>
					</view>
					<view class="flex margin-tb">
						<view class="flex flex-direction align-center" style="flex: 1;">
							<view class="tab-title text-xdf">累计成交金额</view>
							<view class="tab-num" style="padding: 0">{{dataList.transactionAmountTotal}}</view>
						</view>
						<view class="flex flex-direction align-center" style="flex: 1;">
							<view class="tab-title text-xdf">累计成交订单数</view>
							<view class="tab-num" style="padding: 0">{{dataList.orderNumTotal}}</view>
						</view>
						<view class="flex flex-direction align-center" style="flex: 1;">
							<view class="tab-title text-xdf">累计成交人数</view>
							<view class="tab-num" style="padding: 0">{{dataList.numTotal}}</view>
						</view>
					</view>
					<!--  -->
					<view>
						<view class="cu-bar bg-white" style="justify-content: flex-start">
							<view class="action tab-item" v-for="item in dataListLabel" :key="item.key" :data-key="item.key"
								@click="handleSelect">
								<text :class="currentDatakey == item.key ? 'tab-select' : 'tab-default'"> {{item.label}}
								</text>
							</view>
						</view>
						<!---数据暂不支持多条数据-->
						<view class="flex">
							<view class="flex flex-direction align-center" style="flex: 1;">
								<view class="tab-title">成交金额</view>
								<view class="tab-num">{{dataList.transactionAmount}}</view>
								<view class="level">
									{{dataList.compareName}}
									<image class="level-image"
										:src="dataList.amountCompareStatus=='持平' ? 'https://img.songlei.com/distribution/equal.png' : (dataList.amountCompareStatus == '升高' ? 'https://img.songlei.com/distribution/up.png' : 'https://img.songlei.com/distribution/down.png')">
									</image>
									{{dataList.amountCompareStatus == '持平' ? '持平' : (dataList.amountCompareStatus == '升高' ? '升高' : '下降')}}
								</view>
							</view>
							<view class="flex flex-direction align-center" style="flex: 1;">
								<view class="tab-title">成交订单数</view>
								<view class="tab-num">{{dataList.orderNum}}</view>
								<view class="level">{{dataList.compareName}}
									<image class="level-image"
										:src="dataList.orderCompareStatus == '持平' ? 'https://img.songlei.com/distribution/equal.png' : (dataList.orderCompareStatus == '升高' ? 'https://img.songlei.com/distribution/up.png' : 'https://img.songlei.com/distribution/down.png')">
									</image>
									{{dataList.orderCompareStatus == '持平' ? '持平' : (dataList.orderCompareStatus == '升高'?'升高':'下降')}}
								</view>
							</view>
							<view class="flex flex-direction align-center" style="flex: 1;">
								<view class="tab-title">成交人数</view>
								<view class="tab-num">{{dataList.num}}</view>
								<view class="level">{{dataList.compareName}}
									<image class="level-image"
										:src="dataList.numCompareStatus == '持平' ? 'https://img.songlei.com/distribution/equal.png' : (dataList.numCompareStatus=='升高' ? 'https://img.songlei.com/distribution/up.png' : 'https://img.songlei.com/distribution/down.png')">
									</image>
									{{dataList.numCompareStatus == '持平' ? '持平' : (dataList.numCompareStatus == '升高' ? '升高' : '下降')}}
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<view class="module-goods-layout" style="background-color: transparent;">
					<view class="module-head" style="padding: 32rpx 28rpx 10rpx;">
						<view class="module-title">分销商品优选</view>
						<navigator hover-class="none"
							url="/pages/distribution/distribution-commodity-center/distribution-commodity-center">
							<view class="module-more">更多商品<image class="arrow-right"
									src="https://img.songlei.com/distribution/arrow-right2.png">
								</image>
							</view>
						</navigator>
					</view>
					<recommendComponents canLoad />
				</view>
				
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	const util = require("utils/util.js");
	const numberUtil = require("utils/numberUtil.js");
	const app = getApp();
	import api from 'utils/api'
	import recommendComponents from "../component/recommend-components/index";
	import {getDistributionData} from "@/pages/distribution/api/distribution.js"
	import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"

	export default {
		mixins: [navigateUtil],
		components: {
			recommendComponents
		},
		computed: {
			...mapState([
				'CustomBar',
				'StatusBar',
				'menuHeight'
			])
		},
		filters: {
			filterInt(value) {
				value = value+'';
				if (value > 0) {
					const priceInt = value.split('.')[0];
					return priceInt
				}
				return '0'
			},

			filterSmall(value) {
				value = value+'';
				if (value > 0) {
					const priceSmall = value.split('.')[1];
					if (priceSmall > 0) {
						if ((priceSmall + '').length == 1) {
							return '.' + priceSmall + '0';
						}
						return '.' + priceSmall;
					}
					return '.00';
				}
				return '.00'
			},
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				userInfo: {},
				distribution: {
					commissionMonthtEstimate: 0, //当月预估佣金
					commissionNoSettlement: 0, //待结算佣金
					commissionNoWithdrawable: 0, // 可提现佣金(还未提现)
					commissionTotal: 0, //累计佣金金额窗
					commissionWithDrawabling: 0, //提现中佣金
					commissionWithdrawal: 0, // 已提现佣金金额
					isBandBankcard: 0 //是否绑定银行卡 0是微绑定 1是已绑定
				}, //当前佣金金额
				userConsumptionRecord: {
					totalAmount: 0 //已消费金额
				}, //用户消费记录
				distributionConfig: {
					fullAmount: 0
				},
				menuList: [
					[{
						icon: 'https://img.songlei.com/distribution/tool-allgoods.png',
						label: '分销中心',
						url: '/pages/distribution/distribution-commodity-center/distribution-commodity-center',
					}, {
						icon: 'https://img.songlei.com/distribution/tool-order.png',
						label: '分销订单',
						url: '/pages/distribution/distribution-order-list/index',
					}, {
						icon: 'https://img.songlei.com/distribution/tool-customer.png',
						label: '我的客户',
						url: '/pages/distribution/distribution-my-client/distribution-my-client',
					},
					{
						icon: 'https://img.songlei.com/distribution/tool-mygoods.png',
						label: '我的分销',
						url: '/pages/distribution/distribution-commodity-center/distribution-commodity-center?type=my',
					},{
						icon: 'https://img.songlei.com/distribution/tool-chart.png',
						label: '推广排行',
						url: '/pages/distribution/distribution-promotion-ranking/index',
					}
				],
				[
					
					{
						icon: 'https://img.songlei.com/distribution/tool-password.png',
						label: '交易密码',
						url: '/pages/distribution/password/index',
					},
					{
						icon: 'https://img.songlei.com/distribution/tool-bank.png',
						label: '我的银行卡',
						url: '/pages/distribution/my-bank-card/index',
					}
				]
			],
				dataListLabel:[
					{
					label: '今日',
					key: '1'
					},
					{
					label: '昨日',
					key: '0'
					},
					{
					label: '近7日',
					key: '2'
					},
					{
					label: '近30日',
					key: '3'
					},
				],
				dataList: [
				// 	{
				// 	label: '今日',
				// 	levelLabel: '较昨日',
				// 	amount: 400,
				// 	amountLevel: 1, // 0 持平  1 增长  -1 减少
				// 	orders: 30,
				// 	ordersLevel: 0,
				// 	personNum: 20,
				// 	personNumLevel: -1,
				// 	key: 0
				// }, {
				// 	label: '昨日',
				// 	levelLabel: '较昨日',
				// 	amount: 300,
				// 	amountLevel: -1, // 0 持平  1 增长  -1 减少
				// 	orders: 30,
				// 	ordersLevel: 0,
				// 	personNum: 20,
				// 	personNumLevel: -1,
				// 	key: 1
				// }, {
				// 	label: '近7日',
				// 	levelLabel: '较上周',
				// 	amount: 1600,
				// 	amountLevel: 1, // 0 持平  1 增长  -1 减少
				// 	orders: 180,
				// 	ordersLevel: 0,
				// 	personNum: 100,
				// 	personNumLevel: 1,
				// 	key: 2
				// }, {
				// 	label: '近30日',
				// 	levelLabel: '较上月',
				// 	amount: 90000,
				// 	amountLevel: 1, // 0 持平  1 增长  -1 减少
				// 	orders: 300,
				// 	ordersLevel: 0,
				// 	personNum: 250,
				// 	personNumLevel: -1,
				// 	key: 3
				// }
			],
				currentDatakey: 1
			}
		},
		onLoad() {
			this.userInfo = uni.getStorageSync('user_info')
		},
		onShow() {
			this.initData();
			this.getDistributionData()
		},
		methods: {
			//提现
			withdrawal(){
				// 1.判断用户是否开通银行 没有开通去开通
				//1.1 开通设置支付密码
				//1.2 开通及设置密码去提现管理
				console.log("this.distribution==withdrawal=>",this.distribution);
				if (this.distribution.showBankCard) {
					if (this.distribution.showPassWord) {
						// 去提现管理
						uni.navigateTo({
							url: '/pages/distribution/withdraw-admin/index'
						})
					}else{
						// 设置支付密码
						uni.navigateTo({
							url: '/pages/distribution/set-password/index'
						})
					}
				}else{
					// 开通银行
					uni.navigateTo({
						url: '/pages/distribution/bank-card/index'
					})
				}
			},
			showDeveloping() {
				uni.showToast({
					title: '开发中',
					icon: 'none',
					duration: 2000
				})
			},
			// 跳转已提现页面
			withdrawncommission(type) {
				uni.navigateTo({
					url: '/pages/distribution/distribution-withdrawn-commission/distribution-withdrawn-commission?type=' +
						type
				})
			},
			//待结算佣金页面  
			Commissionsettled(type) {
				uni.navigateTo({
					url: '/pages/distribution/distribution-commission-month/distribution-commission-month?type=' +
						type
				})
			},

			initData() {
				api.distributionuser().then(res => {

					if (res.data) { //是分销员
						this.distribution = res.data
						console.log("分销首页数据===》",this.distribution);
						let money = this.distribution.commissionTotal - this.distribution.commissionWithdrawal
						this.money = numberUtil.numberFormat(money, 2)
					}
				});
				api.userRecord().then(res => {
					if (res.data) {
						this.userConsumptionRecord = res.data
					}
				});
				api.distributionConfig().then(res => {
					if (res.data) {
						this.distributionConfig = res.data
					}
				});
			},

			handleSelect(e) {
				const {
					key
				} = e.currentTarget.dataset;
				this.currentDatakey = key;
				this.getDistributionData();
			},

			//分销数据
			getDistributionData(){
				getDistributionData(Object.assign({},{type: this.currentDatakey})).then(res => {
					if (res.data) {
						console.log("getDistributionData",res.data);
						this.dataList = res.data
					}
				});
			}
		}
	}
</script>

<style>
	page {
		background-color: #f4f4f4;
	}

	.distribution-text {
		width: 100%;
	}

	.distribution-image-bg {
		position: relative;
		height: 100vh;
	}

	.distribution-image {
		width: 100%;
		position: fixed;
		height: 79vh;
	}

	.text-height {
		line-height: 50rpx;
	}

	.distribution-text {
		position: fixed;
		padding-top: 800rpx;
	}

	.money-bg {
		width: 100%;
		background-image: url(https://img.songlei.com/distribution/distribution-head-bg2.png);
		background-size: 100% auto;
		background-repeat: no-repeat;
		color: black;

	}

	.distribution-list-bg {
		margin-top: 100rpx;
		width: 93%;
		border-radius: 30rpx;
		margin: auto;
		background: none;
	}

	.withdraw {
		width: 150rpx;
		height: 50rpx;
	}

	.arrow {
		background: none !important;
		height: 120rpx;
		line-height: 120rpx;
	}

	.font-weight {
		font-weight: 300;
	}

	.distribution-money {
		font-size: 68rpx;
	}

	.back-home {
		background-color: #fbd461 !important;
		width: 80% !important;
		height: 86rpx !important;
	}

	.distribution-icon {
		width: 36rpx !important;
		height: 36rpx !important;
	}

	.line {
		width: 1rpx;
		height: 100%;
		background-color: #DDDDDD;
	}

	.price-margin {
		margin-top: 16rpx;
	}

	.module-layout {
		border-radius: 20rpx;
		margin: 0 25rpx;
		background: #FFFFFF;
		padding: 28rpx;
	}

	.module-goods-layout {
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		margin: 0 25rpx;
		background: #FFFFFF;

	}

	.module-head {
		display: flex;
		justify-content: space-between;
		padding-bottom: 18rpx;

	}

	.module-line {
		border-bottom: solid 1rpx #dddddd;
	}

	.module-title {
		font-size: 32rpx;
		color: black;
		font-weight: bold;
	}

	.module-more {
		color: black;
		font-size: 28rpx;
	}

	.tab-item {
		width: 140rpx;
		color: black;
		display: flex;
		font-size: 32rpx;
	}

	.tab-default {
		color: black;
		padding-bottom: 2rpx;
		border-bottom: solid 4rpx white;
	}

	.tab-select {
		color: #A06243;
		padding-bottom: 2rpx;
		border-bottom: solid 4rpx #A06243;
		font-weight: bold;
	}

	.tab-title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.tab-num {
		padding: 20rpx;
		font-size: 32rpx;
		font-weight: 900;
	}

	.level-image {
		width: 30rpx;
		height: 30rpx;
		vertical-align: bottom;

	}

	.level {
		font-size: 22rpx;
	}

	.arrow-right {
		width: 9rpx;
		height: 20rpx;
		margin-left: 16rpx;
	}

	.price-signal {
		font-size: 24rpx;
	}

	.price {
		font-size: 48rpx;
		font-weight: bold;
	}

	.module-tip {
		font-size: 20rpx;
		color: #999;
	}
</style>