<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{title}}</block>
		</cu-custom>
		<view class="content_list">
			<view class="commission_item" v-for="(item,index) in list" :key="index">
				<view class="Ordernumber_status">
					<view class="" style="display: flex;align-items: center;">
						<view class="ordernumber_text">
							订单编号：
						</view>
						<view class="">
							{{item.orderNo}}
						</view>
					</view>

					<view class="status_text">
						<!-- 未退款  退款状态 （0未退款 1退款  2部分退款）") -->
                <text v-if="item.refountStatus=='0'">未退款</text>
				<text v-if="item.refountStatus=='1'">退款</text>
				<text v-if="item.refountStatus=='2'">部分退款</text>
					</view>
				</view>

				<view class="flex justify-start">
					<view class="text-black text-df text-bold">商品名称：</view>
					<view class="margin-left-xs text-smx">{{item.spuName}}</view>
				</view>

				<view class="flex justify-start">
					<view class="text-black text-df text-bold">商品数量：</view>
					<view class="margin-left-xs text-smx">{{item.quantity}}</view>
				</view>	

				<view style="display: flex;align-items: center;">
					<view class="ordernumber_text">
						下单时间：
					</view>
					<view class="">
					    	{{item.createTime}}
					</view>
				</view>
				<view class="ordercommissionamount">
					<view style="display: flex;align-items: center;">
						<view class="ordernumber_text">
							订单金额：
						</view>
						<view>
							<text class="text-price text-df">{{item.paymentPrice}}</text>

							<!-- {{item.}} -->
						</view>
					</view>
					<view style="display: flex;align-items: center;">
						<view class="ordernumber_text">
							分佣金额：
						</view>
						<view>
							
							<text class="text-price text-xl text-red text-bold">{{item.commissionNoSettlement}}</text>

						</view>
					</view>
				</view>
			</view>
		<view class="" style="height: 80rpx;"></view>
		</view>
		<view :class="'cu-load ' + (loadmore?'loading':'over')"></view>
	</view>
	

	

	<!-- </view> -->
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();

	import {getCommissionMonthtEstimateList,getCommissionNoSettlementList} from "@/pages/distribution/api/distribution.js"
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				loadmore: true,
				title: "待结算佣金",
				//根据优惠券获取商品列表
				page: {
					current: 1,
					size: 10,
				},
				list:[]
			}
		},
		onLoad(options) {
				this.page.current =1
				this.page.size = 10
			if(options.type=='1'){
				this.title = '待结算佣金'
	               this.getCommissionNoSettlementList()
		}
			if(options.type=='2'){
				this.title='单月预估佣金'
				this.getCommissionMonthtEstimateList()
		
		}
			// this.initData()
		},

		onReachBottom() {
			if (this.loadmore&&this.title=='待结算佣金') {
				this.page.current = this.page.current + 1;
				this.getCommissionNoSettlementList()
			}
			
				if (this.loadmore&&this.title=='当月预估佣金') {
					this.page.current = this.page.current + 1;
					this.getCommissionMonthtEstimateList()
				}
		},

		methods: {
			getCommissionNoSettlementList(){
				getCommissionNoSettlementList(this.page).then(res=>{
					this.list = [...this.list, ...res.data.records]
					if (this.list.length >= res.data.total) {
						this.loadmore = false;
					}
				})
			},
			getCommissionMonthtEstimateList(){
				getCommissionMonthtEstimateList(this.page).then(res=>{
					// console.log(res,'当月预估佣金')
					this.list = [...this.list, ...res.data.records]
					if (this.list.length >= res.data.total) {
						this.loadmore = false;
					}
				})
				
			}
			
		}
	}
</script>

<style scoped>
	.content_list {
		margin-top: 20rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		margin-bottom: 30rpx;
	}

	.commission_item {
		background: white;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}

	.Ordernumber_status {
		display: flex;
		justify-content: space-between;
	}

	.ordercommissionamount {
		display: flex;
		justify-content: space-between;
	}

	.ordernumber_text {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 800;
		color: #000000;

	}

	.status_text {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #000000;
	}
</style>