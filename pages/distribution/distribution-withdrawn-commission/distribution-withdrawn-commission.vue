<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{title}}</block>
		</cu-custom>
		<view class="content_list">
			<view class="commission_item">
				<view class="Ordernumber_status">
					<view class="" style="display: flex;align-items: center;">
						<view class="ordernumber_text">
							提现卡号：
						</view>
						<view class="">
							1234567890
						</view>
					</view>
				</view>
				<view style="display: flex;align-items: center;">
					<view class="ordernumber_text">
						提现时间：
					</view>
					<view class="">
					    	請3456789
					</view>
				</view>
				<view class="ordercommissionamount">
					<view style="display: flex;align-items: center;">
						<view class="ordernumber_text">
							提现金额：
						</view>
						<view>
							56789
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- <view :class="'cu-load ' + (loadmore?'loading':'over')"></view> -->

	</view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				loadmore: true,
				title: "已提现佣金"
			}
		},
		onLoad() {
			// this.initData()
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.getDistributionOrderData();
			}
		},

		methods: {}
	}
</script>

<style scoped>
	.content_list {
		margin-top: 20rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
	}

	.commission_item {
		background: white;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		padding: 20rpx;
	}

	.Ordernumber_status {
		display: flex;
		justify-content: space-between;
	}

	.ordercommissionamount {
		display: flex;
		justify-content: space-between;
	}

	.ordernumber_text {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 800;
		color: #000000;

	}

	.status_text {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #000000;
	}
</style>