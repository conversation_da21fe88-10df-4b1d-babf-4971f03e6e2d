import {
  requestApi as request
} from '@/utils/api.js'

// 分销员首页弹窗
export const distributionUserInitialLogin = (data) => {
  return request({
    url: '/mallapi/distributionuser/distributionUserInitialLogin',
    method: 'get',
    data
  })
}


// 当月预估金额
export const getCommissionMonthtEstimateList = (data) => {
  return request({
    url: '/mallapi/distributionorder/getCommissionMonthtEstimateList',
    method: 'get',
    data
  })
}

// 待提现佣金
export const getCommissionNoSettlementList = (data) => {
  return request({
    url: '/mallapi/distributionorder/getCommissionNoSettlementList',
    method: 'get',
    data
  })
}

export const distributionpage = (data) => {
  return request({
    url: '/mallapi/distribution/page',
    method: 'get',
    data,
    showLoading: false
  })
}

export const distributionorderitempage = (data) => {
  return request({
    url: '/mallapi/distributionorderitem/page',
    method: 'get',
    data
  })
}

export const distributionCustom = (data) => {
  return request({
    url: '/mallapi/distributionuser/distributionCustom',
    method: 'get',
    data
  })
}
// 分销中心获取分类列表
export const redesCategorySecond = (data) => {
  return request({
    url: '/mallapi/distribution/redesCategorySecond',
    method: 'get',
    data
  })
}

// 我的分销获取分类列表
export const myCategorySecond = (data) => {
  return request({
    url: '/mallapi/distribution/myCategorySecond',
    method: 'get',
    data
  })
}

// 我的分销查询银行卡
export const getUserWithdrawBankAccount = () => {
  return request({
    url: '/mallapi/userwithdrawbankaccount/getUserWithdrawBankAccount',
    method: 'get'
  })
}

// 我的分销绑定、更换银行卡
export const saveUserWithdrawBankAccount = (data) => {
  return request({
    url: '/mallapi/userwithdrawbankaccount/saveOrUpdateWithdrawBankAccount',
    method: 'post',
    data
  })
}

// 我的分销查询银行卡可用金额 最低金额
export const getBankDistributionTotalAmount = () => {
  return request({
    url: '/mallapi/userwithdrawbankaccount/getBankDistributionTotalAmount',
    method: 'get'
  })
}

// 我的分销提现
export const userwithdrawrecord = (data) => {
  return request({
    url: '/mallapi/userwithdrawrecord',
    method: 'post',
    data
  })
}

// 我的分销提现计划列表
export const withdrawalPlanPage = (data = {}) => {
  return request({
    url: '/mallapi/distributionorder/withdrawalPlanPage',
    method: 'get',
    showLoading: false,
    data
  })
}

// 我的分销已提现、提现中列表
export const getPage = (data = {}) => {
  return request({
    url: '/mallapi/userwithdrawrecord/page',
    method: 'get',
    data
  })
}

// 我的分销收入明细列表
export const getWithdrawableList = (data = {}) => {
  return request({
    url: '/mallapi/distributionorder/getCommissionNoWithdrawableList',
    method: 'get',
    data
  })
}

// 我的分销数据
export const getDistributionData = (data = {}) => {
  return request({
    url: '/mallapi/distributionuser/distributionData',
    method: 'get',
    showLoading: false,
    data
  })
}

//银行卡解绑
export function deleteWithdrawbankaccount(id) {
  return request({
    url: `/mallapi/userwithdrawbankaccount/${id}`,
    method: 'delete'
  })
}