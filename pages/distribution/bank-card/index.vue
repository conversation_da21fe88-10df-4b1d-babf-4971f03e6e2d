<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">绑定银行卡</block>
    </cu-custom>

    <view v-if="bankInfo=='null'|| bankInfo===null || isShow ">
      <view class="explain padding">
        填写个人信息
      </view>
  
      <open-bank-card ref="formData" :source="'distribution'" :backgroundColor="'#F5F5F7 !important'"></open-bank-card>
  
      <view class="flex justify-center">
        <button
          class="cu-btn margin-tb-sm lg login_padding"
          :class="'bg-'+theme.backgroundColor"
          @click="regSub"
        >下一步</button>
      </view>
    </view>

    <view v-else class="margin-top">
      <view
        class="padding-xs margin-top-xs text-center"
        style="width:95%;height:571rpx;background: #FFFFFF;margin:20rpx auto;border-radius:15rpx;"
      >
        <view style="margin:0 auto; margin-top:76rpx">
          <view style="width: 161upx;margin:0 auto;" 
            :style="{height:bankInfo.examineStatus=='0'?'175rpx':'161rpx'}">
            <image
              style="width:100%;height: 100%;border-radius:15rpx;"
              :src="bankInfo.examineStatus=='0'?'https://img.songlei.com/distribution/examine.png':(bankInfo.examineStatus=='2'?'https://img.songlei.com/distribution/error.png':'https://img.songlei.com/gift/answer.png')" 
              mode="aspectFill" 
              >
            </image>
          </view>

          <view class="text-xl text-bold text-center padding-sm" style="width: 100%;">
            {{bankInfo.examineStatus=='0'?"银行审核中":(bankInfo.examineStatus=='2'?"银行审核失败":"审核成功")}}
          </view>
          <view v-if="bankInfo.examineStatus=='0'" class="text-xsm text-center" style="width: 100%;">
            请您耐心等待...
          </view>

          <view class="margin" style="width: 475rpx;height: 1rpx;border-bottom: 4rpx dashed #F5F5F7;margin:20rpx auto;"></view>
        </view>  
        <view class="flex justify-center" @click="navTo()">
          <view style="margin-top: 20rpx;color: #FFFFFF;" class="cu-btn round login_padding">
            {{bankInfo.examineStatus=='0'?"返回":(bankInfo.examineStatus=='2'?"重新绑定":"下一步")}}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
const util = require("utils/util.js");
import {getUserWithdrawBankAccount,saveUserWithdrawBankAccount,getBankDistributionTotalAmount} from "@/pages/distribution/api/distribution.js"
import openBankCard from "@/components/open-bank-card/index.vue";
import { navigateUtil } from "@/static/mixins/navigateUtil.js";

export default {
  mixins: [navigateUtil],
  components: {
    openBankCard
	},
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      form:{},
      bankInfo:'',//银行信息
      isShow: false,
    };
  },

  onShow() {
    
  },

  onLoad (e) {
    if (e) {
      if (e.type) {
        this.isShow = e.type
        return
      }
    }
    this.getWithdrawBankAccount()
  },

  methods: {
    //绑定银行卡接口
    async saveWithdrawBankAccount(){
      try {
        const res = await saveUserWithdrawBankAccount(Object.assign({}, util.filterForm(this.form)))
        console.log("res",res);
        
        if (res.code=='0') {
          this.bankInfo = res.data
          this.isShow = false
        }else{
          this.isShow = true
        }
        
      } catch (error) {
        console.log("error",error);
      }
    },

    //跳转逻辑 0 待审核 1审核通过 2审核失败
    navTo(){
      console.log("navTo===>",this.bankInfo);
      if (this.bankInfo) {
        
        if (this.bankInfo.examineStatus=='0') {
          uni.navigateBack({
            delta: 1
          });
          return
        }else if (this.bankInfo.examineStatus=='1') {
          // 还要判断用户有么有设置过密码 1有密码直接到提现管理 2 没有去设置密码
          //设置密码
          if (this.bankInfo.existsPassWord) {
            // 到提现管理
              uni.navigateTo({
                url: '/pages/distribution/withdraw-admin/index?show=show'
              });
            return
          }
          // 去设置密码
          uni.navigateTo({
            url: '/pages/distribution/set-password/index?type=1'
          });
          return
        }else{
          this.isShow = true
        }
      }
    },

    //查询银行卡
    async getWithdrawBankAccount(){
      try {
        const res = await getUserWithdrawBankAccount()
        console.log("this.bankInfo====res==>",res); 
        this.bankInfo = res.data
        if (res.data) {
          this.isShow = false
        }else{
          this.isShow = true
        }
      } catch (error) {
        console.log("error",error);
      }
    },

    //提交
    regSub (e) {
      console.log("下一步", e);
      let that = this
      let data = this.$refs.formData.form;
      that.form ={
        userName:data.cifName,
        cardId:data.idNo,
        phone:data.mobilePhone,
        code:data.messageCode,
        bankCardNo:data.tAcNo
      }
      if (!that.form.userName) {
        uni.showToast({
          title: '请输入中文姓名',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.cardId) {
        uni.showToast({
          title: '请输入身份证号',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.bankCardNo) {
        uni.showToast({
          title: '请输入银行卡号',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.phone) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.code) {
        uni.showToast({
          title: '请输入手机号码验证码',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      let usreWalletInfo = data
      console.log("用户银行卡详情", usreWalletInfo);
      uni.setStorageSync('USER_WALLET_INFO', usreWalletInfo);
      //调接口
      this.saveWithdrawBankAccount()
      // uni.navigateTo({
      //   url: '/pages/wallet/wallet-password/index?type=1'
      // });
    },
  }
}
</script>
<style>
page {
  
}
</style>

<style scoped>

.login_padding {
  width: 343rpx;
  height: 80rpx;
  margin-top: 97rpx;
  background: linear-gradient(90deg, #FF8362 0%, #FF4F00 100%);
  /* border-radius: 40rpx; */
}
.explain {
  /* width: 238rpx; */
  /* height: 38rpx; */
  font-size: 40rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #000000;
  /* line-height: 45rpx; */
}
</style>

