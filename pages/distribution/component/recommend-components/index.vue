<template>
	<view>
		<waterfall :list="goodsListRecom" backgroundColor="#f4f4f4" fullWidth="700" isDistrubution @click="goDetail">
		</waterfall>
		<view :class="'cu-load ' + (loadmoreRecommendList ?'loading':'over')"></view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api';
	import {
		distributionpage
	} from "@/pages/distribution/api/distribution.js";
	import waterfall from "components/waterfall/index.vue"
	export default {
		components: {
			waterfall
		},
		props: {
			//接口参数
			itemId: {
				type: String,
				default: null
			},
			sceneId: {
				type: String,
				default: 'sy101'
			},
			//由父组件控制什么时候可以加载推荐数据,如果需要传入itemId 就等获取到itemId再设置为true，如果不需要传入itemId直接设置为true
			canLoad: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				loadmoreRecommendList: true,
				goodsListRecom: [],
				loadDataTimes: 0
			}
		},
		watch: {
			canLoad: {
				handler(newValue, oldValue) {
					if (newValue) {
						this.getGoodsRecom(true);
					}
				},
				immediate: true
			}
		},

		methods: {
			//推荐商品
			/**
			 * @param Boolean fresh  是否刷新
			 */
			getGoodsRecom(fresh) {
				if (this.loadmoreRecommendList) {
					distributionpage({
						current: 1,
						size: 20,
						type: 1
					}).then(res => {
						if (res.data.records.length > 0) {
							if (fresh) this.goodsListRecom = [...res.data.records];
							else this.goodsListRecom = [...this.goodsListRecom, ...res.data.records];
						}
						this.loadmoreRecommendList = false;
					}).catch(e => {
						this.loadmoreRecommendList = false;
					});
				}
			},


			goDetail(item) {
				if (item.skus && item.skus.length > 0) {
					uni.navigateTo({
						url: `/pages/goods/goods-detail/index?id=${item.id}&skuId=${item.skus[0].id}`
					})
				} else {
					uni.navigateTo({
						url: `/pages/goods/goods-detail/index?id=${item.id}`
					})
				}
			}
		}
	}
</script>

<style>
	.recommend-action {
		display: flex;
		align-items: center;
		height: 100%;
		justify-content: center;
		max-width: 100%;
	}
</style>