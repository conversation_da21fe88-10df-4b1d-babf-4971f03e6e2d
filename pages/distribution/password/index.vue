<template>
  <view>
    <cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">支付密码设置</block>
	  </cu-custom>

		<view style="margin-top: 110rpx;">

			<view class="flex justify-between " style="width: 90%;margin: 0 auto;border-bottom: 2rpx solid #DCDCDC;"> 
				<view style="" class="text-xml text-black text-bold text-left">
					交易密码
				</view>
				<view style="color: #AC785E;" class="margin-right-xl">已设置</view>
			</view>

			<view class="margin-tb-xl">
				<view style="width: 100%;" class="flex justify-between margin-lr">
					<view class="text-sm" style="color: #000000;width: 330rpx;">
						请输入手机 <text style="color: #BD976F;"> {{userpassword.phone| phoneEncryption}} </text> 收到的短信验证码
					</view>

            <button :disabled="msgKey" 
							style="width: 210rpx;background:#F4E6D6; color: #AC785E;margin-right: 68rpx;" 
							class="text-sm cu-btn round" 
							@click="getPhoneCode">{{ msgText }}
          </button>
        </view>
				
				<view class="padding-top">
					<code-page @change="code($event)" :passwordShow="true" :source="'distribution'" :clear="setClear"/>
				</view>
			</view>
			
			<view class="flex justify-center">
        <button
					:disabled="disabled"
          class="cu-btn margin-tb-sm lg"
					style="width: 375rpx;height: 80rpx;margin-top: 97rpx;"
          :class="disabled?'':'login_padding'"
          @click="nextStep"
        >验证并修改提现密码</button>
      </view>
		</view>
  </view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
	import codePage from "@/components/verification-code/index.vue";
	import { phoneCode, phoneCheck ,passwordCheck} from '@/pages/gift/api/gift'
 
	const MSGINIT = "获取验证码",
		MSGSCUCCESS = "${time}秒后可重发",
		MSGTIME = 60;

	export default {
		components: {
			codePage
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '密码设置',
				isPasswordSet:false,//是否设置密码
				validateCode:'',//验证码
				disabled:true,//控制按钮
				setClear:true,//控制输入框是不清空
				msgText: MSGINIT,
				msgTime: MSGTIME,
				msgKey: false,
				userpassword:{
					phone	:''
				},
				source:'edit'//记录来源
			};
		},
		props: {},
		onLoad(options) {
				if (options&&options.source) {
					this.source = options.source
			}
		},
		mounted() {
			
		},

		onShow() {
			// 禁止分享
			uni.hideShareMenu()
			this.setClear = false;
			this.disabled =true
		},

		created(){ 
			if (uni.getStorageSync('user_info')) {
				this.userpassword=uni.getStorageSync('user_info')
			}
		},

		computed: {
		},

		methods: {
		//获取验证码
		getPhoneCode(){
			if (this.msgKey) return
			phoneCode(Object.assign({phone:this.userpassword.phone,type:'1',codeSize:'6'})).then(res=>{
        console.log("res=======>",res);
				if (res.code == 0) {
					uni.showToast({
								title: '验证码发送成功',
								icon: 'none',
								duration: 2000
					});
					this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
					this.msgKey = true
					const time = setInterval(() => {
						this.msgTime--
						this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						if (this.msgTime == 0) {
							this.msgTime = MSGTIME
							this.msgText = MSGINIT
							this.msgKey = false
							clearInterval(time)
						}
					}, 1000)
				}
      })
		},
		
		//下一步
		nextStep(){
			this.setClear = true;
			phoneCheck(Object.assign({phone:this.userpassword.phone,code:this.validateCode})).then(res=>{
				console.log("code==>",res);
				if (res.code == 0) {
					uni.setStorageSync('editPwdSource',this.source)
					uni.navigateTo({
						url: `/pages/distribution/set-password/index?type=1`
					})
				}
			})
		},

		//输入框
		async	code(val){
				console.log("val",val);
				if (val&&val.length=='6') {
					this.disabled = false;
					this.validateCode= val
				}else{
					this.disabled = true;
					this.setClear = false;
				}
			},
    }
	};
</script>

<style lang="scss" scoped>
.login_padding {
  background: linear-gradient(90deg, #FF8362 0%, #FF4F00 100%);
  /* border-radius: 40rpx; */
}
</style>
