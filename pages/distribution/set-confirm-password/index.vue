<template>
  <view>
    <cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">交易密码设置</block>
	  </cu-custom>

		<view v-if="showResult">
			<view class="padding-xs margin-top-xs text-center">
        <view style="margin:0 auto; margin-top:76rpx">
          <view style="width: 136upx; height: 161upx;margin:0 auto;">
            <image
              style="width:100%;height: 100%;border-radius:15rpx;"
              :src="'https://img.songlei.com/gift/ban.png'" 
              mode="aspectFill" 
              >
            </image>
          </view>

          <view class="text-xl text-bold text-center padding-sm" style="width: 100%;">
            交易密码修改成功
          </view>

          <view class="margin" style="width: 475rpx;height: 1rpx;border-bottom: 4rpx dashed #F5F5F7;margin:20rpx auto;"></view>
        </view>  
        <view class="flex justify-center" @click="back()">
          <view style="margin-top: 20rpx;color: #FFFFFF;" class="cu-btn round login_padding">
            返回
          </view>
        </view>
      </view>
		</view>

		<view v-else style="margin-top: 290rpx;">
			<view class="text-sm text-center" style="color: #000000;">
				请再次输入新交易密码
			</view>
			
			<code-page @change="code($event)" :passwordShow="true" :source="'distribution'" :clear="setClear"/>
			<!-- <view  class="text-sm text-center text-red" >
				{{text}}
			</view> -->
			<view v-if="text" class="prompt margin-left-lg">
				提示：同上一次输入密码不相同
			</view>

			<view :class="['flex', 'justify-center',text?'':'margin-top-xl']">
        <button
					:disabled="disabled"
          class="cu-btn margin-tb-sm lg login_padding"
          :class="'bg-'+theme.backgroundColor"
          @click="nextStep"
        >提交</button>
      </view>
		</view>
  </view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
	import codePage from "@/components/verification-code/index.vue";
	import { setPassword,editPassword } from '@/pages/gift/api/gift'

	export default {
		components: {
			codePage
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '交易密码设置',
				isPasswordSet:false,//是否设置密码
				disabled:true,//控制按钮
				setClear:true,//控制输入框是不清空
				password:'',//原密码
				conformPassword:'',//确认密码
				text:'',//错误内容,
				showResult:false,//是否显示结果 
				// type:'1',//1设置 2 修改
			};
		},
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
				if (options) {
					if (options.password) {
						this.password = options.password
					}
				}
			});
		},
		mounted() {
			
		},

		onShow() {
			// 禁止分享
			uni.hideShareMenu()
			this.setClear = false;
			this.disabled =true
		},

		onHide() {
		},

		computed: {
		},

		methods: {
			//忘记密码返回 提现管理 修改密码返回
			back(){
				if (uni.getStorageSync('editPwdSource')&&uni.getStorageSync('editPwdSource')=='admin') {
					uni.navigateBack({
						delta: 3
					});
				}else{
					uni.navigateBack({
						delta: 3
					});
				}
			},
			//提交
			nextStep(){
				this.setClear = true;
				//编辑密码
				if (uni.getStorageSync('editPwdSource')) {
					this.editPwd()
					return
				}
				//设置密码
				this.setPwd()
			},

			//输入框
			async	code(val){
				console.log("val",val);
				if (val&&val.length=='6') {
					if (this.password===val) {
						this.disabled = false;
						this.conformPassword = val
					}else{
						this.disabled = true;
						this.setClear = false;
						this.text = '提示：同上一次输入密码不相同'
					}
				}else{
					this.text= '';
					this.disabled = true;
				}
			},

			//编辑密码
			editPwd(){
				editPassword(Object.assign({password:this.conformPassword})).then(res=>{
					console.log("editPassword=>",res);
					if (res.code == 0) {
						this.showResult = true;
					}
				})
			},

			//设置密码
			setPwd(){
				setPassword(Object.assign({password:this.conformPassword})).then(res=>{
					console.log("setPassword=>",res);
					if (res.code == 0) {
						//直接去提现管理
						uni.redirectTo({
							url: `/pages/distribution/withdraw-admin/index`
						})
					}
				})
			}
    }
	};
</script>

<style lang="scss" scoped>
.prompt{
	font-size: 30rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #FF4E01;
}
.login_padding {
  width: 343rpx;
  height: 80rpx;
  margin-top: 97rpx;
  background: linear-gradient(90deg, #FF8362 0%, #FF4F00 100%);
  /* border-radius: 40rpx; */
}
</style>
