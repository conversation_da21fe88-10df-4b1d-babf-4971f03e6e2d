<template>
	<view>
		<cu-custom bgColor="#bc8468" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">分销订单</block>
		</cu-custom>
		<view style="position: relative;" hover-class="none" hover-stop-propagation="false">
			<image v-if="!customerId" style="width: 100%;height: 360rpx;"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/fenxiao/tgorder.png" mode=""></image>
			<view v-if="!customerId" class="" hover-class="none" hover-stop-propagation="false" style="position: absolute;
    top: 60rpx;
    left: 58rpx;">
				<view style="font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 800;
				color: #000000;">
					您当前累计推广订单
				</view>
				<view style="display: flex;align-items: center;">
					<view style="font-size: 83rpx;
						font-family: DINPro;
						font-weight: bold;
						color: #EE102B;">
						{{distributionOrderTotal}}
					</view>
					<view style="margin-top: 37rpx;">
						笔订单
					</view>
				</view>
			</view>

			<view v-if="!customerId" class="font-weight"
				style="position: absolute;left: 268rpx;top: 110rpx;width: 62%;">
				<view class="cu-bar search">
					<view class="search-form radius" style="border-radius: 30rpx;">
						<text class="cuIcon-search"></text>
						<input type="text" v-model="parameter.orderId" placeholder="订单号" confirm-type="search"
							@confirm="searchData" />
					</view>

					<view class="action" @click="searchData" style="position: absolute;right: 0px;">
						<button class="cu-btn radius"
							style="border-radius: 30rpx;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);color: #fff;width: 160rpx; z-index: 2;"
							@tap.stop="searchData">搜索</button>
					</view>
				</view>
			</view>

			<view class="myclient_list" :style="{ top: customerId > 0 ? '30rpx' : '250rpx' }">
				<template v-if="distributionOrderList&&distributionOrderList.length">
					<view class="myclient_item" v-for="(item, index) in distributionOrderList" :key="index">
						<view style="width: 100rpx;height: 100rpx;">
							<image :src="item.userInfo.headimgUrl " mode="" style="width: 100rpx;height: 100rpx;">
							</image>
						</view>
						<view  style="flex: 1;width: 100%;margin-left: 20rpx;">
							<view  style="display: flex;justify-content: space-between;">
								<view >
									昵称: {{item.userInfo.nickName}}
								</view>
								<view class="text-gray padding-right-sm">{{item.commissionStatus | filterStatus}}
									<text
										class="text-price text-xl text-red text-bold margin-left">{{item.commission}}</text>
								</view>
							</view>
							<view >
								订单编号： {{item.orderInfo.orderNo}}
							</view>
							<view>
								下单时间： {{item.createTime}}
							</view>
							<view>
								商品名称： {{item.spuNameSpecInfo}}
							</view>
							<view>
								商品数量： {{item.quantity ? item.quantity : '0'}}
							</view>
							<view>
								商品单价： {{item.salesPrice ? item.salesPrice : '0.00'}} 元
							</view>
							<view>
								总金额： {{item.totalAmount ? item.totalAmount : '0.00'}} 元
							</view>
						</view>
					</view>
				</template>
				<view v-else class="myclient_item"
					style="height: 200rpx;justify-content: center;font-weight: 500;color: #999999;line-height: 45px;">
					-暂无数据-
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from "utils/api";

	export default {
		filters: {
			filterStatus(val) {
				val = parseInt(val) - 1;
				let stauts = ["冻结", "已解冻"];
				return stauts[val];
			},
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				scrollLeft: 0,
				userInfo: undefined,
				distribution: {},
				distributionOrderTotal: 0,
				distributionOrderList: [],
				page: {
					current: 1,
					size: 10,
					//升序字段
					descs: "create_time",
				},

				parameter: {
					orderId: "",
				},
				loadmore: true,
				//查询某一改客户的订单
				customerId: ''
			};
		},
		onLoad(options) {
			this.customerId = options.customerId;
			this.userInfo = uni.getStorageSync("user_info");
			this.initData();
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.getDistributionOrderData();
			}
		},

		methods: {
			searchData() {
				this.page.current = 1;
				this.getDistributionOrderData();
			},

			getDistributionOrderData() {
				if (this.customerId) {
					this.parameter.userId = this.customerId;
				}
				api
					.distributionorderPage(
						Object.assign({}, this.page, util.filterForm(this.parameter))
					)
					.then((res) => {
						let dataList = res.data.records;
						if (this.page.current == 1) {
							this.distributionOrderList = dataList;
						} else {
							this.distributionOrderList = [
								...this.distributionOrderList,
								...dataList,
							];
						}
						this.distributionOrderTotal = res.data.total;
						if (dataList.length < this.page.size) {
							this.loadmore = false;
						}
					});
			},
			initData() {
				api.distributionuser().then((res) => {
					console.log(res);
					if (res.data) {
						//是分销员
						this.distribution = res.data;
					}
				});
				this.getDistributionOrderData();
			},
		},
	};
</script>

<style scoped>
	page {
		background-color: #fff;
	}

	.cu-capsule.radius .cu-tag:last-child::after,
	.cu-capsule.radius .cu-tag[class*="line-"] {
		border-radius: 50rpx;
	}

	.cu-capsule .cu-tag[class*="line-"]:last-child::after {
		border: 2rpx solid #ff4444;
	}

	.cu-tag[class*="line-"]::after {
		border-radius: 50rpx;
	}

	.cu-capsule .cu-tag[class*="line-"]:first-child::after {
		border: 2rpx solid #ff4444;
	}

	.title-text {
		position: absolute;
		top: 320rpx;
		left: 35%;
		z-index: 999;
		color: #ffebac;
	}

	.title-text-l {
		position: absolute;
		top: 335rpx;
		left: 20%;
		z-index: 999;
		color: #ffebac;
	}

	.title-text-1 {
		font-size: 68rpx;
		text-align: center;
		color: #ffebac;
	}

	.title-image {
		width: 100vh;
	}

	.font-weight {
		font-weight: 300;
	}

	.myclient_list {
		padding-left: 24rpx;
		padding-right: 20rpx;
		width: 100%;
		position: absolute;
	}

	.myclient_item {
		display: flex;
		align-items: center;
		/* border: 1rpx solid red; */
		padding: 20rpx;
		/* border: ; */
		border-radius: 20rpx;
		position: relative;
		background: white;
		margin-bottom: 20rpx;
	}

	.item_right {
		margin-left: 20rpx;
		width: 530rpx;
	}

	.header_one {
		position: absolute;
		left: 4rpx;
		top: 0;
		width: 50rpx;
		height: 50rpx;
	}
</style>