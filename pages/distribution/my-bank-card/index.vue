<template>
  <view class="">
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">我的银行卡</block>
    </cu-custom>

    <!-- 填写提现内容 -->
    <view v-if="bankInfo" class="padding-top padding-lr-sm box-center">
      <view class="flex justify-between solid-bottom">
        <view class="flex justify-start padding-bottom-xs ">
          <view class="cuIcon-card margin-right-xs text-lg" style="color: #AC785E;"></view>
          <view class="text-bold text-df">
            {{bankInfo.bankName || 'XXXX' }}
          </view>
        </view>
        <view @click="unbinding()" class="text-xsm" style="color: #AC785E;text-decoration:underline">
          解绑
        </view>
      </view>
      <view class="padding-tb-xs">
        <text v-if=" bankInfo.bankCardNo" class="text-bold text-xml margin-left-lg">{{ bankInfo.bankCardNo | onlyFourBank}}</text>
      </view>
    </view>

    <view class="flex justify-center" style="margin-top: 426rpx;">
      <button
        class="cu-btn margin-tb-sm lg login_padding"
        :class="'bg-'+theme.themeColor"
        @click="toPage('/pages/distribution/bank-card/index?type=true')"
      >{{bankInfo?'更换银行卡':'添加银行卡'}}</button>
    </view>

    <!-- 解绑弹框 -->
    <view 
		class="cu-modal" 
		:class="modalConfirm ? 'show' : ''" 
		>
			<view 
				class="cu-dialog bg-white " 
				:class="modalConfirm ? 'animation-slide-center' : ''" 
        style="width: 576rpx; border-radius: 45rpx;"
				@tap.stop
			>
      <view style="width: 161upx; height: 161upx;margin:0 auto;margin-top: 38rpx;">
        <image
          style="width:100%;height: 100%;border-radius:15rpx;"
          :src="'https://img.songlei.com/distribution/warn.png'" 
          mode="aspectFill" 
          >
        </image>
      </view>

      <view class="text-center margin-lr solid-bottom" style="padding-top: 28rpx;padding-bottom: 44rpx;">银行卡是否需要解绑？</view>

        <view class="flex justify-between margin-sm padding-lr-xs">
          <button
            class="cu-btn margin-tb-sm lg btn-false"
            :class="'bg-'+theme.themeColor"
            @click="confirm(1)"
          >取消</button>

          <button
            class="cu-btn margin-tb-sm lg btn-true"
            :class="'bg-'+theme.themeColor"
            @click="confirm(2)"
          >确认</button>
        </view>
			</view>
		</view>

    <!-- 成功失败 弹框 -->
    <view 
      class="cu-modal" 
      :class="modalDialog ? 'show' : ''" 
      >
			<view 
				class="cu-dialog bg-white " 
				:class="modalDialog ? 'animation-slide-center' : ''" 
        style="width: 576rpx; border-radius: 45rpx;"
				@tap.stop
			>
      <view style="width: 161upx; height: 161upx;margin:0 auto;margin-top: 38rpx;">
        <image
          style="width:100%;height: 100%;border-radius:15rpx;"
          :src="msg?'https://img.songlei.com/distribution/error.png':'https://img.songlei.com/gift/answer.png'" 
          mode="aspectFill" 
          >
        </image>
      </view>

      <view class="text-center margin-lr solid-bottom" style="padding-top: 28rpx;padding-bottom: 44rpx;">
        {{msg?msg:'解绑成功'}}
      </view>

        <view class="flex justify-center">
          <button
            class="cu-btn margin-tb-sm lg btn-true"
            :class="'bg-'+theme.themeColor"
            @click="confirm(3)"
          >确定</button>
        </view>
			</view>
		</view>
  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'
const numberUtil = require("utils/numberUtil.js");

import {getUserWithdrawBankAccount,deleteWithdrawbankaccount} from "@/pages/distribution/api/distribution.js"
import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"
export default {
  mixins: [navigateUtil],
  components: {
  },

  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      bankInfo:null,//银行信息
      modalConfirm:'',//确认弹框
      modalDialog: '',//成功弹框
      msg:'',//错误信息
    }
  },

  onLoad (e) {
  },

  onShow() {
    this.getBankInfo()
  },

  methods: {
    //解绑
    unbinding(){
      this.modalConfirm = 'show'
    },

    //忘记密码
		navTo(){
			// 忘记 
      uni.navigateTo({
        url: `/pages/distribution/password/index?source=admin`
      })
		},

    //确认密码 num 1 取消 2 确认
    confirm(num){
      if (num) {
        if (num==1) {
          this.modalConfirm = ''
          this.modalDialog = ''
          return
        }
        //确认解绑
        if (num==2) {
          this.modalConfirm = ''
          if (this.bankInfo) {
            deleteWithdrawbankaccount(this.bankInfo.id).then((res)=>{
              console.log("confirm==res==>",res);
              //成功
              // if (res.code=== 0) {
              // }else{
              //   // 失败
              // }
              this.msg = res.msg
              this.modalDialog= 'show'
            })
          }
          return
        }
        //确认
        if (num==3) {
          this.modalConfirm = '';
          this.modalDialog = '';
          this.getBankInfo();
        }
      }
    },

    // 关闭密码支付弹框
    hideModalGiftPwd(){
      this.modalGiftPwd = '';
      // this.pwd = '';
    },

    //查询银行卡可用余额 最低余额 信息
    async  getBankInfo(){
      try {
        const res = await getUserWithdrawBankAccount()
        console.log("getBankInfo==res==》",res);
        // if (res.data) {
          this.bankInfo = res.data
        // }
      } catch (error) {
        console.log("error");
      }
    },

  }
}
</script>

<style>
.btn-false{
  width: 247rpx;
  height: 66rpx;
  background: #FFFFFF;
  border: 1px solid #FF4E01;
  border-radius: 33rpx;
  color: #FF4E01;
}
.btn-true{
  width: 247rpx;
  height: 66rpx;
  background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
  border-radius: 33rpx;
}
.box-center{
  width: 95%;
  margin: 0 auto;
  margin-top: 30rpx;
  border-radius: 15rpx;
  background-color: #FFFFFF;
}

.login_padding {
  width: 343rpx;
  height: 80rpx;
  margin-top: 20rpx;
  background: linear-gradient(90deg, #FF8362 0%, #FF4F00 100%);
  /* border-radius: 40rpx; */
}
</style>
