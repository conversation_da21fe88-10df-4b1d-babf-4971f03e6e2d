<template>
  <view class="">
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">{{ page.userWithdrawStatus=='0'?'提现中':'已提现'}}佣金</block>
    </cu-custom>

    <!-- 填写提现内容 -->
    <view v-if="dataList">
      <view v-for="(item, index) in dataList"
        :key="index" class="padding-tb padding-lr-sm box-center">
        <view class="flex justify-start">
          <view class="text-black text-df text-bold">提现卡号：</view>
          <view class="margin-left-xs text-smx" style="width: 480rpx;">{{item.bankCardNo | bankCard}}（{{item.bankName}}）</view>
        </view>
        <view class="flex justify-start">
          <view class="text-black text-df text-bold">提现时间：</view>
          <view class="margin-left-xs text-smx">{{item.withdrawalTime}}</view>
        </view>
        <view class="flex justify-start">
          <view class="text-black text-df text-bold">提现金额：</view>
          <view class="margin-left-xs text-red text-df text-bold">
            <text class="text-price text-df text-red"></text>{{item.applyAmount}}
          </view>
        </view>
      </view>
    </view>
    <view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'
const numberUtil = require("utils/numberUtil.js");
import codePage from "@/components/verification-code/index.vue";
import {getPage} from "@/pages/distribution/api/distribution.js";
import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"
export default {
  mixins: [navigateUtil],
  components: {
    codePage
  },

  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      page: {
          current: 1,
          size:10,
          userWithdrawStatus:'',//0：提现中 1：提现成功
        },
      dataList:[],//提现计划列表
    }
  },

  onLoad (e) {
    if (e) {
      if (e.type) {
        this.page.userWithdrawStatus=e.type
      }
    }
  },

  onShow() {
    this.refresh()
  },

  //下拉加载更多
  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.getPage();
    }
  },

  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框
    uni.hideNavigationBarLoading(); // 停止下拉动作
    uni.stopPullDownRefresh();
  },

  methods: {
    //提现计划
    getPage(){
      getPage(Object.assign({},this.page)).then((res)=>{
        console.log("getPage==res==>",res);
        if (res.data) {
          if (res.data.records) {
            let dataList=res.data.records
            this.dataList = [...this.dataList, ...dataList];
            if (dataList.length < this.page.size) {
              this.loadmore = false;
            }
          }
        }
      })
    },

    //重置
    refresh () {
      this.loadmore = true;
      this.dataList = [];
      this.page.current = 1;
      this.getPage();
    },
  }
}
</script>

<style>
.box-center{
  width: 95%;
  margin: 0 auto;
  margin-top: 20rpx;
  border-radius: 15rpx;
  background-color: #FFFFFF;
}
</style>
