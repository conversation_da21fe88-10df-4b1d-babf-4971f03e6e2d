<template>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">推广统计</block>
    </cu-custom>

    <view
      class="font-weight"
      style="background: #ffffff;"
    >
      <view class="title-text text-center">
        <view style="border-bottom: 1rpx dashed #ffffff;padding-bottom: 10rpx;">您当前的推广人数</view>
        <view class="text-bold title-text-1">{{distributionPromotionTotal}}<text style="font-size: 30rpx;font-weight: 400;">人</text></view>
      </view>
      <!-- <image class="title-image" src="https://joodroid.oss-cn-beijing.aliyuncs.com/1/material/aadd3deb-8fb8-442f-8eda-23d2d03f0440.png"></image> -->
      <view style="position: relative;height: 380rpx;">
        <image
          mode="widthFix"
          style="width: 100%"
          src="https://img.songlei.com/distribution/distribution-withdraw-bg.png"
        ></image>

        <view style="position: absolute;top: 20rpx;left: 3%;">
          <image
            mode="widthFix"
            style="width: 710rpx"
            src="https://img.songlei.com/distribution/distribution-promotion-statistical.png"
          ></image>
        </view>
      </view>

      <view class="cu-bar search ">
        <view
          class="search-form radius"
          style="border-radius: 30rpx;"
        >
          <text class="cuIcon-search"></text>
          <input
            :adjust-position="false"
            type="text"
            placeholder="会员名称"
            v-model="parameter.nickName"
            confirm-type="search"
          ></input>
        </view>

        <view
          class="action"
          style="position: absolute;right: 0px;"
        >
          <button
            class="cu-btn radius"
            style="border-radius: 30rpx;background-color: #FD4649;color: #fff;width: 210rpx;"
            @click="search"
          >搜索</button>
        </view>
      </view>
    </view>

    <view
      class="bg-white radius promotion-list"
      style="border-radius:0rpx"
    >
      <view class="flex padding-top padding-lr justify-center">
        <view class="cu-capsule radius">
          <view
            style="border-radius: 30rpx;margin-right: 30rpx;width: 313rpx;"
            @click="tabChange(1)"
            class='cu-tag padding padding-lr-xl'
            :class="tabVal==1?'bg-'+theme.themeColor : (' line-'+theme.themeColor)"
          >
            一级（{{distributionPromotionTotal1}}）
          </view>
          <view
            style="border-radius: 30rpx;width: 313rpx;"
            @click="tabChange(2)"
            class="cu-tag padding padding-lr-xl"
            :class="tabVal==2?'bg-'+theme.themeColor : (' line-'+theme.themeColor)"
          >
            二级（{{distributionPromotionTotal2}}）
          </view>
        </view>
      </view>
      <view
        class="margin-top"
        v-show="tabVal==1"
      >
        <view
          class="cu-list menu-avatar"
          v-for="item in distributionPromotionList1"
          :key="item.id"
        >
          <view class="flex justify-between align-center font-weight solid-bottom padding-left padding-right padding-bottom">
            <view class="flex align-center">
              <view
                class="cu-avatar round lg "
                :style="item.headimgUrl?'background-image:url(' + item.headimgUrl + ')':''"
              >
                {{!item.headimgUrl ? '头' : ''}}</view>
              <view class="text-sm margin-left-sm">{{item.nickName}}</view>
            </view>
            <view class="text-sm text-red margin-right-sm">{{item.distributionUser?'分销员':'非分销员'}}</view>
          </view>
        </view>
        <view
          :class="'cu-load ' + (loadmore1?'loading':'over')"
          style=""
        ></view>
      </view>
      <view
        class="margin-top"
        v-show="tabVal==2"
      >
        <view
          class="cu-list menu-avatar"
          v-for="item in distributionPromotionList2"
          :key="item.id"
        >
          <view class="flex justify-between align-center font-weight solid-bottom padding-left padding-right padding-bottom">
            <view class="flex align-center">
              <view
                class="cu-avatar round lg"
                :style="item.headimgUrl?'background-image:url(' + item.headimgUrl + ')':''"
              >
                {{!item.headimgUrl ? '头' : ''}}</view>
              <view class="text-sm margin-left-sm">{{item.nickName}}</view>
            </view>
            <view class="text-sm text-red margin-right-sm">{{item.distributionUser?'分销员':'非分销员'}}</view>
          </view>
        </view>
        <view :class="'cu-load ' + (loadmore2?'loading':'over')"></view>
      </view>
    </view>
  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'

export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      tabVal: 1,
      userInfo: {},
      distributionPromotionTotal: 0,
      distributionPromotionTotal1: 0,
      distributionPromotionTotal2: 0,
      distributionPromotionList1: [],
      distributionPromotionList2: [],
      page1: {
        current: 1,
        size: 10,
        //升序字段
        descs: 'create_time'
      },
      page2: {
        current: 1,
        size: 10,
        //升序字段
        descs: 'create_time'
      },
      parameter: {
        nickName: ''
      },
      loadmore1: true,
      loadmore2: true,
    }
  },

  onLoad () {
    this.userInfo = uni.getStorageSync('user_info')
    this.initData()
  },

  onReachBottom () {
    if (this.tabVal == 1) {
      if (this.loadmore1) {
        this.page1.current = this.page1.current + 1;
        this.getDistributionPromotionData(this.tabVal);
      }
    } else {
      if (this.loadmore2) {
        this.page2.current = this.page2.current + 1;
        this.getDistributionPromotionData(this.tabVal);
      }
    }

  },

  methods: {
    tabChange (val) {
      this.tabVal = val;
      if (val == 1) {
        this.parameter.parentId = this.userInfo.id
        this.parameter.parentSecondId = null
      } else {
        this.parameter.parentId = null
        this.parameter.parentSecondId = this.userInfo.id
      }
      // this.relod();
    },
    search (e) {

      this.relod()
      // this.getDistributionOrderData();
    },

    relod () {
      if (this.tabVal == 1) {
        this.loadmore1 = true;
        this.distributionPromotionList1 = []
        this.page1.current = 1;
      } else {
        this.loadmore2 = true;
        this.distributionPromotionList2 = []
        this.page2.current = 1;
      }
      this.getDistributionPromotionData(this.tabVal);
    },
    getDistributionPromotionData (tabVal) {
      let page = tabVal == 1 ? this.page1 : this.page2
      api.distributionPromotionPage(Object.assign({}, page, util.filterForm(this.parameter))).then(res => {
        let dataList = res.data.records;
        if (tabVal == 1) {
          this.distributionPromotionList1 = [...this.distributionPromotionList1, ...dataList];
          if (this.distributionPromotionTotal1 <= 0) {
            this.distributionPromotionTotal1 = res.data.total
            this.distributionPromotionTotal = this.distributionPromotionTotal1 + this.distributionPromotionTotal2
          }
          if (dataList.length < this.page1.size) {
            this.loadmore1 = false;
          }
        } else {
          this.distributionPromotionList2 = [...this.distributionPromotionList2, ...dataList];
          if (this.distributionPromotionTotal2 <= 0) {
            this.distributionPromotionTotal2 = res.data.total
            this.distributionPromotionTotal = this.distributionPromotionTotal1 + this.distributionPromotionTotal2
          }

          if (dataList.length < this.page2.size) {
            this.loadmore2 = false;
          }
        }

      });
    },
    initData () {
      api.distributionuser(this.userInfo.id).then(res => {
        if (res.data) {//是分销员
          this.distribution = res.data
        } else {//不是分销员

        }
      });
      this.tabChange(2)
      this.getDistributionPromotionData(2);
      this.tabChange(1)
      this.getDistributionPromotionData(1);
    }
  }
}
</script>

<style scoped>
page {
  background-color: #ffffff;
}
.cu-capsule.radius .cu-tag:last-child::after,
.cu-capsule.radius .cu-tag[class*='line-'] {
  border-radius: 50rpx;
}
.cu-capsule .cu-tag[class*='line-']:last-child::after {
  border: 2rpx solid #ff4444;
}
.cu-tag[class*='line-']::after {
  border-radius: 50rpx;
}

.cu-capsule .cu-tag[class*='line-']:first-child::after {
  border: 2rpx solid #ff4444;
}

.title-text {
  position: absolute;
  top: 320rpx;
  left: 35%;
  z-index: 999;
  color: #ffebac;
}

.title-text-1 {
  font-size: 68rpx;
  color: #ffebac;
}

.title-image {
  width: 100vh;
}

.promotion-list {
  /* border-radius: 30rpx 30rpx 0 0;
  margin-top: -30rpx; */
}

.font-weight {
  font-weight: 300;
}
</style>
