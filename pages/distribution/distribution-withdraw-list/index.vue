<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">提现记录</block>
    </cu-custom>
    <image
      mode="widthFix"
      style="width: 100%"
      src="https://img.songlei.com/distribution/distribution-withdraw-bg.png"
    >
    </image>

    <view class="text-center font-weight withdraw-list-bg">
      <view class="title-text">
        <view class="text-white">当前您已累计提现</view>
        <view class="live"></view>
        <view class="text-white margin-top-xs text-price text-xxl">
          <text class="margin-right-xs"></text>
          <text class="text-bold title-text-1">
            {{
              distribution.commissionWithdrawal
                ? distribution.commissionWithdrawal
                : '0'
            }}
          </text>
        </view>
      </view>
      <view style="position: absolute; top: 190rpx; left: 3%">
        <image
          mode="widthFix"
          style="width: 710rpx"
          src="https://img.songlei.com/distribution/distribution-withdraw.png"
        ></image>
      </view>
    </view>

    <view class="cu-list menu-avatar withdraw-list bg-white">
      <navigator
        class="cu-item margin-top padding-bottom"
        v-for="(item, index) in distributionWithdrawList"
        :key="index"
        :url="
          '/pages/distribution/distribution-withdraw-detail/index?id=' + item.id
        "
        hover-class="none"
      >
        <view
          class="cu-avatar round lg"
          :style="
            userInfo.headimgUrl
              ? 'background-image:url(' + userInfo.headimgUrl + ')'
              : ''
          "
        >
          {{ !userInfo.headimgUrl ? '头' : '' }}
        </view>

        <view class="content font-weight">
          <view>{{ item.paymentDetail }}</view>
          <view class="text-gray text-sm flex">{{ item.createTime }}</view>
        </view>

        <view>
          <view class="flex justify-end text-red text-sm margin-right-sm">
            {{ item.status | filterStatus }}
          </view>
          <view
            style="margin-right: 20rpx"
            class="text-xl text-bold text-red text-right text-price"
          >
            {{ item.applyAmount }}
          </view>
        </view>
        <text class="cuIcon-right text-gray text-xs"></text>
      </navigator>
    </view>
    <view
      class="margin-top120"
      :class="'cu-load ' + (loadmore ? 'loading' : 'over')"
    ></view>
  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'

export default {
  filters: {
    filterStatus (val) {
      let stauts = ['审核中', '审核通过', '审核不通过']
      return stauts[val]
    }
  },
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      userInfo: undefined,
      distribution: {},
      distributionWithdrawList: [],
      page: {
        current: 1,
        size: 10,
        //升序字段
        descs: 'create_time'
      },
      parameter: {
        orderId: ''
      },
      loadmore: true,
    }
  },
  onLoad () {
    this.userInfo = uni.getStorageSync('user_info')
    this.initData()
  },
  onShow () {
    this.distributionWithdrawList = []
    this.getDistributionWithdrawListData();
  },
  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.getDistributionWithdrawListData();
    }
  },
  methods: {
    getDistributionWithdrawListData () {
      api.userwithdrawRecordPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
        let dataList = res.data.records;
        this.distributionWithdrawList = [...this.distributionWithdrawList, ...dataList];
        if (dataList.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },
    initData () {
      api.distributionuser(this.userInfo.id).then(res => {
        if (res.data) { //是分销员
          this.distribution = res.data
        }
      });
    }
  }
}
</script>

<style>
page {
  background-color: #ffffff;
}
.live {
  margin: 0 auto;
  width: 220rpx;
  height: 5px;
  border-bottom: 1px dashed #fff;
}
.withdraw-list-bg {
  height: 260rpx;
}

.title-text {
  position: absolute;
  top: 300rpx;
  z-index: 1024;
  text-align: center;
  width: 100%;
}

.title-text-1 {
  font-size: 68rpx;
}

.title-image {
  width: 100vh;
}

.withdraw-list {
  border-radius: 30rpx !important;
  margin: -35rpx auto;
  border-radius: 10rpx;
}

.margin-top120 {
  margin-top: 120rpx;
}

.font-weight {
  font-weight: 300;
}
</style>
