<template>
  <view>
    <cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">支付密码设置</block>
	  </cu-custom>

		<view style="margin-top: 290rpx;">
			<view class="text-sm text-center" style="color: #000000;">
				请设置您的支付密码
			</view>
			
			<code-page @change="code($event)" :passwordShow="true" :source="'distribution'" :clear="setClear"/>

			<view class="flex justify-center">
        <button
					:disabled="disabled"
          class="cu-btn margin-tb-sm lg login_padding"
          :class="'bg-'+theme.backgroundColor"
          @click="nextStep"
        >下一步</button>
      </view>
		</view>
  </view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
	import codePage from "@/components/verification-code/index.vue";

	export default {
		components: {
			codePage
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '密码设置',
				isPasswordSet:false,//是否设置密码
				pwd:'',
				disabled:true,//控制按钮
				setClear:true,//控制输入框是不清空
				type:'0'//0 设置 1 修改
			};
		},
		props: {},
		onLoad(options) {
			// if (options) {
			// 	if (options.type) {
			// 		this.type = options.type
			// 	}
			// }
		},
		mounted() {
			
		},

		onShow() {
			// 禁止分享
			uni.hideShareMenu()
			this.setClear = false;
			this.disabled =true
		},

		onHide() {
		},

		computed: {
		},

		methods: {
		
		//下一步
		nextStep(){
			this.setClear = true;
			console.log("下一步");
			uni.navigateTo({
				url: `/pages/distribution/set-confirm-password/index?password=${this.pwd}`
			})
		},

		//输入框
		async	code(val){
				console.log("val",val);
				if (val&&val.length=='6') {
					this.disabled = false;
					this.pwd = val;
				}else{
					this.disabled = true;
					this.setClear = false;
				}
			},
    }
	};
</script>

<style lang="scss" scoped>
.login_padding {
  width: 343rpx;
  height: 80rpx;
  margin-top: 97rpx;
  background: linear-gradient(90deg, #FF8362 0%, #FF4F00 100%);
  /* border-radius: 40rpx; */
}
</style>
