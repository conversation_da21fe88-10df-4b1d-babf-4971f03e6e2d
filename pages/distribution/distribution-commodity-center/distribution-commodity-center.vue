<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{ pageType == 'my' ? '我的分销' : '分销中心' }}</block>
		</cu-custom>
		<view class="navTab" id="navTab">
			<scroll-view :scroll-x="true" style="white-space: nowrap;">
				<view class="tabBox" style="background: #eee;">
					<view class="tabList" v-for="(item,index) in tablist" :key="index">
						<view :class="[index == tabindex ? 'activetab' : 'list']" @click="tabclick(item, index)">{{ item.name }}</view>
					</view>
				</view>
			</scroll-view>
			<view
				class="justify-center bg-white solid-bottom "
			>
				<view class="grid response text-center align-start">
					<view
						class="flex-sub margin-xs radius overflow text-xdf"
						@tap="sortHandle"
						data-type="title"
						:class="title == 'title' ? 'text-bold text-' + theme.themeColor : ''"
					>默认</view>

					<view class="flex-sub margin-xs radius">
						<view 
							class="grid text-center text-xdf "
							@tap="sortHandle"
							data-type="sales"
							:class="sales=='sales' ? 'text-' + theme.themeColor : ''"
						>
							销量
							<!-- <view class="margin-left-xs" style="font-size: 36rpx; line-height: 36rpx; ">
								<view :class="'cuIcon-triangleupfill ' + (sales=='asc' ? 'text-'+theme.themeColor : '')" data-type="sales"></view>
								<view class="basis-df"></view>
								<view :class="'cuIcon-triangledownfill ' + (sales=='desc' ? 'text-'+theme.themeColor : '')" data-type="sales"></view>
							</view> -->
						</view>
					</view>

					<view class="flex-sub margin-xs radius">
						<view class="grid text-center text-xdf" @tap="sortHandle" data-type="price">
							<text :class="price != '' ? 'text-' + theme.themeColor : ''" data-type="price">价格</text>
							<view class="margin-left-xs" style="font-size: 36rpx; line-height: 36rpx; ">
								<view :class="'cuIcon-triangleupfill ' + (price=='asc' ? 'text-'+theme.themeColor : '')" data-type="price"></view>
								<view class="basis-df"></view>
								<view :class="'cuIcon-triangledownfill ' + (price=='desc' ? 'text-'+theme.themeColor : '')" data-type="price"></view>
							</view>
						</view>
					</view>

					<view class="flex-sub margin-xs radius">
						<view 
							class="grid text-center text-xdf" 
							@tap="sortHandle" 
							data-type="rebateAmount"
							:class="rebateAmount=='rebateAmount' ? 'text-' + theme.themeColor : ''"
						>
							返佣金额
							<!-- <view class="margin-left-xs" style="font-size: 36rpx; line-height: 36rpx; ">
								<view :class="'cuIcon-triangleupfill ' + (rebateAmount=='asc' ? 'text-'+theme.themeColor : '')" data-type="rebateAmount"></view>
								<view class="basis-df"></view>
								<view :class="'cuIcon-triangledownfill ' + (rebateAmount=='desc' ? 'text-'+theme.themeColor : '')" data-type="rebateAmount"></view>
							</view> -->
						</view>
					</view>

				</view>
			</view>
		</view>
			

		<scroll-view scroll-y :style="{height: `calc(100vh - ${CustomBar}px - ${navHeight}px)`}" @scrolltolower="getMore">
			
			<template>
				<goods-row  ref="goodscard" :goodsList="goodsList" :pageType="pageType == 'my' ? 'retailStoreMy' : 'retailStoreAll'" ></goods-row>
			</template>

			<view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
		</scroll-view>
	</view>
</template>
guoshen
<script>
	const app = getApp();
	import {distributionpage, redesCategorySecond,myCategorySecond, distributionorderitempage} from "@/pages/distribution/api/distribution.js"

	import goodsRow from "components/goods-row/index";
	import { mapState } from 'vuex'
	export default {
		components: { goodsRow },
		computed: {
			...mapState([ 'CustomBar' ])
		},

		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				//测试
				background: {
					backgroundColor: '#000'
				},
				height: 0,
				navHeight: 0,
				page: {
					current: 1,
					size: 10,
					ascs: '', // 升序字段
					descs: 'goods_spu.update_time', // 降序
				},
				loadmore: true,
				goodsList: [],
				price: '',
				sales: '',
				title: 'title',
				rebateAmount: '', // 返回佣金
				page_title: '分销商品中心', // 有数统计使用
				tablist:[{name: '全部', id: ''}], // TAB分类列表
				tabindex: 0,
				pageType: 'all'
			};
		},

		async onLoad(options) {

			let query = wx.createSelectorQuery()
			query.select('#navTab').boundingClientRect() 
			await query.exec((res) => {
				// this.topHeight = res[0].height
				this.navHeight = res[0].height
			})

			this.pageType = options.type || 'all'
			app.initPage().then(async res => {
				if(this.pageType == 'my' ){
					await this.myCategorySecond();
				}else {
					await this.redesCategorySecond();
				}
				await this.goodsPage(); //就第一次掉店铺信息传入，后续就没有店铺信息了
			});

		},

		methods: {
			tabclick(item, index){
				this.tabindex  = index
				this.page.categorySecond = item.id
				this.goodsList = []
				this.loadmore = true
				this.page.current = 1
				this.goodsPage()
			},

			//获取分类的 列表
			async redesCategorySecond() {
				const res = await redesCategorySecond()
				if(res.data&&res.data.length>0){
					this.tablist.push(...res.data)
				}
				this.page.ascs = '';
				this.page.descs = 'goods_spu.update_time';
			},
			
			//获取我的分类的 列表
			async myCategorySecond() {
				const res = await myCategorySecond()
				if(res.data&&res.data.length>0){
					this.tablist.push(...res.data)
				}
				this.page.ascs = '';
				this.page.descs = 'goods_spu.update_time';
			},

			getMore() {
				if (this.loadmore) {
					this.page.current = this.page.current + 1;
					this.goodsPage();
				}
			},

			//搜索列表
			goodsPage() {
				if (this.page.categorySecond == '') {
					delete this.page.categorySecond
				}
				if (this.pageType == 'all') { // 全部分销商品
					distributionpage(this.page).then(res => {
						let data = res.data.records
						this.goodsList = [...this.goodsList, ...data];
						if (this.goodsList.length >= res.data.total) {
							this.loadmore = false;
						}
					})
				} else if (this.pageType == 'my') { // 我的分销商品
					distributionorderitempage(this.page).then(res => {
						let data = res.data.records
						this.goodsList = [...this.goodsList, ...data];
						if (this.goodsList.length >= res.data.total) {
							this.loadmore = false;
						}
					})
				}
			},

			sortHandle(e) {
				let type = e.target.dataset.type;
				console.log("price11", type );
				switch (type) {
					case 'price': // 价格
						if (this.price == '') {
							this.price = 'desc';
							this.page.current = 1;
							this.page.ascs = '';
							this.page.descs = 'goods_spu.price_down';
						} else if (this.price == 'desc') {
							this.price = 'asc';
							this.page.current = 1;
							this.page.ascs = 'goods_spu.price_down';
							this.page.descs = '';
						} else if (this.price == 'asc') {
							this.price = '';
							this.page.current = 1;
							this.page.ascs = '';
							this.page.descs = '';
						}
						this.title = ''
						this.sales = '';
						this.createTime = '';
						this.rebateAmount = ''
						break;
					case 'sales': // 销量
						// if (this.sales == '') {
							this.sales = 'sales';
							// this.sales = 'descs';
							this.page.current = 1;
							this.page.ascs = '';
							this.page.descs = 'goods_spu.sale_num';
						// } else if (this.sales == 'desc') {
						// 	this.sales = 'asc';
						// 	this.page.current = 1;
						// 	this.page.ascs = 'goods_spu.sale_num';
						// 	this.page.descs = '';
						// } else if (this.sales == 'asc') {
						// 	this.sales = '';
						// 	this.page.current = 1;
						// 	this.page.ascs = '';
						// 	this.page.descs = '';
						// }
						this.title = ''
						this.price = '';
						this.rebateAmount = ''
						break;
					case 'title': // 综合推荐综合推荐
						this.title = 'title'
						this.page.descs = 'goods_spu.update_time'
						this.tabindex = 0
						this.page.categorySecond = ''
						this.page.current = 1;
						this.price = '';
						this.sales = '';
						this.rebateAmount = ''
						break;
					case 'rebateAmount': //返回佣金
					// if (this.rebateAmount == '') {
							this.rebateAmount = 'rebateAmount';
							// this.rebateAmount = 'descs';
							this.page.current = 1;
							this.page.ascs = '';
							this.page.descs = this.pageType == 'my' ? 'distributionCommissionNum' : 'rebate_amt';
						// } else if (this.rebateAmount == 'desc') {
						// 	this.rebateAmount = 'asc';
						// 	this.page.current = 1;
						// 	this.page.ascs = 'sku.rebate_amount';
						// 	this.page.descs = '';
						// } else if (this.rebateAmount == 'asc') {
						// 	this.rebateAmount = '';
						// 	this.page.current = 1;
						// 	this.page.ascs = '';
						// 	this.page.descs = '';
						// }
						this.title = ''
						this.price = '';
						this.sales = '';
						break;
				}
				this.goodsList = [];
				this.loadmore = true;
				this.goodsPage();
			},

			// relod() {
			// 	this.loadmore = true;
			// 	this.goodsList = [];
			// 	this.page.start = 0;
			// 	this.page.current = 1;
			// 	this.goodsPage();
			// },

		}
	};
</script>
<style scoped lang="scss">
.topfixed-active {
	width: 100%;
	position: fixed;
	top: 0rpx;
	left: 0rpx;
	background: #fff;
	z-index: 9999;
	box-sizing: border-box;
}

.cuIcon-triangledownfill {
	margin-top: -22rpx;
}
.navTab {
	background: white; 
	margin: 0 auto;
	.tabBox {
		width: 100%;
		display: flex;
	}
	
	.tabList {
		margin: 10rpx;
		.list {
			display: inline-block;
			padding: 20rpx;
		}
		.activetab{
			color: red;
			padding: 20rpx;
			display: inline-block;
		}
	}
	
	.tabList:first-child {
		margin-left: 0;
	}
}
</style>
