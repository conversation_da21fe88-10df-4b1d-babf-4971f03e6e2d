<!-- 中间页面二次跳转页面 -->
<template>
	<view>
		
	</view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	export default {
		data() {
			return {
				
			}
		},
		onLoad() {
			const { nGo } = app.globalData;
			//nGo 1 正常跳转  2 没登录直接跳转登录，登录成功跳转到添加收货地址界面，收货地址之后进入领取可乐弹框界面
			console.log("nGo====>",nGo);
			console.log("util.isUserLogin()====>",util.isUserLogin());
			if(nGo==2 && !util.isUserLogin()) {
				uni.reLaunch({
					url: '/pages/login/index?reUrl='+encodeURIComponent(`/pages/user/user-address/form/index?nGo=${nGo}`)
				});
			}else {
				uni.reLaunch({
					url:"/pages/home/<USER>"
				})
			}
		},
		methods: {
			
		}
	}
</script>

<style>

</style>
