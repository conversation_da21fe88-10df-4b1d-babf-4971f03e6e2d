<template>
  <view>
	  <view v-if="scene !=1154" style="padding-bottom: 110rpx;">
		  <divComponents
		    ref="divComponents"
		    :microId="pagePathId"
		    :divType="pageState"
		    :showPage="showPage"
		    :isBack="false"
		  />
	  </view>
    
    <share-single-page v-if="scene==1154"/>
	
  </view>
</template>

<script>
import divComponents from "@/components/div-components/index.vue";
import shareSinglePage from "@/components/share-single-page/index.vue";
const util = require("utils/util.js");

const app = getApp();
export default {
  components: {
    divComponents,
    shareSinglePage,
  },
  data () {
    return {
      pagePathId: null, //微页面id
      pageState: '',
      showPage: true,
      scene: '',
      //有数统计使用
      page_title: '松鼠好物'
    }
  },
  // 底层组件传值
  provide () {
    return {
      reachBottom: this.onReachBottom
    }
  },
  onLoad (options) {
    // #ifdef MP-WEIXIN    
    let getLaunchOptions = uni.getLaunchOptionsSync()
    this.scene = getLaunchOptions.scene
    //场景值等于 1154 分享单页模式
    if (this.scene && this.scene == 1154) return
    console.log("this.scene", this.scene);
    // #endif
    util.saveSharerUserCode(options);
    const tabBar = uni.getStorageSync('tabBar');
    /* 根据缓存中的tabbar pagePathId 字段判断 当前页面是展示微页面还是类别 */
    if (tabBar && tabBar[1] && tabBar[1].pagePathId > 0) {
      if (tabBar[1].pagePathId) {
        this.pagePathId = tabBar[1].pagePathId;
        this.pageState = 'micro';
        // this.getMicroPageData(tabBar[1].pagePathId);
      } else {
        this.pageState = 'home';
      }
    }
  },


  onShow () {
    app.shoppingCartCount();
    // 再次进入页面时，刷新新人专享数据
    EventBus.$emit("refreshNewCustomerDialog")
  },

  onShareAppMessage: function () {
    let share = this.$refs.divComponents.shares;
    return share
  },

  onPullDownRefresh () {
    this.$refs.divComponents.pullDownRefresh();
  },
  // 触底执行
  onReachBottom () {
    EventBus.$emit("divGoodsGroupsReachBottom", "true")
  },
  onPageScroll (res) {
    uni.$emit('vonPageScroll', res);
    uni.$emit('vonVideoPageScroll', res);
  },
  onUnload () {
    EventBus.$off('divGoodsGroupsReachBottom')
    // 新人专享弹框
    EventBus.$off('refreshNewCustomerDialog')
  }
};
</script>
