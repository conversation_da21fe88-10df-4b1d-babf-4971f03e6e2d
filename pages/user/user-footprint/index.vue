<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">我的足迹</block>
    </cu-custom>

    <view class="cu-list menu-avatar">
      <navigator
        class="goods-item cu-item"
        hover-class="none"
        :url="'/pages/goods/goods-detail/index?id=' + item.goodsSpu.id + '&source_module=浏览足迹'"
        v-for="(item, index) in userFootprint"
        :key="index"
        :class="
          'cu-item ' + (modalName == 'move-box-' + index ? 'move-cur' : '')
        "
        :data-target="'move-box-' + index"
      >
        <view
          class="cu-avatar image-box"
          :style="'background-image:url(' + item.goodsSpu.picUrls[0] + ');'"
        ></view>

        <view class="padding-right-sm goods-detail">
          <view class="text-df overflow-2">{{ item.goodsSpu.name }}</view>
          <view class="text-gray text-sm overflow-1">{{
            item.goodsSpu.sellPoint
          }}</view>
          <view class="text-sm">已售{{ item.goodsSpu.saleNum }}</view>
          <view class="margin-right-sm text-gray">{{ item.createTime }}</view>
          <view class="flex justify-between align-center">
            <view class="text-price text-xl text-bold text-red">
              {{
              item.goodsSpu.priceDown
            }}
              <text
                v-if="item.estimatedPriceVo&&item.priceDown!=item.estimatedPriceVo.price"
                class="text-red text-xs"
                style="font-weight: 500;padding-left: 6rpx"
              >劵后价</text>

              <text
                v-if="item.inventoryState==1"
                class="text-gray text-xs"
                style="font-weight: 500;padding-left: 6rpx"
              >已售罄
              </text>

              <text
                v-if="item.estimatedPriceVo&&item.estimatedPriceVo.originalPrice!='0.0'&&item.priceDown!= item.estimatedPriceVo.originalPrice"
                class="price-original text-xs"
              >￥{{ item.estimatedPriceVo.originalPrice }}
              </text>
            </view>
            <view
              class="cu-btn round check-details margin-right-sm sm"
              style="
                box-shadow: none;
                background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%);
              "
              :class="'bg-' + theme.themeColor"
            >
              查看详情</view>
          </view>
        </view>

        <view
          class="move"
          @tap.stop
        >
          <view
            class="bg-red"
            @tap="userCollectDel"
            :data-index="index"
          >删除</view>
        </view>
      </navigator>
    </view>
    <view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'

export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      page: {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: '',
        //升序字段
        descs: 'create_time'
      },
      parameter: {},
      loadmore: true,
      userFootprint: [],
      ListTouchStart: "",
      ListTouchDirection: "",
      modalName: "",
      //有数统计使用
      page_title: '我的足迹'
    };
  },

  components: {},
  props: {},

  onLoad (options) {
    app.initPage().then(res => {
      this.userFootprintPage();
    });
  },

  onShow (options) { },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.userFootprintPage();
    }
  },

  methods: {
    //删除
    userCollectDel (e) {
      let that = this;
      let index = e.target.dataset.index;
      let userCollect = this.userCollect;
      uni.showModal({
        content: '确定删除收藏吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',

        success (res) {
          if (res.confirm) {
            // api.userCollectDel(userCollect[index].id).then(res => {
            //   userCollect.splice(index, 1);
            //   that.userCollect = userCollect;
            // });
          }
        }

      });
    },

    // // ListTouch触摸开始
    // ListTouchStartFun (e) {
    //   this.ListTouchStart = e.touches[0].pageX;
    // },

    // // ListTouch计算方向
    // ListTouchMove (e) {
    //   this.ListTouchDirection = e.touches[0].pageX - this.ListTouchStart > 0 ? 'right' : 'left';
    // },

    // // ListTouch计算滚动
    // ListTouchEnd (e) {
    //   if (this.ListTouchDirection == 'left') {
    //     this.modalName = e.currentTarget.dataset.target;
    //   } else {
    //     this.modalName = null;
    //   }
    //   this.ListTouchDirection = null;
    // },

    userFootprintPage () {
      api.userFootprintPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
        let userFootprint = res.data.records;
        this.userFootprint = [...this.userFootprint, ...userFootprint];
        if (userFootprint.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },
  }
};
</script>
<style>
.collection-types {
  top: unset !important;
}

.goods-item {
  height: 260rpx !important;
}

.store-item {
  height: 260rpx !important;
}

.image-box {
  width: 200rpx;
  height: 200rpx;
}

.goods-detail {
  width: 480rpx;
}

.store-detail {
  width: 480rpx;
}
</style>
