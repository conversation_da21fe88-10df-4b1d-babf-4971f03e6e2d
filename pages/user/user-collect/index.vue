<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">我的收藏</block>
    </cu-custom>

    <!-- 导航按钮 -->
    <scroll-view
      scroll-x
      class="bg-white nav fixed collection-types"
      style="box-shadow: none"
    >
      <view
        class="flex text-center"
        style="background-color: #eee"
      >
        <view
          :class="
            'btn flex-sub ' +
            (index == tabCur ? 'cur bg-' + theme.backgroundColor : '')
          "
          v-for="(item, index) in collectType"
          :key="index"
          @tap="tabSelect"
          :data-index="index"
          :data-key="item.key"
        >{{ item.value }}</view>
      </view>
    </scroll-view>

    <!-- 商品列表 -->
    <view
      class="cu-list menu-avatar goods-list"
      style="border-radius: 21rpx; margin-left: 10rpx; margin-right: 10rpx"
    >
      <template v-for="(item, index) in userCollect">
        <navigator
          class="goods-item goods-border"
          hover-class="none"
          :url="'/pages/goods/goods-detail/index?id=' + item.goodsSpu.id + '&source_module=商品收藏'"
          v-if="tabCur == 0 && item.goodsSpu"
          :class="
          'cu-item ' + (modalName == 'move-box-' + index ? 'move-cur' : '')
        "
          :key="index"
          @touchstart="ListTouchStartFun"
          @touchmove="ListTouchMove"
          @touchend="ListTouchEnd"
          :data-target="'move-box-' + index"
        >
          <view
            class="cu-avatar image-box"
            :style="'background-image:url(' + item.goodsSpu.picUrls[0] + ');'"
          ></view>

          <view class="padding-right-sm goods-detail">
            <view class="text-df overflow-2">{{ item.goodsSpu.name }}</view>

            <view class="text-gray text-sm overflow-1">{{
            item.goodsSpu.sellPoint
          }}</view>

            <view class="text-gray text-sm">已售{{ item.goodsSpu.saleNum }}</view>

            <view class="flex justify-between align-center">
              <view class="text-price text-xl text-bold text-red">
                {{item.goodsSpu.priceDown }}
                <text
                  v-if="item.estimatedPriceVo&&item.priceDown!=item.estimatedPriceVo.price"
                  class="text-red text-xs"
                  style="font-weight: 500;padding-left: 6rpx"
                >劵后价</text>
                <text
                  v-if="item.inventoryState==1"
                  class="text-gray text-xs"
                  style="font-weight: 500;padding-left: 6rpx"
                >已售罄
                </text>

                <text
                  v-if="item.estimatedPriceVo&&item.estimatedPriceVo.originalPrice!='0.0'&&item.priceDown!= item.estimatedPriceVo.originalPrice"
                  class="price-original text-xs"
                >￥{{ item.estimatedPriceVo.originalPrice }}
                </text>
              </view>
              <view
                class="cu-btn round check-details margin-right-sm sm"
                style="
                box-shadow: none;
                background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%);
              "
                :class="'bg-' + theme.themeColor"
              >
                查看详情
                <!-- <text class="cuIcon-cart"></text> -->
              </view>
            </view>
          </view>
          <view
            class="move"
            @tap.stop
          >
            <view
              class="bg-red"
              @tap="userCollectDel"
              :data-index="index"
            >删除</view>
          </view>
        </navigator>
      </template>

      <navigator
        class="store-item"
        hover-class="none"
        :url="'/pages/shop/shop-detail/index?id=' + item.shopInfo.id"
        v-if="tabCur == 1"
        :class="
          'cu-item ' + (modalName == 'move-box-' + index ? 'move-cur' : '')
        "
        v-for="(item, index) in userCollect"
        :key="index"
        @touchstart="ListTouchStartFun"
        @touchmove="ListTouchMove"
        @touchend="ListTouchEnd"
        :data-target="'move-box-' + index"
      >
        <view
          class="cu-avatar image-box"
          :style="
            'background-image:url(' +
            item.shopInfo.imgUrl +
            ');border-radius: 12px'
          "
        ></view>

        <view class="padding-right-sm store-detail">
          <view class="text-df overflow-2">{{ item.shopInfo.name }}</view>

          <view
            class="text-sm text-gray overflow-2 margin-top-xs"
            v-if="item.shopInfo.address"
          ><text class="cuIcon-locationfill"></text><text class="margin-left-xs">{{
              item.shopInfo.address
            }}</text></view>

          <view class="excessive-banner-text">
            <view>
              <text
                v-if="item.shopInfo.selfSupport == 1"
                class="cu-btn round excessive-sm"
                style="background-color: #ff2727"
              >松鼠自营</text>
              <text
                class="cu-btn round excessive-sm"
                style="background-color: #ff9d31"
              >上新</text>
              <text
                class="excessive-follow"
                v-if="item.shopInfo.collectCount >0"
              >{{ item.shopInfo.collectCount }}人关注</text>
            </view>
          </view>

          <view class="flex justify-between align-center">
            <view class="text-sm text-gray margin-top-xs"><text class="cuIcon-mobilefill"></text><text class="margin-left-xs">{{ item.shopInfo.phone }} </text>
            </view>

            <view
              class="cu-btn round check-details margin-right-sm sm"
              style="
                box-shadow: none;
                background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%);
              "
              :class="'bg-' + theme.themeColor"
            >进入店铺</view>
          </view>
        </view>
        <view
          class="move"
          @tap.stop
        >
          <view
            class="bg-red"
            @tap="userCollectDel"
            :data-index="index"
          >删除</view>
        </view>
      </navigator>
    </view>

    <!-- 猜你喜欢 -->
    <view
      v-if="loadmore"
      :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"
    ></view>
    <recommendComponents
      v-if="!loadmore"
      canLoad
    />
  </view>
</template>

<script>
import recommendComponents from "components/recommend-components/index";

const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'


export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      collectType: [{
        value: '商品',
        key: '1'
      }, {
        value: '店铺',
        key: '2'
      }],
      tabCur: 0,
      page: {
        searchCount: false,
        current: 1,
        size: 15,
        ascs: '',
        //升序字段
        descs: 'create_time'
      },
      parameter: {
        type: '1'
      },
      loadmore: true,
      userCollect: [],
      ListTouchStart: "",
      ListTouchDirection: "",
      modalName: "",
      //有数统计使用
      page_title: '我的收藏'
    };
  },

  components: {
    recommendComponents
  },
  props: {},

  onLoad (options) {
    app.initPage().then(res => {
	  this.tabCur = options.tabCur||0;
	  this.parameter.type = this.tabCur==0?1:2;
	  this.refresh();
    });
  },

  onShow (options) { },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.userCollectPage();
    }
  },

  methods: {
    tabSelect (e) {
      let dataset = e.currentTarget.dataset;
      if (dataset.index != this.tabCur) {
        this.tabCur = dataset.index;
        this.parameter.type = dataset.key;
        this.refresh();
      }
    },
    refresh () {
      this.loadmore = true;
      this.userCollect = [];
      this.page.current = 1;
      this.userCollectPage();
    },
    userCollectPage () {
      api.userCollectPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
        let userCollect = res.data.records;
        this.userCollect = [...this.userCollect, ...userCollect];
        if (userCollect.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },

    // ListTouch触摸开始
    ListTouchStartFun (e) {
      this.ListTouchStart = e.touches[0].pageX;
    },

    // ListTouch计算方向
    ListTouchMove (e) {
      this.ListTouchDirection = e.touches[0].pageX - this.ListTouchStart > 0 ? 'right' : 'left';
    },

    // ListTouch计算滚动
    ListTouchEnd (e) {
      if (this.ListTouchDirection == 'left') {
        this.modalName = e.currentTarget.dataset.target;
      } else {
        this.modalName = null;
      }
      this.ListTouchDirection = null;
    },

    userCollectDel (e) {
      let that = this;
      let index = e.target.dataset.index;
      let userCollect = this.userCollect;
      uni.showModal({
        content: '确定删除收藏吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',

        success (res) {
          if (res.confirm) {
            api.userCollectDel(userCollect[index].id).then(res => {
              userCollect.splice(index, 1);
              that.userCollect = userCollect;
            });
          }
        }

      });
    }

  }
};
</script>
<style  scoped>
.excessive-follow {
  font-size: 17rpx;
  height: 25rpx;
}

.excessive-banner-text {
  display: block;
  flex-direction: row;
  width: 365rpx;
}

.excessive-sm {
  padding: 0 8rpx;
  margin-right: 6rpx;
  font-size: 17rpx;
  height: 25rpx;
  color: #ffffff;
}

.btn {
  height: 50rpx;
  width: 352rpx;
  line-height: 50rpx;
  margin: 12rpx 7rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  background-color: #fff;
}

.goods-border {
  border-bottom: 1rpx dashed #eee;
  padding: 0 20rpx;
}
</style>

<style>
.collection-types {
  top: unset !important;
}

.goods-list {
  margin-top: 90rpx;
}

.goods-item {
  height: 260rpx !important;
}

.store-item {
  height: 260rpx !important;
}

.image-box {
  width: 200rpx;
  height: 200rpx;
}

.goods-detail {
  width: 480rpx;
}

.store-detail {
  width: 480rpx;
}
</style>
