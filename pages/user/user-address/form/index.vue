<template>
	<view style="background-color: #FFFFFF; min-height: 100vh;">
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{userAddress.id?'编辑':'新增'}}收货地址</block>
		</cu-custom>

		<form @submit="userAddressSave" style="position: relative;">
			<map v-if="userAddress.latitude>0 && userAddress.longitude>0" :style="{
					height: '500rpx',
					width: '750rpx',
					position: 'absolute',
					top:0
				}" :latitude="userAddress.latitude" :longitude="userAddress.longitude" :markers="covers">
			</map>
			<!-- #ifdef APP -->
			<view style="position: absolute;top: 510rpx; width: 750rpx;">
				<view class="cu-form-group  group-item" style="border-radius: 30rpx;">
				<!-- #endif -->
					<!-- #ifdef MP -->
					<view style="position: absolute;top: 320rpx; width: 750rpx;">
						<view class="cu-form-group  group-item"
							style="border-radius: 30rpx;margin-left: 10rpx; margin-right: 10rpx;">
						<!-- #endif -->
							<view class="title">详细地址</view>
							<input class="text-right" placeholder="请输入街道、楼牌号等" maxlength="50" name="detailInfo"
								:value="userAddress.detailInfo" @blur="e=>handleBlur(e)"></input>
							<view @click="handleClickMap"
								style="height: 120rpx; width: 100rpx;line-height: 120rpx;display: flex;align-items: center;">
								<image style="width: 32rpx; height: 32rpx; vertical-align: bottom;"
									src="https://img.songlei.com/live/location.png"></image>定位
							</view>
						</view>
						<!-- #ifdef APP -->
						<view class="cu-form-group group-item">
						<!-- #endif -->
							<!-- #ifdef MP -->
							<view class="cu-form-group group-item" style="margin-top: 30rpx;">
							<!-- #endif -->

								<view class="title">地址选择</view>
								<!-- #ifndef H5 || APP-PLUS || MP-ALIPAY -->
								<picker mode="region" @change="regionChange" :value="region">
									<view class="picker">{{region[0]}}，{{region[1]}}，{{region[2]}}</view>
								</picker>
								<!-- #endif -->
								<!-- #ifdef H5 || APP-PLUS || MP-ALIPAY -->
								<region-picker @change="regionChange" :value="region">
									<view class="picker">{{region[0]}}，{{region[1]}}，{{region[2]}}</view>
								</region-picker>
								<!-- #endif -->
							</view>

							<view class="cu-form-group  group-item">
								<view class="title">收货人</view>
								<input class="text-right" placeholder="请输入姓名" maxlength="10" name="userName"
									:value="userAddress.userName"></input>
								<image @click="handleChooseContact" style="height:60rpx; width:60rpx;"
									src="https://img.songlei.com/live/icon_phone.png"></image>
							</view>
							<view class="cu-form-group group-item">
								<view class="title">联系电话</view>
								<input class="text-right" placeholder="请输入电话" maxlength="20" name="telNum"
									:value="userAddress.telNum"></input>
							</view>

							<view class="cu-form-group group-item" style="border-bottom: 1rpx solid #eee;">
								<view class="title">设为默认地址</view>
								<switch :class="userAddress.isDefault == '1'?theme.themeColor+' checked ':' '"
									:checked="userAddress.isDefault == '1'" @change="isDefaultChange"></switch>
							</view>
							<view class="compile">
								<!-- #ifdef MP-WEIXIN -->
								<button style="width: 513rpx;height: 80rpx; font-size: 26rpx;"
									class="cu-btn shadow-blur block bg-green margin-sm round bottom-btn"
									@tap="getWxAddress"><text class=" cuIcon-weixin">导入微信地址</text></button>
								<!-- #endif -->
								<button style="width: 513rpx;height: 80rpx;background: #FF2C00; font-size: 26rpx;"
									class="cu-btn shadow-blur block margin-sm round bottom-btn"
									:class="'bg-'+theme.themeColor" formType="submit">立即保存</button>

								<button style="width: 513rpx;height: 80rpx;font-size: 26rpx;"
									class="cu-btn shadow-blur block bg-red margin-sm round bottom-btn"
									@tap="userAddressDelete" v-if="userAddress.id">删除</button>
							</view>
						</view>
		</form>
		<uni-popup ref="perpopup" type="center" :mask-click='false'>
			<view class="permissions_box">
				当您使用APP时，新增编辑收货地址的时候需要位置权限，不授权上述权限，不影响APP其他功能使用。
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'
	import validate from 'utils/validate'
	import __config from 'config/env';
	import regionPicker from "../components/region-picker/region-picker.vue"
	import QQMapWX from 'public/jweixin-module/qqmap-wx-jssdk.min.js';
	var qqmapsdk;

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				region: ['选择省', '选择市', '选择区'],
				userAddress: {
					isDefault: '0',
					userAddress: '',
					telNum: '',
					detailInfo: '',
					latitude: '',
					longitude: ''
				},
				currentLocation: {},
				covers: [],
				canSearchPoi: true,
				//有数统计使用
				page_title: '新增或编辑地址',
				// windowHeight:'',
				nGo: '', //新用户完善收货地址之后的跳转路径
				isNotify: 0 // 是否需要发$EventBus 全局通知，1 需要 0 不需要
			};
		},

		components: {
			regionPicker
		},
		props: {},

		onLoad(options) {
			const {
				nGo,
				isNotify
			} = options;
			this.isNotify = isNotify;
			this.nGo = nGo;
			// 本地获取参数信息
			let that = this;

			// uni.getSystemInfo({
			// 	success: function(res) {
			// 		that.windowHeight = res.windowHeight;
			// 	}
			// });
			if (nGo != 2) {
				uni.getStorage({
					key: 'param-userAddressForm',
					success: function(res) {
						let userAddress = res.data;
						that.currentLocation = userAddress
						that.region[0] = userAddress.provinceName ? userAddress.provinceName : '选择省';
						that.region[1] = userAddress.cityName ? userAddress.cityName : '选择市';
						that.region[2] = userAddress.countyName ? userAddress.countyName : '选择区';
						if (userAddress.userName) {
							userAddress.isDefault = userAddress.isDefault ? userAddress.isDefault : "0";
							that.userAddress = userAddress;
							that.covers = [];
							that.covers.push({
								latitude: userAddress.latitude,
								longitude: userAddress.longitude,
								width: 30,
								height: 30,
								label: {
									content: userAddress.detailInfo,
									fontSize: 12,
									bgColor: '#1E90FF',
									color: '#ffffff',
									borderRadius: 12,
									padding: 2
								},
								iconPath: 'https://img.songlei.com/live/location-map.png'
							});
						} else {
							that.checkPosPermission();
						}
						//使用完，清除需要编辑的信息
						uni.setStorage({
							key: 'param-userAddressForm',
							data: []
						});
					}
				});
			} else {
				that.checkPosPermission();
			}

			// 实例化API核心类
			qqmapsdk = new QQMapWX({
				key: __config.mapKey
			});
		},
	
		methods: {
			checkPosPermission() {
				// #ifdef MP-WEIXIN
				this.getCurrentPos();
				// #endif 
				// #ifdef APP-PLUS
				let that = this;
				var platform = uni.getSystemInfoSync().platform;
				if (platform == 'android') {
					plus.android.checkPermission(
						'android.permission.ACCESS_FINE_LOCATION',
						granted => {
							if (granted.checkResult == -1) {
								//弹出
								that.$refs.perpopup.open('top')
							} else {
								//执行你有权限后的方法
								that.getCurrentPos();
							}
						},
						error => {
							console.error('Error checking permission:', error.message);
						}
					);
					plus.android.requestPermissions(['android.permission.ACCESS_FINE_LOCATION'],
						(e) => {
							//关闭
							that.$refs.perpopup.close()
							if (e.granted.length > 0) {
								//执行你有权限后的方法
								that.getCurrentPos();
							}
						})

				} else {
					//执行你有权限后的方法 ios
					that.getCurrentPos();
				}
				// #endif 
			},

			getCurrentPos() {
				let that = this;
				uni.getLocation({
					type: 'gcj02',
					// 开启高精度定位
					// isHighAccuracy: true,
					// 是否解析地址信息
					geocode: true,
					success: function(res) {
						console.log('当前位置：', res);

						qqmapsdk.reverseGeocoder({
							location: {
								latitude: res.latitude,
								longitude: res.longitude
							},
							success: function(res) {
								const {
									address_component,
									formatted_addresses,
									location
								} = res.result;
								if (address_component) {
									that.$set(that.region, 0, address_component &&
										address_component.province ?
										address_component.province : '选择省');
									that.$set(that.region, 1, address_component &&
										address_component.city ?
										address_component.city : '选择市');
									that.$set(that.region, 2, address_component &&
										address_component.district ?
										address_component.district : '选择区');
								}
								if (formatted_addresses) {
									that.userAddress.detailInfo = formatted_addresses.recommend;
								}
								that.currentLocation.longitude = location.lng;
								that.currentLocation.latitude = location.lat;
								that.userAddress.longitude = location.lng;
								that.userAddress.latitude = location.lat;
								if (that.userAddress.detailInfo) {
									that.covers = [];
									that.covers.push({
										latitude: that.currentLocation.latitude,
										longitude: that.currentLocation.longitude,
										width: 30,
										height: 30,
										label: {
											content: that.userAddress.detailInfo,
											fontSize: 12,
											bgColor: '#1E90FF',
											color: '#ffffff',
											borderRadius: 12,
											padding: 2
										},
										iconPath: 'https://img.songlei.com/live/location-map.png'
									});
								}
							},
							fail: function(res) {
								console.log('fail==>', res)
							},
							complete: function(res) {
								console.log('complete==>', res)
							}
						})
					},
					fail(err) {
						console.log("定位错误", err)
					}
				});
			},
			handleClickMap() {
				let that = this;
				console.log(this.currentLocation, '==>this.currentLocation')
				uni.chooseLocation({
					latitude: this.currentLocation.latitude || '',
					longitude: this.currentLocation.longitude || '',
					success(location) {
						if (location) {
							that.userAddress.latitude = location.latitude;
							that.userAddress.longitude = location.longitude;

							that.userAddress.detailInfo = location.name;
							that.covers = [];
							that.covers.push({
								latitude: location.latitude,
								longitude: location.longitude,
								width: 30,
								height: 30,
								label: {
									content: location.name,
									fontSize: 12,
									bgColor: '#1E90FF',
									color: '#ffffff',
									borderRadius: 12,
									padding: 2
								},
								iconPath: 'https://img.songlei.com/live/location-map.png'
							});
							if (location.address)
								qqmapsdk.geocoder({
									address: location.address,
									success: function(res) {
										const {
											address_components
										} = res.result;
										that.$set(that.region, 0, address_components.province ?
											address_components.province : '选择省');
										that.$set(that.region, 1, address_components.city ?
											address_components.city : '选择市');
										that.$set(that.region, 2, address_components.district ?
											address_components.district : '选择区');
									}
								})
							that.canSearchPoi = false;
						}
					},
					fail: (err) => {
						console.log(err, '======================')
						// #ifdef MP
						// 获取用户当前授权设置
						uni.getSetting({
							success: function(res) {
								var statu = res.authSetting;
								console.log('失败==>', res);
								if (!statu['scope.userLocation']) {
									console.log(123);
									uni.showModal({
										title: '是否授权当前位置',
										content: '需要获取您的地理位置，请确认授权，否则地图功能将无法使用',
										success(tip) {
											if (tip.confirm) {
												// 打开用户授权设置
												uni.openSetting({
													success: function(data) {
														if (data.authSetting[
																"scope.userLocation"
															] === true) {
															uni.showToast({
																title: '授权成功',
																icon: 'success',
																duration: 1000
															})
															//授权成功之后，再调用chooseLocation选择地方
															setTimeout(
																function() {
																	uni.chooseLocation({
																		latitude: that
																			.currentLocation
																			.latitude ||
																			'',
																		longitude: that
																			.currentLocation
																			.longitude ||
																			'',
																		success: (
																			location
																		) => {
																			console
																				.log(
																					"===",
																					location
																				)
																			if (
																				location
																			) {
																				that.userAddress
																					.latitude =
																					location
																					.latitude;
																				that.userAddress
																					.longitude =
																					location
																					.longitude;

																				that.userAddress
																					.detailInfo =
																					location
																					.name;
																				that
																					.covers = [];
																				that.covers
																					.push({
																						latitude: location
																							.latitude,
																						longitude: location
																							.longitude,
																						width: 30,
																						height: 30,
																						label: {
																							content: location
																								.name,
																							fontSize: 12,
																							bgColor: '#1E90FF',
																							color: '#ffffff',
																							borderRadius: 12,
																							padding: 10
																						},
																						iconPath: 'https://img.songlei.com/live/location-map.png'
																					});
																				if (location
																					.address
																				)
																					qqmapsdk
																					.geocoder({
																						address: location
																							.address,
																						success: function(
																							res
																						) {
																							const {
																								address_components
																							} =
																							res
																								.result;
																							console
																								.log(
																									"==qqmapsdk.geocoder========",
																									res,
																									address_components
																								)
																							that.$set(
																								that
																								.region,
																								0,
																								address_components
																								.province ?
																								address_components
																								.province :
																								'选择省'
																							);
																							that.$set(
																								that
																								.region,
																								1,
																								address_components
																								.city ?
																								address_components
																								.city :
																								'选择市'
																							);
																							that.$set(
																								that
																								.region,
																								2,
																								address_components
																								.district ?
																								address_components
																								.district :
																								'选择区'
																							);
																						}
																					})
																				that.canSearchPoi =
																					false;
																			}
																		}
																	})
																}, 1000)
														}
													}
												})
											} else {
												wx.showToast({
													title: '授权失败',
													icon: 'none',
													duration: 1000
												})
											}
										}
									})

								}
							}
						})
						// #endif
					}
				});
				// const key = __config.mapKey; //使用在腾讯位置服务申请的key
				// const referer = '松雷松鼠购物'; //调用插件的app的名称
				// const location = JSON.stringify({
				// 	latitude: this.currentLocation.latitude || '',
				// 	longitude: this.currentLocation.longitude || '',
				// });
				// wx.navigateTo({
				// 	url: `plugin://chooseLocation/index?key=${key}&referer=${referer}&location=${location}`
				// });
			},
			regionChange(e) {
				this.region = e.detail.value;
				this.setMapParams();
			},

			handleBlur(e) {
				this.userAddress.detailInfo = e.target.value;
				if (this.canSearchPoi) {
					this.setMapParams();
				}
			},

			setMapParams() {
				let that = this;
				if (this.userAddress.detailInfo && this.region[0] && this.region[0] != '选择省' && this.region[1] && this
					.region[1] != '选择市' && this.region[2] && this.region[2] != '选择区') {
					// 调用接口
					qqmapsdk.search({
						keyword: this.region[0] + this.region[1] + this.region[2] + this.userAddress.detailInfo,
						success: function(res) {
							console.log("根据地址解析经纬度==", res);
							if (res && res.data.length > 0) {
								that.userAddress.latitude = res.data[0].location.lat;
								that.userAddress.longitude = res.data[0].location.lng;
								that.covers = [];
								that.covers.push({
									latitude: res.data[0].location.lat,
									longitude: res.data[0].location.lng,
									width: 30,
									height: 30,
									label: {
										content: that.userAddress.detailInfo,
										fontSize: 12,
										bgColor: '#1E90FF',
										color: '#ffffff',
										borderRadius: 12,
										padding: 2
									},
									// label:that.userAddress.detailInfo,
									iconPath: 'https://img.songlei.com/live/location-map.png'
								})
							}
						},
						fail: function(res) {
							console.log('==fail====>', res);
						},
						complete: function(res) {
							console.log('==complete====>', res);
						}
					});
				}
			},

			isDefaultChange(e) {
				if (e.detail.value) {
					this.userAddress.isDefault = '1';
				} else {
					this.userAddress.isDefault = '0';
				}
			},

			userAddressSave(e) {
				let value = e.detail.value;
				let region = this.region;
				console.log("this.userAddress===>", this.userAddress)
				if (!value.userName) {
					uni.showToast({
						title: '请填写收货人姓名',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (!value.telNum) {
					uni.showToast({
						title: '请填写联系电话',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (!validate.validateMobile(value.telNum)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (region[0] == '选择省') {
					uni.showToast({
						title: '请选择所在地区',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (!value.detailInfo) {
					uni.showToast({
						title: '请填写详细地址',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (!(this.userAddress.latitude > 0) || !(this.userAddress.longitude > 0)) {
					uni.showToast({
						title: '请定位地址坐标',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				api.userAddressSave({
					id: this.userAddress.id,
					userName: value.userName,
					telNum: value.telNum,
					provinceName: region[0],
					cityName: region[1],
					countyName: region[2],
					detailInfo: value.detailInfo,
					latitude: this.userAddress.latitude,
					longitude: this.userAddress.longitude,
					isDefault: this.userAddress.isDefault
				}).then(res => {
					if (this.nGo == 2) {
						//去新客领取可乐的页面
						api.getGiftList({
							sceneCode: app.globalData.sf,
							eventType: 'REGISTER'
						}).then(res => {
							//如果有礼品可领取
							if (res && res.data && res.data.length > 0) {
								uni.navigateTo({
									url: `/pages/new-custom/corSpring/corSpring?gift=${JSON.stringify(res.data[0])}`
								})
							} else {
								//如果没有礼品可领取
								uni.reLaunch({
									url: "/pages/home/<USER>"
								})
							}
						})
					} else {
						if(this.isNotify==1){
							 this.$EventBus.$emit("updateAddress");
						}
						uni.navigateBack({
							delta: 1
						});
					}

				});
			},

			userAddressDelete() {
				let that = this;
				uni.showModal({
					content: '确认将这个地址删除吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							api.userAddressDel(that.userAddress.id).then(res => {
								if(this.isNotify==1){
									this.$EventBus.$emit("updateAddress");
								}
								uni.navigateBack({
									delta: 1
								});
							});
						}
					}

				});
			},

			/*
			 * 导入微信地址
			 */
			getWxAddress: function() {
				let that = this;
				console.log("===导入微信地址==")
				uni.authorize({
					scope: 'scope.address',
					success: function(res) {
						console.log("===授权获取微信地址成功==")
						uni.chooseAddress({
							success: function(res) {
								console.log("导入微信地址--->", res)
								that.region[0] = res.provinceName;
								that.region[1] = res.cityName;
								that.region[2] = res.countyName;
								that.userAddress.userName = res.userName;
								that.userAddress.telNum = res.telNumber;
								that.userAddress.detailInfo = res.detailInfo;
								//微信实际没返回经纬度，这样做为了重置经纬度
								that.userAddress.latitude = res.latitude || '';
								that.userAddress.longitude = res.longitude || '';
								qqmapsdk.geocoder({
									//获取表单传入地址
									address: `${that.region[0]}${that.region[1]}${that.region[2]}${that.userAddress.detailInfo}`, //地址参数，例：固定地址，address: '北京市海淀区彩和坊路海淀西大街74号'
									success: function(res) { //成功后的回调
										console.log('根据从微信选择的收货地址', res);
										var res = res.result;
										that.userAddress.latitude = res.location
											.lat;
										that.userAddress.longitude = res.location
											.lng;
									},
								})

							},
							fail: function(res) {
								if (res.errMsg == 'chooseAddress:cancel') {
									uni.showToast({
										title: '取消选择',
										icon: 'none',
										duration: 3000
									});
								}
							},
							complete: function(res) {
								console.log("===导入微信地址已完成==")
							}
						});
					},
					fail: function(res) {
						console.log('-----fail')
						console.log(res)
					}
				});
			},
			//从通讯录导入联系人
			handleChooseContact() {
				let that = this;
				console.log("====handleChooseContact========");
				uni.chooseContact({
					success: function(res) {
						console.log(res, '成功回调')
						that.userAddress.userName = res.displayName;
						that.userAddress.telNum = res.phoneNumber;
					},
					fail(res) {
						// uni.showToast({
						// 	title: '获取联系人失败',
						// 	icon: 'error',
						// 	duration: 1000
						// })
						console.log(res, '错误回调')
					},
					complete(res) {
						console.log(res, '结束回调')
					}
				})
			}
		}
	};
</script>

<style>
	.bottom-btn {
		margin: auto;
		width: 96%;
		height: 88rpx;
		margin-bottom: 20rpx;
	}

	.compile {
		margin-top: 30rpx;
	}

	.permissions_box {
		padding: 200rpx 30rpx 50rpx;
		background-color: #fff;
		color: #000000;
	}
</style>