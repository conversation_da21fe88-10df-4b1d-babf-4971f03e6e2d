<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">收货地址</block>
		</cu-custom>
		<view class="cu-list menu-avatar" style="margin-top: 20rpx">
			<template v-for="(item, index) in userAddress">
				<view class="cu-item address-item" :key="index">
					<view class="cu-avatar round bg-orange" v-if="!select" @tap="selectUserAddress(item)" :data-index="index">
						<text class="avatar-text">{{ item.userName }}</text>
					</view>
					<view style="position: absolute; left: 30rpx" v-else @tap="selectUserAddress(item)" :data-index="index">
						<text v-if="selectId == item.id" style="font-size: 36rpx; font-weight: bold; color: #ff2c00" class="cuIcon-roundcheckfill"></text>
						<view v-else style="width: 36rpx; height: 34rpx; background: #ffffff; border: 1px solid #999999; border-radius: 50%"></view>
					</view>
					<view class="content loc-content" style="margin-top: 8rpx; margin-bottom: 20rpx" @tap="selectUserAddress(item)" :data-index="index">
						<view class="overflow-2 margin-top-xs">
							<view style="margin-left: 0" class="cu-tag sm radius bg-green margin-right-xs" v-if="item.isDefault == '1'">
								<text class="text-xs">默认</text>
							</view>
							<text class="text-gray text-sm" style="color: #777777; font-size: 24rpx; font-weight: 500">
								{{ item.provinceName }}{{ item.cityName }}{{ item.countyName }}
							</text>
						</view>

						<view class="overflow-2 margin-top-xs">
							<text class="text-gray text-sm" style="color: #242223; font-size: 27rpx; font-weight: 800">{{ item.detailInfo }}</text>
						</view>

						<view class="flex margin-top-xs">
							<view class="text-black" style="font-size: 23rpx; color: #777777">{{ item.userName }}</view>
							<view class="text-gray text-sm margin-left-sm" style="font-size: 23rpx; color: #777777">
								{{ item.telNum }}
							</view>
						</view>
					</view>
					<view class="text-gray text-sm text-center margin-right-sm" @tap="toEdit" :data-index="index">
						<text style="color: #242223; font-size: 26rpx" class="cuIcon-write"></text>
					</view>
				</view>
			</template>
		</view>
		<view class="add-address" v-if="userAddress.length < 10">
			<!-- :class="'bg-'+theme.themeColor" -->
			<button class="cu-btn block shadow-blur round add-btn" style="width: 500rpx; height: 70rpx; background: #ff2c00; color: #ffffff" @tap="toAdd">
				<text class="cuIcon-add"></text>
				新建收货地址
			</button>
		</view>
		<view :class="'cu-load' + (loadmore ? 'loading' : '')"></view>
		<view class="cu-load margin-top-xl" v-if="userAddress.length <= 0 && !loadmore">无收货地址，请添加</view>
	</view>
</template>

<script>
const app = getApp();
const util = require('utils/util.js');
import api from 'utils/api';

export default {
	data() {
		return {
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			page: {
				searchCount: false,
				current: 1,
				size: 10,
				ascs: '',
				//升序字段
				descs: ''
			},
			parameter: {},
			loadmore: true,
			userAddress: [],
			select: false,
			selectId: '',
			//有数统计使用
			page_title: '收货地址列表'
		};
	},

	components: {},
	props: {},

	onLoad(options) {
		if (options.select) {
			this.select = true;
			this.selectId = options.selectId;
		}
	},

	onShow() {
		app.initPage().then((res) => {
			this.userAddressPage();
		});
	},

	methods: {
		userAddressPage() {
			api.userAddressPage(this.page).then((res) => {
				this.userAddress = res.data.records;
				this.loadmore = false;
			});
		},

		toAdd() {
			uni.setStorage({
				key: 'param-userAddressForm',
				data: []
			});
			uni.navigateTo({
				url: '/pages/user/user-address/form/index'
			});
		},

		toEdit(e) {
			let index = e.currentTarget.dataset.index;
			let userAddressForm = this.userAddress[index];
			/* 把参数信息异步存储到缓存当中 */

			uni.setStorage({
				key: 'param-userAddressForm',
				data: userAddressForm
			});
			uni.navigateTo({
				url: '/pages/user/user-address/form/index'
			});
		},

		selectUserAddress(userAddressForm) {
			if (this.select) {
				this.selectId = userAddressForm.id;
				var pages = getCurrentPages(); // 获取页面栈
				var currPage = pages[pages.length - 1]; // 当前页面
				var prevPage = pages[pages.length - 2]; // 上一个页面
				if (prevPage) prevPage.$vm.setUserAddress(userAddressForm); //调用上一个页面的 setUserAddress 方法，并传入 userAddressForm
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
};
</script>
<style>
page {
	background-color: #fff;
}

.loc-content {
	width: calc(100% - 96rpx - 60rpx - 80rpx) !important;
	left: 126rpx !important;
	line-height: 34rpx !important;
}

.address-item {
	height: 160rpx !important;
}

.add-btn {
	margin: 20rpx auto;
	width: 96%;
	height: 80rpx;
}

.add-address {
	width: 100%;
	height: 120rpx;
	margin-top: 50rpx;
}
</style>