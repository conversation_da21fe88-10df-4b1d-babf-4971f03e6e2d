<template>
  <view style="background-color: #ccc">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">积分商城</block>
    </cu-custom>
    <!-- 用户信息 -->
    <view
      class="text-center padding points text-sm"
      style="
        padding-bottom: 20rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
      "
      :class="'bg-' + theme.backgroundColor"
    >
      <view class="flex align-center">
        <image
          style="
            width: 75rpx;
            height: 75rpx;
            vertical-align: middle;
            border-radius: 50rpx;
            margin-left: 10rpx;
          "
          :src="userInfo.headimgUrl"
        >
        </image>
        <view
          class="padding-top-xs"
          style="margin-left: 15rpx"
        >
          <view class="padding-bottom-xs" style="text-align: left;">昵称：{{ userInfo.nickName ? userInfo.nickName : '' }}</view>
          <view>手机号：{{ userInfo.phone ? userInfo.phone : '' }}</view>
        </view>
      </view>
      <view class="padding-top-sm">
        <text class="text-position"> 可用积分：</text>
        <text style="font-size: 44rpx;">{{ userInfo.pointsCurrent }}</text>
      </view>
    </view>

    <!-- banner -->
    <view class="banner-bg">
      <image
        mode="widthFix"
        style="width: 750rpx"
        src="https://img.songlei.com/user-point/integral-bg.png"
      >
      </image>

      <!-- <navigator class="store-item" hover-class="none" :url="payment.linkUrl"> -->
      <navigator
        class="store-item"
        hover-class="none"
      >
        <view style="padding: 0rpx 18rpx 0 20rpx">
          <image
            mode="widthFix"
            style="width: 710rpx;border-radius: 20rpx;"
            :src="payment.imgUrl | formatImg750"
          ></image>
          <!-- <view class="img-text">点击开始兑换 </view> -->
        </view>
      </navigator>
    </view>

    <!-- 商品列表 -->
    <view
      class="cu-list shop-list"
      style="background-color: #eee"
    >
      <view
        class="cu-item shop-item"
        v-for="(item, index) in pointsRecord"
        :key="index"
      >
        <image
          mode="aspectFit"
          class="shop-img"
          :src="item.picUrls[0]"
        ></image>

        <view class="content">
          <view class="text-sm content-text">{{ item.name }}</view>
          <view class="flex justify-around align-center">
            <view class="text-gray text-xs" style="text-decoration: line-through;" v-show="item.priceOriginal">零售价￥{{ item.priceOriginal }}</view>
            <view>
				<text class="text-xs text-gray">积分：</text>
				<text class="text-lg" style="color: #cda185;">
				  {{ item.goodsPoints ? item.goodsPoints : '' }}
				</text>
			</view>
          </view>

          <view class="content-box" style="margin-top: 20rpx;">
            <navigator
              class="content-ex"
              hover-class="none"
              :url="'/pages/goods/goods-detail/index?id=' + item.id + '&source_module=积分商城'"
            >
              兑换
            </navigator>
          </view>
        </view>
      </view>
    </view>
    <view
      v-if="loadmore"
      :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"
    ></view>
    <recommendComponents
      v-if="!loadmore"
      canLoad
    />
  </view>
</template>

<script>
import recommendComponents from "components/recommend-components/index";

const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'

export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      page: {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: '',
        //升序字段
        descs: 'create_time'
      },
      // countTotal: '',
      parameter: {},
      payment: '',
      loadmore: false,
      pointsRecord: [],
      userInfo: {
        pointsCurrent: ''
      },
	  //有数统计使用
	  page_title:'积分商城'
    };
  },

  components: {
    recommendComponents
  },
  props: {},

  onLoad (options) {
    this.advertisement("INTEGRAL_SHOP")
    // this.getUserCustAccntTot()
    app.initPage().then(res => {
      this.userInfoGet();
      this.pointsRecordPage();
    });
  },

  onShow (options) { },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.pointsRecordPage();
    }
  },

  methods: {
    //广告
    advertisement (id) {
      api.advertisementinfo({
        bigClass: id
      }).then(res => {
        console.log("advertisement===>", res.data);
        this.payment = res.data;
      });
    },

    //获取可用积分
    // getUserCustAccntTot () {
    //   api.getUserCustAccntTot().then((res) => {
    //     console.log("res===>", res.data);
    //     let UserCustAccntTot = res.data
    //     // 需要计算对象数组中某个属性合计
    //     this.countTotal = UserCustAccntTot.reduce((c, item) => c + item.usable * 1, 0)
    //     console.log("countTotal", this.countTotal);

    //   })
    // },

    //获取商城用户信息
    userInfoGet () {
      api.userInfoGet().then(res => {
        this.userInfo = res.data
      });
    },

    pointsRecordPage () {
      api.pointsShoppingMallPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
        let pointsRecord = res.data.records;
        this.pointsRecord = [...this.pointsRecord, ...pointsRecord];
        if (pointsRecord.length < this.page.size) {
          this.loadmore = false;
        }
      });
    }

  }
};
</script>

<style>
.store-item {
  position: absolute;
  top: 0rpx;
  z-index: 999;
}
.list-top {
  display: flex;
  justify-content: space-between;
  margin: 26rpx;
}
</style>
<style scoped lang="scss">
.banner-bg {
  position: relative;
  height: 220rpx;
  background-color: #fff;
  // background-image: url(https://img.songlei.com/user-point/integral-bg.png);
}
// .banner-img {
//   width: 710rpx;
//   // height: 210rpx;
//   margin: 10rpx 0 0;
// }
.shop-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0rpx 10rpx;
  .shop-item {
    background-color: #fff;
    border-radius: 30rpx;
    margin: 5rpx;
    .content-box {
      margin-bottom: 24rpx;
      .content-ex {
        width: 216rpx;
        height: 50rpx;
        background: linear-gradient(90deg, #fc0932 0%, #fc5560 100%);
        border-radius: 25rpx;
        text-align: center;
        font-size: 24rpx;
        color: #f7f7f7;
        line-height: 50rpx;
        margin: 0 auto;
      }
    }

    .content-text {
      width: 315rpx;
      height: 50rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 28rpx;
      font-weight: 500;
      margin: 0 18rpx 18rpx;
      border-bottom: 1rpx dashed #eee;
      color: #000000;
    }
    .content-Price {
      color: #666666;
      font-size: 20rpx;
      height: 48rpx;
      padding-left: 18rpx;
      margin-right: 50rpx;
      text-decoration: line-through;
    }
    .shop-img {
      width: 356rpx;
      height: 356rpx;
      border-radius: 10px;
    }
  }
}
.text-size {
  font-size: 46rpx;
  .text-position {
    font-size: 22rpx;
    margin-left: 10rpx;
    position: relative;
    top: -6rpx;
  }
}
</style>
