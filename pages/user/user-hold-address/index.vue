<template>
  <view class="flex flex-direction" style="height: 100vh;">
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
    	<block slot="backText">返回</block>
    	<block slot="content">自提点</block>
    </cu-custom>
    <view class="user-address flex align-center" style="border-bottom: 1rpx solid #eeeeee;">
      <view class="flex align-center flex-sub text-df" style="color: #000;" @click="select_address">
        <image 
          src="https://img.songlei.com/-1/material/81bcd911-1555-4766-aef2-e199ec0f9791.png"
          style="width: 45rpx;height: 36rpx;"
          class="padding-right-xs"
          mode="widthFix"
        ></image>
        <view>{{ user_address.address_name }}</view>
      </view>
      <view class="flex justify-center align-center text-sm padding-left-sm" style="color: #474747;">
        <image
          src="https://img.songlei.com/-1/material/b99d25e3-3b88-43e7-ba3e-86ccc7da7246.png"
          style="width: 35rpx;margin-right: 4rpx;height: 35rpx;"
          mode="widthFix"
        ></image>
        <view style="line-height: 1;" @click="refrend">重新定位</view>
      </view>
    </view>
    <div style="position: relative;z-index: 0;">
      <map
        style="width: 100%;height: 250px;" 
        :latitude="local.latitude" 
        :longitude="local.longitude"
        :scale="scale"
        :markers="markers"
        @markertap="markertap"
      >
      </map>
    </div>
    <view class="list flex-sub" :style="{ marginTop: `${marginTop}px`, overflow: pageY ? 'hidden' : 'auto' }">
      <view 
        @touchstart="touchstartEvent"
        @touchmove="touchmoveEvent" 
        @touchend="touchendEvent" 
        @touchcancel="touchendEvent"
        class="slider-mode">
        <view style="width: 65rpx;height: 7rpx;background-color: #dadada;margin: 0 auto;"></view>
      </view>
      <view class="list-item text-sm flex" @click="select(item)"  v-for="(item, index) in list" :key="item.id">
        <view class="flex-sub">
          <view class="list-label flex">
            <view v-if="index === 0" class="list-label-item" style="color: #fff;background-color: #d72f23;">距离最近</view>
            <view class="list-label-item">距离{{ item.distance }}千米</view>
          </view>
          <view class="text-df text-bold">{{ item.shopName }}</view>
          <view class="text-sm" style="color: #8f8f8f;">
            {{item.provinceName || ''}}{{item.cityName || ''}}{{item.countyName || ''}}{{item.detailInfo}}
          </view>
        </view>
        <view class="flex align-center justify-center" style="margin-left: 30rpx;">
          <view class="check-group" style="border: 1rpx solid #d72f23;border-radius: 50%;">
            <image v-if="item.hover" src="https://img.songlei.com/-1/material/1e4ebb1e-75c8-48e7-bfee-fb894056c66b.png" class="check-group"></image>
          </view>
        </view>
      </view>
      <view class="text-sm text-center padding-top-xs" style="color: #999;">暂无新自提点</view>
      <view style="width: 100%;height: 140rpx;"></view>
    </view>
    <view class="confirm-hold">
      <view class="confirm-bottom text-lg" @click="sumitBtn">确认自提点</view>
    </view>
  </view>
</template>

<script>
  const app = getApp();
  import { getTaskAddrListByShopId } from "api/use";
  import QQMapWX from 'public/jweixin-module/qqmap-wx-jssdk.min.js';
  import __config from 'config/env';
  var qqmapsdk;
  export default {
    data() {
      return {
        CustomBar: this.CustomBar,
        theme: app.globalData.theme, //全局颜色变量
        user_address: {
          latitude: 39,
          longitude: 116
        },
        markers: [],
        userInfo: {},
        marginTop: 0,
        pageY: 0,
        diffs: 0,
        scale: 16,
        list: [],
        shopId: '',
        addressId: '',
        local : {
          latitude: 39,
          longitude: 116
        }
      }
    },
    onLoad(options) {
      let that = this;
      this.getUserInfo()
      if(options.shopId) {
        this.shopId = options.shopId;
      }
      if(options.addressId) {
        this.addressId = options.addressId;
      }
      // 实例化API核心类
      qqmapsdk = new QQMapWX({
      	key: __config.mapKey
      });
      this.localLoading(() => {
        const { latitude, longitude } = this.user_address;
        this.markers.push({
          id: 0,
          latitude,
          longitude,
          iconPath: "https://img.songlei.com/-1/material/885bb696-70bf-44ca-9268-5404832fa1eb.png",
          width: 25,
          height: 33,
          callout: {
            "content": '收获地址',
            "borderRadius": 5,
            "display": "BYCLICK",
            "padding": 4,
            "bgColor": "#FFFFFF"
          }
        })
        this.getList(() => {
          if(this.addressId) {
            const index = this.list.findIndex(item => item.id === this.addressId);
            this.select(this.list[index === -1 ? 0 : index]);
          } else {
            this.select(this.list[0]);
          }
        });
      });
      
    },
    methods: {
      getList(callback) {
        const { latitude, longitude } = this.user_address;
        getTaskAddrListByShopId({
          longitude, latitude, shopId: this.shopId
        }).then(res => {
          this.list = res.data.map((item, index) => {
            let hover = false;
            let params = {
              id: +item.id,
              latitude: item.latitude,
              longitude: item.longitude,
              iconPath: "https://img.songlei.com/-1/material/c7497d60-d062-4f17-b00a-9716bc9e46da.png",
              width: 25,
              height: 33,
              callout: {
                "content": item.shopName,
                "borderRadius": 5,
                "display": "BYCLICK",
                "padding": 5,
                "bgColor": "#FFFFFF",
                "anchorY": 85,
                "fontSize": 10
              }
            }
            if(index === 0) {
              // params.width = 40;
              // params.height = 52;
              // hover = true;
              const distance = item.distance * 100 * 2;
              var objOptions = [
                { distance: 10, scale: 20 },
                { distance: 20, scale: 19 },
                { distance: 50, scale: 17 },
                { distance: 100, scale: 16 },
                { distance: 200, scale: 15 },
                { distance: 500, scale: 14 },
                { distance: 1000, scale: 13 },
                { distance: 2000, scale: 12 },
                { distance: 5000, scale: 11 },
                { distance: 10000, scale: 10 },
                { distance: 20000, scale: 9 },
                { distance: 50000, scale: 8 },
                { distance: 50000, scale: 7 },
                { distance: 100000, scale: 6 },
                { distance: 200000, scale: 5 },
                { distance: 500000, scale: 4 },
                { distance: 1000000, scale: 3 },
                { distance: 10000000, scale: 2 },
                { distance: 100000000, scale: 1 },
              ]
              this.scale = (objOptions.find(item => {
                if(distance < item.distance) {
                  return true;
                }
                return false
              }).scale - 1)
              console.log('scale', this.scale, distance)
            }
            this.markers.push(params)
            return { ...item, hover }
          });
          uni.hideLoading()
          callback && callback();
        })
      },
      getUserInfo() {
        let userInfo = uni.getStorageSync('user_info');
        this.userInfo = userInfo
      },
      touchstartEvent(e) {
        this.pageY = e.changedTouches[0].pageY;
      },
      touchmoveEvent(e) {
        const pageY = e.changedTouches[0].pageY;
        const diff = this.pageY - pageY;
        if (this.diffs) {
          if(diff <= 0 && diff > -250) {
            this.marginTop = -250 + (-1 * diff);
          } else if(diff > 0) {
            this.marginTop = -250;
          }
        } else {
          if(diff >= 0 && diff < 250) {
            this.marginTop = -1 * diff;
          } else if(diff < 0) {
            this.marginTop = 0;
          }
        }
      },
      touchendEvent(e) {
        this.pageY = 0;
        if(this.diffs) {
          if(this.marginTop > -250) {
            this.marginTop  = 0;
            this.diffs = 0;
          }
        } else {
          if(this.marginTop < 0) {
            this.marginTop  = -250;
            this.diffs = 1;
          }
        }
      },
      select(item) {
        if(item.hover) {
          return;
        }
        const { list } = this;
        this.list = list.map(items => {
          items.hover = false;
          if(item === items) {
            items.hover = true;
            this.local = {
              latitude: items.latitude,
              longitude: items.longitude
            }
            this.showMarker(item.id);
          }
          return items;
        })
      },
      markertap(e) {
        const { markerId } = e.detail;
        this.showMarker(markerId)
      }, 
      showMarker(markerId) {
        let { markers = [] } = this;
        if(+markerId) {
          this.markers = markers.map(item => {
            if(+item.id === +markerId) {
              item.width = 40;
              item.height = 52;
              item.callout.display = 'ALWAYS'
            } else {
              item.width = 25;
              item.height = 33;
              item.callout.display = 'BYCLICK'
            }
            return item;
          })
          this.list = this.list.map(item => {
            if(+item.id === +markerId) {
              item.hover = true;
              this.local = {
                latitude: item.latitude,
                longitude: item.longitude
              }
            } else {
              item.hover = false;
            }
            return item;
          })
        }
      },
      // 确认自提点
      sumitBtn() {
        const { list } = this;
        const obj = list.find(item => item.hover);
        var pages = getCurrentPages(); // 获取页面栈
        var prevPage = pages[pages.length - 2]; // 上一个页面
        if(prevPage)
          prevPage.$vm.updateShopAddress(obj);
        uni.navigateBack({
          delta: 1
        })
      },
      // 重置自己的定位
      refrend() {
        uni.showLoading({
          title: '定位中'
        })
        this.localLoading(() => {
          const { latitude, longitude } = this.user_address;
          this.markers[0] = {
            id: 0,
            latitude,
            longitude,
            iconPath: "https://img.songlei.com/-1/material/885bb696-70bf-44ca-9268-5404832fa1eb.png",
            width: 25,
            height: 33,
            callout: {
              "content": '收获地址',
              "borderRadius": 5,
              "display": "BYCLICK",
              "padding": 4,
              "bgColor": "#FFFFFF"
            }
          }
          this.list = [];
          this.getList();
        })
      },
      // 跳转地址到收货地
      select_address() { 
        uni.navigateTo({
          url: '/pages/user/user-address/list/index?select=1'
        })
      },
      // 收获地址
      setUserAddress(userAddress) {
        const { 
          provinceName, detailInfo, countyName, 
          cityName, latitude, longitude, id 
        } = userAddress;
        this.user_address = {
          latitude, longitude, id,
          address_name: `${provinceName}${cityName}${countyName}${detailInfo}`
        }
        this.markers[0] = {
          id: 0,
          latitude,
          longitude,
          iconPath: "https://img.songlei.com/-1/material/885bb696-70bf-44ca-9268-5404832fa1eb.png",
          width: 25,
          height: 33,
          callout: {}
        }
        this.list = [];
        this.getList(() => {
          this.select(this.list[0]);
        });
      },
      // 定位加载
      localLoading(callback) {
        const that = this;
        uni.getLocation({
          success(res) {
            qqmapsdk.reverseGeocoder({
            	location: {
            		latitude: res.latitude,
            	  longitude: res.longitude
            	},
              success(ress) {
                const { formatted_addresses } = ress.result;
                const { recommend } = formatted_addresses;
                that.user_address = {
                  latitude: res.latitude,
                  longitude: res.longitude,
                  address_name: recommend
                }
                callback && callback()
              },
              fail(errObj) {
                callback && callback()
              }
            });
          }
        })
      }
    }
  }
</script>

<style>
.user-address {
  height: 130upx;
  padding: 0 20upx;
  background-color: #fff;
}
.confirm-hold {
  position: fixed;
  width: 100%;
  bottom: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
  z-index: 2;
}
.confirm-bottom {
  width: 100%;
  height: 90rpx;
  background-color: #e02e24;
  color: #fff;
  border-radius: 10rpx;
  line-height: 90rpx;
  text-align: center;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
}
.list-item {
  margin-bottom: 20rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #ddd;
}
.list {
  background-color: #fff;
  overflow: auto;
  border-radius: 20rpx;
  padding:0 20rpx;
  transition: margin .25s linear;
  position: relative;
  z-index: 1;
}
.slider-mode {
  position: sticky;
  top: 0;
  background-color: #fff;
  padding: 20rpx 0;
  z-index: 1;
}
.list-label {
  margin-bottom: 20rpx;
  padding-left: 2rpx;
}
.list-label-item {
  color: #d72f23;
  border: 1rpx solid #d72f23;
  line-height: 1;
  padding: 6rpx 10rpx;
  border-radius: 6rpx;
  margin-right: 10upx;
}
.check-group {
  height: 35upx;
  width: 35upx;
}
</style>