<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">我的积分明细</block>
		</cu-custom>

		<view class="text-center padding points" style="padding-bottom: 20rpx" :class="'bg-' + theme.backgroundColor">
			<view class="text-xl">
				<image style="
            width: 25rpx;
            height: 28rpx;
            vertical-align: middle;
            margin-left: 10rpx;
          " src="https://img.songlei.com/user-point/icon_point_page.png">
				</image>

				<text style="font-size: 26rpx; margin-left: 10rpx">
					当前积分:
					<text class="text-bold" style="font-size: 40rpx; margin-left: 10rpx">
						{{ pointsCurrent }}
					</text>
				</text>
			</view>

			<view v-if="expireinfo" style="margin-top: 20rpx;font-size: 26rpx; background: #ECDBD1;border-radius: 25rpx;
              color: #F42021; height: 60rpx; line-height:60rpx; padding: 0 20rpx;  display: flex;align-items: center; justify-content: center; width: max-content; margin: auto;">
				<image src="https://img.songlei.com/live/warning.png"
					style="width: 34rpx;height: 34rpx; margin-right: 20rpx;"></image> {{expireinfo}}
			</view>
		</view>

		<navigator v-if="payment && payment.linkUrl" class="store-item" hover-class="none" :url="payment.linkUrl">
			<view style="
          background-color: #ffffff;
          padding: 20rpx 20rpx 0 20rpx;
          position: relative;
        ">
				<image mode="widthFix" style="width: 100%; height: 280rpx;border-radius: 20rpx;"
					:src="payment.imgUrl | formatImg750"></image>
				<!-- <view class="img-text">点击开始兑换 </view> -->
			</view>
		</navigator>

		<view class="cu-list menu" style="background-color: #ffffff">
			<view class="list-top" style="font-size: 28rpx">
				<text>积分明细</text>
				<!-- <text style="color: #898989; font-size: 22rpx">
          积分发放说明
          <text class="cuIcon-question" style="margin-left: 10rpx"></text>
        </text> -->
			</view>

			<view class="cu-item" v-for="(item, index) in pointsRecord" :key="index">
				<view style="
            width: 130rpx;
            font-weight: bold;
            color: #bbbbbb;
            font-size: 24rpx;
          ">
					{{ item.occur_date | filterDate }}
				</view>

				<image mode="aspectFit" style="width: 86rpx; height: 86rpx; margin: 0 20rpx"
					src="https://img.songlei.com/user-point/point-item.png">
				</image>

				<view class="content padding-tb-sm">
					<text class="text-sm padding-right-sm overflow-2" style="color: #777777; font-size: 26rpx">
						{{ item.memo }}
					</text>
					<!-- <view
            class="text-gray text-sm"
            style="color: #898989; font-size: 20rpx; height: 48rpx"
          >
            {{
              item.orderItemId
                ? '订单号：' + item.orderItemId
                : item.description && item.description.indexOf('签到') > -1
                ? '签到日期:' + `${item.createTime | filterDate}`
                : '  '
            }}
          </view> -->

					<view v-if="item.orderItemId" class="text-gray text-sm"
						style="color: #898989; font-size: 20rpx; height: 48rpx">
						订单号：{{ item.orderItemId }}
					</view>

					<view v-if="
              item.memo != null
            " class="text-gray text-sm" style="color: #898989; font-size: 20rpx; height: 48rpx">
						<text v-if="item.memo && item.memo.indexOf('签到') > -1"></text> 发生日期:{{ item.occur_date }}
					</view>
				</view>

				<view class="action">
					<text class="text-bold text-green margin-right-xs" style="color: #7ac2bf; font-size: 30rpx"
						v-if="item.amount < 0">
						{{ item.amount }}
					</text>

					<text class="text-bold text-red margin-right-xs" style="color: #f02323; font-size: 30rpx"
						v-if="item.amount > 0">
						+{{ item.amount }}
					</text>
				</view>
			</view>
		</view>

		<view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
		<!-- 暂时去掉猜你喜欢 -->
		<!-- <view
      v-if="loadmore"
      :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"
    ></view> -->
		<!-- <recommendComponents
      v-if="!loadmore"
      canLoad
    /> -->
	</view>
</template>

<script>
	import recommendComponents from "components/recommend-components/index";
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					// searchCount: false,
					page: 1,
					pageSize: 15,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				payment: {},
				// countTotal: '',//当前积分
				loadmore: true,
				pointsRecord: [],
				pointsCurrent: '', //当前积分
				expireinfo: '',
				// userInfo: {
				//   pointsCurrent: ''
				// },
				//有数统计使用
				page_title: '我的积分明细'
			};
		},

		components: {
			recommendComponents
		},
		props: {},
		filters: {
			filterDate: function(value) {
				if (!value) return "";
				return value.substr(5, 5);
				// return util.dateFilter(value)
			},
		},

		onLoad(options) {
			this.advertisement("USER_POINT")
			console.log("时间");
			// this.getUserCustAccntTot()
			app.initPage().then(res => {
				this.loadmore = true;
				this.pointsRecordPage();
			});
		},

		onShow(options) {},

		onReachBottom() {
			if (this.loadmore) {
				this.page.page = this.page.page + 1;
				this.pointsRecordPage();
			}
		},

		methods: {
			//广告
			advertisement(id) {
				api.advertisementinfo({
					bigClass: id
				}).then(res => {
					console.log("advertisement===>", res.data);
					this.payment = res.data;
				});
			},

			// //获取当前积分
			// getUserCustAccntTot () {
			//   api.getUserCustAccntTot().then((res) => {
			//     console.log("res===>", res.data);
			//     let UserCustAccntTot = res.data
			//     // 需要计算对象数组中某个属性合计
			//     this.countTotal = UserCustAccntTot.reduce((c, item) => c + item.balance * 1, 0)
			//     console.log("countTotal", this.countTotal);
			//   })
			// },


			pointsRecordPage() {
				api.erpPointsRecordPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					this.pointsCurrent = res.data.point;
					this.expireinfo = res.data.expireinfo; //积分过期信息展示
					let pointsRecord = res.data.cust_accnt_logs;
					console.log("pointsRecord", pointsRecord);
					// this.pointsRecord = [...this.pointsRecord, ...pointsRecord];
					this.pointsRecord = [...this.pointsRecord, ...pointsRecord];
					if (pointsRecord.length < this.page.pageSize) {
						this.loadmore = false;
					}
				});
			}

		}
	};
</script>

<style>
	.img-text {
		position: absolute;
		bottom: 30rpx;
		left: 210rpx;
		color: #152a6b;
		font-size: 26rpx;
		font-weight: 500;
	}

	.list-top {
		display: flex;
		justify-content: space-between;
		margin: 26rpx;
	}
</style>
