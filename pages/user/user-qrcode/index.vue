<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">我的二维码</block>
		</cu-custom>
		<view class="page-layout">
			<view style="
			  margin: 20rpx 36rpx 0 36rpx;
			  background-color: #ffffff;
			  border-radius: 20rpx;
			">
				<view style="display: flex; align-items: center; padding: 35rpx 42rpx">
					<view class="text-right cu-avatar radius bg-gray" :style="
			      'background-image:url(' +
			      userInfo.headimgUrl +
			      '); width:100rpx; height:100rpx; border-radius: 50%; padding: 36rpx 28rpx 36rpx 36rpx'
			    ">
					</view>
					<view style="margin-left: 20rpx">
						<view style="margin-bottom: 16rpx" class="text-xdf">{{ userInfo.nickName }}</view>
						<image style="width: 42rpx; height: 32rpx"
							src="https://img.songlei.com/usercard/lithodomous.png">
						</image>
					</view>
				</view>
				<view style="display: flex; flex-direction: column; align-items: center">
					<view style="position: relative">
						<image style="width: 619rpx; height: 384rpx" mode="aspectFit"
							:src="cardTypeImages[carInfo.cust_type]"></image>
						<view class="cardno text-lg" style="color: #ffffff;">
							<!-- <text>{{ cardNames[carInfo.cust_type] }}</text> -->
							<text>{{ userInfo.erpCustTypename ||''}}卡号</text>
							<text style="font-weight:600; padding-left: 20rpx;">NO:{{ userInfo.erpCardno||''}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="qr-layout">
				<view style="width: 488rpx; height: 90rpx; margin-top: 36rpx">
					<canvas @click="()=>previewQrImg(0)" style="height: 46px" class="bar_code" canvas-id="Brcode">
					</canvas>
				</view>
				<image @click="()=>previewQrImg(1)" style="width: 320rpx; height: 320rpx; margin-top: 32rpx" mode="aspectFit" :src="qrImg">
				</image>

				<view @click="handleFreshData" style="
						  color: #999999;
						  margin-top: 4rpx;
						  margin-bottom: 25rpx;
						  text-decoration: underline;
						" class="text-xdf">
					刷新二维码
				</view>
				<!-- #ifdef MP -->
				<view @click="gotoCard" style="font-weight: 800;color: #fff;width: 528rpx;height: 78rpx;line-height: 78rpx;background: #07c160;
						margin: 0px auto 32rpx;
						border-radius: 112rpx;
						text-align: center;" class="text-lg text-bold">去付款</view>
				<!-- #endif -->
			</view>

		</view>

		<view class="open-pay-layout bg-white flex justify-between align-center" v-if="payInfo.signStatus === '-1'">
			<view class="flex align-center">
				<image style="width: 100rpx; max-height: 80rpx;" mode="widthFix"
					src="https://img.songlei.com/live/icon-unionpay.png"></image>
				<text style="font-weight: bold;color: #C1946E; padding-left: 10rpx;"
					class="text-lg text-bold">松雷会员一码付</text>
			</view>
			<view @click="gotoIdentification" class="go-btn flex justify-center align-center text-white text-xdf">去开通
			</view>
		</view>

		<view class="open-pay-layout bg-white" style="color: #000000;" v-if="payInfo.signStatus === '1'">
			<view class="flex justify-between align-center padding-bottom margin-bottom"
				style="border-bottom: 1upx solid #f1f1f1;">
				<view class="flex align-center">
					<image style="width: 60rpx; max-height: 40rpx;" mode="widthFix"
						src="https://img.songlei.com/live/icon-unionpay.png"></image>
					<text style="padding-left: 10rpx;" class="text-df">松雷会员一码付</text>
				</view>
				<view class="flex justify-center align-center text-df" @click="cardManage">管理 <text class="cuIcon-right"
						style="color: #c5c5c5;"></text></view>
			</view>
			<view class="flex justify-between align-start text-xdf">
				<view>已绑定卡：</view>
				<view class="align-center text-df flex-sub">
					<view>{{ payInfo.bankName }}</view>
					<view>{{ payInfo.bankCardNo }}</view>
				</view>
				<!-- <view class="flex justify-center align-center text-df" >换卡绑定 <text class=""></text></view> -->
			</view>
		</view>

		<view 
			class="open-pay-layout bg-white flex justify-between align-center" 
			v-if="couponNum > 0"
			>
			<view class="flex align-center">
				<text style="font-weight: bold;color: #5E5E5E; padding-left: 10rpx;"
					class="text-lg text-bold">您的券包中共有{{couponNum}}张优惠券</text>
			</view>
			<view @click="reading(1)" class="go-btn flex justify-center align-center text-white text-white text-xdf">去查看
			</view>
		</view>

		<!-- 暂时没有开通 -->
		<!-- <view style="display: flex; flex-direction: column; align-items: center">
      <view style="color: #999999; margin-top: 20rpx; margin-bottom: 40rpx"
        >购物时请出示</view
      >
      <view
        @click="handleOpenPay"
        style="
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 28rpx;
          margin-bottom: 40rpx;
          width: 670rpx;
          height: 86rpx;
          background: linear-gradient(
            -90deg,
            #ccac92 0%,
            #c09979 0%,
            #e3cab6 100%
          );
          border-radius: 43px;
        "
      >
        微信支付</view
      >
    </view> -->

		<!-- 领劵中心-->
		<view 
			class="cu-bar bg-white border shop" 
			:class="form.deviceSystem=='2'?'':'tabbar foot'"
			style="background-color: #FFFFFF;"
			:style="{'z-index': '9999' }">
			<view class="flex justify-between align-center">
			</view>

			<view class="flex justify-between align-center bar-rt" style="padding-right: 60rpx;">
				更多优惠券请前往 <text style="color: #FF0000;margin-left: 20rpx;border-bottom: 2rpx solid #FF0000;"
					@click="reading(2)">领券中心</text> <text class="cuIcon-right"></text>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	const util = require("utils/util.js");
	const QR = require("utils/wxqrcode.js");
	const brCode = require("utils/barcode.js");
	import api from 'utils/api'
	import __config from 'config/env';

	import {
		querySign,
		cardManage
	} from "@/pages/qrcodepay/api/qrcodepay.js"
	import {
		getCouponnum
	} from 'api/newcostomer.js';

	export default {
		data() {
			return {
				loadingImg: 'https://img.songlei.com/-1/material/a3332b63-c7a4-4c3a-99c1-dd46cb6ac133.png',
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				carInfo: {},
				userInfo: {},
				payInfo: {},
				form: {},
				qrImg: null,
				cardTypeImages: {
					"108": "https://img.songlei.com/usercard/black-card1.png",
					"103": "https://img.songlei.com/usercard/gray-card1.png",
					"102": "https://img.songlei.com/usercard/gold-card1.png",
					"101": "https://img.songlei.com/usercard/pink-card1.png",
				},
				cardNames: {
					"108": "松雷黑砖卡号",
					"103": "松雷钻卡号",
					"102": "松雷金卡卡号",
					"101": "松雷积分卡号",
				},
				//有数统计使用
				page_title: '我的二维码',
				isOne: false,
				requestTask: null,
				isLonglink: false, //长连接是否已存在
				setTimeout: [],
				couponNum: 0,
				codeImgArray:[],
				// memberBarcode:'',//会员条码
			}
		},
		onLoad(options) {
			const that = this;
			uni.getSystemInfo({
				success: function(e) {
					const {
						deviceType,
						model,
						osName,
						deviceId
					} = e;
					// const deviceID = (osName === 'ios') ? 'IDFV' : 'IMEI';
					const deviceID = deviceId;
					const deviceName = model;
					const deviceSystem = (osName === 'ios') ? 2 : 1
					that.form = {
						deviceName,
						deviceSystem,
						deviceID,
						deviceType: 1
					}
				}
			})
			this.userInfoGet();
			this.cardInfoGet();
			this.getCouponnumPopData()
		},
		onShow() {
			if (this.isOne) {
				for (let i = 0; i < 3; i++) {
					setTimeout(() => {
						this.querySign();
					}, i * 1000)
				}
			}
			this.isOne = true;
		},
		onHide() {
			this.requestTask ? this.requestTask.abort() : ''
			this.isLonglink = false
			// 清除延时器
			this.setTimeout.forEach(time => {
				clearTimeout(time)
			})
			this.setTimeout = []
		},
		onUnload() {
			this.requestTask ? this.requestTask.abort() : ''
			this.isLonglink = false
			// 清除延时器
			this.setTimeout.forEach(time => {
				clearTimeout(time)
			})
			this.setTimeout = []
		},
		methods: {
			//长轮训，回调一码付支付结果，超时之后继续轮训，直到获取到结果
			longLink(_url, method) {
				console.log("过来了", this.payInfo.signStatus);
				let that = this;
				// 判断当前是否满足条件
				if (+this.payInfo.signStatus !== 1) {
					this.requestTask ? this.requestTask.abort() : ''
					return;
				}
				if (this.isLonglink) {
					return;
				}
				this.isLonglink = true;
				const userInfo = uni.getStorageSync('user_info')
				console.log("=====longLink=======")
				that.requestTask = uni.request({
					url: __config.basePath + _url,
					method: method,
					withCredentials: true,
					header: {
						//#ifdef H5
						'client-type': util.isWeiXinBrowser() ? 'H5-WX' : 'H5', //客户端类型普通H5或微信H5
						'tenant-id': app.globalData.tenantId || __config.tenantId,
						'app-id': app.globalData.appId ? app.globalData.appId : '', //微信h5有appId
						//#endif
						//#ifdef MP-WEIXIN
						'client-type': 'MA', //客户端类型小程序
						'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
						//#endif
						//#ifdef APP-PLUS
						'client-type': 'APP', //客户端类型APP
						'tenant-id': app.globalData.tenantId,
						//#endif
						'third-session': uni.getStorageSync('third_session') ? uni.getStorageSync(
							'third_session') : '',
						'user_id': userInfo ? userInfo.id : ''
					},
					success(res) {
						console.log("===res=====", res)
						if (res.statusCode == 200) {
							console.log("====res.data====", res.data)
							if (res.data && res.data.notifyType == 'UNIONPAY') {
								console.log("====res.data.notifyContent====", res.data.notifyContent)
								uni.setStorageSync("qrPayNotifyContent", res.data.notifyContent);
								console.log("===getStorageSync===", uni.getStorageSync("qrPayNotifyContent"))
								uni.navigateTo({
									url: '/pages/qrcodepay/success/index'
								})
								that.isLonglink = false;
							}
						} else if (res.statusCode == 304) {
							// const aa= {"bankCardNo":"439225******6884","cashNo":"110910","chlOrderId":"1686557414079332352","cid":"**********","createTime":"2023-08-02 09:59:56","hospitalId":"YHFH009300002","id":"1686557414081630209","orderId":"**************","payStatus":"1","queryId":"51230802359984563342","shopCode":"201","smallNo":"541","termId":"********","timeTamp":"**********","traceNo":"456334","traceTime":"**********","tradeNo":"YHF202308020959570000111","transactionId":"20111090889**************","txnAmt":"1","updateTime":"2023-08-02 09:59:56","userId":"1554373680561254401"}
							// uni.setStorageSync("qrPayNotifyContent",JSON.stringify(aa))
							// uni.navigateTo({
							// 	url: '/pages/qrcodepay/success/index'
							// })
							that.isLonglink = false;
							that.longLink("/mallapi/userinfo/listener", "get");
						}
					},
					fail(error) {},
				});
			},
			handleFreshData() {
				this.cardInfoGet();
			},
			cardInfoGet() {
				let that = this;
				this.qrImg = this.loadingImg;
				api.getCardList().then(res => {
					// uni.hideLoading();
					res.data = JSON.parse(res.data);
					if (res.data && res.data.vipCardMessageList && res.data.vipCardMessageList.length > 0) {
						this.carInfo = res.data.vipCardMessageList[0];
						if (!this.carInfo) {
							let data = JSON.stringify(res.data)
							uni.showModal({
								title: '提示',
								content: `${data}`,
								success: function(res) {
									if (res.confirm) {
										console.log('用户点击确定');
									} else if (res.cancel) {
										console.log('用户点击取消');
									}
								}
							});
							return;
						}
						this.codeImgArray = [];
						this.qrImg = QR.createQrCodeImg(this.carInfo.qrcode, {
							size: parseInt(1200) //二维码大小  
						});
						this.codeImgArray = [this.qrImg];
						brCode.code128(wx.createCanvasContext('Brcode'), this.carInfo.barcode, 244, 46);
						 // 将canvas转换为图片
						 setTimeout(()=>{
							 uni.canvasToTempFilePath({
							   canvasId: 'Brcode',
							   width: 244,
							   height: 46,
							   success: (res) => {
							 	// 图片路径
							 	const tempFilePath = res.tempFilePath;
							 	console.log('条码图片路径:', tempFilePath);
								//  this.memberBarcode = tempFilePath

							 	// 可以将图片路径保存起来或用于其他用途
							 	// this.codeImgArray.push(tempFilePath);
							 	
							   },
							   fail: (err) => {
							 	console.error('转换失败:', err);
							   }
							 }, this);
						 },3000)
						
					}
				});
				this.querySign()
			},

			querySign() {
				querySign().then(res => {
					this.payInfo = res.data;
					//开通一码付的，执行长轮询
					if (this.payInfo.signStatus == 1) {
						this.longLink("/mallapi/userinfo/listener", "get");
					}
				})
			},

			userInfoGet() {
				api.userInfoGet().then(res => {
					this.userInfo = res.data;
				});
			},

			handleOpenPay() {
				uni.openOfflinePayView();
			},

			gotoIdentification() {
				uni.navigateTo({
					url: '/pages/qrcodepay/identification/identification'
				})
			},
			
			// #ifdef MP-WEIXIN
			gotoCard() {
				wx.navigateToMiniProgram({
					appId: 'wxeb490c6f9b154ef9',
					path: 'pages/card_open/card_open',
					extraData: {
						create_card_appid: 'wx367d0a494ee763f3',
						card_id: 'pNsNa5KxRumYouxMfbP1FzWVI5YA',
						card_code: this.userInfo.erpCid,
						activate_type: 'ACTIVATE_TYPE_NORMAL'
					}
				});
			},
			// #endif

			cardManage() {
				cardManage(this.form).then(res => {
					const urlString = encodeURIComponent(res.data);
					uni.navigateTo({
						url: '/pages/public/webview/webview?url=' + urlString
					})
				})
			},

			reading(num) {
				if (num === 1) {
					uni.navigateTo({
						url: '/pages/coupon/coupon-user-list/index'
					})
				} else if (num === 2) {
					uni.navigateTo({
						url: '/pages/coupon/coupon-centre/index'
					})
				}
			},

			// 劵链路
			async getCouponnumPopData() {
				const res = await getCouponnum({
					type: 2
				});
				this.couponNum = res?.data || null;
				// this.couponNum = 5
			},
			
			previewQrImg(index){
				console.log("==previewQrImg====",JSON.stringify(this.codeImgArray));
				if(index == 0){
					// 在需要预览图片的地方调用customImage-preview页面
					uni.navigateTo({
						url: `/pages/customImage-preview/index`
					});
					return
				}

			  uni.previewImage({
			  	current: index,
				urls: this.codeImgArray
			  })
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-layout {
		position: relative;
	}

	.cardno {
		position: absolute;
		display: flex;
		justify-content: flex-end;
		color: #000000;
		width: 565rpx;
		top: 150rpx;
		right: 30rpx;
	}

	.qr-layout {
		position: relative;
		width: 710rpx;
		// top: 380rpx;
		background-color: #fff;
		display: flex;
		flex-direction: column;
		align-items: center;
		border-radius: 30rpx;
		margin: 0 20rpx;
		margin-top: -200rpx;
	}

	.open-pay-layout {
		border-radius: 20rpx;
		padding: 28rpx;
		margin: 20rpx;
	}

	.go-btn {
		width: 190rpx;
		height: 70rpx;
		background: linear-gradient(0deg, #D0B29A 0%, #C7A385 0%, #DEC5B1 100%);
		border-radius: 35rpx;
	}
</style>