<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">我的卡包</block>
		</cu-custom>
		<view  v-for="(item,index) in mumberlist" :key="index" style="margin: 10rpx;position: relative;">
		<view>
		<navigator :url="'/pages/shop/shop-detail/index?cur=6&id='+item.shopId">
			<view style="display: flex;align-items: center;justify-content: center;background: rgba(0, 0, 0, .6);border-radius: 20rpx;">
				<image type="image" :src="item.vipPic" alt="" style="height: 232rpx;width: 100%;z-index: -1;" />
			</view>
			<img :src="item.logo" alt="" mode="scaleToFill" style="width: 100rpx;height:100rpx;position: absolute;top: 64rpx;left: 30rpx;border-radius: 50rpx;">
			<view  style="position: absolute;top: 60rpx;color: white;left: 150rpx;font-size: 35rpx;z-index: 9;">
				<text>{{item.brandName}}&松鼠美淘联名卡会员</text>
			</view>
			<view style="position: absolute;top: 110rpx;color: white;left: 150rpx;font-size: 35rpx;z-index: 9;">
				<text style="color: #FFFFFF;font-size: 30rpx;">积分 0</text>
			</view>
			</navigator>
		</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: ''
				},
				parameter: {},
				loadmore: true,
				userAddress: [],
				select: false,
				selectId: '',
				//有数统计使用
				page_title: '收货地址列表',
				mumberlist: ""
			};
		},

		components: {},
		props: {},

		onLoad(options) {
			if (options.select) {
				this.select = true;
				this.selectId = options.selectId;
			}
		},

		onShow() {
			app.initPage().then(res => {
				this.userAddressPage();
			});
		},

		methods: {
			userAddressPage() {
				api.mumberilist(uni.getStorageSync('user_info').id).then(res => {
					this.mumberlist = res.data.records;
					this.loadmore = false;
				});
			},

		}
	};
</script>
<style>
	page {
		background-color: #fff;
	}
	.advertisement{
	    	height: 86rpx;
		    background: white;
		    border-radius: 20rpx;
		    padding-left: 20rpx;
		    padding-right: 20rpx;
		    margin-left: 20rpx;
		    margin-right: 20rpx;
			margin-top:20rpx;
			display: flex;
			align-items: center;
			margin-top: 10rpx;
			
	}
</style>
