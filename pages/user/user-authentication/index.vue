<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">实名认证</block>
		</cu-custom>
		<view v-if="userCertified">
			<form @submit="phoneSub">
				<view class="cu-form-group margin-top">
					<view class="title" style="font-size: 26rpx">手机号</view>
					<input style="font-size: 26rpx" placeholder="请输入手机号" name="phone" v-model="form.phone" />
				</view>
				<view class="cu-form-group" style="border-bottom: 1rpx solid #eee">
					<view class="title" style="font-size: 26rpx">验证码</view>
					<input style="font-size: 26rpx" placeholder="请输入验证码" name="code" maxlength="4"
						v-model="form.code" />
					<span @click="getPhoneCode" class="cu-btn bg-gray" :class="'display:' + msgKey">{{ msgText }}</span>
				</view>
				<view class="cu-form-group">
					<view class="title" style="font-size: 26rpx">本人姓名</view>
					<input style="font-size: 26rpx" placeholder="请输入本人姓名" name="name" v-model="form.name" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="font-size: 26rpx">身份证号</view>
					<input style="font-size: 26rpx" placeholder="请输入身份证号" name="idCardNo" v-model="form.idCardNo" />
				</view>
				<view class="padding flex flex-direction">
					<button class="cu-btn margin-tb-sm" style="height: 80rpx" :class="'bg-' + theme.themeColor"
						form-type="submit">
						立即认证
					</button>
				</view>
			</form>
		</view>
		<view v-else>
			<form @submit="updatadata">
				<view class="cu-form-group">
					<view class="title" style="font-size: 26rpx">国籍</view>
					<input style="font-size: 26rpx" name="country" v-model="form.country" disabled="true" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="font-size: 26rpx">证件类型</view>
					<input style="font-size: 26rpx"  name="cardType" v-model="form.cardType" disabled="true" />
				</view>
				<view class="cu-form-group">
					<view class="title" style="font-size: 26rpx">本人姓名</view>
					<input style="font-size: 26rpx" name="name" v-model="form.name"  disabled="true"/>
				</view>
				<view class="cu-form-group">
					<view class="title" style="font-size: 26rpx">身份证号</view>
					<input style="font-size: 26rpx"  name="idCardNo" v-model="form.idCardNo" disabled="true" />
				</view>
				<view class="padding flex flex-direction">
					<button class="cu-btn margin-tb-sm" style="height: 80rpx" :class="'bg-' + theme.themeColor"
						form-type="submit">
						更新认证
					</button>
				</view>
			</form>
		</view>
	</view>
</template>
<script>	
import { usercertified } from "@/api/user.js"
	import validate from 'utils/validate'
	import __config from '@/config/env'; // 配置文件
	const MSGINIT = "发送验证码",
		MSGSCUCCESS = "${time}秒后可重发",
		MSGTIME = 60;
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				msgText: MSGINIT,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				//有数统计使用
			msgText: MSGINIT,
			msgTime: MSGTIME,
			msgKey: false,
			theme: app.globalData.theme, //全局颜色变量
			CustomBar: this.CustomBar,
			showPrivacyPolicy:true,
			showPrivacyPolicy: __config.showPrivacyPolicy,
			checkedRead: false,
			privacyPolicyUrl: __config.privacyPolicyUrl,
			protocolUrl: __config.protocolUrl,
			form:{},
			userCertified:true,
			userInfo:''
			};
		},

		components: {},
		props: {},

		onShow() {
			app.initPage().then(res => {
	
			});
		},
		mounted() {
		let that  = this
		this.getUserinfo()
			 // console.log(that.userCertified,'userCertified')	
			 // }
		},

		onReachBottom() {
		
		},
		methods: {
			updatadata(){
				this.userCertified = true
			},
			phoneSub(e) {
				let that = this
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				
				if (!this.form.code) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (!this.form.name) {
					uni.showToast({
						title: '请输入本人姓名',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (!this.form.idCardNo) {
					uni.showToast({
						title: '请输入身份证号',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				usercertified(this.form).then(res=>{
					console.log(res,'res')
					if(res.ok){
						uni.showModal({
							title:'实名认证',
							content:'认证成功',
							showCancel:false,
							success: function (res) {
								if (res.confirm) {
									that.SetUserinfo()
									// that.userCertified =  false
								}
							}
						})
						getApp().globalData.AuthenticationType = '0'  // 认证成功
					}else{
						getApp().globalData.AuthenticationType = '1'  // 认证失败
						console.log(res,'res')
						uni.showModal({
							title: '提示',
							showCancel:false,
							content:res.msg
						})
					}
				})
				},
				getUserinfo(){
					let that =this
					api.userInfoGet().then((res) => {
						// console.log(res.data,'=====')
						that.userInfo = res.data;
						if(res.data.userCertified){
							that.userCertified =  false
							that.form = res.data.userCertified
						}else if(res.data){
							that.form.phone = that.userInfo.phone
							that.userCertified =  true
						}
					   
					});
				},
				
				SetUserinfo(){
					let that =this
					api.userInfoGet().then((res) => {
						// console.log(res.data,'=====')
						that.userInfo = res.data;
						if(res.data.userCertified){
							that.userCertified =  false
							that.form = res.data.userCertified
							getApp().globalData.AuthenticationType = '0'   //认证通过 
							uni.removeStorageSync('user_info');
							uni.setStorageSync('user_info',JSON.stringify(res.data))
							uni.navigateBack({
									delta:1,//返回层数，2则上上页
								})
						}else if(res.data){
							that.form.phone = that.userInfo.phone
							that.userCertified =  true
							getApp().globalData.AuthenticationType = '1'  // 认证失败
						}
					
					});
				},
			getPhoneCode() {
				if (this.msgKey) return
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.msgKey = true
				api.getPhoneCode({
					type: '1',
					phone: this.form.phone
				}).then(res => {
					this.msgKey = false
					if (res.code == '0') {
						uni.showToast({
							title: '验证码发送成功',
							icon: 'none',
							duration: 3000
						});
						this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						this.msgKey = true
						const time = setInterval(() => {
							this.msgTime--
							this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
							if (this.msgTime == 0) {
								this.msgTime = MSGTIME
								this.msgText = MSGINIT
								this.msgKey = false
								clearInterval(time)
							}
						}, 1000)
					} else {
			
					}
				}).catch(() => {
					this.msgKey = false
				});
			},
	}
	};
</script>
<style>
	.order-image {
		width: 120rpx;
		height: 120rpx;
	}

	.appraises-list {
		margin-left: 90rpx;
	}

	.goods-information {
		width: 400rpx;
	}
</style>