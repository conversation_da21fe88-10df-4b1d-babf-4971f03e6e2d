<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">修改用户信息</block>
		</cu-custom>
		<view style="background: #ffffff; border-radius: 20rpx; margin: 20rpx">
			<form @submit="userInfoSave">
				<!-- 最新用户授权 -->
				<!-- <form @submit="getnickname"> -->
				<view class="cu-form-group item" v-if="AuthorInformation">
					<view class="title item-title" style="font-size: 24rpx">头像</view>
					<view class="text-right">
						<button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
							<image :src="avatarUrl" style="width: 56px; height: 56px"></image>
						</button>
					</view>
				</view>

				<view class="cu-form-group item" v-if="AuthorInformation">
					<view class="title item-title" style="font-size: 24rpx">昵称</view>
					<view class="text-right" @click="showNickNameModal">{{ userInfo.nickName }}<text
							class="cuIcon-right" style="color: #acacac; margin-left: 20rpx"></text></view>
				</view>
				<!-- <view style="margin-left: 30rpx;display: flex;align-items: center;justify-content: center;">
					 <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
						<image :src="avatarUrl" style="width: 56px;height: 56px;"></image>
					</button>
							<input style="margin-left:20rpx" type="nickname" placeholder="请输入昵称" name="nickName" v-model="nickName"></input>
					</view>
					<view style="text-align: center;margin-left:200rpx">
						<button formType="submit" style="height: 30px;line-height: 30px;display: flex;align-items: center;justify-content: center">保存</button>
 					</view> -->
				<!-- </form> -->

				<view class="cu-form-group item" v-else>
					<view class="text-right cu-avatar radius bg-gray" :style="
					  'background-image:url(' +
					  userInfo.headimgUrl +
					  '); width:109rpx; height:109rpx; border-radius: 50%;'
					" @click="chooseImageBefore"></view>
					<view style="flex: 1; margin-left: 30rpx" @click="showNickNameModal">
						<view style="display: flex">
							<view class="title" style="font-size: 26rpx">昵称</view>
							<view class="title" style="font-size: 26rpx">{{
								userInfo.nickName
							  }}</view>
						</view>
						<view style="display: flex">
							<view class="title" style="font-size: 22rpx; color: #727171">编号:</view>
							<view class="title" style="font-size: 22rpx; color: #727171">{{ userInfo.userCode }}
							</view>
						</view>
					</view>
					<view @click="showNickNameModal" class="text-right" style="color: #acacac"><text
							class="cuIcon-right"></text></view>
				</view>

				<view class="cu-form-group item">
					<view class="title item-title" style="font-size: 24rpx">手机号</view>
					<view class="text-right" @click="showPhoneModal">{{ userInfo.phone | phoneEncryption
            }}<text class="cuIcon-right" style="color: #acacac; margin-left: 20rpx"></text></view>
				</view>

				<view class="cu-form-group item">
					<view class="title" style="font-size: 24rpx">性别</view>
					<radio-group class="text-right" @change="radioChange">
						<radio style="transform: scale(0.7); font-weight: bold" class="red margin-right-xs"
							:class="theme.themeColor" value="1" :checked="userInfo.sex === '1'"></radio>男
						<radio style="transform: scale(0.7); font-weight: bold"
							class="red margin-left-sm margin-right-xs" :class="theme.themeColor" value="2"
							:checked="userInfo.sex === '2'"></radio>女
					</radio-group>
				</view>
				<!--  #ifdef  MP-WEIXIN -->
				<view class="cu-form-group item">
					<view class="title" style="font-size: 24rpx">权限设置</view>
					<view class="text-right cuIcon-settingsfill" style="color: #acacac" @tap="settings"></view>
				</view>
				<!--  #endif -->
				<view class="cu-form-group item" @click="toReset">
					<view class="title" style="font-size: 24rpx;">修改密码</view>
					<view class="text-right" style="color: #ACACAC;"><text class="cuIcon-right"></text></view>
				</view>
				<view class="cu-form-group item" @click="toMyQrcode">
					<view class="title" style="font-size: 24rpx">我的二维码</view>
					<view class="text-right" style="color: #acacac"><text class="cuIcon-right"></text></view>
				</view>
				<view class="cu-form-group item" @click="authentication">
					<view class="title" style="font-size: 24rpx">实名认证</view>
					<view class="text-right" style="color: #acacac;font-size: 26rpx">
						{{userInfo.userCertified?'已认证':'未认证'}}<text class="cuIcon-right"></text>
					</view>
				</view>

				<!-- <view class="cu-form-group item" @click="Idnocar">
					<view class="title" style="font-size: 24rpx">身份证号</view>
					<view class="text-right" style="font-size:26rpx;color: #acacac">{{userInfo.idCardNo?'已填写':'请填写'}}<text class="cuIcon-right"></text></view>
				</view> -->
				<view class="item birthday">
					<view class="title" style="font-size: 24rpx">生日</view>
					<view class="text-right" style="color: #acacac;display: flex;align-items: center;">
						<picker mode="date" :value="date" :start="startDate" :end="endDate" @change="bindDateChange">
							<view style="font-size:26rpx" v-if="userInfo.birthday">
								{{userInfo.birthday?userInfo.birthday:""}}<text class="cuIcon-right"></text>
							</view>
							<view class="uni-input" v-else>请填写</view>
						</picker>

					</view>
				</view>
				<!-- 	<view class="compile">
					<button class="cu-btn shadow-blur block margin-sm round bottom-btn" :class="'bg-'+theme.themeColor"
						formType="submit">提交</button>
				</view> -->
				<view class="cu-form-group item" @click="address">
					<view class="title" style="font-size: 24rpx;min-width: 200rpx;">我的地址</view>
					<view class="text-right" style="color: #acacac">
						<text style="font-size:26rpx">{{userInfo.addr?userInfo.addr:''}}</text>
						<text class="cuIcon-right"></text>
					</view>
				</view>
				<!--  #ifdef APP-PLUS -->
				<view class="cu-form-group item" @click="changePushStatus">
					<view class="title" style="font-size: 24rpx;">{{pushStatus?'关闭推送':'打开推送'}}</view>
					<view class="text-right" style="color: #ACACAC;"><text class="cuIcon-right"></text></view>
				</view>
				<!--  #endif -->
				<view class="cu-form-group item" @click="toLogout">
					<view class="title" style="font-size: 24rpx;">注销账户</view>
					<view class="text-right" style="color: #ACACAC;"><text class="cuIcon-right"></text></view>
				</view>
			</form>
			<view class="cu-modal" :class="phoneModal ? ' show' : ''" style="z-index: 10 !important">
				<view class="cu-dialog bg-white">
					<view class="cu-bar justify-end">
						<view class="content">修改手机号</view>
						<view class="action" @click="hidePhoneModal">
							<text class="cuIcon-close text-red"></text>
						</view>
					</view>
					<view class="padding-xl">
						<form @submit="phoneSub">
							<view class="cu-form-group margin-top">
								<view class="title" style="font-size: 26rpx">新号码</view>
								<input style="font-size: 24rpx" placeholder="请输入手机号" name="phone"
									v-model="form.phone" />
							</view>
							<view class="cu-form-group" style="border-bottom: 1rpx solid #eee">
								<view class="title" style="font-size: 26rpx">验证码</view>
								<input style="font-size: 26rpx" placeholder="请输入验证码" name="code" maxlength="4"
									v-model="form.code" />
								<span @click="getPhoneCode" class="cu-btn bg-gray"
									:class="'display:' + msgKey">{{ msgText }}</span>
							</view>
							<view class="padding flex flex-direction">
								<button class="cu-btn margin-tb-sm" style="height: 80rpx"
									:class="'bg-' + theme.themeColor" form-type="submit">
									提交
								</button>
							</view>
						</form>
					</view>
				</view>
			</view>

			<view class="cu-modal" :class="nickNameModal ? ' show' : ''" style="z-index: 10 !important">
				<view class="cu-dialog bg-white">
					<view class="cu-bar justify-end">
						<view class="content">修改昵称</view>
						<view class="action" @click="hideNickNameModal">
							<text class="cuIcon-close text-red"></text>
						</view>
					</view>
					<view class="padding-xl">
						<form @submit="nickNameUpdate">
							<view class="cu-form-group" style="border-bottom: 1rpx solid #eee">
								<view class="title" style="font-size: 26rpx">昵称</view>
								<input type="nickname" name="nickName" v-model="nickName" placeholder="请输入昵称" />
							</view>
							<view class="padding flex flex-direction">
								<button class="cu-btn margin-tb-sm" style="height: 80rpx"
									:class="'bg-' + theme.themeColor" form-type="submit">
									提交
								</button>
							</view>
						</form>
					</view>
				</view>
			</view>

			<!-- <view class="" v-if="birthdMode"> -->
			<view>


			</view>
			<!-- </view> -->

			<view class="cu-modal" :class="idnoModal ? ' show' : ''" style="z-index: 10 !important">
				<view class="cu-dialog bg-white">
					<view class="cu-bar justify-end">
						<view class="content">身份证号码</view>
						<view class="action" @click="idnoModal=false">
							<text class="cuIcon-close text-red"></text>
						</view>
					</view>
					<view class="padding-xl">
						<form @submit="idCarUpdate">
							<view class="cu-form-group" style="border-bottom: 1rpx solid #eee">
								<!-- <view class="title" style="font-size: 26rpx">昵称</view> -->
								<input type="idCardNo" name="idCardNo" v-model="idCardNo" placeholder="请输入身份证号码" />
							</view>
							<view class="padding flex flex-direction">
								<button class="cu-btn margin-tb-sm" style="height: 80rpx"
									:class="'bg-' + theme.themeColor" form-type="submit">
									提交
								</button>
							</view>
						</form>
					</view>
				</view>
			</view>
		</view>
		<view style="background: #ffffff; border-radius: 20rpx; margin: 20rpx">
			<view class="cu-form-group item" @click="toNavWebview(1)">
				<view class="title" style="font-size: 24rpx;">用户协议</view>
				<view class="text-right" style="color: #ACACAC;"><text class="cuIcon-right"></text></view>
			</view>
			<view class="cu-form-group item" @click="toNavWebview(2)">
				<view class="title" style="font-size: 24rpx;">隐私政策</view>
				<view class="text-right" style="color: #ACACAC;"><text class="cuIcon-right"></text></view>
			</view>
		</view>
		<view class="text-red bg-white exit" @click="logout">
			<text class="exit-text">退出登录</text>
		</view>
		<view class="text-gray text-sm text-center margin-sm" style="padding-top: 10rpx;">哈尔滨松雷股份有限公司旗下产品
		</view>
		<view class="text-gray text-sm text-center" style="padding-bottom: 60rpx;">（版本号：{{ packageVersion }}）
		</view>
		<uni-popup ref="perpopup" type="center" :mask-click='false'>
			<view class="permissions_box">
				当您使用APP时，修改头像需要授权相机权限和存储权限，不授权上述权限，不影响APP其他功能使用。
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp();
	const util = require("utils/util.js");
	import api from "utils/api";
	import validate from "utils/validate";
	import __config from "config/env";

	// #ifdef APP-PLUS
	import {
		deletePushAlias,
		startPush,
		setPushAlias,
	} from '@/api/push';
	// #endif
	const version = require("utils/version.js");
	const MSGINIT = "发送验证码",
		MSGSCUCCESS = "${time}秒后可重发",
		MSGTIME = 60;
	export default {
		data() {
			const currentDate = this.getDate({
				format: true
			})
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				userInfo: {},
				phoneModal: false,
				nickNameModal: false,
				form: {},
				nickNameForm: {},
				msgText: MSGINIT,
				msgTime: MSGTIME,
				msgKey: false,
				//有数统计使用
				page_title: "修改用户信息",
				avatarUrl: "https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0",
				AuthorInformation: false,
				unserName: "",
				showwname: true,
				nickName: "",
				idnoModal: false,
				idCardNo: '',
				// birthdMode:true,
				// visible:true,
				title: 'picker',
				index: 0,
				date: currentDate,
				time: '12:01',
				packageVersion: '',
				pushStatus: uni.getStorageSync('push_status'), // true 开启状态  false 关闭状态
			};
		},

		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},

		onLoad(options) {
			let that = this;
			// #ifdef APP-PLUS
			plus.runtime.getProperty(plus.runtime.appid, (wgtInfo) => {
				that.packageVersion = wgtInfo.version // appVersion就是版本号
			})
			// #endif
			// #ifndef APP-PLUS
			uni.getSystemInfo({
				success: function(res) {
					that.packageVersion = res.appVersion;
				}
			})
			// #endif
		},
		onShow() {
			this.userInfoGet();
			if (version.getversion() == "1") {
				this.AuthorInformation = true;
				console.log(this.userInfo, "userInfouserInfo");
			} else if (version.getversion() == "2") {
				this.AuthorInformation = false;
			} else if (version.getversion() == "3") {
				this.AuthorInformation = false;
			}
		},
		methods: {
			authentication() {
				uni.navigateTo({
					url: '/pages/user/user-authentication/index'
				})
			},
			logout() {
				uni.showModal({
					content: '确定退出登录吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							api.logout().then(res => {
								let userInfo = res.data;
								uni.setStorageSync('user_info', userInfo);
								if (userInfo) {
									uni.setStorageSync('third_session', userInfo.thirdSession);
								}
								// #ifdef APP-PLUS
								deletePushAlias();
								// #endif
								//退出登录完成跳到首页
								uni.reLaunch({
									url: '/pages/home/<USER>'
								});
								//清空购物车数量
								app.globalData.shoppingCartCount = '0';
								uni.$emit("updateCart");
							});
						}
					}
				});
			},
			changePushStatus() {
				if (this.pushStatus) {
					this.pushStatus = false;
					uni.setStorageSync('push_status', false);
					// #ifdef APP-PLUS
					deletePushAlias();
					// #endif
					uni.showToast({
						title: '推送关闭成功',
						icon: 'none'
					})
				} else {
					this.pushStatus = true;
					uni.setStorageSync('push_status', true);
					startPush();
					setPushAlias(this.userInfo.id);
					uni.showToast({
						title: '推送开启成功',
						icon: 'none'
					})
				}
			},
			// 注销账户
			toLogout() {
				// #ifdef APP-PLUS
				deletePushAlias();
				// #endif
				uni.navigateTo({
					url: '/pages/login/logout'
				})
			},
			address() {
				uni.navigateTo({
					url: '/pages/user/user-address/list/index'
				})
			},
			bindPickerChange: function(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.index = e.detail.value
			},
			bindDateChange: function(e) {

				if (this.userInfo.birthday) {
					uni.showModal({
						title: '生日不可修改'
					})
				} else {
					this.date = e.detail.value
					console.log(e.detail.value)
					this.userInfo.birthday = e.detail.value
					this.userInfoSave()
					// this.userInfoGet();
				}

			},
			bindTimeChange: function(e) {
				this.time = e.detail.value

			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			Idnocar() {
				// console.log()
				this.idnoModal = true
				console.log(this.userInfo.idCardNo)
				this.idCardNo = this.userInfo.idCardNo
			},
			// #ifdef MP-WEIXIN
			/**
			 * 小程序设置
			 */
			settings: function() {
				uni.openSetting({
					success: function(res) {
						console.log(res.authSetting);
					},
				});
			},
			// #endif
			//获取商城用户信息
			userInfoGet() {
				api.userInfoGet().then((res) => {
					this.userInfo = res.data;
					this.nickName = res.data.nickName;
					this.avatarUrl = res.data.headimgUrl;
				});
			},
			radioChange(e) {
				this.userInfo.sex = e.detail.value;
				this.userInfoSave();
				// this.userInfoGet();
			},

			uploadAvatar(filePath) {
				// 上传头像
				let _url = "/mallapi/file/upload";
				//#ifndef H5
				_url = __config.basePath + _url;
				//#endif
				let that = this;
				uni.showLoading({
					title: "上传中",
				});
				uni.uploadFile({
					header: {
						//#ifdef H5
						"client-type": util.isWeiXinBrowser() ? "H5-WX" : "H5", //客户端类型普通H5或微信H5
						"tenant-id": getApp().globalData.tenantId,
						"app-id": getApp().globalData.appId ? getApp().globalData.appId : "", //微信h5有appId
						//#endif
						//#ifdef MP-WEIXIN
						"client-type": "MA", //客户端类型小程序
						"app-id": uni.getAccountInfoSync().miniProgram.appId, //小程序appId
						//#endif
						//#ifdef APP-PLUS
						"client-type": "APP", //客户端类型APP
						"tenant-id": getApp().globalData.tenantId,
						//#endif
						"third-session": uni.getStorageSync("third_session") ?
							uni.getStorageSync("third_session") : "",
					},
					url: _url,
					filePath: filePath,
					name: "file",
					formData: {
						fileType: "image",
						dir: "headimg/",
					},
					success: (uploadFileRes) => {
						if (uploadFileRes.statusCode == "200") {
							// console.log(JSON.parse(uploadFileRes.data).link,'JSON.parse(uploadFileRes.data).link')
							that.avatarUrl = JSON.parse(uploadFileRes.data).link;
							api
								.userInfoUpdate({
									id: this.userInfo.id,
									nickName: this.userInfo.nickName,
									sex: this.userInfo.sex,
									headimgUrl: JSON.parse(uploadFileRes.data).link,
								})
								.then((res) => {
									this.userInfoGet();
								});
						} else {
							uni.showModal({
								title: "提示",
								content: "上传失败：" + uploadFileRes.data,
								success(res) {},
							});
						}
					},
					fail: (err) => {
						console.log(err);
					},
					complete: () => {
						uni.hideLoading();
					},
				});
			},

			chooseImageBefore() {
				let that = this;
				// #ifdef APP-PLUS
				// 允许从相机和相册更换头像
				var platform = uni.getSystemInfoSync().platform;
				if (platform == 'android') {
					plus.android.checkPermission(
						'android.permission.CAMERA',
						granted => {
							if (granted.checkResult == -1) {
								//弹出
								that.$refs.perpopup.open('top');
								plus.android.requestPermissions(['android.permission.CAMERA','android.permission.WRITE_EXTERNAL_STORAGE'],
									(e) => {
										//关闭
										if (e.granted.length > 0) {
											//执行你有权限后的方法
											that.chooseImage();
											that.$refs.perpopup.close()
										}
										if (e.deniedAlways.length > 0) { //权限被永久拒绝
											uni.showModal({
												title: '提示',
												content: '保存图片权限被拒绝，是否前往开启权限',
												success: (res) => {
													if (res.confirm) {
														// 弹出提示框解释为何需要读写手机储存权限，引导用户打开设置页面开启
														var main = plus.android
															.runtimeMainActivity();
														var Intent = plus.android.importClass(
															"android.content.Intent");
														//直接进入应用列表的权限设置
														var mIntent = new Intent(
															'android.settings.APPLICATION_SETTINGS'
														);
														main.startActivity(mIntent);
														that.$refs.perpopup.close()
													} else if (res.cancel) {
														console.log('用户点击取消');
														that.$refs.perpopup.close()
													}
												}
											});
										}
										if (e.deniedPresent.length > 0) { //权限被临时拒绝
											// 弹出提示框解释为何需要读写手机储存权限，可再次调用plus.android.requestPermissions申请权限
											plus.android.requestPermissions([
												'android.permission.CAMERA',
												'android.permission.WRITE_EXTERNAL_STORAGE'
											])
											that.$refs.perpopup.close()
										}
									},
									function(e) {
										console.log('Request Permissions error:' + JSON.stringify(e));
									});
							} else {
								//执行你有权限后的方法
								that.chooseImage();
							}
						},
						error => {
							console.error('Error checking permission:', error.message);
						}
					);
				} else {
					//执行你有权限后的方法 ios
					that.chooseImage();
				}
				// #endif  

				// #ifndef APP-PLUS
				that.chooseImage();
				// #endif  
			},

			//选择头像上传
			chooseImage() {
				console.error("=====chooseImage======");
				uni.chooseImage({
					count: 1,
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						let _url = "/mallapi/file/upload";
						//#ifndef H5
						_url = __config.basePath + _url;
						//#endif
						let that = this;
						uni.showLoading({
							title: "上传中",
						});
						uni.uploadFile({
							header: {
								//#ifdef H5
								"client-type": util.isWeiXinBrowser() ? "H5-WX" :
								"H5", //客户端类型普通H5或微信H5
								"tenant-id": getApp().globalData.tenantId,
								"app-id": getApp().globalData.appId ?
									getApp().globalData.appId : "", //微信h5有appId
								//#endif
								//#ifdef MP-WEIXIN
								"client-type": "MA", //客户端类型小程序
								"app-id": uni.getAccountInfoSync().miniProgram.appId, //小程序appId
								//#endif
								//#ifdef APP-PLUS
								"client-type": "APP", //客户端类型APP
								"tenant-id": getApp().globalData.tenantId,
								//#endif
								"third-session": uni.getStorageSync("third_session") ?
									uni.getStorageSync("third_session") : "",
							},
							url: _url,
							filePath: tempFilePaths[0],
							name: "file",
							formData: {
								fileType: "image",
								dir: "headimg/",
							},
							success: (uploadFileRes) => {
								if (uploadFileRes.statusCode == "200") {
									that.userInfo.headimgUrl = JSON.parse(uploadFileRes.data).link;
									that.userInfoSave();
								} else {
									uni.showModal({
										title: "提示",
										content: "上传失败：" + uploadFileRes.data,
										success(res) {},
									});
								}
							},
							fail: (err) => {
								console.log(err);
							},
							complete: () => {
								uni.hideLoading();
							},
						});
					},
				});
			},
			userInfoSave(e) {
				// let value = e.detail.value;
				if (!this.userInfo.headimgUrl) {
					uni.showToast({
						title: "请上传头像",
						icon: "none",
						duration: 3000,
					});
					return;
				}
				if (!this.userInfo.nickName) {
					uni.showToast({
						title: "请填写昵称",
						icon: "none",
						duration: 3000,
					});
					return;
				}
				if (!this.userInfo.sex) {
					uni.showToast({
						title: "请选择性别",
						icon: "none",
						duration: 3000,
					});
					return;
				}
				api
					.userInfoUpdate({
						id: this.userInfo.id,
						nickName: this.userInfo.nickName,
						sex: this.userInfo.sex,
						headimgUrl: this.userInfo.headimgUrl,
						idCardNo: this.userInfo.idCardNo,
						birthday: this.userInfo.birthday
					})
					.then((res) => {
						this.userInfoGet();
						// uni.navigateBack({
						// 	delta: 1
						// });
					});
			},
			showPhoneModal() {
				this.phoneModal = true;
			},
			hidePhoneModal() {
				this.phoneModal = false;
			},
			showNickNameModal() {
				this.nickNameModal = true;
				this.nickNameForm.nickName = this.userInfo.nickName || this.nickName;
			},
			hideNickNameModal() {
				this.nickNameModal = false;
			},
			idCarUpdate(e) {
				if (this.idCardNo == '') {
					uni.showToast({
						title: "请输入身份卡号",
						icon: "none",
						duration: 3000,
					});
					return;
				}
				// console.log(reg.test(e.detail.value.idCardNo) === false,'reg.test(e.detail.value.idCardNo')
				// console.log(e.detail.value.idCardNo.length=='15',e.detail.value.idCardNo.length=='18')
				if (e.detail.value.idCardNo.length == '15' || e.detail.value.idCardNo.length == '18') {
					this.userInfo.idCardNo = e.detail.value.idCardNo
					this.userInfoSave()
					this.idnoModal = false
				} else {
					uni.showToast({
						title: "身份证不合法",
						icon: "error"
					})
				}

			},
			nickNameUpdate(e) {
				if (this.nickName == '') {
					uni.showToast({
						title: "请输入昵称",
						icon: "none",
						duration: 3000,
					});
					return;
				}
				if (e) {
					this.userInfo.nickName = e.detail.value.nickName;
				} else {
					this.userInfo.nickName = this.nickNameForm.nickName;
				}
				//   if (this.nickNameForm.nickName) {
				//     uni.showToast({
				//       title: "请输入昵称",
				//       icon: "none",
				//       duration: 3000,
				//     });
				//     return;
				//   }

				this.userInfoSave();
				this.hideNickNameModal();
			},
			getPhoneCode() {
				if (this.msgKey) return;
				if (this.form.phone == this.userInfo.phone) {
					uni.showToast({
						title: "输入号码与当前绑定号码相同",
						icon: "none",
						duration: 3000,
					});
					return;
				}
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: "请输入正确的手机号码",
						icon: "none",
						duration: 3000,
					});
					return;
				}
				this.msgKey = true;
				api
					.getPhoneCode({
						type: "2",
						phone: this.form.phone,
					})
					.then((res) => {
						this.msgKey = false;
						if (res.code == "0") {
							uni.showToast({
								title: "验证码发送成功",
								icon: "none",
								duration: 3000,
							});
							this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
							this.msgKey = true;
							const time = setInterval(() => {
								this.msgTime--;
								this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
								if (this.msgTime == 0) {
									this.msgTime = MSGTIME;
									this.msgText = MSGINIT;
									this.msgKey = false;
									clearInterval(time);
								}
							}, 1000);
						} else {}
					})
					.catch(() => {
						this.msgKey = false;
					});
			},
			phoneSub(e) {
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: "请输入正确的手机号码",
						icon: "none",
						duration: 3000,
					});
					return;
				}
				if (!this.form.code) {
					uni.showToast({
						title: "请输入验证码",
						icon: "none",
						duration: 3000,
					});
					return;
				}
				api.updateUserPhone(this.form).then((res) => {
					this.userInfo.phone = this.form.phone;
					this.phoneModal = false;
					this.form = {};
				});
			},
			toReset() {
				uni.navigateTo({
					url: "/pages/login/register?type=editpwd",
				});
			},

			toMyQrcode() {
				uni.navigateTo({
					url: "/pages/user/user-qrcode/index",
				});
			},
			toNavWebview(type) {
				let obj = {
					title: '用户协议',
					url: __config.protocolUrl
				}
				if (+type === 2) {
					obj.title = "隐私协议",
						obj.url = __config.privacyPolicyUrl
				}
				uni.navigateTo({
					url: `/pages/public/webview/webview?title=${ obj.title }&url=${ obj.url }`
				})
			},
			onChooseAvatar(e) {
				console.error("====onChooseAvatar======")
				const {
					avatarUrl
				} = e.detail;
				this.uploadAvatar(avatarUrl);
			},
			getnickname(e) {
				this.userInfo.nickName = e.detail.value.nickName;
				api
					.userInfoUpdate({
						id: this.userInfo.id,
						nickName: e.detail.value.nickName,
						sex: this.userInfo.sex,
						headimgUrl: this.userInfo.headimgUrl,
					})
					.then((res) => {});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.item {
		padding: 26rpx 0;
		min-height: 114rpx;
		margin: 0 26rpx;
	}

	.avatar-wrapper {
		padding: 0;
		width: 56px !important;
		height: 56px !important;
		border-radius: 8px;
	}

	.cu-form-group picker {
		padding-right: 12rpx !important;
	}

	.exit {
		width: 94%;
		border-radius: 10rpx;
		height: 100rpx;
		margin: 20rpx auto;
	}

	.exit-text {
		margin-left: 300rpx;
		line-height: 100rpx;
	}

	.birthday {
		border-top: 1rpx solid #eee;
		border-bottom: 1rpx solid #eee;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}


	.permissions_box {
		padding: 200rpx 30rpx 50rpx;
		background-color: #fff;
		color: #000000;
	}
</style>