<template>
  <view class="container padding">
    <cu-custom :isBack="true" :iconColor="'#fff'" :boderColor="'#fff'" :hideMarchContent="true">
			<!-- <block slot="backText">返回</block> -->
			<block slot="content" style="color:#fff;">拼图游戏</block>
		</cu-custom>
    <view v-if="puzzleGameData">
      <!-- 用户信息 -->
      <view v-if="puzzleGameData.puzzleUser" class="user-info text-xsm padding-sm">
        <view class="flex align-center">
          <image class="avatar" :src="puzzleGameData.puzzleUser.headImg" mode="widthFix"></image>
          <view class="margin-left-sm">{{ puzzleGameData.puzzleUser.nickName || '暂无' }}</view>
        </view>
        <view class="info text-black flex align-center justify-between margin-top-sm" >
          <view class="text-center">
            <view class="text-xdf">{{ puzzleGameData.puzzleUser.puzzleNum || 0 }}</view>
            <view>已获取拼图数</view>
          </view>
          <view class="text-center margin-left">
            <view class="text-xdf">{{ puzzleGameData.puzzleUser.giftNum || 0 }}</view>
            <view>已得好礼数</view>
          </view>
          <view class="text-center margin-left">
            <view class="text-xdf">{{ puzzleGameData.puzzleUser.helpNum || 0 }}</view>
            <view>今日助力数</view>
          </view>
        </view>
      </view>

      <!-- 活动说明和活动奖品 -->
      <template v-if="page == 1">
        <!-- 活动说明 -->
        <view 
          class="activity-desc text-black padding-lg bg-white margin-bottom-sm" 
          v-if="puzzleGameData.puzzleInfo"
        >
          <view class="title text-df">活动说明</view>
          <scroll-view scroll-y class="scroll-view" style="height: 240rpx;">
            <view class="text-df">
              <view>活动时间：</view>
              <text class="text-xsm">{{ puzzleGameData.puzzleInfo.startTime || '暂无' }} ~ {{ puzzleGameData.puzzleInfo.endTime || '暂无' }}</text>
            </view>
            <view class="margin-top-sm text-df">
              <view>活动规则：</view>
              <text class="text-xsm">{{ puzzleGameData.puzzleInfo.ruler || '暂无' }}</text>
            </view>
          </scroll-view>
        </view>
        <!-- 活动奖品 -->
        <view class="activity-prize padding-lg bg-white margin-bottom-sm" v-if="puzzleGameData.puzzlePrizes">
          <view class="title text-df">活动奖品</view>
          <scroll-view scroll-x class="scroll-view" style="width: 100%;">
            <view class="flex justify-between margin-top">
              <view v-for="(item, index) in puzzleGameData.puzzlePrizes" :key="item.id" style="position: relative;" class="margin-right-lg">
                <image class="prize-img" :src="item.url" mode="aspectFill" />
                <view class="prize-num">{{ `奖品${index + 1}` }}</view>
                <view class="prize-name text-black text-xsm text-center overflow-2" style="width: 150rpx;">
                  {{ item.name }}
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </template>

      <!-- 拼图游戏 -->
      <template v-if="page == 2">
        <view class="puzzle-game">
          <view class="puzzle-game-title text-white text-df">
            我的拼图：
            <text class="current-prize-name" v-if="currentPrizeName">{{ currentPrizeName }}</text>
          </view>
          <view class="margin-top-sm">
            <swiper	
              circular
              :interval="3000"
              :duration="500"
              style="width: 614rpx; height: 670rpx; border-radius: 20rpx; background: linear-gradient(to right, #d9dcfa, #f8fafc); box-shadow: 0 6rpx 10rpx #8c8be6; margin: 0 auto; position: relative;"
              easing-function="linear"
              @change="handleSwiperChange"
              :indicator-dots="true"
              :indicator-color="'rgba(74, 36, 215, 0.3)'"
              :indicator-active-color="'#4a24d7'"
              indicator-active-class="custom-indicator-active"
              indicator-class="custom-indicator"
              :current="currentSwiperIndex"
            >
              
              <swiper-item 
                v-for="item in puzzleGameData.puzzlePrizes"
                :key="item.id"
                style="position: relative; left: 0; top: 0;"
              >
                <view 
                  class="puzzle-grid" 
                  style="width: 100%; height: 100%;  border-radius: 20rpx; background: linear-gradient(to right, #d9dcfa, #f8fafc); box-shadow: 0 6rpx 10rpx #8c8be6;"
                >
                  <view 
                    v-for="(prize, prizeIndex) in item.puzzleJson" 
                    :key="prizeIndex" 
                    class="puzzle-item"
                  >
                    <!-- 有拼图碎片 显示拼图碎片图片 碎片图片透明度为1 -->
                    <image 
                      :src="prize" 
                      :style="{ opacity: getPuzzleNumber(item.id, prizeIndex) ? 1 : 0.1 }"  
                      mode="aspectFill"  
                      class="puzzle-image" 
                      :id="`puzzle-item-img-${item.id}-${prizeIndex}`"
                    />
                    <view class="puzzle-number"  v-if="getPuzzleNumber(item.id, prizeIndex)">
                      {{ getPuzzleNumber(item.id, prizeIndex) || 0 }}
                    </view>
                  </view>
                </view>
              </swiper-item>
            </swiper>
          </view>
        </view>

        <!-- 拼图碎片弹框 -->
        <view
          class="cu-modal pull-out-box"
          :class="pullOutBox ? 'show' : ''"
          @tap="pullOutBox = false"
        >
          <view
            class="cu-dialog"
            style="background: transparent; height: 800rpx;"
            @tap.stop
          >
            <view class="pull-out-box-img">
              <image 
                class="pull-img" 
                v-if="pullOutData.puzzleFragment"
                :src="pullOutData.puzzleFragment.imgUrl" 
                mode="aspectFill"
                :class="{'animate-to-position': isAnimating}"
                :style="animationStyle"
              />
            </view>
            <view v-if="!isAnimating" class="take-btn text-xdf" @click="handleTakePrize">开心收下</view>
          </view>
        </view>

        <!-- 中奖弹框 -->
        <view
          v-if="winData && winData.isWin"
          class="cu-modal win-box"
          :class="isWin ? 'show' : ''"
          catchtouchmove="touchMove"
          @tap="isWin = false"
        >
          <view class="cu-dialog win-box-dialog padding-lg" @tap.stop>
            <view class="cuIcon-roundclose close-btn" @click="isWin = false"></view>
            <view class="text-center text-lg">恭喜你集齐拼图，获得如下奖品</view>
            <image :src="winData.prizeImg" mode="aspectFill" class="win-img" />
            <view class="win-btn text-center text-lg" @click="handleWin">马上兑奖</view>
          </view>
        </view>
      </template>

      <!-- 抽奖按钮 -->
      <view class="raffle-btn-wrap text-sm" v-if="puzzleGameData.puzzleInfo">
        <button 
          v-if="puzzleGameData.puzzleInfo.status == 2" 
          class="text-df"
          :class="puzzleGameData.puzzleUser && puzzleGameData.puzzleUser.drawNum > 0 ? 'raffle-btn' : 'raffle-btn-disabled'" 
          @click="handleRaffle"
        >{{ raffleLoading ? '加载中...' : '抽拼图' }}</button>
        <button 
          v-else-if="puzzleGameData.puzzleInfo.status == 3" 
          class="raffle-btn-disabled text-df"
        >活动已结束</button>
      </view>

      <!-- 抽奖次数 -->
      <view v-if="puzzleGameData.puzzleInfo &&puzzleGameData.puzzleInfo.status == 2 && puzzleGameData.puzzleUser" class="draws-num text-center text-sm margin-top-sm">
        <view style="color: #4a24d7;">今日还有 {{ puzzleGameData.puzzleUser.drawNum || 0 }} 次抽奖机会</view>
      </view>
    </view>
    <view v-else>
      <!-- <view>活动已结束</view> -->
      <image class="not-started-img" src="https://img.songlei.com/live/pintu/not-started.png" mode="aspectFill" />
    </view>
    <!-- 菜单 活动介绍、分享好友、我的拼图 -->
    <view class="menu-wrap text-sm text-white flex align-center justify-between">
      <view class="menu-item" @click="activityDescVisible = true">
        <image src="https://img.songlei.com/live/pintu/acr-desc-icon.png" mode="aspectFill" class="menu-icon" />
        <view class="text-center margin-top-sm text-sm">活动介绍</view>
      </view>
      <view class="menu-item" @click="handleShare">
        <image src="https://img.songlei.com/live/pintu/share-icon.png" mode="aspectFill" class="menu-icon" />
        <view class="tips">
          <view>好友助力</view>
          <view>得抽奖拼图机会</view>
        </view>
        <view class="text-center margin-top-sm text-sm">分享好友</view>
      </view>
      <view class="menu-item" @click="handleMyPrizes">
        <image src="https://img.songlei.com/live/pintu/my-prize.png" mode="aspectFill" class="menu-icon" />
        <view class="text-center margin-top-sm text-sm">我的奖品</view>
      </view>
    </view>

    <!-- 活动介绍弹框 -->
    <activity-desc 
      v-if="activityDescVisible && puzzleGameData"
      :visible="activityDescVisible" 
      :puzzleGameData="puzzleGameData"
      @close="() => activityDescVisible = false"
    />

    <!-- 助力成功弹框 -->
    <enabling 
      v-if="enablingVisible && puzzleGameData"
      :visible="enablingVisible"
      :isHelp="puzzleGameData.isHelp"
      @close="() => enablingVisible = false"
    />

    <!-- 分享好友弹框 -->
    <share-friends 
      ref="shareFriends"
      :visible="shareFriendsVisible"
      :id="id"
      :inviterId="userCode"
      :shareImg="puzzleGameData.puzzleInfo.shareImg"
      :shareParams="shareParams"
      @close="() => shareFriendsVisible = false"
    />
  </view>
</template>

<script>
let app = getApp()
import { getPuzzleGame, getLottery } from './api'
import ActivityDesc from './components/activitydesc.vue' // 活动说明弹框
import Enabling from './components/enabling.vue' // 助力成功弹框
import ShareFriends from './components/sharefriends.vue' // 分享好友弹框

export default {
  components: {
    ActivityDesc,
    Enabling,
    ShareFriends
  },
  data () {
    return {
      id: '',
      puzzleGameData: {},
      activityDescVisible: false,
      page: 1,
      currentPrizeName: '',
      pullOutBox: false, // 抽拼图 中奖弹框
      pullOutData: {}, // 抽拼图 中奖数据
      isAnimating: false,
      animationStyle: {},
      targetPosition: null,
      currentSwiperIndex: 0,
      isWin: false,
      enablingVisible: false, // 助力成功弹框
      shareFriendsVisible: false, // 分享好友弹框
      inviterId: '',
      
      userCode: '',
      winData: null,
      loading: false,
      raffleLoading: false,
      shareParams: {}
    }
  },
  onLoad(options) {
    // 设置页面标题
    // uni.setNavigationBarTitle({
    //   title: '游戏首页'
    // })
    const userInfo = uni.getStorageSync('user_info')
    // 判断是否登录
    if (!userInfo) {
      app.loopUser()
      return
    }
    this.userCode = userInfo.userCode
    this.id = options.id || ''
    this.inviterId = options.inviterid || ''
    
    // 初始化微信分享

    this.getPuzzleGameData()

    this.shareParams = {
      title: `参与活动赢好礼`,
      desc: `我在参加拼图得好礼的活动，邀请您来参加，顺便帮我助力`,
      imgUrl: 'https://img.songlei.com/live/pintu/logo.png',
      page: 'pages/jigsaw/index?id=' + this.id + '&inviterid=' + userInfo.userCode,
	    url: 'pages/jigsaw/index?id=' + this.id + '&inviterid=' + userInfo.userCode,
    }
  },
  onShow() {
    this.winData = null
    this.shareFriendsVisible = false
  },
  onShareAppMessage() {
    // 自定义分享内容
    return {
      title: this.shareParams.title,
      desc: this.shareParams.desc,
      path: this.shareParams.url,
      imageUrl: this.puzzleGameData?.puzzleInfo?.shareImg || this.shareParams.imgUrl
    };
  },
  methods: {
    // 处理分享
    handleShare() {
      this.shareFriendsVisible = true
      this.$refs.shareFriends?.getShareFriends(this.id)
    },
    
    // 获取拼图游戏数据
    getPuzzleGameData() {
      if (this.loading) return
      this.loading = true
      const params = {
        id: this.id
      }
      if (this.inviterId) {
        params.shareUserId = this.inviterId
      }
      getPuzzleGame(params).then(res => {
        if (res.code == 0) {
          if (!res.data) {
            uni.showToast({
              title: res.msg || '获取数据失败',
              icon: 'none'
            })

            this.puzzleGameData = null
            return
          }
          this.puzzleGameData = res.data
          this.currentPrizeName = this.puzzleGameData.puzzlePrizes && this.puzzleGameData.puzzlePrizes[this.currentSwiperIndex] ? this.puzzleGameData.puzzlePrizes[this.currentSwiperIndex].name : ''
          this.enablingVisible = this.puzzleGameData.isHelpBox == '1' ? true : false

          if (this.puzzleGameData.puzzleInfo.status == 2) {
            this.page = this.puzzleGameData.puzzleUser && this.puzzleGameData.puzzleUser.puzzleNum > 0 ? 2 : 1
          } else {
            this.page = 1
          }
        }
        this.loading = false
      }).catch(err => {
        // console.error('获取拼图游戏数据失败:', err)
        this.loading = false
      })
    },
    
    // 抽拼图 按钮
    handleRaffle() {
      if (this.puzzleGameData.puzzleUser.drawNum <= 0) {
        uni.showToast({
          title: '今日抽奖次数已用完',
          icon: 'none'
        })
        this.raffleLoading = false
        return
      }
      if (this.raffleLoading) return
      this.raffleLoading = true
      this.page = 2
      getLottery({
        id: this.id
      }).then(res => {
        this.pullOutData = res.data

        this.winData = {
          isWin: res.data.isWin,
          prizeImg: res.data.prizeImg,
          prizeUserId: res.data.prizeUserId
        }

        this.pullOutBox = true
        this.raffleLoading = false
        
      }).catch(err => {
        this.raffleLoading = false
      })
    },

    handleSwiperChange(e) {
      const currentIndex = e.detail.current;
      if (this.puzzleGameData.puzzlePrizes && this.puzzleGameData.puzzlePrizes[currentIndex]) {
        this.currentPrizeName = this.puzzleGameData.puzzlePrizes[currentIndex].name;
      }
    },

    // 开心收下按钮 处理收下拼图
    handleTakePrize() {
      if (!this.pullOutData.puzzleFragment) return
      
      // 找到当前奖品
      const currentPrize = this.puzzleGameData.puzzlePrizes.find(prize => 
        prize.puzzleJson.some(p => p === this.pullOutData.puzzleFragment.imgUrl)
      )
      
      if (currentPrize) {
        // 找到目标拼图
        const targetPuzzle = currentPrize.puzzleJson.find(
          p => p === this.pullOutData.puzzleFragment.imgUrl
        )
        
        if (targetPuzzle) {
          // 获取目标位置
          const index = currentPrize.puzzleJson.indexOf(targetPuzzle)
          
          // 获取当前奖品索引
          const prizeIndex = this.puzzleGameData.puzzlePrizes.findIndex(p => p.id === currentPrize.id)
          
          // 如果当前不在目标奖品页，先切换到对应奖品页
          if (prizeIndex !== this.currentSwiperIndex) {
            this.currentSwiperIndex = prizeIndex
            
            // 等待swiper切换完成
            setTimeout(() => {
              this.startAnimation(currentPrize.id, index)
            }, 600) 
          } else {
            this.startAnimation(currentPrize.id, index)
          }
        }
      }
    },

    // 重新实现动画逻辑
    startAnimation(prizeId, index) {
      // 使用微信小程序支持的方式获取元素位置
      const query = uni.createSelectorQuery().in(this)
      const targetId = `puzzle-item-img-${prizeId}-${index}`
      
      query.select(`#${targetId}`).boundingClientRect(targetRect => {
        if (!targetRect) {
          console.error('找不到目标元素:', targetId)
          this.pullOutBox = false
          return
        }
        
        console.log('找到目标位置:', targetRect)
        
        // 标记开始动画
        this.isAnimating = true
        
        // 设置初始样式
        this.animationStyle = {
          position: 'fixed',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          width: '300rpx',
          height: '300rpx',
          zIndex: '9999',
          transition: 'none'
        }
        
        // 等待DOM更新
        setTimeout(() => {
          // 设置目标样式，触发动画
          this.animationStyle = {
            position: 'fixed',
            left: `${targetRect.left}px`,
            top: `${targetRect.top}px`,
            width: `${targetRect.width}px`,
            height: `${targetRect.height}px`,
            transform: 'none',
            transition: 'all 0.5s ease-in-out',
            zIndex: '9999'
          }
          
          // 等待过渡开始后关闭弹框
          setTimeout(() => {
            this.pullOutBox = false
          }, 100)
          
          // 动画结束后处理
          setTimeout(() => {
            this.isAnimating = false
            this.animationStyle = {}
            
            // 更新本地数据
            this.updateLocalPuzzleData(prizeId, index)
            
            // 判断是否中奖
            if (this.winData && this.winData.isWin) {
              setTimeout(() => {
                this.isWin = true
              }, 300)
            }
            
            // 刷新数据
            setTimeout(() => {
              this.getPuzzleGameData()
            }, 500)
          }, 500)
        }, 50)
      }).exec()
    },

    // 单独提取数据更新逻辑
    updateLocalPuzzleData(prizeId, index) {
      try {
        if (!this.puzzleGameData) {
          return console.error("无法更新数据: puzzleGameData 不存在")
        }
        
        if (!this.puzzleGameData.myPuzzles) {
          this.puzzleGameData.myPuzzles = []
        }
        
        // 找到对应奖品
        let myPrizeData = this.puzzleGameData.myPuzzles.find(p => p.prizeId === prizeId)
        
        if (!myPrizeData) {
          myPrizeData = {
            prizeId: prizeId,
            puzzleFragments: []
          }
          this.puzzleGameData.myPuzzles.push(myPrizeData)
        }
        
        // 找到对应碎片
        let fragment = myPrizeData.puzzleFragments.find(f => f.index === index)
        
        if (fragment) {
          fragment.number = (fragment.number || 0) + 1
        } else {
          myPrizeData.puzzleFragments.push({ index: index, number: 1 })
        }
        
        // 更新总数
        if (this.puzzleGameData.puzzleUser) {
          this.puzzleGameData.puzzleUser.puzzleNum = (this.puzzleGameData.puzzleUser.puzzleNum || 0) + 1
        }
        
        // 强制视图更新
        this.$forceUpdate()
      } catch (e) {
        console.error("更新本地数据失败:", e)
      }
    },

    // 马上兑奖按钮 处理兑奖
    handleWin() {
      console.log('handleWin', this.pullOutData.prizeUserId)
      uni.navigateTo({
        url: `/pages/jigsaw/myprizes?id=${this.id}&prizeId=${this.pullOutData.prizeUserId}`
      })
    },

    // 获取拼图碎片数量
    getPuzzleNumber(prizeId, index) {
      const myPuzzle = this.puzzleGameData.myPuzzles.find(p => p.prizeId === prizeId)
      if (!myPuzzle) return null
      
      const fragment = myPuzzle.puzzleFragments.find(p => p.index === index)
      return fragment ? fragment.number : null
    },

    // 我的奖品按钮
    handleMyPrizes() {
      console.log('handleMyPrizes')
      uni.navigateTo({
        url: `/pages/jigsaw/myprizes?id=${this.id}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// @import "../../app.scss";
.container {
  width: 100vw;
  min-height: 100vh;
  background: url('https://img.songlei.com/live/pintu/act-deesc-bg.png') no-repeat center center;
  background-size: 100% 100%;
  .not-started-img{
    display: block;
    margin: 400rpx auto 0;
  }
  .user-info{
    width: 643rpx;
    border-radius: 20rpx;
    background: linear-gradient(to right, #d9dcfa, #f8fafc);
    box-shadow: 0 6rpx 10rpx #8c8be6;
    margin: 0 auto 20rpx;
    // margin-bottom: 20rpx;
    .avatar{
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.3);
      // position: absolute;
    }

    .info{
      // flex: 1;
      // background: linear-gradient(to right, #d9dcfa, #f8fafc);
      // border-radius: 60rpx;
      // padding: 0 20rpx;
    }
  }
  .activity-desc, .activity-prize{
    width: 643rpx;
    margin: 0 auto 20rpx;
    padding-top: 80rpx;
    border-radius: 20rpx;
    background: linear-gradient(to right, #d9dcfa, #f8fafc);
    box-shadow: 0 6rpx 10rpx #8c8be6;
    position: relative;
    .title{
      position: absolute;
      top: 0;
      left: 0;
      color: #fff;
      padding: 10rpx 20rpx;
      background-color: #868ced;
      border-radius: 20rpx 0 20rpx 0;
    }
  }
  .activity-prize{
    position: relative;
    .prize-img{
      width: 150rpx;
      height: 150rpx;
      border-radius: 20rpx;
    }
    .prize-num{
      position: absolute;
      top: -16rpx;
      right: 0;
      color: #fff;
      padding: 0 10rpx;
      font-size: 24rpx;
      background-color: #f83232;
      border-radius: 10rpx;
    }
  }
  .raffle-btn-wrap{
    margin: 40rpx auto 0;
    width: 350rpx;
    .raffle-btn{
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 60rpx;
      background: linear-gradient(to top, #bcc6ff, #f8fafc);
      color: #4a24d7;
      box-shadow: 0 6rpx 0 #4a24d7;
    }
    .raffle-btn-disabled{
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 60rpx;
      background: linear-gradient(to top, #d3d2d3, #efefef);
      color: #606060;
      box-shadow: 0 6rpx 0 #606060;
    }
  }
  .menu-wrap{
    width: 643rpx;
    margin: 80rpx auto 0;
    .menu-item{
      position: relative;
      .tips{
        width: 160rpx;
        position: absolute;
        top: -40rpx;
        right: -110rpx;
        font-size: 20rpx;
        color: #fff;
        background-color: #f83232;
        border-radius: 20rpx;
        padding: 0 10rpx;
      }
    }
    .menu-icon{
      display: block;
      width: 80rpx;
      height: 80rpx;
      margin: 0 auto;
    }
  }
  .puzzle-game {
    position: relative;
    .puzzle-game-title {
      width: 624rpx;
      margin: 0 auto;
    }
    .puzzle-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(3, 1fr);
      gap: 20rpx;
      padding: 20rpx;
      box-sizing: border-box;
      
      .puzzle-item {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
        border: 2rpx solid #8c8be6;
        overflow: hidden;
        
        .puzzle-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          opacity: 0.1;
          border-radius: 10rpx;
        }
        .puzzle-number{
          width: 40rpx;
          height: 40rpx;
          line-height: 40rpx;
          text-align: center;
          position: absolute;
          top: 0;
          right: 0;
          background-color: #f83232;
          color: #fff;
          border-radius: 50%;
          font-size: 24rpx;
        }
      }
    }
    :deep(.custom-indicator) {
      width: 20rpx;
      height: 20rpx;
      border-radius: 50%;
      margin: 0 10rpx;
      transform: translateY(20rpx);
    }
    :deep(.custom-indicator-active) {
      width: 30rpx;
      height: 30rpx;
      border-radius: 50%;
      margin: 0 10rpx;
      transform: translateY(20rpx);
    }
   
  }
  .pull-out-box{
    .cu-dialog {
      background: transparent;
    }
    .take-btn{
      width: 300rpx;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 60rpx;
      background: linear-gradient(to top, #bcc6ff, #f8fafc);
      color: #4a24d7;
      box-shadow: 0 6rpx 0 #4a24d7;
      margin: 0 auto;
      transition: all 0.2s ease-out;
      
      &:active {
        transform: translateY(6rpx);
        box-shadow: none;
      }
    }
    .pull-out-box-img{
      width: 100%;
      height: 500rpx;
      background: url('https://img.songlei.com/live/pintu/rays.png') no-repeat center center;
      background-size: 100% auto;
      margin: 0 auto 40rpx;
      transition: all 0.2s ease-out;
      position: relative;
      .pull-img{
        width: 300rpx;
        height: 300rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.2s ease-out;
      }
    }
    
  }
  .win-box{
    .win-box-dialog{
      background: linear-gradient(to right, #d9dcfa, #f8fbfb);
      color: #4a24d7;
      box-shadow: 0 6rpx 5rpx #868ced;
      .close-btn{
        width: 50rpx;
        margin: -13rpx -12rpx 0 auto;
        font-size: 50rpx;
        color: #8f8f8f;
        text-align: right;
      }
      .win-img{
        width: 385rpx;
        height: 385rpx;
        margin: 35rpx auto 45rpx;
        border-radius: 30rpx;
        box-shadow: 0 3rpx 8rpx #4a24d7;
      }
      .win-btn{
        width: 459rpx;
        height: 88rpx;
        line-height: 88rpx;
        text-align: center;
        background: #868ced;
        color: #fff;
        border-radius: 40rpx;
        margin: 0 auto;
      }
    }
  }
  
}

.animate-to-position {
  will-change: transform, width, height;
}

/* 修复动画样式选择器 */
.pull-img.animate-to-position {
  will-change: transform, width, height, left, top;
  z-index: 9999 !important;
  transition: all 0.5s ease-in-out !important;
}
</style>