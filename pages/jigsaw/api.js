// import request from "@/utils/request.js";
import { requestApi as request } from '@/utils/api.js'

// 获取拼图游戏
export const getPuzzleGame = data => {
  return request({
  	url: '/mallmarket/puzzle/game',
  	method: 'get',
  	data
  })
};

// 抽奖
export const getLottery = data => {
  return request({
  	url: '/mallmarket/puzzle/lotteries',
  	method: 'get',
  	data
  })
};

// 我的奖品
export const getMyPrizes = data => {
  return request({
  	url: '/mallmarket/puzzle/myPrize',
  	method: 'get',
  	data
  })
};

// 兑奖
export const getPrize = data => {
  return request({
  	url: '/mallmarket/puzzle/cash/prize',
  	method: 'get',
  	data
  })
};

// 分享好友
export const getShareFriends = data => {
  return request({
  	url: '/mallmarket/puzzle/share',
  	method: 'get',
  	data
  })
};