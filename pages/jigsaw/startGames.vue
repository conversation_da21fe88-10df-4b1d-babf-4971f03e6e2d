<template>
  <view class="container">
    <button class="start-btn text-xl flex align-center justify-center" @click="startGame">
      开始游戏
      <image src="https://img.songlei.com/live/pintu/index-icon.png" mode="aspectFill" class="icon" />
    </button>
  </view>
</template>

<script>
let app = getApp()
export default {
  data () {
    return {
      id: ''
    }
  },
  onLoad(options) {
    // 设置页面标题
    // uni.setNavigationBarTitle({
    //   title: '首页'
    // })
    if (options.q) {
      // 解码 需要两次解码才可以解析成常规链接
      const url = decodeURIComponent(options.q); 
      // 解码
      const decodedUrl = decodeURIComponent(url); 
      // id
      const id = util.UrlParamHash(decodedUrl.split('?')[1], 'id');
      this.id = id
    }
    if (options.id) {
      this.id = options.id
    }
    if (options.scene) {
      const scene = decodeURIComponent(options.scene);
      if (scene) {
        //接受二维码中参数  参数sf=XXX&id=XXX
        const id = util.UrlParamHash(scene, 'id');
        if (id) {
          this.id = id;
        }
      }
    }
  },
  methods: {
    startGame() {
      if (app.isUser() == 1) {
        uni.navigateTo({
          url: `/pages/jigsaw/index?id=${this.id}`
        })
      } else {
        app.loopUser()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  min-height: 100vh;
  background-color: #b7b8ee;
  background: url('https://img.songlei.com/live/pintu/index-bg.png') no-repeat center center;
  background-size: 100% 100%;
  position: relative;
}
.start-btn {
  width: 357rpx;
  height: 102rpx;
  background: linear-gradient(to top, #bcc6ff, #f8fafc);
  color: #4a24d7;
  box-shadow: 0 6rpx 0 #4a24d7;
  line-height: 102rpx;
  border-radius: 60rpx;
  position: absolute;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  .icon{
    width: 32rpx;
    height: 32rpx;
    margin-left: 30rpx;
  }
}
</style>