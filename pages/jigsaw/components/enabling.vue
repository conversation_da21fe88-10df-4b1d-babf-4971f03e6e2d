<template>
  <view
    class="cu-modal"
    :class="localVisible ? 'show' : ''"
    catchtouchmove="touchMove"
    @tap="handleClose"
  >
    <view
      class="cu-dialog padding text-left dialog"
      @tap.stop
    >
      <image class="img" src="https://img.songlei.com/live/pintu/help.png" mode="aspectFill" />
      <view class="text-center text-xl" style="color: #765fcc;" v-if="isHelp != 3">好友已助力，拼图免费得好礼活动火爆进行中，现诚邀您来参加，礼品丰厚</view>
      <view class="text-center text-xl" style="color: #765fcc;" v-else>拼图免费得好礼活动火爆进行中，现诚邀您来参加，礼品丰厚</view>
      <view class="close-btn text-center text-df text-bold" @click="handleClose">马上参加</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ActivityDesc',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isHelp: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      localVisible: false
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        this.localVisible = newVal
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// @import "../../../app.scss";
.dialog{
  background: linear-gradient(to right, #d9dcfa, #f8fafc);
  box-shadow: 0 6rpx 10rpx #8c8be6;
}
.img{
  width: 280rpx;
  height: 280rpx;
  margin: 30rpx auto;
  display: block;
}

.close-btn{
  width: 400rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #868ced;
  color: #fff;
  border-radius: 40rpx;
  margin: 60rpx auto 0;
}
</style>