<template>
  <view
    class="cu-modal"
    :class="localVisible ? 'show' : ''"
    catchtouchmove="touchMove"
    @tap="handleClose"
  >
    <view
      class="cu-dialog padding-sm text-left dialog"
      @tap.stop
    >
      <template v-if="!finalShareImg">
        <view class="text-center text-xml text-bold" style="color: #765fcc;">分享好友</view>
        <view class="text-df padding-lr-sm margin-top" style="color: #765fcc;">
          <view>每日邀请好友，最多可获 {{ shareData.drawMaxNum || 0 }} 次抽拼图机会</view>
          <view>今日助力获得抽奖机会剩余：{{ shareData.availableDrawNum || 0 }} 次</view>
        </view>
        <view class="margin-top" v-if="friendsList.length">
          <view class="current-boosting-friends padding-sm margin-top-sm">
            <view class="text-xdf margin-bottom-sm" style="color: #765fcc;">当前助力好友</view>
            <scroll-view scroll-y class="scroll-view" style="height: 250rpx;">
              <view 
                class="friend-item flex align-center justify-between margin-top-sm text-left" 
                v-for="(item, index) in friendsList" 
                :key="index"
              >
                <view class="flex align-center">
                  <image v-if="item.headImg" :src="item.headImg" mode="aspectFill" class="avatar margin-right-sm" />
                  <text class="text-cut text-sm" style="width: 48rpx">{{ item.nickName }}</text>
                </view>
                <view class="text-sm">
                  {{ item.helpDate }}
                </view>
                <view class="text-sm">
                  {{ item.helpResult }}
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
        <view class="button-group flex justify-between margin-top-lg">
          <view class="close-btn text-center text-df" style="margin: auto;" @click="handleShare">分享好友</view>
          <!-- <view class="close-btn text-center text-df" @click="handleGeneratePoster">生成海报</view> -->
        </view>
      </template>
      <view class="share-preview margin-top" v-if="finalShareImg">
        <image :src="finalShareImg" mode="widthFix" class="share-image"></image>
        <view class="text-center text-lg">长按保存图片</view>
        <!-- <view class="save-btn text-center text-lg" @click="saveImage">保存图片</view> -->
      </view>
    </view>
    <share-component 
      v-if="showShare"
      v-model="showShare" 
      :shareParams="shareParams" 
      :showSharePoster="false" 
    />
  </view>
</template>

<script>
import shareComponent from "@/components/share-component/index"
import { getShareFriends } from '../api'
const QR = require("@/utils/wxqrcode.js")

export default {
  name: 'ActivityDesc',
  components: { shareComponent },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    inviterId: {
      type: String,
      default: ''
    },
    shareImg: {
      type: String,
      default: 'https://img.songlei.com/live/pintu/logo.png'
    },
    shareParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localVisible: false,
      shareData: {},
      friendsList: [],
      shareUri: '',
      qrCodeImg: '',
      finalShareImg: '',
      showShare: false,
      // shareParams: {}
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        this.localVisible = newVal
        if (!newVal) {
          this.showShare = false
        }
      },
      immediate: true
    },
  },
  mounted() {
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleShare() {
      this.showShare = true
      // 获取当前页面URL
      // const currentUrl = window.location.href
      // // 分享URL应包含活动id
      // const shareUrl = currentUrl.includes('?') ? 
      //   `${currentUrl}&inviterid=${this.inviterId}` : 
      //   `${currentUrl}?inviterid=${this.inviterId}`
    },
    getShareFriends(paramsId) {
      getShareFriends({
        id: paramsId ||this.id
      }).then(res => {
        if (res.code == 0) {
          this.shareData = res.data
          if (res.data.puzzleUserVos.length) {
            const frends = [{
              nickName: '好友',
              helpDate: '助力时间',
              helpResult: '助力结果'
            }]
            this.friendsList = [...frends, ...res.data.puzzleUserVos]
          } else {
            this.friendsList = []
          }
          console.log(this.friendsList)
        }
      })
    },
    // async handleGeneratePoster() {
    //   try {
    //     // 获取当前页面URL
    //     // const currentUrl = window.location.href
    //     // // 分享URL应包含活动id
    //     // const shareUrl = currentUrl.includes('?') ? 
    //     //   `${currentUrl}&inviterid=${this.inviterId}` : 
    //     //   `${currentUrl}?inviterid=${this.inviterId}`
        
    //     // 保存分享链接
    //     // this.shareUri = 
    //     this.shareUri = this.shareParams.url

    //     // 创建canvas
    //     const canvas = document.createElement('canvas')
    //     const ctx = canvas.getContext('2d')
        
    //     // 设置canvas大小和边距
    //     const margin = 20 // 统一的边距
    //     canvas.width = 300
    //     canvas.height = 450 // 增加画布高度，确保底部边距
        
    //     // 绘制渐变背景
    //     const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0)
    //     gradient.addColorStop(0, '#f8fafc')
    //     gradient.addColorStop(1, '#d9dcfa')
    //     ctx.fillStyle = gradient
    //     ctx.fillRect(0, 0, canvas.width, canvas.height)
        
    //     // 加载并绘制分享图片
    //     const img = new Image()
    //     img.crossOrigin = 'anonymous'
    //     img.src = this.shareImg
    //     await new Promise((resolve, reject) => {
    //       img.onload = () => {
    //         // 计算图片尺寸，保持比例
    //         const imgWidth = canvas.width - (margin * 2)
    //         const imgHeight = (imgWidth * img.height) / img.width
    //         const imgX = margin
    //         const imgY = margin
    //         ctx.drawImage(img, imgX, imgY, imgWidth, imgHeight)
            
    //         // 计算底部内容的位置（与顶部图片保持相同距离）
    //         const bottomY = imgY + imgHeight + margin
            
    //         // 绘制标题
    //         ctx.fillStyle = '#333333'
    //         ctx.font = 'bold 16px Arial'
    //         ctx.textAlign = 'left'
    //         const title = '参与活动赢好礼'
    //         const titleX = margin
    //         const titleY = bottomY + 10
    //         ctx.fillText(title, titleX, titleY)
            
    //         // 绘制描述文字
    //         ctx.font = '14px Arial'
    //         const desc = '我在参加拼图得好礼的活\n动，邀请您来参加，顺便\n帮我助力'
    //         const descX = margin
    //         const descY = titleY + 25
    //         const lines = desc.split('\n')
    //         lines.forEach((line, index) => {
    //           ctx.fillText(line, descX, descY + (index * 20))
    //         })

    //         // 生成二维码
    //         const qrSize = 100 // 增大二维码尺寸
    //         const qrCode = QR.createQrCodeImg(shareUrl, {
    //           size: qrSize,
    //           typeNumber: 8,
    //           margin: 0 // 移除二维码的内边距
    //         })
            
    //         // 绘制二维码
    //         const qrImg = new Image()
    //         qrImg.crossOrigin = 'anonymous'
    //         qrImg.src = qrCode
    //         qrImg.onload = () => {
    //           const qrX = canvas.width - qrSize - margin // 添加右边距
    //           const qrY = bottomY - 10
    //           ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize)
              
    //           // 转换为图片
    //           this.finalShareImg = canvas.toDataURL('image/jpeg', 1.0)
    //         }
    //         qrImg.onerror = reject
    //         resolve()
    //       }
    //       img.onerror = reject
    //     })
    //   } catch (error) {
    //     console.error('生成海报失败:', error)
    //     uni.showToast({
    //       title: '生成海报失败',
    //       icon: 'none'
    //     })
    //   }
    // },
  }
}
</script>

<style lang="scss" scoped>
.dialog{
  background: linear-gradient(to right, #d9dcfa, #f8fafc);
  box-shadow: 0 6rpx 10rpx #8c8be6;
}

.current-boosting-friends{
  background: linear-gradient(to right, #d1d1f6, #eeeff9);
  // height: 300rpx;
  border-radius: 10rpx;
}
.friend-item{
  .avatar{
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }
}

.button-group {
  
  .close-btn {
    width: 250rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background: #868ced;
    color: #fff;
    border-radius: 40rpx;
  }
}

.share-preview {
  margin-top: 20rpx;
  text-align: center;
  
  .share-image {
    width: 100%;
    max-width: 600rpx;
    border-radius: 10rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  }
  
  .save-btn {
    width: 300rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: #868ced;
    color: #fff;
    border-radius: 40rpx;
    margin: 20rpx auto;
  }
}
</style>