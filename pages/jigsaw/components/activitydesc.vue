<template>
  <view
    class="cu-modal"
    :class="localVisible ? 'show' : ''"
    catchtouchmove="touchMove"
    @tap="handleClose"
  >
    <view
      class="cu-dialog padding-sm text-left act-desc-dialog"
      @tap.stop
    >
      <image class="title-img" src="https://img.songlei.com/live/pintu/act-title.png" mode="aspectFill" />
      <view class="activity-desc margin-top-sm" v-if="puzzleGameData.puzzleInfo">
        <view class="title text-df margin-bottom-sm">活动说明</view>
        <scroll-view scroll-y class="scroll-view padding-lr" style="height: 300rpx;">
          <view class="text-sm">
            <view>活动时间：</view>
            <text>{{ puzzleGameData.puzzleInfo.startTime || '暂无' }} ~ {{ puzzleGameData.puzzleInfo.endTime || '暂无' }}</text>
          </view>
          <view class="text-sm margin-top-sm" style="padding-top:0;">
            <view>活动规则：</view>
            <text>{{ puzzleGameData.puzzleInfo.ruler || '暂无' }}</text>
          </view>
        </scroll-view>
      </view>
      <!-- 活动奖品 -->
      <view class="activity-prize margin-tb-sm" style="padding-top:0;" v-if="puzzleGameData.puzzlePrizes">
        <view class="title text-df margin-bottom-sm">活动奖品</view>
        <scroll-view scroll-x class="scroll-view  padding-lr" style="width: 100%;">
          <view class="flex justify-between padding-sm">
            <view v-for="(item, index) in puzzleGameData.puzzlePrizes" :key="item.id" style="position: relative;" class="margin-right">
              <image class="prize-img" :src="item.url" mode="aspectFill" />
              <view class="prize-num">{{ `奖品${index + 1}` }}</view>
              <view class="prize-name text-center text-sm margin-top-sm overflow-2" style="width: 100rpx;">
                {{ item.name }}
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="close-btn text-center text-df" @click="handleClose">关闭</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ActivityDesc',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    puzzleGameData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      localVisible: false
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        this.localVisible = newVal
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// @import "../../../app.scss";
.act-desc-dialog{
  background: linear-gradient(to right, #d9dcfa, #f8fafc);
  box-shadow: 0 6rpx 10rpx #8c8be6;
}
.title-img{
  width: 135rpx;
  height: 35rpx;
  margin: 0 auto;
  display: block;
}
.activity-desc, .activity-prize{
  .title{
    width: 165rpx;
    height: 60rpx;
    line-height: 60rpx;
    text-align: center;
    background: #868ced;
    color: #fff;
    border-radius: 40rpx;
  }

}
.activity-prize{
  .prize-img{
    width: 100rpx;
    height: 100rpx;
    border-radius: 20rpx;
    display: block;
    margin: 0 auto;
  }
  .prize-num{
    position: absolute;
    top: -16rpx;
    right: 0;
    color: #fff;
    padding: 0 10rpx;
    font-size: 24rpx;
    background-color: #f83232;
    border-radius: 10rpx;
  }
}
.close-btn{
  width: 165rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: #868ced;
  color: #fff;
  border-radius: 40rpx;
  margin: 60rpx auto 0;
}
</style>