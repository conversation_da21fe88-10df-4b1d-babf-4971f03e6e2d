<template>
  <view class="container padding-lg">
    <cu-custom :isBack="true" :iconColor="'#fff'" :boderColor="'#fff'" :hideMarchContent="true">
			<!-- <block slot="backText">返回</block> -->
			<block slot="content" style="color:#fff;">我的奖品</block>
		</cu-custom>
    <view v-if="noData">
      <image src="https://img.songlei.com/storagelocker/no-data.png" class="no-data" />
    </view>
    <view class="prize-list" v-else>
      <view class="prize-item padding-sm margin-bottom-sm" v-for="(item, index) in myPrizes" :key="index">
        <view class="flex align-center">
          <image 
            class="prize-img margin-right-sm" 
            :src="item.prizeImg" 
            mode="aspectFill" 
          />
          <view class="prize-item-info flex-sub" style="width: 470rpx;">
            <view class="flex justify-between" style="width: 470rpx;">
              <view class="text-lg overflow-2" style="width: 380rpx;">
                {{ item.prizeName }}
              </view>
              <view class="text-sm" :style="{ color: item.writeOffStatus == 0 ? '#4a24d7' : '#868ced' }">
                {{ item.writeOffStatus == 0 ? '未核销' : '已核销' }}
              </view>
            </view>
            <view class="text-sm margin-top-sm">
              奖品数量： {{ item.quantity }}
            </view>
            <view class="text-sm">
              中奖时间： {{ item.drawTime }}
            </view>
          </view>
        </view>
        <view class="audit text-sm" v-if="item.writeOffStatus == 0" @click="handleAudit(item.id)">去核销</view>
      </view>
    </view>

    <!-- 核销弹窗 -->
    <view
      class="cu-modal audit-box"
      :class="isAudit ? 'show' : ''"
      catchtouchmove="touchMove"
      @tap="handleAuditClose"
    >
      <view class="cu-dialog audit-box-dialog padding-sm text-left" @tap.stop>
        <view class="title text-df">核销二维码</view>
        <view class="margin-top">
          <!-- #ifdef MP-WEIXIN -->
          <canvas 
            style="width: 388rpx; height: 80rpx; background-color: #ffffff; margin: 0 auto;" 
            class="bar_code" 
            canvas-id="BrcodeAudit"
            v-if="isAudit"
          />
          <!-- #endif -->
          
          <!-- #ifdef H5 -->
          <!-- <canvas 
            style="width: 388rpx; height: 80rpx; background-color: #ffffff; margin: 0 auto;" 
            class="bar_code" 
            canvas-id="BrcodeAudit"
          /> -->
          <!-- #endif -->
          
          <image
            style="width: 250rpx; height: 250rpx; display: block; margin: 20rpx auto"
            mode="aspectFit"
            :src="qrImg"
          />
          <view class="text-center text-sm">领奖时请给工作人员出示核销二维码</view>
        </view>

        <view v-if="rules">
          <view class="title text-df margin-top-lg">核销规则</view>
          <scroll-view scroll-y class="scroll-view margin-top-sm" style="height: 240rpx;">
            <view class="padding-sm padding-left-lg text-sm" style="padding-top: 0;">
              <view>兑奖期限：{{ rules.prizeStartTime }} ~ {{  rules.prizeEndTime }}</view>
              <view>兑奖时段：{{ rules.prizeTime }}</view>
              <view>兑奖地点：{{ rules.prizePlace }}</view>
              <view>兑奖联系人：{{ rules.prizePeople }}</view>
              <view>联系电话：{{ rules.prizePhone }}</view>
              <view>
                兑奖须知：
                <text >{{ rules.detail }}</text>
              </view>
            </view>
          </scroll-view>
        </view>

        <view class="audit" style="margin: 20rpx auto; width: 400rpx;" @click="handleAuditClose">关闭</view>
      </view>
    </view>
  </view>
</template>

<script>
import { getMyPrizes, getPrize } from './api'
const brCode = require("utils/barcode.js");
const QR = require("utils/wxqrcode.js");

export default {
  name: 'MyPrizes',
  data() {
    return {
      id: '',
      myPrizes: [],
      noData: false,
      isAudit: false,
      loadingImg: '',
      qrImg: '',
      rules: null,
      prizeId: ''
    }
  },
  onLoad(options) {
    this.id = options.id || ''
    this.prizeId = options.prizeId || ''
    // uni.setNavigationBarTitle({
    //   title: '我的奖品'
    // })
    this.getMyPrizes()
  },
  mounted() {
    if (this.prizeId) {
      this.$nextTick(()=> {
        this.handleAudit(this.prizeId)
      })
    }
  },
  methods: {
    // 获取我的奖品 列表
    getMyPrizes() {
      getMyPrizes({ id: this.id }).then(res => {
        console.log(res)
        this.myPrizes = res.data
        if (!res.data?.length) {
          this.noData = true
        }
      })
    },
    // 去核销
    handleAudit(id) {
      this.isAudit = true
      this.qrImg = this.loadingImg;
      getPrize({id: id}).then((res) => {
        if (res.code != 0) return
        this.rules = res.data.puzzlePrize
        if (res.data.qrCode) {
          // #ifdef MP-WEIXIN
          // 先清空画布
          setTimeout(() => {
            const ctx = wx.createCanvasContext('BrcodeAudit', this);
            ctx.setFillStyle('#ffffff');
            ctx.fillRect(0, 0, 388, 80);
            ctx.draw(true, () => {
              // 清空画布后再绘制条形码
              brCode.code128(wx.createCanvasContext('BrcodeAudit'), res.data.qrCode, 275, 50);
            });
          }, 100);
          // #endif
          
          // #ifdef H5
          // this.$nextTick(() => {
          //   const ctx = uni.createCanvasContext('BrcodeAudit', this);
          //   // 清空画布
          //   ctx.setFillStyle('#ffffff');
          //   ctx.fillRect(0, 0, 388, 80);
            
          //   // 计算条形码
          //   const code128 = this.generateCode128(res.data.qrCode);
            
          //   // 绘制条形码
          //   ctx.setFillStyle('#000000');
          //   let x = 10;
          //   for (let i = 0; i < code128.length; i++) {
          //     if (code128[i] === '1') {
          //       ctx.fillRect(x, 0, 2, 50);
          //     }
          //     x += 2;
          //   }
          //   ctx.draw();
          // })
          // #endif
          
          this.qrImg = QR.createQrCodeImg(res.data.qrCode, {
            size: parseInt(300) //二维码大小  
          });
        }
      })
      
      console.log('去核销')
    },
    handleAuditClose() {
      // #ifdef MP-WEIXIN
      // 清空画布
      const ctx = wx.createCanvasContext('BrcodeAudit', this);
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, 388, 80);
      ctx.draw();
      // #endif
      
      // 延迟设置isAudit为false，确保清空画布操作完成
      setTimeout(() => {
        this.isAudit = false;
        this.getMyPrizes();
      }, 200);
    },
    // 生成 Code128 编码
    // generateCode128(text) {
    //   const CODE128_CHARS = {
    //     ' ': '11011001100',
    //     '!': '11001101100',
    //     '"': '11001100110',
    //     '#': '10010011000',
    //     '$': '10010001100',
    //     '%': '10001001100',
    //     '&': '10011001000',
    //     '\'': '10011000100',
    //     '(': '10001100100',
    //     ')': '11001001000',
    //     '*': '11001000100',
    //     '+': '11000100100',
    //     ',': '10110011100',
    //     '-': '10011011100',
    //     '.': '10011001110',
    //     '/': '10111001100',
    //     '0': '10011101100',
    //     '1': '10011100110',
    //     '2': '11001110010',
    //     '3': '11001011100',
    //     '4': '11001001110',
    //     '5': '11011100100',
    //     '6': '11001110100',
    //     '7': '11101101110',
    //     '8': '11101001100',
    //     '9': '11100101100',
    //     ':': '11100100110',
    //     ';': '11101100100',
    //     '<': '11100110100',
    //     '=': '11100110010',
    //     '>': '11011011000',
    //     '?': '11011000110',
    //     '@': '11000110110',
    //     'A': '10100011000',
    //     'B': '10001011000',
    //     'C': '10001000110',
    //     'D': '10110001000',
    //     'E': '10001101000',
    //     'F': '10001100010',
    //     'G': '11010001000',
    //     'H': '11000101000',
    //     'I': '11000100010',
    //     'J': '10110111000',
    //     'K': '10110001110',
    //     'L': '10001101110',
    //     'M': '10111011000',
    //     'N': '10111000110',
    //     'O': '10001110110',
    //     'P': '11101110110',
    //     'Q': '11010001110',
    //     'R': '11000101110',
    //     'S': '11011101000',
    //     'T': '11011100010',
    //     'U': '11011101110',
    //     'V': '11101011000',
    //     'W': '11101000110',
    //     'X': '11100010110',
    //     'Y': '11101101000',
    //     'Z': '11101100010'
    //   };

    //   let result = '';
    //   // 起始码 B
    //   result += '11010010000';
      
    //   // 编码数据
    //   for (let i = 0; i < text.length; i++) {
    //     const char = text.charAt(i);
    //     if (CODE128_CHARS[char]) {
    //       result += CODE128_CHARS[char];
    //     }
    //   }
      
    //   // 校验码
    //   let checksum = 104; // 起始码 B 的值
    //   for (let i = 0; i < text.length; i++) {
    //     const char = text.charAt(i);
    //     const value = Object.keys(CODE128_CHARS).indexOf(char);
    //     checksum += value * (i + 1);
    //   }
    //   checksum = checksum % 103;
      
    //   // 添加校验码
    //   result += Object.values(CODE128_CHARS)[checksum];
      
    //   // 结束码
    //   result += '1100011101011';
      
    //   return result;
    // },
  }
}

</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  min-height: 100vh;
  background: url('https://img.songlei.com/live/pintu/act-deesc-bg.png') no-repeat center center;
  background-size: 100% 100%;
}
.no-data{
  display: block;
  width: 300rpx;
  height: 304rpx;
  margin: 300rpx auto 0;
}

.audit{
  width: 150rpx;
  height: 70rpx;
  line-height: 70rpx;
  background: #868ced;
  color: #fff;
  border-radius: 40rpx;
  text-align: center;
  margin: 3ch 0 0 auto;
}
.prize-list {
  .prize-item {
    width: 100%;
    background: linear-gradient(to right, #bcc6ff, #f8fafc);
    border-radius: 20rpx;
    color: #4a24d7;
    box-shadow: 0 3rpx 5rpx #4a24d7;
    .prize-img {
      width: 120rpx;
      height: 120rpx;
      border: 1rpx solid #9377f9;
      border-radius: 20rpx;
    }
  }
}

.audit-box{
  .audit-box-dialog{
    background: linear-gradient(to right, #d9dcfa, #f8fafc);
    box-shadow: 0 6rpx 10rpx #8c8be6;
    .title{
      width: 180rpx;
      height: 60rpx;
      line-height: 60rpx;
      text-align: center;
      background: #868ced;
      color: #fff;
      border-radius: 40rpx;
    }

  }
}
</style>