<template>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">拼团详情</block>
    </cu-custom>
    <view class="cu-card article">
      <view class="cu-item">
        <navigator
          class="padding-sm"
          hover-class="none"
          :url="'/pages/shop/shop-detail/index?id=' + grouponInfo.shopInfo.id"
        >
          <view
            class="cu-avatar sm radius"
            :style="'background-image:url(' + grouponInfo.shopInfo.imgUrl + ')'"
          ></view>
          <text class="text-black text-bold margin-left-sm">{{grouponInfo.shopInfo.name}}</text>
          <text class="cuIcon-right text-sm"></text>
        </navigator>
        <view>
          <view
            class="action text-sm"
            style="margin-left: 30rpx;"
          >{{grouponInfo.name}}</view>
          <view style="margin:10rpx 0 10rpx 30rpx;">
            <text class="cu-tag line-orange radius margin-right-sm">{{grouponInfo.grouponNum}}人团</text>
            <text
              class="text-sm text-gray"
              v-if="grouponInfo.launchNum"
            >已有{{grouponInfo.launchNum}}人参与</text>
            <text
              class="text-blue text-sm margin-left"
              style="margin-left: 280rpx;"
              @tap="ruleShow"
            >拼团规则</text>
          </view>
        </view>
        <view class="content">
          <image
            :src="grouponInfo.picUrl"
            mode="aspectFill"
            class="row-img margin-top-xs"
          ></image>
          <view class="desc row-info block">
            <view class="text-black margin-top-sm overflow-2">{{grouponInfo.goodsSpu.name}}</view>
            <view
              class="text-gray text-sm overflow-1"
              v-if="grouponInfo.goodsSpu&&grouponInfo.goodsSku.specs&&grouponInfo.goodsSku.specs.length > 0"
            >{{specInfo}}</view>
            <view class="flex justify-start margin-top-sm align-center">
              <view class="text-price text-bold text-xl text-red">{{grouponUser.grouponPrice}}</view>
              <view class="text-price text-decorat text-gray margin-left-sm">{{grouponInfo.goodsSku.salesPrice}}</view>
              <view
                class="cu-tag bg-red radius sm margin-left"
                v-if="grouponInfo.goodsSpu.freightTemplat&&grouponInfo.goodsSpu.freightTemplat.type == '2'"
              >包邮</view>
            </view>
          </view>
        </view>
        <view class="padding-lr text-center margin-top-sm">
          <!-- 没参与 -->
          <view v-if="!grouponUser.grouponUser">
            <button
              class="cu-btn shadow-blur round lg"
              :class="'bg-'+theme.themeColor"
              v-if="grouponInfo.enable == '1' && grouponUser.status == '0'"
              @tap="toGroupon"
            ><text class="cuIcon-cardboardforbid">参与拼团</text></button>
            <button
              class="cu-btn shadow-blur round lg bg-gray"
              v-if="grouponInfo.enable == '0'"
            ><text class="cuIcon-cardboardforbid">该拼团活动已关闭</text></button>
            <button
              class="cu-btn shadow-blur round lg"
              :class="'bg-'+theme.themeColor"
              v-if="grouponUser.status == '1'"
            ><text class="cuIcon-cardboardforbid">拼团完成</text></button>
            <button
              class="cu-btn shadow-blur round bg-gray lg"
              v-if="grouponUser.status == '2'"
            ><text class="cuIcon-close">活动已过期</text></button>
            <view class="text-gray text-sm margin-top">{{grouponUser.validBeginTime}}至{{grouponUser.validEndTime}}</view>
          </view>
          <!-- 已经参与 -->
          <view v-if="grouponUser.grouponUser">
            <button
              class="cu-btn shadow-blur round lg"
              :class="'bg-'+theme.themeColor"
              v-if="grouponUser.status == '0'"
              @tap="shareShowFun"
            ><text class="cuIcon-friend">邀请好友拼团</text></button>
            <button
              class="cu-btn shadow-blur round bg-orange lg"
              v-if="grouponUser.status == '1'"
            ><text class="cuIcon-check">已完成拼团</text></button>
            <button
              class="cu-btn shadow-blur round bg-gray lg"
              v-if="grouponUser.status == '2'"
            ><text class="cuIcon-close">该拼团已过期</text></button>
            <view class="text-gray text-sm margin-top">{{grouponUser.validBeginTime}}至{{grouponUser.validEndTime}}</view>
          </view>
        </view>
      </view>
    </view>

    <view
      class="cu-list menu card-menu"
      v-if="grouponUserList.length > 0"
    >
      <view
        class="cu-item"
        v-if="grouponUser.status == '0'"
      >
        <view class="content text-bold">倒计时：<count-down
            class="text-red"
            :outTime="dateUtil.getOutTime(grouponUser.validEndTime)"
            @countDownDone="countDownDone"
          ></count-down>
        </view>
      </view>

      <view class="cu-item">
        <view class="content margin-tb-sm text-center">
          <view
            class="cu-avatar round lg margin-xs text-yellow groupon-user"
            :style="'background-image:url(' + grouponUser.headimgUrl + ');'"
          >
            <view class="cu-tag badge bg-yellow">团长</view>
          </view>
          <view
            class="cu-avatar round lg margin-xs text-yellow groupon-user"
            v-for="(item, index) in grouponUserList"
            :key="index"
            v-if="item.id != grouponUser.id"
            :style="'background-image:url(' + item.headimgUrl + ');'"
          ></view>
          <view
            class="cu-avatar round lg margin-xs groupon-user-no"
            v-for="(item, index) in grouponInfo.grouponNum - grouponUserList.length"
            :key="index"
          >
            <text>?</text>
          </view>
        </view>
      </view>
    </view>

    <view
      class="cu-list menu card-menu"
      v-if="grouponUser.grouponUser"
    >
      <view class="cu-item flex response">
        <view class="flex-twice text-df">
          <view class="text-gray margin-left-sm">拼团时间</view>
        </view>
        <view class="flex-treble text-df">
          <text class="text-black">{{grouponUser.grouponUser.createTime}}</text>
        </view>
        <view class="flex-sub text-df text-gray text-right">
          <text class="margin-right-sm"></text>
        </view>
      </view>
      <navigator
        :url="'/pages/order/order-detail/index?id=' + grouponUser.grouponUser.orderId"
        hover-class="none"
        class="cu-item flex response"
      >
        <view class="flex-twice text-df">
          <view class="text-gray margin-left-sm">订单详情</view>
        </view>
        <view class="flex-treble text-df"></view>
        <view class="flex-twice text-df text-gray text-right">点击查看<text class="cuIcon-right margin-right-sm"></text>
        </view>
      </navigator>
    </view>

    <view class="cu-card">
      <view class="cu-item">
        <view class="cu-bar bg-white">
          <view class="content">商品信息</view>
        </view>
        <view class="bg-white">
          <jyf-parser :html="article_description"></jyf-parser>
        </view>
        <view class="cu-load bg-gray">已经到底啦...</view>
      </view>
    </view>
    <view :class="'cu-modal ' + modalRule">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">规则说明</view>
          <view
            class="action"
            @tap="ruleHide"
          >
            <text class="cuIcon-close text-red"></text>
          </view>
        </view>
        <view class="padding-lr-xl padding-tb-sm text-left">
          <text>{{grouponInfo.grouponRule}}</text>
        </view>
      </view>
    </view>
    <!-- 分享组件 -->
    <share-component
      v-model="showShare"
      :shareParams="shareParams"
    ></share-component>
  </view>
</template>

<script>
import shopInfo from "components/shop-info/index";
import util from '@/utils/util'
const {
  base64src
} = require("utils/base64src.js");
const app = getApp();
import api from 'utils/api'
import dateUtil from 'utils/dateUtil.js'
import jweixin from 'utils/jweixin'

import countDown from "components/count-down/index";
import shareComponent from "@/components/share-component/index"

export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      dateUtil: dateUtil,
      page: {
        searchCount: false,
        current: 1,
        size: 100,
        ascs: '',
        //升序字段
        descs: 'create_time'
      },
      parameter: {},
      grouponUser: {
        grouponNum: 0,
        validBeginTime: '',
        validEndTime: ''
      },
      grouponUserList: [],
      grouponInfo: {
        launchNum: 0,
        shopInfo: {

        },
        goodsSpu: {

        },
        goodsSku: {

        }
      },
      shareShow: '',
      curLocalUrl: '',
      userInfo: null,
      modalRule: '',
      id: "",
      specInfo: "",
      loadmore: false,
      posterUrl: "",
      posterShow: false,
      posterConfig: "",
      article_description: "",
      showShare: false,
      shareParams: {}
    };
  },

  components: {
    shareComponent,
    countDown,
  },
  props: {},

  onShow () {
    app.initPage().then(res => {
      this.grouponUserList = [];
      this.grouponUserGet();
      this.grouponUserPage();
    });
  },

  onLoad (options) {
    let id;
    if (options.scene) {
      //接受二维码中参数
      let scenes = decodeURIComponent(options.scene).split('&');
      id = scenes[0];
    } else {
      id = options.id;
    }
    this.id = id;
    this.userInfo = uni.getStorageSync('user_info');
  },

  onShareAppMessage: function () {
    let grouponInfo = this.grouponInfo;
    let title = grouponInfo.shareTitle;
    let imageUrl = grouponInfo.picUrl + '-jpg_w360_q90';
    let path = '';
    path = '/pages/groupon/groupon-user-detail/index?id=' + this.grouponUser.id;
    const userInfo = uni.getStorageSync('user_info')
    const userCode = userInfo ? '&sharer_user_code=' + userInfo.userCode : ''
    path = path + userCode;

    return {
      title: title,
      path: path,
      imageUrl: imageUrl,
      success: function (res) {
        if (res.errMsg == 'shareAppMessage:ok') {
          console.log(res.errMsg);
        }
      },
      fail: function (res) { // 转发失败
      }
    };
  },
  methods: {
    //查询拼团记录信息
    grouponUserGet () {
      api.grouponUserGet(this.id).then(res => {
        let grouponUser = res.data;
        let grouponInfo = grouponUser.grouponInfo;
        let goodsSku = grouponInfo.goodsSku;
        let specInfo = '';
        goodsSku.specs.forEach(function (specItem, index) {
          specInfo = specInfo + specItem.specValueName;
          if (goodsSku.specs.length != index + 1) {
            specInfo = specInfo + ';';
          }
        });
        if (grouponInfo.goodsSpu) {
          //html转wxml
          //WxParse.wxParse('description', 'html', grouponInfo.goodsSpu.description, this, 0)
          let that = this;
          this.$nextTick(function () {
            that.article_description = grouponInfo.goodsSpu.description;
          })
        }
        this.grouponUser = grouponUser;
        grouponInfo.shopInfo = grouponInfo.shopInfo ? grouponInfo.shopInfo : {}
        grouponInfo.goodsSpu = grouponInfo.goodsSpu ? grouponInfo.goodsSpu : {}
        grouponInfo.goodsSku = grouponInfo.goodsSku ? grouponInfo.goodsSku : {}
        this.grouponInfo = grouponInfo;
        this.specInfo = specInfo;
      });
    },

    grouponUserPage () {
      api.grouponUserPage(Object.assign({
        groupId: this.id
      }, this.page, util.filterForm(this.parameter))).then(res => {
        let grouponUserList = res.data.records;
        this.grouponUserList = [...this.grouponUserList, ...grouponUserList];
        if (grouponUserList.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },

    //参与拼团
    toGroupon (e) {
      let grouponInfo = this.grouponInfo;
      let goodsSpu = grouponInfo.goodsSpu;
      let goodsSku = grouponInfo.goodsSku;

      if (goodsSku.stock > 0) {
        /* 把参数信息异步存储到缓存当中 */
        uni.setStorage({
          key: 'param-orderConfirm',
          data: [{
            spuId: goodsSpu.id,
            skuId: goodsSku.id,
            quantity: 1,
            salesPrice: grouponInfo.grouponPrice,
            spuName: goodsSpu.name,
            specInfo: this.specInfo,
            picUrl: goodsSku.picUrl ? goodsSku.picUrl : goodsSpu.picUrls[0],
            freightTemplat: goodsSpu.freightTemplat,
            weight: goodsSku.weight,
            volume: goodsSku.volume,
            orderType: '2',
            marketId: grouponInfo.id,
            relationId: this.grouponUser.id,
            shopInfo: grouponInfo.shopInfo
          }]
        });
        uni.navigateTo({
          url: '/pages/groupon/groupon-order-confirm/index'
        });
      } else {
        uni.showToast({
          title: '拼团商品库存不足',
          icon: 'none',
          duration: 2000
        });
      }
    },

   async shareShowFun () {
      let desc = '长按识别小程序码';
      let shareImg = this.grouponInfo.picUrl;
      // #ifdef H5 || APP-PLUS
      desc = '长按识别二维码';
      // h5的海报分享的图片有的有跨域问题，所以统一转成base64的
      // 之所以不在组件里面转换是因为无法区分哪张image图片需要处理，一般处理主图
      shareImg = await util.imgUrlToBase64(shareImg);
      // #endif
      //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
      let posterConfig = {
        width: 750,
        height: 1280,
        backgroundColor: '#fff',
        debug: false,
        blocks: [{
          width: 690,
          height: 808,
          x: 30,
          y: 183,
          borderWidth: 2,
          borderColor: '#f0c2a0',
          borderRadius: 20
        }, {
          width: 634,
          height: 74,
          x: 59,
          y: 770,
          backgroundColor: '#fff',
          opacity: 0.5,
          zIndex: 100
        }],
        texts: [{
          x: 30,
          y: 113,
          baseLine: 'top',
          text: this.grouponInfo.shareTitle,
          fontSize: 38,
          color: '#080808'
        }, {
          x: 92,
          y: 810,
          fontSize: 38,
          baseLine: 'middle',
          text: this.grouponInfo.goodsSpu.name,
          width: 570,
          lineNum: 1,
          color: '#080808',
          zIndex: 200
        }, {
          x: 59,
          y: 895,
          baseLine: 'middle',
          text: [{
            text: '拼团价',
            fontSize: 28,
            color: '#ec1731'
          }, {
            text: '¥' + this.grouponInfo.grouponPrice,
            fontSize: 36,
            color: '#ec1731',
            marginLeft: 30
          }]
        }, {
          x: 522,
          y: 895,
          baseLine: 'middle',
          text: '原价 ¥' + this.grouponInfo.goodsSku.salesPrice,
          fontSize: 28,
          color: '#929292'
        }, {
          x: 59,
          y: 945,
          baseLine: 'middle',
          text: [{
            text: this.grouponInfo.goodsSpu.sellPoint,
            fontSize: 28,
            color: '#929292',
            width: 570,
            lineNum: 1
          }]
        }, {
          x: 360,
          y: 1065,
          baseLine: 'top',
          text: desc,
          fontSize: 38,
          color: '#080808'
        }, {
          x: 360,
          y: 1123,
          baseLine: 'top',
          text: '快来和我一起拼一单吧！',
          fontSize: 28,
          color: '#929292'
        }],
        images: [{
          width: 634,
          height: 634,
          x: 59,
          y: 210,
          url: shareImg
        }, {
          width: 230,
          height: 230,
          x: 92,
          y: 1020,
          url: null,
          qrCodeName: 'qrCodeName',// 二维码唯一区分标识
        }]
      };
      let userInfo = uni.getStorageSync('user_info');

      if (userInfo && userInfo.headimgUrl) {
        //如果有头像则显示
        posterConfig.images.push({
          width: 62,
          height: 62,
          x: 30,
          y: 30,
          borderRadius: 62,
          url: userInfo.headimgUrl
        });
        posterConfig.texts.push({
          x: 113,
          y: 61,
          baseLine: 'middle',
          text: userInfo.nickName,
          fontSize: 32,
          color: '#8d8d8d'
        });
      }

      this.shareParams = {
        title: this.grouponInfo.shareTitle,
        desc: this.grouponInfo.goodsSpu.name,
        imgUrl: this.grouponInfo.picUrl,
        scene: this.grouponUser.id,
        page: 'pages/groupon/groupon-user-detail/index',
        posterConfig: posterConfig
      }
      this.showShare = true;
    },
    ruleShow () {
      this.modalRule = 'show';
    },

    ruleHide () {
      this.modalRule = '';
    },

    countDownDone () {
      this.onLoad();
    }

  }
};
</script>
<style>
.row-img {
  width: 200rpx !important;
  height: 200rpx !important;
  border-radius: 10rpx;
}

.cu-bar.tabbar.shop .action {
  width: unset;
}

.cu-avatar {
  display: inline-flex !important;
}

.groupon-user {
  border-style: solid;
}

.groupon-user-no {
  border-style: dotted;
  background-color: #fff;
  color: #ccc;
}

.cu-btn {
  width: 100%;
  height: 76rpx;
}
</style>
