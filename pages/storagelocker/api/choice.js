import {
  requestApi as request
} from '@/utils/api.js'

//获取寄存柜大中小价格
export const getEquipmentByNo = (data) => {
  return request({
    url: '/mallapi/lockerequipment/getEquipmentByNo',
    method: 'get',
	  data
  })
}

//获取附近寄存柜
export const getLockerequipmentPage = (data) => {
  return request({
    url: '/mallapi/lockerequipment/page',
    method: 'get',
	  data
  })
}

//寄存柜开柜
export const getLockerorder = (data) => {
  return request({
    url: '/mallapi/lockerorder',
    method: 'post',
	  data
  })
}

// 查询箱子状态信息
export const getStateByNo = (data) => {
  return request({
    url: '/mallapi/lockerequipment/getStateByNo',
    method: 'get',
	  data
  })
}

//获取门店分页列表
export const getStoreinfoPage = (data) => {
  return request({
    url: '/mallapi/storeinfo/page',
    method: 'get',
	  data
  })
}

// 自选柜数据
export const getAavailableBoxes = (data) => {
  return request({
    url: '/mallapi/lockerequipment/getAavailableBoxes',
    method: 'get',
	  data
  })
}