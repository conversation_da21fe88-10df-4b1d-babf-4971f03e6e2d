import {
  requestApi as request
} from '@/utils/api.js'

// 获取订单数据
export const getorderPage = (data) => {
  return request({
    url: '/mallapi/lockerorder/page',
    method: 'get',
	  data
  })
}
// 预下单
export const preOrder = (data) => {
  return request({
    url: '/mallapi/lockerorder/orderGenerate',
    method: 'post',
	  data
  })
}

// 生成订单
export const generateOrders = (data) => {
  return request({
    url: '/mallapi/lockerorder/payLockerOrder-zt',
    method: 'post',
	  data
  })
}

