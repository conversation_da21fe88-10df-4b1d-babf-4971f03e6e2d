<template>
  <view>
    <cu-custom :isBack="true" bgImage="https://img.songlei.com/storagelocker/bg-1.png" :hideMarchContent="true">
			<block slot="content" style="color:#000000;">管理员操作页面</block>
		</cu-custom>
    <view class="wrapper">
      <image class="bg-img" src="https://img.songlei.com/storagelocker/bg-2.png" />
      <view class="content">
        <view class="box">
          <view class="form">
            <view class="flex align-center justify-between">
              <view class="label margin-right-sm">顾客手机号</view>
              <input class="input" v-model="form.phone" type="text" placeholder="请输入顾客手机号" />
            </view>
            <view class="flex margin-top-sm align-center justify-between">
              <view class="label margin-right-sm">柜组编号</view>
              <input class="input" v-model="form.equipmentNo" type="text" placeholder="请输入柜组编号" />
            </view>
            <view class="flex margin-top-lg align-center justify-around">
              <view class="btn" @click="getlockerCabinetExistList">快速查询</view>
              <view class="btn" @click="resetForm">重置</view>
            </view>
          </view>
          <template v-if="cabinetList.length">
            <view class="margin-top-xl" v-for="(item, index) in cabinetList" :key="index">
              <view class="flex align-center justify-between margin-bottom-sm">
                <view 
                  class="open-btn" 
                  style="margin-left: 0; border-radius: 15rpx; padding: 10rpx 30rpx;" 
                  @click="openAllLocker(item)"
                >打开所有已寄存柜</view>
                <view>柜组编号：{{ item.equipmentNo }}</view>
              </view>
              <!-- 表格 -->
              <table class="table" border stripe emptyText="暂无更多数据" >
                <!-- 表头行 -->
                <tr class="tr">
                  <th class="td" align="center" width="120">
                    <view>寄存中柜子编号</view>
                  </th>
                  <th class="td" align="center" width="100">
                    <view>客户电话</view>
                  </th>
                  <th class="td" align="center" width="100">
                    <view>操作</view>
                  </th>
                </tr>
                <!-- 表格数据行 -->
                <tr class="tr" v-for="(table, tableIndex) in item.lockerUserCabinetVOList" :key="tableIndex">
                  <td class="td" align="center">
                    <view>{{ table.cabinetCode }}</view>
                  </td>
                  <td class="td" align="center">
                    <view>{{ table.phone }}</view>
                  </td>
                  <td class="td" align="center">
                    <view class="open-btn" @click="openLocker(table, item.equipmentNo)">开柜</view>
                  </td>
                </tr>
              </table>
            </view>
          </template>
          <view v-else-if="noData" class="margin-top-xl">
            <image src="https://img.songlei.com/storagelocker/no-data.png" class="no-data" />
          </view>
        </view>

        <view class="footer">
          <view class="btn" @click="logout">退出登录</view>
        </view>
      </view>
    </view>

    <view
      class="cu-modal"
      :class="modalSku ? 'show' : ''"
      catchtouchmove="touchMove"
      @tap="modalSku = ''"
    >
      <view
        class="dialog-tips cu-dialog bg-white"
        @tap.stop
      >
        <view class="text-xml text-center text-black">{{ isCabinet ? '您不是管理员' : '柜门已开' }}</view>
        <view class="btn-tips" @tap="toPage">我知道了</view>
      </view>
    </view>
  </view>
</template>
<script>
const app = getApp();
import api from 'utils/api'
// #ifdef APP-PLUS
import { deletePushAlias } from '@/api/push';
// #endif
import { getlockerCabinetExistList, openLocker } from './api/administrator.js';
import util from 'utils/util'

export default {
  data() {
    return {
      modalSku: false,
      form: {
        phone: '',
        equipmentNo: ''
      },
      cabinetList:  [],
      isCabinet: false,
      noData: false
    }
  },
  onShow() {
    app.initPage().then(res => {
      if (!util.isUserLogin()) {
        const pages = getCurrentPages()
        const url = pages[pages.length - 1]['$page']['fullPath']
        uni.reLaunch({
          url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
        })
      }
    });
  },
  methods: {
    // 重置表单
    resetForm() {
      this.form = {
        phone: '',
        equipmentNo: ''
      }
    },
    // 获取柜子列表
    getlockerCabinetExistList() {
      getlockerCabinetExistList({
        phone: this.form.phone,
        equipmentNo: this.form.equipmentNo
      }).then(res => {
        this.noData = false
        if (typeof res.data === 'boolean') {
          this.modalSku = true
          this.isCabinet = true
        } else {
          this.cabinetList = res.data || []
          this.isCabinet = false
          if (this.cabinetList.length === 0) {
            this.noData = true
          }
        }
      })
    },
    // 开柜
    openLocker(row, equipmentNo) {
      openLocker([{
        cabinetCode: row.cabinetCode,
        equipmentNo: equipmentNo,
        orderId: row.orderId
      }]).then(res => {
        if (res.data.code === 0) {
          this.modalSku = true
          this.isCabinet = false
        }
      })
    },
    // 批量开柜
    openAllLocker(item) {
      const params = []
      item.lockerUserCabinetVOList.forEach(row => {
        params.push({
          cabinetCode: row.cabinetCode,
          equipmentNo: item.equipmentNo,
          orderId: row.orderId
        })
      })
      openLocker(params).then(res => {
        if (res.data.code === 0) {
          this.modalSku = true
          this.isCabinet = false
        }
      })
    },
    // 弹框 我知道了
    toPage() {
      uni.reLaunch({
        url: '/pages/home/<USER>'
      }); 
      this.modalSku = false
    },
    // 退出登录
    logout() {
      uni.showModal({
        content: '确定退出登录吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',
        success(res) {
          if (res.confirm) {
            api.logout().then(res => {
              let userInfo = res.data;
              uni.setStorageSync('user_info', userInfo);
              if (userInfo) {
                uni.setStorageSync('third_session', userInfo.thirdSession);
              }
              // #ifdef APP-PLUS
              deletePushAlias();
              // #endif
              //退出登录完成跳到首页
              uni.reLaunch({
                url: '/pages/home/<USER>'
              });
              //清空购物车数量
              app.globalData.shoppingCartCount = '0';
              uni.$emit("updateCart");
            });
          }
        }
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.bg-img {
  width: 100%;
  height: 331rpx;
  position: absolute;
  z-index: 0;
}
.content {
  padding: 30rpx 18rpx 0;
  position: relative;
  z-index: 10;
}

.box{
  width: 100%;
  margin: 0 auto;
  background: #fff;
  border-radius: 60rpx;
  padding: 40rpx;
  .no-data{
    display: block;
    width: 200rpx;
    height: 204rpx;
    margin: 0 auto;
  }
  .form{
    .label{
      width: 136rpx;
      text-align: right;
    }
    .input{
      width: 484rpx;
      height: 80rpx;
      background: #F8F8F8;
      border-radius: 16rpx;
      padding: 0 30rpx;
    }
    .btn{
      width: 286rpx;
      height: 77rpx;
      background: #4A71FE;
      border-radius: 39rpx;
      text-align: center;
      line-height: 77rpx;
      color: #fff;
      font-size: 28rpx;
    }
  }
  .table{
    border: 1rpx solid #f1f1f1;
    box-sizing: border-box;
    .tr{
      display: flex;
      border-bottom: 1rpx solid #f1f1f1;
      align-items: center;
      box-sizing: border-box;
      &:last-child{
        border-bottom: none;
      }
      .td{
        border-right: 1rpx solid #f1f1f1;
        flex: 1;
        text-align: center;
        box-sizing: border-box;
        &:last-child{
          border-right: none;
        }
        view{
          margin: 20rpx 10rpx;
        }
      }
    }
  }

  .open-btn{
    padding: 6rpx 16rpx;
    background: #4A71FE;
    border-radius: 42rpx;
    text-align: center;
    line-height: 64rpx;
    color: #fff;
    font-size: 28rpx;
    margin: 0 auto;
  }
}

.footer{
  margin: 100rpx auto 0;
  .btn{
    width: 336rpx;
    height: 100rpx;
    background: #D0D5E2;
    border-radius: 50rpx;
    font-size: 28rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 100rpx;
    margin: 34rpx auto 0;
  }
}

.dialog-tips{
  padding: 60rpx 40rpx;
  .btn-tips{
    width: 313rpx;
    height: 84rpx;
    background: #4A71FE;
    border-radius: 52rpx;
    font-size: 32rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 84rpx;
    margin: 53rpx auto 0;
  }
}
</style>
