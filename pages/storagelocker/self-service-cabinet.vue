<template>
  <view>
    <cu-custom :isBack="true" bgImage="https://img.songlei.com/storagelocker/bg-1.png" :hideMarchContent="true">
      <block slot="content" style="color:#000000;">自选柜</block>
    </cu-custom>
    <view class="wrapper">
      <image class="bg-img" src="https://img.songlei.com/storagelocker/bg-2.png" />
      <!-- 无柜组可选 -->
      <view class="content">
        <view class="box">
          <view class="text-xdf text-black text-bold text-center">以下标蓝色的为空闲寄存柜，请选择</view>
          <view class="flex justify-around margin-top align-center padding-lr-lg">
            <view class="small-scale flex align-center">
              <view class="small-scale-blue margin-right-sm"></view>
              <view class="text-xsm text-black">空闲</view>
            </view>
            <view class="small-scale flex align-center">
              <view class="small-scale-gray margin-right-sm"></view>
              <view class="text-xsm text-black">满仓不可用</view>
            </view>
          </view>
          <view class="locker-container">
            <!-- 使用flex实现 -->
            <view class="locker-row">
              <!-- Col 1 -->
              <view class="locker-col">
                <view 
                  v-for="(item, index) in lockers.col1" 
                  :key="index"
                  :class="['locker-item', 'item-' + item.size, item.status === 1 ? 'bg-blue' : 'bg-gray']"
                  @click="openabinet(item)"
                >
                  <view 
                    class="inner flex flex-direction align-center " 
                    :style="{'color': item.status === 1 ? '#fff' : '#B3B3B3'}" 
                    :class="[{ 'active': selectedLockerId === item.id}]"
                  >
                    <view class="text-sm">{{ item.text }}</view>
                    <!-- <view class="text-sm" v-if="item.status === 1">空闲</view> -->
                  </view>
                </view>
              </view>
              <!-- Col 2 -->
              <view class="locker-col">
                <view 
                  v-for="(item, index) in lockers.col2" 
                  :key="index"
                  :class="['locker-item', 'item-' + item.size, item.status === 1 ? 'bg-blue' : 'bg-gray']"
                  @click="openabinet(item)"
                >
                  <view 
                    class="inner flex-direction align-center" 
                    :style="{'color': item.status === 1 ? '#fff' : '#B3B3B3'}" 
                    :class="[{ 'active': selectedLockerId === item.id }]"
                  >
                    <view class="text-sm">{{ item.text }}</view>
                    <!-- <view class="text-sm" v-if="item.status === 1">空闲</view> -->
                  </view>
                </view>
              </view>
              <!-- Col 3 -->
              <view class="locker-col">
                <view 
                  v-for="(item, index) in lockers.col3" 
                  :key="index"
                  :class="['locker-item', 'item-' + item.size, item.status === 1 ? 'bg-blue' : 'bg-gray']"
                  @click="openabinet(item)"
                >
                  <view 
                    class="inner flex-direction align-center" 
                    :style="{'color': item.status === 1 ? '#fff' : '#B3B3B3'}" 
                    :class="[{ 'active': selectedLockerId === item.id }]"
                  >
                    <view class="text-sm">{{ item.text }}</view>
                    <!-- <view class="text-sm" v-if="item.status === 1">空闲</view> -->
                  </view>
                </view>
              </view>
              <!-- Col 4 -->
              <view class="locker-col">
                <view 
                  v-for="(item, index) in lockers.col4" 
                  :key="index"
                  :class="['locker-item', 'item-' + item.size, item.status === 1 ? 'bg-blue' : 'bg-gray']"
                  @click="openabinet(item)"
                >
                  <view v-if="item.id == 'BOX0-1'" class="screen"></view>
                  <view 
                    v-else 
                    class="inner flex-direction align-center" 
                    :style="{'color': item.status === 1 ? '#fff' : '#B3B3B3'}" 
                    :class="[{ 'active': selectedLockerId === item.id }]"
                  >
                    <view class="text-sm">{{ item.text }}</view>
                    <!-- <view class="text-sm" v-if="item.status === 1">空闲</view> -->
                  </view>
                </view>
              </view>
              <!-- Col 5 -->
              <view class="locker-col">
                <view 
                  v-for="(item, index) in lockers.col5" 
                  :key="index"
                  :class="['locker-item', 'item-' + item.size, item.status === 1 ? 'bg-blue' : 'bg-gray']"
                  @click="openabinet(item)"
                >
                  <view 
                    class="inner flex-direction align-center" 
                    :style="{'color': item.status === 1 ? '#fff' : '#B3B3B3'}" 
                    :class="[{ 'active': selectedLockerId === item.id }]"
                  >
                    <view class="text-sm">{{ item.text }}</view>
                    <!-- <view class="text-sm" v-if="item.status === 1">空闲</view> -->
                  </view>
                </view>
              </view>
              <!-- Col 6 -->
              <view class="locker-col">
                <view 
                  v-for="(item, index) in lockers.col6" 
                  :key="index"
                  :class="['locker-item', 'item-' + item.size, item.status === 1 ? 'bg-blue' : 'bg-gray']"
                  @click="openabinet(item)"
                >
                  <view 
                    class="inner flex-direction align-center" 
                    :style="{'color': item.status === 1 ? '#fff' : '#B3B3B3'}" 
                    :class="[{ 'active': selectedLockerId === item.id }]"
                  >
                    <view class="text-sm">{{ item.text }}</view>
                    <!-- <view class="text-sm" v-if="item.status === 1">空闲</view> -->
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="charge-box">
            <view class="text-xdf text-black text-bold">收费说明：</view>
            <view class="text-xsm text-black margin-top-sm" style="white-space: pre-wrap;">
              小柜：每小时{{ smallFee }}元、中柜：每小时{{ mediumFee }}元、大柜：每小时{{ largeFee }}元
            </view>
            <!-- <view class="text-xsm text-black margin-top-sm" style="white-space: pre-wrap;" v-html="ruleFee"></view> -->
          </view>
        </view>
      </view>
      <view class="submit-btn" :class="{'disabled': !currentLocker}" @click="submit">
        确定
      </view>
    </view>

    <!-- 弹窗 -->
    <view
      class="cu-modal"
      :class="modalSku ? 'show' : ''"
      catchtouchmove="touchMove"
      @tap="modalSku = ''"
    >
      <view
        class="dialog-tips cu-dialog bg-white"
        @tap.stop
      >
        <view class="text-xdf text-center text-black">温馨提示</view>
        <view class="text-xsm text-center text-black margin-top">请选择空闲包柜（蓝色）</view>
        <view class="btn-tips" @tap="modalSku = ''">知道了</view>
      </view>
    </view>
    
  </view>
</template>

<script>
import { getAavailableBoxes, getLockerorder } from './api/choice'
const app = getApp();
import util from 'utils/util'

export default {
  data () {
    return {
      selectedLockerId: -1,
      equipmentNo: '',
      storeId: '',
      floor: '',
      ruleFee: '',
      smallFee: 0,
      mediumFee: 0,
      largeFee: 0,
      modalSku: '',
      lockers: {
        col1: [
          { id: 'BOX001', size: 's', status: 0, text: '001', price: 0 },
          { id: 'BOX002', size: 's', status: 1, text: '002', price: 0 },
          { id: 'BOX003', size: 's', status: 1, text: '003', price: 0 },
          { id: 'BOX004', size: 's', status: 0, text: '004', price: 0 },
          { id: 'BOX005', size: 's', status: 0, text: '005', price: 0 },
          { id: 'BOX006', size: 's', status: 0, text: '006', price: 0 },
        ],
        col2: [
          { id: 'BOX007', size: 's', status: 0, text: '007', price: 0 },
          { id: 'BOX008', size: 's', status: 0, text: '008', price: 0 },
          { id: 'BOX009', size: 's', status: 0, text: '009', price: 0 },
          { id: 'BOX010', size: 's', status: 0, text: '010', price: 0 },
          { id: 'BOX011', size: 's', status: 0, text: '011', price: 0 },
          { id: 'BOX012', size: 's', status: 0, text: '012', price: 0 }
        ],
        col3: [
          { id: 'BOX013', size: 'l', status: 0, text: '013', price: 0 },
          { id: 'BOX014', size: 'l', status: 0, text: '014', price: 0 }
        ],
        col4: [
          { id: 'BOX0-1', size: 'l', status: 0, text: '', price: 0 },
          { id: 'BOX015', size: 'l', status: 0, text: '015', price: 0 }
        ],
        col5: [
          { id: 'BOX016', size: 'm', status: 0, text: '016', price: 0  },
          { id: 'BOX017', size: 'm', status: 1, text: '017', price: 0 },
          { id: 'BOX018', size: 'm', status: 1, text: '018', price: 0 }
        ],
        col6: [
          { id: 'BOX019', size: 'm', status: 0, text: '019', price: 0 },
          { id: 'BOX020', size: 'm', status: 1, text: '020', price: 0 },
          { id: 'BOX021', size: 'm', status: 1, text: '021', price: 0 }
        ]
      },
      currentLocker: null,
    }
  },
  onLoad(options){
    this.equipmentNo = options.equipmentNo
    this.storeId = options.storeId
    this.floor = options.floor
  },
  onShow(){
    app.initPage().then(res => {
      if (util.isUserLogin()) {
        this.getAavailableBoxes()
      } else {
        const pages = getCurrentPages()
        const url = pages[pages.length - 1]['$page']['fullPath']
        uni.reLaunch({
          url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
        })
      }
    });
  },
  methods: {
    getAavailableBoxes(){
      getAavailableBoxes({equipmentNo: this.equipmentNo}).then(res => {
        this.ruleFee = res.data.lockerRule.remark || ''
        this.smallFee = res.data.lockerRule.smallPrice || 0
        this.mediumFee = res.data.lockerRule.mediumPrice || 0
        this.largeFee = res.data.lockerRule.largePrice || 0
        const availBoxes = res.data.availBoxes || []
        // 遍历每一列，更新status和price
        const newLockers = {}
        Object.keys(this.lockers).forEach(colKey => {
          newLockers[colKey] = this.lockers[colKey].map(item => {
            let price = 0
            if (item.size === 's') price = this.smallFee
            else if (item.size === 'm') price = this.mediumFee
            else if (item.size === 'l') price = this.largeFee
            return {
              ...item,
              status: availBoxes.includes(item.id) ? 1 : 0,
              price
            }
          })
        })
        this.lockers = newLockers
      })
    },
    async openabinet(item){
        if (item.status === 0) {
          this.modalSku = true
          this.selectedLockerId = -1
          this.currentLocker = null
          return
        }

        if (item.id == 'BOX0-1') {
          this.selectedLockerId = -1
          this.currentLocker = null
          return
        }

        if (item.status === 1) {
          this.selectedLockerId = item.id
          this.currentLocker = item
        }
    },
    async submit(){
      if (!this.currentLocker) {
        uni.showToast({
          title: '请选择柜子',
          icon: 'none'
        })
        return
      }
      try {
        const parameter = {
          equipmentNo: this.equipmentNo,
          // cabinetType: this.currentLocker.id,
          storeId: this.storeId,
          floor: this.floor,
          cabinetPrice: this.currentLocker.price,
          number: this.currentLocker.id,
        }
        // console.error(parameter)
        const { data, msg } = await getLockerorder(parameter)
        // this.msg = msg || ''
        // if (this.msg) {
        //   this.$nextTick(()=>{
        //     let query = uni.createSelectorQuery()
        //     query.select('#navTab').boundingClientRect() 
        //     query.exec((res) => {
        //       if (res) {
        //         this.navHeight = res[0].height
        //       }
        //     })
        //   })
        //   return
        // }
        // 开柜成功
        uni.navigateTo({
          url: `/pages/storagelocker/success?code=${this.equipmentNo}`
        })
        
      } catch (error) {
        console.error("err",error);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-img {
  width: 100%;
  height: 331rpx;
  position: absolute;
  z-index: 0;
}

.content {
  padding: 30rpx 18rpx 0;
  position: relative;
  z-index: 10;
}

.box {
  width: 100%;
  min-height: 838rpx;
  margin: 10rpx auto 0;
  background: #fff;
  border-radius: 60rpx;
  padding: 40rpx 50rpx;
}

.small-scale-blue {
  width: 76rpx;
  height: 30rpx;
  background: #88DDFF;
  border-radius: 10rpx;
}

.small-scale-gray {
  width: 76rpx;
  height: 30rpx;
  background: #EBEBF0;
  border-radius: 10rpx;
}

.locker-container {
  margin-top: 20rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.locker-row {
  display: flex;
  flex-direction: row;
  gap: 6rpx;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.locker-col {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  justify-content: center;
  align-items: center;
}
.locker-item {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10rpx;
}
.locker-item .inner {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  font-size: 26rpx;
  text-align: center;
  // padding-top: 4rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.locker-item .active {
  border: 4rpx solid #333333;
  box-sizing: border-box;
}
.locker-item .screen {
  width: 98rpx;
  height: 150rpx;
  background: #BEBEBE;
  border: 3px solid #8B8B8B;
  border-radius: 10rpx;
  margin: 33rpx auto;
}
.bg-blue {
  background-color: #88DDFF;
}
.bg-gray {
  background-color: #EBEBF0;
}
.item-s {
  width: 75rpx;
  height: 75rpx;
}
.item-m {
  width: 120rpx;
  height: 154rpx;
}
.item-l {
  width: 120rpx;
  height: 235rpx;
}

.charge-box{
  margin-top: 51rpx;
}

.submit-btn {
  width: 336rpx;
  height: 100rpx;
  background: #4A71FE;
  border-radius: 50rpx;
  margin: 63rpx auto 0;
  font-size: 30rpx;
  color: #FFFFFF;
  line-height: 100rpx;
  text-align: center;
}
.disabled {
  background: #D0D5E2;
}

.dialog-tips{
  padding: 60rpx 40rpx;
  .btn-tips{
    width: 313rpx;
    height: 84rpx;
    background: #4A71FE;
    border-radius: 52rpx;
    font-size: 30rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 84rpx;
    margin: 77rpx auto 0;
  }
}
</style>