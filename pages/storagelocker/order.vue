<template>
	<view>
		<cu-custom :isBack="true" bgImage="https://img.songlei.com/storagelocker/bg-1.png" :hideMarchContent="true">
			<block slot="content" style="color: #000000">我的寄存</block>
		</cu-custom>
		<view class="wrapper">
			<image class="bg-img" src="https://img.songlei.com/storagelocker/bg-2.png" />
			<view class="content">
				<view class="box" v-if="noData">
					<image src="https://img.songlei.com/storagelocker/no-data.png" class="no-data" />
				</view>
				<template v-else>
					<view
						class="box"
						v-for="(item, index) in orderData"
						:key="index"
					>
						<view class="flex align-center justify-between text-xsm text-black margin-bottom-sm"  @click="handleShow(item, index)">
							<view>订单编号：{{ item.orderNo }}</view>
							<view style="color: #4a71fe" v-if="item.orderType == 0">寄存中</view>
							<view v-else-if="item.orderType == 1">已完成</view>
							<view v-else-if="item.orderType == 2">已退款</view>
							<view v-else-if="item.orderType == 3">管理员开柜</view>
							<view v-else-if="item.orderType == 4">部分退款</view>
							<!-- v-if="item.orderType != 0 && item.orderType != 3" -->
							<view class="arrow" :class="{ active: item.isShow == 1 }" ></view>
						</view>
						<view class="content-text" :class="{ show: item.isShow == 1 }">
							<view class="text-sm">
								<text class="text-light-gray">寄存柜类型：</text>
								<text v-if="item.cabinetType == 1">大号</text>
								<text v-else-if="item.cabinetType == 2">中号</text>
								<text v-else-if="item.cabinetType == 3">小号</text>
							</view>
							<view class="text-sm margin-top-xs">
								<text class="text-light-gray">收费规则：</text>
								<text>{{ item.cabinetPrice }} 元每小时</text>
							</view>
							<view class="text-sm margin-top-xs">
								<text class="text-light-gray">租借地点：</text>
								<text>{{ item.storeName }}{{ item.floor }}{{ item.position }}</text>
							</view>
							<view class="text-sm margin-top-xs">
								<text class="text-light-gray">租借柜号：</text>
								<text>{{ item.number }}</text>
							</view>
							<view class="text-sm margin-top-xs">
								<text class="text-light-gray">租借时间：</text>
								<text>{{ item.createTime }}</text>
							</view>
							<view class="text-sm margin-top-xs">
								<text class="text-light-gray">租借时长：</text>
								<text>{{ item.rentalTime }}</text>
							</view>
							<view class="text-sm margin-top-xs">
								<text class="text-light-gray">预估费用：</text>
								<text>{{ item.paymentPrice }}</text>
							</view>
							<!-- 管理员已开柜展示的tips -->
							<view class="tips" v-if="item.orderType == 3">备注:按客户要求，已由管理员开柜取出，产生费用{{ item.paymentPrice.toFixed(2) }}元</view>

							<!-- 从【商城我的页面】进入的不展示支付按钮 -->
							<view 
								class="btn" 
								:class="{ show: item.isShow == 1 }" 
								v-if="origins != 'my' && item.orderType == 0" 
								@click="toPage(item.id)"
							>结束寄存</view>
							<image 
								v-else-if="item.orderType == 1" 
								src="https://img.songlei.com/storagelocker/finish.png" 
								class="finish" 
								:class="{ show: item.isShow == 1 }" 
							/>
							<image 
								v-else-if="item.orderType == 2" 
								src="https://img.songlei.com/storagelocker/refunded.png" 
								class="finish" 
								:class="{ show: item.isShow == 1 }" 
							/>
							<!-- 从【商城我的页面】进入的不展示支付按钮 -->
							<view 
								class="btn-pay" 
								v-else-if="origins != 'my' && item.orderType == 3" 
								@click="toPage(item.id)"
							>待支付 {{ item.paymentPrice.toFixed(2) }} 元</view>
							<image 
								v-else-if="item.orderType == 4" 
								src="https://img.songlei.com/storagelocker/partial-refunds.png" 
								mode="widthFix" 
								class="finish" 
								:class="{ show: item.isShow == 1 }"
							/>
						</view>
					</view>
					<view v-if="isLoad" :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
				</template>
			</view>
		</view>
	</view>
</template>
<script>
import { getorderPage } from './api/order.js';
const app = getApp();
import util from 'utils/util';

export default {
	data() {
		return {
			orderData: [],
			origins: '',
			page: {
        current: 1,
        size: 10,
        ascs: '',
        //升序字段
        descs: 'create_time'
      },
			loadmore: true,
			isLoad: false,
			noData: false
		};
	},
	onLoad(options) {
		this.origins = options.origins;
	},
	onShow() {
		app.initPage().then((res) => {
			if (util.isUserLogin()) {
				this.loadmore = true;
				this.orderData = [];
				this.page.current = 1;
				this.getOrderData();
			} else {
				const pages = getCurrentPages();
				const url = pages[pages.length - 1]['$page']['fullPath'];
				uni.reLaunch({
					url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
				});
			}
		});
	},
	onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.getOrderData();
    }
  },
	onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框
    uni.hideNavigationBarLoading(); // 停止下拉动作
    uni.stopPullDownRefresh();
  },
	methods: {
		// 刷新
		refresh () {
			this.isLoad = false;
			this.loadmore = true;
			this.orderData = [];
			this.page.current = 1;
			this.getOrderData();
		},
		// 获取订单数据
		getOrderData() {
			if (this.isLoad) return;
			this.isLoad = true;
			getorderPage({
				...this.page
			}).then((res) => {
				this.isLoad = false;
				const orderList = res.data.records;
				if(this.page.current == 1) {
					this.orderData = orderList;
				}else {
					this.orderData = [...this.orderData, ...orderList];
				}
        if (orderList.length < this.page.size) {
          this.loadmore = false;
					this.isLoad = true;
        }
				// 没有数据
				if(!this.orderData.length) {
					this.noData = true;
					return
				}
				this.orderData.forEach((item, index) => {
					if (item.orderType == 0 || item.orderType == 3) {
						this.$set(this.orderData, index, {
							...this.orderData[index],
							isShow: 1
						});
					} else {
						this.$set(this.orderData, index, {
							...this.orderData[index],
							isShow: 0
						});
					}
				});
			});
		},
		// 跳转页面
		toPage(id) {
			uni.navigateTo({
				url: `/pages/storagelocker/order-confirmation?id=${id}`
			});
		},

		handleShow(item, index) {
			if (this.orderData[index].isShow == 1) {
				this.$set(this.orderData, index, {
					...this.orderData[index],
					isShow: 0
				});
			} else {
				this.$set(this.orderData, index, {
					...this.orderData[index],
					isShow: 1
				});
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.bg-img {
	width: 100%;
	height: 331rpx;
	position: absolute;
	z-index: 0;
}
.content {
	padding: 30rpx 18rpx 0;
	position: relative;
	z-index: 10;
}

.box {
	width: 100%;
	margin: 10rpx auto 0;
	background: #fff;
	border-radius: 60rpx;
	padding: 40rpx;
	position: relative;
	&:first-child {
		margin-top: 0;
	}
	.no-data {
		display: block;
		width: 200rpx;
		height: 204rpx;
		margin: 0 auto;
	}
	.tips {
		width: 646rpx;
		height: 92rpx;
		background: #e9fcff;
		border-radius: 38rpx;
		font-size: 24rpx;
		color: #4a71fe;
		line-height: 92rpx;
		text-align: center;
		margin: 33rpx auto;
	}

	.btn,
	.btn-pay {
		height: 64rpx;
		background: #4a71fe;
		border-radius: 32rpx;
		font-size: 28rpx;
		color: #ffffff;
		text-align: center;
		line-height: 64rpx;
	}
	.btn {
		padding: 0 40rpx;
		position: absolute;
		bottom: 40rpx;
		right: 40rpx;
		max-height: 0;
		overflow: hidden;
		padding: 0 32rpx;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		
		&.show {
			max-height: 1000rpx; /* 根据实际内容调整 */
			padding-bottom: 28rpx;
		}
	}
	.finish {
		position: absolute;
		bottom: 40rpx;
		right: 40rpx;
		width: 188rpx;
		height: 134rpx;
		max-height: 0;
		overflow: hidden;
		padding: 0 32rpx;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		
		&.show {
			max-height: 1000rpx; /* 根据实际内容调整 */
			padding-bottom: 28rpx;
		}
	}
	.btn-pay {
		width: 213rpx;
		margin-left: auto;
	}
}

.content-text {
	max-height: 0;
	overflow: hidden;
	padding: 0 32rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	&.show {
		max-height: 1000rpx; /* 根据实际内容调整 */
		padding-bottom: 28rpx;
	}
}

.dialog-tips {
	padding: 60rpx 40rpx;
	.btn-tips {
		width: 213rpx;
		height: 64rpx;
		background: #4a71fe;
		border-radius: 32rpx;
		font-size: 28rpx;
		color: #ffffff;
		text-align: center;
		line-height: 64rpx;
		margin: 53rpx auto 0;
	}
}

.arrow {
	width: 24rpx;
	height: 24rpx;
	border-left: 4rpx solid #007aff;
	border-bottom: 4rpx solid #007aff;
	transform: rotate(-45deg);
	transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	&.active {
		transform: rotate(135deg);
	}
}
</style>
