<template>
  <view>
    <cu-custom :isBack="true" bgImage="https://img.songlei.com/storagelocker/bg-1.png" :hideMarchContent="true">
			<block slot="content" style="color:#000000;">附近柜组</block>
		</cu-custom>
    <view class="wrapper">
      <image class="bg-img" src="https://img.songlei.com/storagelocker/bg-2.png" />
      <view class="content">
        <view class="box">
          <scroll-view scroll-y @scrolltolower="getMore">
            <view class="item margin-top flex align-center" v-for="(item, index) in listPages" :key="index">
              <image src="https://img.songlei.com/storagelocker/cabinet.png"  class="cabinet"/>
              <view class="flex-sub">
                <view class="text-sm">
                  <text class="text-light-gray">柜组名称：</text>
                  <text>{{item.equipmentName}}</text>
                </view>
                <view class="text-sm margin-top-xs">
                  <text class="text-light-gray">所在楼层：</text>
                  <text>{{item.floor}}</text>
                </view>
                <view class="text-sm margin-top-xs">
                  <text class="text-light-gray">所在位置：</text>
                  <text>{{item.position}}</text>
                </view>
                <view class="text-sm margin-top-xs">
                  <text class="text-light-gray">可用柜组：</text>
                  <text>{{item.availableCabinetNum}}个</text>
                </view>
              </view>
            </view>
            <view v-if="loadmore|| listPages&&listPages.length>0" :class="'cu-load ' + (loadmore ? 'loading' : 'over')"></view>
            <image v-else  src="https://img.songlei.com/storagelocker/no-data.png" class="no-data" />
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
// 获取附近柜子  获取门店分页 
import { getLockerequipmentPage, getStoreinfoPage, } from './api/choice.js';
import distanceUtil from 'utils/distanceUtil';

export default {
  data() {
    return { 
      storelist: [], // 门店列表分页
      selectStore:null, //当前定位信息
      loadmore: true,
      page: {
        current: 1,
        size: 10,
      },
      listPages: [], // 附近寄存柜列表
      storeCode: '', // 门店编码,现在默认写死松雷-南岗店201 松雷-香坊店203 松雷-道里店202
    }
  },
  onShow(){
    this.getstorelist()
  },

  methods: {
    // 获取有寄存柜门店分页列表
    async getstorelist() {
			const { data } = await getStoreinfoPage(this.page)
      this.storelist = data.records;
      console.log("getstorelist=this.storelist=>",this.storelist);
      this.checkPosPermission()
		},

    // 定位兼容
		checkPosPermission() {
			// #ifdef MP-WEIXIN
			this.getCurrentPos();
			// #endif 
			// #ifdef APP-PLUS
			let that = this;
			var platform = uni.getSystemInfoSync().platform;
			if (platform == 'android') {
				plus.android.checkPermission(
					'android.permission.ACCESS_FINE_LOCATION',
					granted => {
						if (granted.checkResult == -1) {
							//弹出
							that.$refs.perpopup.open('top')
						} else {
							//执行你有权限后的方法
							that.getCurrentPos();
						}
					},
					error => {
						console.error('Error checking permission:', error.message);
					}
				);
				plus.android.requestPermissions(['android.permission.ACCESS_FINE_LOCATION'],
					(e) => {
						//关闭
						that.$refs.perpopup.close()
						if (e.granted.length > 0) {
							//执行你有权限后的方法
							that.getCurrentPos();
						}
					})
			} else {
				//执行你有权限后的方法 ios
				that.getCurrentPos();
			}
			// #endif 
		},

    // 定位当前位置
		async getCurrentPos() {
      let that = this;
      uni.getLocation({
        type: 'gcj02',
        success:  (res) => {
          const { latitude, longitude } = res;
          // 测试南岗定位
          // const { latitude, longitude } = {latitude: "+45.765013",longitude: "126.650278"};
          const nearestStore = that.findNearestStore(latitude, longitude);
          console.log("位置==nearDistance==》", nearestStore);
          that.selectStore = nearestStore;
          if (nearestStore) {
            that.storeCode = nearestStore.storeCode
            this.lockerequipmentList()
          }
        },
        fail() {
          // 定位失败
          that.selectStore = null;
        }
      });
    },

    // 查找最近的门店
    findNearestStore(currentLat, currentLng) {
      let nearestStore = null;
      let minDistance = Infinity;
      this.storelist.forEach(store => {
        const distance = distanceUtil.calculateDistance(currentLat, currentLng, store.latitude, store.longitude);
        if (distance < minDistance && distance <= 200 ) { // 只考虑200米内的门店 && distance <= 200 
          minDistance = distance;
          nearestStore = { ...store, distance: Math.round(distance) }; // 将距离添加到门店对象中
        }
      });
      console.log("findNearestStore==>",nearestStore);
      
      return nearestStore || null; // 超出200米返回null
    },

    //附近寄存柜列表
    lockerequipmentList(){
      getLockerequipmentPage(Object.assign({storeCode:this.storeCode},this.page)).then((res)=>{
        console.log("lockerequipmentList==res==>",res);
        if (res.data) {
          if (res.data.records) {
            let listPages=res.data.records
            this.listPages = [...this.listPages, ...listPages];
            if (listPages.length < this.page.size) {
              this.loadmore = false;
            }
          }else{
            this.loadmore = false;
          }
        }
      })
    },

    // 滚动到底部
		getMore() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
        this.lockerequipmentList();
			}
		},
  },
}
</script>
<style lang="scss" scoped>
.bg-img {
  width: 100%;
  height: 331rpx;
  position: absolute;
  z-index: 0;
}
.content {
  padding: 30rpx 18rpx 0;
  position: relative;
  z-index: 10;
}

.box{
  width: 100%;
  margin: 0 auto;
  background: #fff;
  border-radius: 60rpx;
  padding: 40rpx;
  .item:first-child{
    margin-top: 0;
  }
  .no-data{
    display: block;
    width: 200rpx;
    height: 204rpx;
    margin: 0 auto;
  }
  .cabinet{
    width: 196rpx;
    height: 196rpx;
    margin-right: 30rpx;
  }
}
</style>
