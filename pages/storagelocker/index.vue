<template>
	<view>
		<cu-custom :isBack="true" bgImage="https://img.songlei.com/storagelocker/bg-1.png">
		</cu-custom>
		<view class="wrapper">
			<image class="bg-img" src="https://img.songlei.com/storagelocker/bg-2.png" />
			<view class="content">
				<view class="title">
					<view class="text-xml text-black text-bold">松鼠自助存取包</view>
					<view class="text-xdf text-white text-bold">Squirrel self-service storage bag</view>
				</view>
				<view class="box">
					<view class="item flex justify-center align-center" @tap="scanCode">
						<image src="https://img.songlei.com/storagelocker/survive.png" class="icon" />
						<view class="flex-sub">
							<view class="text-xml text-black text-bold">存包</view>
							<view class="text-sm text-black">闭店前一个小时请勿存包</view>
							<view class="text-smx" style="color: #ACACB0">营业时间：09:00-21:30</view>
							<view class="btn">查看</view>
						</view>
					</view>

					<view class="item flex justify-center align-center">
						<image src="https://img.songlei.com/storagelocker/get.png" class="icon" />
						<view class="flex-sub">
							<view class="text-xml text-black text-bold">取包</view>
							<view class="text-sm text-black">请确认您寄存的包裹已取走</view>
							<view class="text-smx" style="color: #ACACB0">营业时间：09:00-21:30</view>
							<view class="btn" @click="toPage(`/pages/storagelocker/order`)">查看</view>
						</view>
					</view>

					<view class="item flex justify-center align-center">
						<image src="https://img.songlei.com/storagelocker/neighbourhood.png" class="icon" />
						<view class="flex-sub">
							<view class="text-xml text-black text-bold">附近柜组</view>
							<view class="text-sm text-black">当前柜组不可用时，请用其它柜组</view>
							<view class="text-smx" style="color: #ACACB0">营业时间：09:00-21:30</view>
							<view class="btn" @click="toPage(`/pages/storagelocker/neighbourhood`)">查看</view>
						</view>
					</view>

					<!-- 广告 -->
					<view class="margin-top-xl" v-if="adList.length">
						<swiper circular autoplay :interval="3000" :duration="500" :indicator-dots="false"
							easing-function="linear" style="width: 714rpx;height: 250rpx; ">
							<swiper-item v-for="(item, index) in adList" :key="index"
								style="border-radius: 20rpx; height: 250rpx; width: 714rpx; "
								@click.stop="toPage(item.skipApplet)">
								<image
									style="width: 100%; height: 300rpx; display: block; margin: auto; border-radius: 20rpx;"
									:src="item.picUrl" mode="aspectFill" />
							</swiper-item>
						</swiper>
					</view>
				</view>
			</view>
			<view class="footer">
				<view v-for="(item, index) in activities" :key="index" :class="activity === item.id ? 'activity' : ''"
					@click="handleClick(item.id, item.route)">
					<image :src="activity === item.id ? item.activeIcon : item.inactiveIcon" class="icon" />
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	// #ifdef APP-PLUS
	const mpaasScanModule = uni.requireNativePlugin("Mpaas-Scan-Module");
	// #endif
	import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js";
	import {
		getAdvertising
	} from './api/advertising.js';
	const app = getApp();

	export default {
		mixins: [navigateUtil],
		data() {
			return {
				activity: 1, // 默认活动状态
				activities: [{
						id: 1,
						activeIcon: "https://img.songlei.com/storagelocker/icon-home-white.png",
						inactiveIcon: "https://img.songlei.com/storagelocker/icon-home.png",
						route: '/pages/storagelocker/index' // 路由地址
					},
					{
						id: 2,
						activeIcon: "https://img.songlei.com/storagelocker/icon-my-white.png",
						inactiveIcon: "https://img.songlei.com/storagelocker/icon-me.png",
						route: '/pages/storagelocker/my' // 路由地址
					}
				],
				adList: []
			};
		},

		onShow() {
			this.activity = 1
			app.initPage().then(res => {
				this.getAdvertising();
			});
		},

		methods: {
			// tab切换
			handleClick(activityId, route) {
				this.activity = activityId; // 更新活动状态
				uni.navigateTo({
					url: route // 路由跳转
				});
			},
			//登录校验
			loopUser(sence) {
				const user_info = uni.getStorageSync("user_info");
				if (!user_info) {
					setTimeout(() => {
						this.loopUser(sence);
					}, 0);
					return;
				}
				const {
					id
				} = user_info;
				if (!id) {
					uni.navigateTo({
						url: "/pages/login/index?reUrl=" +
							encodeURIComponent(
								`/pages/storagelocker/index`
							),
					});
				} else {
					uni.navigateTo({
						url: "/pages/storagelocker/choice?equipmentNo=" + sence
					});
				}
			},

			//扫一扫
			scanCode() {
				let that = this;
				// #ifdef MP-WEIXIN
				// 允许从相机和相册扫码
				uni.scanCode({
					scanType: ["barCode", "qrCode"], //所扫码的类型 barCode	一维码 qrCode	二维码
					success: (res) => {
						if (res.result) {
							const code = res.result; //result	所扫码的内容 测试：6902902006811
							console.log("code", code)
							if (code) {
								console.log("code--->", code)
								// &&code.startsWith("https://shopapi.songlei.com/slshop-h5/ma/giftCardActivation")
								if (code) {
									const id = code
									console.log("scanCode===>", id);

									// const tempUrl =decodeURIComponent(code);
									// //对解码后的参数进行拆分组装，获取要跳转的路径
									// const id = util.UrlParamHash(tempUrl.split('?')[1], 'id');
									// const shopid = util.UrlParamHash(tempUrl.split('?')[1], 'shopid');
									if (id > 0 || id) {
										that.loopUser(id);
									}
									// if(shopid || shopid>0){
									// 	that.shopid = shopid;
									// }

								} else {
									// this.bindCard = code
									uni.showToast({
										title: `该条码不符合要求`,
										icon: "none",
										duration: 2000
									});
								}

							}
						} else {
							console.log('请重新扫描');
							return false;
						}
					},
					fail: (res) => {
						console.log('未识别到二维码');
					}
				})
				// #endif   

				// #ifdef APP-PLUS
				// 允许从相机和相册扫码
				mpaasScanModule.mpaasScan({
					// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
					'scanType': ['qrCode', 'barCode'],
					// 是否隐藏相册，默认false不隐藏
					'hideAlbum': false
				}, (res) => {
					if (res.resp_code == 1000 && res.resp_message == 'success') {
						const code = res.resp_result; //result	所扫码的内容
						if (code) {
							// &&code.startsWith("https://shopapi.songlei.com/slshop-h5/ma/giftCardActivation")
							if (code) {
								const id = code
								console.log("scanCode===>", id);
								// const tempUrl =decodeURIComponent(code);
								// //对解码后的参数进行拆分组装，获取要跳转的路径
								// const id = util.UrlParamHash(tempUrl.split('?')[1], 'id');
								// const shopid = util.UrlParamHash(tempUrl.split('?')[1], 'shopid');
								if (id || id > 0) {
									that.loopUser(id);
								}
								// if(shopid||shopid>0){
								// 	that.shopid = shopid;
								// }

							} else {
								// this.bindCard = code
								uni.showToast({
									title: `该条码不符合要求`,
									icon: "none",
									duration: 2000
								});
							}
						} else {
							uni.showToast({
								title: `未识别到内容`,
								icon: "none",
								duration: 2000
							});
						}
					} else {
						// uni.showToast({
						// 	title: `未识别到信息`,
						// 	icon:"none",
						// 	duration: 2000
						// });
					}
				})
				// #endif   
			},

			// 获取广告
			getAdvertising() {
				getAdvertising({
					type: 3
				}).then(res => {
					console.log('广告', res.data)
					this.adList = res.data || [];
				});
			},

		},
	}
</script>
<style lang="scss" scoped>
	.bg-img {
		width: 100%;
		height: 331rpx;
		position: absolute;
		z-index: 0;
	}

	.content {
		padding: 46rpx 18rpx 0;
		position: relative;
		z-index: 10;

		.title {
			padding-left: 36rpx;
		}
	}

	.box {
		.item {
			width: 100%;
			height: 250rpx;
			background-color: #fff;
			border-radius: 60rpx;
			padding: 40rpx;
			margin-top: 10rpx;

			.icon {
				width: 178rpx;
				height: 178rpx;
				margin-right: 36rpx;
			}

			.btn {
				width: 150rpx;
				height: 64rpx;
				background: #4A71FE;
				border-radius: 32rpx;
				text-align: center;
				line-height: 64rpx;
				color: #fff;
				font-size: 28rpx;
				margin: 14rpx 0 0 auto;
			}
		}
	}

	.footer {
		width: 90%;
		margin: 0 auto;
		height: 140rpx;
		background: #FFFFFF;
		border-radius: 70rpx;
		position: fixed;
		bottom: 30rpx;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10rpx;

		view {
			flex: 1;
			// border: 1px solid #E5E5E5;
			height: 100%;
			border-radius: 62rpx;
		}

		.activity {
			background: #4A71FE;
		}

		.icon {
			width: 50rpx;
			height: 50rpx;
			display: block;
			margin: 36rpx auto;
		}
	}
</style>