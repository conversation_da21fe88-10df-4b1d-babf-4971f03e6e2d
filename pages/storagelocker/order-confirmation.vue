<template>
  <view>
    <cu-custom :isBack="true" bgImage="https://img.songlei.com/storagelocker/bg-1.png" :hideMarchContent="true">
			<block slot="content" style="color:#000000;">订单确认</block>
		</cu-custom>
    <view class="wrapper">
      <image class="bg-img" src="https://img.songlei.com/storagelocker/bg-2.png" />
      <view class="content">
        <!-- <view class="box">
          <image src="https://img.songlei.com/storagelocker/no-data.png" class="no-data" />
        </view> -->
        <view class="box flex justify-between ">
          <image src="https://img.songlei.com/storagelocker/cabinet.png"  class="cabinet"/>
          <view class="flex-sub">
            <view class="text-sm">
              <text class="text-light-gray">收费规则：</text>
              <text>{{ orderInfo.cabinetPrice || 0 }} 元每小时</text>
            </view>
            <view class="text-sm  margin-top-xs">
              <text class="text-light-gray">寄存柜类型：</text>
              <text v-if="orderInfo.cabinetType == 1">大号</text>
              <text v-else-if="orderInfo.cabinetType == 2">中号</text>
              <text v-else-if="orderInfo.cabinetType == 3">小号</text>
            </view>
            <view class="text-sm margin-top-xs">
              <text class="text-light-gray">货柜编号：</text>
              <text>{{ orderInfo.number }}</text>
            </view>
            <view class="text-sm margin-top-xs">
              <text class="text-light-gray">租借地点：</text>
              <text>
                {{ orderInfo.storeName || '' }}{{ orderInfo.floor || '' }}{{ orderInfo.position || '' }}
              </text>
            </view>
            <view class="text-sm margin-top-xs">
              <text class="text-light-gray">租借时间：</text>
              <text>{{ orderInfo.createTime }}</text>
            </view>
            <view class="text-sm margin-top-xs">
              <text class="text-light-gray">归还时间：</text>
              <text>{{ orderInfo.endTime }}</text>
            </view>
            <view class="text-sm margin-top-xs">
              <text class="text-light-gray">使用时间：</text>
              <text>{{ orderInfo.rentalTime }}</text>
            </view>
          </view>
        </view>

        <!-- 支付金额 -->
        <view class="box">
          <view class="flex justify-between align-center">
            <text class="text-xsm text-black text-bold">支付金额：</text>
            <text class="text-xsm text-black">
              ¥{{ orderInfo.paymentPrice ? orderInfo.paymentPrice.toFixed(2) : 0 }}
            </text>
          </view>
          <!-- <view class="flex justify-between align-center margin-top">
            <view>
              <text class="text-xsm text-black text-bold margin-right">时长券</text>
              <text class="tab text-xsm text-red">已选1张</text>
            </view>
            <view>
              <text class="text-xsm text-red">-¥5.00</text>
              <text class="cuIcon-right" style="color: #e6e6e6;"></text>
            </view>
          </view> -->
          <view class="flex justify-between align-center margin-top" @tap="openPoints">
            <view>
              <text class="text-xsm text-black text-bold margin-right">可用积分</text>
              <text class="tab text-xsm text-red">{{ orderInfo.userPoints || 0 }} 积分</text>
            </view>
            <view>
              <text class="text-xsm text-red">-¥{{ orderInfo.pointDeductionAmount || 0 }}</text>
              <text class="cuIcon-right" style="color: #e6e6e6;"></text>
            </view>
          </view>
        </view>

        <!-- 支付方式 -->
        <view class="box">
          <pay-components 
            ref="pay" 
            :callBack="true" 
            @success="paySuccessBack" 
            @fail="payFailBack" 
            :islocker="true" 
          />
        </view>
      </view>

        <!-- 支付按钮 -->
        <view class="footer flex justify-end">
          <view class="text-sm" style="color: #747474; padding-top: 5rpx;">共 1 件</view>
          
          <view class="margin-left-sm text-df text-black">合计:</view>
          <view class="margin-left-sm">
            <view class="text-xdf" style="color: #FF305C">
              ¥{{ orderInfo.paymentPrice ? orderInfo.paymentPrice.toFixed(2) : 0 }}
            </view>
            <view class="text-sm" style="color: #747474">
              共省 ¥{{ orderInfo.pointDeductionAmount ? orderInfo.pointDeductionAmount.toFixed(2) : 0 }}
            </view>
          </view>
          <view class="btn" @click="generateOrders">确认支付</view>
        </view>
    </view>

    <!-- 温馨提示 -->
    <view
      class="cu-modal"
      :class="modalPoints ? 'show' : ''"
      catchtouchmove="touchMove"
      @click="closePoints"
    >
      <view
        class="dialog-tips cu-dialog bg-white"
        @click.stop
      >
        <view class="text-xl text-center text-black text-bold">
          积分抵扣
          <text @click.stop="closePoints" class="close cuIcon-roundclose" style="color: #e5e5e5; font-size: 50rpx;"></text>
        </view>
        <view class="text-lg text-black margin-top-xl padding-tb" style="border-bottom: 1rpx solid #E6E6E6; border-top: 1rpx solid #E6E6E6;">
          <view class="flex align-center">
            <text>当前可用积分：</text>
            <text>{{ orderInfo.userPoints || 0 }}积分</text>
          </view>
          <view class="flex align-center margin-top-xs">
            <text>本次可用积分：</text>
            <input class="input-points" type="number" v-model="orderInfo.usedPoints" />
          </view>
          <view class="flex align-center margin-top-xs">
            <text>可抵扣金额：</text>
            <text>
              <!-- {{ orderInfo.pointDeductionAmount ? orderInfo.pointDeductionAmount.toFixed(2) : 0 }} -->
                <!-- pointsPrice  points -->
              {{ ((orderInfo.pointsPrice / orderInfo.points) * orderInfo.usedPoints).toFixed(2) }}
            </text>
          </view>
        </view>
        <view class="text-lg text-red margin-top text-bold">
          提示：本次最多可用积分{{ orderInfo.availMaxPoints || 0 }}
        </view>
        <view class="btn-tips" @click.stop="submitPoints">确认抵扣</view>
      </view>
    </view>
  </view>
</template>
<script>
import { preOrder, generateOrders } from './api/order.js';
export default {
  data() {
    return { 
      orderInfo: {},
      modalPoints: false
    }
  },
  onLoad(options) {
    console.log(options.id)
    this.preOrder({orderId: options.id})
  },
  methods: {
    // 打开积分抵扣弹窗
    openPoints() {
      this.modalPoints = true
    },
    // 关闭积分抵扣弹窗
    closePoints() {
      this.modalPoints = false
    },
    // 确认积分抵扣
    submitPoints() {
      if (this.orderInfo.usedPoints > this.orderInfo.availMaxPoints) {
        uni.showToast({
          title: `单次使用积分不能超过${this.orderInfo.availMaxPoints}`,
          icon: 'none'
        })
        return
      }
      this.modalPoints = false
      this.preOrder({
        orderId: this.orderInfo.orderId,
        usedPoints: this.orderInfo.usedPoints,
        orderKey: this.orderInfo.orderKey
      })
    },
    // 预订单
    preOrder(params) {
      preOrder(params).then(res => {
        console.log(res.data)
        this.orderInfo = res.data
      })
    },
    // 生成订单
    generateOrders() {
      const params = {
        lockerOrderPayBO:{
          orderId: this.orderInfo.orderId,
          endTime: this.orderInfo.endTime,
          position: this.orderInfo.position,
          amount: this.orderInfo.amount,
          paymentPrice: this.orderInfo.paymentPrice,
          couponDeductionAmount: this.orderInfo.couponDeductionAmount,
          pointDeductionAmount: this.orderInfo.pointDeductionAmount,
          storeCode: this.orderInfo.storeCode,
          usedPoints: this.orderInfo.usedPoints || 0
        }
      }
      generateOrders(params).then(res => {
        if (this.orderInfo.paymentPrice == 0) {
          this.paySuccessBack()
        } else {
          this.$refs.pay.weChatPayOther(res.data.barCodePay)
        }
      })
    },
    // 支付成功回调
    paySuccessBack() {
      uni.redirectTo({
        url: '/pages/storagelocker/success?isorder=true&code=' + this.orderInfo.number
      }); 
    },
    // 支付失败回调
    payFailBack() {
      uni.redirectTo({
        url: '/pages/storagelocker/order'
      }); 
    }
  }
}
</script>
<style lang="scss" scoped>
.bg-img {
  width: 100%;
  height: 331rpx;
  position: absolute;
  z-index: 0;
}
.content {
  padding: 30rpx 18rpx 0;
  position: relative;
  z-index: 10;
}

.box{
  width: 100%;
  margin: 10rpx auto 0;
  background: #fff;
  border-radius: 60rpx;
  padding: 40rpx;
  position: relative;
  &:first-child{
    margin-top: 0;
  }
  // .no-data{
  //   display: block;
  //   width: 200rpx;
  //   height: 204rpx;
  //   margin: 0 auto;
  // }

  .cabinet{
    width: 196rpx;
    height: 196rpx;
    margin-right: 30rpx;
  }
  .tab{
    padding: 6rpx 22rpx;
    background: #FFEEEE;
    border-radius: 23rpx;
    line-height: 46rpx;
  }
}

.footer{
  width: 100%;
  height: 207rpx;
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 33rpx 33rpx 0 0;
  // display: flex;

  // justify-content: end;
  // align-items: center;
  .btn{
    width: 264rpx;
    height: 98rpx;
    background: #4A71FE;
    border-radius: 49rpx;
    text-align: center;
    line-height: 98rpx;
    color: #fff;
    font-size: 30rpx;
    margin-left: 40rpx;
  }
}

.dialog-tips{
  padding: 60rpx 40rpx;
  .btn-tips{
    width: 213rpx;
    height: 84rpx;
    background: #4A71FE;
    border-radius: 42rpx;
    font-size: 28rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 84rpx;
    margin: 53rpx auto 0;
    position: relative;
  }
  .close{
    position: absolute;
    right: 36rpx;
    top: 36rpx;
  }
  .input-points{
    width: 200rpx;
    height: 64rpx;
    border: 1rpx solid #E6E6E6;
    border-radius: 12rpx;
    padding: 0 20rpx;
  }
}
</style>
