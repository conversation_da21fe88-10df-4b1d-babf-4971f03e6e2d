import codePage from '@/components/verification-code/index.vue';
<template>
  <view>
    <cu-custom :isBack="true" bgImage="https://img.songlei.com/storagelocker/bg-1.png" :hideMarchContent="true">
			<block slot="content" style="color:#000000;">{{ isorder ? '支付成功' : '开柜成功' }}</block>
		</cu-custom>
    <view class="wrapper">
      <image class="bg-img" src="https://img.songlei.com/storagelocker/bg-2.png" />
      <view class="content">
        <view class="box">
          <image src="https://img.songlei.com/storagelocker/success.png" class="success" />
          <view v-if="isorder" class="text-xl text-center text-bold" style="color: #4A71FE; margin-top: 42rpx;">
            支付成功
          </view>
          <view class="text-xl text-center text-bold" style="color: #4A71FE;" :style="{marginTop: isorder ? '25rpx' : '72rpx'}">
            {{ isorder ? `${code}号柜门已开，请拿好物品` : `${code}号柜门已开，请寄存` }}
          </view>
          <view v-if="isorder" class="text-smx text-center text-black" style="margin-top: 30rpx;">
            欢迎下次使用
          </view>
          <view v-else="isorder" class="text-smx text-center text-black">寄存完成，请务必关好柜门</view>
        </view>
        <view class="btn" @click="toPage"> {{ isorder ? '返回首页' : '关闭' }}</view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      isorder: '',
      code: ''
    }
  },
  onLoad(options) {
    this.isorder = options.isorder
    this.code = options.code
  },
  methods: {
    toPage() {
      uni.reLaunch({
        url: '/pages/home/<USER>'
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.bg-img {
  width: 100%;
  height: 331rpx;
  position: absolute;
  z-index: 0;
}
.content {
  padding: 30rpx 18rpx 0;
  position: relative;
  z-index: 10;
}

.box{
  width: 100%;
  margin: 0 auto;
  background: #fff;
  border-radius: 60rpx;
  padding: 188rpx 0 300rpx;
  .success{
    width: 240rpx;
    height: 240rpx;
    display: block;
    margin: 0 auto 0;
  }
}

.btn{
  width: 336rpx;
  height: 100rpx;
  background: #4a72fe;
  border-radius: 50rpx;
  font-size: 30rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 100rpx;
  margin: 150rpx auto 0;
}
</style>
