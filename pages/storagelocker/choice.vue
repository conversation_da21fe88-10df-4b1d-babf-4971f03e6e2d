<template>
  <view>
    <cu-custom :isBack="true" bgImage="https://img.songlei.com/storagelocker/bg-1.png" :hideMarchContent="true">
			<block slot="content" style="color:#000000;">请选择寄存柜</block>
		</cu-custom>
    <view class="wrapper">
      <image class="bg-img" src="https://img.songlei.com/storagelocker/bg-2.png" />
      <!-- 无柜组可选 -->
      <view class="content" v-if="msg">
        <view class="no-box">
          <view id="navTab" :style="{boxShadow:'none'}" >
            <image src="https://img.songlei.com/storagelocker/reach.png" class="reach" />
            <view class="tips text-center">
              <view class="text-xsm margin-top-sm" style="color: #4A71FE">当前柜组已满</view>
            </view>
          </view>
          <scroll-view v-if="selectStore || listPages&&listPages.length>0" scroll-y :style="{height: `calc(85vh - ${navHeight}px)`}" @scrolltolower="getMore">
            <view class="text-lg text-black margin-top-sm">推荐附近可用柜组</view>
            <view class="item margin-top flex align-center" v-for="(item, index) in listPages" :key="index">
              <image src="https://img.songlei.com/storagelocker/cabinet.png"  class="cabinet"/>
              <view class="flex-sub">
                <view class="text-sm">
                  <text class="text-light-gray">柜组名称：</text>
                  <text>{{item.equipmentName}}</text>
                </view>
                <view class="text-sm margin-top-xs">
                  <text class="text-light-gray">所在楼层：</text>
                  <text>{{item.floor}}</text>
                </view>
                <view class="text-sm margin-top-xs">
                  <text class="text-light-gray">所在位置：</text>
                  <text>{{item.position}}</text>
                </view>
                <view class="text-sm margin-top-xs">
                  <text class="text-light-gray">可用柜组：</text>
                  <text>{{item.availableCabinetNum}}个</text>
                </view>
              </view>
            </view>
            <view :class="'cu-load ' + (loadmore ? 'loading' : 'over')"></view>
          </scroll-view>
        </view>
      </view>

      <!-- 有柜组可选 -->
      <view class="content" v-else>
        <view class="box">
          <view class="item flex align-center">
            <image class="img" src="https://img.songlei.com/storagelocker/big.png" />
            <view class="flex-sub">
              <view class="text-xsm text-black text-bold">大柜</view>
              <view class="flex justify-between" style="margin-top: 70rpx;">
                <view>
                  <view class="text-sm">尺寸：40cm×20cm</view>
                  <view class="text-sm">收费：{{equipmentList.lockerRule.largePrice || 0}}元/小时</view>
                </view>
                <view class="btn" @click="open('1',equipmentList.lockerRule.largePrice)">选择</view>
              </view>
            </view>
          </view>
        </view>
        <view class="box">
          <view class="item flex align-center">
            <image class="img" src="https://img.songlei.com/storagelocker/middle.png" />
            <view class="flex-sub">
              <view class="text-xsm text-black text-bold">中柜</view>
              <view class="flex justify-between" style="margin-top: 70rpx;">
                <view>
                  <view class="text-sm">尺寸：20cm×20cm</view>
                  <view class="text-sm">收费：{{equipmentList.lockerRule.mediumPrice || 0}}元/小时</view>
                </view>
                <view class="btn" @click="open('2',equipmentList.lockerRule.mediumPrice)">选择</view>
              </view>
            </view>
          </view>
        </view>
        <view class="box">
          <view class="item flex align-center">
            <image class="img" src="https://img.songlei.com/storagelocker/small.png" />
            <view class="flex-sub">
              <view class="text-xsm text-black text-bold">小柜</view>
              <view class="flex justify-between" style="margin-top: 70rpx;">
                <view>
                  <view class="text-sm">尺寸：10cm×20cm</view>
                  <view class="text-sm">收费：{{equipmentList.lockerRule.smallPrice || 0}}元/小时</view>
                </view>
                <view class="btn" @click="open('3',equipmentList.lockerRule.smallPrice)">选择</view>
              </view>
            </view>
          </view>
        </view>

        <view class="box">
          <view class="item flex align-center">
            <image class="img" src="https://img.songlei.com/storagelocker/self-service-cabinet.png" />
            <view class="flex-sub">
              <view class="text-xsm text-black text-bold">自选柜</view>
              <view class="text-sm" style="margin-top: 16rpx;">
                自由选择当前可用寄存柜
              </view>
              <view class="btn" style="margin: 30rpx 0 0 auto;" @click="open('-1')">选择</view>
            </view>
          </view>
        </view>

        <view class="rules" v-if="equipmentList.lockerRule&&equipmentList.lockerRule.remark">
          <view class="text-xsm text-black text-bold">规则说明</view>
          <view class="text-smx margin-top-sm"  style="white-space: pre-wrap;" v-html="equipmentList.lockerRule.remark">
          </view>
        </view>
      </view>
    </view>

    <uni-popup ref="perpopup" type="center" :mask-click='false'>
			<view class="permissions_box">
				当您使用APP时，获取最近的寄存柜需要位置权限，不授权上述权限，不影响APP其他功能使用。
			</view>
		</uni-popup>
  </view>
</template>
<script>
// 获取大中小柜  获取附近柜子 查询箱子状态信息 获取门店分页 打开柜子
import { getEquipmentByNo, getLockerequipmentPage, getStateByNo, getStoreinfoPage, getLockerorder } from './api/choice.js';
import distanceUtil from 'utils/distanceUtil';
const app = getApp();
import util from 'utils/util'

export default {
  data() {
    return {
      activity: 1,
      equipmentNo: '', //扫码识别类型
      equipmentList: {}, // 柜组信息
      loadmore: true,
      navHeight: 0,
      page: {
        current: 1,
        size: 10,
      },
      listPages: [], // 附近寄存柜列表
      storeCode: '', // 门店编码,现在默认写死松雷-南岗店201 松雷-香坊店203 松雷-道里店202
      msg: null, // msg有数据说明柜组已满 null未满
      storelist: [], // 门店列表分页
      selectStore:null, //当前定位信息
    }
  },
  onLoad(options){
    if (options.q) {
      // 解码 需要两次解码才可以解析成常规链接
      const url = decodeURIComponent(options.q); 
      // 解码
      const decodedUrl = decodeURIComponent(url); 
      // 获取equipmentNo
      const equipmentNo = util.UrlParamHash(decodedUrl.split('?')[1], 'equipmentNo');
      this.equipmentNo = equipmentNo
    }
    if (options.equipmentNo) {
      this.equipmentNo = options.equipmentNo
    }
  },
  onShow(){
    app.initPage().then(res => {
      if (util.isUserLogin()) {
        this.getEquipmentNo(this.equipmentNo)
        this.getStateByNo(this.equipmentNo)
        this.getstorelist()
      } else {
        const pages = getCurrentPages()
        const url = pages[pages.length - 1]['$page']['fullPath']
        uni.reLaunch({
          url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
        })
      }
    });
  },

  methods: {
    // 打开柜子
    async open(num,price){
      try {
        if (num == '-1') {
          uni.navigateTo({
            url: `/pages/storagelocker/self-service-cabinet?equipmentNo=${this.equipmentNo}&storeId=${this.equipmentList.storeId}&floor=${this.equipmentList.floor}`
          })
          return
        }

        const parameter = {
          equipmentNo: this.equipmentNo,
          cabinetType: num,
          storeId: this.equipmentList.storeId,
          floor: this.equipmentList.floor,
          cabinetPrice: price,
        }
        const { data, msg } = await getLockerorder(parameter)
        this.msg = msg || ''
        if (this.msg) {
          this.$nextTick(()=>{
            let query = uni.createSelectorQuery()
            query.select('#navTab').boundingClientRect() 
            query.exec((res) => {
              if (res) {
                this.navHeight = res[0].height
              }
            })
          })
          return
        }
        // 开柜成功
        uni.navigateTo({
          url: `/pages/storagelocker/success?code=${this.equipmentNo}`
        })
        
      } catch (error) {
        console.error("err",error);
      }
    },

    // 获取有寄存柜门店分页列表
    async getstorelist() {
			const { data } = await getStoreinfoPage(this.page)
      this.storelist = data.records;
      this.checkPosPermission()
		},

    // 定位兼容
		checkPosPermission() {
			// #ifdef MP-WEIXIN
			this.getCurrentPos();
			// #endif 
			// #ifdef APP-PLUS
			let that = this;
			var platform = uni.getSystemInfoSync().platform;
			if (platform == 'android') {
				plus.android.checkPermission(
					'android.permission.ACCESS_FINE_LOCATION',
					granted => {
						if (granted.checkResult == -1) {
							//弹出
							that.$refs.perpopup.open('top')
						} else {
							//执行你有权限后的方法
							that.getCurrentPos();
						}
					},
					error => {
						console.error('Error checking permission:', error.message);
					}
				);
				plus.android.requestPermissions(['android.permission.ACCESS_FINE_LOCATION'],
					(e) => {
						//关闭
						that.$refs.perpopup.close()
						if (e.granted.length > 0) {
							//执行你有权限后的方法
							that.getCurrentPos();
						}
					})
			} else {
				//执行你有权限后的方法 ios
				that.getCurrentPos();
			}
			// #endif 
		},

    // 定位当前位置
		async getCurrentPos() {
      let that = this;
      uni.getLocation({
        type: 'gcj02',
        success:  (res) => {
          const { latitude, longitude } = res;
          // 测试南岗定位
          // const { latitude, longitude } = {latitude: "+45.765013",longitude: "126.650278"};

          const nearestStore = that.findNearestStore(latitude, longitude);
          that.selectStore = nearestStore;
          if (nearestStore) {
            that.storeCode = nearestStore.storeCode
            this.lockerequipmentList()
          }
        },
        fail() {
          // 定位失败
          that.selectStore = null;
        }
      });
    },

    // 查找最近的门店
    findNearestStore(currentLat, currentLng) {
      let nearestStore = null;
      let minDistance = Infinity;
      this.storelist.forEach(store => {
        const distance = distanceUtil.calculateDistance(currentLat, currentLng, store.latitude, store.longitude);
        if (distance < minDistance && distance <= 200 ) { // 只考虑200米内的门店
          minDistance = distance;
          nearestStore = { ...store, distance: Math.round(distance) }; // 将距离添加到门店对象中
        }
      });
      return nearestStore || null; // 超出200米返回null
    },

    //附近寄存柜列表
    lockerequipmentList(){
      getLockerequipmentPage(Object.assign({storeCode:this.storeCode},this.page)).then((res)=>{
        if (res.data) {
          if (res.data.records) {
            let listPages = res.data.records
            this.listPages = [...this.listPages, ...listPages];
            if (listPages.length < this.page.size) {
              this.loadmore = false;
            }
          }else{
            this.loadmore = false;
          }
        }
      })
    },

    // 滚动到底部
		getMore() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
        this.lockerequipmentList();
			}
		},

    // 获取大，中 小寄存柜金额
    async getEquipmentNo(num){
      try {
        const { data } = await getEquipmentByNo({equipmentNo:num})
        this.equipmentList = data
      } catch (error) {
        console.error("err---->",error);
        
      }
    },

     // 查询箱子状态信息
    async getStateByNo(num){
      try {
        const { data } = await getStateByNo({equipmentNo:num})
        // msg有数据说明柜组已满 null未满
        this.msg = data || ''
        // this.msg = '测试'
        if (this.msg) {
          this.$nextTick(()=>{
            let query = uni.createSelectorQuery()
            query.select('#navTab').boundingClientRect() 
            query.exec((res) => {
              if (res) {
                this.navHeight = res[0].height
              }
            })
          })
        }
      } catch (error) {
        console.error("err---->",error);
        
      }
    }
  },
}
</script>
<style lang="scss" scoped>
.bg-img {
  width: 100%;
  height: 331rpx;
  position: absolute;
  z-index: 0;
}
.content {
  padding: 30rpx 18rpx 0;
  position: relative;
  z-index: 10;
}

.no-box{
  width: 100%;
  margin: 0 auto;
  background: #fff;
  border-radius: 60rpx;
  padding: 40rpx;
  .item:first-child{
    margin-top: 0;
  }
  .tips{
    padding-bottom: 30rpx;
    border-bottom: 2rpx solid #f9f7ff;
  }
  .reach{
    display: block;
    width: 183rpx;
    height: 183rpx;
    margin: 0 auto;
  }
  .cabinet{
    width: 196rpx;
    height: 196rpx;
    margin-right: 30rpx;
  }
  
}

.box{
  width: 100%;
  height: 250rpx;
  margin: 10rpx auto 0;
  background: #fff;
  border-radius: 60rpx;
  padding: 35rpx;
  &:first-child{
    margin-top: 0;
  }
  .img{
    width: 196rpx;
    height: 196rpx;
    margin-right: 26rpx;
  }
  .btn{
    width: 150rpx;
    height: 64rpx;
    background: #4A71FE;
    border-radius: 32rpx;
    font-size: 28rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 64rpx;
  }
}

.rules{
  padding: 34rpx;
}
</style>
