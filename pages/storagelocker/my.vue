<template>
  <view>
    <cu-custom :isBack="true" bgImage="https://img.songlei.com/storagelocker/bg-1.png" :hideMarchContent="true">
			<block slot="content" style="color:#000000;">我的</block>
		</cu-custom>
    <view class="wrapper">
      <image class="bg-img" src="https://img.songlei.com/storagelocker/bg-2.png" />
      <view class="content">
        <view class="box">
          <view class="my-info flex justify-center align-center">
            <image :src="userInfo.headimgUrl || 'https://img.songlei.com/storagelocker/avatar.png'" class="avatar" />
            <view class="flex-sub">
              <view class="text-sm text-black">{{ userInfo.phone| phoneEncryption }}</view>
              <view class="text-sm text-black">会员等级：{{  userInfo.erpCustTypename }}</view>
              <view class="text-sm text-black">可用积分：{{ userInfo.pointsCurrent }}</view>
            </view>
          </view>
          <view class="order flex justify-around align-center">
            <view @click="toPage('/pages/storagelocker/order')">
              <image src="https://img.songlei.com/storagelocker/my-order.png" class="icon" />
              <view class="text-center">我的订单</view>
            </view>
            <view @click="toPage('/pages/coupon/coupon-user-list/index')">
              <image src="https://img.songlei.com/storagelocker/voucher.png" class="icon" />
              <view class="text-center">我的卡券</view>
            </view>
          </view>
          <view class="service flex justify-center align-center" @tap="makeCall">
            <image src="https://img.songlei.com/storagelocker/service.png" class="icon" />
            <view class="flex-sub">
              <view class="text-sm text-black text-bold">客服中心</view>
              <view class="text-sm text-black">{{ tel }}</view>
              <view class="text-sm text-black">服务时间：10:00-20:00</view>
            </view>
          </view>
        </view>
        <!-- 广告 -->
        <view class="margin-top-xl" v-if="adList.length">
          <swiper	
            circular
            autoplay 
            :interval="3000"
            :duration="500"
            :indicator-dots="false"
            easing-function="linear"
            style="width: 714rpx;height: 250rpx; "
          >
            <swiper-item 
              v-for="(item, index) in adList" 
              :key="index" 
              style="border-radius: 20rpx; height: 250rpx; width: 714rpx; "
              @click.stop="toPage(item.skipApplet)"
            >
              <image style="width: 100%; height: 300rpx; display: block; margin: auto; border-radius: 20rpx;" :src="item.picUrl" mode="aspectFill" />
            </swiper-item>
          </swiper>
        </view>
      </view>
      
      
      
      <view class="footer">
        <view class="text-sm text-black text-center">
          版本号：{{ packageVersion }}
        </view>
        <view class="btn" @click="logout">退出登录</view>
      </view>
    </view>
  </view>
</template>
<script>
const app = getApp();
import api from 'utils/api'
// #ifdef APP-PLUS
import { deletePushAlias } from '@/api/push';
// #endif
import { getAdvertising } from './api/advertising.js';

export default {
  data() {
    return { 
      userInfo: {},
      tel: '4006171819',
      packageVersion: '',
      adList: []
    }
  },
  onLoad(options) {
    let that = this;
    // #ifdef APP-PLUS
    plus.runtime.getProperty(plus.runtime.appid, (wgtInfo) => {
      that.packageVersion = wgtInfo.version // appVersion就是版本号
    })
    // #endif
    // #ifndef APP-PLUS
    uni.getSystemInfo({
      success: function(res) {
        that.packageVersion = res.appVersion;
      }
    })
    // #endif
  },
  onShow() {
    app.initPage().then(res => {
      this.userInfoGet();
      this.getAdvertising();
    });
  },

  methods: {
    // 获取用户信息
    userInfoGet() {
      api.userInfoGet().then(res => {
        this.userInfo = res.data || {};
      });
    },
    // 跳转页面
    toPage(url) {
      console.log('跳转页面', url)
      uni.navigateTo({
        url: url
      }); 
    },
    // 拨打电话
    makeCall() {
      uni.makePhoneCall({
        phoneNumber: '4006171819',
        success: function () {
          console.log('拨打电话成功');
        },
        fail: function (err) {
          console.error('拨打电话失败', err);
        }
      });
    },
    // 获取广告
    getAdvertising() {
      getAdvertising({type: 3}).then(res => {
        console.log('广告', res.data)
        this.adList = res.data || [];
      });
    },
    // 退出登录
    logout() {
      uni.showModal({
        content: '确定退出登录吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',
        success(res) {
          if (res.confirm) {
            api.logout().then(res => {
              let userInfo = res.data;
              uni.setStorageSync('user_info', userInfo);
              if (userInfo) {
                uni.setStorageSync('third_session', userInfo.thirdSession);
              }
              // #ifdef APP-PLUS
              deletePushAlias();
              // #endif
              //退出登录完成跳到首页
              uni.reLaunch({
                url: '/pages/home/<USER>'
              });
              //清空购物车数量
              app.globalData.shoppingCartCount = '0';
              uni.$emit("updateCart");
            });
          }
        }
      });
    },
  }
}
</script>
<style lang="scss" scoped>
.bg-img {
  width: 100%;
  height: 331rpx;
  position: absolute;
  z-index: 0;
}
.content {
  padding: 30rpx 18rpx 0;
  position: relative;
  z-index: 10;
  .title{
    padding-left: 36rpx;
  }
}

.box{
  .my-info{
    width: 100%;
    height: 189rpx;;
    background-color: #fff;
    border-radius: 60rpx;
    padding: 40rpx;
    margin-top: 10rpx;
    .avatar{
      width: 108rpx;
      height: 108rpx;
      margin-right: 30rpx;
      border-radius: 50%;
    }
  }
  .order{
    width: 100%;
    height: 233rpx;
    background-color: #fff;
    border-radius: 60rpx;
    margin-top: 10rpx;
    .icon {
      width: 125rpx;
      height: 125rpx;
      border-radius: 50%;
    }
  }
  .service {
    width: 100%;
    height: 233rpx;
    background-color: #fff;
    border-radius: 60rpx;
    padding: 40rpx 120rpx;
    margin-top: 10rpx;
    .icon {
      width: 153rpx;
      height: 153rpx;
      margin-right: 42rpx;
      border-radius: 50%;
    }
  }
}

.footer{
  margin: 100rpx auto 0;
  .btn{
    width: 336rpx;
    height: 100rpx;
    background: #D0D5E2;
    border-radius: 50rpx;
    font-size: 28rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 100rpx;
    margin: 34rpx auto 0;
  }
}

</style>
