<template>
	<view class="padding-bottom">
		<cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">礼品卡付款码</block>
		</cu-custom>
		<!-- 余额 -->
		<view>
			<view class="relative">
				<image style="width: 750upx; height: 186upx;" src="https://img.songlei.com/gift/gift-card.png">
				</image>

				<view style="width: 90%;top: 10rpx;left: 0;right: 0;margin: 0 auto;"
					class="absolute flex justify-between padding-sm">
					<view style="margin: auto;">
						<!-- <view style="margin: auto;"> -->
						<view class="text-xsm text-bold text-black flex justify-center align-baseline"
							style="margin-top: 12rpx;">
							余额
							<format-price :price="oye" priceFontSize="44rpx" />
						</view>
						<view v-if="cardNo" class=" text-black" style="font-size: 22rpx;">{{cardName}}：{{cardNo}}</view>
						<view v-else class=" text-black" style="font-size: 22rpx;">礼品卡数量：{{num}}张</view>
					</view>

					<view class="my-gift-card" @tap="showModalSku()">
						说明
					</view>
				</view>
			</view>

			<view v-show="!isDialog" style="margin: 0rpx 36rpx 0 36rpx;background-color: #ffffff;border-radius: 20rpx;">
				<view style="display: flex; flex-direction: column; align-items: center">
					<view style="width: 600rpx; height: 120rpx; margin-top: 30rpx;">
						<canvas class="bar_code" canvas-id="Brcode">
						</canvas>
					</view>

					<image style="width: 259rpx; height: 259rpx; margin-top: 35rpx" mode="aspectFit" :src="qrImg">
					</image>

					<view @click="handleFreshData"
						style="color: #999999;font-size: 20rpx;margin-top: 24rpx;margin-bottom: 120rpx;text-decoration: underline;">
						刷新二维码
					</view>
				</view>
			</view>

			<view v-if="isDialog" style="margin: 0rpx 36rpx 0 36rpx;background-color: #ffffff;border-radius: 20rpx;">
				<view style="display: flex; flex-direction: column; align-items: center">
					<image class="margin-top-xl margin-bottom" style="width: 136upx; height: 161upx;" mode="aspectFill"
						src="https://img.songlei.com/gift/ban.png">
					</image>
					<!-- 说明信息 -->
					<view class="padding-sm text-center">
						<view class="text-xsm text-black text-center">该功能用于向商家付款时出示使用，</view>
						<view class="text-xsm text-black margin-bottom">请不要将付款码及数字截屏发送给他人。</view>
						<!-- 确认按钮 -->
						<view style="margin: 80rpx 0 110rpx; ">
							<button @click="isDialog=!isDialog"
								style="width: 317rpx;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%); color: #FFF;"
								class="cu-btn round">
								知道了
							</button>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api'
	import __config from 'config/env';
	import util from '@/utils/util'
	import formatPrice from "@/components/format-price/index.vue";
	const QR = require("utils/wxqrcode.js");
	const brCode = require("utils/barcode.js");
	import {
		setScreenshot
	} from "@/static/mixins/setScreenshot.js";
	import {
		getPaypayCode,
		getOnePaypayCode
	} from "@/pages/gift/api/gift";

	export default {
		mixins: [setScreenshot],
		components: {
			formatPrice
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '',
				loadingImg: 'data:image/png;base64,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',
				carInfo: {},
				userInfo: {},
				qrImg: null,
				isDialog: false, //知道了弹框
				num: 0, //礼品卡数量
				oye: 0, // 余额
				cardNo: '', //卡号，用来存储单卡卡号
				cardName: '', //卡类型名称
				pollingTimer: null,
				requestTask: null,
			};
		},
		props: {},
		onLoad(options) {

		},
		mounted() {},

		onShow() {
			// 禁止分享
			uni.hideShareMenu()
			if (uni.getStorageSync('cardNo')) {
				this.cardNo = uni.getStorageSync('cardNo');
				uni.removeStorageSync('cardNo');
			}
			this.cardInfoGet();
			this.onUserCaptureScreen()
		},

		onHide() {
			// 在页面隐藏时清除定时器，以防止内存泄漏
			console.log("onHide", this.pollingTimer, this.requestTask);
			this.requestTask ? this.requestTask.abort() : '';
			if (this.pollingTimer) {
				clearTimeout(this.pollingTimer);
			}
		},

		onUnload() {
			// 在用户退出页面时清除定时器，以防止内存泄漏
			console.log("onUnload", this.pollingTimer, this.requestTask);
			this.requestTask ? this.requestTask.abort() : ''
			if (this.pollingTimer) {
				clearTimeout(this.pollingTimer);
			}
			
		},

		computed: {},

		// destroyed() {
		//   // 在页面销毁时清除定时器，以防止内存泄漏
		//   console.log("destroyed",this.pollingTimer);
		//   this.requestTask ? this.requestTask.abort() : ''
		//   if (this.pollingTimer) {
		//     clearTimeout(this.pollingTimer);
		//   }
		// },

		// beforeDestroy() {
		//   console.log("beforeDestroy",this.pollingTimer);
		//   this.requestTask ? this.requestTask.abort() : ''
		//   if (this.pollingTimer) {
		//     clearTimeout(this.pollingTimer);
		//   }
		// },

		methods: {
			//长轮训，回调支付结果，超时之后继续轮训，直到获取到结果
			longLink(_url, method) {
				let that = this;
				console.log("回调支付结果", _url, method);
				const userInfo = uni.getStorageSync('user_info')
				that.requestTask = uni.request({
					url: __config.basePath + _url,
					method: method,
					withCredentials: true,
					header: {
						//#ifdef H5
						'client-type': util.isWeiXinBrowser() ? 'H5-WX' : 'H5', //客户端类型普通H5或微信H5
						'tenant-id': app.globalData.tenantId || __config.tenantId,
						'app-id': app.globalData.appId ? app.globalData.appId : '', //微信h5有appId
						//#endif
						//#ifdef MP-WEIXIN
						'client-type': 'MA', //客户端类型小程序
						'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
						//#endif
						//#ifdef APP-PLUS
						'client-type': 'APP', //客户端类型APP
						'tenant-id': app.globalData.tenantId,
						//#endif
						'third-session': uni.getStorageSync('third_session') ? uni.getStorageSync(
							'third_session') : '',
						'user_id': userInfo ? userInfo.id : ''
					},
					success: (res) => {
						if (res.statusCode === 200 && res.data && res.data.notifyType == 'GIFTCARD') {
							// 接口返回成功状态码，处理数据并结束轮询
							that.handleSuccess(res.data);
							clearTimeout(that.pollingTimer);
						} else {
							// 接口返回非成功状态码，继续下一轮轮询
							that.pollingTimer = setTimeout(function() {
								that.longLink("/mallapi/userinfo/listener", "get")
							}, 1000)
						}
					},
					fail: (err) => {
						// 请求失败处理，继续下一轮轮询
						that.pollingTimer = setTimeout(function() {
							that.longLink("/mallapi/userinfo/listener", "get")
						}, 1000)
					}
				});
			},

			// 处理成功返回的数据
			handleSuccess(data) {
				uni.setStorageSync("giftPayNotifyContent", data.notifyContent);
				uni.navigateTo({
					url: '/pages/gift/success/index'
				})
				console.log("===getStorageSync===", uni.getStorageSync("giftPayNotifyContent"))
			},

			//监听用户截屏
			onUserCaptureScreen() {
				console.log("-----");
				uni.onUserCaptureScreen(function() {
					console.log('用户截屏了222')
					this.showModalSku()
				});
			},

			//刷新二维码
			handleFreshData() {
				this.cardInfoGet();
			},

			//获取二维码
			cardInfoGet() {
				// uni.showLoading({
				// 	title: '加载中',
				// 	mask: true
				// });
				let that = this;
				this.qrImg = this.loadingImg;
				if (this.cardNo) {
					getOnePaypayCode(this.cardNo).then(res => {
						if (res.code == 0) {
							if (res.data.orcode) {
								this.qrImg = QR.createQrCodeImg(res.data.orcode, {
									size: parseInt(200) //二维码大小  
								});
								brCode.code128(wx.createCanvasContext('Brcode'), res.data.orcode, 300, 55);
								//先清理 再轮训，方式重复
								if (this.pollingTimer) {
									clearTimeout(this.pollingTimer);
								}
								this.pollingTimer = setTimeout(function() {
									that.longLink("/mallapi/userinfo/listener", "get")
								}, 1000)
							}
							this.oye = res.data.oye || 0;
							this.cardName = res.data.cardName
						}
					})
					return
				}else {
				
				getPaypayCode().then(res => {
					if (res.code == 0) {
						if (res.data.orcode) {
							this.qrImg = QR.createQrCodeImg(res.data.orcode, {
								size: parseInt(200) //二维码大小  
							});
							brCode.code128(wx.createCanvasContext('Brcode'), res.data.orcode, 300, 55);
							this.pollingTimer = setTimeout(function() {
								that.longLink("/mallapi/userinfo/listener", "get")
							}, 1000)
						}
						this.oye = res.data.oye || 0;
						this.num = res.data.num || 0;
				    	}
				  })	
				}
			},

			//关闭弹框
			hideModalDialog(num) {
				console.log("222");
				this.isDialog = false;
			},

			//打开弹框
			showModalSku() {
				this.isDialog = true;
			},
		}
	};
</script>

<style lang="scss" scoped>
	.my-gift-card {
		position: absolute;
		right: 0;
		width: 149rpx;
		text-align: center;
		border-radius: 15px;
		padding: 8rpx 20rpx 8rpx 20rpx;
		font-size: 26rpx;
		background: #FFFFFF;
		opacity: 0.62;
		color: #653006;
	}
</style>