<template>
  <view>
    <cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">礼品卡付款码</block>
	  </cu-custom>

		<!-- <warn-page v-if="isPasswordSet" :btnTo="`/pages/gift/set-password/code/index?isPasswordSet=${isPassword}`" @change="btn($event)"/>
    
    <view v-else> -->
    <view style="margin-top: 235rpx;">
      <view class="text-center" style="margin-bottom: 90rpx;">请输入支付密码</view>

      <code-page @change="code($event)" :passwordShow="true"/>
    </view>

    </view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
	import warnPage from "../components/warn.vue";
	import codePage from "@/components/verification-code/index.vue";
	import {passwordCheck} from '@/pages/gift/api/gift';
	export default {
		components: {
			warnPage,
            codePage
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '礼品卡付款码',
				isPasswordSet:true,//是否设置密码
				isPassword:false,//是否设置密码
			};
		},
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
				
			});
		},
		mounted() {
	
		},

		onShow() {
			// 禁止分享
			uni.hideShareMenu()
		},

		onHide() {
		},

		computed: {
		},

		methods: {
			// 是否设置密码
			async	btn(bool){
				this.isPasswordSet= bool
        console.log("btn",this.isPasswordSet);
			},

      //输入框
      async	code(val){
          console.log("val",val);
					passwordCheck(Object.assign({password:val})).then(res=>{
						console.log("setPassword=>",res);
						if (res.code == 0) {
							uni.redirectTo({
								url: `/pages/gift/payment-code/gift-payment-code/index`
							})
						}
					}).catch((e)=>{
						console.log("ee",e);
					})
				}
      }
	};
</script>

<style lang="scss" scoped>
</style>
