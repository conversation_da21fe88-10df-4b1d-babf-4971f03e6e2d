<template>
	<view>
		<cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content"> 绑定新卡</block>
		</cu-custom>
		<!-- 卡片背景图 -->
		<view class="relative">
			<image style="width: 750upx; height: 264upx;" mode="aspectFill"
				src="https://img.songlei.com/gift/bgimg.png">
			</image>

			<view v-if="payment" style="width: 90%;top: 10rpx;left: 0;right: 0;margin: 0 auto;"
				class="absolute flex justify-between padding-sm">
				<view style="margin: auto;margin-right: 80rpx;">
					<image style="width: 629upx; height: 358upx;border-radius: 30rpx;" mode="aspectFill"
						:src="payment.imgUrl | formatImg750">
					</image>
				</view>
			</view>

			<view>
				<view class="my-gift-card text-xs" @tap="showModalSku">绑卡说明</view>
			</view>

			<view style="position: absolute;top: 255rpx;">
				<image style="width: 750upx; height: 150upx;" mode="aspectFill"
					src="https://img.songlei.com/gift/bottomImg.png">
				</image>
			</view>

			<!-- <view style="position: absolute;top: 300rpx;right: 0;left: 0;" class="text-center">
				<view class="text-bold text-df text-black">- 卡号密码或涂层卡密 -</view>
				<view class="text-xsm text-black">请输入卡号密码或涂层卡密</view>
				<view class="text-sm text-light-gray">（电子礼品卡无需在此操作）</view>
			</view> -->
		</view>

		<!-- 输入框 -->
		<!-- <view class="carmi padding-left-sm padding-right-sm" style="">
			<input disabled style="width: 475rpx;height: 80rpx;text-align: center;" type="text" placeholder="点此区域手动输入卡号密码或涂层卡密"
				placeholder-style="color: #CCCCCC;font-size: 26rpx;margin-left:20rpx" @input="bindNewCard($event)"
				v-model="bindCard" maxlength="29">
			<view class="cuIcon-camera icon" @tap="showModalScan" style="color: #DAB691;"></view>
		</view> -->

		<!-- 绑定按钮 -->
		<view class="btn" style="margin-top: 70rpx;">
			<button @tap="scanCode"
				style="width: 100%;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%); color: #FFF;"
				class="cu-btn round">
				继续绑卡
			</button>
		</view>

		<view class="line">
		</view>

		<!-- 使用流程 -->
		<view>
			<view class="text-bold text-df text-black margin-bottom-sm text-center">- 使用流程 -</view>
			<view>
				<view class="cu-list grid col-5 no-border"
					style="width: 700rpx;background: #FFFFFF;border-radius: 34px;margin: 0 auto;padding-bottom: 0;">
					<!-- 去绑定 -->
					<view class="cu-item">
						<!-- <navigator url="/pages/order/order-list/index?status=0" hover-class="none"> -->
						<view>
							<image style="width: 108upx; height: 108upx;" mode="aspectFill"
								src="https://img.songlei.com/gift/binding.png">
							</image>
						</view>
						<text class="user-text-xl text-df text-black">去绑定</text>
						<!-- </navigator> -->
					</view>

					<view class="cu-item">
						<view style="font-size: 64rpx ; margin-top:0">
							<image style="width: 82upx; height: 25upx;" mode="aspectFill"
								src="https://img.songlei.com/gift/right-drop.png">
							</image>
						</view>
					</view>
					<!-- 去购物 -->
					<view class="cu-item">
						<!-- <navigator url="/pages/order/order-list/index?status=2" hover-class="none"> -->
						<view>
							<image style="width: 108upx; height: 108upx;" mode="aspectFill"
								src="https://img.songlei.com/gift/shopping.png">
							</image>
						</view>
						<text class="user-text-xl text-df text-black">去购物</text>
						<!-- </navigator> -->
					</view>

					<view class="cu-item">
						<view style="font-size: 64rpx ; margin-top:0">
							<image style="width: 82upx; height: 25upx;" mode="aspectFill"
								src="https://img.songlei.com/gift/right-drop.png">
							</image>
						</view>
					</view>
					<!-- 结算 -->
					<view class="cu-item">
						<!-- <navigator url="/pages/order/order-refunds/index" hover-class="none"> -->
						<view>
							<image style="width: 108upx; height: 108upx;" mode="aspectFill"
								src="https://img.songlei.com/gift/settlement.png">
							</image>
						</view>
						<text class="user-text-xl text-df text-black">结算</text>
						<!-- </navigator> -->
					</view>
				</view>
			</view>
		</view>

		<!-- 绑卡说明弹框 -->
		<view class="cu-modal" :class="modalDialog ? 'show' : ''" @tap="hideModalDialog" catchtouchmove="touchMove">
			<!-- 关闭弹框icon -->
			<text class="cuIcon-close"
				style="color: #FFF;position: absolute;top: 19%;right: 5%;border: 1rpx solid #FFF;width: 40rpx;border-radius: 50%;"
				@tap="hideModalDialog"></text>
			<view class="cu-dialog dialo-sku bg-white " :class="modalDialog ? 'animation-slide-bottom' : ''"
				style="height:720upx;border-radius: 30rpx;" @tap.stop>
				<view class="cu-card article no-card " style="padding-top: 20upx;">
					<view class="text-xl close-icon text-center  padding-left-sm padding-right-sm ">
						<text class="text-df text-black">- 绑卡说明 -</text>
					</view>
				</view>
				<!-- 说明信息 -->
				<view class="padding-sm">
					<view class="text-xsm text-black text-left" style="text-indent: 2em; ">
						欢迎您使用松雷商业集团-松鼠美淘礼品卡！松鼠美淘礼品卡是以松雷商业集团为实体，松鼠美淘售卖的支付卡。绑定后可在松雷商业集团各业态使用。同时，我们会对您的信息进行严格的保密，不会泄露给非行政执法部门的第三方，请您放心使用。
					</view>

					<view class="text-xsm text-black text-left">1、此卡仅可绑定一个微信用户，且每卡仅绑定一次，不得解绑、不可充值、不可返现。</view>
					<view class="text-xsm text-black text-left">2、您可以在“松鼠美淘小程序”-“我的”-“礼品卡”中查看相关信息及维护操作。</view>
					<view class="text-xsm text-black text-left">
						3、使用方法。绑定后可于松雷商业集团各业态使用。礼品卡的支付等同于现金，在结账时出示实体卡或使用松鼠美淘小程序-“礼品卡付款码”支付。线上订单可在支付时选择“礼品卡支付”。如果当余额不足时可用其他支付方式补足。
					</view>
					<view class="text-xsm text-black text-left">4、使用礼品卡消费的项目不可开具发票。</view>
				</view>
			</view>
		</view>

		<!-- 扫描确认弹框 -->
		<view class="cu-modal" :class="modalScanDialog ? 'show' : ''" @tap="hideModalDialog" catchtouchmove="touchMove">
			<!-- 关闭弹框icon -->
			<text class="cuIcon-close"
				style="color: #FFF;position: absolute;top: 20%;right: 5%;border: 1rpx solid #FFF;width: 40rpx;border-radius: 50%;"
				@tap="hideModalDialog"></text>
			<view class="cu-dialog dialo-sku bg-white " :class="modalScanDialog ? 'animation-slide-bottom' : ''"
				style="width: 590rpx;height:670upx;border-radius: 30rpx;" @tap.stop>
				<image style="width: 389upx; height: 401upx;margin-top:10upx" mode="aspectFill"
					src="https://img.songlei.com/gift/scan.png">
				</image>
				<!-- 说明信息 -->
				<view class="padding-sm">
					<view class="text-xsm text-black">为增加识别率</view>
					<view class="text-xsm text-black">请<text style="border-bottom: 1rpx solid #000000;">竖握手机
						</text> 、<text style="border-bottom: 1rpx solid #000000;"> 横置卡片</text>进行拍照</view>
					<!-- 确认按钮 -->
					<view class="btn" style="margin-top: 20rpx;">
						<button @tap="scanCode"
							style="width: 100%;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%); color: #FFF;"
							class="cu-btn round">
							确定
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 失败或者成功弹框 -->
		<view class="cu-modal" :class="isodalDialog ? 'show' : ''" @tap="hideModalDialog" catchtouchmove="touchMove">
			<!-- 关闭弹框icon -->
			<text class="cuIcon-close"
				style="color: #FFF;position: absolute;top: 32%;right: 5%;border: 1rpx solid #FFF;width: 40rpx;border-radius: 50%;"
				@tap="hideModalDialog"></text>
			<view class="cu-dialog dialo-sku bg-white " :class="isodalDialog ? 'animation-slide-bottom' : ''"
				style="width: 590rpx;border-radius: 30rpx;" @tap.stop>
				<!--错误图： https://img.songlei.com/gift/wrong.png -->
				<image style="width: 136upx; height: 136upx;margin-top:30upx" mode="aspectFill"
					:src="msg?'https://img.songlei.com/gift/wrong.png':'https://img.songlei.com/gift/answer.png'">
				</image>
				<!-- 说明信息 -->
				<view class="padding-sm">
					<view class="text-xsm text-black">{{msg?msg:'绑定成功！'}}</view>
					<view class="line" style="width: 475rpx;"></view>
					<!-- 确认按钮 -->
					<view class="btn" style="margin-top: 20rpx;">
						<button @tap="bind()"
							style="width: 100%;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%); color: #FFF;"
							class="cu-btn round">
							{{msg?'确定':'查看我的礼品卡'}}
						</button>
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
	import formatPrice from "@/components/format-price/index.vue";
	import {
		bindCardCode
	} from "@/pages/gift/api/gift";

	// #ifdef APP-PLUS
	const mpaasScanModule = uni.requireNativePlugin("Mpaas-Scan-Module");
	// #endif
	export default {
		components: {
			formatPrice
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '我的礼品卡',
				modalDialog: false, //控制帮助弹框
				modalScanDialog: false, //控制扫描弹框
				isodalDialog: false, //绑定成功或错误弹框
				bindCard: '', //绑定新卡
				msg: '', //错误返回信息
				payment: '',
				shopid:''//
			};
		},
		props: {},
		onLoad(options) {
			console.log("options==>", options);
			this.advertisement("GIFT_CARD")
			if (options.hasOwnProperty('q') && options.q) {
				//对参数进行解码
				const tempUrl = decodeURIComponent(options.q);
				//对解码后的参数进行拆分组装，获取要跳转的路径
				console.log("tempUrl=====",tempUrl);
				const id = util.UrlParamHash(tempUrl.split('?')[1], 'id');
				console.log("====id ====",id);
				const shopid = util.UrlParamHash(tempUrl.split('?')[1], 'shopid');
				console.log("====shopid====",shopid);
				if (id>0) {
					this.loopUser(id);
				}
				if(shopid>0){
					this.shopid = shopid;
				}
			}
			
			//直接获取礼品卡参数绑定
			if (options.id) {
				let sence = decodeURIComponent(options.id);
				this.loopUser(sence);
			}

			//直接获取礼品卡参数绑定的店铺id
			if (options.shopid) {
				let shopid = decodeURIComponent(options.shopid);
				this.shopid = shopid;
			}
		},

		methods: {
			//广告
			advertisement(id) {
				//绑卡banner
				api.advertisementinfo({
					bigClass: id
				}).then(res => {
					this.payment = res.data;
				});
			},
			//登录校验
			loopUser(sence) {
				const user_info = uni.getStorageSync("user_info");
				this.bindCard = sence;
				if (!user_info) {
					setTimeout(() => {
						this.loopUser(sence);
					}, 0);
					return;
				}
				const {
					id
				} = user_info;
				if (!id) {
					uni.navigateTo({
						url: "/pages/login/index?reUrl=" +
							encodeURIComponent(
								`/pages/gift/new-card/index?id=${sence}&shopid=${this.shopid}`
							),
					});
				} else {
					this.showIsodalDialog();
				}
			},

			// 绑卡弹框按钮事件
			bind() {
				//msg 没值跳转我的礼品卡 有值则重新绑定
				if (!this.msg) {
					// 有店铺ID的激活后就跳去店铺首页
					let url = this.shopid ? '/pages/shop/shop-detail/index?id=' + this.shopid : '/pages/gift/my-gift-card/index';
    			    uni.redirectTo({ url });
				}else if(this.shopid>0){
					let url = '/pages/shop/shop-detail/index?id=' + this.shopid;
					uni.redirectTo({ url });
				}
				this.bindCard = ''
				this.hideModalDialog()
			},

			//绑定新卡
			bindNewCard(e) {
				console.log("e===>", e.detail.value);
				this.$nextTick(() => {
					this.bindCard = e.detail.value
				})
			},

			//扫码
			scanCode() {
				let that = this;
				// #ifdef MP-WEIXIN
				// 允许从相机和相册扫码
				uni.scanCode({
					scanType: ["barCode", "qrCode"], //所扫码的类型 barCode	一维码 qrCode	二维码
					success: (res) => {
						if (res.result) {
							const code = res.result; //result	所扫码的内容 测试：6902902006811
							console.log("code", code)
							if (code) {
								console.log("code--->", code)
								if(code&&code.startsWith("https://shopapi.songlei.com/slshop-h5/ma/giftCardActivation")){
									const tempUrl =decodeURIComponent(code);
									//对解码后的参数进行拆分组装，获取要跳转的路径
									const id = util.UrlParamHash(tempUrl.split('?')[1], 'id');
									const shopid = util.UrlParamHash(tempUrl.split('?')[1], 'shopid');
									if (id>0||id) {
										that.loopUser(id);
									}
									if(shopid || shopid>0){
										that.shopid = shopid;
									}
									
								}else {
									// this.bindCard = code
									uni.showToast({
										title: `该条码不符合要求`,
										icon:"none",
										duration: 2000
									});
								}
								
							}
						} else {
							console.log('请重新扫描');
							return false;
						}
					},
					fail: (res) => {
						console.log('未识别到二维码');
					}
				})
				// #endif   


				// #ifdef APP-PLUS
				// 允许从相机和相册扫码
				mpaasScanModule.mpaasScan({
					// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
					'scanType': ['qrCode', 'barCode'],
					// 是否隐藏相册，默认false不隐藏
					'hideAlbum': false
				}, (res) => {
					if (res.resp_code == 1000 && res.resp_message == 'success') {
						const code = res.resp_result; //result	所扫码的内容
						if (code) {
							if(code&&code.startsWith("https://shopapi.songlei.com/slshop-h5/ma/giftCardActivation")){
								const tempUrl =decodeURIComponent(code);
								//对解码后的参数进行拆分组装，获取要跳转的路径
								const id = util.UrlParamHash(tempUrl.split('?')[1], 'id');
								const shopid = util.UrlParamHash(tempUrl.split('?')[1], 'shopid');
								if (id || id>0) {
									that.loopUser(id);
								}
								if(shopid||shopid>0){
									that.shopid = shopid;
								}
								
							}else {
								// this.bindCard = code
								uni.showToast({
									title: `该条码不符合要求`,
									icon:"none",
									duration: 2000
								});
							}
						} else {
							uni.showToast({
								title: `未识别到内容`,
								icon:"none",
								duration: 2000
							});
						}
					} else {
						// uni.showToast({
						// 	title: `未识别到信息`,
						// 	icon:"none",
						// 	duration: 2000
						// });
					}
				})
				// #endif   
			},

			//复制
			copyCode(value) {
				console.log("111111", value);
				uni.setClipboardData({
					data: value,
					success: () => {
						console.log('success');
						uni.showToast({
							title: "复制成功"
						})
					}
				});
			},

			//关闭弹框
			hideModalDialog(num) {
				console.log("222");
				this.modalDialog = false;
				this.modalScanDialog = false;
				this.isodalDialog = false
			},

			//打开帮助弹框
			showModalSku() {
				this.modalDialog = true;
			},

			//打开扫描弹框
			showModalScan() {
				this.modalScanDialog = true;
			},

			//绑定成功或错误弹框
			showIsodalDialog() {
				if (!this.bindCard) {
					uni.showToast({
						title: '卡号密码或涂层卡密不能为空！',
						icon: 'none',
						duration: 3000
					});
					return
				}
				bindCardCode({
					cardpwd: this.bindCard
				}).then((res => {
					console.log("res==>", res);
					if (res.code == 0) {
						this.msg = ''
					} else {
						this.msg = res.msg
					}
					this.isodalDialog = true
				})).catch((err) => {
					console.log('err==>', err);

				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.cu-list.grid.no-border {
		padding: 20rpx 55rpx;
	}

	.line {
		width: 618rpx;
		height: 1rpx;
		margin: 30rpx auto;
		border-bottom: 1px dashed #999;
		background-color: #ccc;
	}

	.btn {
		width: 343rpx;
		height: 80rpx;
		line-height: 80rpx;
		border-radius: 40rpx;
		margin: 0 auto;
	}

	.icon {
		color: #DAB691;
		width: 100rpx;
		height: 42rpx;
		line-height: 42rpx;
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		text-align: center;
		font-size: 40rpx;
		border-left: dotted;
	}

	.carmi {
		position: relative;
		margin: 165rpx auto;
		margin-bottom: 30rpx;
		width: 611rpx;
		height: 80rpx;
		background: #FFFFFF;
		border-radius: 40rpx;
	}

	.my-gift-card {
		width: 135rpx;
		height: 50rpx;
		position: absolute;
		top: 50rpx;
		right: -10rpx;
		color: #FFF;
		text-align: center;
		line-height: 50rpx;
		background: #000000;
		opacity: 0.5;
		border-radius: 25rpx;

	}
</style>