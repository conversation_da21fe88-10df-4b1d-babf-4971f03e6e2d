<template>
	<view class="padding-bottom">
		<cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">我的礼品卡</block>
		</cu-custom>
		<!-- 余额 -->
		<view class="cu-bar fixed bg-white" :style="{ marginTop: CustomBar + 'px' }" style="background-color: #f5f5f7">
			<view class="relative">
				<image style="width: 750upx; height: 186upx" src="https://img.songlei.com/gift/gift-card.png"></image>

				<view style="width: 90%; top: 10rpx; left: 0; right: 0; margin: 0 auto" class="absolute flex justify-between padding-sm">
					<view style="margin: auto; margin-right: 80rpx">
						<view class="text-xsm text-bold text-black flex justify-center align-baseline" style="margin-top: 12rpx">
							余额
							<format-price :price="validAmt" priceFontSize="44rpx" />
						</view>
						<view class="text-black" style="font-size: 22rpx">礼品卡数量：{{ totCnt }}张</view>
					</view>

					<view>
						<view class="my-gift-card" @tap="Usagerules()">帮助说明</view>
					</view>
				</view>

				<!-- tap切换 -->
				<view class="flex text-center" style="background-color: #f5f5f7">
					<view
						class="cu-item flex-sub text-lg"
						:class="index == tabCur ? 'text-color' : ''"
						style="padding: 0rpx 20rpx; height: 60rpx; line-height: 60rpx"
						v-for="(item, index) in giftStatus"
						:key="index"
						@tap="tabSelect"
						:data-index="index"
						:data-key="item.key"
					>
						<view>
							{{ item.value }}({{ item.num }})
							<view v-if="index == tabCur" class="line"></view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 商品信息 -->
		<view :style="{ marginBottom: '42px' }" style="margin-top: 270upx">
			<view class="margin-top-xs" v-for="(item, index) in myGiftCardListPages" :key="index">
				<view class="margin-top-xs" style="position: relative">
					<view class="flex justify-around padding-xs margin-top-xs" style="width: 95%; height: 100%; background: #ffffff; margin: 0 auto; border-radius: 15rpx">
						<view style="width: 180upx; height: 180upx" @click="navto(item.cardNo)">
							<image
								style="width: 100%; height: 100%; border-radius: 15rpx; border: 1px solid #ccc"
								:src="item.imgUrl | formatImg360"
								mode="aspectFill"
								class="row-img"
							></image>
						</view>

						<view class="padding-lr-sm" style="flex: 1; position: relative">
							<view class="flex justify-start align-center">
								<view class="text-black overflow-2" style="width: 350rpx">
									{{ item.cdtName }}
								</view>
							</view>

							<view class="text-light-gray text-xs">{{ item.minDate }}-{{ item.maxDate }}</view>

							<view class="text-sm" style="width: 350rpx; color: #ef9416">
								{{ item.scope }}
							</view>

							<view @tap.stop="showModalSku(item.cardNo)" class="text-xs" style="position: absolute; top: 0px; right: 0px">
								消费记录
								<text class="cuIcon-right margin-left-xs"></text>
							</view>

							<view class="flex justify-between align-center text-sm">
								<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
									<view class="text-black">面值:</view>
									<format-price :styleProps="priceStyle2" signFontSize="22rpx" color="#000" :price="item.faceValue" priceFontSize="24rpx" smallFontSize="20rpx" />
								</view>

								<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
									<view class="text-black text-bold">余额:</view>
									<format-price :styleProps="priceStyle" signFontSize="18rpx" color="#000" :price="item.balance" priceFontSize="24rpx" smallFontSize="20rpx" />
								</view>
							</view>
						</view>
						<view class="without" v-if="tabCur == '1'" @tap.stop="showModalSku(item.cardNo)">
							<image style="width: 160rpx; height: 160rpx" src="https://img.songlei.com/gift/without.png" mode="aspectFill" class="row-img"></image>
						</view>
					</view>
				</view>
			</view>
			<view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
		</view>

		<!-- 购卡 绑卡-->
		<view class="cu-bar bg-white tabbar shop foot">
			<view style="display: flex; justify-content: space-between; margin: 0 auto">
				<view @click="toPage('/pages/shop/shop-detail/index?cartType=3&id=' + shopId)" style="width: 225rpx; height: 78upx" class="cu-btn round btnBackground text-white">
					购买新卡
				</view>

				<navigator
					hover-class="none"
					open-type="navigate"
					:url="'/pages/gift/new-card/index'"
					style="width: 225rpx; height: 78upx"
					class="cu-btn round margin-left-sm btnCenterBackground text-white"
				>
					绑定新卡
				</navigator>

				<view style="width: 225rpx; height: 78upx" @click="navto()" class="cu-btn round margin-left-sm btnRightBackground text-white">去支付</view>
			</view>
		</view>

		<!-- 消费记录 -->
		<view class="cu-modal" :class="modalDialog ? 'show' : ''" @tap="hideModalDialog">
			<view class="cu-dialog bg-white" :class="modalDialog ? 'animation-slide-bottom' : ''" style="height: 670upx; border-radius: 30rpx" @tap.stop>
				<view class="cu-card article no-card" style="padding-top: 20upx">
					<view class="text-xl close-icon text-center padding-left-sm padding-right-sm">
						<text class="text-df text-black">卡消费记录</text>
					</view>
					<!-- 关闭弹框icon -->
					<text
						class="cuIcon-close"
						style="color: #000; background-color: #eee; position: absolute; top: 3%; right: 3%; border: 1rpx solid #fff; width: 40rpx; border-radius: 50%"
						@tap="hideModalDialog"
					></text>
				</view>

				<!-- 卡信息 -->
				<scroll-view scroll-y="true" style="max-height: 540upx" @scrolltolower="billScrolltolower" class="bg-white">
					<view class="margin-lr" v-if="billList && billList.length">
						<view class="flex justify-start margin-top-xs text-sm">
							<view class="text-black text-sm text-bold">卡号：</view>
						</view>

						<view @click.stop="copyCode(billList[0].cardNo)" class="flex justify-start text-sm">
							<view class="text-black text-sm text-bold text-light-gray">
								{{ billList[0].cardNo }}
								<text class="text-black margin-left">复制</text>
							</view>
						</view>

						<view style="height: 1px; margin: 20rpx 0; background: #ccc"></view>
					</view>

					<view class="padding-lr" v-for="(item, index) in billList" :key="index">
						<view style="max-height: 60vh">
							<view @click.stop="copyCode(item.billNo)" class="text-purple-grey flex justify-between margin-top-xs text-sm">
								<view class="text-black">{{ item.channel }}订单：</view>
								<view class="text-light-gray">
									{{ item.billNo }}
									<text class="text-black margin-left-xs">复制</text>
								</view>
							</view>

							<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
								<view class="text-black">日期：</view>
								<view class="text-light-gray">{{ item.occurDate }}</view>
							</view>

							<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
								<view class="text-black">{{ item.statusDesc }}</view>
								<view>
									<format-price
										:styleProps="priceStyle"
										signFontSize="18rpx"
										:color="String(item.amount).startsWith('-') ? '#e54d42' : '#000'"
										:price="`${item.amount}`"
										priceFontSize="28rpx"
									/>
								</view>
							</view>

							<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
								<view class="text-black">余额：</view>
								<view class="text-red">
									<format-price :styleProps="priceStyle" signFontSize="18rpx" color="#000" :price="item.cdlYe" priceFontSize="28rpx" />
								</view>
							</view>

							<view style="height: 1px; margin: 20rpx 0; background: #ccc"></view>
						</view>
					</view>

					<view :class="'cu-load ' + (loadmoreRecommendList ? 'loading' : 'over')"></view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp();

import api from 'utils/api';
// import jweixin from '@/utils/jweixin'
import util from '@/utils/util';
import formatPrice from '@/components/format-price/index.vue';
import { myGiftCardList, getRecords } from '@/pages/gift/api/gift';
import { navigateUtil } from '@/static/mixins/navigateUtil.js';

import { mapState } from 'vuex';
export default {
	mixins: [ navigateUtil],
	components: {
		formatPrice
	},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			//有数统计使用
			page_title: '我的礼品卡',
			modalDialog: false, //控制弹框
			giftStatus: [
				{ value: '可用卡', key: '0', num: '0' },
				{ value: '不可用卡', key: '1', num: '0' }
			],
			priceStyle: 'display: flex; justify-content: center; font-weight: bold; align-items: baseline;',
			priceStyle2: 'display: flex; justify-content: center; align-items: baseline;',
			tabCur: 0,
			parameter: {},
			orderList: [],
			myGiftCardListPages: [], //我的礼品卡
			billList: [
				//   {
				//     id:'123456',
				//     createTime: "2023-03-10 12:38:54",
				//     pic:900.00,
				//     createPic:520.00,
				// },
				// {
				//     id:'1234567',
				//     createTime: "2023-03-10 12:38:54",
				//     pic:900.00,
				//     createPic:520.00,
				// },{
				//     id:'1234568',
				//     createTime: "2023-03-10 12:38:54",
				//     pic:900.00,
				//     createPic:520.00,
				// }
			], //卡消费记录
			page: {
				status: 'Y',
				pageNo: 1,
				pageSize: 10
			},
			billPage: {
				pageNo: 1, //页码
				pageSize: 10 //最多20条
			},
			loadmore: true,
			validAmt: 0, //总余额
			totCnt: 0, //礼品卡数量
			loadmoreRecommendList: true,
			shopId: ''
		};
	},
	props: {},
	onLoad(options) {},
	mounted() {},
	filters: {},

	onShow() {
		uni.removeStorageSync('cardNo');
		app.initPage().then((res) => {
			this.refresh();
		});
	},

	onReachBottom() {
		if (this.loadmore) {
			this.page.pageNo = this.page.pageNo + 1;
			this.myGiftCardList();
		}
	},

	onPullDownRefresh() {
		// 显示顶部刷新图标
		uni.showNavigationBarLoading();
		this.refresh(); // 隐藏导航栏加载框
		uni.hideNavigationBarLoading(); // 停止下拉动作
		uni.stopPullDownRefresh();
	},
	created() {
		this.shopId = uni.getStorageSync('paypaymentuserpassword')?.shopId;
	},

	computed: {
		...mapState(['windowWidth', 'HeightBar', 'CustomBar', 'menuWidth', 'leftMenuWidth', 'pixelRatio'])
	},

	methods: {
		//去付款
		navto(num) {
			// 不可用状态不跳转
			if (this.tabCur == '1') return;
			if (num) {
				let cardNo = num;
				uni.setStorageSync('cardNo', cardNo);
			}
			let isActive = uni.getStorageSync('paypaymentuserpassword')?.isActive;
			// isActive 0 没有开通去开通
			if (isActive == '0') {
				uni.navigateTo({
					url: `/pages/gift/set-password/code/index`
				});
				return;
			} else {
				// 付款码
				uni.navigateTo({
					url: `/pages/gift/payment-code/index`
				});
			}
		},
		//规则跳转
		Usagerules() {
			console.log('出发了');
			uni.navigateTo({
				url: '/pages/goods/goodsrules/goodsrules?pageName=gift'
			});
		},
		//我的礼品卡列表
		myGiftCardList() {
			myGiftCardList(Object.assign({}, this.page)).then((res) => {
				console.log('myGiftCardList==res==>', res);
				if (res.data) {
					if (res.data.cardList) {
						let myGiftCardListPages = res.data.cardList;
						this.myGiftCardListPages = [...this.myGiftCardListPages, ...myGiftCardListPages];
						if (myGiftCardListPages.length < this.page.pageSize) {
							this.loadmore = false;
						}
					}
					let { validAmt, totCnt, invalidCnt, validCnt } = { ...res.data };
					this.totCnt = totCnt || 0;
					this.validAmt = validAmt || 0;
					this.$set(this.giftStatus[0], 'num', validCnt);
					this.$set(this.giftStatus[1], 'num', invalidCnt);
					console.log('validAmt', validAmt, 'totCnt', totCnt);
				}
			});
		},

		//复制
		copyCode(value) {
			console.log('111111', value);
			uni.setClipboardData({
				data: value,
				success: () => {
					console.log('success');
					uni.showToast({
						title: '复制成功'
					});
				}
			});
		},

		//关闭弹框
		hideModalDialog(num) {
			console.log('222');
			this.modalDialog = false;
		},

		//打开弹框
		showModalSku(cardNo) {
			this.cardNo = cardNo;
			this.modalDialog = true;
			this.loadmoreRecommendList = true;
			this.billPage.pageNo = 1;
			this.billList = [];
			this.getCardbill();
		},

		//获取电子账单明细
		getCardbill() {
			// if (cardNo) {
			let pages = {
				pageNo: this.billPage.pageNo,
				pageSize: this.billPage.pageSize,
				cardNo: this.cardNo
			};
			getRecords(Object.assign({}, pages))
				.then((res) => {
					console.log('getCardbill', res);
					let giftCardList = res.data;
					this.billList = [...this.billList, ...giftCardList];
					console.log('giftCardList.length', giftCardList.length, giftCardList);
					if (giftCardList.length < this.billPage.pageSize) {
						this.loadmoreRecommendList = false;
					}
				})
				.catch((err) => {
					this.loadmoreRecommendList = false;
					console.log(err);
				});
			// }
		},

		//账单滚动到底部
		billScrolltolower() {
			if (this.loadmoreRecommendList) {
				this.billPage.pageNo = this.billPage.pageNo + 1;
				this.getCardbill();
			}
		},

		//研发中
		payCode() {
			uni.showToast({
				icon: 'none',
				title: '功能研发中...敬请期待!',
				duration: 2000
			});
		},

		refresh() {
			this.loadmore = true;
			this.myGiftCardListPages = [];
			this.page.pageNo = 1;
			this.myGiftCardList();
		},

		//tab切换
		tabSelect(e) {
			let dataset = e.currentTarget.dataset;
			if (dataset.index != this.tabCur) {
				this.tabCur = dataset.index;
				this.parameter.status = dataset.key;
				this.page.status = dataset.key == '1' ? 'N' : 'Y';
				this.refresh();
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.without {
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: #ffffff;
	z-index: 999;
	opacity: 0.6;
	text-align: right;
}
.btnRightBackground {
	background: linear-gradient(90deg, #f42634 0%, #ce1a26 100%);
}
.btnCenterBackground {
	background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%);
}
.btnBackground {
	background: linear-gradient(-90deg, #cdad90 0%, #cda185 100%);
}
.line {
	width: 108rpx;
	height: 4rpx;
	background: #af815f;
	border-radius: 2rpx;
	margin: 0px auto;
}
.text-color {
	// text-decoration: underline;
	color: #af815f;
}
.my-gift-card {
	border-radius: 15px;
	padding: 8rpx 20rpx 8rpx 20rpx;
	font-size: 26rpx;
	background: #ffffff;
	opacity: 0.62;
	color: #653006;
}
</style>