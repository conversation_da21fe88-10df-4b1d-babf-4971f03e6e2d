<template>
	<view class="relative" style="background-color:#F5F5F7">
			<cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">待领取礼品卡</block>
			</cu-custom>
			
				<!-- 单个礼品卡展示方式 -->
				<view v-if="giftCardList&&giftCardList.length">
					<view class="margin-top-xs" v-for="(ele, index) in giftCardList" :key="index">
						<view>
							<view
								class="padding-xs margin-top-xs"
								style="width:95%;height:100%;background: #FFFFFF;margin:0 auto;border-radius:15rpx;"
							>
								<view v-if="payment" class="prefer-scroll">
									<block class="cu-item"> 
										<view class="flex justify-between align-center ">
											<view style="width:180upx;height:180upx">
												<image 
													style="width:100%;height: 100%;border-radius:15rpx;"
													:src="payment.imgUrl | formatImg360" 
													mode="aspectFill" 
													class="row-img"
													>
												</image>
											</view>

											<view class="margin-top-xs margin-right-xs text-sm">
													<view class="flex justify-between" :class="'text-black text-bold'">总额:
                            <format-price :price="ele.totalFaceValue" color="#000000" signFontSize="22rpx" priceFontSize="26rpx" smallFontSize="24rpx"/>
                          </view>
                          <view class=" text-right">
                            <view class="lines-light-grey">共 {{ ele.quantity }}张</view>
                          </view>
											</view>
										</view>
									</block>
								</view>

                <view style="width: 682rpx;background: #E7E7E7;height: 2rpx;margin:20rpx auto;" ></view>

                <view class="flex justify-between align-center margin-xs">
									<view class="text-sm">赠予人:</view>

									<view class="text-sm">{{ele.nickName}}</view>
								</view>

								<view class="flex justify-between align-center margin-xs">
									<view class="text-sm">赠予人手机号:</view>

									<view class="text-sm">{{ele.userPhone}}</view>
								</view>

                <view class="flex justify-start margin-xs">
									<view class="text-sm" style="width:90rpx;">寄语:</view>

									<view class="text-sm overflow-2">{{ ele.wishWord }}</view>
								</view>

                <view v-if="ele&&ele.timestamp" class="flex justify-between align-center margin-xs">
									<view class="text-xs lines-light-grey" style="width:350rpx;">24小时内未领取将自动退还</view>

									<view class="text-red text-xs">仅剩
                    <count-down :outTime="ele.timestamp*1000" :fontSize="'14'" :textColor="'red'"
                      :backgroundColor="'#FFFFFF'" :connectorColor="'red'" @countDownDone="countDownDone">
                    </count-down>
                  </view>
								</view>

								<view
									class="flex justify-around align-center text-sm btn" 
									style="
										width: 370rpx;
										margin: auto;
										margin-top:20rpx;
                    margin-right:0rpx;
										">
									<view class="flex justify-between text-sm" @click="unpack(2,ele.id)">
										<view class="margin-left check text-black solid-black">退还</view>
									</view>
					
									<view @click="unpack(1,ele.id)" class="text-purple-grey">
										<view class=" align-center check text-sm" style="background-color:#EE112B;color:#FFFFFF">领取</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
				</view>

        <view v-else class="margin-top">
          <view
            class="padding-xs margin-top-xs text-center"
            style="width:95%;height:800rpx;background: #FFFFFF;margin:0 auto;border-radius:15rpx;"
          >
          <view style="margin:0 auto; margin-top:206rpx">
            <view style="width:257upx;height:236upx;margin:0 auto;">
					  	<image 
					  		style="width:100%;height: 100%;border-radius:15rpx;"
					  		:src="'https://img.songlei.com/gift/unclaimed.png' | formatImg360" 
					  		mode="aspectFill" 
					  		class="row-img"
					  		>
					  	</image>
					  </view>

            <view class="text-sm lines-light-grey text-center" style="width: 100%;">
              您当前没有未领取的礼品卡!
            </view>
          </view>  
            <!-- 购卡 绑卡-->
            <view class="bg-white " style="margin-top:180rpx">
              <view class="flex justify-center">
                <navigator hover-class="none" open-type="navigate" :url="'/pages/gift/gift-card/index'"  style="width: 293rpx;height: 80rpx;"  class="cu-btn round btnBackground"> 
                  关闭
                </navigator>
    
                <view @click="toPage('/pages/gift/give/list/index')" style="width: 293rpx;height: 80rpx;"  class="cu-btn round margin-left btnCenterBackground">
                我也要送
                </view>
              </view>
            </view>
          </view>
        </view>
	</view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
  import {collect,revoke,getByReceivePhones } from '@/pages/gift/api/gift'
  import { navigateUtil } from "@/static/mixins/navigateUtil.js";
	import formatPrice from "@/components/format-price/index.vue";

  import countDown from "@/components/count-down/index";

  import {mapState} from 'vuex'
	export default {
		mixins: [navigateUtil],
		components: {
			formatPrice,
      countDown
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '我的礼品卡',
        modalDialog: false,//控制弹框
				tabCur:'',
				parameter: {},
				giftCardList:[],//礼品卡数据列表
				loadmore: true,
				page: {
					type: 2,
          current: 1,
          size: 10,
        },
        payment:'',
        userInfo:{},//获取用户信息
			};
		},
		props: {},
		onLoad(options) {
		},
		mounted() {
		},
		filters: {
		},

		onShow() {
      app.initPage().then(res => {
        this.advertisement("GIFT_UNCLAIMED")
        this.userInfo = uni.getStorageSync('user_info')
				this.refresh()
			});
		},

    created(){
    },

		onReachBottom () {
        if (this.loadmore) {
          this.page.current = this.page.current + 1;
          this.giftPage();
        }
      },

    onPullDownRefresh () {
      // 显示顶部刷新图标
      uni.showNavigationBarLoading();
      this.refresh(); // 隐藏导航栏加载框
      uni.hideNavigationBarLoading(); // 停止下拉动作
      uni.stopPullDownRefresh();
    },

		computed: {
      ...mapState([
				'windowWidth',
				'HeightBar',
				'CustomBar',
				'menuWidth',
				'leftMenuWidth',
				'pixelRatio'
			])
    },

		methods: {
      countDownDone() {
				this.giftPage();
			},
      //广告
			advertisement(id) {
				//绑卡banner
        api.advertisementinfo({
          bigClass: id
        }).then(res => {
          this.payment = res.data;
        });
			},
      //拆开1-接收 2-拒收
      async unpack(type,id){
            console.log("type",type,id );
            try {
                let data={
                    transferId :id,
                    mobile:this.userInfo.phone,
                    operType:type
                }
                const res = await collect(data)
                let code = res.code
                if (code=='0') {
                    if (type===1) {
                      // 点击领取，跳转到领取礼品卡页面;
                      uni.navigateTo({
                        url: `/pages/gift/give/receive/index?id=${id}`
                      })
                    }else{
                      // 点击退还，刷新待领取礼品卡页面
                        this.refresh()
                    }
                }
            }catch(err){
                console.log(err);
            }
        } ,

			//我的礼品卡列表
      giftPage(){
        getByReceivePhones().then((res)=>{
          console.log("giftPage==res==>",res);
          if (res.data) {
            let giftPage=res.data
            this.giftCardList = giftPage;
            // this.giftCardList = [...this.giftCardList, ...giftPage];
            console.log("giftPage.length < this.page.pageSize",giftPage.length < this.page.pageSize);
            if (giftPage.length < this.page.size) {
              this.loadmore = false;
            }
          }
        })
      },
			// 重置数据
			refresh() {
				this.loadmore = true;
				this.giftCardList = [];
				this.page.current = 1;
				this.giftPage()
			},
    }
	};
</script>

<style lang="scss" scoped>
.btnCenterBackground{
  background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
  color: #FFFFFF;
}
.btnBackground{
  background:#FFFFFF;
  border:1px solid #FF5002;
  color: #FF5002;
}
.solid-black{
	border:1px solid #ccc;
}
.solid-red{
  border: 1px solid red;
}
.prefer-scroll {
  white-space: nowrap;
  width: 100%;
}
.check{
	height: 66rpx;
  width: 168rpx;
  line-height: 66rpx;
	text-align: center;
  margin:2rpx;
  border-radius: 35rpx;
  background-color: #fff;
}
.giveList {
  display: inline-block;
  padding-right: 40rpx;
  text-align: center;
}

.btn {
  height: 70rpx;
  width: 340rpx;
  line-height: 70rpx;
  margin: 19rpx 20rpx 20rpx 25rpx;
  border-radius: 35rpx;
  font-size: 30rpx;
  background-color: #fff;
}

</style>
