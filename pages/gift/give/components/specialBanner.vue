<template>
  <div class="banner-container">
    <swiper :style="{width: '100vw', height: '420rpx'}" 
      :indicator-dots="swiperConfig.indicatorDots" 
      :indicator-color="swiperConfig.indicatorColor" 
      :indicator-active-color="swiperConfig.indicatorActiveColor"
      :autoplay="swiperConfig.autoplay" 
      :interval="swiperConfig.interval" 
      :duration="swiperConfig.duration" 
      :circular="swiperConfig.circular"
      :current="curIndex"
      :previous-margin="swiperConfig.previousMargin"
      :next-margin="swiperConfig.nextMargin"
      @change="swiperChange" 
      @animationfinish="animationfinish">
      <swiper-item v-for="(item, i) in bannerList" :key="i">
		<!-- 1.当前展示为第1项时，bannerList最后一项和第二项的justifyContent值分别为flex-end和flex-start，其余项值为center -->
		<!-- 2.当前展示为最后一项时，bannerList倒数第2项和第1项的justifyContent值分别为flex-end和flex-start，其余项值为center -->
		<!-- 3.当前展示为其他项（非第1和最后1项）时，bannerList当前项的前1项和后1项的justifyContent值分别为flex-end和flex-start，其余项值为center -->
		<!-- 4.padding值也需要根据不同项设定不同值，但理同justifyContent -->
        <div class="image-container" 
			:class="[curIndex===0?((i===listLen-1)?'item-left':(i===1?'item-right':'item-center')):(curIndex===listLen-1?(i===0?'item-right':(i===listLen-2?'item-left':'item-center')):(i===curIndex-1?'item-left':(i===curIndex+1?'item-right':'item-center')))]">
          <image :src="item.image" 
            class="slide-image" 
            :style="{
              transform: curIndex===i?'scale(' + scaleX + ',' + scaleY + ')':'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              borderRadius:'30rpx'
            }" 
            @click="getBannerDetail(i,item.image)"/>
        </div>
      </swiper-item>
    </swiper>
    <div class="desc-wrap" :class="[isDescAnimating?'hideAndShowDesc':'']" :style="{height:totalText?'130rpx':'100rpx'}">
      <div v-if="totalText" class="title">{{totalText}}</div>
      <div :class="totalText?'desc':'title'">{{bannerList[descIndex].title}}</div>
    </div>

    <!--多图点击联动商品大图-->
    <view v-if="minList&&minList.length>1">
      <view class="givescroll" :style="{'text-align':'center'}">
        <scroll-view class="scroll-top" scroll-x="true" show-scrollbar="false" :scroll-into-view="seqToView">
            <view class="scroll-view-item" v-for="(item,index) in minList" :key="index" :id="'seq-' + index" @click="tabClick(index,item.image)">
                <view :class="curIndex == index?'solid-red':''" class="scroll-view-item-view">
                    <image class="scroll-top-item-image" :src="item.image"></image>
                </view>
            </view>
        </scroll-view>
      </view>
    </view>
  </div>
</template>
<script>
export default {
  props: {
    // swiper的图片数据
    bannerList: {
      type: Array,
      default () {
        return []
      }
    },
    // swiper的默认配置项
    swiperConfig: {
      type: Object,
      default () {
        return {
          indicatorDots: false,
          indicatorColor: 'rgba(255, 255, 255, .4)',
          indicatorActiveColor: 'rgba(255, 255, 255, 1)',
          autoplay: false,
          interval: 3000,
          duration: 300,
          circular: true,
          previousMargin: '58rpx',//前边距，可用于露出前一项的一小部分，接受 px 和 rpx 值
          nextMargin: '58rpx'//后边距，可用于露出后一项的一小部分，接受 px 和 rpx 值
        }
      }
    },
    // 图片在X轴展示的效果
    scaleX: {
      type: String,
      default: (410 / 317).toFixed(4)
    },
    // 图片在Y轴展示的效果
    scaleY: {
      type: String,
      default: (410 / 317).toFixed(4)
    },
    // 总数文案
    totalText:{
      type: String,
      default: ''
    },
    //小图列表
    minList: {
      type: Array,
      default () {
        return []
      }
    },
  },
  computed:{
    listLen () {
      return this.bannerList.length
    }
  },
  data () {
    return {
      curIndex: 0,
      descIndex: 0,
      currentSelect: 0,//选中标记
      seqToView: '', // 滚动至序号
      isDescAnimating: false
    }
  },
  methods: {
    // 多图点击联动商品大图
    tabClick(index,url) {
        if (index == this.currentSelect) {
            return
        } else {
          console.log("2222222");
          
          this.minList[index].image = url
          // this.minList[this.currentSelect].image = url
          this.currentSelect = index
          this.curIndex = index
        }
      },
    //大图轮播列表
    swiperChange (e) {
      const that = this
      this.curIndex = e.mp.detail.current
      let url = this.bannerList[this.curIndex].image
      console.log(this.curIndex,this.bannerList[this.curIndex].image)
      this.isDescAnimating = true
      this.tabClick(this.curIndex,url)
      this.seqToView = 'seq-' + this.curIndex // 滚动至选中序号位置
      let timer = setTimeout(function () {
        that.descIndex = e.mp.detail.current
        clearTimeout(timer)
      }, 150)
    },

    animationfinish (e) {
      this.isDescAnimating = false
    },

    getBannerDetail (index,url) {
      this.tabClick(index,url)
	  // uni.showLoading({
		// title: '将前往详情页面',
		// duration: 2000,
		// mask: true
	  // })
    }
  }
}
</script>
<style lang="scss" scoped>
.banner-container {
  width: 100vw;
  height: 524rpx;
  .image-container {
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	display: flex;
	.slide-image {
	  width: 317rpx;
	  height: 317rpx;
	  z-index: 200;
	}
  }
  .item-left {
	justify-content: flex-end;
	padding: 56rpx 50rpx 0 0;
  }
  .item-right {
	justify-content: flex-start;
	padding: 56rpx 0 0 26rpx;
  }
  .item-center {
	justify-content: center;
	padding: 56rpx 0 0 0;
  }
  .desc-wrap {
    box-sizing: border-box;
    width: 100%;
    height: 135rpx;
    padding: 24rpx 66rpx 0;
    .title {
      width: 100%;
      height: 42rpx;
      line-height: 42rpx;
      color: #222222;
      font-size: 30rpx;
      font-family: 'PingFangTC-Regular';
      font-weight: 600;
      // text-align: left;
    }
    .desc {
      margin-top: 4rpx;
      width: 100%;
      height: 51rpx;
      line-height: 51rpx;
      color: #282828;
      font-size: 24rpx;
      font-family: 'PingFangTC-Regular';
      // text-align: left;
    }
  }
  @keyframes descAnimation {
    0% {
      opacity: 1;
    }
    25% {
      opacity: .5;
    }
    50% {
      opacity: 0;
    }
    75% {
      opacity: .5;
    }
    100% {
      opacity: 1;
    }
  }
  @-webkit-keyframes descAnimation {
    0% {
      opacity: 1;
    }
    25% {
      opacity: .5;
    }
    50% {
      opacity: 0;
    }
    75% {
      opacity: .5;
    }
    100% {
      opacity: 1;
    }
  }
  .hideAndShowDesc {
    animation: descAnimation .3s ease 1;
    -webkit-animation: descAnimation .3s ease 1;
  }
  .givescroll {
    width: 680rpx;
    padding: 16rpx 0rpx 17rpx 0rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
    margin: auto;
    
  .scroll-top {
        // margin: 34rpx 54rpx 0 0;
        white-space: nowrap;
        width: 100%;
        .scroll-view-item {
          display: inline-block;
          padding: 0 13rpx;
          font-size: 32rpx;
          color: #666666;
          line-height: 74rpx;
          position: relative;
      }

      .scroll-view-item-view {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex-wrap: wrap;
      }
      .solid-red{
        border: 1px solid red;
      }
      .scroll-top-item-image {
        width: 164rpx;
        height: 164rpx;
      }
    }
  }
}
</style>
