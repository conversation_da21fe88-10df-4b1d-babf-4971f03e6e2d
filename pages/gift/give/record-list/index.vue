<template>
	<view class="relative" style="background-color:#F5F5F7">
			<cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">收送记录</block>
			</cu-custom>

			<!-- 按钮收到，送出类型 -->
			<view class=" nav fixed collection-types" style="background-color:#F5F5F7" >
				<!-- 一级导航菜单 -->
				<scroll-view scroll-x >
					<view class="flex text-center">
						<view 
							:class="'btn ' +(item.key == type ? 'cur bg-' + theme.backgroundColor : '')" 
							v-for="(item, index) in giftType" :key="index" 
							@tap="tabSelect" 
							:data-index="index"
							:data-key="item.key">
							{{ item.value }}
						</view>
					</view>
				</scroll-view>
				<!-- 送出展示的状态二级导航菜单 -->
				<template v-if="type=='1'">
					<scroll-view scroll-x >
						<view class="flex text-center">
							<view 
								class="cu-item flex-sub text-xsm "
								:class="index == tabCur ? 'text-' + theme.themeColor : ''"
								style="padding: 0rpx 20rpx; height: 77rpx; line-height: 77rpx;"
								v-for="(item, index) in giftStatus"
								:key="index"
								@tap="twoTabSelect"
								:data-index="index"
								:data-key="item.key"
							>{{ item.value }}</view>
						</view>
					</scroll-view>
				</template>
			</view>
			
				<!-- 单个礼品卡展示方式 -->
				<view :style="{marginTop: type=='1'?'200rpx' : '120rpx'}">
					<view class="margin-top-xs" v-for="(ele, index) in giftCardList" :key="index">
						<view v-if="ele.listUserGiftCardTransferItem&&ele.listUserGiftCardTransferItem.length=='1'">
							<view class="margin-top-xs" v-for="(item, index1) in ele.listUserGiftCardTransferItem" :key="index1" style="position: relative;">
								<view class="flex justify-around padding-xs margin-top-xs"
										style="width:95%;height:100%;background: #FFFFFF;margin:0 auto;border-radius:15rpx;">
										<view style="width:180upx;height:180upx">
											<image style="width:100%;height: 100%;border-radius:15rpx;border:1px solid #ccc;"
												:src="item.picUrl | formatImg360" mode="aspectFill" class="row-img">
											</image>
										</view>
				
										<view class="padding-lr-sm" style="flex:1;position: relative;">
											<view class="flex justify-start align-center">
												<view class="text-black overflow-2 text-bold text-sm" style="width:350rpx;">
													{{item.name}}
												</view>
											</view>
				
											<view class="text-sm" style="width:350rpx;">
												{{type=='2'?'来自好友：':(ele.receiveUserName?'赠予好友：':'')}} {{ type=='2'?ele.nickName: (ele.receiveUserName?ele.receiveUserName:'')}}
											</view>

											<view class="text-light-gray text-xs">
												{{type=='2'?'接收日期：':'送出日期：'}} {{ type=='2'? ele.updateTime : ele.createTime }}
											</view>
				
											<view v-if="type=='1'" class="text-sm" style="position:absolute;top: 0rpx;right: 6rpx;" :class="ele.status=='0'?'text-red':''">
													{{ele.status|statusType}}
											</view>
				
											<view class="flex justify-start align-center text-sm">
												<view class="text-purple-grey flex justify-between margin-top-xs text-sm margin-right-xl">
													<view class="text-black">张数: {{ ele.quantity }}</view>
												</view>
					
												<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
													<view class="text-black">面值:</view>
													<format-price :styleProps="priceStyle" signFontSize="18rpx" color="#EE112B" :price="item.faceValue"
													priceFontSize="24rpx" smallFontSize="20rpx"/>
												</view>
											</view>

											<view
												v-if="ele.status=='2'"
												class="flex justify-around align-center text-sm btn" 
												style="
													background: #F5F5F5;
													width: 450rpx;
													margin: auto;
													margin-top:20rpx;
													margin-right:10rpx;
													">
												<view class="text-purple-grey flex justify-between text-sm margin-right">
													<view class="text-red margin-left">已退回我的礼品卡中</view>
												</view>
								
												<navigator url="/pages/gift/my-gift-card/index" hover-class="none" class="text-purple-grey">
													<view class="text-black align-center check text-sm ">去查看</view>
												</navigator>
											</view>

											<view
												v-if="ele.status=='0'"
												class="flex justify-around align-center text-sm btn" 
												style="
													background: #F5F5F5;
													width: 172rpx;
													margin: auto;
													margin-top:20rpx;
													margin-right:10rpx;
													">
												<view @click="giftRevoke(ele.id)" class="text-purple-grey">
													<view class="text-black align-center check text-sm ">取消转赠</view>
												</view>
											</view>
										</view>
								 </view>
							</view>
						</view>
						
						<view v-else>
							<view
								class="padding-xs margin-top-xs"
								style="width:95%;height:100%;background: #FFFFFF;margin:0 auto;border-radius:15rpx;"
							>
								<view class="flex justify-between align-center margin-xs">
									<!-- <view class="text-black overflow-1 text-bold text-sm" style="width:473rrpx;">
										{{ele.listUserGiftCardTransferItem[0].name}}
									</view> -->
									<swiper 
										style="width: 473rpx;height: 35rpx;" 
										:indicatorDots="swiperConfig.indicatorDots"
										:autoplay="swiperConfig.autoplay" 
										:interval="swiperConfig.interval" 
										:duration="swiperConfig.duration" 
										:circular="swiperConfig.circular"
										
										@change="swiperChange" 
										>
										<swiper-item v-for="(swiperItem, i) in ele.listUserGiftCardTransferItem" :key="i">
											<view class="text-black overflow-1 text-bold text-sm" style="width:473rrpx;">
												{{swiperItem.name}}
											</view>
										</swiper-item>
									</swiper>

									<view v-if="type=='1'" class="text-sm" :class="ele.status=='0'?'text-red':''">
											{{ele.status|statusType}}
									</view>
								</view>

								<view class="flex justify-between align-center margin-xs">
									<view class="text-sm" style="width:350rpx;">
											{{type=='2'?'来自好友：':(ele.receiveUserName?'赠予好友：':'')}} {{ type=='2'?ele.nickName: (ele.receiveUserName?ele.receiveUserName:'')}}
									</view>

									<view class="text-light-gray text-xs">
										{{type=='2'?'接收日期：':'送出日期：'}} {{ type=='2'? ele.updateTime : ele.createTime }}
									</view>
								</view>

								<scroll-view class="prefer-scroll" scroll-x="true" show-scrollbar="false" :scroll-into-view="seqToView">
									<template
										class="cu-item"
									> 
										<view class="giveList"
											v-for="(item, index2) in ele.listUserGiftCardTransferItem"
											:key="index2"
											:id="'seq-' + index2"
											>
											<view style="width:180upx;height:180upx">
												<image 
													style="width:100%;height: 100%;border-radius:15rpx;"
													:src="item.picUrl | formatImg360" 
													mode="aspectFill" 
													class="row-img"
													:class="curIndex == index2?'solid-red':'solid-black'"
													>
												</image>
											</view>
											<view class="text-purple-grey flex justify-between margin-top-xs margin-left-xs text-sm">
													<view :class="curIndex == index2?'text-red':'text-black'">面值:￥{{ item.faceValue }}</view>
											</view>
										</view>
									</template>
								</scroll-view>

								<view class="flex justify-end align-center text-sm margin-right-sm">
									<view class="text-purple-grey flex justify-between margin-top-xs text-sm margin-right-xl">
										<view class="text-black">张数: {{ ele.quantity }}</view>
									</view>
					
									<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
										<view class="text-black ">总面值: <text class="text-red text-bold">￥{{ ele.totalFaceValue }}</text></view>
									</view>
								</view>

								<view
									v-if="ele.status=='2'"
									class="flex justify-around align-center text-sm btn" 
									style="
										background: #F5F5F5;
										width: 450rpx;
										margin: auto;
										margin-top:20rpx;
										margin-right:10rpx;
										">
									<view class="text-purple-grey flex justify-between text-sm margin-right-sm">
										<view class="text-red margin-left">已退回我的礼品卡中</view>
									</view>
					
									<navigator url="/pages/gift/my-gift-card/index" hover-class="none" class="text-purple-grey">
										<view class="text-black align-center check text-sm ">去查看</view>
									</navigator>
								</view>

								<view
									v-if="ele.status=='0'"
									class="flex justify-around align-center text-sm btn" 
									style="
										background: #F5F5F5;
										width: 172rpx;
										margin: auto;
										margin-top:20rpx;
										margin-right:10rpx;
										">
									<view @click="giftRevoke(ele.id)" class="text-purple-grey">
										<view class="text-black align-center check text-sm ">取消转赠</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
				</view>
	</view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
  import {giftPage,revoke } from '@/pages/gift/api/gift'
  import { navigateUtil } from "@/static/mixins/navigateUtil.js";
	import formatPrice from "@/components/format-price/index.vue";

  import {mapState} from 'vuex'
	export default {
		mixins: [navigateUtil],
		components: {
			formatPrice
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '我的礼品卡',
        modalDialog: false,//控制弹框
				curIndex:0,//swiper标记
				currentSelect: 0,//选中标记
				swiperConfig: {
					indicatorDots: false,
					autoplay: true,
					interval: 5000,
					duration: 500,
					circular: true,
					vertical:true
				},
				seqToView: '', // 滚动至序号
				priceStyle: "display: flex; justify-content: center; font-weight: bold; align-items: baseline;",
				giftType: [
          { value: '收到的', key: '2' },
          { value: '送出的', key: '1' }, 
        ],
				giftStatus: [
					{ value: '全部', key: '' },
					{ value: '赠送中', key: '0' }, 
					{ value: '已送出', key: '1' },
					{ value: '未领取', key: '2' },
				],
				type: 2,//默认 收到的
				tabCur:'',
				parameter: {},
				giftCardList:[],//礼品卡数据列表
				loadmore: true,
				page: {
					type: 2,
          current: 1,
          size: 10,
        },
				twoStatus:'',
			};
		},
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
				this.giftPage()
			});
		},
		mounted() {
	
		},
		filters: {
			statusType( key){
				switch (key) {
					case '0':
						return '赠送中';
					case '1':
						return '已送出';
					case '2':
						return '已退回';
					case '3':
						return '已撤销';
					case '4':
						return '已过期';
				}
			}
		},

		onShow() {
		},

    created(){
      
    },

		onReachBottom () {
        if (this.loadmore) {
          this.page.current = this.page.current + 1;
					this.curIndex = 0;
          this.giftPage();
        }
      },

    onPullDownRefresh () {
      // 显示顶部刷新图标
      uni.showNavigationBarLoading();
      this.refresh(); // 隐藏导航栏加载框
      uni.hideNavigationBarLoading(); // 停止下拉动作
      uni.stopPullDownRefresh();
    },

		computed: {
      ...mapState([
				'windowWidth',
				'HeightBar',
				'CustomBar',
				'menuWidth',
				'leftMenuWidth',
				'pixelRatio'
			])
    },

		methods: {
			//撤销
			async giftRevoke(id){
				try {
					const res = await revoke({transferId:id})
					console.log(res);
					if (res.code == '0') {
						uni.showToast({
							title: '撤销成功！',
							icon: 'success',
							duration: 2000
						});
						this.refresh()
					}
					
				} catch (error) {
					console.log(error);
				}
			},
			// 多图点击联动名称
			tabClick(index) {
        if (index == this.currentSelect) {
            return
        } else {
          this.currentSelect = index
          this.curIndex = index ||0
        }
      },
			//多图名称列表
			swiperChange (e) {
				let curIndex = e.mp.detail.current
				
				this.curIndex = curIndex;
				this.currentSelect = curIndex;
				this.tabClick(this.curIndex)
				this.seqToView = 'seq-' + this.curIndex // 滚动至选中序号位置
			},
			//我的礼品卡列表
      giftPage(){
        giftPage(Object.assign({},this.page,util.filterForm(this.parameter))).then((res)=>{
          console.log("giftPage==res==>",res);
          if (res.data) {
            if (res.data.records) {
              let giftPage=res.data.records
              this.giftCardList = [...this.giftCardList, ...giftPage];
							console.log("giftPage.length < this.page.pageSize",giftPage.length < this.page.pageSize);
              if (giftPage.length < this.page.size) {
                this.loadmore = false;
              }
            }
          }
        })
      },

			//一级tab切换
      tabSelect (e) {
        let dataset = e.currentTarget.dataset;
				console.log("tabSelect",e,dataset);
				
        if (dataset.index != this.type) {
          this.type = dataset.key;
					if (this.type=='2') {
						delete	this.parameter.status
					}else{
						this.parameter.status = this.twoStatus
					}
          this.parameter.type =  this.type
          this.refresh();
        }
      },

			//二级tab切换
			twoTabSelect (e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.index != this.tabCur) {
					this.tabCur = dataset.index;
					this.parameter.status = dataset.key;
					this.twoStatus=dataset.key;
					this.refresh();
				}
			},

			// 重置数据
			refresh() {
				this.loadmore = true;
				this.giftCardList = [];
				this.page.current = 1;
				this.curIndex = 0;
				this.giftPage()
			},
    }
	};
</script>

<style lang="scss" scoped>
.solid-black{
	border:1px solid #ccc;
}
.solid-red{
  border: 1px solid red;
}
.prefer-scroll {
  white-space: nowrap;
  width: 100%;
}
.check{
	height: 66rpx;
  width: 168rpx;
  line-height: 66rpx;
	text-align: center;
  margin:2rpx;
  border-radius: 35rpx;
  background-color: #fff;
}
.giveList {
  display: inline-block;
  padding-right: 40rpx;
  text-align: center;
}

.collection-types {
  top: unset !important;
}
.btn {
  height: 70rpx;
  width: 340rpx;
  line-height: 70rpx;
  margin: 19rpx 20rpx 20rpx 25rpx;
  border-radius: 35rpx;
  font-size: 30rpx;
  background-color: #fff;
}

</style>
