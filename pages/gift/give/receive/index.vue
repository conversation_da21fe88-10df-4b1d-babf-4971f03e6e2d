<template>
    <view class="padding-bottom">
        <cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
            <block slot="backText">返回</block>
            <block slot="content">领取礼品卡</block>
        </cu-custom>
    <!-- 礼品卡展示 -->
        <view style="width: 100%;" class="relative text-center margin-bottom">
            <view v-if="(givtInfo.userStatus=='2'&&isUnpack)||(givtInfo.userStatus=='3'&&givtInfo.status=='0'&&isUnpack)">
                <!-- 背景图 -->
                <view>
                    <image :src="'https://img.songlei.com/gift/give-bg-img.png'" mode="aspectFit" class="give-bg-img">
                    </image>
                </view>
        
                <!-- 商品内容 -->
                <view class="absolute goods-bg-box text-center">
                    <!-- 商品图片 -->
                    <special-banner :banner-list="list" :swiper-config="swiperConfig" :min-list="swiperList"></special-banner>
                </view>
            </view>

        <view v-if="giveList&&giveList.length">
            <view :class="(givtInfo.userStatus=='2'&&isUnpack)||(givtInfo.userStatus=='3'&&givtInfo.status=='0'&&isUnpack)?(giveList.length=='1'?'absolute goods-bg-box3':'absolute goods-bg-box4'):'margin-top-sm'">
              <view class="give-radio" :style="{'text-align':'center','height':'none'}">
                    <!-- userStatus   1:赠送人 2:接收人 3:不是赠送人并且接收人为空 -->
                <!-- 赠送信息 -->
                <view v-if="(givtInfo.userStatus=='2'&&isUnpack)||(givtInfo.userStatus=='3'&&givtInfo.status=='0'&&isUnpack)" class="flex justify-start margin" style="border-bottom: 1rpx #BEBEBE dashed;">
                    <view v-if="givtInfo.headimgUrl">
                        <image :src="givtInfo.headimgUrl" mode="aspectFit" class="user-img">
                    </view>
                    <view class="margin-left text-left">
                        <view v-if="givtInfo.nickName"><text class="text-black text-xdf text-bold">{{givtInfo.nickName}}</text>~  送您</view>
                        <view v-if="givtInfo.totalFaceValue" class="text-sm">{{giveList.length}}张共{{givtInfo.totalFaceValue}}元礼品卡 已领取</view>
                    </view>
                </view>

                <view v-else class="margin">
                    <view v-if="givtInfo.userStatus!='3'&&givtInfo.headimgUrl&&givtInfo.status!='3'">
                        <image :src="givtInfo.headimgUrl" mode="aspectFit" class="user-img">
                    </view>
                    
                    <view v-if="givtInfo.userStatus=='2' ||(givtInfo.userStatus=='3'&&givtInfo.status=='0')" class=" text-center">
                        <view v-if="givtInfo.nickName"><text class="text-black text-xdf text-bold">{{givtInfo.nickName}}</text>~  送您</view>
                        <view v-if="givtInfo.totalFaceValue" class="text-sm">{{giveList.length}}张共{{givtInfo.totalFaceValue}}元礼品卡 {{statusType}}</view>
                    </view>

                    <view v-else-if="givtInfo.userStatus!='3'" class=" text-center">
                        <view v-if="givtInfo.status!='3'">
                            <view>
                                <text class="text-black text-xdf text-bold margin-right-xs">我</text>
                                送出的
                            </view>
    
                            <view class="text-sm">
                                {{giveList.length}}张共
                                <text class="text-red">{{givtInfo.totalFaceValue}}</text>
                                元礼品卡
                            </view>
                        </view>
                        <!-- status状态0：赠送中；1：已领取；2：已退回；3：已撤销；4：超时自动撤回；-->
                        <view v-if="givtInfo.status=='0'">
                            <text class="text-black text-df">还未被领取</text>
                            <text class="text-black text-df margin-left-xs">仅剩</text>
                            <count-down :outTime="givtInfo.timestamp*1000" :fontSize="'14'" :textColor="'red'"
                                :backgroundColor="'#FFFFFF'" :connectorColor="'red'" @countDownDone="countDownDone">
                            </count-down>
                            <text @click="giftRevoke(givtInfo.id)" class="text-df text-red">{{statusType}}</text>
                        </view>

                        <view v-if="givtInfo.status=='1'|| givtInfo.status=='2'">
                            <text class="text-black text-df">已被好友</text>
                            <text class="text-black text-df text-bold margin-left-xs">{{givtInfo.receiveUserName}}</text>
                            <text class="text-black text-df">（{{givtInfo.receivePhone | phoneEncryption}}）</text>
                            <text class="text-black text-df">{{statusType}}</text>
                        </view>

                        <view v-if="givtInfo.status=='4'">
                            <text class="text-black text-df">超过24小时未被领取已退回我的礼品卡账户</text>
                            <navigator url="/pages/gift/my-gift-card/index" hover-class="none" class="text-df text-red">{{statusType}}</navigator>
                        </view>

                    </view>
                </view>
    
                <view v-if="(givtInfo.userStatus=='2'&&isUnpack)||(givtInfo.userStatus=='3'&&givtInfo.status=='0'&&isUnpack)" class="margin text-left">
                    <view class="text-black text-df">
                        礼品卡已放入{{givtInfo.receivePhone | phoneEncryption}}账号里
                        <!--
                            <text class="text-sm " style=" margin-left:38rpx;color:#A4785B;text-decoration: underline">
                            去查看 <text class="cuIcon-right"></text>
                        </text>
                            -->
                    </view>
                    <view class="text-sm text-light-gray margin-top-sm">
                        查看路径：松鼠美淘小程序-我的-礼品卡-我的礼品卡
                    </view>
                </view>

                <view v-else class="margin-top-lg">
                    <image :src="statusTypeImg" mode="aspectFit" class="refuse-img">
                    </image>

                    <view v-if="(givtInfo.userStatus=='1'&&givtInfo.status=='3')||(givtInfo.userStatus=='3'&&givtInfo.status!='0')" class="margin-top-xl">
                        <text class="text-black text-df">{{statusType}}</text>
                    </view>
                </view>

                <view class="flex justify-between align-center" :style="{marginTop:isUnpack?'181rpx':'125rpx'}">
                    <button
                    class="button text-xdf text-white submit"
                    @click="close"
                    style="width: 292rpx;
                    height: 80rpx;
                    background: #FFFFFF;
                    color: #FF5002;
                    border-radius: 40rpx;
                    border: 1px solid #FF4F02;
                    margin: 60rpx auto;"
                    >关闭</button>

                    <button
                        class="button text-xdf text-white submit"
                        @click="giveAlso"
                        style="width: 292rpx;
                        height: 80rpx;
                        background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
                        border-radius: 40rpx;
                        margin: 60rpx auto;"
                    >我也要送</button>
                </view>
              </view>
            </view>
        </view>
      </view>
      <!-- 弹框 -->
        <view
            class="cu-modal"
            style="background: rgba(0, 0, 0, 0.8);"
            :class="modalDialog ? 'show' : ''"
            catchtouchmove="touchMove"
            >
            <view
            class="cu-dialog dialo-sku"
            :class="modalDialog ? 'animation-slide-bottom' : ''"
            @tap.stop
            >
                <view class="relative">
                    <view 
                    class="flex justify-start margin" 
                    style="position: absolute;
                        top: 178rpx;
                        z-index: 99999;
                        left: 114rpx;">
                        <view v-if="givtInfo.headimgUrl">
                            <image :src="givtInfo.headimgUrl" mode="aspectFit" class="user-img">
                        </view>
                        <view class="margin-left text-left">
                            <view v-if="givtInfo.nickName">
                                <text class="text-black text-xdf text-bold">{{givtInfo.nickName}}</text>
                                ~  送您
                            </view>
                            <view v-if="givtInfo.totalFaceValue" class="text-lg">{{giveList.length}}张共
                                <text class=" text-red">{{givtInfo.totalFaceValue}}</text>
                                元礼品卡
                            </view>
                        </view>
                    </view>
    
                    <image :src="'https://img.songlei.com/gift/gift-modal.png'" mode="aspectFit" class="gift-modal">
                    </image>
    
                    <view 
                        style="position: absolute;
                        width: 610rpx;
                        height: 180rpx;
                        z-index: 99999;
                        top:578rpx;
                        padding-left: 42rpx;
                        right: 0rpx;
                        bottom:0rpx;
                        left: 0rpx;">
                        <view>{{givtInfo.wishWord}}</view>
                
                        <button
                            class="button text-xdf text-white submit"
                            style="width: 292rpx;
                            height: 80rpx;
                            background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
                            border-radius: 40rpx;
                            margin: 0rpx auto;"
                            @click="unpack(1)"
                        >拆开</button>
                    </view>
    
                    <view class="relative text-xdf text-white">
                        24小时内未领取，将退还给对方。<text style="color: #DFAB64;" @click="unpack(2)">退还</text>
                    </view>
                </view>

            </view>
        </view>
	</view>
</template>

<script>
	const app = getApp();
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
    import {revoke,collect,receive} from '@/pages/gift/api/gift'
    import { navigateUtil } from "@/static/mixins/navigateUtil.js";
    import specialBanner from '../components/specialBanner.vue';
    import countDown from "@/components/count-down/index";
    import {mapState} from 'vuex'
	export default {
	mixins: [navigateUtil],
	components: {
        specialBanner,
        countDown
	},
	data() {
		return {
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '我的礼品卡',
                modalDialog: false,//控制弹框
                isUnpack: true,//是退还是接
                swiperConfig: {
					indicatorDots: false,
					indicatorColor: 'rgba(255, 255, 255, .4)',
					indicatorActiveColor: 'rgba(255, 255, 255, 1)',
					autoplay: false,
					interval: 3000,
					duration: 300,
					circular: true,
					previousMargin: '170rpx',
					nextMargin: '170rpx'
				},
                //多礼品卡展示图
                list: [
                // {
                //     image: 'https://img.songlei.com/gift/tuihuoka.png',
                //     title: '海关数据系统\n多账号免费用'
                // },
                // {
                //     image: 'https://img.songlei.com/gift/tuihuoka.png',
                //     title: '建站推广服务\n抵用券'
                // },
                // {
                //     image: 'https://img.songlei.com/gift/tuihuoka.png',
                //     title: '企业网站免费\n诊断1次'
                // }, 
                // {
                //     image: 'https://img.songlei.com/gift/tuihuoka.png',
                //     title: '海外广告投放\n免费开'
                // }, 
                // {
                //     image: 'https://img.songlei.com/gift/tuihuoka.png',
                //     title: '2CShop独立\n站商城试用'
                // }, 
                // {
                //     image: 'https://img.songlei.com/gift/tuihuoka.png',
                //     title: '提供优质服务\n商产品折扣'
                // }
                ],
                //大图轮播列表
                swiperList: [
                    // {
                    //     image: 'https://img.songlei.com/gift/tuihuoka.png'
                    // },
                    // {
                    //     image: 'https://img.songlei.com/gift/tuihuoka.png'
                    // },
                    // {
                    //     image: 'https://img.songlei.com/gift/tuihuoka.png'
                    // }, 
                    // {
                    //     image: 'https://img.songlei.com/gift/tuihuoka.png'
                    // }, 
                    // {
                    //     image: 'https://img.songlei.com/gift/tuihuoka.png'
                    // }, 
                    // {
                    //     image: 'https://img.songlei.com/gift/tuihuoka.png'
                    // }
                ],
                type:'',//1 是短信 2 是微信
                giveList:[],//赠送列表
                givtInfo:{},//赠送集合
                showShare: false,//是否显示分享组件
                shareParams: {},//分享的内容
                id:'',//订单id
                code:'',//赠送返回的code值 0 成功 1失败
                msg:'',//赠送接口错误信息
                userInfo:{},//获取用户信息
                statusType:'',//状态说明
                statusTypeImg:'',//状态图片展示
		};
	},
    
	props: {},
	onLoad(options) {
		    app.initPage().then(res => {
                uni.hideShareMenu()
            // 保存别人分享来的 userCode
		    util.saveSharerUserCode(options);
            this.userInfo = uni.getStorageSync('user_info')
        
            console.log("this.giveList",this.giveList,options);
            if (options&&options.id) {
                this.id = options.id
                this.receive(options.id)
            }
            
		});
	},

    watch:{
      'giveList': {
        handler(newVal, oldVal) {
          const that = this;
          console.log("newVal",newVal,oldVal);
          if (newVal && newVal.length>0) {
            that.list = [];
            that.swiperList = []
            newVal.forEach((item, index) => {
              if (item) {
                that.list.push({image:item.picUrl,title:item.name})
                that.swiperList.push({image:item.picUrl})
              }
            })
          }
        },
        deep: true
      }
    },
	filters: {},

	onShow() {},

    created(){},

	computed: {
      ...mapState([
	    	'windowWidth',
	    	'HeightBar',
	    	'CustomBar',
	    	'menuWidth',
	    	'leftMenuWidth',
	    	'pixelRatio'
	    ]),
    },

	methods: {
        //撤销
		async giftRevoke(id){
			try {
				const res = await revoke({transferId:id})
				console.log(res);
				if (res.code == '0') {
					uni.showToast({
						title: '撤销成功！',
						icon: 'success',
						duration: 2000
					});
					this.receive(this.id)
				}
				
			} catch (error) {
				console.log(error);
			}
		},

        countDownDone() {
			this.receive(this.id)
		},
        // 关闭 去礼品卡首页
        close(){
            uni.redirectTo({
                url: '/pages/gift/gift-card/index'
            });
        },

        //我也要送 去赠送
        giveAlso(){
            uni.redirectTo({
                url: '/pages/gift/give/list/index'
            });
        },

        //拆开1-接收 2-拒收
        async unpack(type){
            console.log("type",type );
            try {
                let data={
                    transferId :this.id,
                    mobile:this.userInfo.phone,
                    operType:type
                }
                const res = await collect(data)
                let code = res.code
                if (code=='0') {
                    if (type===1) {
                        this.modalDialog = false
                    }else{
                        this.modalDialog = false
                        this.isUnpack =false
                    }
                    this.receive(this.id)
                }else{
                    this.modalDialog=true
                }
            }catch(err){
                console.log(err);
            }
        } ,

      // 赠送查询接口
        async receive(id){
        try {
          console.log("data",id);
          const res = await receive(id)
        //   调试数据
        //   const res = {
        //         "code": 0,
        //         "msg": null,
        //         "data": {
        //             "id": "1678605928103784449",
        //             "tenantId": "1",
        //             "delFlag": "0",
        //             "createTime": "2023-07-11 11:23:34",
        //             "updateTime": "2023-07-11 11:23:34",
        //             "nickName": "微信用户",
        //             "quantity": 2,
        //             "wishWord": "赠送语",
        //             "status": "1",
        //             "userId": "1473576832474939394",
        //             "receiveUserId": "1473576832474939394",
        //             "receivePhone": "18771009973",
        //             "timestamp": 66631,
        //             "userPhone": 18771009973,
        //             "receiveUserName": "微信用户",
        //             "userStatus": "1",
        //             "type": null,
        //             "totalFaceValue": 200,
        //             "listUserGiftCardTransferItem": [
        //                 {
        //                     "id": "1678605928137338882",
        //                     "transferId": "1678605928103784449",
        //                     "tenantId": "1",
        //                     "cardNo": "6110000000038",
        //                     "name": "礼品卡100元儿童节主题",
        //                     "cardType": "LPK100S1",
        //                     "picUrl": "https://img.songlei.com/1/material/f534d5ee-3427-45c5-b5f5-073cc3d21b70.png",
        //                     "faceValue": 100,
        //                     "delFlag": "0",
        //                     "createTime": "2023-07-11 11:23:34",
        //                     "updateTime": "2023-07-11 11:23:34"
        //                 },
        //                 {
        //                     "id": "1678605928145727490",
        //                     "transferId": "1678605928103784449",
        //                     "tenantId": "1",
        //                     "cardNo": "6110000000039",
        //                     "name": "礼品卡100元儿童节主题",
        //                     "cardType": "LPK100S1",
        //                     "picUrl": "https://img.songlei.com/1/material/f534d5ee-3427-45c5-b5f5-073cc3d21b70.png",
        //                     "faceValue": 100,
        //                     "delFlag": "0",
        //                     "createTime": "2023-07-11 11:23:34",
        //                     "updateTime": "2023-07-11 11:23:34"
        //                 }
        //             ],
        //             "headimgUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132"
        //         },
        //         "ok": true
        //     }
          this.code = res.code
          this.givtInfo = res.data
          console.log("res",res.data);
          // 成功
          if (this.code=='0') {
              this.giveList = res.data.listUserGiftCardTransferItem;
            // userStatus   1:赠送人 2:接收人 3:不是赠送人并且接收人为空
            // 点击链接的人为不是该赠送记录的赠送人，也不是该赠送记录的接收人，该赠送记录为已领取/已被退/已超时自动撤销/已被赠送人手动撤回还状态
            if (res.data.userStatus=='3'&&res.data.status!='0') {
                this.statusType = '您来晚了，该链接已失效！';
                this.statusTypeImg = 'https://img.songlei.com/gift/unclaimed.png';
                return
            }
            // status状态0：赠送中；1：已领取；2：已退回；3：已撤销；4：超时自动撤回；
            switch (res.data.status) {
                case '0':
                    this.isUnpack = true;
                    if (res.data.userStatus=='1') {
                        this.statusType = '取消转赠';
                        this.statusTypeImg = 'https://img.songlei.com/gift/unclaimed-status.png';
                    }else{
                        this.modalDialog = true;
                        this.statusType = '已领取'
                    }
                    break;
                case '1':
                    this.modalDialog = false;
                    this.isUnpack = true;
                    if (res.data.userStatus=='1') {
                        this.statusType = '领取';
                        this.statusTypeImg = 'https://img.songlei.com/gift/receive-status.png';
                    }else{
                        this.statusType = '已领取'
                    }
                    break;
                case '2':
                    this.modalDialog = false;
                    this.isUnpack = false;
                    if (res.data.userStatus=='1') {
                        this.statusType = '拒绝';
                    }else{
                        this.statusType = '已拒绝';
                    }
                    this.statusTypeImg = 'https://img.songlei.com/gift/refuse-img.png';
                    break;
                case '3':
                    this.modalDialog = false;
                    this.isUnpack = false;
                    if (res.data.userStatus=='1') {
                        this.statusType = '本次赠送已被您撤回，该链接已失效！';
                        this.statusTypeImg = 'https://img.songlei.com/gift/unclaimed.png';
                    }else{
                        this.statusType = '已撤回';
                        this.statusTypeImg = 'https://img.songlei.com/gift/retract.png';
                    }
                    break;
                case '4':
                    this.modalDialog = false;
                    this.isUnpack = false;
                    if (res.data.userStatus=='1') {
                        this.statusType = '去查看';
                        this.statusTypeImg = 'https://img.songlei.com/gift/timeout.png';
                    }else{
                        this.statusType = '超时自动撤回';
                        this.statusTypeImg = 'https://img.songlei.com/gift/timeout.png';
                    }
                    break;
            }
          }else{
            this.msg = res.data
          }
        } catch (error) {
          console.log(error);
        }
      },

      //确认按钮
      async confirm(){
        if (!this.orderId) {
          await this.gifttransfer()
        }
        console.log("this.orderId==>",this.orderId);
          this.showShare = true;
      },
    }
	};
</script>

<style lang="scss" scoped>
.refuse-img{
    width: 224rpx;
    height: 197rpx;
}
.gift-modal{
    width: 730rpx;
    height: 806rpx;
}

.give-bg-img{
  width: 750rpx;
  height: 747rpx;
}

.goods-bg-box{
  transform: translate(-50%, -50%);
  top: 35%;
  left: 50%;
}

.goods-bg-box2{
  transform: translate(-50%, -50%);
  top: 78%;
  left: 53%;
}
.goods-bg-box3{
  transform: translate(-50%, -50%);
  top: 110%;
  left: 50%;
}
.goods-bg-box4{
  transform: translate(-50%, -50%);
  top: 144%;
  left: 50%;
}
.user-img{
    width: 90rpx;
    height: 90rpx;
    border-radius: 30px;
}

.give-radio{
  width: 695rpx;
  padding: 16rpx 0rpx 17rpx 0rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: auto;
}

</style>