<template>
	<view class="padding-bottom">
		<cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">赠送</block>
		</cu-custom>
		<!-- 余额 -->
		<view class="cu-bar fixed bg-white" :style="{ marginTop: CustomBar + 'px', boxShadow: 'none' }" style="background-color: #f5f5f7">
			<view class="relative">
				<image style="width: 750upx; height: 186upx" src="https://img.songlei.com/gift/gift-card.png"></image>

				<view style="width: 90%; top: 10rpx; left: 0; right: 0; margin: 0 auto" class="absolute flex justify-between padding-sm">
					<view style="margin: auto; margin-right: 80rpx">
						<view class="text-xsm text-bold text-black flex justify-center align-baseline" style="margin-top: 12rpx">
							余额
							<format-price :price="validAmt" priceFontSize="44rpx" />
						</view>
						<view class="text-black" style="font-size: 22rpx">礼品卡数量：{{ totCnt }}张</view>
					</view>

					<view>
						<view class="my-gift-card" @tap="Usagerules()">收送记录</view>
					</view>
				</view>

				<!-- 提示 -->
				<view
					class="text-center round padding-top-xs padding-bottom-xs"
					style="background-color: #ffffff; color: #ee102b; margin: auto; margin-bottom: 20rpx; font-size: 26rpx; width: 95%"
				>
					提示：未使用的礼品卡可进行赠送,已使用的不支持此功能
				</view>
			</view>
		</view>

		<!-- 商品信息 -->
		<view :style="{ marginBottom: '42px' }" style="margin-top: 270upx">
			<checkbox-group>
				<view class="margin-top-xs" v-for="(item, index) in myGiftCardListPages" :key="index">
					<view class="margin-top-xs" style="position: relative">
						<view class="flex justify-around padding-xs margin-top-xs">
							<checkbox
								class="round red margin-right-sm bg-blue"
								style="margin: auto; margin-right: 10rpx"
								@tap.stop="checkboxChange(item, index)"
								:value="item.id"
								:checked="item.checked ? true : false"
							></checkbox>

							<view class="flex justify-around padding-xs margin-top-xs" style="width: 95%; height: 100%; background: #ffffff; margin: 0 auto; border-radius: 15rpx">
								<view style="width: 180upx; height: 180upx">
									<image
										style="width: 100%; height: 100%; border-radius: 15rpx; border: 1px solid #ccc"
										:src="item.imgUrl | formatImg360"
										mode="aspectFill"
										class="row-img"
									></image>
								</view>

								<view class="padding-lr-sm" style="flex: 1; position: relative">
									<view class="flex justify-start align-center">
										<view class="text-black overflow-2" style="width: 350rpx">
											{{ item.cdtName }}
										</view>
									</view>

									<view class="text-light-gray text-xs">{{ item.minDate }}-{{ item.maxDate }}</view>

									<view class="text-sm" style="width: 350rpx; color: #ef9416">
										{{ item.scope }}
									</view>

									<view class="flex justify-between align-center text-sm">
										<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
											<view class="text-black">面值:</view>
											<format-price
												:styleProps="priceStyle2"
												signFontSize="22rpx"
												color="#000"
												:price="item.faceValue"
												priceFontSize="24rpx"
												smallFontSize="20rpx"
											/>
										</view>

										<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
											<view class="text-black text-bold">余额:</view>
											<format-price
												:styleProps="priceStyle"
												signFontSize="18rpx"
												color="#000"
												:price="item.balance"
												priceFontSize="24rpx"
												smallFontSize="20rpx"
											/>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
			</checkbox-group>
		</view>

		<!-- 短信赠送 短信赠送-->
		<view class="cu-bar bg-white tabbar border shop foot" style="background-color: #ffffff" :style="{ 'z-index': '9999' }">
			<view class="flex justify-between align-center">
				<checkbox-group @change="checkboxAllChange">
					<checkbox class="round red margin-left bg-blue" :style="isAllSelect ? theme.themeColor + ' checked' : ''" value="all" :checked="isAllSelect"></checkbox>
				</checkbox-group>

				<view>
					<view class="flex justify-between align-center">
						<view class="margin-left-xs text-black">全选</view>

						<view class="margin-left-xs">
							<view class="flex align-center justify-end text-light-gray">
								<view class="text-black" style="margin-right: 10px" v-if="selectValue && selectValue.length">合计：{{ selectValue.length }}张</view>
							</view>
						</view>
					</view>

					<view class="margin-left-xs">(不支持赠送多人)</view>
				</view>
			</view>

			<view class="flex justify-between align-center bar-rt">
				<button :disabled="isShow" @click="giveWeChat(1)" class="cu-btn round text-black settle-bt" style="background: #ffffff; border: 1px solid #999999">短信赠送</button>

				<button :disabled="isShow" @click="giveWeChat(2)" class="cu-btn round text-white margin-left-xs settle-bt background">微信赠送</button>
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp();

import api from 'utils/api';
// import jweixin from '@/utils/jweixin'
import util from '@/utils/util';
import formatPrice from '@/components/format-price/index.vue';
import { myGiftCardList, getCardbill } from '@/pages/gift/api/gift';
import { navigateUtil } from '@/static/mixins/navigateUtil.js';

import { mapState } from 'vuex';
export default {
	mixins: [ navigateUtil],
	components: {
		formatPrice
	},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			//有数统计使用
			page_title: '我的礼品卡',
			priceStyle: 'display: flex; justify-content: center; font-weight: bold; align-items: baseline;',
			priceStyle2: 'display: flex; justify-content: center; align-items: baseline;',
			myGiftCardListPages: [], //我的礼品卡
			page: {
				status: 'Y',
				canTransfer: 'Y',
				pageNo: 1,
				pageSize: 10
			},
			loadmore: true,
			validAmt: 0, //总余额
			totCnt: 0, //礼品卡数量
			cardNo: '',
			shoppingCartData: [], // 选中商品
			isAllSelect: false, // 全选按钮 是否选中
			selectValue: [], // 选中商品id集合
			isShow: true
		};
	},
	props: {},
	onLoad(options) {},

	created() {},

	filters: {},

	watch: {
		//控制按钮禁用
		shoppingCartData() {
			if (this.shoppingCartData.length > 0) {
				this.isShow = false;
			} else {
				this.isShow = true;
			}
		}
	},

	onShow() {
		app.initPage().then((res) => {
			uni.removeStorageSync('giveList'); //删除赠送记录缓存
			this.refresh();
		});
	},

	onReachBottom() {
		if (this.loadmore) {
			this.page.pageNo = this.page.pageNo + 1;
			this.myGiftCardList();
		}
	},

	onPullDownRefresh() {
		// 显示顶部刷新图标
		uni.showNavigationBarLoading();
		this.refresh(); // 隐藏导航栏加载框
		uni.hideNavigationBarLoading(); // 停止下拉动作
		uni.stopPullDownRefresh();
	},
	created() {},

	computed: {
		...mapState(['windowWidth', 'HeightBar', 'CustomBar', 'menuWidth', 'leftMenuWidth', 'pixelRatio'])
	},

	methods: {
		//微信赠送
		giveWeChat(type) {
			//type 1短信 2微信
			uni.navigateTo({
				url: '/pages/gift/give/index?type=' + type
			});
		},

		// 判断全选按钮是否选中
		checkboxHandle(selectValue) {
			let isAllSelect = false;
			if (!selectValue.length) {
				isAllSelect = false;
			} else if (this.myGiftCardListPages.length === selectValue.length) {
				isAllSelect = true;
			}
			this.isAllSelect = isAllSelect;
			this.selectValue = selectValue;
		},

		//多选
		checkboxAllChange(e) {
			var value = e.detail.value;
			if (value && value.length > 0) {
				this.isAllSelect = true;
				this.setAllSelectValue(true);
			} else {
				this.isAllSelect = false;
				this.setAllSelectValue(false);
				uni.removeStorageSync('giveList');
			}
		},

		//设置全选
		setAllSelectValue(status) {
			let shoppingCartData = this.myGiftCardListPages;
			let selectValue = [];
			let _shoppingCartList = []; // 选中的商品数组
			if (shoppingCartData && shoppingCartData.length > 0) {
				if (status) {
					shoppingCartData.forEach(function (shoppingCart, index) {
						shoppingCart.checked = true;
						selectValue.push(shoppingCart.cardNo);
						_shoppingCartList.push(shoppingCart);
					});
				} else {
					shoppingCartData.forEach(function (shoppingCart) {
						shoppingCart.checked = false;
					});
				}
				this.shoppingCartData = _shoppingCartList;
				console.log('多选礼品卡', this.shoppingCartData);
				uni.setStorageSync('giveList', this.shoppingCartData);
				this.checkboxHandle(selectValue);
			}
		},

		//单个商品勾选点击
		checkboxChange(item, storeIndex, shopIndex) {
			item.checked = !item.checked;
			let index = this.selectValue.indexOf(item.cardNo);
			let shoppingCartData = this.shoppingCartData;
			if (item.checked === true) {
				if (index === -1) {
					this.selectValue.push(item.cardNo);
					shoppingCartData.push(item);
				}
			} else {
				if (index !== -1) {
					this.selectValue.splice(index, 1);
					shoppingCartData.splice(index, 1);
				}
			}
			this.shoppingCartData = shoppingCartData;
			console.log('单选礼品卡', this.shoppingCartData);
			uni.setStorageSync('giveList', this.shoppingCartData);
			this.checkboxHandle(this.selectValue);
		},

		//收送记录
		Usagerules() {
			console.log('出发了');
			// 去收送记录
			uni.navigateTo({
				url: '/pages/gift/give/record-list/index'
			});
		},

		//我的礼品卡列表
		myGiftCardList() {
			myGiftCardList(Object.assign({}, this.page)).then((res) => {
				console.log('myGiftCardList==res==>', res);
				if (res.data) {
					if (res.data.cardList) {
						let myGiftCardListPages = res.data.cardList;
						myGiftCardListPages.forEach((e) => {
							e.checked = false;
						});
						this.myGiftCardListPages = [...this.myGiftCardListPages, ...myGiftCardListPages];
						if (myGiftCardListPages.length < this.page.pageSize) {
							this.loadmore = false;
						}
					}
					let { validAmt, totCnt, invalidCnt, validCnt } = { ...res.data };
					this.totCnt = totCnt || 0;
					this.validAmt = validAmt || 0;
					console.log('validAmt', validAmt, 'totCnt', totCnt);
				}
			});
		},

		refresh() {
			this.loadmore = true;
			this.myGiftCardListPages = [];
			this.page.pageNo = 1;
			this.shoppingCartData = []; // 选中商品
			this.isAllSelect = false; // 全选按钮 是否选中
			this.selectValue = []; // 选中商品id集合
			this.myGiftCardList();
		}
	}
};
</script>

<style lang="scss" scoped>
.background {
	background: linear-gradient(90deg, #33c528 0%, #33c528 0%, #18d244 0%, #15d253 100%);
}
.settle-bt {
	width: 195rpx;
}
.bar-rt {
	text-align: right !important;
	margin-right: 10rpx !important;
}
.without {
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: #ffffff;
	z-index: 999;
	opacity: 0.6;
	text-align: right;
}
.btnRightBackground {
	background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%);
}
.btnBackground {
	background: linear-gradient(-90deg, #cdad90 0%, #cda185 100%);
}
.line {
	width: 108rpx;
	height: 4rpx;
	background: #af815f;
	border-radius: 2rpx;
	margin: 0px auto;
}
.text-color {
	// text-decoration: underline;
	color: #af815f;
}
.my-gift-card {
	border-radius: 15px;
	padding: 8rpx 20rpx 8rpx 20rpx;
	font-size: 26rpx;
	background: #ffffff;
	opacity: 0.62;
	color: #653006;
}
</style>
