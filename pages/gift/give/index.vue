<template>
	<view class="padding-bottom">
			<cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">赠送</block>
			</cu-custom>
      <!-- 礼品卡展示 -->
      <view v-if="!isSuccess" style="width: 100%;" class="relative text-center margin-bottom">
        <!-- 背景图 -->
        <view>
          <image :src="'https://img.songlei.com/gift/give-bg-img.png'" mode="aspectFit" class="give-bg-img">
          </image>
        </view>
        <!-- 商品内容 -->
        <view v-if="giveList&&giveList.length" class="absolute goods-bg-box text-center">
          <!-- 商品图片 -->
          <special-banner :banner-list="list" :swiper-config="swiperConfig" :min-list="swiperList" :totalText="`赠送共${giveList.length}张`" ></special-banner>
        </view>
       
        <!-- 只有短信显示手机号码 如果是短信并且大于一张就展示在 多礼品卡下 否则就在多礼品卡的定位上-->
        <view class="absolute" :class="setosition">
          <view v-if="type=='1'"  style="margin-bottom: 19rpx;">
            <view class="givescroll" :style="{'text-align':'center'}">
              <view class="give-title">输入电话号码</view>
              <view>
                <input
                  placeholder-style="color: #a1a1a1; font-size:30rpx"
                  v-model="phone"
                  type="text"
                  placeholder="输入赠送好友电话"
                  style="width: 550rpx;
                    height: 80rpx;
                    background: #FFFFFF;
                    border: 1rpx solid #E6D5C8;
                    border-radius: 27rpx;
                    margin: auto;
                    margin-top: 10rpx;"
                >
              </view>
            </view>
          </view>

          <view v-if="items&&items.length>0" class="give-radio" :style="{'text-align':'center','height':'none'}">
            <view class="give-title">送上您的转赠语</view>
            <scroll-view style="height:210rpx;" scroll-y="true" show-scrollbar="false" :scroll-into-view="seqToView">
              <radio-group style="min-height:100rpx;align-items: center;width: 95%;" @change="radioChange">
                <label 
                  class="flex justify-between align-center" 
                  v-for="(item, index) in items" :key="index"
                  :id="'seq-' + index"
                  :style="{'width': '100%','padding': '10rpx 0','border-bottom':index==items.length-1 ? '' : '1rpx solid #eee'}" 
                  >
                  <view class="margin-left-sm flex align-center">
                    {{item.content}}
                  </view>
  
                  <radio style="transform: scale(0.7); font-weight: bold" :value="item.content" :checked="index === current"
                    class="round red margin-right-sm" :class="theme.themeColor"/>
                </label>
              </radio-group>
            </scroll-view>
            <!-- 多行输入框 -->
            <view class="give-textarea">
              <textarea
              class="text-left padding-sm"
              style="width: 550rpx;height: 190rpx;"
              confirm-type="done"
              maxlength="30"
              cursor-spacing="20"
              :focus="textareaFocus"
              v-model="customBlessing"
              @focus="focusTextarea"
              @input="textareaConfirm"
              placeholder-style="color:#eee;left:0rpx"
              placeholder="点此处输入"/>
            </view>
            <!-- 确认 -->
            <button
              class="button text-xdf text-white submit"
              @click="confirm"
              style="width: 370rpx;
                height: 80rpx;
                background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
                border-radius: 40rpx;
                margin: 60rpx auto;"
              type
            >{{orderId?'分享':'确认'}}</button>
          </view>
        </view>

        <!-- 分享组件 -->
				<share-component v-model="showShare" :shareParams="shareParams" :showSharePoster="false" @openShare="openShare"></share-component>
      </view>
      <!-- 短信成功 -->
      <view v-if="isSuccess" class="text-center">
        <view style="margin: 0 auto;margin-bottom:150rpx;margin-top:180rpx;">
          <view class="cuIcon-roundcheck" 
              style="
              color:#18D244;
              width: 136rpx;
              height: 136rpx;
              margin: auto;
              font-size: 136rpx;
              text-align: center;
              line-height: 136rpx">
          </view>

          <view class="text-black text-bold margin-top-sm text-lg ">赠送成功！</view>
        </view>

        <view style="margin: 0 auto;">
          <navigator 
            hover-class="none" 
            open-type="navigate" 
            :url="'/pages/gift/my-gift-card/index'"  
            style="width: 293rpx;height: 80rpx;"  
            class="cu-btn round btnBackground"> 
              查看我的礼品卡
          </navigator>
        </view>
      </view>
	</view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
  import {gifttransfer,getWishword} from '@/pages/gift/api/gift'
  import { navigateUtil } from "@/static/mixins/navigateUtil.js";
  import shareComponent from "@/components/share-component/index"
  import specialBanner from './components/specialBanner.vue'
  import validate from 'utils/validate'

  import {mapState} from 'vuex'
	export default {
		mixins: [navigateUtil],
		components: {
			shareComponent,
           specialBanner
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '我的礼品卡',
        modalDialog: false,//控制弹框
        swiperConfig: {
					indicatorDots: false,
					indicatorColor: 'rgba(255, 255, 255, .4)',
					indicatorActiveColor: 'rgba(255, 255, 255, 1)',
					autoplay: false,
					interval: 3000,
					duration: 300,
					circular: true,
					previousMargin: '170rpx',
					nextMargin: '170rpx'
				},
        seqToView: '', // 滚动至序号
        //多礼品卡展示图
        list: [
          // {
          //     image: 'https://img.songlei.com/gift/tuihuoka.png',
          //     title: '海关数据系统\n多账号免费用'
          // },
          // {
          //     image: 'https://img.songlei.com/gift/tuihuoka.png',
          //     title: '建站推广服务\n抵用券'
          // },
          // {
          //     image: 'https://img.songlei.com/gift/tuihuoka.png',
          //     title: '企业网站免费\n诊断1次'
          // }, 
          // {
          //     image: 'https://img.songlei.com/gift/tuihuoka.png',
          //     title: '海外广告投放\n免费开'
          // }, 
          // {
          //     image: 'https://img.songlei.com/gift/tuihuoka.png',
          //     title: '2CShop独立\n站商城试用'
          // }, 
          // {
          //     image: 'https://img.songlei.com/gift/tuihuoka.png',
          //     title: '提供优质服务\n商产品折扣'
          // }
        ],
        //大图轮播列表
        swiperList: [
            // {
            //     image: 'https://img.songlei.com/gift/tuihuoka.png'
            // },
            // {
            //     image: 'https://img.songlei.com/gift/tuihuoka.png'
            // },
            // {
            //     image: 'https://img.songlei.com/gift/tuihuoka.png'
            // }, 
            // {
            //     image: 'https://img.songlei.com/gift/tuihuoka.png'
            // }, 
            // {
            //     image: 'https://img.songlei.com/gift/tuihuoka.png'
            // }, 
            // {
            //     image: 'https://img.songlei.com/gift/tuihuoka.png'
            // }
        ],
        phone:'',
        //祝福语
        items: [
          // {
          //     value: '1',
          //     name: '送一份心意只想送一份心意只想和你分享分享',
          //     checked: 'true'
          // },
          // {
          //     value: '2',
          //     name: '有爱的礼物送给可爱的你~'
          // },
          // {
          //     value: '3',
          //     name: '自定义祝福语 (0/30)'
          // }
        ],
        //祝福语
        blessing:'',
        //自定义祝福语
        customBlessing:'',
        textareaFocus:false,
        type:'',//1 是短信 2 是微信
        giveList:[],//赠送列表
        showShare: false,//是否显示分享组件
        shareParams: {},//分享的内容
        orderId:'',//订单id,'1679058545558384642'
        code:'',//赠送返回的code值 0 成功 1失败
        msg:'',//赠送接口错误信息
        current:'',
        isSuccess:false,//是否成功
			};
		},
    
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
        this.getWishword()
        // --------小程序分享朋友圈开始------
			// #ifdef MP-WEIXIN
			uni.showShareMenu({
				withShareTicket: true,
				menus: ['shareAppMessage']
			});
			// --------小程序分享朋友圈结束------
      // #endif
        this.giveList = uni.getStorageSync('giveList')
        console.log("this.giveList",this.giveList,options);
        if (options&&options.type) {
          this.type = options.type
        }
			});
		},
    
    onShareAppMessage: function(ops) {
			// this.$refs.poster.onCanvas()
			let from_type = "menu"
			if (ops.from === 'button') {
				from_type = "button"
			}
			let goodsSpu = this.giveList;
			let title = this.blessing;
			let sharePic = '';
			// if (goodsSpu.sharePic && goodsSpu.sharePic.startsWith("http")) {
			if (goodsSpu[0].sharePic) {
				sharePic = goodsSpu[0].sharePic;
			}
			// let imageUrl = '' || goodsSpu.picUrls[0];
			let imageUrl = sharePic ? sharePic + '-jpg_w360_q90' : this.giveList[0].imgUrl;

			const userInfo = uni.getStorageSync('user_info')
			let userCode = userInfo ? '&sharer_user_code=' + userInfo.userCode : ''
			let path = '/pages/gift/give/receive/index?id=' + this.orderId + userCode;
      // 2018-09-12通知 ->10月10日起新提交发布的版本，不再支持分享回调参数 success 、fail 、complete，即用户从小程序/小游戏中分享消息给好友时，开发者将无法获知用户是否分享完成，也无法在分享后立即获得分享成功后的回调参数shareTicket。该调整可以在基础库 2.3.0及以上版本体验
			return {
				title: title,
				path: path,
				imageUrl: imageUrl,
				success: res=> {
          // 分享成功
				  if (res.errMsg == 'shareAppMessage:ok') {
            console.log("分享成功===》",res);
          }
					uni.showToast({
						title: '分享成功'
					})
				},
				fail: res=> { // 转发失败
          // 用户取消
				  if (res.errMsg == 'shareAppMessage:fail cancel') {
            console.log("用户取消===》",res);
          }
				  // 分享失败
				  if (res.errMsg == 'shareAppMessage:fail') {
            console.log("分享失败===》",res);
          }
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					})
				}
			};
		},
    watch:{
      'giveList': {
        handler(newVal, oldVal) {
          const that = this;
          if (newVal && newVal.length>0) {
            that.list = [];
            that.swiperList = []
            newVal.forEach((item, index) => {
              if (item) {
                that.list.push({image:item.imgUrl,title:item.cdtName})
                that.swiperList.push({image:item.imgUrl})
              }
            })
          }
        },
        deep: true
      }
    },
		filters: {},

		onShow() {},

    created(){},

		computed: {
      ...mapState([
				'windowWidth',
				'HeightBar',
				'CustomBar',
				'menuWidth',
				'leftMenuWidth',
				'pixelRatio'
			]),

      //设置转赠语位置 
      setosition(){
        //1 短信 
        if(this.type=='1'){
          if (this.giveList) {
            if (this.giveList.length==1) {
              return 'goods-bg-box6'
            }
            if (this.giveList.length>1) {
              return 'goods-bg-box7'
            }
          }
          return 'goods-bg-box7'
        }
        // 2微信
        if(this.type=='2'){
          if (this.giveList) {
            if (this.giveList.length==1) {
              return 'goods-bg-box4'
            }
            if (this.giveList.length>1) {
              return 'goods-bg-box5'
            }
          }
          return 'goods-bg-box5'
        }
      }
    },

		methods: {
      //获取转赠语
      async getWishword(){
            try {
                const res = await getWishword()
                if (res.code=='0') {
                    console.log("res",res.data);
                    this.items = res.data
                    this.items.push({content:'自定义祝福语 (0/30)'})
                }
            } catch (error) {
                console.log("error",error);
                
            }
        },
      // 赠送接口
      async gifttransfer(){
        //转赠语 和手机号校验
        if (!this.blessing){
          uni.showToast({
            title: "请选择或输入转赠语！",
            icon: "none",
            duration: 2000,
          });
          return
        }
        if (this.type=='1') {
          if (!this.phone) {
            uni.showToast({
              title: "请输入电话号码！",
              icon: "none",
              duration: 2000,
            });
            return
          }
          
          if (!validate.validateMobile(this.phone)) {
            uni.showToast({
              title: '请输入正确的手机号码',
              icon: 'none',
              duration: 3000
            });
            return;
          }
        }
        
        try {
          let data = this.giveList.map(item=>{
            return{
              cardNo:item.cardNo,
              name:item.cdtName,
              cardType:item.cdtNo,
              picUrl:item.imgUrl,
              faceValue:item.faceValue
            }
          })
          console.log("data",data);
          const params = {
						wishWord:this.blessing,
            userGiftCardTransferItem:data
					}
          // 1 短信
          if (this.type=='1') {
            params.phone=this.phone
          }else{
            delete params.phone
          }
          const res = await gifttransfer(params)
          this.code = res.code
          console.log("res",res.data);
          // 成功
          if (this.code=='0') {
            if (this.type=='1') {
              this.isSuccess=true
              return
            }
            this.orderId = res.data
          }else{
            this.msg = res.data
          }
        } catch (error) {
          console.log(error);
        }
      },

      //确认按钮
      async confirm(){
        // 短信
        if (this.type=='1') {
          await this.gifttransfer()
          return
        }else if (!this.orderId) {
          await this.gifttransfer()
        }
        console.log("this.orderId==>",this.orderId);
        const userInfo = uni.getStorageSync('user_info')
        let userCode = userInfo ? '&sharer_user_code=' + userInfo.userCode : ''
        let path = '/pages/gift/give/receive/index?id=' + this.orderId + userCode;
        if (this.orderId) {
          this.shareParams = {
					title: this.giveList[0].name,
					desc: '发现一个好物，推荐给你呀',
					imgUrl: this.giveList[0].sharePic?this.giveList[0].sharePic:this.giveList[0].imgUrl,
					scene: path,
					page: path,
					// posterConfig: posterConfig
				  }
          this.showShare = true;
        }
      },

      //选择自定义输入框
      focusTextarea(){
        console.log("focusTextarea",this.items.length);
        this.current = this.items.length-1;
        this.seqToView = 'seq-' + this.current // 滚动至选中序号位置
      },

      //自定义输入
      textareaConfirm(e){
        if (e.detail.value) {
          this.blessing = e.detail.value
        }
      },
      
      //转赠语
      radioChange: function(evt) {
        this.blessing='';
          for (let i = 0; i < this.items.length; i++) {
            console.log("evt.detail.",i,evt.detail,this.items.length);
              if (this.items[i].content === evt.detail.value) {
                  this.current = i;
                  this.blessing=evt.detail.value
                  if (this.current==this.items.length-1) {
                    this.textareaFocus=true
                  }else{
                    this.textareaFocus=false
                  }
                  console.log("11111",this.current,this.blessing);
                  break;
              }
          }
      },
    }
	};
</script>

<style lang="scss" scoped>
.btnBackground{
  width: 343rpx;
  height: 80rpx;
  background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
  border-radius: 40rpx;
  color: #FFFFFF;
}

.give-bg-img{
  width: 750rpx;
  height: 747rpx;
}

.goods-bg-box{
  transform: translate(-50%, -50%);
  top: 35%;
  left: 50%;
}

.goods-bg-box2{
  transform: translate(-50%, -50%);
  top: 64%;
  left: 59%;
}
.goods-bg-box3{
  transform: translate(-50%, -50%);
  top: 65%;
  left: 50%;
}
.goods-bg-box4{
  transform: translate(-50%, -50%);
  top: 117%;
  left: 50%;
}

.goods-bg-box5{
  transform: translate(-50%, -50%);
  top: 148%;
  left: 50%;
}

.goods-bg-box6{
  transform: translate(-50%, -50%);
  top: 130%;
  left: 50%;
}

.goods-bg-box7{
  transform: translate(-50%, -50%);
  top: 161%;
  left: 50%;
}

.give-title{
  font-size: 40rpx;
  font-weight: bold;
  color: #A4785B;
  }

.givescroll {
  width: 680rpx;
  height: 197rpx;
  padding: 16rpx 0rpx 17rpx 0rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: auto;
}

.give-radio{
  width: 680rpx;
  padding: 16rpx 0rpx 17rpx 0rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: auto;
}

.give-textarea{
  width: 550rpx;
  height: 190rpx;
  background: #FFFFFF;
  border: 1rpx solid #E6D5C8;
  border-radius: 27rpx;
  margin: auto;
}
</style>
