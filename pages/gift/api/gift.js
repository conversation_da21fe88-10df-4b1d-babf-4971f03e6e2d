import {
  requestApi as request
} from '@/utils/api.js'


// 礼品卡筛选列表
export const giftCardList = (data) => {
  return request({
    url: '/mallapi/giftCard/suggest',
    method: 'get',
    data
  })
}

// 礼品卡支付查询是否设置密码
export const getIsPassword = () => {
  return request({
    url: '/mallapi/paypaymentuserpassword',
    method: 'get',
    showLoading: false,
  })
}

// 礼品卡支付设置发送短信
export const phoneCode = (data) => {
  return request({
    url: '/mallapi/phone/code',
    method: 'get',
    data
  })
}

// 礼品卡支付短信验证接口
export const phoneCheck = (data = {}) => {
  return request({
    url: '/mallapi/paypaymentuserpassword/check',
    method: 'post',
    data
  })
}

// 礼品卡支付设置密码
export const setPassword = (data = {}) => {
  return request({
    url: '/mallapi/paypaymentuserpassword',
    method: 'post',
    data
  })
}

// 礼品卡支付验证密码
export const passwordCheck = (data = {}) => {
  return request({
    url: '/mallapi/paypaymentuserpassword/passwordCheck',
    method: 'post',
    data
  })
}

// 礼品卡支付修改密码
export const editPassword = (data = {}) => {
  return request({
    url: '/mallapi/paypaymentuserpassword',
    method: 'put',
    data
  })
}

// 礼品卡开启免密支付
export const openPayment = (data = {}) => {
  return request({
    url: '/mallapi/paypaymentuserpassword/confidentiality',
    method: 'post',
    data
  })
}

// 礼品卡绑定新卡
export const bindCardCode = (data) => {
  return request({
    url: '/mallapi/giftCard/bind',
    method: 'get',
    data
  })
}

// 我的礼品卡列表
export const myGiftCardList = (data = {}) => {
  return request({
    url: '/mallapi/giftCard/list',
    method: 'post',
    showLoading: false,
    data
  })
}

// 礼品卡支付二维码
export const getPaypayCode = () => {
  return request({
    url: '/mallapi/giftCard/rcode',
    method: 'get',
	showLoading: false,
  })
}

// 礼品卡单卡支付二维码
export const getOnePaypayCode = (data) => {
  return request({
    url: '/mallapi/giftCard/rcode?cardNo=' + data,
    method: 'get',
	showLoading: false,
  })
}

// 礼品卡单卡账单
export const getCardbill = (data) => {
  return request({
    url: '/mallapi/giftCard/bill',
    method: 'get',
    data
  })
}

// 礼品卡赠送接口
export const gifttransfer = (data = {}) => {
  return request({
    url: '/mallapi/gifttransfer/launch',
    method: 'post',
    showLoading: false,
    data
  })
}

// 礼品卡接收
export const collect = (data = {}) => {
  return request({
    url: '/mallapi/gifttransfer/receive',
    method: 'post',
    // showLoading: false,
    data
  })
}

// 礼品卡赠送查询接口
export const receive = (id) => {
  return request({
    url: '/mallapi/gifttransfer/' + id,
    method: 'get',
  })
}

// 礼品卡查账单
export const getRecords = (data) => {
  return request({
    url: '/mallapi/giftCard/records',
    method: 'get',
    data
  })
}

// 礼品卡收送记录
export const giftPage = (data = {}) => {
  return request({
    url: '/mallapi/gifttransfer/page',
    method: 'get',
    // showLoading: false,
    data
  })
}

// 查询转赠语
export const getWishword = (data = {}) => {
  return request({
    url: '/mallapi/wishword/get',
    method: 'get',
    showLoading: false,
    data
  })
}

// 礼品卡撤销接口
export const revoke = (data = {}) => {
  return request({
    url: '/mallapi/gifttransfer/revoke',
    method: 'post',
    // showLoading: false,
    data
  })
}

// 礼品卡手机赠送待领取查询接口
export const getByReceivePhones = () => {
  return request({
    url: '/mallapi/gifttransfer/getByReceivePhones',
    method: 'get',
  })
}

// 红包信息
export const getRedPackInfo = () => {
  return request({
    url: '/mallapi/giftCard/redPack',
    method: 'get',
  })
}