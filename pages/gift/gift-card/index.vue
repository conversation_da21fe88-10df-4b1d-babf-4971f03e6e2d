<template>
	<view class="padding-bottom">
		<cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">我的红包和礼品卡</block>
		</cu-custom>

		<view class="shop-top" :style="{ marginTop: (scrollY > topHeight ? CustomBar : 0) + 'px' }">
			<!-- 礼品卡余额 -->
			<view class="relative">
				<image style="width: 750upx; height: 186upx" src="https://img.songlei.com/gift/gift-card.png"></image>

				<view style="width: 90%; top: 15rpx; left: 0; right: 0; margin: 0 auto" class="absolute flex justify-between padding-sm">
					<view>
						<view class="text-purple-grey flex margin-top-xs text-sm align-baseline">
							<view class="text-black text-color">礼品卡余额：</view>
							<format-price
								:styleProps="priceStyle"
								style="height: 40rpx; line-height: 40rpx"
								signFontSize="24rpx"
								color="#EE102B"
								:price="validAmt"
								priceFontSize="36rpx"
								smallFontSize="36rpx"
							/>
							<view class="text-xsm padding-top-xs padding-left-sm text-color">{{ totCnt }}张</view>
						</view>

						<view class="text-black margin-top-xs flex text-color text-sm align-baseline">
							红包余额：
							<text
								style="font-weight: bold; color: #ee102b"
								:style="{
									fontSize: Number(redPackageInfo.redPackNum) > 9999 ? '28rpx' : '36rpx'
								}"
							>
								{{ redPackageInfo.redPackNum }}
							</text>
							<text class="text-xsm padding-top-xs padding-left-sm text-color">相当于 ￥{{ redPackageInfo.redPackPrice }}</text>
						</view>
					</view>

					<view>
						<view class="text-xsm margin-bottom-sm text-color flex">
							<!-- <navigator :url="`/pages/shop/shop-detail/index?id=${shopId}}`" hover-class="none"> 购卡 </navigator>   -->
							<text @tap="navShopTo()">购卡</text>
							<text style="padding: 0 10rp红包x">|</text>
							<navigator url="/pages/gift/bill-list/index" hover-class="none">账单</navigator>
							<text style="padding: 0 10rpx">|</text>
							<!-- <navigator url="/JavaScript" hover-class="none"> 帮助 </navigator> -->
							<navigator @tap="Usagerules()" url="/JavaScript" hover-class="none">帮助</navigator>
						</view>

						<view class="my-gift-card" @click="toPage('/pages/gift/my-gift-card/index')">
							我的红包卡
							<text class="cuIcon-right"></text>
						</view>
					</view>
				</view>
			</view>

			<!-- tap栏跳转 -->
			<view class="cu-list menu card-menu margin-top-xs all-orders">
				<view class="cu-list grid col-5 no-border" style="padding: 0rpx">
					<view class="cu-item" style="display: flex; justify-content: center; flex-direction: row">
						<view @tap="bindCode()">
							<!-- <navigator url="/pages/gift/new-card/index" hover-class="none"> -->
							<view style="margin-top: 26rpx">
								<image style="width: 88rpx; height: 88rpx" src="https://img.songlei.com/gift/tab1.png"></image>
							</view>
							<text class="user-text-xl text-df" style="color: #000000">绑定新卡</text>
							<!-- </navigator> -->
						</view>
					</view>

					<view class="cu-item" style="display: flex; justify-content: center; flex-direction: row">
						<!-- <view @tap="bindCode()"> -->
						<navigator url="/pages/gift/give/list/index" hover-class="none">
							<view style="margin-top: 26rpx">
								<image style="width: 88rpx; height: 88rpx" src="https://img.songlei.com/gift/tab2.png"></image>
							</view>
							<text class="user-text-xl text-df" style="color: #000000">赠送</text>
						</navigator>
						<!-- </view> -->
					</view>

					<view class="cu-item" style="display: flex; justify-content: center; flex-direction: row">
						<!-- <navigator url="/pages/gift/payment-code/index" hover-class="none"> -->
						<view @tap="navto(1)">
							<view style="margin-top: 26rpx">
								<image style="width: 88rpx; height: 88rpx" src="https://img.songlei.com/gift/tab3.png"></image>
							</view>
							<text class="user-text-xl text-df" style="color: #000000">礼品卡付款</text>
						</view>
						<!-- </navigator> -->
					</view>

					<view class="cu-item" style="display: flex; justify-content: center; flex-direction: row">
						<view @tap="navto(2)">
							<view style="margin-top: 26rpx">
								<image style="width: 88rpx; height: 88rpx" src="https://img.songlei.com/gift/tab4.png"></image>
							</view>
							<text class="user-text-xl text-df" style="color: #000000">支付设置</text>
						</view>
					</view>

					<view class="cu-item" style="display: flex; justify-content: center; flex-direction: row">
						<view @tap="navto(3)">
							<view style="margin-top: 26rpx">
								<image style="width: 88rpx; height: 88rpx" src="https://img.songlei.com/live/activity/lottery/red-pay.png"></image>
							</view>
							<text class="user-text-xl text-df" style="color: #000000">红包</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 企业购卡 -->
			<view style="background-color: #ffffff" class="padding-sm flex justify-between margin-right margin-left member margin-top-xs radius margin-bottom-xs">
				<view style="display: flex; justify-content: flex-start; align-items: center">
					<text class="text-sm">企业购卡</text>
				</view>

				<!-- <navigator url="/pages/user/user-qrcode/index" hover-class="none" style="display: flex;justify-content: center;align-items: center;color: #777777" class="text-sm"> -->
				<view @tap="payCode" style="display: flex; justify-content: center; align-items: center; color: #777777" class="text-sm">
					企业购卡享百元返卡
					<text class="cuIcon-right margin-left-xs"></text>
				</view>
			</view>

			<!-- 广告 -->
			<view v-if="payment && payment.imgUrl" style="margin: 20rpx auto; display: flex; align-items: center; justify-content: center">
				<image mode="aspectFill" style="width: 700upx; height: 140upx; border-radius: 20rpx" :src="payment.imgUrl | formatImg750"></image>
			</view>

			<!-- 我的礼品卡 -->
			<view v-if="myGiftCardListPages && myGiftCardListPages.length">
				<view
					style="background-color: #a26547; border-top-right-radius: 30px; border-top-left-radius: 30px"
					class="padding-sm flex justify-between margin-right margin-left member radius"
				>
					<view style="display: flex; justify-content: flex-start; align-items: center">
						<text class="text-xsm text-white">我的礼品卡</text>
					</view>

					<navigator
						url="/pages/gift/my-gift-card/index"
						hover-class="none"
						style="display: flex; justify-content: center; align-items: center"
						class="text-sm text-white"
					>
						查看更多
						<text class="cuIcon-right margin-left-xs text-white"></text>
					</navigator>
				</view>
				<!-- 礼品卡信息轮播 -->
				<swiper autoplay circular>
					<swiper-item v-for="(item, index) in myGiftCardListPages" :key="index">
						<view
							@tap="toPage('/pages/gift/my-gift-card/index')"
							class="flex justify-around padding-xs"
							style="width: 95%; height: 100%; background: #ffffff; margin: 0 auto; border-radius: 15rpx"
						>
							<view style="width: 180upx; height: 75%">
								<image
									style="width: 100%; height: 100%; border-radius: 15rpx; border: 1px solid #ccc"
									:src="item.imgUrl | formatImg360"
									mode="aspectFill"
									class="row-img"
								></image>
							</view>

							<view class="padding-lr-sm" style="flex: 1; position: relative">
								<view class="flex justify-start align-center">
									<view class="text-black overflow-2" style="width: 350rpx">
										{{ item.cdtName }}
									</view>
								</view>

								<view class="text-light-gray text-xs">{{ item.minDate }}-{{ item.maxDate }}</view>

								<view class="text-sm" style="width: 350rpx; color: #ef9416">
									{{ item.scope }}
								</view>

								<view @click.stop="" class="text-xs" style="position: absolute; top: 0px; right: 0px">
									<!-- 消费记录 <text class="cuIcon-right margin-left-xs"></text> -->
								</view>

								<view class="flex justify-between align-center text-sm">
									<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
										<view class="text-black">面值:</view>
										<format-price
											:styleProps="priceStyle2"
											signFontSize="22rpx"
											color="#000"
											:price="item.faceValue"
											priceFontSize="24rpx"
											smallFontSize="20rpx"
										/>
									</view>

									<view class="text-purple-grey flex justify-between margin-top-xs text-sm">
										<view class="text-black text-bold">余额:</view>
										<format-price
											:styleProps="priceStyle"
											signFontSize="18rpx"
											color="#000"
											:price="item.balance"
											priceFontSize="24rpx"
											smallFontSize="20rpx"
										/>
									</view>
								</view>
							</view>
						</view>
					</swiper-item>
				</swiper>
			</view>
		</view>

		<!-- 礼品卡筛选列表 -->
		<view
			class="justify-center bg-white solid-bottom margin-sm padding-top-xs"
			:class="{ 'topfixed-active': scrollY > topHeight, round: scrollY < topHeight }"
			:style="{ top: (scrollY > topHeight ? CustomBar : 0) + 'px' }"
		>
			<view class="grid response text-center align-start">
				<view class="flex-sub margin-xs">
					<view v-if="range && range.length" class="grid text-center text-xdf padding-left-sm" style="position: relative" data-type="price">
						<uni-data-select style="width: 168rpx; border: none; flex: 1" v-model="value" :localdata="range" @change="change"></uni-data-select>
						<view style="font-size: 28rpx; line-height: 70rpx; position: absolute; right: 30rpx; pointer-events: none">
							<view class="cuIcon-triangledownfill text-black" data-type="price"></view>
						</view>
					</view>
				</view>

				<view class="flex-sub margin-xs">
					<view class="grid text-center text-xdf text-black" @tap="sortHandle" data-type="price">
						价格
						<view class="margin-left-xs" style="font-size: 28rpx; line-height: 24rpx">
							<view :class="'cuIcon-triangleupfill ' + (price == 'asc' ? 'text-' + theme.themeColor : '')" data-type="price"></view>
							<view class="basis-df"></view>
							<view :class="'cuIcon-triangledownfill ' + (price == 'desc' ? 'text-' + theme.themeColor : '')" data-type="price"></view>
						</view>
					</view>
				</view>

				<view class="flex-sub margin-xs">
					<view class="grid text-center text-xdf text-black" @tap="sortHandle" data-type="sales">
						销量
						<view class="margin-left-xs" style="font-size: 28rpx; line-height: 24rpx">
							<view :class="'cuIcon-triangleupfill ' + (sales == 'asc' ? 'text-' + theme.themeColor : '')" data-type="sales"></view>
							<view class="basis-df"></view>
							<view :class="'cuIcon-triangledownfill ' + (sales == 'desc' ? 'text-' + theme.themeColor : '')" data-type="sales"></view>
						</view>
					</view>
				</view>

				<view class="flex-sub margin-xs">
					<view
						class="text-xdf text-black"
						:class="ifAvailable == '0' ? 'text-bold text-' + theme.themeColor : ''"
						@tap="sortHandle"
						data-type="goods"
						style="width: 121rpx"
					>
						仅看有货
					</view>
				</view>
			</view>
		</view>

		<!-- 礼品卡列表 -->
		<view :style="{ marginTop: (scrollY > topHeight ? 10 : 0) + 'px' }" style="height: calc(100vh)">
			<goods-card @onCaetAnimation="caetShake" :showCart="true" ref="goodscard" :goodsList="giftCardList" :cartType="3"></goods-card>
			<view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
		</view>

		<!--悬浮购物车-->
		<suspension-cart :shoppingCartCount="shoppingCartCount" :animation="animation" :cartType="3" :shopId="shopId"></suspension-cart>
	</view>
</template>

<script>
const app = getApp();

import api from 'utils/api';
// import jweixin from '@/utils/jweixin'
import util from '@/utils/util';
import { giftCardList, getIsPassword, myGiftCardList, getRedPackInfo } from '@/pages/gift/api/gift';
import formatPrice from '@/components/format-price/index.vue';
import { navigateUtil } from '@/static/mixins/navigateUtil.js';
import suspensionCart from '@/components/suspension-cart/suspension-cart.vue';
// #ifdef APP-PLUS
const mpaasScanModule = uni.requireNativePlugin('Mpaas-Scan-Module');
// #endif

import uniDataSelect from "../components/uni-data-select.vue"; 

import { mapState } from 'vuex';
import __config from '@/config/env';
export default {
	mixins: [ navigateUtil],
	components: {
		formatPrice,
		suspensionCart,
		uniDataSelect
	},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			//有数统计使用
			page_title: '礼品卡',
			priceStyle: 'display: flex; justify-content: center; font-weight: bold; align-items: baseline;',
			priceStyle2: 'display: flex; justify-content: center; align-items: baseline;',
			payment: {}, //广告图片
			myGiftCardListPages: [], //我的礼品卡
			price: '', //价格
			sales: '', //销量
			ifAvailable: '', // 是否售罄 0是;1否
			sortList: [], // 排序
			//根据筛选获取商品列表
			page: {
				current: 1,
				size: 10
			},
			value: 0, //默认选中
			range: [
				{ value: 0, text: '全分类' },
				{ value: 1, text: '实体卡' },
				{ value: 2, text: '电子卡' }
			],
			giftCardList: [], //礼品卡列表数据
			parameter: {},
			loadmore: true,
			// userpassword:{},
			shoppingCartCount: 0, //购物车数量
			animation: '', // 动画样式
			//我的礼品卡
			myPage: {
				status: 'Y', //状态 Y-可用 N-不可用(包括赠与中,待领取,已过期,已失效) Z-赠与中 J-接收中
				pageNo: 1, //页码
				pageSize: 5 //	行数
			},
			validAmt: 0, //总余额
			totCnt: 0, //礼品卡总数量
			scrollY: 0,
			topHeight: 0,
			shopId: '', //店铺id
			redPackageInfo: {}
		};
	},
	props: {},

	onLoad(options) {
		app.initPage().then((res) => {
			//广告
			this.advertisement('GIFT_PAGE');
			this.getIsPassword();
			this.refresh();
		});
	},

	onShow() {
		this.myGiftCardList();
		this.getRedPackInfo();
	},

	onPullDownRefresh() {
		// 显示顶部刷新图标
		uni.showNavigationBarLoading();
		this.refresh(); // 隐藏导航栏加载框
		uni.hideNavigationBarLoading(); // 停止下拉动作
		uni.stopPullDownRefresh();
	},

	// 触底执行
	onReachBottom() {
		// console.log("onReachBottom", "true")
		this.reachBottom();
	},

	//滚动监听
	onPageScroll(e) {
		// console.log('onPageScroll==>',e)
		this.scrollY = parseInt(e.scrollTop);
	},

	computed: {
		...mapState(['windowWidth', 'HeightBar', 'CustomBar', 'menuWidth', 'leftMenuWidth', 'pixelRatio'])
	},

	methods: {
		//规则跳转
		Usagerules() {
			console.log('出发了');
			uni.navigateTo({
				url: '/pages/goods/goodsrules/goodsrules?pageName=gift'
			});
		},
		//去设置
		navto(num) {
			if (num == '3') {
				if (__config.basePath == 'https://site.songlei.com') {
					uni.navigateTo({
						url: `/pages/micro-page/index?id=1879441980968009730`
					});
				} else {
					uni.navigateTo({
						url: `/pages/micro-page/index?id=1879443629649629185`
					});
				}
				return;
			} else {
				let isActive = uni.getStorageSync('paypaymentuserpassword')?.isActive;
				// isActive 0 没有开通去开通
				if (isActive == '0') {
					uni.navigateTo({
						url: `/pages/gift/set-password/code/index`
					});
					return;
				} else {
					//  1 是付款码 2 支付设置
					if (num == '1') {
						uni.navigateTo({
							url: `/pages/gift/payment-code/index`
						});
						return;
					}
					uni.navigateTo({
						url: `/pages/gift/set-password/index`
					});
				}
			}
		},
		//购卡到店铺,没有店铺则提示
		navShopTo() {
			if (!this.shopId)
				return uni.showToast({
					icon: 'none',
					title: '礼品卡店铺不存在！',
					duration: 2000
				});
			uni.navigateTo({
				url: `/pages/shop/shop-detail/index?id=${this.shopId}`
			});
		},
		//滚动到底部
		reachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.searchList();
			}
		},

		//我的礼品卡列表
		myGiftCardList() {
			myGiftCardList(Object.assign({}, this.myPage)).then((res) => {
				if (res.data) {
					if (res.data.cardList) {
						this.myGiftCardListPages = res.data.cardList;
					}
					let { validAmt, totCnt } = { ...res.data };
					this.totCnt = totCnt || 0;
					this.validAmt = validAmt || 0;
					// console.log("validAmt",validAmt,"totCnt",totCnt);
				}
			});
		},

		getRedPackInfo() {
			getRedPackInfo().then((res) => {
				if (res.data) {
					this.redPackageInfo = res.data;
				}
			});
		},

		//购物车摇
		caetShake(isShake, num) {
			console.log('isShake', isShake, num);
			this.animation = '';
			setTimeout(() => {
				this.animation = `${isShake}`;
			}, 800);
			if (isShake) {
				//组件传入的购物车数量
				this.shoppingCartCount = num;
			}
		},

		//查询是否设置密码
		getIsPassword() {
			getIsPassword().then((res) => {
				console.log('getIsPassword', res);
				if (res.data) {
					// this.userpassword=res.data
					// isActive 0：未开通 1：已开通支付密码
					this.shopId = res.data.shopId;
					uni.setStorageSync('paypaymentuserpassword', res.data);
				}
			});
		},

		change(e) {
			//不传是全部 1实体;2电子
			console.log('e:', e);
			if (e != 0) {
				this.parameter.type = e;
			}
			this.refresh();
		},

		//搜索列表
		searchList() {
			// console.log("000000");
			let that = this;
			if (this.ifAvailable) {
				this.parameter.ifAvailable = this.ifAvailable;
			} else {
				delete this.parameter.ifAvailable;
			}
			if (this.sortList && this.sortList.length) {
				(this.parameter['columns'] = this.sortList[0].columns), (this.parameter['sortType'] = this.sortList[0].sortType);
			} else {
				delete this.parameter['columns'];
				delete this.parameter['sortType'];
			}
			console.log('this.sortList==>', this.sortList, 'this.parameter', this.parameter);
			// 礼品卡筛选列表接口
			giftCardList(Object.assign({}, util.filterForm(this.page), this.parameter)).then((res) => {
				console.log('res=======>', res);
				let giftCardList = res.data.records;
				this.giftCardList = [...this.giftCardList, ...giftCardList];
				console.log('giftCardList.length', giftCardList.length);
				if (giftCardList.length < this.page.size) {
					this.loadmore = false;
				}
				that.$nextTick(() => {
					let info = uni.createSelectorQuery().select('.shop-top');
					console.log('info', info);
					info.boundingClientRect(function (data) {
						//data - 各种参数
						that.topHeight = (data && data.height) || 0;
						// if (that.parameter.couponId > 0) that.topHeight += 140;
						console.log('that.topHeight', that.topHeight);
					}).exec();
				});
			});
		},

		// tab筛选
		sortHandle(e) {
			let type = e.target.dataset.type;
			switch (type) {
				//价格
				case 'price':
					if (this.price == '') {
						this.price = 'asc';
						console.log('price11');
						//升序
						this.sortList = [];
						let price = {
							column: 'price_down',
							sortType: 1
						};
						this.sortList.push(price);
					} else if (this.price == 'asc') {
						console.log('price222');
						this.sortList = [];
						let price = {
							columns: 'price_down',
							sortType: '0'
						};
						this.sortList.push(price);
						this.price = 'desc';
					} else if (this.price == 'desc') {
						console.log('price333');
						this.price = '';
						this.sortList = [];
					}
					this.sales = '';
					this.createTime = '';
					this.ifAvailable = '';
					break;
				//销量
				case 'sales':
					if (this.sales == '') {
						this.sales = 'desc';
						this.sortList = [];
						let sales = {
							columns: 'sale_num',
							sortType: '0'
						};
						console.log('sales11');
						this.sortList.push(sales);
					} else if (this.sales == 'desc') {
						console.log('sales22');
						this.sales = 'asc';
						this.sortList = [];
						let sales = {
							columns: 'sale_num',
							sortType: 1
						};
						console.log('sales11');
						this.sortList.push(sales);
					} else if (this.sales == 'asc') {
						console.log('sales33');
						this.sales = '';
						this.sortList = [];
					}
					this.price = '';
					this.goodsTitle = '';
					this.ifAvailable = '';
					break;
				//仅看有货
				case 'goods':
					console.log('this.ifAvailable', this.ifAvailable);
					if (this.ifAvailable == '') {
						console.log('ifAvailable--11');
						this.ifAvailable = '0';
						// this.page.descs = 'create_time';
						this.page.ascs = '';
					} else if (this.ifAvailable == '0') {
						console.log('ifAvailable--22');
						this.ifAvailable = '';
						// this.page.descs = '';
						this.page.ascs = '';
					}
					this.price = '';
					this.sales = '';
					// this.goodsTitle == ''
					break;
			}
			this.refresh();
		},

		//广告
		advertisement(id) {
			//礼品卡banner
			if (id == 'GIFT_PAGE') {
				api.advertisementinfo({
					bigClass: id
				}).then((res) => {
					this.payment = res.data;
				});
			}
		},

		bindCode() {
			// #ifdef MP-WEIXIN
			// 允许从相机和相册扫码
			uni.scanCode({
				scanType: ['barCode', 'qrCode'], //所扫码的类型 barCode	一维码 qrCode	二维码
				success: (res) => {
					if (res.result) {
						const code = res.result; //result	所扫码的内容 测试：6902902006811
						console.log('code', code);
						if (code) {
							if (code.indexOf('https://') > -1) {
							   	util.scanH5ToMaPage(code, api.qrcodequery);
								return;
							} else {
								this.bindCard = code;
							}
						}
					} else {
						console.log('请重新扫描');
						return false;
					}
				},
				fail: (res) => {
					console.log('未识别到二维码');
				}
			});
			// #endif

			// #ifdef APP-PLUS
			// 允许从相机和相册扫码
			mpaasScanModule.mpaasScan(
				{
					// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
					scanType: ['qrCode', 'barCode'],
					// 是否隐藏相册，默认false不隐藏
					hideAlbum: false
				},
				(res) => {
					if (res.resp_code == 1000 && res.resp_message == 'success') {
						const code = res.resp_result; //result	所扫码的内容
						if (code) {
							if (code.indexOf('https://') > -1) {
								util.scanH5ToMaPage(code, api.qrcodequery);
								return;
							} else {
								this.bindCard = code;
							}
						} else {
							uni.showToast({
								title: `未识别到内容`,
								icon: 'none',
								duration: 2000
							});
						}
					} else {
						// uni.showToast({
						// 	title: `未识别到信息`,
						// 	icon:"none",
						// 	duration: 2000
						// });
					}
				}
			);
			// #endif
		},
		//研发中
		payCode() {
			uni.showToast({
				icon: 'none',
				title: '功能研发中...敬请期待!',
				duration: 2000
			});
		},

		refresh() {
			this.loadmore = true;
			this.giftCardList = [];
			this.page.current = 1;
			this.searchList();
		}
	}
};
</script>

<style>
.uni-select__input-text {
	font-size: 34rpx !important;
	text-align: left;
	/* color: #000!important; */
}
.uni-select {
	border: none !important;
	padding: 0rpx !important;
	/* padding-left: 40rpx!important; */
	height: 50rpx !important;
}
</style>

<style lang="scss" scoped>
.img {
	width: 100%;
	border-radius: 20rpx;
}
.all-orders {
	width: 94% !important;
	margin: auto !important;
	// margin-top: 15rpx !important;
	border-radius: 20rpx !important;
}
.text-color {
	color: #653006;
}
.my-gift-card {
	border-radius: 15px;
	background: linear-gradient(90deg, #ccac92 0%, #b47a4b 0%, #c58d5f 100%);
	font-size: 26rpx;
	padding: 8rpx 8rpx 8rpx 25rpx;
	color: #ffffff;
}

.topfixed-active {
	width: 100%;
	position: fixed;
	top: 0rpx;
	left: 0rpx;
	background: #fff;
	z-index: 9999;
	box-sizing: border-box;
	margin: 0 auto;
	border-radius: none !important;
}
</style>
