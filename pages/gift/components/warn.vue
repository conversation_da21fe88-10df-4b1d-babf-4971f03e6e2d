<template>
  <view>
    <view class="text-center" style="width: 100%;margin: 0 auto;margin-top: 200rpx;">
			<image style="width: 136upx; height: 136upx;" mode="aspectFill"
          src="https://img.songlei.com/gift/warn-icon.png">
      </image>

			<view class="margin-top">
				- 您未设置支付密码 -
			</view>

			<!-- 确认按钮 -->
			<view class="btn" style="margin-top: 175rpx;">
        <button @tap="isToPage(btnTo)" style="width: 330rpx;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%); color: #FFF;" class="cu-btn round">设置支付密码
        </button>
      </view>

		</view>
  </view>
</template>
<script>
import { navigateUtil } from "@/static/mixins/navigateUtil.js";
export default {
  mixins: [navigateUtil],
  props:{
    // 设置支付密码
    btnTo:{
      type: String,
      default: '/'
    }
  },
  data(){
    return {

    }
  },
  methods:{
    isToPage(val){
      console.log("isToPage",val);
      if (val.indexOf('/pages')!==-1) {
        console.log("111");
        uni.navigateTo({
						url: `${val}`
					})
          this.$EventBus.$emit('isPassword',false)
          return
      }
      this.$emit('change',false)
    }
  }
}
</script>

<style>

</style>