<template>
  <view>
    <cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">支付密码设置</block>
	  </cu-custom>

		<warn-page v-if="isPasswordSet&&userpassword.isActive!='1'" @change="btn($event)"/>

		<view v-else>
			<view>
        <view style="margin-top: 100rpx;" class="text-xml text-black text-bold text-left padding-left-xl">
          {{password==1?'输入原密码':'输入验证码'}}
        </view>

        <view>
          <view style="width: 100%;" class="text-right padding-right">
            <button v-if="!tag" :disabled="msgKey" style="width: 210rpx;background:#F4E6D6; color: #AC785E;" class="text-sm  cu-btn round" @click="getPhoneCode">{{ msgText }}
            </button>
          </view>

          <view class="margin-top-sm">
            <view v-if="!tag" class="text-sm text-center" style="color: #000000;">
              请输入手机 <text style="color: #BD976F;"> {{userpassword.phone| phoneEncryption}} </text> 收到的短信验证码
            </view>
						
						<code-page @change="code($event)"/>

						<view style="width: 100%;" class="text-center padding-top margin-top-xl">
							<button @click="validateTo(password)" :disabled="isValidate" style="width: 343rpx;background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%); color: #FFF;" class="text-sm  cu-btn round">{{password==1?'确认':'验证'}}
							</button>
						</view>
          </view>
        </view>
      </view>
		</view>

    </view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
	import warnPage from "../../components/warn.vue";
	import codePage from "@/components/verification-code/index.vue";
 
	import { phoneCode, phoneCheck ,passwordCheck} from '@/pages/gift/api/gift'
	const MSGINIT = "获取验证码",
		MSGSCUCCESS = "${time}秒后可重发",
		MSGTIME = 60;
	export default {
		components: {
			warnPage,
			codePage
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '礼品卡支付设置',
				isPasswordSet:true,//是否设置密码
				msgText: MSGINIT,
				msgTime: MSGTIME,
				msgKey: false,
				isValidate:true,
				validateCode:'',
				password:'',//1是修改密码 2是忘记密码||设置密码
				tag:true,
				userpassword:{
					phone	:'',
					isActive:'0'//0：未开通 1：已开通支付密码
				},
			};
		},
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
				if (options) {
					// password 1是修改密码 2是忘记密码
					console.log("options",options);
					// if (options.isPasswordSet== 'false' || options.isPasswordSet == false) {
					// 	// this.isPasswordSet = false
					// }

					if (options.password) {
						this.password = options.password;
					}
				}
			});
		},

		mounted() {
		},

		onShow() {
			this.tag =uni.getStorageSync('isPassword')
			console.log("this.tag",this.tag);
			// 禁止分享
			uni.hideShareMenu()
		},

		created() {
		},

		onHide() {
		},

		computed: {
			// isShow(){
			// 	if (this.tag===1&&this.password&&this.password==1) {
			// 		return false
			// 	}
			// 	return true
			// }
		},

		created(){ 
			if (uni.getStorageSync('paypaymentuserpassword')) {
				this.userpassword=uni.getStorageSync('paypaymentuserpassword')
			}
		},

		methods: {
			//验证跳转
			validateTo(num){
				if (num==1) {
					passwordCheck(Object.assign({password:this.validateCode})).then(res=>{
						console.log("code==>",res);
						if (res.code == 0) {
							uni.setStorageSync('password',this.password)
							uni.navigateTo({
									url: "/pages/gift/set-password/password/index"
								})
						}
					})
					return
				}
				phoneCheck(Object.assign({phone:this.userpassword.phone,code:this.validateCode})).then(res=>{
						console.log("code==>",res);
						if (this.password==2) {
							uni.setStorageSync('password',this.password)
						}
						if (res.code == 0) {
							uni.navigateTo({
									url: "/pages/gift/set-password/password/index"
								})
						}
					})
			},
			// 是否设置密码
			async	btn(bool){
				this.isPasswordSet= bool;
				this.tag = bool
			},

			//输入框
			async	code(val){
				console.log("val",val);
				if(val){
					this.isValidate = false
					this.validateCode= val
				}
			},
			//获取验证码
			getPhoneCode(){
				if (this.msgKey) return
				phoneCode(Object.assign({phone:this.userpassword.phone,type:'1',codeSize:'6'})).then(res=>{
          console.log("res=======>",res);
					if (res.code == 0) {
						uni.showToast({
									title: '验证码发送成功',
									icon: 'none',
									duration: 2000
						});
						this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						this.msgKey = true
						const time = setInterval(() => {
							this.msgTime--
							this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
							if (this.msgTime == 0) {
								this.msgTime = MSGTIME
								this.msgText = MSGINIT
								this.msgKey = false
								clearInterval(time)
							}
						}, 1000)
					}
        })

			}
    }
	};
</script>

<style scoped>
</style>
