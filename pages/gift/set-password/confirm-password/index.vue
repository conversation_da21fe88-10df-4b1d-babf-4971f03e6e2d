<template>
	<view>
		<cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">支付密码设置</block>
		</cu-custom>

		<view>
			<view>
				<view style="margin-top: 100rpx;" class="text-xml text-black text-bold text-left padding-left-xl">
					设置密码
				</view>

				<view>
					<view class="margin-top-sm">
						<view v-if="text" class="text-sm text-center text-red">
							{{text}}
						</view>
						<view class="text-sm text-center" style="color: #000000;">
							请再输入一次刚才设置过的支付密码
						</view>

						<code-page @change="code($event)" :passwordShow="true" />
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
	import codePage from "@/components/verification-code/index.vue";
	import {
		setPassword,
		editPassword
	} from '@/pages/gift/api/gift'
	export default {
		components: {
			codePage
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '礼品卡支付设置',
				password: '',
				conformPassword: '',
				text: ''
			};
		},
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
				if (options) {
					if (options.password) {
						this.password = options.password
					}
				}
			});
		},
		mounted() {

		},

		onShow() {
			// 禁止分享
			uni.hideShareMenu()
		},

		onHide() {},

		computed: {},

		methods: {
			//输入框
			async code(val) {
				console.log("val", val);
				if (val) {
					if (this.password === val) {
						if (uni.getStorageSync('password')) {
							editPassword(Object.assign({
								password: val
							})).then(res => {
								console.log("setPassword=>", res);
								if (res.code == 0) {
									uni.navigateBack({
										delta: 3
									});
									// uni.redirectTo({
									// 	url: `/pages/gift/set-password/index?success=1`
									// })
								}
							})
							return
						}
						setPassword(Object.assign({
							password: val
						})).then(res => {
							console.log("setPassword=>", res);
							if (res.code == 0) {
								 uni.navigateBack({
								    delta: 3, // 关闭前面2个页面
								    success: function() {
								      // 关闭成功后，跳转到目标页面
								      uni.navigateTo({
								        url:`/pages/gift/set-password/index?success=1`
								      });
								    },
								    fail: function() {
								      console.log('关闭页面失败');
								    }
								  });
							}
						})
					} else {
						this.text = '密码不一致'
					}
				}
			},
		}
	};
</script>

<style lang="scss" scoped>
</style>