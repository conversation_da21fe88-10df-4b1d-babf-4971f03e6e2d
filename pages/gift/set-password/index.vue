<template>
  <view>
    <cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">支付密码</block>
	  </cu-custom>
		
		<view class="relative">
			<view class="message-input " :style="{ bottom: bottomVal}">
				<!-- 修改密码 -->
				<view class="padding-top padding-bottom flex margin-right margin-left justify-between" style="border-bottom: 1rpx solid #ccc;">
					<view>
						<text class="text-black text-lg text-bold">支付密码</text>
					</view>
				</view>

				<!--开关  -->
				<view class="padding-top padding-bottom flex margin-right margin-left justify-between" style="border-bottom: 1rpx solid #ccc;">
					<view>
						<view class="text-black text-xsm">免密支付</view>
						<view class="text-black text-xs" style="color: #999999;">消费金额{{passwordlessAmount}}元内无需输入密码</view>
					</view>

					<view>
						<switch :class="isSwitch == '1'?theme.themeColor+'checked':''" :checked="isSwitch == '1'" @change="isSwitchChange" style="transform:scale(0.8)"/>
					</view>
				</view>

				<view @tap="navTo(2)" class="padding-top padding-bottom flex margin-right margin-left justify-between" style="border-bottom: 1rpx solid #ccc;">
					<view>
						<text class="text-black text-xsm">忘记密码</text>
					</view>

					<!--<navigator url="/pages/gift/set-password/code/index?password=2" hover-class="none" style="color: #BB8368;">-->
					<view style="color: #BB8368;">
						<text class="cuIcon-right text-light-gray"></text>
					</view>
				</view>

				<view @tap="navTo(1)" class="padding-top padding-bottom flex margin-right margin-left justify-between" style="border-bottom: 1rpx solid #ccc;">
					<view>
						<text class="text-black text-xsm">修改密码</text>
					</view>

					<view>
						<text class="cuIcon-right text-light-gray"></text>
					</view>
				</view>

				<view @tap="navTo(3)" class="padding-top padding-bottom flex margin-right margin-left justify-between">
					<view>
						<text class="text-black text-xs" style="color: #BB8368;">安全使用说明</text>
					</view>

					<view>
					<!--<text class="cuIcon-right text-light-gray"></text>-->
					</view>
				</view>
			</view>
		</view>

		<!-- 开启关闭免密弹框 -->
		<view 
		class="cu-modal" 
		:class="modalPassword ? 'show' : ''" 
		catchtouchmove="touchMove"
		>
    <!-- 关闭弹框icon -->
    <text class="cuIcon-close" style="width:40rpx;height:44rpx;color: #FFF;position: absolute;top: 28%;right: 5%;border: 1rpx solid #FFF;width: 40rpx;border-radius: 50%;" @tap="hideModalSku()" :style="{top:modalText?'12%':'28%'}"></text>
			<view 
				class="cu-dialog dialo-sku bg-white " 
				:class="modalPassword ? 'animation-slide-bottom' : ''" 
				style="border-radius: 30rpx;"
				:style="{height:modalText?'880rpx':'450rpx'}"
				@tap.stop
			>
        <!-- 说明信息 -->
        <view v-if="!modalText" class="padding-sm">
          <view class="text-xdf text-black">{{isOpen?'开启免密支付':'关闭免密支付'}}</view>
					<view class="cu-item padding-top">
						<view class="text-xl margin-bottom-xl padding-bottom-xl">
							<text class="text-lg text-darkgrey ">请输入支付密码，以验证身份</text>
						</view>
						<code-page @change="code($event)" @changeBottomVal="changeBottomVal" :passwordShow="true"/>
					</view>
        </view>

				<scroll-view scroll-y="true" style="height:880rpx" v-if="modalText" class="padding">
					<view class="text-center text-bold"> 安全使用说明</view>
					<view class="text-black text-xs padding-bottom text-left">
						1、妥善保管好自己的账户和密码：不要在任何时候以任何方式向别人泄露自己的密码：松鼠美淘绝对不会以任何名义、任何方式向用户索取密码；松鼠美淘联系用户一律使用公司固定电话，对外电话显示区号为0451，任何时候都不会使用手机联系用户，并且工作人员都会主动向用户报上花名。如果有人知道了您的松鼠美淘账户支付密码，请立即更改并联系我们；如果您向别人透露银行密码，请及时到银行柜台办理修改密码手续。
					</view>
					<view class="text-black text-xs padding-bottom text-left">
						2、创建一个安全密码：松鼠美淘的登录密码和支付密码一定要分别设置，不能为了方便设置成同样一个密码。密码最好是数字加上字母以及符号的组合，尽量避免选择用您的生日和昵称作为登录密码或支付密码。请不要使用其他的在线服务（比如易趣、MSN或网上银行）一样的密码。在多个网站中使用一样的密码会增加其他人获取您的密码并访问您的账户的可能性；
					</view>
					<view class="text-black text-xs padding-bottom text-left">
						3、认真核实松鼠美淘的网址：每次登录尽量直接输入正确网址，例：www.songlei.com，不要从来历不明的超级链接访问网站；松鼠美淘登录页面的网址开头为：https://www.songlei.com 。如果网址中第一个斜杠(/)前面没有立即出现 songlei.com ，请千万不要输入您的松鼠美淘用户名和密码。
							请仅在路径以 https://www.songlei.com 开头的页面上输入松鼠美淘密码，而不要在其他路径的页面中输入。即使在当前网页的网址中包含有 songlei 一词，也有可能不是松鼠美淘公司的网站。
							那些假冒网站（也称为“欺诈”网站）会试图模仿松鼠美淘的样式风格来获得您的密码以及对您账户的访问权限。如果网址中斜杠前包含其他字符，如 @、下划线等，那么该网站绝不是松鼠美淘的网站。
					</view>
					<view class="text-black text-xs padding-bottom text-left">
						4、开通专业版网银进行付款：您如果经常进行网上消费，建议您前往银行柜台办理网上银行专业版开通手续，在自己的上网终端上安装网上银行数字证书，确保银行账户安全；
					</view>
					<view class="text-black text-xs padding-bottom text-left">
						5、遵守松鼠美淘交易流程购物：
							对于买家：请买家一定要在自己收到货且没有异议后去点击确认收到货，如您在未收到货或者收到的货物与卖家的描述不符的情况下，就先点击确认收到货，您将因此承担不必要的损失。您在付款后请经常查看松鼠美淘提示的“完成本次交易”的剩余时间，如果您长时间没有收到货物，请及时向松鼠美淘提出退款申请；在您提出退款申请后，该申请将在您收到货物或者争议解决后撤销。
							对于卖家：请卖家一定要在确认买家付款以后，再发货。如买家还没有付款的情况下，您就先发货了，没有按照松鼠美淘流程操作，造成的损失由您本人承担。
					</view>
				</scroll-view>
			</view>
		</view>

    </view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
	import codePage from "@/components/verification-code/index.vue";

	import { getIsPassword,openPayment } from "@/pages/gift/api/gift";
	export default {
		components: {
			codePage
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '支付密码',
				isSwitch:0,//0 关 1开
				modalPassword:false,//密码弹框
				isOpen:0,
				modalText:true,//安全说明弹框
				passwordlessAmount:0,//免密金额
				bottomVal:'',//盒子高度问题
			};
		},
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
				if(options){
					// 设置密码成功
					if (options.success==1) {
						uni.removeStorageSync('password')
						let paypaymentuserpassword =  uni.getStorageSync('paypaymentuserpassword')
						paypaymentuserpassword.isActive="1"
						uni.setStorageSync('paypaymentuserpassword',paypaymentuserpassword)
						uni.showToast({
							title:'密码设置成功',
							icon: 'success',
							duration: 2000
						});
					}
				}
			});
		},
		mounted() {
	
		},

		onShow() {
		},

		onHide() {
		},

		computed: {
		},
		created(){
			this.getIsPassword()
		},
		methods: {
		//解决输入框顶上去
		changeBottomVal(val){
			this.bottomVal = val
		},
		//输入框
		async	code(val){
				console.log("val",val);
				if (val) {
					let isPasswordless=this.isOpen?'1':'0'
					console.log("isPasswordless",isPasswordless);
					openPayment(Object.assign({password:val,isPasswordless})).then(res=>{
						console.log("setPassword=>",res);
						if (res.code == 0) {
							uni.showToast({
								title:`已${this.isOpen?'开启':'关闭'}支付密码`,
								icon:'none',
								duration: 2000
							});
							this.hideModalSku()
						}
					}).catch((e)=>{
						console.log("ee",e);
						this.getIsPassword()
					})
				}
			},

			//关闭弹框
			hideModalSku() {
				if (!this.modalText) {
					this.getIsPassword()	
				}
				this.modalPassword = false;
			},

			//忘记修改
			navTo(num){
				// 2是忘记 1是修改 3安全说明
				if (num==3) {
					this.modalText=true;
					this.modalPassword=true
					return
				}
				if (num==1) {
					uni.setStorageSync('isPassword',true)
				}else{
					uni.setStorageSync('isPassword',false)
				}
				uni.navigateTo({
					url: `/pages/gift/set-password/code/index?password=${num}`
				})
			},

			//查询是否设置免密
    async	getIsPassword(){
			this.isSwitch=this.isOpen?'1':'0'
        getIsPassword().then((res=>{
						if (res.data) {
							this.isSwitch = res.data.isPasswordless
							this.passwordlessAmount = res.data.passwordlessAmount
						}
        }))
      },

			//开关 0 关 1开启
			isSwitchChange(e){
				this.isOpen = e.detail.value;
				this.modalPassword = true;
				this.modalText=false;
			},
    }
	};
</script>

<style lang="scss" scoped>
		.message-input {
        position: absolute;   // input 所在盒子设置绝对定位
        flex-shrink: 0;
        width: 100%;
				top:0;
        left: 0;
        bottom: 0;  // 默认 0
        z-index: 199;
        padding-bottom: env(safe-area-inset-bottom);
        // background: #FFFFFF;
        box-sizing: border-box;
		}
</style>
