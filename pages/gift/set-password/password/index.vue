<template>
  <view>
    <cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">支付密码设置</block>
	  </cu-custom>

		<view>
			<view>
        <view style="margin-top: 100rpx;" class="text-xml text-black text-bold text-left padding-left-xl">
          设置密码
        </view>

        <view class="">
          <view class="margin-top-sm">
            <view class="text-sm text-center" style="color: #000000;">
              请设置您的支付密码
            </view>
						
						<code-page @change="code($event)" :passwordShow="true"/>
          </view>
        </view>
      </view>
		</view>

    </view>
</template>

<script>
	const app = getApp();

	import api from 'utils/api'
	// import jweixin from '@/utils/jweixin'
	import util from '@/utils/util'
	import codePage from "@/components/verification-code/index.vue";

	export default {
		components: {
			codePage
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '密码设置',
				isPasswordSet:false,//是否设置密码
			};
		},
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
				
			});
		},
		mounted() {
	
		},

		onShow() {
			// 禁止分享
			uni.hideShareMenu()
		},

		onHide() {
		},

		computed: {
		},

		methods: {
			//输入框
		async	code(val){
				console.log("val",val);
				if (val) {
					uni.navigateTo({
						url: `/pages/gift/set-password/confirm-password/index?password=${val}`
					})
				}
			},
    }
	};
</script>

<style lang="scss" scoped>
</style>
