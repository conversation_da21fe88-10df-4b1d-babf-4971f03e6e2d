<template>
  <view>
    <cu-custom :bgColor="'#BC8468'" :isBack="true" :hideMarchContent="true">
				<block slot="backText">返回</block>
				<block slot="content">礼品卡账单</block>
	  </cu-custom>
		
		<view class="content-head">
      <picker
        mode="date"
        :value="billPage.reqDate"
        fields="month"
        @change="bindDateChange"
      >
        <view>
          <view class="top-date padding-left-sm padding-right-sm">
            <!-- <view style="margin-left:15rpx;margin-right:15rpx;font-size:30rpx;color:#000000">
            本月
          </view> -->
            {{date}}
            <text class="cuIcon-unfold"></text>
          </view>
          <!-- <view class="total">
            <text>支出￥12015.4</text>
            <text>收入￥755.20</text>
          </view> -->
        </view>
      </picker>

			<scroll-view scroll-x class="bg-white collection-types" style="box-shadow: none;z-index: 1;">
				<view class="flex text-center" style="background-color: #eee;" :style="{ width: collectTypeWidth }">
					<view :class="'btn ' +(item.key == type ? 'cur bg-' + theme.backgroundColor : '')" v-for="(item, index) in couponArr" :key="index" @tap="navTabSelect" :data-index="index" :data-key="item.key">
						{{ item.value }}
					</view>
				</view>
			</scroll-view>
    </view>


    <view class="cu-card no-card" style="margin-top:180rpx">
      <view
        class="cu-item padding-xs"
				style="
					width: 97%;
					margin: auto;
					border-radius: 20rpx;
					margin-bottom: 20rpx;"
        v-for="(item,index) in billList"
        :key="index"
      >
				<view class="flex align-center padding-left-sm padding-top-sm padding-bottom-sm bill">
					<text class="text-xsm">订单号： </text>
					<view class="flex-twice text-xsm">{{item.orderNo}}</view>
					<view class="flex-twice text-xsm text-center">{{item.transactionDate}}</view>
				</view>

				<view 
					class="flex justify-between margin padding-bottom-sm "
					:class="item.open?'bill':''"
					style="margin-left:10rpx;margin-bottom: 0;">
					<view class="flex justify-end">
						<view class="text-center">
							<image
              :src="item.icon || 'https://img.songlei.com/live/img/no_pic.png'"
              mode="aspectFill"
              class="radius-text"
            />
						</view>

						<view class="text-xsm margin-left-xs">
							<view>
								{{item.shopName}}
							</view>
							<view>
							{{item.orderDesc}}
							</view>
						</view>
					</view>

					<!-- <view class="text-xsm text-center" :class="String(item.transactionAmount).startsWith('-')?'text-red':'text-black'">
						{{String(item.transactionAmount).startsWith('-')?`${item.transactionAmount}`:`+${item.transactionAmount}`}}
					</view> -->
					<view class="margin-right-xs">
              <format-price :styleProps="priceStyle" signFontSize="18rpx" :color="String(item.transactionAmount).startsWith('-')?'#e54d42':'#000'" :price="`${item.transactionAmount}`"
              priceFontSize="28rpx" />
          </view>
				</view>

				<view v-if="item.open==true&&item.bills&&item.bills.length">
					<view class="" v-for="(data,index1) in item.bills"
						:key="index1" 
						style="margin: 26rpx 25rpx 29rpx 88rpx;">
						<view class="bill">
							<view class="flex align-center">
								<text class="text-xsm text-light-gray">卡号： </text>
								<view class="flex-twice text-xsm text-light-gray">{{data.cardNo}}</view>
								<view class="flex-twice text-xsm text-center cardName-line text-light-gray">{{data.cardName}}</view>
							</view>
		
							<view class="flex justify-between">
								<view class="text-xsm text-light-gray">
									{{data.statusDesc}}:
								</view>
		
								<view class="margin-right-xs">
                    <format-price :styleProps="priceStyle" signFontSize="18rpx" :color="String(data.amount).startsWith('-')?'#e54d42':'#000'" :price="`${data.amount}`"
                    priceFontSize="28rpx" />
                </view>
							</view>
	
							<view class="flex justify-between">
								<view class="text-xsm text-light-gray">
									余额:
								</view>

								<view class="margin-right-xs">
                    <format-price :styleProps="priceStyle" signFontSize="18rpx" :color="String(data.cdlYe).startsWith('-')?'#e54d42':'#000'" :price="`${data.cdlYe}`"
                    priceFontSize="28rpx" />
                </view>
							</view>
						</view>
					</view>
				</view>

				<view @click="changeContent(item,index)"  class="text-center" :class="item.open?'cuIcon-fold':'cuIcon-unfold'"></view>
      </view>
      <view :class="'cu-load ' + (loadmoreRecommendList ?'loading':'over')"></view>
    </view>
  </view>
</template>

<script>
const app = getApp();

const util = require("utils/util.js");
import __config from 'config/env';
import api from 'utils/api';
import validate from 'utils/validate';
import formatPrice from "@/components/format-price/index.vue";
import { getCardbill } from "@/pages/gift/api/gift";
import { mapState } from 'vuex' 

	export default {
		components: {
			formatPrice
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				//有数统计使用
				page_title: '礼品卡账单',
				date: '',
        billPage: {
					reqDate: '',//年月
					pageNo: 1,//页码
					pageSize: 20,//最多20条
        },
				// 优惠券所有数据
				couponArr: [{
						value: '全部',
						key: '',
						twoTabCur: 0,
					}, {
						value: '线上消费',
						key: '0',
						twoTabCur: 0,
					}, {
						value: '门店消费',
						key: '1',
						twoTabCur: 0,
					}],
				type: '',//默认全部
				billList: [],
				loadmoreRecommendList: true,
				};
		},
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
				if(options){
					 //获取当前月份
					this.getInitDate()
				}
			});
		},
		mounted() {
		},

		onShow() {
		},

		onHide() {
		},

		computed: {
			...mapState([
      	'CustomBar',
      ]),

			collectTypeWidth() {
        const length = this.couponArr.length;
        if(length <= 4) {
          return '750rpx'
        } else {
          return 187.5 * length + 'rpx'
        }
      }
		},

		created(){
		},

		// 页面滚动到底部的事件
		onReachBottom () {
    if (this.loadmoreRecommendList) {
      this.billPage.pageNo = this.billPage.pageNo + 1;
      this.getCardbill();
    }
  },

		// 监听用户下拉动作，一般用于下拉刷新
		onPullDownRefresh () {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();//停止当前页面下拉刷新。
		},

  methods: {
		// 控制折叠收起
		changeContent(item,index) {
      this.billList.forEach(i => {
          if (i.open !== this.billList[index].open) {
            i.open = false;
          }
        })
        item.open = !item.open
    },
		// 导航栏切换
		navTabSelect(e) {
				let dataset = e.currentTarget.dataset;
				this.type = dataset.key;
				console.log("type",this.type);
				//0 线上 //1 线下
				if (this.type) {
					//全部
					this.billPage.channel = this.type
				}else{
					delete this.billPage.channel
				}
        this.refresh();
			},

    //获取当前月份
    getInitDate () {
      var date = new Date()
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      if (month / 10 < 1) month = '0' + month
      this.date = year + '年' + month + '月'
      this.billPage.reqDate = `${year}-${month}`;
			this.getCardbill()
    },

		//修改年月
    bindDateChange (e) {
      this.date = e.target.value;
      let reqDate = e.target.value.replace("年", '-');
      this.billPage.reqDate = reqDate.replace("月", '');
      console.log("date", this.date, this.billPage.reqDate);
      this.loadmoreRecommendList = true
      this.refresh();
    },

    //获取电子账单明细  
    getCardbill () {
			getCardbill(Object.assign({},this.billPage)).then((res) => {
				console.log("getCardbill", res);
				if (res.data) {
					let giftCardList = res.data
					giftCardList.forEach((el,index) => {
								this.$set(giftCardList[index],'open',false)
								
							});
					this.billList = [...this.billList, ...giftCardList];
					console.log("giftCardList.length",giftCardList.length,giftCardList);
					if (giftCardList.length  < this.billPage.pageSize) {
						this.loadmoreRecommendList = false;
					}
				}
			}).catch((err) => {
				this.loadmoreRecommendList = false;
				console.log(err);
			})
    },

    //重置
    refresh () {
      this.loadmoreRecommendList = true;
      this.billList = [];
      this.billPage.pageNo = 1;
      this.getCardbill();
    }

  }
};
</script>
<style scoped>
.cardName-line{
	white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}
.radius-text{
	margin: auto;
	/* background-color:#00A1DE; */
	width: 76rpx;
	height: 76rpx;
	border-radius: 40rpx;
	/* line-height: 76rpx; */
	/* color: #FFFFFF; */
}
.collection-types {
  top: unset !important;
}
.content-head {
  position: fixed;
  width: 100%;
	z-index: 999;
}
.bill {
  border-bottom: 1rpx solid #dbcecd;
}
.top-bill {
  border-top: 1rpx solid #dbcecd;
}
.top-date {
  display: flex;
  /* width: 40%; */
  align-items: center;
  height: 80rpx;
  background: #efefef;
}
.total {
  width: 60%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 24rpx;
  color: #a2a2a2;
  background: #efefef;
}

.btn {
  height: 50rpx;
  width: 352rpx;
  line-height: 50rpx;
  margin: 18rpx 7rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  background-color: #fff;
}
</style>

