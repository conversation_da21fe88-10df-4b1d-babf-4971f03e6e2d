<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">松雷电子礼品卡</block>
		</cu-custom>

		<view class="result-box">
			<view class="padding flex flex-direction" style="padding-bottom:10rpx;padding-top:40rpx">
				<view class="text-sxl text-center text-success flex align-center">
					<image style="width: 50rpx; height: 50rpx;" src="https://img.songlei.com/gift/ok.png"></image>
					<text style="color: #1592FC; ">{{orderInfo.trans || '支付成功'}}</text>
				</view>

				<view v-if="orderInfo" class="flex margin-top-sm lines-black text-center">
					<view class="flex-twice text-price" style="flex: 5;font-size: 80rpx">{{orderInfo.amount}}
					</view>
				</view>

				<navigator open-type="navigateBack" class="result-btn" delta="1">完成
				</navigator>
			</view>


			<view class="user-line"></view>

			<view class="margin-left-sm margin-top-lg flex align-center">
				<text class="margin-left flex-sub text-df text-gray">终端号:</text>
				<view class="flex-twice text-df text-black"
					style="text-align: right; padding-right: 40rpx;display:flex;flex-direction: row;justify-content: flex-end;align-items: center;">
					{{orderInfo.term}}
				</view>
			</view>

			<view class="margin-left-sm margin-top-lg flex align-center">
				<text class="margin-left flex-sub text-df text-gray">订单号:</text>
				<view class="flex-twice text-df text-black"
					style="text-align: right; padding-right: 40rpx;display:flex;flex-direction: row;justify-content: flex-end;align-items: center;">
					{{orderInfo.orderId}}
				</view>
			</view>

			<view class="margin-left-sm margin-top-lg flex align-center">
				<text class="margin-left flex-sub text-df text-gray">订单时间:</text>
				<view class="flex-twice text-df text-black"
					style="text-align: right; padding-right: 40rpx;display:flex;flex-direction: row;justify-content: flex-end;align-items: center;">
					{{orderInfo.orderDate}}
				</view>
			</view>

			<view class="margin-left-sm margin-top-lg flex align-center">
				<text class="margin-left flex-sub text-df text-gray">支付方式:</text>
				<view class="flex-twice text-df text-black"
					style="text-align: right; padding-right: 40rpx;display:flex;flex-direction: row;justify-content: flex-end;align-items: center;">
					松雷礼品卡
				</view>
			</view>

			<navigator v-if="payment" hover-class="none" class="result-img" :url="payment.linkUrl">
				<image class="img" mode="widthFix" :src="payment.imgUrl"></image>
			</navigator>
		</view>
		<recommendComponents canLoad />
	</view>
</template>

<script>
	const app = getApp();
	import __config from 'config/env';
	import api from 'utils/api'
	import util from 'utils/util'

	import formatPrice from "@/components/format-price/index.vue";
	import recommendComponents from "components/recommend-components/index";

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderInfo: {
					// "term":"20100001" ,"orderId":"123" ,"orderDate":"2023-8-28 12:00:00","amount":"123.00"
				},
				paymentPrice: "", //实付金额
				payment: {}, //广告
				//有数统计使用
				page_title: '一码付结果页',

				pollingTimer: null,
				requestTask: null,
			};
		},

		components: {
			formatPrice,
			recommendComponents
		},
        
		onLoad() {
		   if (uni.getStorageSync('giftPayNotifyContent')) {
		   	let orderInfo = uni.getStorageSync('giftPayNotifyContent')
		   	console.log("=======giftPayNotifyContent=====", orderInfo);
		   	this.orderInfo = JSON.parse(orderInfo);
		   	console.log("======= this.orderInfo=====", this.orderInfo);
		   }	
		},
		
		onShow() {
			let that = this;
			app.initPage().then(res => {
				this.advertisement("GIFTCODE_PAY_SUCCESS")
				this.pollingTimer = setTimeout(function() {
					that.longLink("/mallapi/userinfo/listener", "get")
				}, 1000)
			});
		},


		onHide() {
			// 在页面隐藏时清除定时器，以防止内存泄漏
			console.log("onHide", this.pollingTimer, this.requestTask);
			this.requestTask ? this.requestTask.abort() : '';
			if (this.pollingTimer) {
				clearTimeout(this.pollingTimer);
			}
		},

		onUnload() {
			uni.setStorageSync('giftPayNotifyContent', null)
			// 在用户退出页面时清除定时器，以防止内存泄漏
			console.log("onUnload", this.pollingTimer, this.requestTask);
			this.requestTask ? this.requestTask.abort() : ''
			if (this.pollingTimer) {
				clearTimeout(this.pollingTimer);
			}
		},


		methods: {
			//长轮训，回调支付结果，超时之后继续轮训，直到获取到结果
			longLink(_url, method) {
				let that = this;
				console.log("回调支付结果", _url, method);
				const userInfo = uni.getStorageSync('user_info')
				that.requestTask = uni.request({
					url: __config.basePath + _url,
					method: method,
					withCredentials: true,
					header: {
						//#ifdef H5
						'client-type': util.isWeiXinBrowser() ? 'H5-WX' : 'H5', //客户端类型普通H5或微信H5
						'tenant-id': app.globalData.tenantId || __config.tenantId,
						'app-id': app.globalData.appId ? app.globalData.appId : '', //微信h5有appId
						//#endif
						//#ifdef MP-WEIXIN
						'client-type': 'MA', //客户端类型小程序
						'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
						//#endif
						//#ifdef APP-PLUS
						'client-type': 'APP', //客户端类型APP
						'tenant-id': app.globalData.tenantId,
						//#endif
						'third-session': uni.getStorageSync('third_session') ? uni.getStorageSync(
							'third_session') : '',
						'user_id': userInfo ? userInfo.id : ''
					},
					success: (res) => {
						console.log("=====success页面===============", res)
						if (res.statusCode === 200 && res.data && res.data.notifyType == 'GIFTCARD') {
							// 接口返回成功状态码，处理数据并结束轮询
							that.handleSuccess(res.data);
							that.pollingTimer&&clearTimeout(that.pollingTimer);
							//这里还有继续轮训，因为还可能撤回等，更新状态
							that.pollingTimer = setTimeout(function() {
								that.longLink("/mallapi/userinfo/listener", "get")
							}, 1000)
						} else {
							// 接口返回非成功状态码，继续下一轮轮询
							that.pollingTimer = setTimeout(function() {
								that.longLink("/mallapi/userinfo/listener", "get")
							}, 1000)
						}
					},
					fail: (err) => {
						// 请求失败处理，继续下一轮轮询
						that.pollingTimer = setTimeout(function() {
							that.longLink("/mallapi/userinfo/listener", "get")
						}, 1000)
					}
				});
			},

			// 处理成功返回的数据
			handleSuccess(data) {
				//刷新状态
				this.orderInfo = JSON.parse(data.notifyContent);
				console.log("====刷新状态====",this.orderInfo )
			},
			//获取广告
			advertisement(id) {
				let that = this;
				api.advertisementinfo({
					bigClass: id
				}).then(res => {
					console.log("res", res.data);
					this.payment = res.data;
				});
			},
		}
	};
</script>
<style scoped>
	.result-box {
		width: 750rpx;
		background: white;
	}

	.result-btn {
		width: 60%;
		height: 80rpx;
		border: 1px solid #ffffff;
		background: #1592FC;
		border-radius: 31px;
		margin: 20rpx auto;
		text-align: center;
		line-height: 80rpx;
		color: #fff;
		font-size: 34rpx;
	}

	.result-img {
		width: 95%;
		margin: 0 auto;
		margin-top: 50rpx;
		padding-bottom: 60rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
	}

	.img {
		/* height: 296rpx; */
		width: 100%;
		height: auto;
		border-radius: 20rpx;
	}

	.user-line {
		margin: 14rpx auto;
		width: 699rpx;
		border-bottom: 2rpx dashed #999;
	}

	.color-white {
		color: #999999;
	}

	.color-white .text-sm {
		font-size: 30rpx !important;
	}

	.text-success {
		display: flex;
		margin: 0 auto;
	}

	.text-success image {
		width: 70rpx;
		height: 70rpx;
		margin-right: 20rpx;
	}
</style>