<template>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">资讯</block>
    </cu-custom>
    <swiper
      class="screen-swiper square-dot"
      :indicator-dots="true"
      :circular="true"
      :autoplay="true"
      interval="5000"
      duration="500"
    >
      <swiper-item
        v-for="(item,index) in articleListBanner"
        :key="index"
        @tap="jumpPage(item.id)"
      >
        <image :src="item.picUrl"></image>
      </swiper-item>
    </swiper>
    <scroll-view
      scroll-x
      class="bg-white nav solid-bottom"
    >
      <view class="flex text-center">
        <view
          :class="'cu-item flex-sub ' + (-1==tabCur?'cur text-'+theme.themeColor:'')"
          @tap="getHot()"
        >热门</view>
        <view
          :class="'cu-item flex-sub ' + (index==tabCur?'cur text-'+theme.themeColor:'')"
          v-for="(item, index) in articleCategoryList"
          :key="index"
          @tap="tabSelect(item.id, index)"
        >{{item.name}}</view>
      </view>
    </scroll-view>
    <view
      class="cu-card case"
      :class="isCard?'no-card':''"
    >
      <navigator
        class="cu-item shadow"
        v-for="(item,index) in articleList"
        :key="index"
        hover-class="none"
        :url="'/pages/article/article-info/index?id=' + item.id"
      >
        <view class="image">
          <image
            :src="item.picUrl"
            style="width: 100%; height: 280rpx;"
            mode="aspectFill"
          ></image>
        </view>
        <view class="cu-list padding">
          <view class="text-black overflow-2">{{item.articleTitle}}</view>
          <view class="text-gray text-sm flex justify-between margin-top-xs">
            <view class="text-xs text-gray">{{item.authorName}}</view>
            <view class="text-xs text-gray">{{item.updateTime}}</view>
          </view>
        </view>
      </navigator>
      <view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
    </view>
  </view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				loadmore: true,
				articleListBanner: [],
				articleList: [],
				articleCategoryList: [],
				tabCur: -1,
				categoryId: null,
				//有数统计使用
				page_title:'文章列表'
			}
		},
		
		onLoad(options) {
			app.initPage().then(res => {
				//查出banner文章
				this.articlePageBanner({
					isBanner: '1'
				})
				//查出热门文章
				this.getHot()
				//查出文章分类
				this.articleCategoryPage()
			});
		},
		
		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.articlePage();
			}
		},
		
		methods: {
			articlePageBanner(parameter) {
				api.articlePage(Object.assign({}, {
					searchCount: false,
					current: 1,
					size: 5,
					ascs: '',
					//升序字段
					descs: ''
				}, util.filterForm(parameter))).then(res => {
					let articleListBanner = res.data.records;
					this.articleListBanner = articleListBanner;
				});
			},
			articlePage() {
				api.articlePage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let articleList = res.data.records;
					this.articleList = [...this.articleList, ...articleList];
					if (articleList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},
			articleCategoryPage() {
				api.articleCategoryPage({
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: ''
				}).then(res => {
					let articleCategoryList = res.data.records;
					this.articleCategoryList = articleCategoryList;
				});
			},
			getHot(){
				this.tabCur = -1
				this.articleList = []
				this.loadmore = true
				this.page.current = 1
				this.parameter = {
					categoryId: null,
					isHot: '1'
				}
				this.articlePage()
			},
			tabSelect(id, index){
				this.tabCur = index
				this.articleList = []
				this.loadmore = true
				this.page.current = 1
				this.parameter = {
					categoryId: id,
					isHot: null
				}
				this.articlePage()
			},
			jumpPage(id) {
				uni.navigateTo({
					url: '/pages/article/article-info/index?id='+id
				});
			}
		}
	}
</script>

<style>
.screen-swiper {
  min-height: 280rpx !important;
}
</style>
