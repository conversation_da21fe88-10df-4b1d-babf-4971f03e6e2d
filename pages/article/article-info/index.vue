<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content"><text class=" overflow-1">{{ articleInfo.articleTitle }}</text></block>
    </cu-custom>

    <view class="bg-white padding">
      <view class="text-bold text-black text-xl">{{
        articleInfo.articleTitle
      }}</view>
      <view class="flex justify-between margin-top-sm">
        <view class="text-sm text-gray">{{ articleInfo.authorName }}</view>
        <view class="text-sm text-gray">{{ articleInfo.updateTime }}</view>
      </view>
    </view>

    <view
      v-if="article_description"
      class="padding"
    >
      <!-- <view class="img-box flex">
				<image class="article-image" :src="articleInfo.picUrl" mode="aspectFill"></image>
			</view> -->
      <view
        class="margin-top-sm text-lg"
        style="line-height: 2em"
      >
        <jyf-parser :html="article_description"></jyf-parser>
      </view>
    </view>

    <view v-if="goodsList && goodsList.length > 0">
      <goods-row :goodsList="goodsList"></goods-row>
    </view>
  </view>
</template>

<script>
import goodsRow from "components/goods-row/index";
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'
export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      articleInfo: {},
      article_description: "",
      spuIds: [],
      goodsList: [],
	  //有数统计使用
	  page_title:'文章详情'
    }
  },
  components: {
    goodsRow
  },
  onLoad (options) {
    
    let id = options.id
    this.id = id
    app.initPage().then(res => {
      this.articleGet();
    });
  },

  methods: {
    articleGet () {
      api.articleGet(this.id).then(res => {
        let articleInfo = res.data;
        const articleInfoItemList = res.data.articleInfoItemList
        // 提取id并放入新数组
        const idArray = articleInfoItemList.map(item => item.spuId);
        // 将id数组以逗号分隔的形式转换为字符串
        this.spuIds = [idArray.join(',')];
        // this.spuIds = res.data.spuIds
        console.log("articleInfo", articleInfo);
        this.articleInfo = articleInfo;
		this.page_title='文章详情'+this.articleInfo.articleTitle 
        if (this.spuIds.length > 0) {
          this.articleGetGoods()
        }
        setTimeout(() => {
          this.article_description = articleInfo.articleContent;
        }, 300);
      });
    },
    //获取商品
    articleGetGoods () {
      let ids = this.spuIds.join(",")
      api.articleGetGoods(ids).then(res => {
        console.log("res.data", res.data);
        this.goodsList = res.data
      })
    }
  }
}
</script>

<style>
.img-box {
  width: 100%;
  height: 240rpx;
}

.article-image {
  width: 100%;
  height: 200rpx;
  margin: 0 auto;
}
</style>
