<template>
  <view>
      <view class="btn-box text-26 flex justify-between align-center">
        <view class="btn-item" @tap="goOrder">
          <image class="icon" src="https://img.songlei.com/live/jielong/order.png" />
          <view class="footer-label">订单</view>
        </view>
        <button class="btn-item"  style="line-height: inherit;background: white;" @click="showShareApp" open-type="share" >
          <image class="icon" src="https://img.songlei.com/live/jielong/share.png" />
          <view class="footer-label">分享</view>
        </button>
        <view class="btn-item" @tap="$emit('getShoppingCartList')">
          <view class="shopping-cart-count text-xs" v-if="shoppingCartCount">
            {{ shoppingCartCount }}
          </view>
          <image class="icon" src="https://img.songlei.com/live/jielong/shop-cart.png" />
          <view class="footer-label">购物车</view>
        </view>
      </view>
      <share-component v-model="showShare" :shareParams="shareParams" />
  </view>
</template>
<script>
import shareComponent from "@/components/share-component/index"

export default {
  components: { shareComponent },
  props: {
    shoppingCartCount: {
      type: Number,
      default: 0
    },
    shareData: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      shareParams: {},
      showShare: false
    }
  },
  methods: {
    goOrder() {
      uni.navigateTo({
        url: '/pages/order/order-list/index?orderType=6'
      })
    },
    shareFn() {
      const posterConfig = {
        width: 750,
        height: 1280,
        backgroundColor: '#fff',
        debug: false,
        images: [{
          width: 690,
          height: 690,
          x: 30,
          y: 10,
          url: this.shareData.imgUrl
        }, {
          width: 180,
          height: 180,
          x: 490,
          y: 840,
          url: null,
          qrCodeName: 'qrCodeName', // 二维码唯一区分标识
        }],
        texts: [{
          x: 290,
          y: 990,
          text: '',
          fontSize: 28,
          color: '#080808'
        }, {
          x: 30,
          y: 880,
          fontSize: 38,
          baseLine: 'middle',
          text: this.shareData.desc||'',
          width: 320,
          lineNum: 3,
          color: '#333333',
          zIndex: 200
        }, {
          x: 490,
          y: 1040,
          fontSize: 34,
          baseLine: 'top',
          text: '扫描/长按识别',
          width: 250,
          lineNum: 10,
          lineHeight: 70,
          color: '#666666',
          zIndex: 200
        }],
        lines: [{}],
        blocks: [{}],
      }
      // 如果有头像则显示
      const userInfo = uni.getStorageSync('user_info');
      if (userInfo && userInfo.headimgUrl) {
        posterConfig.images.push({
          width: 62,
          height: 62,
          x: 30,
          y: 760,
          borderRadius: 62,
          url: userInfo.headimgUrl,
          zIndex: 200
        });
        posterConfig.texts.push({
          x: 113,
          y: 790,
          baseLine: 'middle',
          text: userInfo.nickName, // 手机号 或者名称
          fontSize: 32,
          color: '#666666'
        });
      }
      this.shareParams = {
        title: this.shareData.title,
        desc: this.shareData.title,
        imgUrl: this.shareData.imgUrl,
        url: this.shareData.page,
        scene: this.shareData.scene,
        page: this.shareData.page,
        posterConfig: posterConfig
      }
      this.showShare = true;
    },
    showShareApp() {
      // #ifdef APP
      this.$emit('reciveShare')
      // #endif
    }
  },
}
</script>
<style lang="scss" scoped>
	.footer-label {
		font-size: 30rpx;
		color: #000;
	}
  .btn-box{
    flex: 1;
    height: 100%;
    .btn-item{
      width: 130rpx;
      height: 100%;
      text-align: center;
      color: #000;
      padding: 20rpx;
      .icon{
        width: 36rpx;
        height: 36rpx;
      }
      &:nth-child(3){
        position: relative;
      }
    }
    .shopping-cart-count{
      position: absolute;
      right: 28rpx;
      top: 4rpx;
      width: 34rpx;
      height: 34rpx;
      background: #FF4800;
      border-radius: 50%;
      z-index: 2;
      color: #fff;
      text-align: center;
      line-height: 34rpx;
    }
  }
</style>