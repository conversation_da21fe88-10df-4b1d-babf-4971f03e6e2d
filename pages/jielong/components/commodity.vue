<template>
  <view class="list-item flex align-start">
    <view style="position:relative">
      <image class="goods-img" :src="goods.picUrl" mode="aspectFit" />
      <view class="goods-img text-white"  v-if="goods.limitNum===0" style="position: absolute;opacity: 0.6;background-color: #000000;top: 0;line-height:252rpx;text-align: center;">已售罄</view>
    </view>
    <view class="goods-info" :style="goodsInfoStyle">
      <view class="text-30 text-black text-cut text-left">
        {{ goods.name }}
      </view>
      <view class="buying-point text-26 text-cut" v-if="goods.goodsSpu.sellPoint">
        {{ goods.goodsSpu.sellPoint }}
      </view>
      <view class="recommend flex justify-between" v-if="goods.isSuggest == 1 || goods.groupNum" >
        <text class="shopowner text-sm" v-show="goods.isSuggest == 1">店长推荐</text>
        <text class="text-28" v-if="goods.groupNum">已团{{ goods.groupNum }}</text>
      </view>
      <view class="text-28 margin-top-xs" v-if="goods.limitNum">
        库存 <text class="orange padding-lr-xs">{{ goods.limitNum }}</text> 件
      </view>
      <view class="price-purchase flex justify-between align-end">
        <view>
          <format-price
            styleProps="margin-bottom: -15rpx;"
            styleTextDecoration="line-through"
            signFontSize="20rpx" 
            smallFontSize="20rpx" 
            priceFontSize="28rpx"
            color="#A6A6A6"
            :price="goods.goodsSku.salesPrice"
          />
          <format-price
            styleProps="font-weight: 500;"
            signFontSize="24rpx" 
            smallFontSize="24rpx" 
            priceFontSize="44rpx"
            color="#FF4800"
            :price="goods.groupPrice"
          />
        </view>
        <view v-if="cartAsync" class="purchase-img">
          <base-stepper
            v-if="goods.shoppingCartQuantity && goods.shoppingCartQuantity > 0"
            customClass="width-180 padding-bottom-xs"
            reduceBtnStyle="width: 50rpx; height: 50rpx !important; background: #FFFFFF !important; border: 2px solid #73CD2B; border-radius: 14rpx;"
            addBtnStyle="width: 50rpx; height: 50rpx !important; background: #ACE42F !important; border: 2px solid #ACE42F; border-radius: 14rpx;"
            inputStyle="width: 80rpx; background: #fff;" :disabled="false"
            :stNum="goods.shoppingCartQuantity" :min="0" :max="goods.limitNum"
            @numChange="numChange"
          />
          <image v-else class="img" @tap.stop="$emit('addPurchase', goods, 1)" src="https://img.songlei.com/live/jielong/shopping-cart.png" />
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import baseStepper from "@/components/base-stepper/index";

export default {
  components: { baseStepper },
  props: {
    goods: {
      type: Object,
      default: {}
    },
    goodsInfoStyle: {
      type: String,
      default: ''
    },
    cartAsync: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    numChange(val) {
      this.$emit('numChange', val)
    }
  },
}
</script>

<style lang="scss" scoped>
.orange{
  color: #ff4d0a;
}
.width-180{
  width: 180rpx !important;
}
.list-item{
  width: 100%;
  padding: 30rpx 20rpx;
  box-shadow: 0rpx -12rpx 25rpx -15rpx #e2e2e2 inset;
  .goods-img{
    width: 252rpx;
    height: 252rpx;
    border: 1rpx solid #efefef;
    border-radius: 30rpx;
    margin-right: 20rpx;
  }
  .goods-info{
    width: 361rpx;
  }
  .buying-point{
    color: #757575;
  }
  .recommend{
    color: #ff4d0a;
    margin-top: 10rpx;
    height: 34rpx;
    line-height: 34rpx;
    .shopowner{
      padding: 2rpx 20rpx;
      color: #ff4901;
      background: #ffccb8;
      border-radius: 25rpx;
    }
  }
  .price-purchase{
    margin-top: 33rpx;
    .purchase-img{
      .img{
        width: 62rpx;
        height: 62rpx;
      }
    }
  }
}
</style>