<template>
  <view
    class="cu-modal bottom-modal"
    :class="dialogClass"
    @tap="$emit('closeDialog')"
    v-if="dialogClass === 'show'"
    catchtouchmove="touchMove"
  >
    <view 
      class="cu-dialog bg-white text-left"
      :class="dialogClass ? 'animation-slide-bottom' : ''" 
      style="height: 80%;"
      @tap.stop
    >
      <commodity
        :goods="goodsData"
        goodsInfoStyle="width: 440rpx;"
        :cartAsync="false"
        @addPurchase="(event) => { $emit('addPurchase', event) }"
      />
      <view class="border-bottom flex justify-between align-center padding-tb-sm margin-lr-sm">
        <text class="text-xdf text-black">数量(库存{{ goodsData.limitNum }})</text>
        <base-stepper
          customClass="width-150"
          reduceBtnStyle="width: 50rpx; height: 50rpx !important; border: 1px solid #999; border-right: 0; border-radius: 14rpx 0 0 14rpx;"
          addBtnStyle="width: 50rpx; height: 50rpx !important; border: 1px solid #999; border-left: 0; border-radius: 0 14rpx 14rpx 0;"
          inputStyle="width: 80rpx; background: #fff; border: 1px solid #999; border-radius: 0; "
          :disabled="false"
          :stNum="cartNum"
          :min="1"
          :max="goodsData.limitNum" 
          @numChange="numChange" 
        />
      </view>
      <!-- 拼团人员头像，人数展示 -->
      <view
        class="border-bottom flex justify-between align-center margin-lr-sm padding-tb-sm"
        v-if="goodsData.headimgUrls && goodsData.headimgUrls.length && goodsData.groupNum"
      >
        <view class="text-center text-black text-xdf" style="width: 140rpx;">
          <view class="orange-red">{{ goodsData.groupNum }}</view>
          <view>购买人数</view>
        </view>
        <view class="margin-right-sm" >
          <template v-for="(item, index) in goodsData.headimgUrls">
            <image
              class="round"
              style="width: 56rpx; height: 56rpx;"
              :style="goodsData.headimgUrls.length > 1 ? 'margin-left:-20rpx' : ''"
              :key="index"
              v-if="index < 13"
              :src="item"
              mode="aspectFill"
            />
          </template>
          <text v-if="goodsData.headimgUrls.length >= 13">...</text>
        </view>
      </view>
      <!-- 商品详情图 -->
      <scroll-view scroll-y scroll-with-animation style="height:69%; padding: 20rpx 0 88rpx 0;">
        <view class="bg-white padding-lr-sm" style="padding-bottom: 160rpx;">
					<jyf-parser v-if="goodsData.goodsSpu.description" :html="goodsData.goodsSpu.description" />
				</view>
      </scroll-view>
      <view class="footer flex justify-between align-center padding-right-sm">
        <footer-left
          :shoppingCartCount="shoppingCartCount" 
          @getShoppingCartList="() => { $emit('getShoppingCartList') }"
        />
        <view class="btns flex align-center">
          <view v-if="goodsData.limitNum>0" class="btns flex align-center">
            <button
              class="add-purchase text-white text-center text-sm"
              @tap="$emit('addPurchase', goodsData)"
            >加入购物车</button>
            <button
              class="go-pay text-white text-center text-sm"
              @tap="goOrderConfirmPage"
            >立即购买</button>
          </view>
          <view v-else >
            <button
              class="sell-out text-lg text-white text-center"
              style="position: absolute;left: 400rpx;top: 35rpx;"
            >已售罄</button>
          </view>
        </view>
      </view>
      
    </view>
  </view>
</template>
<script>
import commodity from './commodity.vue';
import footerLeft from "./footer-left.vue";
import baseStepper from "@/components/base-stepper/index";

export default {
  components: { commodity, footerLeft, baseStepper },
  props: {
    dialogClass: {
      type: String,
      default: ''
    },
    goodsData: {
      type: Object,
      default: {}
    },
    shoppingCartCount: {
      type: Number,
      default: 0
    },
    marketHallId: {
      type: String,
      default: ''
    }
  },
  watch: {
    dialogClass(val) {
      if (val) {
        this.cartNum = 1;
      }
    }
  },
  data() {
    return {
      cartNum: 1
    }
  },
  methods: {
    numChange(val) {
      this.cartNum = val;
      this.$emit('numChange', this.cartNum) 
    },
    goOrderConfirmPage() {
      const { id, spuId, goodsSpu, goodsSku, quantity  } = this.goodsData;
      uni.setStorage({
        key: 'param-orderConfirm',
        data: {
          source: 6,
          type: 1, // 团购--立即购买
          groupPurchaseIds: [id],
          goodsIds: [spuId],
          groupPurchaseHallId: this.marketHallId,
          shoppingCarItemIds: {
            spuId: goodsSpu.id,
            skuId: goodsSku.id,
            quantity: quantity || this.cartNum,
            specInfo: goodsSku.specInfo,
          }
        }
      });
      uni.navigateTo({
        url: `/pages/order/order-confirm/index?type=2`
      })
    }
  },
}
</script>
<style lang="scss" scoped>
.orange-red{
  color: #FF4B00;
}
.border-bottom{
  border-bottom: 1rpx solid #E5E5E5;
}
.footer{
  width: 100%;
  height: 185rpx;
  padding-bottom: 40rpx;
  background: #FFFFFF;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;
  // .go-pay{
  //   width: 265rpx;
  //   height: 100%;
  //   background: linear-gradient(90deg, #FF7500 0%, #FF4B00 100%);
  // }
  button::after{ border: none;}
  .btns{
    width: 360rpx;
    height: 70rpx;
    .add-purchase{
      // flex: 1;
      background: linear-gradient(90deg, #FFC305 0%, #FF9700 100%);
      border-radius: 35rpx 0rpx 0rpx 35rpx;
      line-height: 70rpx;
    }
    .go-pay{
      flex: 1;
      background: linear-gradient(90deg, #FF7500 0%, #FF4B00 100%);
      border-radius: 0rpx 35rpx 35rpx 0rpx;
      line-height: 70rpx;
    }

    .sell-out{
      width: 222rpx;
      height: 81rpx;
      background: #CCCCCC;
      border-radius: 41rpx;
      line-height: 81rpx;
    }
  }
}
</style>