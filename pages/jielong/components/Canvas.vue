<template>
  <view style="position: fixed;left:100%;z-index: -9999;opacity: 0;">
    <canvas
      :canvas-id="canvasID"
      :style="{ width: canvasW + 'px', height: canvasH + 'px' }"
    ></canvas>
  </view>
</template>

<script>
var _this;
export default {
  name: "printable-coupon",
  props: {
    //canvasID 等同于 canvas-id
    canvasID: {
      Type: String,
      default: 'posterCanvas'
    },  
    // 接收shan 
    purchaseData: {
      type: Object,
      default: true
    },
  },
  data () {
    return {
      canvasW: 0,
      canvasH: 0,
      ctx: null,
      width: 700,
	  pic: "" ,//使用canvas
    };
  },
  methods: {
  // 使用画布绘制图片
  async onCanvas(purchaseData) {
    let  _this = this
    uni.showLoading({
      title: "努力加载中.."
    })
    const ctx = uni.createCanvasContext(_this.canvasID, _this);
    _this.canvasW = uni.upx2px(_this.width)
    _this.canvasH = uni.upx2px(_this.width * 0.8)
    // _this.canvasH = uni.upx2px(_this.width / 2)
    ctx.setFillStyle('#FFFFFF'); //canvas背景颜色
    ctx.fillRect(0, 0, _this.canvasW, _this.canvasH); //canvas画布大小
    ctx.scale(_this.canvasW / 350, _this.canvasW / 350)
      // https://slshop-file.oss-cn-beijing.aliyuncs.com/vipmine/ljcy.png
    let spuImg1 = await _this.getImageInfo(purchaseData.groupPurchaseInfos[0].picUrl)
    let spuImg2 = await _this.getImageInfo(purchaseData.groupPurchaseInfos[1].picUrl)
    let spuImg3 = await _this.getImageInfo(purchaseData.groupPurchaseInfos[2].picUrl)
    let spuImg4 = await _this.getImageInfo(purchaseData.groupPurchaseInfos[3].picUrl)
    let spuImg5 = await _this.getImageInfo(purchaseData.groupPurchaseInfos[4].picUrl)
    let spuImg6 = await _this.getImageInfo(purchaseData.groupPurchaseInfos[5].picUrl)
    let spuImg7 = await _this.getImageInfo('https://slshop-file.oss-cn-beijing.aliyuncs.com/vipmine/ljcy.png')
	   //   绘制图片															 
    ctx.drawImage(spuImg1, 0, 0, 110, 100)
    ctx.drawImage(spuImg2, 117, 0, 110, 100)
    ctx.drawImage(spuImg3, 235, 0, 110, 100) 
    ctx.drawImage(spuImg4, 0,112, 110, 100) 
    ctx.drawImage(spuImg5, 117,112, 110, 100) 
    ctx.drawImage(spuImg6, 235,112, 110, 100) 
    ctx.drawImage(spuImg7, 0,220, 342, 60) 
    _this.pic = await _this.setTime(ctx)
    _this.$emit('getCanvaImage',_this.pic)
    console.log(this.pic, 'pic------')
  },
  
  //彻底改成同步 防止拿到的图片地址为空
  setTime(ctx) {
  	let _this = this;
  	return new Promise((resole, err) => {
  		setTimeout(() => {
  			ctx.draw(false, async () => {
  				let pic = await _this.getNewPic();
  				resole(pic)
  			});
  		}, 600)
  	})
  },
  
  getNewPic() {
  	let _this = this;
  	return new Promise((resolve, errs) => {
  		setTimeout(() => {
  			uni.canvasToTempFilePath({
  				canvasId: _this.canvasID,
  				quality: 1,
  				complete: (res) => {
  					// 在H5平台下，tempFilePath 为 base64
  					// 关闭showLoading
  					uni.hideLoading();
  					//  储存海报地址  也是分享的地址
  					resolve(res.tempFilePath)
  				}
  			}, _this);
  		}, 200)
  	})
  },
  //获取图片的临时地址
  getImageInfo(imgSrc) {
  	return new Promise((resolve, errs) => {
  		uni.getImageInfo({
  			src: imgSrc,
  			success: (image) => {
  				resolve(image.path);
  			},
  			fail: (err) => {
  				console.error('getImageInfo:', err)
  			}
  		});
  	});
  },
  
    
     },
  mounted () {

  },
}
</script>

<style lang="scss">
</style>
