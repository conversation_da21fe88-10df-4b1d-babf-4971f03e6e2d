<template>
  <view style="background: #a5d232">
    <cu-custom
      :isBack="true"
      :iconColor="'#000000'"
      :boderColor="'#cccccc'"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content" style="color: #000000">松鼠接龙</block>
    </cu-custom>
    <view class="banner">
      <image
        :src="
          purchaseData.picUrl ||
          'https://img.songlei.com/live/jielong/banner.png'
        "
      />
      <view
        v-if="purchaseData.description"
        class="rule-details text-black text-center text-df"
        @tap="relueDetailClass = 'show'"
        >规则明细</view
      >
    </view>

    <view class="content-box">
      <view class="fence">
        <image src="https://img.songlei.com/live/jielong/top.png" />
      </view>
      <view class="content">
        <view class="title text-lg">
          {{ purchaseData.hallName || "***" }}
        </view>
        <view class="info text-gray text-sm">
          <view class="flex justify-between">
            <text v-if="purchaseData.beginTime">
              {{ purchaseData.beginTime | releaseTime }}天{{
                isTime ? "" : purchaseData.status == 0 ? "后" : "前"
              }}发布
            </text>
            <view class="orange" v-if="purchaseData">
              <view v-if="purchaseData.status == 1 && purchaseData.endTime">
                <count-down
                  :outTime="
                    new Date(purchaseData.endTime).getTime() -
                    new Date().getTime()
                  "
                  fontSize="24rpx"
                  textColor="#FF4800"
                  connectorColor="#FF4800"
                  backgroundColor="#fff"
                  :showType="2"
                />后结束
              </view>
              <view v-else-if="purchaseData.status == 0">活动未开始</view>
              <view v-else-if="purchaseData.status == 2">活动已结束</view>
            </view>
          </view>
          <view v-if="purchaseData.view && purchaseData.joinHallNum"
            >{{ purchaseData.view }}人看过 |
            {{ purchaseData.joinHallNum }}人参与</view
          >
        </view>
        <swiper
          v-if="
            purchaseData &&
            purchaseData.groupPurchaseInfos &&
            purchaseData.groupPurchaseInfos.length
          "
          :indicator-dots="false"
          :autoplay="true"
          :circular="false"
          :interval="3500"
          style="height: 150rpx"
        >
          <!-- 
							@click="handleClicks(items.pageUrl)" -->
          <swiper-item
            v-for="(item, index) in purchaseData.groupPurchaseInfos"
            :key="index"
          >
            <view
              class="purchase-goods flex align-center"
              @tap="openGoodsDetail(item)"
            >
              <view style="position: relative">
                <image class="goods-img" :src="item.picUrl" mode="aspectFit" />
                <view
                  class="goods-img align-center text-white"
                  v-if="item.limitNum === 0"
                  style="
                    position: absolute;
                    opacity: 0.6;
                    background-color: #000000;
                    top: 0;
                    line-height: 100rpx;
                  "
                >
                  已售罄</view
                >
              </view>
              <view class="margin-left-sm">
                <view class="text-black">{{ item.name }}</view>
                <view class="flex align-center">
                  <format-price
                    class="margin-right-sm"
                    styleTextDecoration="line-through"
                    signFontSize="20rpx"
                    smallFontSize="20rpx"
                    priceFontSize="28rpx"
                    color="#999"
                    :price="item.goodsSku.salesPrice"
                  />
                  <format-price
                    styleProps="font-weight: 500;"
                    signFontSize="24rpx"
                    smallFontSize="24rpx"
                    priceFontSize="44rpx"
                    color="#FF4800"
                    :price="item.groupPrice"
                  />
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>

        <view class="time-address text-black" v-if="purchaseData">
          <view class="item">
            <view class="text-28">接龙时间</view>
            <view class="text-26">
              {{ purchaseData.beginTime || "00" }} 至
              {{ purchaseData.endTime || "00" }}
            </view>
          </view>
          <view class="item">
            <view class="text-28">提货时间</view>
            <view class="text-26">{{ purchaseData.pickupTime }}</view>
          </view>
          <view class="item">
            <view class="text-28">提货地址</view>
            <view class="text-26">{{ purchaseData.deliveryPlace }}</view>
          </view>
        </view>
        <view class="orange text-sm text-center margin-tb-sm">
          【成功后请在提货时间内到提货地点提货，过期自动取消】
        </view>
        <view class="flow-path">
          <image src="https://img.songlei.com/live/jielong/flow-path.png" />
          <view class="semi-circle"></view>
        </view>
      </view>
      <view class="content content-good">
        <view class="goods-list">
          <view
            v-for="(item, index) in purchaseData.groupPurchaseInfos"
            :key="index"
            @tap="openGoodsDetail(item)"
          >
            <commodity
              :goods="item"
              @addPurchase="
                (goods, num) => {
                  addPurchase(goods, num, index);
                }
              "
              @numChange="
                (num) => {
                  goodsNumChange(num, index);
                }
              "
            />
          </view>
        </view>
      </view>

      <view class="fence bottom">
        <image src="https://img.songlei.com/live/jielong/bottom.png" />
      </view>

      <!-- 跟团记录 -->
      <view class="followGroupRecords" v-if="recordsList && recordsList.length">
        <view class="followGroup_title"> 跟团记录 </view>
        <view class="follow_content">
          <view
            class="follow_list"
            v-for="(item, index) in recordsList"
            :key="index"
          >
            <view class="left_index">
              <view class="numer_index">
                {{ index + 1 }}
              </view>
              <view class="Header_image">
                <image class="image" :src="item.imgUrl" mode=""></image>
              </view>
              <view class="right_cell">
                <view style="display: flex; align-items: center">
                  <view class="nickName_user">
                    {{ item.nickName }}
                  </view>
                  <view style="" class="shop_Time">
                    {{ getDaysAgo(item.createTime) }}
                  </view>
                </view>
                <view class="shopName">
                  {{ item.orderItems[0].spuName }}
                  {{ item.orderItems[0].quantity }}袋 *
                  {{ item.orderItems[0].quantity }}
                </view>
              </view>
            </view>
          </view>
          <view
            :class="'cu-load bg-gray ' + (false ? 'loading' : 'over')"
            style="background: white"
          ></view>
        </view>
      </view>
      <view class="footer flex justify-between">
        <footer-left
          style="flex: 1; padding: 0 30rpx"
          :shoppingCartCount="shoppingCartCount"
          @getShoppingCartList="getShoppingCartList"
          @reciveShare="reciveShare"
        />
      </view>
    </view>

    <!-- 商品详情弹框 -->
    <commodity-detail
      :dialogClass="goodsDetailAsync"
      :goodsData="goodsData"
      :shoppingCartCount="shoppingCartCount"
      :marketHallId="marketHallId"
      @getShoppingCartList="getShoppingCartList"
      @closeDialog="goodsDetailAsync = ''"
      @numChange="goodsDetailNumChange($event)"
      @addPurchase="addPurchase"
    />

    <!-- 购物车弹框 -->
    <view
      class="cu-modal bottom-modal"
      :class="shoppingCart"
      @tap="shoppingCartClose()"
      catchtouchmove="touchMove"
    >
      <view
        class="shopping-cart-box cu-dialog bg-white text-left"
        :class="shoppingCart ? 'animation-slide-bottom' : ''"
        style="height: 70%"
        @tap.stop
      >
        <view
          class="title text-black text-sm flex justify-between align-center"
        >
          <view>
            <text class="text-lg text-bold">购物车</text>
            <text>（共{{ shoppingCartCount }}件商品）</text>
          </view>
          <view @tap="shoppingCartDel()">
            <text class="cuIcon-delete text-xdf margin-right-xs"></text>
            删除
          </view>
        </view>
        <view class="commodity-box">
          <checkbox-group>
            <view
              class="commodity-list"
              v-for="(item, index) in shoppingCartData"
              :key="index"
            >
              <view
                class="flex align-center"
                v-for="(goodsItem, goodsIndex) in item.shoppingCartData"
                :key="goodsIndex"
              >
                <checkbox
                  class="round scale"
                  :value="goodsItem.id"
                  :disabled="
                    goodsItem.quantity == 0 ||
                    goodsItem.quantity > goodsItem.surplusNum
                  "
                  :checked="goodsItem.checked"
                  @tap="checkboxChange(goodsItem)"
                />
                <image
                  class="commodity-img"
                  :src="goodsItem.picUrl"
                  mode="aspectFit"
                />
                <view class="goods-info">
                  <view class="text-30 text-black text-cut">
                    {{ goodsItem.goodsSpu.name }}
                  </view>
                  <view class="recommend text-sm">
                    {{ goodsItem.surplusNum > 3 ? "库存" : "仅剩" }}
                    {{ goodsItem.surplusNum }}件
                  </view>
                  <view class="price-purchase flex justify-between align-end">
                    <view>
                      <format-price
                        styleProps="margin-bottom: -15rpx;"
                        styleTextDecoration="line-through"
                        signFontSize="20rpx"
                        smallFontSize="20rpx"
                        priceFontSize="28rpx"
                        color="#A6A6A6"
                        :price="goodsItem.goodsSku.salesPrice"
                      />
                      <format-price
                        styleProps="font-weight: 500;"
                        signFontSize="24rpx"
                        smallFontSize="24rpx"
                        priceFontSize="44rpx"
                        color="#FF4800"
                        :price="
                          goodsItem.goodsSku.estimatedPriceVo.estimatedPrice
                        "
                      />
                    </view>
                    <view class="padding-bottom-xs">
                      <base-stepper
                        customClass="width-180"
                        reduceBtnStyle="width: 50rpx; height: 50rpx !important; background: #FFFFFF !important; border: 2px solid #73CD2B; border-radius: 14rpx;"
                        addBtnStyle="width: 50rpx; height: 50rpx !important; background: #ACE42F !important; border: 2px solid #ACE42F; border-radius: 14rpx;"
                        inputStyle="width: 80rpx; background: #fff;"
                        :disabled="false"
                        :stNum="goodsItem.quantity"
                        :min="0"
                        :max="goodsItem.surplusNum"
                        @numChange="
                          (val) => {
                            cartNumChange(val, index, goodsIndex);
                          }
                        "
                      />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </checkbox-group>
          <!-- 购物车没商品 -->
          <view
            class="text-center margin-top-xl"
            style="display: flex; flex-direction: column"
            v-if="!shoppingCartData.length"
          >
            <view> 购物车空空如也，赶快行动吧~~ </view>
            <view
              style="
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <view class="margin-top">
                <image
                  style="width: 210rpx; height: 208rpx"
                  src="https://img.songlei.com/live/shopping-cart.png"
                >
                </image>
              </view>
            </view>
          </view>
        </view>
        <view class="footer flex justify-between align-center padding-lr-sm">
          <view class="flex align-center padding-top-sm">
            <checkbox-group @change="checkboxAllGoods">
              <checkbox
                class="round scale"
                :class="isAllSelect ? theme.themeColor + ' checked' : ''"
                value="all"
                :checked="isAllSelect"
              />
            </checkbox-group>
            <view class="text-lg text-bold margin-left-sm">全选</view>
          </view>
          <view
            class="price text-black flex align-end"
            v-if="
              totalEstimatedPriceVo !== 0 &&
              shoppingCartCount &&
              selectGoods.length > 0
            "
          >
            <text style="padding-bottom: 12rpx">合计:</text>
            <format-price
              styleProps="font-weight:bold"
              signFontSize="30rpx"
              smallFontSize="30rpx"
              priceFontSize="55rpx"
              color="#FF0000"
              :price="totalEstimatedPriceVo"
            />
          </view>
          <view>
            <button
              :disabled="isDisabled"
              class="go-pay text-lg text-white text-center"
              @tap="goOrderConfirmPage"
            >
              去支付<text v-if="selectGoods.length"
                >({{ selectGoods.length }})</text
              >
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 规则明细 -->
    <view
      class="cu-modal"
      :class="relueDetailClass"
      @tap="relueDetailClass = ''"
      catchtouchmove="touchMove"
    >
      <view
        class="cu-dialog bg-white text-left padding-lg"
        style="height: 50%"
        @tap.stop
      >
        <view class="text-black text-lg text-center margin-bottom-lg"
          >活动规则</view
        >
        <scroll-view scroll-y scroll-with-animation style="height: 73%">
          <jyf-parser
            v-if="purchaseData.description"
            :html="purchaseData.description"
          />
        </scroll-view>
        <view class="i-know margin" @tap="relueDetailClass = ''">我知道了</view>
      </view>
    </view>
    <!-- 加入购物车动画 cartx 和 carty 是购物车位置在屏幕位置的比例 例如左上角x0.1 y0.1 右下角 x0.9 y0.9-->
    <!-- <shopCarAnimation
      ref="carAnmation"
      cartx="0.65"
      carty="1.7"
    ></shopCarAnimation> -->
    <!-- 活动未开始、已结束弹框 -->
    <view
      v-if="purchaseData"
      class="cu-modal"
      :class="
        purchaseData.status == 0 || purchaseData.status == 2 ? 'show' : ''
      "
      @tap.stop
      catchtouchmove="touchMove"
    >
      <view class="cu-dialog bg-white padding-lg" style="height: 15%" @tap.stop>
        <view class="text-lg text-black text-bloder margin-top-sm">
          活动
          <text class="orange" v-if="purchaseData.status == 2">已结束</text>
          <text class="orange" v-if="purchaseData.status == 0">未开始</text>
          ，去看看别的商品吧
        </view>
        <view v-if="purchaseData.status == 0">
          距离活动开始还有
          <count-down
            v-if="purchaseData.beginTime"
            :outTime="
              new Date(purchaseData.beginTime).getTime() - new Date().getTime()
            "
            fontSize="24rpx"
            textColor="#FF4800"
            connectorColor="#FF4800"
            backgroundColor="#fff"
            @countDownDone="activityCountDownDone"
          />
        </view>
        <navigator
          open-type="switchTab"
          hover-class="none"
          url="/pages/home/<USER>"
          class="text-lg orange margin-top-sm"
          >去逛逛</navigator
        >
      </view>
    </view>
    <canvasImage
      ref="canvasImage"
      :purchaseData="purchaseData"
      @getCanvaImage="getCanvaImage"
    ></canvasImage>
  </view>
</template>
<script>
const app = getApp();
import formatPrice from "@/components/format-price/index.vue";
import countDown from "@/components/count-down/index";
import baseStepper from "@/components/base-stepper/index";
import footerLeft from "./components/footer-left.vue";
import commodity from "./components/commodity.vue";
import commodityDetail from "./components/commodity-detail.vue";
import canvasImage from "./components/Canvas.vue";

import api from "utils/api";
import util from "utils/util";
import __config from "config/env";

import { getCurrentTitle } from "@/public/js_sdk/sensors/utils.js";
import { senTrack } from "@/public/js_sdk/sensors/utils.js";

export default {
  components: {
    formatPrice,
    countDown,
    baseStepper,
    footerLeft,
    commodity,
    commodityDetail,
    canvasImage,
  },
  data() {
    return {
      id: "", // 团购会场id
      theme: app.globalData.theme, //全局颜色变量
      purchaseData: {}, // 团购会场数据
      marketHallId: "", // 会场id
      shoppingCartCount: 0, // 购物车数量
      shoppingCart: "", // 购物车弹框
      shoppingCartLoading: false, // 购物车loading
      shoppingCartData: [], // 购物车数据
      goodsDetailAsync: "", // 商品详情弹框
      goodsData: {}, // 商品详情数据
      selectGoods: [], // 选中的商品
      isAllSelect: true, // 是否全选
      cartEstimateLoading: false, // 购物车预估价格loading
      totalEstimatedPriceVo: 0, // 购物车预估价格
      relueDetailClass: "",
      firstSetAllSelect: true, // 刚刚进入页面默认全选
      recordsList: "", // 显示跟团  记录
      pic: "", //使用canvas
      enterTime: "",
    };
  },
  onShow() {
    this.enterTime = new Date().getTime();
    app.initPage().then((res) => {
      // 第一次 onLoad完之后 onShow，这时候groupPurchaseHallList这个接口改没执行完this.marketHallId还没有值
      if (this.marketHallId) {
        this.shoppingCartClose();
        this.getShoppingCartNum();
        //提交完订单返回到该页面要刷新购物车，但并不打开购物车弹框
        this.getShoppingCartList(true);
      }
    });
  },
  // 使用分心  分享朋友
  async onShareAppMessage(res) {
    senTrack("ActivityPageShareOrCollect", {
      page_name: getCurrentTitle(0),
      page_level: "一级",
      activity_id: this.purchaseData.id,
      activity_name: this.purchaseData.hallName,
      activity_type_first: "营销活动",
      activity_type_second: "拼团购",
      activity_type: "分享",
    });

    if (this.pic) {
      return {
        title: this.purchaseData.shareTitle,
        imageUrl: this.pic,
        path: "/pages/jielong/index?id=" + this.marketHallId,
      };
    } else {
      await this.$refs.canvasImage.onCanvas(this.purchaseData);
      return {
        title: this.purchaseData.shareTitle,
        imageUrl: this.pic,
        path: "/pages/jielong/index?id=" + this.marketHallId,
      };
    }
  },

  onLoad(options) {
    if (options.id) {
      this.id = options.id;
    } else if (options.scene) {
      const scene = decodeURIComponent(options.scene);
      if (scene) {
        //接受二维码中参数  参数sf=XXX&id=XXX
        const id = util.UrlParamHash(scene, "id");
        if (id) {
          this.id = id;
        }
      }
    }
    app.initPage().then(async (res) => {
      await this.groupPurchaseHallList();
      await this.getShoppingCartNum();
      // this.onCanvas()
    });
    //监听购物车选中的商品已经下单成功,这时候应该清空selectGoods
    uni.$on("creatJielongOrder", () => {
      this.selectGoods = [];
      this.isAllSelect = false;
    });
  },
  onUnload() {
    uni.$off("creatOrder");
    const leaveTime = new Date().getTime();
    const stayDuration = Math.floor((leaveTime - this.enterTime) / 1000);

    senTrack("ActivityPageLeave", {
      page_name: getCurrentTitle(0),
      forward_source: getCurrentTitle(0),
      page_level: "一级",
      activity_id: this.purchaseData.id,
      activity_name: this.purchaseData.hallName,
      activity_type_first: "营销活动",
      activity_type_second: "拼团购",
      stay_duration: stayDuration,
    });
  },

  computed: {
    isDisabled() {
      if (this.selectGoods && this.selectGoods.length > 0) {
        return false;
      } else {
        return true;
      }
    },
    isTime() {
      const nowData = new Date().getTime();
      const data = new Date(this.purchaseData.beginTime).getTime();
      const newData = Math.abs(
        Math.ceil((nowData - data) / 1000 / 60 / 60 / 24) - 1,
      );
      if (newData === 0) {
        return true;
      }
      return false;
    },
  },
  filters: {
    releaseTime: function (val) {
      if (val) {
        const nowData = new Date().getTime();
        const data = new Date(val).getTime();
        const newData = Math.abs(
          Math.ceil((nowData - data) / 1000 / 60 / 60 / 24) - 1,
        );
        if (newData === 0) {
          return "今";
        }
        return newData;
      }
      return "0";
    },
  },
  methods: {
    // 获取绘制图片
    getCanvaImage(pic) {
      this.pic = pic;
      console.log(pic, "拿到了");
    },

    // 定义一个函数，计算给定日期距今天数
    getDaysAgo(data) {
      //将字符串转换成时间格式
      var result = "";
      var timePublish = new Date(data);
      var timeNow = new Date();
      var minute = 1000 * 60;
      var hour = minute * 60;
      var day = hour * 24;
      var month = day * 30;
      var diffValue = timeNow - timePublish;
      var diffMonth = diffValue / month;
      var diffWeek = diffValue / (7 * day);
      var diffDay = diffValue / day;
      var diffHour = diffValue / hour;
      var diffMinute = diffValue / minute;

      if (diffValue < 0) {
        alert("错误时间");
      } else if (diffMonth > 3) {
        result = timePublish.getFullYear() + "-";
        result += timePublish.getMonth() + "-";
        result += timePublish.getDate();
        // alert(result);
      } else if (diffMonth > 1) {
        result = parseInt(diffMonth) + "月前";
      } else if (diffWeek > 1) {
        result = parseInt(diffWeek) + "周前";
      } else if (diffDay > 1) {
        result = parseInt(diffDay) + "天前";
      } else if (diffHour > 1) {
        result = parseInt(diffHour) + "小时前";
      } else if (diffMinute > 1) {
        result = parseInt(diffMinute) + "分钟前";
      } else {
        result = "刚下单";
      }
      return result;
    },

    //关闭购物车弹框
    shoppingCartClose() {
      this.shoppingCart = "";
    },
    // 获取跟团记录
    // 获取跟团记录
    groupRecords(id) {
      api.groupRecords({ id: id }).then((res) => {
        console.log(res.data);
        if (res.data && res.data.length) {
          this.recordsList = res.data;
        }
      });
    },
    // 获取团购会场数据
    async groupPurchaseHallList() {
      try {
        const params = {};
        if (this.id > 0) {
          params.id = this.id;
        }
        const res = await api.groupPurchaseHallList(params);

        this.purchaseData = res.data;
        this.marketHallId = res.data?.id;
        this.groupRecords(res.data.id);
        if (this.purchaseData === null) {
          uni.showModal({
            title: "提示",
            content: "暂无团购活动！",
            success: function (res) {
              // if (res.confirm) {
              // 	console.log('用户点击确定');
              // } else if (res.cancel) {
              // 	console.log('用户点击取消');
              // }
              uni.switchTab({
                url: `/pages/home/<USER>
              });
            },
          });
          return;
        }
        senTrack("ActivityPageView", {
          page_name: getCurrentTitle(0),
          forward_source: getCurrentTitle(0),
          page_level: "一级",
          activity_id: this.purchaseData.id,
          activity_name: this.purchaseData.hallName,
          activity_type_first: "营销活动",
          activity_type_second: "拼团购",
        });

        // status 0未开始 1 是进行中 2 是已结束 null 是暂无团购活动
        if (this.purchaseData && this.purchaseData.status == "2") {
          setTimeout(() => {
            uni.switchTab({
              url: `/pages/home/<USER>
            });
          }, 5000);
        }
        this.share.imageUrl = this.purchaseData.sharePic;
        this.share.title = this.purchaseData.shareTitle;
      } catch (error) {
        console.log(error);
      }
    },
    // 获取购物车数量
    async getShoppingCartNum() {
      try {
        const params = {
          type: 2, // 1-正常商品 2-团购商品 3-礼品卡
          marketHallId: this.marketHallId,
        };
        const res = await api.shoppingCartCount(params);
        this.shoppingCartCount = res.data;
      } catch (error) {
        console.log(error);
      }
    },
    // 加入购物车
    async addPurchase(goods, num, goodsIndex) {
      try {
        const params = {
          type: 2, // 1-正常商品 2-团购商品 3-礼品卡
          marketHallId: this.marketHallId,
          addPrice: goods.groupPrice,
          picUrl: goods.picUrl,
          quantity: num || goods.quantity || 1,
          shopId: goods.shopId, // 店铺Id
          skuId: goods.skuId,
          specInfo: goods.goodsSku.specInfo,
          spuId: goods.spuId,
          spuName: goods.goodsSpu.name,
        };

        const res = await api.shoppingCartAdd(params);
        if (res.data) {
          this.checkboxChange(res.data, 2);
        }
        this.getShoppingCartNum();
        // uni.showToast({
        // 	title: '加入购物车成功',
        // 	icon: 'success'
        // })
        const goodsFindIndex = this.purchaseData.groupPurchaseInfos.findIndex(
          (item) => item.id == goods.id,
        );
        if (goodsFindIndex > -1) {
          this.$set(
            this.purchaseData.groupPurchaseInfos[goodsFindIndex],
            "shoppingCartQuantity",
            res.data.quantity,
          );
          this.$set(
            this.purchaseData.groupPurchaseInfos[goodsFindIndex],
            "shoppingCartId",
            res.data.id,
          );
        }
      } catch (error) {
        console.log(error);
      }
    },

    async getShoppingCartList(isOpenCart) {
      try {
        if (this.shoppingCartLoading) return;
        this.shoppingCartLoading = true;
        const params = {
          current: 1,
          size: 50,
          ascs: "",
          descs: "create_time", // 升序字段
          type: 2, // 1-正常商品 2-团购商品 3-礼品卡
          marketHallId: this.marketHallId,
        };
        const res = await api.shoppingCartPage(params);
        this.shoppingCartData = res.data.effectiveData || [];
        this.shoppingCart = isOpenCart ? "" : "show";
        this.goodsDetailAsync = "";
        //刚打开购物车默认全选
        if (this.firstSetAllSelect) {
          this.checkboxAllGoods(1);
          this.firstSetAllSelect = false;
        } else if (this.selectGoods && this.selectGoods.length > 0) {
          //再次打开购物车弹框的，保持原来的选择不变
          const selectGoodsIds = this.selectGoods.map((item) => item.id);
          this.shoppingCartData &&
            this.shoppingCartData.forEach((shopItem) => {
              shopItem.shoppingCartData &&
                shopItem.shoppingCartData.forEach((goodsItem) => {
                  if (selectGoodsIds.indexOf(goodsItem.id) > -1) {
                    goodsItem.checked = true;
                  }
                });
            });
          this.checkIsAllshopCartChecked();
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.shoppingCartLoading = false;
      }
    },
    // 列表页面数量改变
    async goodsNumChange(val, index) {
      try {
        this.$set(
          this.purchaseData.groupPurchaseInfos[index],
          "shoppingCartQuantity",
          val,
        );
        if (val == 0) {
          const params = {
            ids: [this.purchaseData.groupPurchaseInfos[index].shoppingCartId], //需要传数组
          };
          await api.shoppingCartDel(params);
          await this.getShoppingCartNum();
          return;
        }
        const params = {
          id: this.purchaseData.groupPurchaseInfos[index].shoppingCartId,
          quantity: val,
        };
        await api.shoppingCartUpdate(params);
      } catch (e) {
        console.log(e);
      }
    },
    // 购物车数量改变
    async cartNumChange(val, index, goodsIndex) {
      const goodsSkuId =
        this.shoppingCartData[index].shoppingCartData[goodsIndex].skuId;
      const goodsFindIndex = this.purchaseData.groupPurchaseInfos.findIndex(
        (item) => item.skuId == goodsSkuId,
      );
      if (val == 0) {
        try {
          const params = {
            ids: [this.shoppingCartData[index].shoppingCartData[goodsIndex].id], //需要传数组
          };
          await api.shoppingCartDel(params);
          this.checkboxChange(
            this.shoppingCartData[index].shoppingCartData[goodsIndex],
            1,
          );
          await this.getShoppingCartNum();
          this.getShoppingCartList();
          if (goodsFindIndex > -1) {
            this.$set(
              this.purchaseData.groupPurchaseInfos[goodsFindIndex],
              "shoppingCartQuantity",
              val,
            );
          }
        } catch (error) {
          console.log(error);
        }
        return;
      }
      this.$set(
        this.shoppingCartData[index].shoppingCartData[goodsIndex],
        "quantity",
        val,
      );
      try {
        const params = {
          id: this.shoppingCartData[index].shoppingCartData[goodsIndex].id,
          quantity: val,
        };
        await api.shoppingCartUpdate(params);
        !this.selectGoods.length && (this.totalEstimatedPriceVo = 0);
        this.selectGoods.length && this.getCartEstimatePrice();
        if (goodsFindIndex > -1) {
          this.$set(
            this.purchaseData.groupPurchaseInfos[goodsFindIndex],
            "shoppingCartQuantity",
            val,
          );
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 打开商品详情弹框
    openGoodsDetail(goods) {
      this.goodsData = goods;
      this.goodsDetailAsync = "show";
    },
    // 商品详情数量改变
    goodsDetailNumChange(val) {
      this.$set(this.goodsData, "quantity", val);
    },
    // 购物车删除商品
    async shoppingCartDel() {
      try {
        const goodsIds = this.selectGoods.map((item) => item.id);
        const goodsSkuIds = this.selectGoods.map((item) => item.skuId);
        const params = {
          ids: goodsIds,
        };
        await api.shoppingCartDel(params);
        //删除之后清空已选中的商品
        this.selectGoods = [];
        this.isAllSelect = false;
        await this.getShoppingCartNum();
        this.getShoppingCartList();
        this.purchaseData.groupPurchaseInfos.forEach((item) => {
          if (goodsSkuIds.indexOf(item.skuId) > -1) {
            this.$set(item, "shoppingCartQuantity", 0);
          }
        });
      } catch (error) {
        console.log(error);
      }
    },

    checkIsAllshopCartChecked() {
      let _leng = 0;
      if (this.shoppingCartData && this.shoppingCartData.length) {
        this.shoppingCartData.forEach((item) => {
          if (item.shoppingCartData && item.shoppingCartData.length) {
            _leng += item.shoppingCartData.length;
          }
        });
      }
      if (this.selectGoods.length == _leng) {
        this.isAllSelect = true;
      } else {
        this.isAllSelect = false;
      }
      !this.selectGoods.length && (this.totalEstimatedPriceVo = 0);
      this.selectGoods.length && this.getCartEstimatePrice();
    },

    // 选择购物车商品
    /**
     * @param {Object} goods 选中的商品
     * @param {Object} isForceUnchecked 1强制该商品取消选中，商品数量减少到0 删除该商品的时候传该参数为1
     * 2 强制选中改商品。添加该商品到购物车的话,就默认选中状态
     *
     */
    checkboxChange(goods, forceCheckedType) {
      if (forceCheckedType == 1) {
        //删除该商品，取消选中
        goods.checked = false;
      } else if (forceCheckedType === 2) {
        //该商品购物车，默认选中
        goods.checked = true;
      } else {
        goods.checked = !goods.checked;
      }
      const goodsIds = this.selectGoods.map((item) => item.id);
      let index = -1;
      if (goodsIds) {
        index = goodsIds.indexOf(goods.id);
      }
      if (goods.checked === true && index === -1) {
        this.selectGoods.push(goods);
      } else if (goods.checked === false && index > -1) {
        this.selectGoods.splice(index, 1);
      }
      this.checkIsAllshopCartChecked();
    },
    // 全选商品
    // selectTag  1 表示 全选  2 表示全不选  3  不传的话 现在的情况下取反
    checkboxAllGoods(selectTag) {
      if (selectTag == 1 || selectTag == 2) {
        this.isAllSelect = selectTag == 1 ? true : false;
      } else {
        this.isAllSelect = !this.isAllSelect;
      }
      if (this.shoppingCartData && this.shoppingCartData.length) {
        this.shoppingCartData.forEach((item) => {
          if (item.shoppingCartData && item.shoppingCartData.length) {
            item.shoppingCartData.forEach((goods) => {
              goods.checked = this.isAllSelect;
              const goodsIds = this.selectGoods.map((item) => item.id);
              let index = -1;
              if (goodsIds) {
                index = goodsIds.indexOf(goods.id);
              }
              if (goods.checked === true && index === -1) {
                this.selectGoods.push(goods);
              } else if (goods.checked === false && index > -1) {
                this.selectGoods.splice(index, 1);
              }
            });
          }
        });
      }

      this.checkIsAllshopCartChecked();
    },
    // 获取购物车预估价格--总价
    async getCartEstimatePrice() {
      if (this.cartEstimateLoading) return;
      try {
        this.cartEstimateLoading = true;
        const params = {
          type: 2, // 1-正常商品 2-团购商品 3-礼品卡
          ids: this.selectGoods.map((item) => item.id).join(","),
        };
        const res = await api.cartEstimatePrice(params);
        this.totalEstimatedPriceVo =
          res.data.totalEstimatedPriceVo.estimatedPrice;
      } catch (error) {
        console.log(error);
      } finally {
        this.cartEstimateLoading = false;
      }
    },
    // 购物车结算
    goOrderConfirmPage() {
      if (!this.selectGoods.length) {
        uni.showToast({
          title: "请选择商品",
          icon: "none",
        });
        return;
      }
      const shoppingCarItemIds = this.selectGoods.map((item) => item.id);
      const goodsIds = this.selectGoods.map((item) => item.spuId);
      uni.setStorage({
        key: "param-orderConfirm",
        data: {
          source: 6,
          type: 2, // 1-团购--购物车
          shoppingCarItemIds: shoppingCarItemIds,
          groupPurchaseIds: shoppingCarItemIds,
          goodsIds: goodsIds,
          groupPurchaseHallId: this.marketHallId,
        },
      });
      uni.navigateTo({
        url: `/pages/order/order-confirm/index?type=2`,
      });
    },
    // 活动未开始倒计时结束
    async activityCountDownDone() {
      await this.groupPurchaseHallList();
      await this.getShoppingCartNum();
    },
    async reciveShare() {
      const obj = {
        provider: "weixin",
        scene: "WXSceneSession",
        type: 5,
        imageUrl:
          "https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/<EMAIL>",
        title: this.purchaseData.shareTitle,
        miniProgram: {
          id: __config.originAppid,
          path: "/pages/jielong/index?id=" + this.marketHallId,
          type: 0,
          webUrl: "https://shopapi.songlei.com/",
        },
        fail(err) {
          console.log("错误信息", JSON.stringify(err));
          uni.showModal({
            title: "提示",
            content: "正在开发中",
            showCancel: false,
          });
        },
      };
      if (!this.pic) {
        await this.$refs.canvasImage.onCanvas(this.purchaseData);
      }
      obj.imageUrl = this.pic;
      uni.share(obj);
    },
  },
};
</script>
<style lang="scss">
button::after {
  border: none;
}

.width-180 {
  width: 180rpx !important;
}
</style>
<style lang="scss" scoped>
.orange {
  color: #ff4800;
}

.text-26 {
  font-size: 26rpx;
}

.text-28 {
  font-size: 28rpx;
}

.text-30 {
  font-size: 30rpx;
}

.banner {
  width: 100%;
  height: 300rpx;
  position: relative;

  image {
    width: 100%;
    height: 100%;
  }

  .rule-details {
    position: absolute;
    top: 20rpx;
    right: 0;
    width: 160rpx;
    height: 60rpx;
    padding-left: 20rpx;
    background: rgba(255, 255, 255, 0.5);
    // opacity: 0.5;
    border-radius: 30rpx 0 0 30rpx;
    line-height: 60rpx;
  }
}

.content-box {
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, #badb66 2rpx, transparent 2rpx),
    linear-gradient(to bottom, #badb66 2rpx, transparent 2rpx);
  background-repeat: repeat;
  /* 默认为 repeat */
  background-size: 40rpx 40rpx;
  position: relative;
  padding: 23rpx 0 197rpx;
  overflow: hidden;

  &::before {
    content: "";
    display: block;
    position: absolute;
    top: 2rpx;
    left: 20rpx;
    width: 450rpx;
    height: 400rpx;
    border-radius: 100%;
    background: #89bb0d;
    z-index: 1;
  }

  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 600rpx;
    left: -450rpx;
    width: 2350rpx;
    height: 2350rpx;
    border-radius: 100%;
    background: #89bb0d;
    z-index: 1;
  }

  .fence {
    height: 43rpx;
    width: 91%;
    margin: -3rpx 0 0 auto;
    position: relative;
    z-index: 2;

    &.bottom {
      height: 31rpx;
    }

    image {
      width: 100%;
      height: 100%;
    }
  }

  .content {
    width: 91%;
    height: 100%;
    background: #fff;
    margin: 0 0 0 auto;
    position: relative;
    z-index: 2;
    padding: 0 16rpx 24rpx;

    &.content-good {
      padding-left: 0;
      padding-right: 0;
    }

    .title {
      font-weight: 500;
    }

    .info {
      color: #a2a2a2;
    }

    .purchase-goods {
      width: 651rpx;
      height: 123rpx;
      background: #f4f4f4;
      border-radius: 25rpx;
      margin: 20rpx auto;
      padding: 0 13rpx;

      .goods-img {
        width: 100rpx;
        height: 100rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 10rpx;
      }
    }

    .time-address {
      .item {
        padding: 10rpx 0;
        border-bottom: 1rpx dashed #e5e5e5;
        line-height: 35rpx;
      }
    }

    .flow-path {
      position: relative;

      &::after {
        content: "";
        display: block;
        height: 1rpx;
        margin: 30rpx 0 0 25rpx;
        border-bottom: 1rpx dashed #e5e5e5;
      }

      image {
        width: 667rpx;
        height: 155rpx;
      }

      .semi-circle {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background: #89bb0d;
        position: absolute;
        bottom: -30rpx;
        left: -50rpx;
        z-index: 2;
      }
    }
  }
}

.footer {
  width: 100%;
  height: 165rpx;
  padding-bottom: 40rpx;
  background: #ffffff;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;
  border-top: 1rpx solid #e5e5e5;

  .go-pay {
    // width: 265rpx;
    // height: 100%;
    // background: linear-gradient(90deg, #FF7500 0%, #FF4B00 100%);
    width: 222rpx;
    height: 81rpx;
    background: #ff4800;
    border-radius: 41rpx;
    line-height: 81rpx;
    // color: #fff;
  }
}

// 购物车
.shopping-cart-box {
  .title {
    padding: 0 30rpx;
    height: 87rpx;
    background: linear-gradient(90deg, #72ce2c 0%, #ade430 100%);
  }

  .commodity-box {
    height: 100%;
    overflow: auto;
    padding-bottom: 250rpx;

    .commodity-list {
      padding: 10rpx 30rpx;

      .commodity-img {
        width: 180rpx;
        height: 180rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 10rpx;
        margin: 0 20rpx;
      }

      .goods-info {
        width: 410rpx;
      }

      .buying-point {
        color: #757575;
      }

      .recommend {
        color: #ff4d0a;
        margin-top: 10rpx;
        width: 160rpx;
        height: 34rpx;
        line-height: 34rpx;
        background: #ffccb8;
        border-radius: 25rpx;
        text-align: center;
      }

      .price-purchase {
        margin-top: 33rpx;
      }
    }
  }
}

// 规则明细
.i-know {
  margin: 10rpx auto 0;
  width: 300rpx;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
  background: linear-gradient(#ff7500 0%, #ff4b00 100%);
  border-radius: 40rpx;
  color: #fff;
}

.followGroupRecords {
  height: 1000rpx;
  margin-left: 68rpx;
  margin-top: 40rpx;
  background: white;

  .followGroup_title {
    padding-top: 40rpx;
    margin-left: 20rpx;
    height: 28rpx;
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #222222;
    line-height: 28rpx;
  }

  .follow_content {
    overflow: scroll;
    height: 900rpx;
    margin-top: 50rpx;
  }

  .follow_list {
    margin-bottom: 60rpx;
    overflow: scroll;
    padding-left: 20rpx;
    padding-right: 20rpx;

    .left_index {
      display: flex;
      align-items: center;

      .numer_index {
        width: 60rpx;
        text-align: center;
        text-align: left;
      }
    }

    .Header_image {
      margin-left: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .image {
        width: 68rpx;
        height: 68rpx;
      }
    }

    .right_cell {
      margin-left: 20rpx;
    }

    .nickName_user {
      font-size: 29rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #0a0a0a;
      display: flex;
      align-items: center;
      width: 200rpx;
    }

    .shopName {
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #9e9e9e;
    }

    .shop_Time {
      font-size: 22rpx;
      margin-left: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #969696;
    }
  }
}
</style>
