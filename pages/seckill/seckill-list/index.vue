<template>
	<view>
		<cu-custom :hideMarchContent="true" :isBack="true" :bgColor="bgColor">
			<block slot="backText">返回</block>
			<block slot="content">
				{{seckillCategorys&&seckillCategorys[selectedCategoryIndex]?(seckillCategorys[selectedCategoryIndex].name||''):''}}秒杀列表
			</block>
		</cu-custom>
		<view class="list-container">
			<image mode="widthFix"
				:src="seckillCategorys[selectedCategoryIndex]?seckillCategorys[selectedCategoryIndex].picUrl[0]:''"
				:style="{
				   height:`${(CustomBar>0?CustomBar:60)}px`,
				   width:'750rpx',
				   overflow:'hidden',
				   display:'block'
				}"></image>
			<view style="position: relative;">
				<image mode="widthFix"
					:src="seckillCategorys[selectedCategoryIndex]?seckillCategorys[selectedCategoryIndex].picUrl[1]:''"
					:style="{
					   width:'750rpx',
					   overflow:'hidden',
					   display:'block'
					}"></image>
				<view class="tab-container" :style="{
						position:'absolute',
						bottom:'70rpx',
						marginLeft:selectedCategoryIndex==0?'16rpx':'34rpx',
						marginRight:selectedCategoryIndex==0?'34rpx':'16rpx',
						width:'700rpx',
						backgroundSize: '700rpx 100%',
						backgroundRepeat:'no-repeat',
						backgroundImage: selectedCategoryIndex==0?'url(https://img.songlei.com/live/category-one.png)':`url(https://img.songlei.com/live/category-two.png)`,
					  }" v-if="seckillCategorys&&seckillCategorys.length>0">
					<view class="tab-item tab1" @click="handleClickItem(0)" :class="selectedCategoryIndex==0?'on':''">
						{{seckillCategorys[0].name}}
					</view>
					<view class="tab-item tab2" @click="handleClickItem(1)" :class="selectedCategoryIndex==1?'on':''">
						{{seckillCategorys[1].name}}
					</view>
				</view>

				<view :style="{
						   position:'absolute',
						   bottom:'-74rpx',
						   backgroundColor:'#ffffff',
						   marginLeft:'16rpx',
						   marginRight:'16rpx',
						   width:'718rpx',
						   height: '126rpx',
						   borderTopRightRadius:selectedCategoryIndex==0?'20rpx':0,
						   borderTopLeftRadius:selectedCategoryIndex==1?'20rpx':0,
						}" class="hotstyle margin-bottom-sm" v-if="seckillList.length > 0">
					<view class="hotstyle_l">
						<view>热卖中</view>
						<text>不能错过</text>
					</view>

					<view class="hotstyle_r">
						<scroll-view scroll-x class="nav text-center seckill-time" scroll-with-animation
							:scroll-left="scrollLeft">
							<view class="navItem flex-sub" :class="index == TabCur ? 'text-white ' : ''"
								v-for="(item, index) in seckillList" :key="index" @tap="tabSelect($event, item)"
								:data-id="index">
								<view class="text-sm" :class="
								      index == TabCur?'ongoing':'unplayed'
				          ">
									{{ item.hallDate |dateFilter }}
								</view>
								<view class="text-sm" :class="
								      index == TabCur?'ongoing':'unplayed'
				          ">
									<text class="text-xs">{{item.hallTime}}点场</text>
								</view>
								<!-- <view v-if="index==TabCur" class="cu-tag bg-white sm round text-red text-sm">
				            {{curHour==item.hallTime?'抢购中':curHour>item.hallTime?'已结束':'即将开始'}}
				          </view> -->
								<view class="text-xs bg-block" :class="
				           index == TabCur?'ongoing':'unplayed'
				          " style="font-size: 22rpx;">
									{{
				            item.beginSecond<=0 && item.remainSecond>=0
				              ? '抢购中'
				              :item.beginSecond>0
				              ? '即将开始'
				              : '已结束'
				          }}
								</view>
							</view>
						</scroll-view>
					</view>

				</view>
			</view>

			<view v-if="!loadingSeckill" class="article no-card" style="margin-top: 76rpx;">
				<view class="goods-item flex" v-for="(item, index) in listSeckillGoodsInfo" :key="index"
					@click="handelGoodsItem(item)">
					<!-- 	<navigator class="padding-lr padding-top" hover-class="none" :url="'/pages/shop/shop-detail/index?id=' + item.shopInfo.id">
									<view class="cu-avatar sm radius" :style="'background-image:url(' + item.shopInfo.imgUrl + ')'"></view>
									<text class="text-black margin-left-sm">{{item.shopInfo.name}}</text>
									<text class="cuIcon-right text-sm"></text>
								</navigator> -->
					<!-- <view class="flex padding-xs padding-bottom align-center padding-top">
									<image :src="item.picUrl" mode="aspectFill" class="row-img margin-left-sm"></image>
									<view class="margin-left-sm seckill-information">
										<view class="text-black text-sm overflow-2 padding-right-sm">{{item.name}}</view>
										<view class="flex justify-start margin-top-sm align-center">
											<view class="cu-capsule round">
												<view class='cu-tag bg-red text-sm'>秒杀价</view>
												<view class="cu-tag line-red text-price text-bold text-df text-red padding-left-xs">{{item.seckillPrice}}</view>
											</view>
											<view class="text-price text-decorat text-gray margin-left-sm">{{item.goodsSku.salesPrice}}</view>
										</view>
										<view class="regmen margin-top-sm padding-right">
											<view class="text-xs text-gray">限量 {{item.limitNum}} 件，已售 {{item.seckillNum}} 件</view>
											<view class="cu-progress round margin-top-sm progress-bar">
												<view class="bg-red" :style="'width:'+item.progress+'%'">
													<text class="margin-left percentage">{{item.progress}}%</text>
												</view>
											</view>
										</view>
									</view>
								</view> -->

					<view class="imgBox">
						<image :src="item.picUrl | formatImg360" mode="aspectFill" class="row-img">
						</image>
					</view>
					<view class="seckill-information">
						<view class="text-black overflow-1">{{ item.name }}</view>
						<view class="" style="padding-right: 28rpx">
							<view class="cu-progress round margin-top-sm progress-bar">
								<view class="setColor" :style="'width:' + item.progress + '%'">
									<text class="margin-left percentage">{{ item.progress }}%</text>
									<image src="https://img.songlei.com/live/shop/schedule.png" mode=""
										class="cu-progress-icon"></image>
								</view>
							</view>
						</view>


						<view class="goods-item-footer">
							<view class="text">
								<view style=" text-decoration: line-through">
									<text style="padding-right: 10rpx">总价值</text>
									<text class="text-gray">¥<text
											class="text-gray">{{item.originalPrice || item.goodsSku.salesPrice }}</text></text>
								</view>
								<!-- <view>
									<text style="padding-right: 10rpx">预估到手价</text>
									<text class="text-color">¥<text
											class="text-big">{{ item.seckillPrice }}</text></text>
								</view> -->
							</view>
							<!-- 	<view class="cu-btn round btn" :class="'bg-' + theme.themeColor" hover-class="none">
								{{ curHour == curSeckillHall.hallTime ? '抢购' : '查看' }}
							</view> -->

						</view>
						<view class="flex align-center justify-around"
							style="margin-top: 10rpx; width: 400rpx; height: 72rpx;background-size: cover; background-image: url(https://img.songlei.com/live/seckillprice_bg.png);">
							<view style="font-size: 24rpx;color: #A96600; ">
								零售价<text style="text-decoration:line-through;">￥{{item.goodsSku.salesPrice}}</text>
							</view>
							<view class="flex flex-direction">
								<view>
									<span style="font-size: 12rpx; color: #fff;">￥</span>
									<span style="font-size: 30rpx; color: #fff;">{{item.seckillPrice}}</span>
								</view>
								<view style="margin-left: 16rpx;font-size: 20rpx; color: #fff;margin-top: -5rpx;">限时秒杀价
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-else style="height:80vh; background:#fff; padding-top:200rpx" class="cu-load loading"></view>
			<recommend-components :canLoad="!loadmore" />

		</view>
	</view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from "@/utils/api";
	import countDown from "@/components/count-down/index";
	import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
	import recommendComponents from '@/components/recommend-components/index.vue'
	import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"
	import {
		senTrack,
		getCurrentTitle
	} from "@/public/js_sdk/sensors/utils.js"
	export default {
		mixins: [navigateUtil],
		components: {
			countDown,
			divBaseNavigator,
			recommendComponents
		},
		watch: {
			curHour() {},
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 100,
					ascs: "sort",
					//升序字段
					descs: "",
				},
				parameter: {
					categoryId: ''
				},
				payment: {},
				loadmore: true,
				listSeckillGoodsInfo: [], //当前时间场次的秒杀商品集合
				seckillList: [],
				curSeckillHall: {
					//当前会场
				},
				outTime: -1,
				TabCur: 0,
				scrollLeft: 0,
				color: "red",
				loading: false,
				modalName: "",
				active: false,
				curHour: 0, //当前小时
				activityPage: {
					current: 1,
					size: 88,
				},
				//有数统计使用
				page_title: '秒杀列表',
				seckillCategorys: [],
				selectedCategoryIndex: 0,
				bgColor: "rgba(255, 255, 255, 0)",
				seckillHallId: '',
				loadingSeckill: true
			};
		},

		onPageScroll(e) {
			this.pageScrollTop = e.scrollTop;
			if (this.pageScrollTop > 10) {
				this.bgColor = "rgba(186, 156, 128, " + ((this.pageScrollTop + 30) / 100) + ')'
			} else {
				this.bgColor = "rgba(186, 156, 128, 0)"
			}
		},

		onLoad: function(options) {
			console.log(options, 'OPTONSHUOQU')
			if (options.shopId) {
				this.parameter.shopId = options.shopId;
			}
			if (options.id) {
				this.parameter.categoryId = options.id;
			}
			if (options.seckillHallId) {
				this.seckillHallId = options.seckillHallId
			}
			if (options.categoryId) {
				this.parameter.categoryId = options.categoryId
			}
			this.curHour = this.$moment().format("H");
			app.initPage().then((res) => {
				this.getSeckillCategory();

			});
			let that = this;
			this.advertisement("SECKILL_LIST")
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.seckillhallList();
			}
		},

		onUnload() {
			this.handleSenActivityTrack('ActivityPageLeave');
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		
		onShareAppMessage: function () {
			this.handleSenActivityTrack("ActivityPageShareOrCollect", "分享");
		},
		
		filters: {
			dateFilter(value) {
				if (!value) return ''
				let reg = new RegExp("2022-", "g");
				let newVal = value.replace(reg, "");
				return newVal
			}
		},
		methods: {
			handleClickItem(index) {
				console.log("---tab---", index, this.selectedCategoryIndex)
				// this.selectedCategoryIndex = 1 
				if (index != this.selectedCategoryIndex) {
					// this.categoryId = ''
					this.parameter.categoryId = ''
					this.selectedCategoryIndex = index;
					this.listSeckillGoodsInfo = [];
					this.TabCur = 0
					this.seckillhallList();
				}
			},
			//广告
			advertisement(id) {
				api.advertisementinfo({
					bigClass: id
				}).then(res => {
					this.payment = res.data;
				});
			},

			getSeckillCategory() {
				api.getSeckillCategory().then(res => {
					if (res.data && res.data.length > 0) {
						this.seckillCategorys = res.data;
						console.log("this.seckillCategorys===>", this.seckillCategorys,
							"this.parameter.categoryId", this.parameter.categoryId)
						for (let i = 0; i < this.seckillCategorys.length; i++) {
							if (this.seckillCategorys[i].id == this.parameter.categoryId) {
								this.selectedCategoryIndex = i;
							}
						}
					}
					this.seckillhallList();
				});
			},

			countDownDone() {
				this.seckillhallList();
			},

			seckillhallList() {
				this.handleSenActivityTrack('ActivityPageLeave');
				let curDate = this.$moment().format("YYYY-MM-DD");
				let that = this;
				let categoryId = null;
				if (this.seckillCategorys && this.seckillCategorys.length > 0 && this.seckillCategorys[this
						.selectedCategoryIndex]) {
					categoryId = this.seckillCategorys[this.selectedCategoryIndex].id;
				}
				const params = {
					curDate
				}
				if (categoryId > 0) {
					params.categoryId = categoryId
				}
				this.loadingSeckill = true;
				
				api.seckillhallList(params).then((res) => {
					this.loadingSeckill = false;
					let seckillList = res.data;
					this.loadmore = false;
					if (seckillList && seckillList.length > 0) {
						let hasSeckill = false;
						this.seckillList = seckillList;
						if (that.seckillHallId && this.parameter.categoryId) {
							this.curSeckillHall.id = this.seckillHallId ;
							this.seckillList.forEach(function(item, index) {
								if (that.seckillHallId == item.id) {
									that.curSeckillHall = item;
									that.handleSenActivityTrack('ActivityPageView');
									that.TabCur = index
									that.getSeckillGoodsData(item.id, item.beginSecond);
								}
							})
							return
						}
						seckillList.forEach((item, index) => {
							if (item.hallDate == that.$moment(new Date()).format("YYYY-MM-DD")) {
								if (item.hallTime == that.curHour) {
									//默认设置当前小时的秒杀时间，如果没有就设置最近时间的秒杀时间
									this.curSeckillHall = item;
									this.handleSenActivityTrack('ActivityPageView');
									this.TabCur = index;
									this.scrollLeft = index * 60;
									hasSeckill = true;
									this.getSeckillGoodsData(item.id, item.beginSecond);
									return;
								} else if (!hasSeckill && item.hallTime > that.curHour) {
									//秒杀时间必须大于当前时间
									this.curSeckillHall = item;
									this.handleSenActivityTrack('ActivityPageView');
									this.TabCur = index;
									this.scrollLeft = index * 60;
									hasSeckill = true;
									this.getSeckillGoodsData(item.id, item.beginSecond);
									return;
								}
							}
						});
						if (!hasSeckill) {
							this.curSeckillHall = seckillList[0];
							that.handleSenActivityTrack('ActivityPageView');
							this.getSeckillGoodsData(this.curSeckillHall.id, this.curSeckillHall.beginSecond);
						}
					} else {
						this.seckillList = [];
					}
				}).catch(e => {
					this.loadingSeckill = false;
				});
			},

			setCountDown() {
				// 设置倒计时时间，
				// 如果当前时间大于会场时间，结束
				// 如果当前时间等于，正在进行中
				// 如果小于暂未开始
				if (this.curSeckillHall.hallTime < this.curHour) {
					this.outTime = 0;
				} else if (this.curSeckillHall.hallTime == this.curHour) {
					//计算倒计时多少秒
					let curDateTime = new Date().getTime(); //当前时间
					let nextHour = Number(this.curHour) + 1;
					let nextDateTime =
						this.curSeckillHall.hallDate + " " + nextHour + ":00:00";
					let timeTemp = this.$moment(nextDateTime).toDate();
					this.outTime = new Date(timeTemp).getTime() - curDateTime; //下一个整点时间
				} else {
					this.outTime = -1;
				}
			},

			tabSelect(e, item) {
				this.handleSenActivityTrack('ActivityPageLeave');
				this.curHour = this.$moment().format("H");
				this.TabCur = e.currentTarget.dataset.id;
				this.scrollLeft = (e.currentTarget.dataset.id - 1) * 60;
				this.listSeckillGoodsInfo = [];
				console.error("=====tabSelect===1==", item);
				if (item) {
					item.listSeckillInfo = item.listSeckillInfo ? item.listSeckillInfo : [];
					this.curSeckillHall = item;
					this.getSeckillGoodsData(item.id, item.beginSecond);
					console.error("=====tabSelect===2==", item);
					this.handleSenActivityTrack('ActivityPageView');
				} else {
					this.curSeckillHall = {
						listSeckillInfo: [],
						outTime: 0,
					};
				}
				// this.setCountDown();
			},

			getSeckillGoodsData(id, beginSecond) {
				this.loadingSeckill = true;
				api
					.seckillinfoPage(
						Object.assign({
								seckillHallId: id,
							},
							this.page,
							util.filterForm(this.parameter)
						)
					)
					.then((res) => {
						let listSeckillGoodsInfo = res.data.records;
						console.log("listSeckillGoodsInfo", listSeckillGoodsInfo);
						listSeckillGoodsInfo.forEach((item, index) => {
							if (beginSecond > 0) {
								item.progress = 0;
							} else {
								console.log(item.seckillNum, item.limitNum, 'limynum')
								item.progress = (item.seckillNum / item.limitNum * 100).toFixed()
							}
						});
						this.listSeckillGoodsInfo = [
							...this.listSeckillGoodsInfo,
							...listSeckillGoodsInfo,
						];
						if (listSeckillGoodsInfo.length < this.page.size) {
							this.loadmore = false;
						}
						this.loadingSeckill = false;
					}).catch(e => {
						this.loadingSeckill = false;
					});
			},

			refresh() {
				this.loadmore = true;
				this.seckillList = [];
				this.listSeckillGoodsInfo = [];
				this.page.current = 1;
				this.seckillhallList();
			},


			handleSenActivityTrack(eventName, actionType) {
				if(this.curSeckillHall.id>0){
					const params = {};
					if (eventName == "ActivityPageView") {
						this.startTime = new Date().getTime();
						params.forward_source = getCurrentTitle(1);
					} else if (eventName == "ActivityPageLeave") {
						params.forward_source = getCurrentTitle(1);
						params.stay_duration = Math.floor((new Date().getTime() - this.startTime) / 1000)
					}
					if(actionType) {
						params.action_type = actionType;
					}
					
					//神策添加埋点
					senTrack(eventName, {
						page_name: getCurrentTitle(0),
						page_level: 1,
						activity_id: this.curSeckillHall.id,
						activity_name: this.curSeckillHall.seckillName,
						activity_type_first: '营销活动',
						activity_type_second: '秒杀',
						...params
					});
				}
			},

			handelGoodsItem(item) {
				this.handleSenActivityTrack("ActivityClick");
				this.toPage('/pages/seckill/seckill-detail/index?seckillHallInfoId=' + item.seckillHallInfo.id+'&source_module=秒杀列表页面');
			}
		},
	};
</script>
<style scoped lang="scss">
	.list-container {
		position: absolute;
		top: 0;
	}

	.navItem {
		display: inline-block;
		line-height: 30rpx;
		margin: 0 20rpx;
	}

	.seckill-time {
		min-height: 50px;
		// margin-top: -220rpx;
	}

	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx;
	}

	.seckill {
		min-height: 1450rpx;
	}

	.line {
		display: inline-block;
		width: 120rpx;
		border-top: 0.5rpx solid #cccccc;
		vertical-align: 5px;
		opacity: 0.5;
	}

	.goods-item {
		background-color: #ffffff;
		margin: 0 16rpx;
		border-radius: 22rpx;
		margin-bottom: 20rpx;
		padding: 10rpx 0 8rpx 15rpx;

		.imgBox {
			width: 229rpx;
			margin-right: 20rpx;
		}

		.seckill-information {
			width: calc(100% - 229rpx - 30rpx);

			.text-black {
				font-size: 28rpx;
				margin-right: 45rpx;
			}

			.progress-bar {
				height: 30rpx;
				background-color: #ffebe8;
				border-radius: 15rpx;
				overflow: inherit;

				.setColor {
					background: linear-gradient(0deg, #ff3537 0%, #fa603c 100%);
					border: 1px solid #dd392f;
					box-sizing: border-box;
					border-radius: 15rpx;
					position: relative;
					font-size: 26rpx;

					.cu-progress-icon {
						position: absolute;
						width: 40rpx;
						height: 40rpx;
						right: -20rpx;
						max-width: inherit;
					}

					// &::before{
					// 	content: " ";
					// 	display: block;
					// 	width: ;
					// }
				}
			}

			.goods-item-footer {
				padding-right: 24rpx;
				display: flex;
				align-items: flex-end;
				padding-top: 10rpx;

				.text {
					font-size: 22rpx;
					color: #666666;
					flex: 1;

					.text-color {
						color: #ff4015;
					}

					.text-big {
						font-size: 32rpx;
					}
				}

				.btn {
					width: 132rpx;
					height: 58rpx;
					background: linear-gradient(-90deg, #ff324f 0%, #ff5102 100%);
					border-radius: 29rpx;
					font-size: 26rpx;
				}
			}
		}
	}

	.seckill-img {
		height: 208rpx;
		box-sizing: border-box;
		position: relative;
		top: 0rpx;
	}

	.seckill-image-bg {
		width: 100%;
		position: absolute;
		z-index: 1;
	}

	.seckill-image-bg image {
		width: 100%;
		height: 158rpx;
	}

	.seckill-image {
		width: 100%;
		padding: 0 20rpx;
		position: absolute;
		z-index: 2;
		top: 10rpx;
		// margin-top: -120rpx;
	}

	.seckill-image image {
		width: 100%;
		height: 198rpx;
	}

	.hotstyle {
		width: 100%;
		display: flex;
		flex: 1;
	}

	.hotstyle_l {
		width: 18%;
		text-align: center;
		padding-top: 10rpx;
	}

	.hotstyle_l view {
		line-height: 38rpx;
		font-size: 28rpx;
		font-weight: bold;
		color: #000000;
	}

	.hotstyle_l text {
		display: block;
		padding-top: 8rpx;
		font-size: 22rpx;
		font-weight: 500;
		color: #666666;
	}

	.hotstyle_r {
		width: 82%;
		padding: 10rpx 0;
	}

	.wrapper-list-goods {
		white-space: nowrap;
		// margin-top: 25rpx;
		// margin-bottom: 25rpx;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 172rpx;
		height: auto;
		margin-left: 10rpx;
	}

	.wrapper-list-goods .item .img-box {
		width: 100%;
		height: 180rpx;
	}

	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx 5rpx 0 0;
	}

	.percentage {
		color: #ffb04a !important;
	}

	.btn-enter {
		width: 580rpx;
		height: 76rpx;
	}

	.navItem {
		.text-sm {
			font-size: 24rpx;
			font-weight: bold;
			// color: #FC2E05;
			padding-bottom: 8rpx;
		}

		.text-xs {
			font-size: 30rpx;
		}

		.bg-block {
			border-radius: 15rpx;
			padding: 0rpx 12rpx;
		}

		// 进行中
		.text-sm.ongoing {
			color: #fc2e05;
		}

		.text-xs.ongoing {
			background: linear-gradient(90deg, #ff324f 0%, #ff5102 100%);
		}

		// 已结束
		.text-sm.end {
			color: #999999;
		}

		.text-xs.end {
			color: #999999;
		}

		// 未开始
		.text-sm.unplayed {
			color: #000000;
		}

		.text-xs.unplayed {
			color: #666666;
		}
	}

	.tab-container {
		width: 750rpx;
		height: 66rpx;

		.tab-item {
			display: inline-block;
			width: 50%;
			font-size: 30rpx;
			font-weight: normal;
			line-height: 34px;
			text-align: center;
			margin-top: 4rpx;
		}

		.tab1 {
			color: #FF2D37;
		}

		.tab2 {
			color: #7B4223;
		}

		.on {
			font-weight: bold;
		}

	}
</style>