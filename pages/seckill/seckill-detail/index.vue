<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">秒杀详情</block>
    </cu-custom>
    <view class="cu-card article no-card">
      <image
        mode="widthFix"
        class="cu-avatar radius seckill-image"
        :src="seckillInfo.picUrl"
      >
      </image>

      <view
        class="padding-xs"
        style="background: linear-gradient(90deg, #ff4c4f 0%, #fd6164 100%)"
      >
        <!-- <view class="action">{{ seckillInfo.name }}</view> -->

        <view class="flex align-center justify-between padding-left-sm">
          <view class="bg-video">
            <view
              v-if="seckillInfo && seckillInfo.seckillPrice"
              class="flex align-center"
            >
              <format-price
                styleProps="font-weight:bold; display:flex; align-items:baseline;"
                color="#fff"
                signFontSize="20rpx"
                smallFontSize="24rpx"
                priceFontSize="44rpx"
                :price="seckillInfo.seckillPrice"
              ></format-price>
              <text
                class="bg-white round text-xs discount-tag"
                style="margin-left: 10rpx"
                >限时折扣</text
              >
            </view>

            <view class="text-white">
              <text class="text-sm">价格</text>
              <text class="text-price text-decorat padding-left:10rpx">
                {{
                  seckillInfo.goodsSku.salesPrice
                    ? seckillInfo.goodsSku.salesPrice
                    : ""
                }}
              </text>
            </view>
          </view>

          <view
            class="cu-item"
            v-if="
              seckillInfo.seckillHall &&
              (seckillInfo.seckillHall.beginSecond >= 0 ||
                seckillInfo.seckillHall.remainSecond >= 0)
            "
          >
            <view
              class="text-sm margin-left-xs padding-bottom-xs"
              style="color: #ffffff"
            >
              {{
                seckillInfo.seckillHall.beginSecond >= 0
                  ? "距离开始"
                  : "距离结束"
              }}
              仅剩：
            </view>

            <count-down
              v-if="seckillInfo.seckillHall.beginSecond >= 0"
              class="bg-white round padding-xs text-xs"
              :connectorColor="'#f00'"
              :outTime="seckillInfo.seckillHall.beginSecond * 1000"
              @countDownDone="countDownDone"
            >
            </count-down>
            <count-down
              v-else
              class="bg-white round padding-xs text-xs"
              :connectorColor="'#f00'"
              :outTime="seckillInfo.seckillHall.remainSecond * 1000"
              @countDownDone="countDownDone"
            >
            </count-down>
          </view>

          <view v-else style="color: #ffffff">
            {{
              seckillInfo.seckillHall &&
              seckillInfo.seckillHall.beginSecond < 0 &&
              seckillInfo.seckillHall.remainSecond > 0
                ? "立即购买"
                : seckillInfo.seckillHall &&
                  seckillInfo.seckillHall.beginSecond >= 0
                ? "即将开始"
                : "已结束"
            }}
          </view>

          <view
            @click="shareShowFun"
            class="text-sm text-white flex align-center"
            style="
              position: absolute;
              width: 100rpx;
              height: 80rpx;
              top: 5rpx;
              right: 0rpx;
              color: #898989;
            "
          >
            <view class="cuIcon-share"></view>
            <text class="margin-left-xs">分享</text>
          </view>
        </view>
      </view>
    </view>

    <view class="cu-item bg-white">
      <view class="content padding-sm" style="padding: 15rpx">
        <!-- 领劵 -->
        <view class="product-bg">
          <view class="cu-bar myBar" style="min-height: 60rpx">
            <view
              class="flex response margin-left-sm"
              v-if="couponInfoList.length > 0"
            >
              <view class="flex">
                <view
                  class="coupon text-sm text-orange"
                  style="background-color: #eeeeee"
                >
                  {{ couponInfoList[0].name }}
                </view>

                <view
                  class="coupon text-sm text-orange"
                  style="margin-left: 10rpx; background-color: #eeeeee"
                  v-if="couponInfoList.length > 1"
                >
                  {{ couponInfoList[1].name }}
                </view>
              </view>

              <view
                class="flex-sub text-orange text-right"
                @tap="showModalCoupon"
                >领券<text class="cuIcon-right"></text>
              </view>
            </view>
          </view>
        </view>

        <view class="desc">
          <view
            class="text-black overflow-2"
            v-if="
              seckillInfo && seckillInfo.goodsSpu && seckillInfo.goodsSpu.name
            "
          >
            {{ seckillInfo.goodsSpu.name }}
          </view>

          <view
            class="cu-tag bg-red radius sm margin-left"
            v-if="seckillInfo.goodsSpu.freightTemplat.type == '2'"
          >
            包邮
          </view>

          <view
            class="text-gray text-sm overflow-1 margin-top-xs"
            v-if="seckillInfo.goodsSku.specs.length > 0"
          >
            规格:{{ specInfo || "" }}
          </view>

          <view class="flex justify-between margin-top-sm">
            <text class="text-sm text-gray">
              <!-- 已售{{ seckillInfo.seckillNum }} -->
            </text>
            <text class="text-sm text-gray">
              <!-- 限量{{ seckillInfo.limitNum }} -->
            </text>
            <text class="text-sm" style="color: #898989" @tap="ruleShow"
              >秒杀规则<text
                class="cuIcon-question"
                style="margin-left: 10rpx"
              ></text
            ></text>
          </view>
        </view>
      </view>
    </view>

    <navigator
      class="product-bg bg-white"
      style="margin: 20rpx; width: auto"
      :url="'/pages/shop/shop-detail/index?id=' + seckillInfo.goodsSpu.shopId"
    >
      <view class="storeBgImgUrl" v-if="seckillInfo.goodsSpu.storeBgImgUrl">
        <image :src="seckillInfo.goodsSpu.storeBgImgUrl"></image>
      </view>
    </navigator>

    <view
      class="goodsNameCon bg-white margin-top-sm"
      style="position: relative"
    >
      <view class="cu-bar myBar">
        <view
          class="text-lg text-bold padding-tb-xs padding-right-sm margin-bottom-xs"
          style="margin-right: 50rpx"
        >
          <class-label
            v-for="(item, index_) in titleLable"
            :key="index_"
            :text="item.text"
            :value="item.value"
            :marginRight="20"
          ></class-label>
          <text class="text-black">{{ seckillInfo.name }}</text>
        </view>
      </view>
      <!-- <view class="cu-bar bg-white">
					<view class="text-sm padding-tb-xs padding-lr-sm">
						<text class="text-gray">{{goodsSpu.sellPoint}}</text>
					</view>
				</view> -->
      <view
        @click="shareShowFun"
        class="text-sm text-white flex align-center"
        style="
          position: absolute;
          width: 100rpx;
          height: 80rpx;
          bottom: 0rpx;
          right: 0rpx;
          color: #898989;
        "
      >
        <view class="cuIcon-share"></view>
        <text class="margin-left-xs">分享</text>
      </view>
    </view>

    <!-- 发货、保障 -->
    <view
      class="goodsNameCon"
      style="background-color: #ffffff; padding: 20rpx"
    >
      <view
        class="goodsChoice bg-white"
        v-if="seckillInfo.goodsSpu.specType == '1'"
      >
        <view class="cu-bar myBar margin-top-sm">
          <view class="flex response" @tap="showModalSku">
            <view class="label_goodsChoice text-df">
              <view class="margin-left-sm">选择</view>
            </view>

            <view class="flex-treble text-sm text-black">
              <view
                class="display-ib"
                v-for="(item, index) in goodsSpecData"
                :key="index"
              >
                <view class="display-ib" v-if="!item.checked">
                  {{ item.value }}
                </view>

                <view
                  class="display-ib"
                  v-if="item.checked"
                  v-for="(item2, index2) in item.leaf"
                  :key="index2"
                >
                  <view class="display-ib" v-if="item.checked == item2.id">
                    {{ item2.value }}
                  </view>
                </view>
                <view
                  class="display-ib"
                  v-if="goodsSpecData.length != index + 1"
                  >,
                </view>
              </view>

              <view
                class="colorClass"
                v-if="
                  seckillInfo.goodsSpu.skus &&
                  seckillInfo.goodsSpu.skus[0].picUrl
                "
              >
                <image
                  v-for="(item, index) in seckillInfo.goodsSpu.skus.slice(0, 4)"
                  :key="index"
                  :src="item.picUrl"
                  mode="widthFix"
                ></image>

                <class-label
                  :text="'共' + seckillInfo.goodsSpu.skus.length + '种分类可选'"
                  value="lightDarkGrey"
                  :marginRight="0"
                  :marginBottom="0"
                  :padding="[18, 18]"
                >
                </class-label>
              </view>
            </view>
            <text class="cuIcon-more margin-right-sm"></text>
          </view>
        </view>
      </view>

      <view class="cu-bar myBar">
        <view class="flex response">
          <view class="label_goodsChoice text-df">
            <view>发货</view>
          </view>

          <view class="flex-treble text-sm">
            <text
              class="cuIcon-location text-black"
              v-if="goodsSpus.deliveryPlace"
              >{{ goodsSpus.deliveryPlace.place }} |</text
            >

            <text v-if="goodsSpus.freightMsg && goodsSpus.freightMsg != null">
              {{ goodsSpus.freightMsg }}
            </text>
          </view>
          <!-- <view class="flex-treble text-sm padding-left-xs">
            <text
              class="cuIcon-location text-black"
              v-if="seckillInfo.goodsSpu.deliveryPlace"
            >{{ seckillInfo.goodsSpu.deliveryPlace.place }} |
            </text>
            <text v-if="seckillInfo.goodsSpu.freightTemplat">
              运费：{{
                seckillInfo.goodsSpu.freightTemplat.type == '2'
                  ? '全国包邮'
                  : '￥' + seckillInfo.goodsSpu.freightTemplat.firstFreight
              }}
            </text>
          </view> -->
          <text class="margin-right-sm transparent">''</text>
          <!-- <view class="flex-sub text-sm text-gray text-right margin-right">销量{{
              seckillInfo.goodsSpu.saleNum ? seckillInfo.goodsSpu.saleNum : 0
            }}</view> -->
        </view>
      </view>

      <!-- 
			当前无字段
			<view class="cu-bar myBar margin-top-sm">
				<view class="flex response">
					<view class="label_goodsChoice text-sm">
						<view class="text-gray margin-left-sm">送至</view>
					</view>
					<view class="flex-treble text-sm">
						<view class="margin-bottom-xs">
							<text class="cuIcon-location margin-right-xs text-orange "></text><text>{{'客户地址' || '请选择你的收货地址'}}</text>
						</view>
						<view class="margin-bottom-xs">
							<text>是否现货、或从哪发货，预计送达时间</text>
						</view>
						<view class="margin-bottom-xs">
							<text>物流服务项</text>
						</view>
						
					</view>
					<text class="cuIcon-more margin-right-sm"></text>
				</view>
			</view> 
			-->
      <view class="cu-bar myBar" v-if="ensureList.length > 0">
        <view class="flex response" @tap="showModalService">
          <view class="label_goodsChoice text-df" style="color: #000000">
            <view>保障</view>
          </view>
          <view class="flex-treble text-sm padding-left-xs">
            <text>
              {{ ensureList[0].name
              }}{{ ensureList[1] ? " | " + ensureList[1].name : "" }}
            </text>
          </view>
          <text class="cuIcon-more margin-right-sm"></text>
          <!-- <text class="margin-right-sm transparent">''</text> -->
        </view>
      </view>
    </view>
    <!-- 商品评价 -->
    <view class="goodsEvaluate bg-white margin-top-sm">
      <view class="cu-bar solid-bottom">
        <view class="flex response">
          <view class="flex-sub text-df">
            <view class="text-black margin-left-sm" style="color: #000">
              商品评价（{{ goodsAppraises.total ? goodsAppraises.total : 0 }}）
            </view>
          </view>

          <navigator
            :url="'/pages/appraises/list/index?spuId=' + seckillHallInfoId"
            hover-class="none"
            class="flex-sub text-df text-gray text-right margin-right-sm"
            v-if="goodsAppraises.total > 0"
          >
            查看全部<text class="cuIcon-right"></text>
          </navigator>
        </view>
      </view>

      <view class="cu-list menu-avatar comment">
        <navigator
          :url="'/pages/appraises/list/index?spuId=' + seckillHallInfoId"
          hover-class="none"
          class="cu-item"
          v-for="(item, index) in goodsAppraises.records"
          :key="index"
        >
          <view
            class="cu-avatar round"
            :style="'background-image:url(' + item.headimgUrl + ')'"
          >
            {{ !item.headimgUrl ? "头" : "" }}
          </view>
          <view class="content padding-bottom-xs margin-top-xs">
            <view class="text-black">
              {{ item.nickName ? item.nickName : "匿名" }}
              <view class="text-gray margin-left text-sm">
                {{ item.createTime }}
              </view>
            </view>

            <view class="text-gray text-sm" v-if="item.specInfo">
              规格：{{ item.specInfo }}
            </view>

            <base-rade :value="item.goodsScore" size="lg"></base-rade>

            <view class="text-black text-content text-sm overflow-2">
              {{ item.content ? item.content : "此用户未填写评价内容" }}
            </view>

            <view class="grid col-4 grid-square flex-sub">
              <view
                class="bg-img margin-top-sm"
                v-for="(picUrl, index2) in item.picUrls"
                :key="index2"
              >
                <image :src="picUrl | formatImg750" mode="aspectFill"></image>
              </view>
            </view>
          </view>
        </navigator>
      </view>
    </view>
    <!-- 1 -->
    <view
      class="cu-bar bg-white"
      v-if="
        seckillInfo.goodsSpu.pointsGiveSwitch == '1' ||
        seckillInfo.goodsSpu.pointsDeductSwitch == '1'
      "
    >
      <view class="flex response">
        <view class="flex-sub text-sm">
          <view class="text-gray margin-left-sm">优惠</view>
        </view>

        <view class="flex-four text-sm">
          <view v-if="seckillInfo.goodsSpu.pointsGiveSwitch == '1'">
            <text class="cu-tag bg-red light sm radius">积分赠送</text>
            <text class="text-black margin-left-xs">
              购买可获得{{ seckillInfo.goodsSpu.pointsGiveNum }}积分
            </text>
          </view>

          <view
            class="margin-top-xs"
            v-if="seckillInfo.goodsSpu.pointsDeductSwitch == '1'"
          >
            <text class="cu-tag bg-red light sm radius">积分抵扣</text>
            <text class="text-black margin-left-xs">
              1积分可抵{{
                seckillInfo.goodsSpu.pointsDeductAmount
              }}元，最多可抵{{ seckillInfo.goodsSpu.pointsDeductScale }}%
            </text>
          </view>
        </view>
      </view>
    </view>

    <view :class="'cu-modal bottom-modal ' + modalService">
      <view class="cu-dialog">
        <view class="padding-xl" style="background-color: #fff">
          <view class="text-lg text-center">基础服务</view>
          <view class="cu-list text-left solid-bottom">
            <view
              class="cu-item"
              v-for="(item, index) in ensureList"
              :key="index"
            >
              <view class="content padding-tb-sm">
                <view>
                  <text class="cuIcon-roundcheckfill text-orange"> </text>
                  {{ item.name }}
                </view>
                <view class="text-gray text-sm" v-if="item.detail">
                  {{ item.detail }}
                </view>
              </view>
            </view>
          </view>

          <button
            class="cu-btn margin-top response lg"
            :class="'bg-' + theme.themeColor"
            @tap="hideModalService"
          >
            确定
          </button>
        </view>
      </view>
    </view>

    <!-- 优惠劵 -->
    <coupon-receive
      v-if="modalCoupon"
      :couponInfoList="couponInfoList"
      :modalCoupon="modalCoupon"
      @changeModalCoupon="modalCoupon = $event"
      @receiveCouponChange="receiveCouponChange($event)"
    >
    </coupon-receive>

    <view class="margin-top-sm">
      <shopInfo
        :shopInfo="seckillInfo.shopInfo"
        :card="false"
        borderRadius="0"
      ></shopInfo>
    </view>

    <view class="cu-card no-card margin-top-xs" style="padding-bottom: 120rpx">
      <view class="cu-item">
        <view class="cu-bar bg-white">
          <view class="content">商品信息</view>
        </view>

        <view class="bg-white">
          <jyf-parser :html="article_description"></jyf-parser>
        </view>
        <recommendComponents
          :itemId="seckillHallInfoId"
          :showTitle="true"
          :sceneId="goodsSpus.sceneId"
          :canLoad="canLoad"
        />
        <view class="cu-load bg-gray to-down">已经到底啦...</view>
      </view>
    </view>

    <!-- 底部 -->
    <view class="cu-bar bg-white tabbar border shop foot">
      <view class="action bg-white" @click="showModalCustomerService">
        <view class="cuIcon-servicefill"></view>客服
      </view>

      <view class="action" @tap="gotoShop">
        <view class="cuIcon-cart" style="width: 85rpx">
          <view
            style="position: absolute; right: 8rpx; top: -10rpx"
            class="cu-tag badge"
            v-show="shoppingCartCount != '0'"
          >
            {{ shoppingCartCount }}
          </view>
        </view>
        购物车
      </view>

      <!-- <view class="submit" @tap="tobuy">
        <view class="cu-btn bg-orange round shadow-blur">
          <view class="text-price">{{ seckillInfo.goodsSku.salesPrice }}</view>
          <view>原价购买</view>
        </view>
      </view>

      <view @tap="toSeckillBuy" >
        <view
          class="cu-btn round shadow-blur margin-right"
          :class="'bg-' + (outTime > 0 ? 'red' : 'gray') + ' submit'"
        >
          <view class="text-price">{{ seckillInfo.seckillPrice }}</view>
          <view>
            {{
              outTime > 0 ? '立即购买' : outTime == 0 ? '即将开始' : '已结束'
            }}
          </view>
        </view>
      </view> -->
      <view class="btn-group">
        <button
          v-if="
            seckillInfo.seckillHall.remainSecond < 0 ||
            seckillInfo.seckillHall.beginSecond > 0 ||
            seckillInfo.seckillNum >= seckillInfo.limitNum
          "
          :style="{
            height: '72rpx',
            flex: '1',
            background: 'linear-gradient(90deg, #FFC305 0%, #FF9700 100%)',
            borderRadius: '36px 0px 0px 36px',
            padding: 0,
          }"
          class="cu-btn bg-orange round shadow-blur"
          @tap="tobuy"
        >
          <text class="text-price">{{ seckillInfo.goodsSku.salesPrice }}</text>
          <text>零售价购买</text>
        </button>

        <button
          v-else
          :style="{
            height: '72rpx',
            flex: '1',
            background: 'linear-gradient(90deg, #FFC305 0%, #FF9700 100%)',
            borderRadius: '36px 0px 0px 36px',
            padding: 0,
          }"
          class="cu-btn bg-orange round shadow-blur"
        >
          <text style="opacity: 0.5">加入购物车</text>
        </button>

        <template v-if="seckillInfo.seckillHall">
          <button
            :style="{
              height: '72rpx',
              flex: '1',
              borderRadius: '0px 36px 36px 0px',
              padding: 0,
            }"
            class="cu-btn round shadow-blur"
            :disabled="
              seckillInfo.seckillHall.remainSecond < 0 ||
              seckillInfo.seckillHall.beginSecond > 0 ||
              seckillInfo.seckillNum >= seckillInfo.limitNum
            "
            :class="
              'bg-' +
              (seckillInfo.seckillHall.remainSecond > 0 &&
              seckillInfo.seckillHall.beginSecond < 0 &&
              seckillInfo.seckillNum < seckillInfo.limitNum
                ? 'red'
                : 'gray') +
              ' submit'
            "
            @tap="toSeckillBuy"
          >
            {{
              seckillInfo.seckillHall.remainSecond > 0 &&
              seckillInfo.seckillHall.beginSecond < 0 &&
              seckillInfo.seckillNum < seckillInfo.limitNum
                ? "立即购买"
                : seckillInfo.seckillHall.beginSecond > 0
                ? "即将开始"
                : seckillInfo.seckillNum >= seckillInfo.limitNum
                ? "已售罄"
                : seckillInfo.seckillHall.remainSecond < 0
                ? "已结束"
                : ""
            }}
          </button>
        </template>
      </view>
    </view>

    <view :class="'cu-modal ' + modalRule">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">规则说明</view>

          <view class="action" @tap="ruleHide">
            <text class="cuIcon-close text-red"></text>
          </view>
        </view>

        <view class="padding-xl text-left" style="background: white">
          <text>{{ seckillInfo.seckillRule }}</text>
        </view>
      </view>
    </view>
    <!-- 分享组件 -->
    <share-component
      v-if="showShare"
      v-model="showShare"
      :shareParams="shareParams"
    ></share-component>

    <customer-service-modal
      v-if="modalCustomerService"
      :modalCustomerService="modalCustomerService"
      @changeModal="modalCustomerService = $event"
      :shopId="seckillInfo.shopInfo.id"
      :phone="seckillInfo.shopInfo.phone"
      :wxCustomerUrl="seckillInfo.shopInfo.wxCustomerUrl"
      :goodsSpuId="seckillInfo.goodsSpu.id"
      :goodsName="seckillInfo.goodsSpu.name"
      :goodsPic="seckillInfo.goodsSpu.picUrls[0]"
      :sharePic="seckillInfo.goodsSpu.sharePic"
    >
    </customer-service-modal>

    <view :class="'cu-modal ' + (seckKillError ? 'show' : '')">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">提示</view>
        </view>
        <view class="padding-xl">抱歉，该秒杀商品已下架</view>
      </view>
    </view>

    <share-product-pic
      ref="poster"
      :riTitle="isPrice ? '松鼠优惠' : '松鼠爆款'"
      :riTime="preferentialPrice"
      :showDesc="isPrice"
      :estimatedPrice="seckillInfo.seckillPrice"
      :originalPrice="seckillInfo.goodsSku.salesPrice"
      :spuImg="seckillInfo.picUrl"
      @success="shareSuccess"
    >
    </share-product-pic>
  </view>
</template>

<script>
const util = require("@/utils/util.js");
const { base64src } = require("utils/base64src.js");
const app = getApp();
import api from "@/utils/api";
import numberUtil from "utils/numberUtil.js";
import dateUtil from "utils/dateUtil.js";
import { senTrack, getCurrentTitle } from "@/public/js_sdk/sensors/utils.js";
import baseRade from "@/components/base-rade/index";
import countDown from "@/components/count-down/index";
import shareComponent from "@/components/share-component/index";
import shopInfo from "components/shop-info/index";
import recommendComponents from "@/components/recommend-components/index.vue";
import couponReceive from "@/components/coupon-receive/index";
import customerServiceModal from "@/components/customer-service-modal";
import shareProductPic from "components/share-product-pic/index";
import formatPrice from "@/components/format-price/index.vue";
export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      dateUtil: dateUtil,
      numberUtil: numberUtil,
      modalService: "",
      goodsAppraises: {}, //商品评价
      modalSku: false,
      modalSkuType: "",
      goodsSpecData: [],
      shoppingCartCount: 0,
      titleLable: [],
      goodsSpus: {}, //商品详情
      canLoad: false,
      id: "",
      seckillInfo: {
        shopInfo: {},
        name: "",
        seckillNum: 0,
        hallTime: 0,
        goodsSpu: {
          freightTemplat: {},
        },
        goodsSku: {
          specs: [],
        },
        beginSecond: 0,
        remainSecond: 0,
      },
      disabled: false,
      parameter: {},
      ensureList: [], //商品保障
      modalCoupon: false,
      shareShow: "",
      couponInfoList: [],
      curLocalUrl: "",
      userInfo: null,
      modalRule: "",
      seckillHallInfoId: "",
      specInfo: "",
      cutPercent: "",
      canCutPrice: "",
      havCutPrice: "",
      posterUrl: "",
      posterShow: false,
      posterConfig: "",
      article_description: "",
      hasBargainUser: false, //是否存在砍价数据
      bargainUserId: "",
      curHour: 0,
      outTime: -1,
      nextCountDown: -1, //如果该商品详情是暂未开始的商品那么获取自动启动倒计时

      showShare: false,
      shareParams: {},
      seckKillError: false,
      modalCustomerService: false,
      //有数统计使用
      page_title: "秒杀详情",
      shareImgSrc: "",
      source_module: "", // 神策统计使用来源模块
    };
  },

  components: {
    shareComponent,
    countDown,
    shopInfo,
    recommendComponents,
    couponReceive,
    baseRade,
    customerServiceModal,
    shareProductPic,
    formatPrice,
  },
  computed: {
    //松鼠优惠价显示与否  有优惠价并且划线减优惠价大于0 显示
    isPrice() {
      if (this.seckillInfo.goodsSku && this.seckillInfo.goodsSku.salesPrice) {
        if (
          this.seckillInfo.goodsSku.salesPrice - this.seckillInfo.seckillPrice >
          0
        ) {
          return true;
        }
      }
      return false;
    },

    //计算松鼠优惠价
    preferentialPrice() {
      let price = "";
      if (this.seckillInfo.goodsSku && this.seckillInfo.goodsSku.salesPrice) {
        let originalPrice = this.seckillInfo.goodsSku.salesPrice;
        let estimatedPrice = this.seckillInfo.seckillPrice;
        if (originalPrice - estimatedPrice > 0) {
          price = originalPrice - estimatedPrice;
          let pric = price.toFixed(1);
          return pric;
        }
      }
    },
  },

  onLoad(options) {
    this.dateUtil = dateUtil;
    this.numberUtil = numberUtil;
    this.curHour = this.$moment().format("H");
    let seckillHallInfoId;
    if (options.scene) {
      //接受二维码中参数
      let scenes = decodeURIComponent(options.scene).split("&");
      seckillHallInfoId = scenes[0];
    } else {
      if (options.seckillHallInfoId) {
        seckillHallInfoId = options.seckillHallInfoId;
      } else if (options.id) {
        seckillHallInfoId = options.id;
      }
    }
    this.seckillHallInfoId = seckillHallInfoId;
    this.source_module = options.source_module
      ? decodeURIComponent(options.source_module)
      : "";
    app.initPage().then((res) => {
      this.userInfo = uni.getStorageSync("user_info");
      this.shoppingCartCountFun();
    });
  },

  async onShow() {
    await this.seckillInfoGet();
    this.enterTime = Date.now();
    this.handleSenGoods("GoodsDetailPageView");
  },

  onHide() {
    this.handleHideView();
  },

  onUnload() {
    console.log("=======onUnload=========");
    this.handleHideView();
  },

  onShareAppMessage: function () {
    let seckillInfo = this.seckillInfo;
    let title = seckillInfo.shareTitle;
    let imageUrl = this.shareImgSrc
      ? this.shareImgSrc
      : seckillInfo.picUrl + "-jpg_w360_q90";
    let path =
      "/pages/seckill/seckill-detail/index?seckillHallInfoId=" +
      this.seckillHallInfoId +
      "&source_module=分享";

    const userInfo = uni.getStorageSync("user_info");
    const userCode = userInfo ? "&sharer_user_code=" + userInfo.userCode : "";
    path = path + userCode;
    return {
      title: title,
      path: path,
      imageUrl: imageUrl,
      success: function (res) {
        if (res.errMsg == "shareAppMessage:ok") {
          console.log(res.errMsg);
        }
      },
      fail: function (res) {
        // 转发失败
      },
    };
  },
  methods: {
    handleHideView() {
      const seconds = (Date.now() - this.enterTime) / 1000;
      this.handleSenGoods("GoodsDetailPageLeave", {
        stay_duration: seconds,
      });
    },
    shareSuccess(e) {
      console.log("imgSrc:", e);
      this.shareImgSrc = e;
    },

    showModalCustomerService() {
      this.modalCustomerService = true;
    },

    hideModalCustomerService() {
      this.modalCustomerService = false;
    },

    //领劵
    showModalCoupon() {
      this.modalCoupon = true;
    },

    hideModalCoupon() {
      this.modalCoupon = false;
    },

    //购物车数量
    shoppingCartCountFun() {
      api.shoppingCartCount().then((res) => {
        this.shoppingCartCount = res.data; //设置TabBar购物车数量
        console.log("this.shoppingCartCount==>", this.shoppingCartCount);
        app.globalData.shoppingCartCount = this.shoppingCartCount + "";
      });
    },

    //去购物车
    gotoShop() {
      uni.navigateTo({
        url: "/pages/shop/shop-cart/index",
      });
    },

    //查询商品可用电子券
    couponInfoPage(spuId) {
      api
        .couponInfoPage({
          current: 1,
          size: 50,
          descs: "create_time",
          spuId: spuId,
        })
        .then((res) => {
          this.couponInfoList = res.data.records;
          setTimeout(() => {
            uni.hideLoading();
          }, 1000);
        });
    },

    //商品查询
    async goodsGet(id) {
      const res = await api.goodsGet(id);
      this.goodsSpus = res.data;
      if (res.data.freightTemplat) {
        this.seckillInfo.goodsSpu.freightTemplat = res.data.freightTemplat;
      }
    },

    //商品规格查询
    goodsSpecGet(spuId) {
      api
        .goodsSpecGet({
          spuId: spuId,
        })
        .then((res) => {
          res.data
            ? res.data.map((t) => {
                t.checked = "";
              })
            : [];
          this.goodsSpecData = res.data;
        });
    },

    showModalSku(e) {
      this.modalSku = true;
      this.modalSkuType = e.target.dataset.type
        ? e.target.dataset.type + ""
        : "";
    },

    //商品评价
    goodsAppraisesPage(id) {
      api
        .goodsAppraisesPage({
          current: 1,
          size: 3,
          descs: "create_time",
          spuId: id,
        })
        .then((res) => {
          this.goodsAppraises = res.data;
          console.log(
            "this.goodsAppraises",
            JSON.stringify(this.goodsAppraises),
          );
        });
    },

    //获取商品保障
    listEnsureBySpuId(spuId) {
      api
        .listEnsureBySpuId({
          spuId: spuId,
        })
        .then((res) => {
          this.ensureList = res.data;
          console.log("this.ensureList", this.ensureList);
        });
    },

    showModalService() {
      this.modalService = "show";
    },

    //基础服务弹框
    hideModalService() {
      this.modalService = "";
    },

    //查询秒杀详情
    async seckillInfoGet() {
      try {
        const res = await api.seckillInfoGet(this.seckillHallInfoId);
        this.seckKillError = false;
        console.log("seckillInfoGet", res.data);
        res.data.goodsSpu.freightTemplat = res.data.goodsSpu.freightTemplat
          ? res.data.goodsSpu.freightTemplat
          : {};
        // if (res.data.seckillHall) {
        // let hallHour = Number(res.data.seckillHall.hallTime);
        // let curDate = this.$moment().format("YYYY-MM-DD");
        // // 必须当前日期和当前时间相同才能进行秒杀
        // if (curDate == res.data.seckillHall.hallDate) {
        //   // 计算倒计时时间
        //   let curDateTime = new Date().getTime(); //当前时间
        //   if (hallHour == this.curHour) {
        //     let nextHour = hallHour + 1;
        //     let nextDateTime = res.data.seckillHall.hallDate + ' ' + nextHour + ':00:00';
        //     let timeTemp = this.$moment(nextDateTime).toDate();
        //     this.outTime = new Date(timeTemp).getTime() - curDateTime; //下一个整点时间
        //   } else if (hallHour > this.curHour) {
        //     //还未开始的也要启动倒计时，例如，当我59进到该详情页面时不能秒杀购买，但是在该页面等一分钟后该商品应该自动变为可以秒杀购买
        //     this.outTime = 0;
        //     let startDateTime = res.data.seckillHall.hallDate + ' ' + hallHour + ':00:00';
        //     let timeTemp = this.$moment(startDateTime).toDate();
        //     let nextCountDown = new Date(timeTemp).getTime() - curDateTime; //下一个整点时间
        //     this.nextCountDown = nextCountDown / 1000;
        //     var that = this;
        //     let timer = setInterval(function () { //启动倒计时
        //       that.nextCountDown--
        //       if (that.nextCountDown == 0) {
        //         //结束,刷新
        //         clearInterval(timer);
        //         that.seckillInfoGet();
        //       }
        //     }, 1000);
        //   }
        // }
        // }
        let specInfo = "";
        if (
          res.data &&
          res.data.goodsSku &&
          res.data.goodsSku.specs &&
          res.data.goodsSku.specs.length > 0
        ) {
          res.data.goodsSku.specs.forEach(function (specItem, index) {
            specInfo = specInfo + specItem.specValueName;
            if (res.data.goodsSku.specs.length != index + 1) {
              specInfo = specInfo + ";";
            }
          });
          this.specInfo = specInfo;
        }
        this.seckillInfo = res.data;
        console.error("=======秒杀信息=====", this.seckillInfo.goodsSpu.id);
        if (this.seckillInfo.goodsSpu.id) {
          this.listEnsureBySpuId(this.seckillInfo.goodsSpu.id);
          this.goodsSpecGet(this.seckillInfo.goodsSpu.id);
          this.goodsAppraisesPage(this.seckillInfo.goodsSpu.id);
          await this.goodsGet(this.seckillInfo.goodsSpu.id);
          this.couponInfoPage(this.seckillInfo.goodsSpu.id);
        }

        setTimeout(() => {
          this.canLoad = true;
          this.article_description = this.seckillInfo.goodsSpu
            ? this.seckillInfo.goodsSpu.description
            : "";
          //生成图片
          this.$refs.poster && this.$refs.poster.onCanvas();
        }, 300);
        // #ifdef H5
        this.curLocalUrl = util.setH5ShareUrl();
        // #endif
      } catch (error) {
        console.error(error);
        this.seckKillError = true;
      }
    },
    //立即秒杀
    toSeckillBuy(e) {
      let seckillInfo = this.seckillInfo;
      if (
        this.seckillInfo.seckillHall.remainSecond < 0 ||
        this.seckillInfo.seckillHall.beginSecond > 0
      ) {
        uni.showToast({
          title: "当前时间不能发起秒杀！",
          icon: "none",
        });
        return;
      }
      let goodsSpu = seckillInfo.goodsSpu;
      let goodsSku = seckillInfo.goodsSku;
      if (goodsSku && goodsSku.stock > 0) {
        /* 把参数信息异步存储到缓存当中 */
        if (this.seckillInfo.seckillNum >= this.seckillInfo.limitNum) {
          uni.showToast({
            title: "卖出数量已达限量",
            icon: "none",
          });
          return;
        }
        uni.setStorage({
          key: "param-orderConfirm",
          data: {
            source: 3,
            // shoppingCarItemIds: this.seckillHallInfoId
            shoppingCarItemIds: this.seckillInfo.id,
            marketId: seckillInfo.id,
            relationId: seckillInfo.seckillHall.id,
          },
          // [{
          // 	spuId: goodsSpu.id,
          // 	skuId: goodsSku.id,
          // 	quantity: 1,
          // 	specInfo: this.specInfo,
          // 	salesPrice: seckillInfo.seckillPrice,
          // 	spuName: goodsSpu.name,
          // 	picUrl: goodsSku.picUrl ? goodsSku.picUrl : goodsSpu.picUrls[0],
          // 	freightTemplat: goodsSpu.freightTemplat,
          // 	weight: goodsSku.weight,
          // 	volume: goodsSku.volume,
          // 	orderType: '3',
          // 	marketId: seckillInfo.id,
          // 	relationId: seckillInfo.seckillHall.id,
          // 	shopInfo: seckillInfo.shopInfo
          // }]
        });
        this.handleSenGoods("GoodsBuySoon");
        uni.navigateTo({
          url:
            "/pages/order/order-confirm/index?acitivityId=" +
            this.seckillHallInfoId,
        });
      } else {
        uni.showToast({
          title: "秒杀商品库存不足",
          icon: "none",
          duration: 2000,
        });
      }
    },
    //原价购买
    tobuy() {
      let seckillInfo = this.seckillInfo;
      let goodsSpu = seckillInfo.goodsSpu;
      let goodsSku = seckillInfo.goodsSku;
      let specInfo = this.specInfo;
      /* 把参数信息异步存储到缓存当中 */
      // console.log(goodsSku.stock, "库存");
      if (goodsSku && goodsSku.stock > 0 && goodsSpu) {
        uni.setStorage({
          key: "param-orderConfirm",
          data: {
            source: 2,
            shoppingCarItemIds: {
              spuId: goodsSpu.id,
              skuId: goodsSku.id,
              quantity: 1,
              specInfo: specInfo,
              // salesPrice: goodsSku.salesPrice,
              // spuName: goodsSpu.name,
              // picUrl: goodsSku.picUrl ? goodsSku.picUrl : goodsSpu.picUrls[0],
              // pointsDeductSwitch: goodsSpu.pointsDeductSwitch,
              // pointsDeductScale: goodsSpu.pointsDeductScale,
              // pointsDeductAmsount: goodsSpu.pointsDeductAmount,
              // pointsGiveSwitch: goodsSpu.pointsGiveSwitch,
              // pointsGiveNum: goodsSpu.pointsGiveNum,
              // freightTemplat: goodsSpu.freightTemplat,
              // weight: goodsSku.weight,
              // volume: goodsSku.volume,
              // shopInfo: seckillInfo.shopInfo
            },
          },
        });
        uni.navigateTo({
          url: "/pages/order/order-confirm/index",
        });
      } else {
        uni.showToast({
          title: "秒杀商品库存不足",
          icon: "none",
          duration: 2000,
        });
      }
    },
    // 简化后的 handleSenGoods 方法
    handleSenGoods(type, params = {}) {
      senTrack(type, this.getTrackParams(params));
    },
    // 抽取公共的埋点参数构造方法
    getTrackParams(extraParams = {}) {
      const seckillInfo = this.seckillInfo || {};
      const promotion_type_list = [];
      return {
        forward_source: getCurrentTitle(1),
        source_module: decodeURIComponent(this.source_module),
        activity_id: this.seckillHallInfoId,
        goods_crossed_price: Number(seckillInfo.seckillPrice),
        goods_strike_price: Number(seckillInfo.goodsSku.salesPrice),
        promotion_type_list,
        is_recommend_goods: false,
        is_flash_sale: true,
        goods_id: this.goodsSpus.id,
        goods_name: this.goodsSpus.name,
        store_id: this.goodsSpus.shopCode,
        goods_status: this.goodsSpus.inventoryState == 1 ? "售罄" : "有货",
        LCID: this.goodsSpus.floor,
        PLID: this.goodsSpus.categoryId,
        goods_brand_id: this.goodsSpus.brand || "",
        shoppe_id: this.goodsSpus.cabinetGroup || "",
        vender_id: this.goodsSpus.shopId,
        is_pre_sale: this.goodsSpus.isPresale == 1 ? true : false,
        goods_sale: this.goodsSpus.saleNum,
        ...extraParams,
      };
    },

    async shareShowFun() {
      // #ifdef H5
      this.curLocalUrl = util.setH5ShareUrl();
      // #endif
      // #ifdef APP-PLUS
      this.curLocalUrl = util.setAppPlusShareUrl();
      // #endif
      let desc = "长按识别小程序码";
      let shareImg = this.seckillInfo.picUrl;
      // #ifdef H5 || APP-PLUS
      desc = "长按识别二维码";
      // h5的海报分享的图片有的有跨域问题，所以统一转成base64的
      // 之所以不在组件里面转换是因为无法区分哪张image图片需要处理，一般处理主图
      shareImg = await util.imgUrlToBase64(shareImg);
      // #endif
      //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
      let posterConfig = {
        width: 750,
        height: 1280,
        backgroundColor: "#fff",
        debug: false,
        blocks: [
          {
            width: 690,
            height: 808,
            x: 30,
            y: 183,
            borderWidth: 2,
            borderColor: "#f0c2a0",
            borderRadius: 20,
          },
          {
            width: 634,
            height: 74,
            x: 59,
            y: 770,
            backgroundColor: "#fff",
            opacity: 0.5,
            zIndex: 100,
          },
        ],
        texts: [
          {
            x: 30,
            y: 113,
            baseLine: "top",
            text: this.seckillInfo.shareTitle,
            fontSize: 38,
            color: "#080808",
          },
          {
            x: 92,
            y: 810,
            fontSize: 38,
            baseLine: "middle",
            text: this.seckillInfo.goodsSpu.name,
            width: 570,
            lineNum: 1,
            color: "#080808",
            zIndex: 200,
          },
          {
            x: 59,
            y: 895,
            baseLine: "middle",
            text: [
              {
                text: "秒杀价",
                fontSize: 28,
                color: "#ec1731",
              },
              {
                text: "¥" + this.seckillInfo.seckillPrice,
                fontSize: 36,
                color: "#ec1731",
                marginLeft: 30,
              },
            ],
          },
          {
            x: 522,
            y: 895,
            baseLine: "middle",
            text: "零售价 ¥" + this.seckillInfo.goodsSku.salesPrice,
            fontSize: 28,
            color: "#929292",
          },
          {
            x: 59,
            y: 945,
            baseLine: "middle",
            text: [
              {
                text: this.seckillInfo.goodsSpu.sellPoint,
                fontSize: 28,
                color: "#929292",
                width: 570,
                lineNum: 1,
              },
            ],
          },
          {
            x: 360,
            y: 1065,
            baseLine: "top",
            text: desc,
            fontSize: 38,
            color: "#080808",
          },
          {
            x: 360,
            y: 1123,
            baseLine: "top",
            text: "限时秒杀，抢到就是赚到！",
            fontSize: 28,
            color: "#929292",
          },
        ],
        images: [
          {
            width: 634,
            height: 634,
            x: 59,
            y: 210,
            url: shareImg,
          },
          {
            width: 230,
            height: 230,
            x: 92,
            y: 1020,
            url: null,
            qrCodeName: "qrCodeName", // 二维码唯一区分标识
          },
        ],
      };
      let userInfo = uni.getStorageSync("user_info");
      console.log("-----");
      if (userInfo && userInfo.headimgUrl) {
        //如果有头像则显示
        posterConfig.images.push({
          width: 62,
          height: 62,
          x: 30,
          y: 30,
          borderRadius: 62,
          url: userInfo.headimgUrl,
        });
        posterConfig.texts.push({
          x: 113,
          y: 61,
          baseLine: "middle",
          text: userInfo.nickName,
          fontSize: 32,
          color: "#8d8d8d",
        });
      }
      // let path = 'pages/seckill/seckill-detail/index?seckillHallInfoId=' + this.seckillHallInfoId;
      // this.curLocalUrl = '&seckillHallInfoId=' + this.seckillHallInfoId;
      this.shareParams = {
        title: this.seckillInfo.shareTitle,
        desc: this.seckillInfo.goodsSpu.name,
        imgUrl: this.seckillInfo.picUrl,
        url: this.curLocalUrl,
        scene: this.seckillHallInfoId,
        page: "pages/seckill/seckill-detail/index",
        posterConfig: posterConfig,
        trackParams: this.getTrackParams({
          click_content: "分享",
          page_name: getCurrentTitle(0),
        }),
      };
      this.showShare = true;
      console.log("showShare", this.showShare);
      this.handleSenGoods("GoodsShareOrCollect", {
        click_content: "分享",
        page_name: getCurrentTitle(0),
      });
    },
    ruleShow() {
      this.modalRule = "show";
    },
    ruleHide() {
      this.modalRule = "";
    },
    countDownDone() {
      this.seckillInfoGet();
    },
  },
};
</script>
<style scoped lang="scss">
// 货品名称部分
.goodsNameCon {
  margin: 20rpx;
  border-radius: 20rpx;
}

// 商品选择部分
.goodsChoice {
  margin: 20rpx;
  padding: 20rpx 0 40rpx;
  border-radius: 20rpx;

  .colorClass {
    display: flex;
    align-items: center;
    width: 100%;

    image {
      width: 12%;
      height: 120rpx;
      margin-right: 8rpx;
    }
  }
}

.label_goodsChoice {
  width: 70rpx;
  font-weight: bold;
  color: #000;
}

// 商品评价部分
.goodsEvaluate {
  margin: 20rpx;
  padding: 20rpx 0 40rpx;
  border-radius: 20rpx;
}

.myBar {
  min-height: auto !important;
}

.myBar1 {
  min-height: 50rpx !important;
}

.myBar1 .content {
  font-size: 28rpx;
}

.storeBgImgUrl,
.storeBgImgUrl image {
  width: 100%;
  height: 97rpx;
}

.product-bg {
  width: 100%;
  position: relative;
}

.product-bg swiper {
  width: 100%;
  height: calc(100vw);
  position: relative;
}

.product-bg video {
  width: 100%;
  height: calc(100vw);
  position: relative;
}

.product-bg .page-index {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
}

.cu-bar.tabbar.shop .action {
  width: unset;
}

.to-down {
  margin-bottom: 100rpx;
}

.coupon {
  // border-radius: 4rpx;
  // padding: 8rpx 20rpx 8rpx 20rpx;
  // background: radial-gradient(circle at bottom left, transparent 4px, #f9e7d4 0) top left,
  //             radial-gradient(circle at bottom right, transparent 4px, #f9e7d4 0) top right,
  //             radial-gradient(circle at top left, transparent 4px, #f9e7d4 0) bottom left,
  //             radial-gradient(circle at top right, transparent 4px, #f9e7d4 0) bottom right;
  // background-repeat: no-repeat;
  // background-size: 51% 51%;
  padding: 8rpx 20rpx;
  background: #fff6f5;
  border-radius: 10rpx;
}

.discount-tag {
  background-color: #fff;
  text-align: center;
  font-size: 15rpx;
  padding: 5rpx;
  color: #ff4c4f;
  padding: 0 10rpx;
}

.transparent {
  color: transparent;
}

.seckill-image {
  background-color: #fff;
  width: 100%;
  height: calc(100vw);
}

.show-bg {
  height: 84%;
  margin-top: 120rpx;
}

.image-box {
  height: 90%;
}

.show-btn {
  margin-top: -130rpx;
}
</style>
