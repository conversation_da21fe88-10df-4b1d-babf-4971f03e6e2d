import { requestApi as request } from '@/utils/api.js'

const authentication = {
  "authType": "2",
}
const welfare = {
  "act": "BENEFIT-BASE",
  "channel": "SONGSHU",
  "store": "288",
}

const addWelfare = {
  "act": "BENEFIT-FULL",
  "channel": "SONGSHU",
  "store": "288",
}

const channel = {
  "channel": "SONGSHU",
}

//认证会员信息
export function authInfo(query = {}) {
  return request({
    url: '/sco-customer-api/rest?method=sco.customer.auth',
    method: 'post',
    data: {
      ...query,
      ...authentication,
      ...channel
    }
  })
}

// 会员福利激励
export function getWelfare(query = {}) {
  return request({
    url: '/sco-customer-api/rest?method=sco.cust.activity.realtime',
    method: 'post',
    data: {
      ...query,
      ...welfare
    }
  })
}

// 添加会员信息
export function add(query = {}) {
  return request({
    url: '/sco-customer-api/rest?method=sco.customer.update',
    method: 'post',
    data: {
      ...query,
      ...channel
    }
  })
}

// 完善会员信息
export function addInfo(query = {}) {
  return request({
    url: '/sco-customer-api/rest?method=sco.cust.activity.realtime',
    method: 'post',
    data: {
      ...query,
      ...addWelfare
    }
  })
}