<template>
	<view style="min-height: 100vh;">
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">完善信息得好礼</block>
		</cu-custom>

		<view class="relative">
			<!-- 背景 -->
			<image
				:src="iconPic.bg | formatImg750"
				mode="widthFix"
				class="margin-bottom"
				style="width: 100%; height: 322rpx;"
			>
			</image>

			<view class="text-center statement-box">
				<!-- 公司log -->
				<image
					:src="iconPic.log"
					mode="widthFix"
					style="width: 400rpx; height: 36rpx"
				>
				</image>
				<!-- 申明 -->
				<view class="statement">
					<text class="text-xss" style="color: #B0723F;">
						郑重声明：为了更好的为您服务，请放心输入您的信息，松雷商业不会将其透露给第三方，我们也会无条件配合监管部门的工作。
					</text>
				</view>

				<view class="text-center">
					<!-- 总共积分图 -->
					<image
							:src="iconPic.banner"
							mode="widthFix"
							style="height: 274rpx;"
						>
					</image>
					<!-- 提交的表单 -->
						<form @submit="userAddressSave" class="relative">
							<view class="user-info">
								<view 
									class="cu-form-group  group-item" 
									style="background-color: unset;border-top: white"
									>
									<view class="title">姓名</view>
									<input 
										class="input-style"
										placeholder="请填写姓名"
										placeholder-style="color:#000000"
										:style="{color:authUserAddress&&authUserAddress.custName?'#C3C3C3':'#000000'}"
										maxlength="10"
										@input="onKeyInput"
										:disabled="authUserAddress&&authUserAddress.custName?true:false"
										:value="userAddress.custName">
									<view class="flex">
										<view class="add" :style="{color:authUserAddress&&authUserAddress.custName?'#C3C3C3':'#FF0000'}">
											+
										</view>
										<view class="faceValue" :style="{color:authUserAddress&&authUserAddress.custName?'#C3C3C3':'#FF0000'}">
											100
										</view>
										<view class="integral" :style="{color:authUserAddress&&authUserAddress.custName?'#C3C3C3':'#000000'}">
											积分
										</view>
									</view>
								</view>

								<view 
									class="cu-form-group  group-item" 
									v-if="authUserAddress&&authUserAddress.custName"
									style="background-color: unset;border-top: white;min-height: 0rpx;"
									>
									<view style="color: #9E9E9E;font-size: 24rpx;padding-left: 98rpx;">已有该信息，不需要重新完善</view>
								</view>

								<view 
									class="cu-form-group  group-item" 
									style="background-color: unset;border-top: white"
									>
									<view class="title" style="height: 60rpx;line-height: 33rpx;padding-right: 0rpx;">
										<view style="width: 60rpx;">出生</view>
										<view style="width: 88rpx;">年月日</view>
									</view>
										<picker 
											class="input-style" 
											mode="date" 
											:style="{color:authUserAddress&&authUserAddress.birthDay?'#C3C3C3':'#000000'}"
											:disabled="authUserAddress&&authUserAddress.birthDay?true:false"
											@change="bindDateChange">
											<view style="width: 363rpx;">{{date}}</view>
										</picker>
									<view class="flex">
										<view class="add" :style="{color:authUserAddress&&authUserAddress.birthDay?'#C3C3C3':'#FF0000'}">
											+
										</view>
										<view class="faceValue" :style="{color:authUserAddress&&authUserAddress.birthDay?'#C3C3C3':'#FF0000'}">
											200
										</view>
										<view class="integral" :style="{color:authUserAddress&&authUserAddress.birthDay?'#C3C3C3':'#000000'}">
											积分
										</view>
									</view>
								</view>

								<view 
									v-if="authUserAddress&&authUserAddress.birthDay"
									class="cu-form-group  group-item" 
									style="background-color: unset;border-top: white;min-height: 0rpx;"
									>
									<view style="color: #9E9E9E;font-size: 24rpx;padding-left: 98rpx;">已有该信息，不需要重新完善</view>
								</view>

								<view 
									class="cu-form-group  group-item" 
									style="background-color: unset;border-top: white"
									>
									<view class="title">性别</view>

									<picker 
										class="input-style" 
										mode="selector" 
										:style="{color:authUserAddress&&authUserAddress.sex?'#C3C3C3':'#000000'}"
										@change="bindPickerChange" 
										range-key="name" 
										:disabled="authUserAddress&&authUserAddress.sex?true:false"
										:range="genderArray" >
										<view >{{array[index]}}</view>
									</picker>
									<view class="flex">
										<view class="add" :style="{color:authUserAddress&&authUserAddress.sex?'#C3C3C3':'#FF0000'}">
											+
										</view>
										<view class="faceValue" :style="{color:authUserAddress&&authUserAddress.sex?'#C3C3C3':'#FF0000'}">
											100
										</view>
										<view class="integral" :style="{color:authUserAddress&&authUserAddress.sex?'#C3C3C3':'#000000'}">
											积分
										</view>
									</view>
								</view>

								<view 
									v-if="authUserAddress&&authUserAddress.sex"
									class="cu-form-group  group-item" 
									style="background-color: unset;border-top: white;min-height: 0rpx;"
									>
									<view style="color: #9E9E9E;font-size: 24rpx;padding-left: 98rpx;">已有该信息，不需要重新完善</view>
								</view>

								<view 
									class="cu-form-group  group-item" 
									style="background-color: unset;border-top: white"
									>
									<view class="title">地址</view>
										<textarea
											placeholder="请填写详细地址"
											placeholder-style="color:#000000"
											:disabled="authUserAddress&&authUserAddress.addr?true:false"
											:style="{color:authUserAddress&&authUserAddress.addr?'#C3C3C3':'#000000'}"
											style="
												width: 363rpx;
												height: 183rpx;
												border: 1px solid #C3C3C3;
												border-radius: 27rpx;
												margin: 10rpx 0 10rpx;
												padding-left: 10rpx;
												text-align: left;
												"
											maxlength="200"
											v-model="userAddress.addr"
											:data-index="index"
										></textarea>
									<view class="flex">
										<view class="add" :style="{color:authUserAddress&&authUserAddress.addr?'#C3C3C3':'#FF0000'}">
											+
										</view>
										<view class="faceValue" :style="{color:authUserAddress&&authUserAddress.addr?'#C3C3C3':'#FF0000'}">
											200
										</view>
										<view class="integral" :style="{color:authUserAddress&&authUserAddress.addr?'#C3C3C3':'#000000'}">
											积分
										</view>
									</view>
								</view>

								<view 
									v-if="authUserAddress&&authUserAddress.addr"
									class="cu-form-group  group-item" 
									style="background-color: unset;border-top: white;min-height: 0rpx;"
									>
									<view style="color: #9E9E9E;font-size: 24rpx;padding-left: 98rpx;">已有该信息，不需要重新完善</view>
								</view>

								<view class="compile">
									<button 
										style="
											width: 513rpx;
											height: 80rpx;
											background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%); 
											font-size: 26rpx;"
										class="cu-btn block margin-sm round bottom-btn"
										:class="'bg-'+theme.themeColor" formType="submit">提交即可得</button>
								</view>
							</view>
						</form>
				</view>
			</view>
			
			<!-- 赠送的积分弹框 -->
			<view 
				class="cu-modal" 
				:class="changeModalDialog?'show':''" 
				>
					<view 
						class="cu-dialog" 
						:class="changeModalDialog?'animation-slide-center':''" 
						style="width: 657rpx; border-radius: 45rpx;"
						@tap.stop
					>
					<view class="relative"  v-if="preferentialList">
						<!--单条或者双条优惠背景-->
						<image
								v-if="preferentialList.length==1"
								style="width:100%;height: 902rpx;border-radius:15rpx;"
								:src="iconPic.modal1" 
								mode="aspectFill" 
								>
							</image>

							<image
								v-else
								style="width:100%;height: 980rpx;border-radius:15rpx;"
								:src="iconPic.modal2" 
								mode="aspectFill" 
								>
							</image>

							<!--单条或者双条数据-->
							<view 
									v-if="preferentialList.length==1"
									style="
									position: absolute;
									top: 410rpx;
									left: -132rpx;
									bottom: 0rpx;
									right: 0rpx;">
								<view
									style="
									font-size: 120rpx;
									font-weight: bold;
									padding-left: 90rpx;
									color: #FF4146;
									margin-bottom: 125rpx;">
									<view	style="
										font-size: 30rpx;
										pointer-events:none;
										position: absolute;
										top: 30rpx;
										left: 0;
										right: 220rpx;
										bottom: 0;"
										v-if="preferentialList[0].groupId=='01'"
										>
										￥</view>
										{{preferentialList[0].balance || ''}}
										<view class="text-sxl" style="color: #864C1E;display: inline-block;">
											<view>
												{{preferentialList[0].typeName || ''}}
											</view>
											<view style="color: #494949;font-size: 25rpx;height: 34rpx;">
												{{(preferentialList[0].simpleUsageRule || '')}}
											</view>
										</view>
								</view>
								<view
									@tap="hideModalDialog" 
									style="
									font-size: 36rpx;
									font-weight: bold;
									padding-left: 120rpx;
									color: #864C1E;"
								>获取更多优惠</view>
							</view>

							<view 
								v-else
								style="
									position: absolute;
									top: 328rpx;
									left: -132rpx;
									bottom: 0rpx;
									right: 0rpx;">
								<view 
									style="
									font-size: 120rpx;
									font-weight: bold;
									padding-left: 100rpx;
									color: #FF4146;">
									{{preferentialList[0].balance || ''}}
										<view class="text-sxl" style="color: #864C1E;display: inline-block;">
											<view>
												{{preferentialList[0].typeName || ''}}
											</view>
											<view style="color: #494949;font-size: 25rpx;height: 34rpx;">
												{{(preferentialList[0].simpleUsageRule || '')}}
											</view>
										</view>
								</view>
								<view
									style="
									font-size: 120rpx;
									font-weight: bold;
									padding-left: 90rpx;
									color: #FF4146;
									margin-bottom: 125rpx;">
									<view	style="
										font-size: 30rpx;
										pointer-events:none;
										position: absolute;
										top: 225rpx;
										left: 0;
										right: 220rpx;
										bottom: 0;"
										>
										￥</view>
										{{preferentialList[1].balance || ''}}
										<view class="text-sxl" style="color: #864C1E;display: inline-block;">
											<view>
												{{preferentialList[1].typeName || ''}}
											</view>
											<view style="color: #494949;font-size: 25rpx;height: 34rpx;">
												{{(preferentialList[1].simpleUsageRule || '')}}
											</view>
										</view>
								</view>
								<view
									@tap="hideModalDialog" 
									style="
									font-size: 36rpx;
									font-weight: bold;
									padding-left: 120rpx;
									color: #864C1E;"
								>获取更多优惠</view>
							</view>

						</view>
					</view>
			</view>

			<!-- 提交弹框 -->
			<view 
				class="cu-modal" 
				:class="submitModalDialog?'show':''" 
				>
					<view 
						class="cu-dialog" 
						:class="submitModalDialog?'animation-slide-center':''" 
						style="width: 576rpx; border-radius: 45rpx;"
						@tap.stop
					>
						<view class="bg-white padding-bottom-xs">
								<view class="padding text-center text-black text-lg"
									style="
										width: 451rpx;
										font-weight: 800;
										border-bottom: 1px dashed #000;
										margin: 0 auto;">
									温馨提示
								</view>
								<view class="padding text-center text-black text-df"
									style="
										margin: 0 auto;
										font-weight: 500;">
									{{msg?msg:'礼品已经存入您的账户'}}
								</view>
								<!--关闭去首页，查看去积分详情或者劵-->
								<view class="flex justify-evenly padding-top-xs margin-bottom-lg">
									<view 
										style="
										width: 220rpx;
										height: 66rpx;
										line-height: 66rpx;
										color: #FFFFFF;
										background: linear-gradient(-90deg, #CCAC92 0%, #C09979 0%, #D8BBA3 100%);
										border-radius: 45rpx;"
										@click="toPage('/pages/home/<USER>')"
										>关闭</view>

									<view
										style="
										width: 220rpx;
										height: 66rpx;
										line-height: 66rpx;
										color: #FFFFFF;
										background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
										border-radius: 45rpx;"
										@click="toPage('/pages/user/user-points-record/index')"
										>去查看</view>
								</view>
						</view>
					</view>
			</view>
		</view>
	</view>

</template>

<script>
	const app = getApp();
	import util from 'utils/util'
	import {authInfo,getWelfare,add,addInfo } from '@/pages/perfect/api/perfect'
	import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"

	export default {
		mixins: [navigateUtil],
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				userAddress:{
					custName:'',
					birthDay:'',
					sex:'',
					addr:'',
				},
				array:["请选择性别","男",'女'],
				index: 0,
				genderArray: [
					{ value: '', name: '请选择性别' },
					{ value: 'M', name: '男' },
					{ value: 'F', name: '女' }
				],
				gender: '',
				date:'请选择日期',
				timeIndex:0,
				anyDate:'自定义日期',
				iconPic:{
					bg:"http://img.songlei.com/perfect/bg.png",
					log:"http://img.songlei.com/perfect/log.png",
					banner:"http://img.songlei.com/perfect/banner.png",
					modal1:"http://img.songlei.com/perfect/modal1.png",
					modal2:"http://img.songlei.com/perfect/modal2.png",
				},
				changeModalDialog:false,//奖励弹框
				submitModalDialog:false,//提交弹框
				custId:'',//会员ID
				inviter:'',//会员等级
				preferentialList:[],//优惠所有数据
				authUserAddress:{},//认证查询的数据
				msg:'',//错误提示
			};
		},

		props: {},

		onLoad(options) {
			app.initPage().then(res => {
				// this.getInitDate()
				if (!util.isUserLogin()) {
					uni.navigateTo({
						url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/perfect/user-info/index')
					})
					return
				} else {
					this.getAuthenticationInfo()
				}
			});
		},
	
		methods: {
			// 获取姓名
			onKeyInput: function(event) {
          this.userAddress.custName = event.target.value
      },

			// 对比用户信息
			compareAddress(userAddress, authUserAddress) {
				let propertyOrder = ['custName', 'birthDay', 'sex', 'addr'];
				let inviter = propertyOrder.filter(function(key) {
					return userAddress[key] !== authUserAddress[key];
				}).join(',');
				
				return inviter;
			},
			
			//获取会员基础福利
			async getWelfare(){
				try {
					
					let params = {
						inviter:this.inviter,
						custId:this.custId
					};
					const res = await getWelfare(params);
      		const {data} = {...res};
					console.log('===data=====>',data);
					this.preferentialList = data
					// this.preferentialList=[
					// 	{
					// 		accntNo: "202312005296144981balance: 68c™颠坊芭顽憾牡彩哀 艾大癌鞍猜槛",
					// 		balance: 10,
					// 		couponPicUrl: "https://img.songlei.com/1/material/145de6b8-0950-4fe6-a780-9c2cb9399705.png",
					// 		custId: "**********",
					// 		detailUsageDesc:"1、本券为松雷南岗店邀请函专用礼券;42、本券可在“1F化妆品区”使用;e3、本券可在收",
					// 		detailUsageRule:"松雷南岗店线下使用",
					// 		effDate: "2023-12-15 00:08:00",
					// 		expDate: "2023-12-15 00:00:00",
					// 		faceMode:"2",
					// 		faceValue: 68,groupId: "02",media: "2",
					// 		qrcode: "120418553650393",
					// 		simpleUsageRule:"邀请函邀",
					// 		typeId:"MZ2023120141",
					// 		typeName:"美妆券",
					// 		useType: "05",
					// 		useTypeName:"满减券"
					// 	},
					// 	{
					// 		accntNo: "202312005296144981balance: 68c™颠坊芭顽憾牡彩哀 艾大癌鞍猜槛",
					// 		balance: 100,
					// 		couponPicUrl: "https://img.songlei.com/1/material/145de6b8-0950-4fe6-a780-9c2cb9399705.png",
					// 		custId: "**********",
					// 		detailUsageDesc:"1、本券为松雷南岗店邀请函专用礼券;42、本券可在“1F化妆品区”使用;e3、本券可在收",
					// 		detailUsageRule:"松雷南岗店线下使用",
					// 		effDate: "2023-12-15 00:08:00",
					// 		expDate: "2023-12-15 00:00:00",
					// 		faceMode:"2",
					// 		faceValue: 68,groupId: "01",media: "2",
					// 		qrcode: "120418553650393",
					// 		simpleUsageRule:"邀请函",
					// 		typeId:"MZ2023120141",
					// 		typeName:"积分",
					// 		useType: "05",
					// 		useTypeName:"满减券"
					// 	}
					// ]
					this.preferentialList.sort(function(a, b) {
						if (a.groupId === "01") {
							return -1; // a排在b前面
						} else if (b.groupId === "01") {
							return 1; // b排在a前面
						} else {
							return 0; // 保持原有顺序
						}
					});
					if (this.preferentialList&&this.preferentialList.length) {
						this.changeModalDialog = true
					}
					console.log('getWelfare========>',this.preferentialList);
				} catch (error) {
					console.log("error",error);
				}
			},

			//获取会员认证信息
			async getAuthenticationInfo(){
				try {
					const userInfo = uni.getStorageSync('user_info')
					let params = {authKey:userInfo.phone};
					const res = await authInfo(params);
      		const {data} = {...res};
					this.custId = data.custId
					this.inviter = data.custType
					this.userAddress.custName = data.custmbmer.custName ||'';
					if (data.custmbmer.birthDay) {
						this.userAddress.birthDay = data.custmbmer.birthDay.replace("-", "年").replace("-", "月") + "日";
						this.authUserAddress.birthDay = data.custmbmer.birthDay.replace("-", "年").replace("-", "月") + "日";
						this.date = data.custmbmer.birthDay.replace("-", "年").replace("-", "月") + "日";
					}else{
						this.authUserAddress.birthDay = data.custmbmer.birthDay;
						// this.getInitDate()
					}
					this.userAddress.sex = data.custmbmer.sex ||'';
					this.index = data.custmbmer.sex?(data.custmbmer.sex==='M'?1:2):0;
					this.userAddress.addr = data.custmbmer.addr ||'';
					this.authUserAddress.addr = data.custmbmer.addr ||'';
					this.authUserAddress.custName = data.custmbmer.custName ||'';
					this.authUserAddress.sex = data.custmbmer.sex;
					console.log('data=====>',data);
					this.getWelfare()
				} catch (error) {
					console.log("error",error);
				}
			},

			 //关闭弹框
			hideModalDialog() {
				this.changeModalDialog = false
			},

			// 性别
			bindPickerChange: function(e) {
          console.log('picker发送选择改变，携带值为', e.detail.value)
          this.index = e.detail.value;
					const index = e.detail.value;
					this.gender = this.genderArray[index].name;
					this.userAddress.sex = this.genderArray[index].value
				console.log('this.gender',this.gender,this.genderArray[index].value);
      },

			//获取当前月份
			getInitDate () {
      var date = new Date()
      var year = date.getFullYear()
      var month = date.getMonth() + 1
			let day = date.getDate();
      if (month / 10 < 1) month = '0' + month
      this.date = year + '年' + month + '月' + day + '日'
      this.userAddress.birthDay = `${year}年-${month}月-${day}日`;
    },

		//修改年月
    bindDateChange (e) {
			this.getInitDate()
      let dateStr = e.target.value;
			this.date = dateStr.replace("-", "年").replace("-", "月") + "日";
      let reqDate = e.target.value.replace("年", '-');
      let month = reqDate.replace("月", '-');
      this.userAddress.birthDay = month.replace("日", '');
      console.log("date", this.date, this.userAddress.birthDay);
    },

		// 提交
			userAddressSave(e) {
				let value = e.detail.value;
				console.log("this.userAddress===>", this.userAddress,value)
				if (!this.userAddress.custName) {
					uni.showToast({
						title: '请输入姓名!',
						icon: 'none'
					})
				} else if (!this.userAddress.sex) {
					uni.showToast({
						title: '请选择性别!',
						icon: 'none'
					})
				} else if (!this.userAddress.birthDay) {
					uni.showToast({
						title: '请选择出生年月日!',
						icon: 'none'
					})
				} else if (!this.userAddress.addr) {
					uni.showToast({
						title: '请填写详细地址!',
						icon: 'none'
					})
				} else{
					let data = {
						custId: this.custId,
						custName: this.userAddress.custName,
						birthDay: this.userAddress.birthDay.replace(/年|月/g, "-").replace(/日/g, ""),
						sex:this.userAddress.sex,
						addr: this.userAddress.addr,
					}
					console.log("userAddressSave====data====",data);
					let inviter = this.compareAddress(this.authUserAddress,this.userAddress)
					if (inviter) {
						add(data).then(res => {
							if (res.code==0) {
								//完善会员信息
								addInfo({
									custId: this.custId,
									inviter: inviter
								}).then(res => {
									if (res.code==0) {
										//如果有礼品可领取
										this.submitModalDialog = true
									}
								})
							}
						});
					}else{
						this.submitModalDialog = true
						this.msg = "没有可修改的资料"
					}
				}
			}
		}
	};
</script>

<style scoped >
.cu-form-group picker::after {
    font-family: cuIcon;
    display: block;
    content: "";
    position: absolute;
    font-size: 34rpx;
    color: #8799a3;
    line-height: 100rpx;
    width: 60rpx;
    text-align: center;
    top: 0;
    bottom: 0;
    right: -20rpx;
    margin: auto;
    margin-top: auto;
    margin-right: auto;
    margin-bottom: auto;
    margin-left: auto;
}
.integral{
	width: 26rpx;
	height: 52rpx;
	margin-top: 25rpx;
	line-height: 26rpx;
	font-size: 26rpx;
	margin-left: 10rpx;
}
.faceValue{
	color: #FF0000;
	font-weight: bold;
	font-size: 60rpx;
}
.add{
	color: #FF0000;
	font-size: 60rpx;
	margin-left: 10rpx;
}
.input-style{
		width: 363rpx;
		padding-left: 20rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: #FFFFFF;
    border: 1px solid #C3C3C3;
    border-radius: 27rpx;
    text-align: left;
}
	.user-info{
		margin: -40rpx 36rpx;
		background: #FFFFFF;
		border-radius: 38rpx 38rpx 38rpx 50rpx;
		padding: 40rpx 0rpx 58rpx;
	}
	.statement-box{
		position: absolute;
		top: 20rpx;
		left: 0;
		right: 0;
		bottom: 0;
	}
	.statement {
		margin: 16rpx auto;
		width: 688rpx;
		height: 148rpx;
		padding: 24rpx 25rpx;
		background: #FBF6F2;
		border-radius: 19rpx;
		text-align: left;
	}
	.bottom-btn {
		margin: auto;
		width: 96%;
		height: 88rpx;
		margin-bottom: 20rpx;
	}

	.compile {
		margin-top: 60rpx;
	}

	.permissions_box {
		padding: 200rpx 30rpx 50rpx;
		background-color: #fff;
		color: #000000;
	}
</style>