<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" simple :pageTo="pageTo" :hideMarchContent="false">
			<view slot="marchContent">
				<view class="search-form round" :style="{
						height: computedHeight,
						lineHeight: '100%',
						marginLeft: ' 0',
						border: ' 1rpx solid #CDA488'
					}">
					<text class="cuIcon-search text-xl" style="color: #a1a1a1"></text>
					<input :placeholder-style="{
							'font-size': isPhone ? '28rpx' : '18rpx'
						}" v-model="searchKeyword" type="text" :placeholder="placeholder" confirm-type="search" @confirm="searchHandle"
						class="text-df" focus
						:style="{ width: `${750 - menuWidth * pixelRatio - (leftMenuWidth / 2) * pixelRatio - searchKeywordWidth}rpx`, paddingRight: `0rpx` }" />
					<text v-if="searchKeyword" @click="delSearchKeyword" class="cuIcon-roundclosefill"
						style="font-size: 30rpx; color: #a1a1a1"></text>
					<text @click="searchClick(placeholder)" class="overflow-1 text-df padding-right-sm"
						:class="'text-' + theme.themeColor">
						{{ shopId ? '搜索本店' : '搜索' }}
					</text>
				</view>
			</view>
		</cu-custom>
		<view class="bg-white padding-lg" :class="showPopup ? 'margin-top padding-top-xl' : ' '"
			style="border-radius: 40rpx">
			<view v-show="showPopup">
				<view v-if="searchHistory.length > 0">
					<view class="flex justify-between">
						<view class="action text-bold text-df-px text-black">
							<text class="cuIcon-time padding-right-sm"></text>
							历史搜索
						</view>
						<view class="action">
							<text class="cuIcon-delete text-gray text-df-px" @tap="clearSearchHistory"></text>
						</view>
					</view>
					<view class="padding-top-sm padding-bottom-xl flex flex-wrap">
						<block v-for="(item, index) in searchHistory" :key="index">
							<view class="padding-xs" v-if="item.name">
								<view class="cu-tag round text-df-px text-gray"
									style="background-color: transparent; border: solid 1rpx #eee" :style="{
										height: isPhone ? '48rpx' : '29rpx',
										padding: isPhone ? '0 14rpx' : '0 8rpx'
									}" @tap="searchHandle" :data-name="item.name" data-type="历史搜索">
									{{ item.name }}
								</view>
							</view>
						</block>
					</view>
				</view>

				<view v-if="searchHot.length > 0 && eye">
					<view class="flex justify-between text-bold text-df-px text-black">
						<view class="action">
							<text class="cuIcon-search padding-right-sm"></text>
							搜索发现
						</view>
						<view class="action">
							<text class="cuIcon-attentionfill lg text-gray" @tap="isEye"></text>
						</view>
					</view>

					<view class="padding-top-sm flex flex-wrap">
						<block v-for="(item, index) in searchHot" :key="index">
							<view class="padding-xs" v-if="item.name">
								<view class="cu-tag round text-df-px text-gray"
									style="background-color: transparent; border: solid 1rpx #eee" :style="{
										height: isPhone ? '48rpx' : '29rpx',
										padding: isPhone ? '0 14rpx' : '0 8rpx'
									}" @tap="searchHandle" :data-name="item.name" data-type="搜索发现">
									{{ item.name }}
								</view>
							</view>
						</block>
					</view>
				</view>

				<view v-if="goodsList" class="padding-top-xl padding-bottom-xl">
					<view class="flex justify-between">
						<view class="action text-orange text-bold text-df-px">
							<text class="cuIcon-hot padding-right-sm"></text>
							全网热榜
						</view>
					</view>
					<view class="cu-list menu sm-border margin-tb-sm">
						<view class="cu-item flex padding-top padding-bottom"
							style="min-height: auto; line-height: 130%" v-for="(item, index) in goodsList" :key="index"
							>
							<view @click="handleHotItem(item,index)" class="flex padding-top padding-bottom"
								:data-name="item.name" style="line-height: 130%">
								<text class="text-df-px text-orange padding-right-sm">{{ index + 1 }}.</text>
								<text class="text-df-px overflow-1 text-black">{{ item.name }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 搜索列表 -->
			<view v-show="!showPopup">
				<view class="cu-list menu sm-border search-list">
					<!-- 热门店铺 -->
					<view v-if="shopList">
						<view class="Micro_page padding-sm flex align-center" v-for="(item, index) in shopList"
							:key="index">
							<text class="cuIcon-shop micro-icon position-icon padding-right-sm"></text>
							<view class="shop-name padding-left-sm">
								<rich-text class="text-df" @click="toPages(item.name, 1, item.id)"
									:nodes="item.name"></rich-text>
							</view>
						</view>
					</view>

					<!-- 跳转页面 -->
					<view v-if="searchNavigationList.length">
						<view class="Micro_page flex align-center" v-for="(item, index) in searchNavigationList"
							:key="index">
							<text class="cuIcon-tag micro-icon"></text>
							<view class="text paddding-left-sm" @click="handleToPage(item.url)">
								<rich-text class="text-df" :nodes="item.words"></rich-text>
							</view>
							<!-- <view v-else-if="item.url == 'CustomerService'" class="text paddding-left-sm" @click="handleCustomerService">
								<rich-text class="text-df" :nodes="item.words"></rich-text>
							</view> -->
						</view>
					</view>

					<!-- 微页面 -->
					<view v-if="pageDeviseList">
						<view class="Micro_page" v-for="(item, index) in pageDeviseList" :key="index">
							<text class="cuIcon-tag micro-icon"></text>
							<navigator hover-class="none" class="text" :url="'/pages/micro-page/index?id=' + item.id">
								<rich-text class="text-df" :nodes="item.name"></rich-text>
							</navigator>
						</view>
					</view>

					<!-- 商品列表 -->
					<view class="cu-item" style="min-height: auto" v-for="(item, index) in searchList" :key="index">
						<view class="goods padding-top padding-bottom" @click="toPages(item.name, 1)">
							<rich-text class="text-df" :nodes="item.name"></rich-text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	const util = require('utils/util.js');
	import api from 'utils/api';
	
	import {
		mapState
	} from 'vuex';
	import __config from '@/config/env';
	import {
		senTrack,
		senGoods
	} from "@/public/js_sdk/sensors/utils.js"
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				searchHistory: [], //搜索历史
				searchHot: [], //搜索发现
				goodsList: [], //商品列表
				searchList: [], //搜索列表
				shopList: [], //店铺列表
				pageDeviseList: [], //微页面列表
				searchKeyword: '',
				placeholder: '',
				searchRequestId: '', //下拉，底纹id
				shopId: '',
				eye: true,
				showPopup: true, //弹框显示
				pageTo: '', //记录页面
				shopType: '',
				//有数统计使用
				page_title: '搜索输入页',
				lastTime: null,
				searchNavigationList: [],
				keyword_type: '自定义' //自定义  历史搜索  搜索发现  
			};
		},
		computed: {
			searchKeywordWidth() {
				if (this.shopId) {
					if (this.searchKeyword) {
						return 310;
					}
					return 248;
				} else if (this.searchKeyword) {
					return 248;
				} else {
					return 182;
				}
			},

			...mapState(['windowWidth', 'HeightBar', 'CustomBar', 'menuWidth', 'leftMenuWidth', 'pixelRatio', 'isPhone',
				'multipleView'
			]),

			computedHeight() {
				if (this.HeightBar > 0) {
					return this.HeightBar + 'px';
				} else {
					return 'auto';
				}
			}
		},
		watch: {
			searchKeyword: {
				handler(val, oldVal) {
					//普通的watch监听
					console.log('val==> ' + val, 'oldVal==>', oldVal);
					if (val && !this.shopId) {
						if (this.searchKeyword && oldVal) {
							console.log('11111');
							this.showPopup = false;
							this.getSearchDownList(val);
							return;
						}
						// this.showPopup = true
						// if (oldVal) {
						this.showPopup = false;
						this.getSearchDownList(val);
						return;
						// }
					}
					this.showPopup = true;
				}
				// immediate: true
			}
		},

		onShow() {
			this.searchHistory = uni.getStorageSync('searchHistory') ? uni.getStorageSync('searchHistory') : [];
			this.placeholder = uni.getStorageSync('searchPlaceholderText') ? uni.getStorageSync('searchPlaceholderText') :
				'';
			this.getSearchHotList();
			// this.getSearchDownList()
			//获取当前页面路由
			let routes = getCurrentPages(); //获取当前页面栈
			let curRoute = routes[routes.length - 1].route; //获取当前页面的路由
			console.log('页面长度', routes.length);

			this.pageTo = curRoute;
			// let pages = getCurrentPages(); //页面对象
			// console.log("获取当前页面的路由==>", this.pageTo);
			// let prevpage = pages[pages.length - 2]; //上一个页面对象
			// console.log("上一个页面路由地址==>", prevpage.route) //上一个页面路由地址
			// let path = prevpage.route;
			// console.log('path===>', path)
			uni.setStorageSync('pageTo', curRoute);
		},

		onLoad(options) {
			if (options.shopId && options.shopId != 'null' && options.shopId != 'undefined') {
				this.shopId = options.shopId;
			}
			if (options.searchKeyword) {
				let val = decodeURIComponent(options.searchKeyword);
				this.searchKeyword = decodeURI(val);
				// this.getSearchDownList(decodeURI(val))
			}
			if (options.shopType && options.shopType == 1) {
				this.shopType = options.shopType;
			}
			app.initPage().then((res) => {
				this.goodsPage();
			});

			// #ifdef MP
			let {
				height
			} = uni.getMenuButtonBoundingClientRect();
			this.searchHeight = height;
			// #endif
		},

		methods: {
			//删除搜索内容
			delSearchKeyword() {
				this.searchKeyword = '';
			},

			//处理HTML标签
			removeHtml(val) {
				return val && val.replace(/<[^>]+>/g, '');
			},

			//下拉框跳转页
			goSearch(item, num) {
				let nowTime = new Date();
				if (!this.lastTime || nowTime - this.lastTime > 1500) {
					this.toPages(item, num);
					this.lastTime = nowTime;
				}
			},

			toPages(item, num, shopId) {
				if (num) {
					let name = this.removeHtml(item);
					//添加缓存
					this.searchHandle({
							detail: {
								value: name
							}
						},
						shopId
					);
				}
			},
			//去左右空格;
			trim(s) {
				return s && s.replace(/(^\s*)|(\s*$)/g, '');
			},
			// 设置搜索下拉显示高亮
			// 拼接
			join(str, key) {
				let reg = new RegExp(`(${key})`, 'gm');
				let nweReg = '<span style="color:#FD463E;font-weight:bold;">$1</span>';
				return str && str.replace(reg, nweReg);
			},

			//搜索下拉提示列表
			getSearchDownList(name) {
				api.searchDownList({
					hit: 10,
					query: name
				}).then((res) => {
					console.log('下拉返回所有结果==>', res.data);
					console.log('下拉提示商品列表==>', res.data.suggestions);
					if (res.data) {
						//搜索下拉id
						this.searchRequestId = res.data.request_id;
						let dataList = res.data.suggestions;
						let filterArr = [];
						// 过滤出符合条件的值
						dataList.forEach((item, index) => {
							if (item.suggestion.includes(this.searchKeyword)) {
								// filterArr.push(this.join(item.suggestion, this.searchKeyword));
								filterArr.push(
									Object.assign({
										name: this.join(item.suggestion, this.searchKeyword)
									})
								);
							} else {
								filterArr.push(
									Object.assign({
										name: item.suggestion
									})
								);
							}
						});
						this.searchList = filterArr;

						//下拉店铺列表
						if (res.data.shopList) {
							let shopList = res.data.shopList;
							let filterShopList = [];
							// 过滤出符合条件的值
							shopList.forEach((item, index) => {
								// if (item.name.includes(this.searchKeyword)) {
								filterShopList.push(
									Object.assign({
										name: this.join(item.name, this.searchKeyword),
										id: item.id
									})
								);
								// }
							});
							this.shopList = filterShopList;
							console.log('店铺列表', this.shopList);
						}

						//下拉微页面
						if (res.data.pageDeviseList) {
							let pageDeviseList = res.data.pageDeviseList;
							console.log('微页面', pageDeviseList);
							let filterPageDeviseList = [];
							// 过滤出符合条件的值
							pageDeviseList.forEach((item, index) => {
								filterPageDeviseList.push(
									Object.assign({
										name: this.join(item.pageName, this.searchKeyword),
										id: item.id
									})
								);
							});
							this.pageDeviseList = filterPageDeviseList;
						}

						//下拉跳转其他页面
						if (res.data.searchNavigationList) {
							let searchNavigationList = res.data.searchNavigationList;
							console.log('跳转到其他页面逻辑', searchNavigationList);
							const filterPageDeviseList = [];
							// 过滤出符合条件的值
							searchNavigationList.forEach((item, index) => {
								filterPageDeviseList.push(item);
							});
							this.searchNavigationList = filterPageDeviseList;
						}

						if (this.searchList.length == '0' && this.shopList.length == '0' && this.pageDeviseList
							.length == '0' && this.searchNavigationList.length == '0') {
							this.showPopup = true;
							this.searchList = [];
						}
					}
				});
			},

			//搜索发现列表
			getSearchHotList() {
				api.searchFindList({
					hit: 10
				}).then((res) => {
					if (res) {
						this.searchHot = res.data.result;
					}
					// console.log("搜索发现列表==>", res.data.result);
				});
			},

			//搜索
			searchClick(param) {
				this.searchHandle({
					detail: {
						value: this.searchKeyword ? this.searchKeyword : param || this.placeholder
					}
				});
				const key = this.searchKeyword ? this.searchKeyword : param || this.placeholder;

			},
			//缓存本地
			searchHandle(e, shopId) {
				console.log(this.placeholder, 'this.placeholder');
				let value;
				let nowTime = new Date().getTime();
				if (!(!this.lastTime || nowTime - this.lastTime > 1500)) {
					return;
				}
				this.lastTime = nowTime;
				if (e.currentTarget?.dataset.type) {
					this.keyword_type = e.currentTarget?.dataset.type || "自定义";
				}
				if (e.detail.value) {
					value = e.detail.value;
				} else if (e.currentTarget?.dataset.name) {
					value = e.currentTarget.dataset.name;
				}
				value = this.trim(value);
				let searchHistory = this.searchHistory ? this.searchHistory : value;
				searchHistory.forEach(function(item, index) {
					let i = 9; //最多缓存10条
					if (item.name == value) {
						searchHistory.splice(index, 1);
						i++;
					}

					if (index >= i) {
						searchHistory.splice(index, 1);
					}
				});
				searchHistory.unshift({
					name: value
				});
				uni.setStorageSync('searchHistory', searchHistory);
				let needBack = false;
				let pages = getCurrentPages();
				let prevPage = {};
				if (pages.length > 1) {
					let prevPage = pages[pages.length - 2];
					if (prevPage.route === 'pages/goods/goods-list/index') {
						needBack = true;
					}
					if (needBack) {
						//店铺列表里面点击的搜索，然后再回到店铺列表
						if (this.shopType == 1) {
							this.sendSenSearchStart(`搜索内容:${value}`);
							prevPage.$vm.getBackParams({
								name: value,
								searchType: this.keyword_type
							});
						} else {
							this.sendSenSearchStart(`搜索内容:${value}`);
							prevPage.$vm.isRefreshGoodsList = true; //修改上一页data里面的isRefresh
							prevPage.$vm.getBackParams({
								name: value,
								shopId: this.shopId || shopId || '',
								searchType: this.keyword_type
							});

						}
						uni.navigateBack({
							delta: 1 // 返回的页面数
						});
						return;
					}
				}
				if (this.shopType && this.shopType == 1) {
					//店铺列表里面点击的搜索，然后再回到店铺列表
					this.sendSenSearchStart(`搜索内容:${value}`);
					uni.redirectTo({
						url: '/pages/shop/shop-list/index?name=' + encodeURIComponent(value) +
							'&searchType=' + this.keyword_type
					});
				} else if (this.shopId || shopId) {
					this.sendSenSearchStart(`店铺id:${this.shopId || shopId || ''};搜索内容:${value || this
												.placeholder};`);
					uni.redirectTo({
						url: '/pages/goods/goods-list/index?title=' +
							encodeURIComponent(value) +
							'&shopId=' +
							(this.shopId || shopId) +
							'&searchKeyword=' +
							(encodeURIComponent(value) || encodeURIComponent(this.placeholder) || '') +
							'&searchType=' + this.keyword_type
					});
				} else {
					this.sendSenSearchStart(`店铺id:${this.shopId || shopId || ''};搜索内容:${value || this
							.placeholder}`);
					uni.redirectTo({
						url: '/pages/goods/goods-list/index?name=' + encodeURIComponent(value || this
								.placeholder) + '&shopId=' + (this.shopId || shopId || '') +
							'&searchType=' + this.keyword_type
					});
				}
			},
			// 给神策发送 发起搜索事件
			sendSenSearchStart(keyword) {
				senTrack('SearchStart', {
					search_keyword: keyword,
					keyword_type: this.keyword_type
				})
			},
			//显示隐藏
			isEye() {
				this.eye = !this.eye;
			},

			//删除历史
			clearSearchHistory() {
				let that = this;
				uni.showModal({
					content: '确认删除全部历史记录？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							that.searchHistory = [];
							uni.setStorageSync('searchHistory', []);
						}
					}
				});
			},

			goodsPage() {
				api.selectHot().then((res) => {
					this.goodsList = res.data;
				});
			},

			// 判断是否是tab页
			isTabBarPage(url) {
				const tabBarPages = ['pages/home/<USER>', 'pages/second-tab/index', 'pages/shopping-cart/index',
					'pages/tab-personal/index', 'pages/third-tab/index'
				]; // 添加你的tabBar页面路径
				return tabBarPages.includes(url);
			},
			handleToPage(url) {
				if (url == 'CustomerService') {
					util.handleCustomerService();
				} else if (url.startsWith('pages')) {
					if (this.isTabBarPage(url)) {
						uni.switchTab({
							url: '/' + url
						})
					} else {
						uni.navigateTo({
							url: '/' + url
						})
					}
				}
			},

			handleHotItem(item, index) {
				senGoods('SearchResultClick', {
					goods_rank: index+1,
					goods_strike_price: Number(item.priceDown),
					business_format: item.formatType,
					is_flash_sale: false,
				}, item);
				uni.navigateTo({
					url: '/pages/goods/goods-detail/index?id=' + item.id
				})
			}
			
			// handleCustomerService() {
			// 	util.handleCustomerService();
			// }
		}
	};
</script>
<style>
	.search-home {
		top: unset !important;
	}
</style>
<style scoped>
	.text-bold {
		font-weight: 500;
	}

	.goods {
		display: block;
		width: 100%;
	}

	.position-icon {
		position: absolute;
		bottom: 32rpx;
	}

	.Micro_page {
		position: relative;
		padding: 30rpx;
		background-color: #ffffff;
		/* border-bottom: 0.5upx solid #ccc; */
		margin-bottom: 1rpx;
	}

	.micro-icon {
		color: red;
	}

	.text {
		position: absolute;
		bottom: 30rpx;
		left: 35rpx;
		margin-left: 28rpx;
		font-size: 30upx;
		font-family: cuIcon;
	}

	.shop-text {
		position: absolute;
		top: 24rpx;
		left: 35rpx;
		margin-left: 30rpx;
		font-size: 30upx;
		font-family: cuIcon;
	}

	.more {
		position: absolute;
		right: 20rpx;
		color: #ccc;
		top: 24rpx;
		font-size: 30upx;
		font-family: cuIcon;
	}

	.shop-name {
		margin-top: 10rpx;
		padding-left: 34rpx;
		font-family: cuIcon;
	}

	.search-list {
		/* position: absolute; */
		top: 240rpx;
		width: 100%;
	}
</style>