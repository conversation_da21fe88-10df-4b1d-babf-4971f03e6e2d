<template>
  <view>
	 <view v-if="scene!=1154" style="padding-bottom: 110rpx;">
		 <shopcart
		   :isBack="false"
		    ref="shopcart"
		   :type="type"
			 />
	 </view> 
    <share-single-page v-if="scene==1154" />
  </view>
</template>
<script>
import shareSinglePage from "@/components/share-single-page/index.vue";
import shopcart from "@/pages/shop/components/shop-cart/shop-cart";
const app = getApp();

export default {
  components: {
	shopcart,
    shareSinglePage,
  },
  data () {
    return {
      // isFirst: true
      scene: '',
	  //有数统计使用
	  page_title:'购物车',
	  prevpageAsync: false,
    type: ''
    }
  },

  onLoad (options) {
    console.log(options.type, '==============options.type==================================')
    this.type = options.type || '';
  },

  onShow () {
    app.shoppingCartCount();
    // #ifdef MP-WEIXIN
    let getLaunchOptions = uni.getLaunchOptionsSync()
    this.scene = getLaunchOptions.scene
    //场景值等于 1154 分享单页模式
    if (this.scene && this.scene == 1154) return
    // #endif
    this.$refs.shopcart?.shoppingCartPage();
    if (this.$refs.shopcart && !this.$refs.shopcart.isFirst) { 
      // 提交订单页面返回购物车页面更新数据商品券
      this.$refs.shopcart.selectValue.length && this.$refs.shopcart.getDiscountsDetails()
    }
    if(this.$refs.shopcart){
      this.$refs.shopcart.isFirst = true	
    }
  },

};
</script>
