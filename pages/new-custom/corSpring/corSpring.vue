<template>
	<view>
		<cu-custom :hideMarchContent="true" :isBack="true" 
		 bgImage="https://img.songlei.com/live/new-custom/yellow-bg.png"
		>
			<block slot="backText">返回</block>
			<block slot="content">会员专属福利</block>
		</cu-custom>
		<view :style="{
			height: phoneHeight+'rpx',
			width: '750rpx',
			backgroundSize: '100% auto',
			backgroundImage: `url(https://img.songlei.com/live/new-custom/new-bg.png)`,
		  }"></view>
		<view class="cu-modal show" style="z-index: 999;background: rgba(0, 0, 0, 0.8);" catchtouchmove='true'>
			<view class="cu-dialog dialog-bg">
				<view class="" style="position: relative;">
					<image style="width: 644rpx;height:888rpx;"
						src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/c6a26a51-7857-4ae3-8132-186fa022af89.png"
						mode=""></image>
					<view style="position: absolute;
									left: 0;
									top: 27px;
									width: 100%;
									display: flex;
									flex-direction: column;
									justify-content: center;
									align-items: center;">
						<image style="width: 312rpx;height: 296rpx;" :src="gift.couponPicUrl" mode="aspectFit"></image>
						<view
							style=" font-size: 30rpx;font-weight: 400;color: #000000;  ">
							{{gift.couponName||''}}
						</view>
					</view>
					<view @click="getcor"
						style="text-align: center; height:82rpx;position: absolute;left: 110rpx; width: 420rpx;top: 694rpx;line-height: 81rpx;font-size: 38rpx;font-family: PingFang SC;color: #863722;	font-weight: 800;text-shadow: 1px 1px 1px #FFFFFF;">
						去领取
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	export default {
		data() {
			return {
				showBg: false, //是否显示背景色
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				gift: {},
				phoneHeight: 500,
			}
		},
		onLoad(options) {
			const {
				gift
			} = options;
			if (gift) {
				this.gift = JSON.parse(gift || '');
				console.log("===this.gift======", this.gift)
			}
		   uni.getSystemInfo({ 
			  success: (res) => {
				// 可使用窗口高度，将px转换rpx
				this.phoneHeight = (res.windowHeight * (750/res.windowWidth))
				console.log(this.phoneHeight)
			  }
			})
		},
		methods: {
			getcor() {
				api.getcorqc({
					"sceneCode": app.globalData.sf,
					"eventType": "REGISTER",
					"couponGroup": this.gift.couponGroup,
					"couponType": this.gift.couponType,
					"num": 1
				}).then(res => {
					if (res && res.data &&res.data.length>0) {
						uni.navigateTo({
							url: `/pages/new-custom/verification-gift/verification-gift?gift=${JSON.stringify(res.data[0])}`
						})
					}
				}).catch(e => {
					console.log(e)
					uni.showModal({
						title: '提示',
						content: e,
						showCancel: false,
						success(res) {}
					});
				});
			},
		}
	}
</script>

<style scoped>
	.popup-bg {
		width: 100%;
		height: 100%;
		position: fixed;
		top: 0;
		bottom: 0;
		background-color: rgba(1, 1, 1, 0.3);
	}

	.popup-cont {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-radius: 12rpx;
	}
</style>
