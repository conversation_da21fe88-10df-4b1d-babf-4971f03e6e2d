<template>
	<view>
		<cu-custom :hideMarchContent="true" :isBack="true"
			bgImage="https://img.songlei.com/live/new-custom/yellow-bg.png">
			<block slot="backText">返回</block>
			<block slot="content">注册成功领取礼品</block>
		</cu-custom>
		<view :style="{
			height: phoneHeight+'rpx',
			width: '750rpx',
			backgroundSize: '100% auto',
			backgroundImage: `url(https://img.songlei.com/live/new-custom/new-bg.png)`,
		  }"></view>

		<view class="cu-modal show" style="z-index: 999;background: rgba(0, 0, 0, 0.8);" catchtouchmove='true'>
			<view class="cu-dialog dialog-bg">
				<view class="title">礼品核销码</view>
				<view class="name">{{gift.couponName||''}}</view>
				<view class="desc">{{gift.simpleUsageDesc||''}}</view>
				<image style="width: 332rpx; height: 332rpx; margin-top: 32rpx" mode="aspectFit" :src="qrImg"></image>
				<view class="tip-content">
				  <rich-text :nodes="gift.detailUsageRule"></rich-text>
				</view>
				<view class="action-content" @click="handleDone">
					<view class="action">完成</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	const util = require("utils/util.js");
	const app = getApp();
	const QR = require("utils/wxqrcode.js");
	import api from 'utils/api'
	export default {
		data() {
			return {
				showBg: true, //是否显示背景色
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				gift: {
					// "couponName": "可口可乐350ml一瓶",
					// "effDate": "2022-07-28 00:00:00",
					// "couponGroup": "02",
					// "couponType": "HG622072800044",
					// "faceValue": 3,
					// "qrcode": "1031281737469892",
					// "couponNo": "0000456673113155",
					// "expDate": "2022-08-31 00:00:00",
					// "simpleUsageDesc": "请联系现场工作人员核销，兑换礼品",
					// "simpleUsageRule": "请联系现场工作人员核销，兑换礼品",
					// "detailUsageRule": "注：1、请妥善保管核销码，一个核销码仅可兑换一次；"
				},
				qrImg: null,
				loadingImg: 'data:image/png;base64,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',
				phoneHeight: 500,
			}
		},
		onLoad(options) {
			const {
				gift
			} = options;
			if (gift) {
				this.gift = JSON.parse(gift);
				console.log("===this.gift======", this.gift)
			}
			this.qrImg = this.loadingImg;
			if (this.gift.qrcode) {
				this.qrImg = QR.createQrCodeImg(this.gift.qrcode, {
					size: parseInt(300) //二维码大小  
				});
			}
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					this.phoneHeight = (res.windowHeight * (750 / res.windowWidth))
				}
			})
		},
		methods: {
			handleDone() {
				uni.navigateTo({
					url: '/pages/coupon/coupon-user-list/index'
				})
			}
		}
	}
</script>

<style scoped>
	.dialog-bg {
		width: 612rpx;
		min-height: 956rpx;
		background: #FFFFFF;
		border-radius: 38rpx;
		padding: 30rpx;
	}

	.title {
		font-weight: 800;
		color: #000000;
		padding-bottom: 30rpx;
		text-align: center;
		border-bottom: 1px dashed #aaa;
	}

	.name {
		font-size: 40rpx;
		font-family: PingFang SC;
		font-weight: 800;
		color: #000000;
		padding-top: 30rpx;
		text-align: center;
	}

	.desc {
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #585858;
		padding-top: 24rpx;
	}

	.tip-content {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #707070;
		line-height: 39rpx;
		text-align: left;
		margin: 40rpx 30rpx 50rpx 30rpx;
		border-bottom: 1px dashed #aaa;
		padding-bottom: 30rpx;
	}

	.action-content {
		display: flex;
		justify-content: center;
	}

	.action {
		width: 437rpx;
		height: 92rpx;
		background: #FFCB05;
		border-radius: 46rpx;
		color: #353E83;
		font-size: 30rpx;
		line-height: 92rpx;
	}
</style>
