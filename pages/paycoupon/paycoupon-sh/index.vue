<template>
	<view class="page-carnumber-test">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content"> 劵购买 </block>
		</cu-custom>
		<!-- <button v-if="showGotoApp" open-type="launchApp" app-parameter="wechat"
			binderror="launchAppError">返回APP查看订单</button> -->
	</view>
</template>

<script>
	import api from "utils/api";
	import __config from "config/env";
	const app = getApp();
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				showGotoApp: false
			};
		},
		watch: {},
		filters: {},
		onLoad(options) {
			console.log(options, "options");
			//获取订单id
			if (options.orderId) {
				api
					.searchpayorder({
						'channel': "TENANT",
						'custId': "0",
						'orderId': options.orderId,
					})
					.then((res) => {
						console.log(res);
						api.paywct({
							'orderId': res.data.orderId,
							"paymentPrice": res.data.payRequest.paymentPrice,
							"listOrderInfo": res.data.payRequest.listOrderInfo,
							"paymentType": "1",
							"businessType": res.data.payRequest.businessType,
							"businessCode": res.data.payRequest.businessCode,
						}).then(res => {
							if (res.data) {
								//微信小程序支付
								uni.requestPayment({
									'provider': 'wxpay',
									'timeStamp': res.data.barCodePay.timeStamp,
									'nonceStr': res.data.barCodePay.nonceStr,
									'package': res.data.barCodePay.package1,
									'signType': res.data.barCodePay.signType,
									'paySign': res.data.barCodePay.paySign,
									success: (res) => {
										console.log(res)
									},
									fail: (error) => {

									},
									complete: function(res) {
										this.showGotoApp = true;
										if (res.errMsg === 'requestPayment:ok') {
											uni.showToast({
												title: `购买成功`,
											});
											uni.switchTab({
												url: '/pages/home/<USER>',
												},1500)
    
										} else if (res.errMsg === 'requestPayment:fail cancel') {
											uni.showToast({
												title: `购买失败`,
											});
											uni.switchTab({
												url: '/pages/home/<USER>',
												},1500)

										}
									}
								});
							}
						})
					});
			}
		},
		methods: {
			launchAppError(e) {
				console.log(e.detail.errMsg)
			}
		}
	};
</script>

<style scoped lang="less">
</style>