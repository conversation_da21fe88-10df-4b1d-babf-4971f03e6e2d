<template>
	<view>

	</view>
</template>

<script>
	import __config from 'config/env';
	import api from 'utils/api'
	import util from 'utils/util'
	const app = getApp();
	// #ifdef MP-WEIXIN
	const businessCirclePlugin = requirePlugin('business-circle-plugin');
	// #endif
	export default {
		data() {
			return {
				openTimes: 1
			}
		},
		onLoad(options) {
			app.initPage().then(res => {
				const userInfo = uni.getStorageSync('user_info');
				if (!util.isUserLogin()) {
					uni.navigateTo({
						url: '/pages/login/index?fromChannel=SMART&&reUrl=business-circle-plugin'
					});
					return
				}
				// #ifdef MP-WEIXIN
				businessCirclePlugin.getLocation(userInfo.openId).then(
					res => {
						console.log("=====res========",res);
						if (res.return_code == 0) {
							api.erpOpenIdBind({
								channelId: 'SMART'
							}).then(res => {
								businessCirclePlugin.getAuthStatus(userInfo.openId, __config.mchId)
									.then(res => {
										uni.navigateTo({
											url: `plugin://business-circle-plugin/index?mch_id=${__config.mchId}&openid=${userInfo.openId}&member_status=${res.status}`
										});
									})

							});
						}
					})
				// #endif
			});
		},
		onShow() {
			if (this.openTimes > 1) {
				uni.reLaunch({
					url: '/pages/home/<USER>'
				});
			}
			this.openTimes++;
		},
		methods: {

		}
	}
</script>

<style>

</style>