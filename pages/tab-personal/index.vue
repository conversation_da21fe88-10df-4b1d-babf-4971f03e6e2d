<template>
	<view>
		<cu-custom bgColor="#FFF4E9" :hideMarchContent="true"></cu-custom>
		<view v-if="scene != 1154" style="padding-bottom: 110rpx">
			<view class="pb-xxxmd" style="background: linear-gradient(180deg, #fff4e9 0%, #fff1dc 26%, #ffe1c1 80%, #f7f7f7 100%)">
				<view class="flex justify-between mlr-xsm align-center">
					<navigator
						url="/pages/user/user-info/index"
						hover-class="none"
						v-if="userInfo && (userInfo.nickName || userInfo.headimgUrl)"
						class="flex align-center personal-information"
					>
						<image class="size-110 head" :src="userInfo.headimgUrl || $imgUrl('live/user-center/default_pic.png')"></image>
						<view class="content ml-md flex flex-direction">
							<view class="font-xxl text-bold">
								{{ userInfo.nickName || '' }}
							</view>
							<view class="text-88 font-md" v-if="userInfo.phone">手机号：{{ userInfo.phone | phoneEncryption }}</view>
						</view>
					</navigator>
					<navigator url="/pages/login/index" hover-class="none" v-else style="display: flex; align-items: center" class="personal-information">
						<image class="size-110 head" :src="$imgUrl('live/user-center/default_pic.png')"></image>
						<view class="font-xxl" style="padding-left: 20rpx; font-weight: 900">去登录</view>
					</navigator>

					<view
						@click="checkLogin('/pages/user/user-address/list/index')"
						v-if="userInfo && (userInfo.nickName || userInfo.headimgUrl)"
						class="ml-md flex flex-direction align-center"
					>
						<image :src="$imgUrl('live/user-center/gray_address.png')" class="icon-settings" />
						<text class="text-88 font-md mt-sss">地址</text>
					</view>

					<view @click="handleCusServiceClick" class="ml-md flex flex-direction align-center">
						<image :src="$imgUrl('live/user-center/kefu.png')" class="icon-settings" />
						<text class="text-88 font-md mt-sss">客服</text>
					</view>

					<navigator
						url="/pages/user/user-info/index"
						hover-class="none"
						v-if="userInfo && (userInfo.nickName || userInfo.headimgUrl)"
						class="ml-md flex flex-direction align-center"
					>
						<image :src="$imgUrl('live/user-center/settings.png')" class="icon-settings" />
						<text class="text-88 font-md mt-sss">设置</text>
					</navigator>
				</view>
				<view class="mt-xsm mlr-xsm flex justify-between" v-if="userInfo && userInfo.erpCustTypename">
					<view style="flex: 1; border-radius: 10rpx" class="bg-white">
						<view class="flex justify-between more-tab" style="background: #fff7f7; border-radius: 17rpx">
							<view class="reduce-bg flex font-xxxxsm text-white padding align-center">
								<text>为您节省</text>
								<price-handle v-if="recommendCoupon" color="#ffffff" :value="recommendCoupon.discount" signFont="20rpx" bigFont="36rpx" smallFont="20rpx"></price-handle>
							</view>
							<view class="flex align-center justify-end" style="flex: 1" @click="checkLogin('/pages/coupon/coupon-centre/index')">
								<text class="font-xmd text-center" style="color: #f32a20; flex: 1">领券中心</text>
								<image :src="$imgUrl('live/user-center/arrow-right.png')" class="icon-arrow margin-lr-xs" />
							</view>
						</view>
						<view class="flex m-xxsm justify-around">
							<view style="flex: 1" class="flex flex-direction align-center" @click="checkLogin('/pages/signrecord/signrecord-info/index')">
								<view class="text-88 font-md">积分</view>
								<view class="pay-num font-xxl mt-xxxs">
									{{ userInfo.pointsCurrent || '0' }}
								</view>
							</view>

							<view style="flex: 1" class="flex flex-direction align-center" @click="checkLogin('/pages/coupon/coupon-user-list/index')">
								<text class="text-88 font-md">优惠劵</text>
								<view class="pay-num font-xxl mt-xxxs">
									{{ userInfo.couponNum ? userInfo.couponNum : 0 }}
									<text class="font-md">张</text>
								</view>
							</view>

							<view style="flex: 1" class="flex flex-direction align-center" @tap="isGo">
								<text class="text-88 font-md">零钱</text>
								<view class="pay-num mt-xxxs">
									<format-price :styleProps="priceStyle" color="#000" :price="availableBalance" priceFontSize="36rpx" />
								</view>
							</view>

							<view style="flex: 1" class="flex flex-direction align-center" @click="checkLogin('/pages/gift/gift-card/index')">
								<text class="text-88 font-md">礼品卡</text>
								<view class="pay-num font-xxl mt-xxxs">
									{{ num }}
									<text class="font-md">张</text>
								</view>
							</view>
						</view>
						<!-- 券类型 1线上券 2线下券 -->
						<view
							v-if="recommendCoupon"
							@click="
								checkLogin(
									recommendCoupon.type == 2
										? '/pages/coupon/coupon-offline-detail-plus/index?id=' + recommendCoupon.couponId
										: recommendCoupon.type == 1?  '/pages/goods/goods-list/index?couponId=' + recommendCoupon.couponId :  '/pages/coupon/coupon-detail/index?id=' + recommendCoupon.couponId+'&toUse=true&couponUser=null'
								)
							"
							class="flex justify-between align-center plr-xxmd pt-xsm pb-xxxsm m-sss"
							style="background: #fff7f7; border-radius: 14rpx"
						>
							<image :src="$imgUrl('live/user-center/ic_coupon.png')" class="coupon-image margin-right-xs" />
							<view class="flex flex-direction padding-left-xs" style="flex: 1">
								<view class="text-black font-xmd overflow-1">
									{{ recommendCoupon.couponName }}
								</view>
								<view class="font-md text-red overflow-1">{{ recommendCoupon.usageDesc }}</view>
							</view>
							<view class="use-btn flex justify-center align-center font-md">{{recommendCoupon.type == 3? '去领取': '去使用'}}</view>
						</view>
						<!-- 占位置使用-->
						<view v-else class="size-54"></view>
					</view>

					<!-- 一码付内容 -->
					<view
						style="border-radius: 10rpx"
						@click="checkLogin('/pages/user/user-qrcode/index')"
						class="bg-white ml-xxxs flex flex-direction justify-between align-center"
					>
						<view class="vip-bg more-tab font-xxmd text-white text-center" :class="'member-bg-' + userInfo.erpCustType" style="padding-right: 0">{{userInfo.erpCustTypename || 'VIP会员'}}</view>
						<image :src="memberLevel[userInfo.erpCustType]" class="size-64 mtb-xxsm" />
						<view class="flex flex-direction justify-between align-center plr-xxmd pt-xsm pb-xxxsm m-sss" :class="'member-color-'+userInfo.erpCustType">
							<view class="text-black font-xmd">卡号 | 一码付</view>
							<!-- <view class="font-md">{{ userInfo.erpCustTypename }}</view> -->
						</view>
					</view>
				</view>
			</view>
			<view class="mlr-xsm">
				<view class="cu-list menu card-menu all-orders margin-reduce bg-white">
					<!-- 我的订单 -->
					<view class="flex align-center justify-between bg-white mt-xxmd ml-xxxsm mr-xxs">
						<view class="action font-xxxmd text-bold text-black">我的订单</view>
						<view class="action text-88 font-md" @click="checkLogin('/pages/order/order-list/index')" hover-class="none">
							全部
							<text class="cuIcon-right text-88"></text>
						</view>
					</view>
					<view class="bg-white mt-xsm mb-xsm mlr-xsm">
						<!-- 待付款、待发货、待收货、待评价、退换/售后 -->
						<view class="cu-list grid col-5 no-border" style="padding: 0rpx">
							<!-- 待付款 -->
							<view class="cu-item">
								<view @click="checkLogin('/pages/order/order-list/index?status=0')" hover-class="none">
									<view class="text-black">
										<image :src="$imgUrl('live/user-center/icon-pay.png')" class="size-64"></image>
										<view v-if="orderCountAll[0] > 0" class="icon-num font-xxxxsm size-lg">
											{{ orderCountAll[0] }}
										</view>
									</view>
									<text class="user-text-xl font-md text-black">待付款</text>
								</view>
							</view>
							<!-- 待发货 -->
							<view class="cu-item">
								<view @click="checkLogin('/pages/order/order-list/index?status=1')">
									<view class="text-black" style="margin-top: 0">
										<image :src="$imgUrl('live/user-center/icon-send.png')" class="size-64"></image>
										<view v-if="orderCountAll[1] > 0" class="icon-num font-xxxxsm size-lg">
											{{ orderCountAll[1] }}
										</view>
									</view>
									<text class="user-text-xl font-md text-black">待发货</text>
								</view>
							</view>
							<!-- 待收货 -->
							<view class="cu-item">
								<view @click="checkLogin('/pages/order/order-list/index?status=2')">
									<view class="text-black" style="margin-top: 0">
										<image :src="$imgUrl('live/user-center/icon-deliver.png')" class="size-64"></image>
										<view v-if="orderCountAll[2] > 0" class="icon-num font-xxxxsm size-lg">
											{{ orderCountAll[2] }}
										</view>
									</view>
									<text class="user-text-xl font-md text-black">待收货</text>
								</view>
							</view>
							<!-- 待评价 -->
							<view class="cu-item">
								<view @click="checkLogin('/pages/order/order-list/index?status=4')">
									<view class="text-black" style="margin-top: 0">
										<image :src="$imgUrl('live/user-center/icon-message.png')" class="size-64"></image>
										<view v-if="orderCountAll[3] > 0" class="icon-num  font-xxxxsm size-lg">
											{{ orderCountAll[3] }}
										</view>
									</view>
									<text class="user-text-xl font-md text-black">待评价</text>
								</view>
							</view>
							<!-- 退换/售后 -->
							<view class="cu-item">
								<view @click="checkLogin('/pages/order/order-refunds/index')">
									<view class="text-black" style="margin-top: 0">
										<image :src="$imgUrl('live/user-center/icon-recharge.png')" class="size-64"></image>
										<view v-if="orderCountAll[4] > 0" class="icon-num  font-xxxxsm size-lg">
											{{ orderCountAll[4] }}
										</view>
									</view>
									<text class="user-text-xl font-md text-black">退换/售后</text>
								</view>
							</view>
						</view>
						<!-- 物流信息轮播 -->
						<!-- <swiper v-if="logisticsList.length" style="height:100rpx;" autoplay circular vertical>
						<swiper-item v-for="(item, index) in logisticsList" :key="index">
							<view class="flex justify-around align-center"
								style="width:95%;height:100%;background:#f1f1f1;margin:0 auto;border-radius:15rpx;">
								<view class="" style="width:100rpx;height:100%;">
									<image style="width:100%;height:100%;border-radius:15rpx;border:1px solid #ccc;"
										:src="item.picUrl || '@https://img.songlei.com/live/img/no_pic.png'" mode="aspectFill"
										class="row-img">
									</image>
								</view>
								待支付
								<view v-if="item.orderStatus == 0"
									class="padding-lr-sm flex justify-between align-center" style="flex:1;">
									<view class="">
										<view class="text-black">
											{{item.statusDesc}}
										</view>
										<view class="text-gray font-md">
											剩余时间
											<count-down :outTime="1000 * item.remainSecond"
												@countDownDone="countDownDone" connectorColor="#f00" textColor="#f00"
												backgroundColor="#f1f1f1">
											</count-down>
										</view>
									</view>
									<navigator @click="checkLogin('/pages/order/order-list/index?status=0')"
										hover-class="none">
										<view class="text-red font-md"
											style="border:2rpx solid red; padding: 10rpx 20rpx;border-radius: 50rpx;">
											去支付
										</view>
									</navigator>
								</view>
								备货中、运输中
								<view v-else class="padding-lr-sm" style="flex:1;"
									@click="toLogisticsInfo(item.orderIds)">
									<view class="flex justify-between align-center">
										<view class="text-black">
											{{item.statusDesc}}
										</view>
										<view class="text-gray font-md">
											{{item.routeDate}}
										</view>
									</view>
									<view class="text-gray font-md"
										style="width:530rpx;height:30rpx;black-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
										{{item.routeInfo}}
									</view>
								</view>
							</view>
						</swiper-item>
					</swiper> -->
					</view>
				</view>

				<!-- 快递、收藏、足迹 -->
				<view class="cu-list menu card-menu g-white pt-xxxxsm pl-md all-orders bg-white">
					<scroll-view scroll-x="true" style="overflow: hidden; width: 100%">
						<view class="cu-list flex no-border" style="padding: 0rpx">
							<!-- 红包雨活动 -->
							<view v-if="redPacketInfo&& redPacketInfo.isPop" class="cu-item line-before collect-tab mr-xxsm" @click="toPage('/pages/home/<USER>')">
								<view class="text-black font-xxmd text-bold text-left">红包雨活动</view>
								<view class="text-left font-md mt-xxs" style="color: #999999">今日剩余{{ redPacketInfo.dayDrawNum }}次机会</view>
								<view class="mt-xxxsm">
									<image src="http://img.songlei.com/live/home/<USER>" mode="aspectFill" class="row-img row-tab-img size-198 margin-top-sm" />
									<view class="mt-xsm font-md text-black">参与人数</view>
									<view class="mb-xsm mt-s font-md text-cut" style="color: #999999">{{redPacketInfo.drawPeopleNum}}+</view>
								</view>
							</view>
							<view class="cu-item line-before collect-tab mr-xxsm" @click="checkLogin('/pages/order/order-list/index')">
								<view class="text-black font-xxmd text-bold text-left">快递</view>
								<view v-if="rollList.logisticsList && rollList.logisticsList.length">
									<view class="text-left font-md mt-xxs" style="color: #999999">{{ rollList.logisticsList.length }}个快递更新</view>
									<swiper class="mt-xxxsm tab-swiper" circular autoplay vertical :interval="3000" :duration="500" easing-function="linear">
										<swiper-item
											v-for="item in rollList.logisticsList"
											:key="item.id"
											style="position: relative"
											@click.stop="checkLogin(`/pages/order/order-detail/index?id=${item.orderIds}`)"
										>
											<image :src="item.picUrl || $imgUrl('live/img/no_pic.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
											<view class="mt-xsm font-md text-black">{{ item.statusDesc }}</view>
											<view class="mb-xsm mt-s font-md text-cut" style="color: #999999">
												{{ item.routeInfo }}
											</view>
										</swiper-item>
									</swiper>
								</view>
								<view v-else>
									<view class="text-left font-md mt-xxs" style="color: #999999">您的快递</view>
									<view class="mt-xxxsm">
										<image src="http://img.songlei.com/rank/express-nodata.png" mode="aspectFill" class="row-img row-tab-img size-198 margin-top-sm" />
										<view class="mt-xsm font-md text-black">暂无快递</view>
										<view class="mb-xsm mt-s font-md text-cut" style="color: #999999">快去下单吧</view>
									</view>
								</view>
							</view>
							<view class="cu-item collect-tab mr-xxsm" @click="checkLogin('/pages/user/user-collect/index')">
								<view class="text-black font-xxmd text-bold text-left">收藏</view>
								<view v-if="rollList.collectList && rollList.collectList.length">
									<view class="text-left font-md mt-xxs" style="color: #999999">查看最近收藏</view>
									<swiper class="mt-xxxsm tab-swiper" circular autoplay vertical :interval="3500" :duration="600" easing-function="linear">
										<swiper-item
											v-for="item in rollList.collectList"
											:key="item.id"
											@click.stop="checkLogin(`/pages/goods/goods-detail/index?id=${item.goodsSpu.id}&source_module=${encodeURIComponent('个人中心')}`)"
										>
											<image :src="item.goodsSpu.picUrls[0] || $imgUrl('live/img/no_pic.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
											<view class="mt-xsm font-md text-black">
												<text class="margin-0" v-if="item.collectNum > 100">有眼光</text>
												<text class="margin-0" v-else-if="item.collectNum > 10 && item.collectNum < 100">有潜力</text>
												<text class="margin-0" v-else-if="item.collectNum < 10">有个性</text>
											</view>
											<view class="mb-xsm mt-s font-md tip">{{ item.collectNum }}+人收藏</view>
										</swiper-item>
									</swiper>
								</view>
								<view v-else>
									<view class="text-left font-md mt-xxs" style="color: #999999">查看最近的收藏</view>
									<view class="mt-xxxsm">
										<image :src="$imgUrl('rank/collection-nodata.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
										<view class="mt-xsm font-md text-black">暂无收藏</view>
										<view class="mb-xsm mt-s font-md text-cut" style="color: #999999">快去收藏商品吧</view>
									</view>
								</view>
							</view>
							<view class="cu-item collect-tab mr-xxsm" @click="checkLogin('/pages/user/user-footprint/index')">
								<view class="text-black font-xxmd text-bold text-left">足迹</view>
								<view v-if="rollList.footprintList && rollList.footprintList.length">
									<view class="text-left font-md mt-xxs" style="color: #999999">看过的商品</view>
									<swiper class="mt-xxxsm tab-swiper" circular vertical autoplay :interval="4000" :duration="700" easing-function="linear">
										<swiper-item
											v-for="item in rollList.footprintList"
											:key="item.id"
											@click.stop="checkLogin(`/pages/goods/goods-detail/index?id=${item.goodsSpu.id}`)"
										>
											<image :src="item.goodsSpu.picUrls[0] || $imgUrl('live/img/no_pic.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
											<view class="mtb-xsm font-md overflow-2 text-black">
												{{ item.goodsSpu.name }}
											</view>
										</swiper-item>
									</swiper>
								</view>
								<view v-else>
									<view class="text-left font-md mt-xxs" style="color: #999999">看过的商品</view>
									<view class="mt-xxxsm">
										<image :src="$imgUrl('rank/footprints-nodata.png')" mode="aspectFit" class="row-img row-tab-img margin-top-sm size-198" />
										<view class="mt-xsm font-md text-black">暂无足迹</view>
										<view class="font-md text-cut mb-xsm mt-s" style="color: #999999">快去逛逛吧</view>
									</view>
								</view>
							</view>
							<view class="cu-item collect-tab pr-md" @tap="isGo">
								<view class="text-black font-xxmd text-bold text-left">我的钱包</view>
								<view>
									<view class="text-left font-md font-md mt-xxs" style="color: #999999">我的资产</view>
									<view class="mt-xxxsm">
										<image :src="$imgUrl('live/user-center/tab_wallet.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
										<view class="mt-xsm font-md text-black">钱包余额</view>
										<view class="font-md text-cut tip mb-xsm mt-s">
											{{ availableBalance == '' || availableBalance == undefined ? '马上开通' : '￥' + availableBalance }}
										</view>
									</view>
								</view>
							</view>

							<!-- 暂未开发改功能，先注释，后面会用 -->
							<!-- <view class="cu-item collect-tab" @click="checkLogin('')">
								<view class="text-black font-md text-bold text-left">本地生活</view>
								<view>
									<view class="text-left font-md margin-top-xs" style="color: #999999">外卖和到店</view>
									<view class="margin-top-xs">
										<image :src="$imgUrl('live/user-center/locallive.png')" mode="aspectFill" class="row-img row-tab-img margin-top-sm" />
										<view class="margin-top font-md text-black">松雷松鼠专送</view>
										<view class="font-md text-cut tip">5000+人收藏</view>
									</view>
								</view>
							</view>

							<view class="cu-item collect-tab" @click="checkLogin('/pages/user/user-collect/index')">
								<view class="text-black font-md text-bold text-left">互动游戏</view>
								<view>
									<view class="text-left font-md margin-top-xs" style="color: #999999">玩游戏得奖品</view>
									<view class="margin-top-xs">
										<image :src="$imgUrl('live/user-center/icon_game.png')" mode="aspectFill" class="row-img row-tab-img margin-top-sm" />
										<view class="margin-top font-md text-black overflow-2">参与大转盘赢88元 妆品优惠券....</view>
									</view>
								</view>
							</view> -->
						</view>
					</scroll-view>
				</view>

				<!-- 广告配置 -->
				<advert styleProps="height: 196rpx;border-radius: 20rpx !important;margin: auto !important;margin-top: 15rpx !important;" searchKey="USER_CENTER" />

				<view class="mine" style="margin-bottom: 15rpx !important; margin-top: 10rpx !important">
					<official-account></official-account>
				</view>

				<view class="cu-list menu card-menu all-orders bg-white mlr-xs ptb-xxsm" style="margin-top: 0 !important">
					<view v-for="(item, index) in menuList" :key="index">
						<view class="flex align-center">
							<view style="width: 138rpx" v-for="(menuItem, menuIndex) in menuList[index]" :key="menuIndex">
								<view
									@click="checkLogin(menuItem.url)"
									v-if="
										(menuItem && menuItem.url && menuItem.label != '我的分销') ||
										(menuItem && menuItem.url && menuItem.label == '我的分销' && userInfo && userInfo.distributionUser && userInfo.distributionUser.userId > 0)
									"
									class="ptb-xxsm flex flex-direction align-center"
								>
									<image :src="menuItem.icon" class="size-60"></image>
									<view class="mt-s font-xmd">{{ menuItem.label }}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<recommendComponents canLoad />
		</view>
		<share-single-page v-if="scene == 1154" />
	</view>
</template>

<script>
const app = getApp();
import api from 'utils/api';
import util from 'utils/util';
import __config from '@/config/env'; // 配置文件Ω
import shareSinglePage from '@/components/share-single-page/index.vue';
import countDown from 'components/count-down/index';
import formatPrice from '@/components/format-price/index.vue';
import advert from '@/components/advert/advert.vue';
import recommendComponents from '@/components/recommend-components/index';
import { getPaypayCode } from '@/api/gift';
const version = require('utils/version.js');
import { mapState } from 'vuex';
import { getWxTemplate } from '@/api/message.js';
import { getRoll } from '@/api/logisticsMy.js';
import { getHomePop } from '@/components/div-components/div-red-packet-dialog/api/redPacketApi';
import { navigateUtil } from "@/static/mixins/navigateUtil.js"

export default {
	mixins: [navigateUtil],
	computed: {
		...mapState(['CustomBar', 'StatusBar'])
	},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			userInfo: {},
			distributionConfig: {
				enable: '1'
			},
			shareUser: {},
			// countTotal: '',//当前积分
			orderCountAll: [],
			showPrivacyPolicy: __config.showPrivacyPolicy,
			privacyPolicyUrl: __config.privacyPolicyUrl,
			protocolUrl: __config.protocolUrl,
			canIUseGetUserProfile: false,

			isWeiXinBrowser: util.isWeiXinBrowser(),
			scene: '',
			//有数统计使用
			page_title: '个人中心',
			availableBalance: '', //余额
			logisticsList: [], // 物流信息
			AuthorInformation: false,

			background: ['color1', 'color2', 'color3'],
			autoplay: true,
			interval: 4000,
			duration: 500,
			priceStyle: 'display: flex; justify-content: center; font-weight: bold; align-items: baseline;',
			cusServicePageUrl: '',
			menuList: [
				[
					{
						icon: this.$imgUrl('live/user-center/parking.png'),
						label: '停车场',
						url: '/pages/parkinglot/parking-home/index'
					},
					{
						icon: this.$imgUrl('live/user-center/signrecord.png'),
						label: '积分签到',
						url: '/pages/signrecord/signrecord-info/index'
					},
					{
						icon: this.$imgUrl('live/user-center/bargain.png'),
						label: '我的砍价',
						url: '/pages/bargain/bargain-user-list/index'
					},
					{
						icon: this.$imgUrl('live/user-center/mycardbag.png'),
						label: '我的卡包',
						url: '/pages/user/mycardbag/mycardbag'
					},
					{
						icon: this.$imgUrl('live/user-center/coupon.png'),
						label: '会员权益',
						url: '/pages/micro-page/index?id=1624942147010723842'
					}
				],
				[
					// {
					// 	icon: this.$imgUrl('live/user-center/address.png'),
					// 	label: '收货地址',
					// 	url: '/pages/user/user-address/list/index'
					// },
					// {
					// 	icon:  this.$imgUrl('live/user-center/planttree.png'),
					// 	label: '松鼠乐园',
					// 	url: '/pages/squirrelParadise/home/<USER>',
					// },
					// {
					// 	icon: 'https://img.songlei.com/live/user-center/my-prize.png',
					// 	label: '我的奖品',
					// 	url: '/pages/myPrize/index',
					// },
					{
						icon: this.$imgUrl('live/user-center/fpzx.png'),
						label: '发票中心',
						url: '/pages/invoice/invoiceCenter/index'
					},
					{
						icon: this.$imgUrl('live/user-center/appraises.png'),
						label: '我的评价',
						url: '/pages/user/user-appraises/index'
					},
					// {
					// 	icon: this.$imgUrl('live/competition/my-guess.png'),
					// 	label: '我的竞猜',
					// 	// url: "/pages/activity/mycompetition/index",
					// 	url: "/pages/activity/my-gold-medal/index",
					// },
					{
						icon: this.$imgUrl('live/user-center/huiyuanhuodong.png'),
						label: '会员活动',
						url: '/pages/memberactivity/list/index'
					},
					{
						icon: this.$imgUrl('live/user-center/pack.png'),
						label: '存包柜',
						url: '/pages/storagelocker/order?origins=my'
					},
					{
						icon: 'https://img.songlei.com/live/user-center/my-prize.png',
						label: '我的奖品',
						url: '/pages/myPrize/index',
					},
				],
				[
						// {
						// 	icon: this.$imgUrl('live/user-center/huiyuanhuodong.png'),
						// 	label: '会员活动',
						// 	url: '/pages/memberactivity/list/index',
						// },
						{
							icon: this.$imgUrl('live/user-center/distribution.png'),
							label: '我的分销',
							url: '/pages/distribution/distribution-center/index',
						},
				]
			],
			num: 0, //礼品卡数量
			rollList: {}, // 快递，收藏，足迹数据
			recommendCoupon: null,
			customerGrade: {},
			//会员级别
			memberLevel: {
				101: 'https://img.songlei.com/live/appraises/qr-101.png',
				102: 'https://img.songlei.com/live/appraises/qr-102.png',
				103: 'https://img.songlei.com/live/appraises/qr-103.png',
				108: 'https://img.songlei.com/live/appraises/qr-108.png'
			},
			redPacketInfo:{},//红包雨
		};
	},

	components: {
		shareSinglePage,
		countDown,
		formatPrice,
		recommendComponents,
		advert
	},
	props: {},

	onShow() {
		app.shoppingCartCount();
		if (this.scene && this.scene == 1154) return;
		if (uni.getStorageSync('WALLET_CLOSE') == 'close') {
			uni.removeStorageSync('USER_WALLET_INFO');
			uni.removeStorageSync('Authentication');
			uni.removeStorageSync('WALLET_CLOSE');
		}
		app.initPage().then((res) => {
			this.userInfoGet();
			this.orderCountAllFun();
			this.getPaypayCode();
			//开通
			this.isAccount();
			console.log('用户登录查看数据', uni.getStorageSync('user_info'));
			if (util.isUserLogin()) {
				//同步数据至erp
				api.erpOpenIdBind();
				//获取物流信息状态
				// this.getLogisticsList();
				// 获取快递、收藏、足迹数据
				this.getRollList();
				this.getRecommendCoupon();
				// 获取红包雨参与次数人数 
				this.getRedPacket()
			}
		});

		if (version.getversion() == '1') {
			this.AuthorInformation = true;
			// console.log(this.userInfo, 'userInfouserInfo')
		} else if (version.getversion() == '2') {
			this.AuthorInformation = false;
		} else if (version.getversion() == '3') {
			this.AuthorInformation = false;
		}
	},

	onLoad(option) {
		// #ifdef MP-WEIXIN
		let getLaunchOptions = uni.getLaunchOptionsSync();
		this.scene = getLaunchOptions.scene;
		//场景值等于 1154 分享单页模式
		if (this.scene && this.scene == 1154) return;
		// #endif
		//广告
		this.advertisement('USER_CENTER');
		//分享朋友
		this.advertisement('SHARE_USER_CENTER');
		//获取客服链接
		this.getCustomerServiceInfo();
		//认证
		// this.isAuthentication();
		// this.getUserCustAccntTot()
		// #ifdef MP-WEIXIN
		if (uni.getUserProfile) {
			this.canIUseGetUserProfile = true;
		}
		// #endif
		// #ifdef H5
		let code = option.code;
		let state = option.state;
		//授权code获取用户信息
		if (code && state == 'snsapi_userinfo_update') {
			//有code
			this.userInfoUpdateByMp({
				jsCode: code,
				scope: state
			});
		}
		// #endif
	},
	//分享朋友
	onShareAppMessage: function () {
		let shareUser = this.shareUser;
		let title = shareUser.name;
		let linkUrl = shareUser.linkUrl;
		let imageUrl = shareUser.imgUrl + '-jpg_w360_q90' || '';
		const userInfo = uni.getStorageSync('user_info');
		let userCode = userInfo ? '&type=1&sharer_user_code=' + userInfo.userCode : '';
		let path = `${linkUrl}?userId=` + this.userInfo.id + userCode;
		return {
			title: title ? title : null,
			path: linkUrl ? path : null,
			imageUrl: imageUrl ? imageUrl : null,
			success: function (res) {
				uni.showToast({
					title: '分享成功'
				});
			},
			fail: function (res) {
				// 转发失败
				uni.showToast({
					title: '分享失败',
					icon: 'none'
				});
			}
		};
	},

	methods: {
		// 获取红包雨参与次数人数 
		async getRedPacket(){
			const { data } = await getHomePop()
			console.log("---data----", data);
			this.redPacketInfo = data
		},

		//获取礼品卡数量
		getPaypayCode() {
			getPaypayCode().then((res) => {
				if (res.code == 0 && res.data) {
					this.num = res.data.num || 0;
				}
			});
		},

		getCustomerServiceInfo() {
			let that = this;
			api.getLivingTag().then((res) => {
				if (res && res.data) {
					that.cusServicePageUrl = res.data.platWxCustomerUrl;
				}
			});
		},

		handleCusServiceClick() {
			// #ifdef MP
			wx.openCustomerServiceChat({
				extInfo: {
					url: this.cusServicePageUrl
				},
				corpId: __config.chatId,
				success(res) {}
			});
			// #endif
			// #ifdef APP
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				openCustomerServiceChat: true,
				corpid: __config.chatId,
				customerUrl: this.cusServicePageUrl,
				fail(err) {
					console.log('打开客服错误', err);
					// uni.makePhoneCall({
					// 	phoneNumber: '**********'
					// })
				}
			});

			// #endif
		},

		//付款码
		payCode() {
			uni.showToast({
				icon: 'none',
				title: '功能研发中...敬请期待!',
				duration: 2000
			});
		},

		//点击资产跳转
		isGo() {
			getWxTemplate({
				type: 6
			}).then((res) => {
				// #ifdef MP
				uni.requestSubscribeMessage({
					tmplIds: res.data,
					complete: () => {
						this.isGoNext();
					}
				});
				// #endif
				// #ifndef MP
				this.isGoNext();
				// #endif
			});
		},

		isGoNext() {
			let url = '/pages/wallet/personal-information/index';
			if (uni.getStorageSync('Account') != null && uni.getStorageSync('Account').length != 0) {
				url = '/pages/wallet/my-balance/index';
			}
			if (!util.isUserLogin()) {
				uni.navigateTo({
					url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
				});
				return;
			} else {
				uni.navigateTo({
					url
				});
			}
		},

		//钱包
		toWallet() {
			// //1.判断用户是否认证 没有认证去认证
			// if (!uni.getStorageSync('isAuthentication')) {
			// uni.navigateTo({
			//   url: "/pages/wallet/wallet-authentication/index"
			// });
			// } else {
			//2判断用户是否开通 没有开通去开通
			if (uni.getStorageSync('Account') != null && uni.getStorageSync('Account').length == 0) {
				//填写个人信息
				uni.navigateTo({
					url: '/pages/wallet/personal-information/index'
				});
				return;
			} else {
				//3.如果开通和认证了就去钱包页面
				uni.navigateTo({
					url: '/pages/wallet/wallet-pages/index'
				});
			}
			// }
		},

		//用户是否认证 返回有数据代表已经认证，没数据代表没认证
		// isAuthentication () {
		//   api.isAuthentication().then((res) => {
		//     let Authentication = res.data
		//     uni.setStorageSync('Authentication', Authentication);
		//   })
		// },

		//是否开户的，也是有数据代表开户，没数据代表没开户
		isAccount() {
			api.isAccount().then((res) => {
				let Account = res.data;
				if (Account != null && Account.length != '0') {
					//获取余额
					this.getAccountQuery();
				}
				uni.setStorageSync('Account', Account);
			});
		},

		//获取电子账户信息 余额
		getAccountQuery() {
			api.getAccountQuery()
				.then((res) => {
					if (res.data) {
						this.availableBalance = res.data.available_balance;
					}
				})
				.catch((err) => {});
		},

		//获取当前积分
		// getUserCustAccntTot () {
		//   api.getUserCustAccntTot().then((res) => {
		//     let UserCustAccntTot = res.data
		//     // 需要计算对象数组中某个属性合计
		//     this.countTotal = UserCustAccntTot.reduce((c, item) => c + item.balance * 1, 0)
		//   })
		// },

		//广告
		advertisement(id) {
			if (id == 'SHARE_USER_CENTER') {
				api.advertisementinfo({
					bigClass: id
				}).then((res) => {
					this.shareUser = res.data;
				});
			}
		},

		// #ifdef MP-WEIXIN
		agreeGetUser(e) {
			let that = this;
			if (e.detail.errMsg == 'getUserInfo:ok') {
				app.initPage(true).then((res) => {
					api.userInfoUpdateByMa(e.detail).then((res) => {
						that.userInfo = res.data;
						uni.setStorageSync('user_info', that.userInfo);
						that.userInfoGet();
					});
				});
			}
		},

		getUserProfile(e) {
			// 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
			// 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
			let that = this;
			wx.getUserProfile({
				desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
				success: (detail) => {
					app.initPage(true).then((res) => {
						api.userInfoUpdateByMa(detail).then((res) => {
							that.userInfo = res.data;
							uni.setStorageSync('user_info', that.userInfo);
							that.userInfoGet();
						});
					});
				}
			});
		},
		// #endif
		// #ifdef H5
		updateUserInfo() {
			if (util.isWeiXinBrowser()) {
				//微信公众号H5，页面授权获取用户详情信息
				let appId = app.globalData.appId;
				let pages = getCurrentPages();
				let currentPage = pages[pages.length - 1];
				let route = currentPage.route;
				let redirectUri = location.href;
				let componentAppId_str = app.globalData.componentAppId ? '&component_appid=' + app.globalData.componentAppId : '';
				redirectUri = encodeURIComponent(redirectUri);
				let wx_url =
					'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
					appId +
					'&redirect_uri=' +
					redirectUri +
					componentAppId_str +
					'&response_type=code&scope=snsapi_userinfo&state=snsapi_userinfo_update#wechat_redirect';
				location.href = wx_url;
			}
		},
		//通过微信公众号网页授权更新用户信息
		userInfoUpdateByMp(parm) {
			let that = this;
			api.userInfoUpdateByMp(parm)
				.then((res) => {
					//公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
					let query = that.$Route.query;
					delete query.code;
					delete query.state;
					util.resetPageUrl(query);
					this.userInfo = res.data;
					uni.setStorageSync('user_info', this.userInfo);
					this.userInfoGet();
				})
				.catch((res) => {});
		},
		// #endif
		//获取商城用户信息
		userInfoGet() {
			api.userInfoGet().then((res) => {
				this.userInfo = res.data || {};
			});
			//分销设置
			api.distributionConfig().then((res) => {
				if (res.data) {
					this.distributionConfig = res.data;
				}
			});
		},

		orderCountAllFun() {
			api.orderCountAll().then((res) => {
				this.orderCountAll = res.data;
			});
		},

		// 跳转物流信息页面
		// toLogisticsInfo(orderIds) {
		// 	const url = `/pages/order/order-detail/index?id=${orderIds}`
		// 	uni.navigateTo({
		// 		url: url
		// 	});
		// },

		// 获取物流信息
		// async getLogisticsList() {
		// 	try {
		// 		const res = await api.logisticsDeliverList()
		// 		this.logisticsList = res.data
		// 	} catch (e) {
		// 		console.log(e)
		// 	}
		// },

		// // 倒计时结束
		// countDownDone() {
		// 	this.getLogisticsList()
		// },

		handleReCoupon(coupon) {
			if (coupon.type == 1) {
				this.checkLogin('/pages/coupon/coupon-offline-detail-plus/index?id=' + coupon.couponId);
			} else {
				this.checkLogin('/pages/coupon/coupon-detail/index?id=' + coupon.couponId);
			}
		},

		checkLogin(url) {
			if (!url) {
				uni.showToast({
					title: '开发中，敬请期待',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			if (!util.isUserLogin()) {
				uni.navigateTo({
					url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
				});
				return;
			} else {
				if (
					'/pages/signrecord/signrecord-info/index' === url ||
					'/pages/coupon/coupon-user-list/index' === url ||
					'/pages/gift/gift-card/index' === url ||
					'/pages/distribution/distribution-center/index' === url
				) {
					let type = 3;
					switch (url) {
						case '/pages/signrecord/signrecord-info/index':
							type = 3;
							break;
						case '/pages/coupon/coupon-user-list/index':
							type = 4;
							break;
						case '/pages/gift/gift-card/index':
							type = 5;
							break;
						case '/pages/distribution/distribution-center/index':
							type = 7;
							break;
					}
					getWxTemplate({ type }).then((res) => {
						// #ifdef MP
						uni.requestSubscribeMessage({
							tmplIds: res.data,
							complete: () => {
								uni.navigateTo({ url });
							}
						});
						// #endif
						// #ifndef MP
						uni.navigateTo({ url });
						// #endif
					});
					return;
				}
				uni.navigateTo({ url });
			}
		},

		// 获取快递，收藏，足迹数据
		async getRollList() {
			try {
				const res = await getRoll();
				this.rollList = res.data;
			} catch (e) {}
		},

		async getRecommendCoupon() {
			try {
				const res = await api.getRecommendCoupon();
				this.recommendCoupon = res.data;
			} catch (e) {}
		}
	}
};
</script>

<style scoped lang="scss">
.icon-settings {
	width: 40rpx;
	height: 40rpx;
}

.collect-layout {
	background-color: #f3f3f3;
	border-top-left-radius: 28rpx;
	border-top-right-radius: 28rpx;
}

.collect-item {
	height: 70rpx;
}

.order-item {
	font-size: 64rpx;
	margin-top: 0;
}

.icon-num {
	right: 20%;
	top: 8rpx;
	left: auto;
	background-color: red;
	border-radius: 50%;
	color: #ffffff;
	vertical-align: middle;
	position: absolute;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	black-space: nowrap;
	line-height: 1;
}

.pay-num {
	font-weight: bold;
	color: #000;
	line-height: 1;
}

.result-img {
	margin: 0 auto;
	margin-top: 15rpx;
	margin-bottom: 15rpx;
	overflow: hidden;
}

.img {
	/* height: 296rpx; */
	width: 100%;
	height: auto;
	border-radius: 20rpx;
	border: 1px solid red;
}

.user-text-phone {
	font-size: 26rpx !important;
}

.user-text-xl {
	color: #000000 !important;
	margin: 0rpx !important;
}

.user-font-md {
	color: #000000;
}

.user-padding {
	padding: 10rpx;
	margin-bottom: 12rpx;
}

.user-btn-sm {
	font-size: 22rpx;
	height: 36rpx;
	margin-left: 10rpx;
}

.user-line {
	margin: 14rpx auto;
	width: 699rpx;
	border-bottom: 2rpx dashed #eee;
}

.personal-information {
	flex: 1;
	display: flex;
}

.head {
	border-radius: 50%;
}

.all-orders {
	border-radius: 20rpx !important;
	margin: auto !important;
	margin-top: 15rpx !important;
}

.mine {
	margin: auto !important;
	border-radius: 10rpx !important;
}

.font-24 {
	font-size: 25rpx;
}

.more-tab {
	height: 56rpx;
	line-height: 56rpx;
}

.reduce-bg,
.vip-bg {
	background-image: url(https://img.songlei.com/live/user-center/more-tab.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	padding-right: 60rpx;
}

.vip-bg {
	background: linear-gradient(to right, #e0ba9b, #d2a28a);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	border-radius: 10rpx;
	width: 100%;
}

.icon-arrow {
	width: 10rpx;
	height: 16rpx;
}

.coupon-image {
	width: 68rpx;
	height: 68rpx;
}

.use-btn {
	width: 112rpx;
	height: 48rpx;
	background: #f32a20;
	border-radius: 24rpx;
	color: #ffffff;
}

.order-margin {
	margin-top: -10rpx;
}

.collect-tab {
	width: 198rpx;
	min-width: 198rpx;
	.row-tab-img {
		display: block;
		border-radius: 15rpx;
	}
	.tab-swiper {
		height: 300rpx;
	}
}

.margin-reduce {
	margin-top: -14rpx !important;
}

.member-bg-101 {
	background: linear-gradient(126deg, #ccac92 0%, #ff678b 0%, #fc3f6b 100%);
}

.member-bg-102 {
	background: linear-gradient(to right, #ccac92, #d2a28a);
}

.member-bg-103 {
	background: linear-gradient(126deg, #ccac92 0%, #a4a4a4 0%, #888888 100%);
}

.member-bg-108 {
	background: linear-gradient(126deg, #403e3e 0%, #191617 100%);
}

.member-color-101 {
	color: #FF678A;
	background: #FFF7F7;
	border-radius: 14rpx;
}

.member-color-102 {
	color: #A4A4A4;
	background: #F3F3F3;
	border-radius: 14rpx;
}

.member-color-103 {
	color: #BE896E;
	background: #FFF7F7;
	border-radius: 14rpx;
}

.member-color-104 {
	color: #000000;
	background: #F2F2F2;
	border-radius: 14rpx;
}
</style>
