<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">设备信息</block>
		</cu-custom>

		<view style="padding: 30rpx;width: 690rpx;">
			<view> deviceModel: {{info.deviceModel}}</view>
			<view> system: {{info.system}}</view>
			<view> deviceId: {{info.deviceId}}</view>
			<view> deviceType: {{info.deviceType}}</view>
			<view> deviceBrand: {{info.deviceBrand}}</view>
		</view>

	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				info: {}
			}
		},
		onShow() {
			let that = this;
			uni.getSystemInfo({
				success: function(e) {
					console.log("====", e)
					that.info = e
				}
			})
		}
	}
</script>

<style>

</style>