<template>
	<!-- 微页面抖音直播组件 -->
	
	<view
		class="tiktokLivingComponent"
		:class="newData.bgColor && newData.bgColor.indexOf('bg-') != -1 ? newData.bgColor : ''"
		:style="{
			marginBottom: `${newData.marginBottomSpacing}px`,
			marginTop: `${newData.marginTopSpacing}px`,
			marginLeft: `${newData.marginLeftSpacing}px`,
			marginRight: `${newData.marginRightSpacing}px`,
			overflow: 'hidden',
			position: 'relative',
			backgroundColor: `${newData.bgColor && newData.bgColor.indexOf('bg-') != -1 ? '' : newData.bgColor}`
		}"
	>
		<view
			:style="{
				backgroundSize: '100% 100%',
				'background-repeat': 'no-repeat',
				backgroundImage: `url(${newData.backgroundImg})`,
				paddingBottom: `${newData.paddingBottomSpacing}px`,
				paddingTop: `${newData.paddingTopSpacing}px`,
				paddingLeft: `${newData.paddingLeftSpacing}px`,
				paddingRight: `${newData.paddingRightSpacing}px`
			}"
		>
			<template v-if="livingList && livingList.length > 0">
				<view class="liveItem" v-for="(item, index) in livingList" :key="index">
					<view class="left">
						<image
							v-if="item.dateName && item.dateName.length > 0"
							:src="item.dateName[0]"
							 mode="widthFix"
							:style="{
								height: '96rpx',
								width: '96rpx',
								display: 'block'
							}"
s						></image>
					</view>
					<view class="right" :class="item.state == 2 ? 'lived' : item.state == 1 ? 'living' : 'willLive'" @tap.stop="handleCopy(item)">
						{{ item.state == 2 ? '直播已结束' : item.state == 1 ? '直播中...   一键复制链接' : '未开始' }}
					</view>
				</view>
			</template>
			<view v-else style="height: 200rpx; display: flex; align-items: center; justify-content: center">暂无数据</view>
		</view>
		<!-- 长按二维码 -->
		<view>
			<image :src="qrCodeUrl" style="width: 710rpx; height: 232rpx; display: block; margin: 14rpx auto 40rpx;" show-menu-by-longpress />
		</view>
	</view>
</template>

<script>
const app = getApp();
import { getTiktokLivingList } from '@/api/activity.js';
// import { solar2lunar } from 'solarlunar';
export default {
	name: 'tiktok-living',
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					pageUrl: ``,
					imageUrl: '',
					height: 100,
					hour: 10
				};
			}
		},
		isStatusBar: {
			type: Boolean,
			default: false
		}
	},

	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value,
			hour: 10,
			pagesLength: 1,
			livingList: [], //直播
			qrCodeUrl: ''
		};
	},

	created() {
		//监听当前页面栈的个数内容
		let pages = getCurrentPages();
		this.pagesLength = pages.length; //当前页面栈的个数
		this.getTiktokLivingList();
	},

	methods: {
		handleCopy(item) {
			if (item && item.urlLink && (item.state == 1 || item.state == 2)) {
				uni.setClipboardData({
					data: item.urlLink + '',
					success: () => {
						uni.showToast({
							title: `复制成功，请切换至抖音APP`,
							icon: 'none'
						});
					},
					fail: (e) => {
						console.error(e);
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						});
					}
				});
			} else if (item.state == 1 || item.state == 2) {
				uni.showToast({
					title: '暂无直播链接',
					icon: 'none'
				});
			}
		},

		getTiktokLivingList() {
			getTiktokLivingList().then((res) => {
				if (res.code == 0 && res.data.liveConfig) {
					this.qrCodeUrl = res.data.qrUrl;
					this.livingList = JSON.parse(res.data.liveConfig);
					if (this.livingList && this.livingList.length > 0) {
						this.livingList.forEach((item) => {
							// const dateTime = new Date(item.date);
							// const nextDay = new Date(this.$moment(item.date).add().add(1, 'days').format('YYYY-MM-DD'));
							// const nongliDateObj = solar2lunar(dateTime.getFullYear(), dateTime.getMonth() + 1, dateTime.getDate());
							// const nextDayObj = solar2lunar(nextDay.getFullYear(), nextDay.getMonth() + 1, nextDay.getDate());
							// if (nongliDateObj && nongliDateObj.monthCn === '正月' && nongliDateObj.dayCn === '初一') {
							// 	item.nongliDate = '春节';
							// } else if (nextDayObj && nextDayObj.monthCn === '正月' && nextDayObj.dayCn === '初一') {
							// 	item.nongliDate = '除夕';
							// } else {
							// 	item.nongliDate = nongliDateObj.dayCn;
							// }s
							const today = this.$moment();
							const specifiedDate = this.$moment(item.date);
							if (today.isSame(specifiedDate, 'day')) {
								item.state = '1'; // 直播中
							} else if (today.isAfter(specifiedDate)) {
								item.state = '2'; // 直播已结束
							} else {
								item.state = '3'; // 未开始
							}
						});
					}
				}
			});
		}
	}
};
</script>

<style lang="less" scoped>
.tiktokLivingComponent {
	.liveItem {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-top: 20rpx;
		.left {
			font-size: 30rpx;
			line-height: 40rpx;
			color: #ff544e;
			display: flex;
			flex-direction: column;
			justify-content: center;
		}

		.right {
			flex: 1;
			margin-left: 24rpx;
			height: 92rpx;
			text-align: center;
			line-height: 92rpx;
			background: #ff544e;
			border-radius: 92rpx;
			font-size: 30rpx;
			color: #fff;
		}

		.lived {
			font-size: 30rpx;
			color: #b0b0b0;
			background: #e5e5e5;
		}

		.living {
			font-size: 15px;
			color: #fff;
			background: #ff544e;
		}

		.willLive {
			font-size: 30rpx;
			color: #ff4517;
			background: #fdedd3;
		}
	}
}
</style>
