<template>
  <!-- <view :class="'bg-'+theme.backgroundColor"> -->
  <!-- 通知通告 组件 -->
  <view
    class="adsec light bg-yellow"
    :style="{
      marginBottom: `${newData.pageSpacing * 2}rpx`,
      backgroundImage: `url(${newData.bgImage})`,
      backgroundSize: `cover`,
      overflow: `hidden`,
      height: `${newData.height * 2}rpx`,
      backgroundColor: newData.background,
    }"
  >
    <!-- <view class="adsec-icon text-xl margin-right-xs margin-left-xs"
      > -->
    <text
      v-if="newData.titleIcon"
      class="margin-right-xs margin-left-xs"
      :class="newData.titleIcon"
      :style="{
        fontSize: `${newData.iconSize * 2}rpx`,
        color: `${newData.background}`,
      }"
    ></text>

    <swiper
      class="swiper_container"
      autoplay="true"
      circular="true"
      :interval="newData.interval"
    >
      <swiper-item
        v-for="(item, index) in newData.noticeList"
        :key="index"
        @tap="jumpPage(item.pageUrl)"
      >
        <view
          class="text-orange text-sm"
          :style="{
            color: newData.textColor,
            fontSize: `${newData.fontSize * 2}rpx`,
          }"
        >
          <text
            v-if="item.tag"
            class="bg-red announcement text-sm padding-lr-xs"
            :style="{
              fontSize: `${newData.fontSize * 2}rpx`,
              borderRadius: '15rpx',
              padding: '15rpx',
            }"
            >{{ item.tag }}</text
          >

          <text class="details margin-left-xs">{{ item.content }}</text>

          <!-- <image
            :src="item.imageUrl"
            :style="{
              height: `${newData.height * 2}rpx`,
              verticalAlign: 'middle',
            }"
          ></image> -->

          <text v-if="item.pageUrl" class="cuIcon-right"></text>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
const app = getApp();
import { pageUrls } from '@/components/div-components/div-base/div-page-urls.js'
export default {
  props: {
    value: {
      type: Object,
      default: function () {
        return {
          noticeList: []
        }
      }
    }
  },
  components: {
  },
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      pageUrls: pageUrls
    };
  },
  methods: {
    jumpPage (page) {
      if (page) {
        if (this.pageUrls.tabPages.indexOf(page) != -1) {
          uni.switchTab({
            url: page
          });
        } else {
          uni.navigateTo({
            url: page
          });
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
/* 公告 */
.adsec {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  padding: 10rpx 10rpx;
  height: 80rpx;
}

.adsec-icon {
  height: 80rpx;
  line-height: 80rpx;
}

.swiper_container {
  height: 80rpx;
  width: 100%;
  line-height: 80rpx;
}
</style>
