<template>
	<!-- 微页面地图导航组件 -->
	<view
		:style="{
			marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
			marginTop: `${newData.marginTopSpacing * 2}rpx`,
			marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
			marginRight: `${newData.marginRightSpacing * 2}rpx`,
			backgroundColor: `${newData.bgColor && newData.bgColor.indexOf('bg-') != -1 ? '' : newData.bgColor}`,
			paddingBottom: `${newData.paddingBottomSpacing}px`,
			paddingTop: `${newData.paddingTopSpacing}px`,
			paddingLeft: `${newData.paddingLeftSpacing}px`,
			paddingRight: `${newData.paddingRightSpacing}px`
		}"
		:class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''"
		@click="handleViewClick"
	>
		<view class="hot-zone">
			<image
				:src="newData.backgroundImg | formatImg750"
				mode="widthFix"
				:style="{
					width: `calc(100% - ${(Number(newData.marginLeftSpacing) + Number(newData.marginRightSpacing)) * 2}px  )`
				}"
			></image>
			<template v-if="newData.hotZones && newData.hotZones.length > 0">
				<view
					class="zone"
					v-for="(zone, index) in newData.hotZones"
					:key="index"
					:style="{
						width: getZoneStyle(zone.widthPer),
						height: getZoneStyle(zone.heightPer),
						top: getZoneStyle(zone.topPer),
						left: getZoneStyle(zone.leftPer)
					}"
				>
					<view class="hot-area" @tap.stop="(e) => handleZoneClick(e, zone.pageUrl)" hover-class="none">
						<text class="hot-txt" hover-class="none">{{ zone.pageName }}{{ newData.borderRadius }}</text>
					</view>
				</view>
			</template>
		</view>
	</view>
</template>

<script>
const app = getApp();
import { getActCycle } from '@/api/activity.js';
import { gotoPage } from '@/components/div-components/div-base/div-page-urls.js';
import __config from '@/config/env';
// #ifdef MP-WEIXIN
let plugin = requirePlugin('routePlan');
// #endif
export default {
	name: 'basic-advert',
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					pageUrl: ``,
					imageUrl: '',
					height: 100,
					hour: 10
				};
			}
		},
		isStatusBar: {
			type: Boolean,
			default: false
		}
	},

	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value,
			hour: 10,
			pagesLength: 1,
			sessionList: [], //场次列表
			prizeList: [] // 奖品列表
		};
	},

	created() {
		//监听当前页面栈的个数内容
		let pages = getCurrentPages();
		this.pagesLength = pages.length; //当前页面栈的个数
		this.getSessions();
	},

	methods: {
		getSessions() {
			// 获取场次信息
			if (this.newData && this.newData.actId) {
				getActCycle({
					id: this.newData.actId
				})
					.then((res) => {
						if (res && res.data) {
							this.sessionList = res.data.cycles || [];
							this.prizeList = res.data.prizes || [];
						}
					})
					.catch((e) => {});
			}
		},
		getZoneStyle(val) {
			return `${(val || 0) * 100}%`;
		},
		handleZoneClick(e, page) {
			console.log(e, page);
			if (page) {
				gotoPage(page, this.pagesLength);
			} else {
				this.handleViewClick();
			}
		},

		handleViewClick() {
			// #ifdef MP-WEIXIN
			console.log('handleViewClick===', this.newData);
			let referer = '松鼠美淘'; //调用插件的app的名称
			let endPoint = JSON.stringify({
				//终点
				name: this.newData.address,
				latitude: this.newData.latitude,
				longitude: this.newData.longitude
			});
			console.error('==endPoint===', endPoint);
			wx.navigateTo({
				url: 'plugin://routePlan/index?key=' + __config.mapKey + '&referer=' + referer + '&endPoint=' + endPoint
			});
			// #endif
		}
	}
};
</script>

<style scoped lang="scss">
.hot-zone {
	position: relative;
}

.zone {
	position: absolute;
	.hot-area {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;

		.hot-txt {
			overflow: hidden;
			text-overflow: ellipsis;
			-webkit-line-clamp: 2;
			height: 40px;
			color: rgba($color: #000000, $alpha: 0);
		}
	}
}

.sec-container {
	display: flex;
	align-items: center;
	height: 25px;
}
</style>
