<template>
	<!-- 微页面展示红包资产的页面 -->
	<view
		:style="{
			marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
			marginTop: `${newData.marginTopSpacing * 2}rpx`,
			marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
			marginRight: `${newData.marginRightSpacing * 2}rpx`,
			backgroundColor: `${newData.bgColor && newData.bgColor.indexOf('bg-') != -1 ? '' : newData.bgColor}`,
			position: 'relative'
		}"
		:class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''"
	>
		<image
			:src="newData.backgroundImg"
			:style="{
				width: '100%',
				display: 'block'
			}"
			mode="widthFix"
		></image>
		<view
			:style="{
				paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
				paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
				paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
				paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
				position: 'absolute',
				top: 0,
				left: 0,
				width: '100%'
			}"
		>
			<view
				:style="{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center'
				}"
			>
				<view style="font-size: 14px; color: #000">红包余额</view>
				<text style="font-size: 20px; color: #ee102b">{{ redPackageInfo.redPackNum || '0'}}</text>
				<text style="font-size: 12px; color: #000">，相当于 ￥{{ redPackageInfo.redPackPrice || '0' }}</text>
			</view>
			<view
				:style="{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center'
				}"
			>
				<view v-if="redPackageInfo.redPackCardNo" class="red-btn" @click="handleRedPackage">红包支付</view>
				<view v-else-if="actInfo && actInfo.actId > 0" class="red-btn" @click="handleToLottery">去抽奖</view>
                <view v-else style="margin-top: 16rpx;">暂无红包活动</view> 		
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp();
import { getRedPackInfo } from '@/api/gift';
import { getRedPacketPop } from '@/api/activity';
import __config from '@/config/env';

export default {
	name: 'basic-advert',
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					pageUrl: ``,
					imageUrl: '',
					height: 100,
					hour: 10
				};
			}
		},
		isStatusBar: {
			type: Boolean,
			default: false
		}
	},

	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value,
			hour: 10,
			pagesLength: 1,
			redPackageInfo: {},
			actInfo: {}
		};
	},

	created() {
		//监听当前页面栈的个数内容
		let pages = getCurrentPages();
		this.pagesLength = pages.length; //当前页面栈的个数
		this.getRedPackInfo();
		this.getRedPacketPop();
	},

	methods: {
		
		getRedPacketPop() {
			getRedPacketPop().then((res) => {
				if (res.data) {
					this.actInfo = res.data;
				}
			});
		},

		getRedPackInfo() {
			getRedPackInfo().then((res) => {
				if (res.data) {
					this.redPackageInfo = res.data;
				}
			});
		},
		
		handleToLottery(){
			uni.navigateTo({
				url: "/pages/activity/lottery/index?id="+ this.actInfo.actId
			})
		},

		handleRedPackage() {
			uni.setStorageSync('cardNo', this.redPackageInfo.redPackCardNo);
			let isActive = uni.getStorageSync('paypaymentuserpassword')?.isActive;
			// isActive 0 没有开通去开通
			if (isActive == '0') {
				uni.navigateTo({
					url: `/pages/gift/set-password/code/index`
				});
				return;
			} else {
				uni.navigateTo({
					url: `/pages/gift/payment-code/index`
				});
			}
		}
	}
};
</script>

<style scoped lang="scss">
.hot-zone {
	position: relative;
}

.zone {
	position: absolute;
	.hot-area {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;

		.hot-txt {
			overflow: hidden;
			text-overflow: ellipsis;
			-webkit-line-clamp: 2;
			height: 40px;
			color: rgba($color: #000000, $alpha: 0);
		}
	}
}

.sec-container {
	display: flex;
	align-items: center;
	height: 25px;
}

.red-btn {
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 209rpx;
	height: 53rpx;
	background: linear-gradient(90deg, #f42634 0%, #ce1a26 100%);
	border-radius: 27rpx;
	margin-top: 6rpx;
	border-radius: 8px;
}
</style>
