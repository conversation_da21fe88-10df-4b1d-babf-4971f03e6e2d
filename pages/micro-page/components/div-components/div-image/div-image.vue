<template>
	<!-- 单图显示组件 -->
	<div-base-navigator :pageUrl="newData.pageUrl" style="background: #ffffff"
		:style="{ height: `${newData.height * 2}rpx` }">
		<view :style="{
        position: 'relative',
        height: `${newData.height}px`,
        marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
        marginTop: `${newData.marginTopSpacing * 2}rpx`,
        marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
        marginRight: `${newData.marginRightSpacing * 2}rpx`,
        paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
        paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
        paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
        paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
      }" :class="
	    newData.background&& newData.background.length>0 && newData.background!=undefined
	      ? newData.background
	      : ''
	  ">
			<image v-if="getFileType(newData.imageUrl) == 'image'" :src="newData.imageUrl | formatImg750"
				style="width: 100%; height: 100%; margintop: '-1px'" mode="aspectFill"
				:show-menu-by-longpress="newData.canLongPress==1"></image>

			<video v-else-if="!isVideo" id="myVideo" autoplay loop :muted="muted" :src="newData.imageUrl"
				@error="videoErrorCallback" enable-danmu danmu-btn :controls="false" style="width: 100%; height: 100%"
				object-fit="cover">
			</video>
			<cover-view v-if="getFileType(newData.imageUrl) == 'video'&&!isVideo" class="text-xl close-icon"
				style="position: absolute; bottom: 20rpx; right: 20rpx; z-index: 1000">
				<cover-image @tap.stop="handleMuted" v-if="muted"
					src="http://slshop-file.oss-cn-beijing.aliyuncs.com/client/mp/jingyin.png"
					style="width: 40rpx;height: 40rpx;"></cover-image>
				<cover-image @tap.stop="handleMuted" v-else
					src="http://slshop-file.oss-cn-beijing.aliyuncs.com/client/mp/yinliang.png"
					style="width: 40rpx;height: 40rpx;"></cover-image>
			</cover-view>
		</view>
	</div-base-navigator>
</template>

<script>
	const app = getApp();
	import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
	export default {
		name: 'basic-image',
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {
						pageUrl: ``,
						imageUrl: '',
						height: 100,

					}
				}
			},
			// 控制video false显示 true不显示
			//兼容APP中原生video显示层级过高问题
			isVideo: {
				type: Boolean,
				default: false
			}
		},
		components: {
			divBaseNavigator
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				muted: true
			};
		},
		watch: {
			value: {
				handler(newVal, oldVal) {
					if (newVal.isMute == 0) {
						this.muted = false;
					} else {
						this.muted = true;
					}
					console.error("==this.muted =========",this.muted );
				},
				immediate: true,
				deep: true
			}
		},
		methods: {
			getFileType(fileUrl) {
				if (!fileUrl) return "";
				const fileName = fileUrl.split("//")[1].split("/");
				const file = fileName[fileName.length - 1].split(".")[1];
				if (file == "jpg" || file == "png" || file == "gif" || file == "webp") {
					return "image";
				} else if (
					file == "mp4" ||
					file == "ogg" ||
					file == "flv" ||
					file == "avi" ||
					file == "rmvb" ||
					file == "mov" ||
					file == "wmv"
				) {
					return "video";
				} else {
					return undefined;
				}
			},

			videoErrorCallback: function(e) {
				// this.newData.imageUrl = this.newData.imageUrl
				// uni.showModal({
				// 	content: e.target.errMsg,
				// 	showCancel: false
				// })
			},
			handleMuted() {
				this.muted = !this.muted;
			}
		}
	}
</script>

<style scoped lang="scss">
</style>