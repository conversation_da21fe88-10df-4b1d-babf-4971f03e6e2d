<template>
	<!-- 微页面红包组件 -->
	<view
		:style="{
			backgroundColor: `${newData.background}`,
      backgroundSize: '100% 100%',
      width: '100%',
      minHeight: '100%',
      backgroundRepeat: 'no-repeat',
      backgroundImage: `url(${newData.backgroundImg})`,
			marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
			marginTop: `${newData.marginTopSpacing * 2}rpx`,
			marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
			marginRight: `${newData.marginRightSpacing * 2}rpx`,
			paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
      paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
      paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
      paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
			position: 'relative'
		}"
		:class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''"
	>
		<!-- 顶部形象图 -->
		<image
			v-if="newData && newData.topBackgroundImg"
			:src="newData.topBackgroundImg | formatImg750"
			mode="widthFix"
			:style="{
				width: '100%',
        minHeight: '100%',
        display: 'block',
			}"
		></image>


		<!-- 奖品背景图 -->
		<view 
			v-if="newData && newData.prizeBackgroundImg"
			:style="{
				backgroundSize: '100% 100%',
				width: '100%',
				minHeight: '100%',
				backgroundRepeat: 'no-repeat',
				backgroundImage: `url(${newData.backgroundImg})`,
			}"
		>
			<!-- 奖品列表 -->
			<scroll-view
				scroll-x="true"
				:style="{
					width: '100%',
					whiteSpace: 'nowrap'
				}"
			>
				<view class="flex">
					<view v-for="(item, index) in prizeList" class="prize-item">
						<image class="prize" :src="item.url"></image>
						<view class="txt">{{ item.prizeName }}</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 红包形象图 -->
		<view class="hot-zone">
			<image
				:src="newData.redPacketImg | formatImg750"
				mode="widthFix"
				:style="{
					width: `calc(100% - ${(Number(newData.marginLeftSpacing) + Number(newData.marginRightSpacing)) * 2}px  )`
				}"
			></image>
			<template v-if="newData.hotZones && newData.hotZones.length > 0">
				<view
					class="zone"
					v-for="(zone, index) in newData.hotZones"
					:key="index"
					:style="{
						width: getZoneStyle(zone.widthPer),
						height: getZoneStyle(zone.heightPer),
						top: getZoneStyle(zone.topPer),
						left: getZoneStyle(zone.leftPer)
					}"
				>
					<view class="hot-area" @click="(e) => handleZoneClick(e, zone.pageUrl)" hover-class="none">
						<text class="hot-txt" hover-class="none">{{ zone.pageName }}{{ newData.borderRadius }}</text>
					</view>
				</view>
			</template>
		</view>

		<!-- 直播排期背景图 -->
		<view 
			v-if="newData && newData.schedulingBackgroundImg"
			:style="{
				backgroundSize: '100% 100%',
				width: '100%',
				minHeight: '100%',
				backgroundRepeat: 'no-repeat',
				backgroundImage: `url(${newData.schedulingBackgroundImg})`,
			}"
		>
			<!-- 直播排期列表 -->
			<view></view>
		</view>

		<!-- 规则背景图 -->
		<image
			v-if="newData && newData.ruleBackgroundImg"
			:src="newData.ruleBackgroundImg | formatImg750"
			mode="widthFix"
			:style="{
				width: '100%',
        minHeight: '100%',
        display: 'block',
			}"
		></image>
	</view>
</template>

<script>
const app = getApp();
import { getActCycle } from '@/api/activity.js';
import { gotoPage } from '@/components/div-components/div-base/div-page-urls.js';
export default {
	name: 'basic-advert',
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					pageUrl: ``,
					imageUrl: '',
					height: 100,
					hour: 10
				};
			}
		},
		isStatusBar: {
			type: Boolean,
			default: false
		}
	},

	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value,
			hour: 10,
			pagesLength: 1,
			sessionList: [], //场次列表
			prizeList: [] // 奖品列表
		};
	},

	created() {
		//监听当前页面栈的个数内容
		let pages = getCurrentPages();
		this.pagesLength = pages.length; //当前页面栈的个数
		this.getSessions();
	},

	methods: {
		getSessions() {
			// 获取场次信息
			if (this.newData && this.newData.actId) {
				getActCycle({
					// id: this.newData.actId
					// 测试数据
					id: '1874770337913139202'
				})
					.then((res) => {
						if (res && res.data) {
							this.sessionList = res.data.cycles|| [];
							this.prizeList = res.data.prizes || [];
						}
					})
					.catch((e) => {});
			}
		},
		getZoneStyle(val) {
			return `${(val || 0) * 100}%`;
		},
		handleZoneClick(e, page) {
			console.log(e, page);
			if (page) {
				gotoPage(page, this.pagesLength);
			} else {
				// uni.showToast({
				//   title: '没有配置链接地址',
				//   icon: 'none',
				//   duration: 2000
				// });
			}
		}
	}
};
</script>

<style scoped lang="scss">
.hot-zone {
	position: relative;
}

.zone {
	position: absolute;

	.hot-area {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;

		.hot-txt {
			overflow: hidden;
			text-overflow: ellipsis;
			-webkit-line-clamp: 2;
			height: 40px;
			color: rgba($color: #000000, $alpha: 0);
		}
	}
}

.sec-container {
	display: flex;
	align-items: center;
	height: 25px;
}

.prize-item {
	width: 190rpx;
	height: 244rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-left: 20rpx;
	background-color: #fff;
	border-radius: 20rpx;

	.prize {
		width: 170rpx;
		height: 170rpx;
		margin: 10rpx auto 30rpx;
	}

	.txt {
		font-weight: 500;
		font-size: 28rpx;
		color: #120a5a;
	}
}
</style>
