<template>
	<view style="position: relative;">
		<view v-if="pageDivData.pageComponent.togetherComponent&&pageDivData.pageComponent.togetherComponent.bgImage"
			:style="{
		position:'absolute',
		left:0,
		top:0,
		width:'100%',		
		height:(pageDivData.pageComponent.togetherComponent.height*2)+'rpx',
		backgroundSize: 'cover',
		backgroundImage: pageDivData.pageComponent.togetherComponent ? `url(${pageDivData.pageComponent.togetherComponent.bgImage})` : '',
	}">
		</view>
		<home-skeleton :loading="loading"></home-skeleton>
		<template v-if="pageDivData.pageComponent.componentsList && pageDivData.pageComponent.componentsList.length>0">
			<div-componentlist :componentsList="pageDivData.pageComponent.componentsList" @getpage="handleGetMicroPage"
				@openRulePopup="openRulePopup" :isBack="isBack" ref="divComponentlist" :showScreen="showScreen"
				:showMinutes="showMinutes" :pageSelectPoi="pageSelectPoi" />
		</template>
		<template v-else>
			<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
				<block slot="content">微页面</block>
			</cu-custom>
			<view style="font-size: 28rpx; margin-top: 200rpx; color: #8799A3; display: flex; justify-content: center;">
				该页面未搭建或者未发布
			</view>
		</template>
		<div-advert-dialog v-if="showDialog" v-model="pageDivData.pageComponent.pageAdvertDialog" />
		<div-birthday-dialog v-if="showBirthDayDialog" v-model="birthdayData" />
		<!-- <div-couponnum-dialog v-if="couponNum > 0" v-model="couponNum" /> -->
		<div-distribution-dialog v-if="showDistributionDialog" />
		<!-- 新客专享 -->
		<div-new-costomer-dialog v-if="showNewcustomerDialog" :showDialog="showNewcustomerDialog"
			:newCustomerData="newCustomerData" />

		<div-pop v-if="pageDivData.pageComponent.pageAdvertDialog"
			:pageAdvertDialog="pageDivData.pageComponent.pageAdvertDialog" />
		<!-- 大转盘 -->
		<pop-lottery v-if="showLotteryDialog"
			:lotteryId="newCustomerLotteryId || pageDivData.pageComponent.pageAdvertDialog.lottery.lotteryId" />
		<div-rule-dialog v-if="showRuleDialog" :setData="showRuleData" @closeRulePopup="closeRulePopup" />
		<div-competion-dialog v-if="showCompetionDialog" v-model="competionInfo" />
		<div-gold-medal-dialog v-if="showGoldMedalDialog" v-model="goldMedalInfo" />
	</view>
</template>

<script>
	const app = getApp();
	import api from "@/utils/api";
	import {
		distributionUserInitialLogin
	} from "@/api/distribution.js"
	import homeSkeleton from "@/components/base-skeleton/page-skeleton/home-skeleton";
	import divComponentlist from "../div-componentslist/index";
	import divAdvertDialog from "@/components/div-components/div-advert-dialog/div-advert-dialog";
	import divCompetionDialog from "@/components/div-components/div-comptetion-dialog/div-comptetion-dialog";
	import divBirthdayDialog from "@/components/div-components/div-birthday-dialog/div-birthday-dialog";
	import divNewCostomerDialog from "@/components/div-components/div-newcostomer-dialog/div-newcostomer-dialog";
	import divDistributionDialog from "@/components/div-components/div-distribution-dialog/div-distribution-dialog";
	// import divCouponnumDialog from "@/components/div-components/div-couponnum-dialog/div-couponnum-dialog";
	import divPop from "@/components/div-components/div-pop/index.vue";
	import PopLottery from '@/components/lottery/pop-lottery.vue'
	import divRuleDialog from "@/components/div-components/div-rule-dialog/div-rule-dialog";
	import __config from "@/config/env";
	import divGoldMedalDialog from "@/components/div-components/div-gold-medal-dialog/index.vue";
	import {
		EventBus
	} from '@/utils/eventBus.js'
	// 新人专享
	import {
		getNewcustomerPop,
		getCouponnum
	} from 'api/newcostomer.js';
	// 竞猜活动首页弹框
	import {
		guessPop,
		getGoldPopResult,
		getRedPacketPop
	} from 'api/activity.js';
	const util = require("utils/util.js");
    import microPageStack from '@/public/js_sdk/sensors/globalStack';

	export default {
		components: {
			homeSkeleton,
			divComponentlist,
			divAdvertDialog,
			divBirthdayDialog,
			divDistributionDialog,
			divPop,
			PopLottery,
			divNewCostomerDialog,
			divRuleDialog,
			divCompetionDialog,
			// divCouponnumDialog,
			divGoldMedalDialog
		},
		data() {
			return {
				loading: true,
				theme: app.globalData.theme, //全局颜色变量
				loadmore: true,
				pageDivData: {
					pageComponent: {
						componentsList: [],
					},
				},
				pageName: "",
				loadmore3: true, // 自定义页面的加载状态
				shares: {
					title: '',
					imageUrl: '',
					path: ''
				},
				id: '',
				showDialog: false,
				showBirthDayDialog: false,
				showDistributionDialog: false,
				showLotteryDialog: false,
				birthdayData: {},
				currentPageParams: null,
				lotteryId: '1727263244893495298', // 大转盘活动 Id
				// 新人专享
				newCustomerLotteryId: '', // 新人专享抽奖 Id
				showNewcustomerDialog: false, // 是否显示新人专享弹窗
				newCustomerData: null, // 新人专享商品推荐数据
				showRuleData: '',
				showRuleDialog: false,
				showCompetionDialog: false,
				competionInfo: {},
				couponNum:0,//劵链路 0不用弹，大于0弹
				showGoldMedalDialog:false,
				goldMedalInfo: {},
			};
		},

		props: {
			divType: {
				type: String,
				default: "home", //home 平台首页  micro 微页面
			},
			microId: {
				type: String | Number,
				default: "",
			},

			isBack: {
				type: Boolean,
				default: true,
			},

			dialogIsShowing: {
				type: Boolean,
				default: true,
			},

			showScreen: {
				type: Boolean,
				default: false,
			},
			showMinutes: {
				type: Number,
				default: -1,
			},
			//设置对象方便更新
			pageSelectPoi: {
				type: Object,
				default: () => ({})
			}
		},
		watch: {
			microId: {
				handler(val, oldVal) {
					//普通的watch监听
					if (val > 0) {
						this.loading = true;
						this.loadData();
					}
				},
				immediate: true
			},
		},

		created() {
			EventBus.$on("refreshNewCustomerDialog", async () => {
				if (app.isUser() == 1) {
					const pageAdvertDialog = this.pageDivData.pageComponent.pageAdvertDialog;
					// 新人专享
					if (pageAdvertDialog && pageAdvertDialog.newcustomerinfo && pageAdvertDialog
						.newcustomerinfo.isShow == 1) {
						await this.getNewcustomerPopData(pageAdvertDialog.newcustomerinfo.newcustomerId)
					}
				}

			})
		},

		mounted() {
			// this.checkLoadData();
			//获取路由信息
			const pages = getCurrentPages()
			//获取当前路由
			let nowPage = pages[pages.length - 1]
			this.shares.path = nowPage.route
			this.shareMessage();

			this.$EventBus.$on("handleNyRedPacket", (val) => {
				if (val == 2) {
					getRedPacketPop().then(res => {
						if (res.data.isRafflePop == 1) {
							this.showLotteryDialog = true;
							this.newCustomerLotteryId = res.data.actId;
						}
					})
				}
			});
		},

		beforeDestroy() {
			this.$EventBus.$off("handleNyRedPacket");
			microPageStack.pop();
		},

		methods: {
			pullDownRefresh() {
				// 显示顶部刷新图标
				uni.showNavigationBarLoading();
				this.refresh(); // 隐藏导航栏加载框
				uni.hideNavigationBarLoading(); // 停止下拉动作
				uni.stopPullDownRefresh();

			},
			shareMessage: function() {
				let title = this.shares.title ? this.shares.title : this.pageName;
				let imageUrl = this.shares.imageUrl ? this.shares.imageUrl : ''
				const userInfo = uni.getStorageSync("user_info");
				const userCode = userInfo ? "&sharer_user_code=" + userInfo.userCode : "";
				//标题
				this.shares.title = title;
				//路径
				this.shares.path = `${this.shares.path}?id=${this.microId}${userCode}`;
				//图片
				this.shares.imageUrl = imageUrl
			},

			checkLoadData() {
				if (this.divType == "home" || (this.microId && this.divType == "micro")) {
					this.loadData();
				}
			},

			loadData() {
				api.pagedevisePage(this.microId).then(async (res) => {
					let pageDivData = res.data;

					if (pageDivData) {
						this.pageName = pageDivData.pageName;
						microPageStack.push(pageDivData.id,this.pageName);
						this.pageDivData = pageDivData;
						let pageComponent = pageDivData.pageComponent
						this.id = pageDivData.id
						let pageAdvertDialog = pageDivData.pageComponent.pageAdvertDialog;
						if (pageComponent && pageAdvertDialog && pageAdvertDialog.shareShow != 0) {
							this.shares.title = pageAdvertDialog.shareTitle
							this.shares.imageUrl = pageAdvertDialog.shareImageUrl
						}
						if (pageAdvertDialog && pageAdvertDialog.canShare) {
							this.$emit("canMicroPageShare", pageAdvertDialog.canShare)
						}

						// 新人专享
						if (pageAdvertDialog && pageAdvertDialog.newcustomerinfo && pageAdvertDialog
							.newcustomerinfo.isShow == 1) {
							if (app.isUser() == 1) {
								await this.getNewcustomerPopData(pageAdvertDialog.newcustomerinfo
									.newcustomerId)
							}
						}

						if (pageAdvertDialog && pageAdvertDialog.lottery && pageAdvertDialog.lottery.isShow ==
							1) {
							this.showLotteryDialog = true;
						}
						if (app.isUser() == 1) {
							await this.getCouponnumPopData()
						}
					}
					this.loading = false;
					const userInfo = uni.getStorageSync('user_info')
					if (this.divType == "home" && userInfo && userInfo.erpCid) {
						distributionUserInitialLogin().then(res => {
							if (res.code == 0 && res.data && res.data.isShow == 1) {
								this.showDistributionDialog = true;
							}
						}).catch(e => {
							console.error(e)
						});

						api.getBirthDayDialogInfo({
							channel: 'SONGSHU',
							custId: userInfo.erpCid
						}).then(res => {
							if (res.code == 0) {
								this.showBirthDayDialog = true;
								this.birthdayData = res.data
							} else {
								this.dealShowDialog(pageDivData)
							}
						}).catch(e => {
							this.dealShowDialog(pageDivData)
						})

						this.guessPopDialog()

						this.goldMedalDialog()
						return
					}
					this.dealShowDialog(pageDivData);
				});
			},

			guessPopDialog() {
				guessPop().then(res => {
					//测试数据
					// res = {"code":0,"msg":null,"data":{"myGuess":[{"id":"1806237548195151873","matchName":"1","teamA":{"id":"1805134445801222145","name":"奥地利","img":"https://juhe.oss-cn-hangzhou.aliyuncs.com/api_image/616/euro2024/D13.png","score":"2"},"teamB":{"id":"1805124252786831361","name":"法国法国法国噶过","img":"https://juhe.oss-cn-hangzhou.aliyuncs.com/api_image/616/euro2024/D14.png","score":"2"},"matchStartTime":"00:00:00","matchStartDate":"2024-06-27","guessDetail":" 平局","guessResult":0,"matchType":0,"isEnd":1,"isRaffle":0,"weekday":"周四","marketId":"1805410109635502081"},{"id":"1806238110567432194","matchName":"2","teamA":{"id":"1805124252786831361","name":"法国法国法国噶过","img":"https://juhe.oss-cn-hangzhou.aliyuncs.com/api_image/616/euro2024/D14.png","score":"1"},"teamB":{"id":"1805801635516272642","name":"测试1","img":"https://img.songlei.com/1/material/699090bc-3d88-4996-9696-e2b935aaabe6.png","score":"1"},"matchStartTime":"01:01:01","matchStartDate":"2024-06-27","guessDetail":" 平局","guessResult":1,"matchType":0,"isEnd":1,"isRaffle":1,"weekday":"周四","marketId":"1805410109635502081"},{"id":"1806241665537867778","matchName":"3","teamA":{"id":"1805801635516272642","name":"测试1","img":"https://img.songlei.com/1/material/699090bc-3d88-4996-9696-e2b935aaabe6.png","score":"1"},"teamB":{"id":"1805801689358553089","name":"测试2—改","img":"https://img.songlei.com/1/material/961d89ff-fe61-4154-a57b-8c6f2903b7ec.jpg","score":"1"},"matchStartTime":"02:02:02","matchStartDate":"2024-06-27","guessDetail":" 测试1胜","guessResult":2,"matchType":0,"isEnd":1,"isRaffle":0,"weekday":"周四","marketId":"1805410109635502081"}],"raffleTotalNum":1,"raffleNum":1,"joinNum":3,"winNum":1,"matchDate":"2024-06-27","weekDay":"周四","isPop":1,"marketId":"1805410109635502081"},"ok":true}
					if (res.data&&res.data.myGuess&&res.data.myGuess.length>0) {
						this.showCompetionDialog = res.data.isPop==1;
						this.competionInfo = res.data;
					}
				})
			},

			goldMedalDialog(){
				getGoldPopResult().then(res => {
					// //测试数据
					// res.data = {
					// 		id: "1811240235832283137",
					// 		goldNum: 11,
					// 		myGuess: {
					// 				"type": 0,
					// 				"min": 1,
					// 				"max": 10
					// 		},
					// 		guessResult: 1,
					// 		isRaffle: 1,
					// 		raffleNum: 1,
					// 		matchDate: "2024-07-14",
					// 		marketId: "1810928643338678274"
					// }
					if (res.data) {
						this.showGoldMedalDialog = true;
					}
					this.goldMedalInfo = res.data;
				})
			},

			dealShowDialog(pageDivData) {
				this.showDialog = this.dialogIsShowing && pageDivData && pageDivData.pageComponent &&
					pageDivData.pageComponent.pageAdvertDialog && pageDivData.pageComponent.pageAdvertDialog
					.showAdvertDialog == 1;
				if (this.showDialog) {
					this.showDialog = this.showDialog && util.isShowAdvert(pageDivData.pageComponent
						.pageAdvertDialog.intervalTime, pageDivData.pageComponent.pageAdvertDialog
						.intervalNumber, 'advert')
				}
				if (this.showDialog) {} else {
					uni.$emit('showBottomBar')
				}
			},

			refresh() {
				this.loadmore = true;
				this.goodsList = [];
				this.goodsListNew = [];
				this.goodsListHot = [];
				if (this.currentPageParams && this.currentPageParams.microId) {
					this.handleGetMicroPage(this.currentPageParams)
				} else {
					this.loadData();
				}
			},

			handleGetMicroPage(params) {
				if (params.index == 0) {
					this.currentPageParams = null;
					this.loadData();
					return
				}
				this.currentPageParams = params;
				if (params.microId > 0) {
					// this.loading=true;
					api.pagedevisePage(params.microId).then((res) => {
						let result = res.data;
						if (result) {
							let temp = [];
							for (let j = 0; j < this.pageDivData.pageComponent.componentsList.length; j++) {
								temp.push(this.pageDivData.pageComponent.componentsList[j])
								if (this.pageDivData.pageComponent.componentsList[j].id == params.componentId) {
									break;
								}
							}
							if (result.pageComponent.componentsList && result.pageComponent.componentsList.length >
								0 && result.pageComponent.componentsList[0].componentName == 'headdivComponent') {
								result.pageComponent.componentsList.splice(0, 1)
							}
							temp = temp.concat(result.pageComponent.componentsList);
							this.pageDivData.pageComponent.componentsList = temp;
						}
						this.loading = false;
					});
				}
			},

			// 下拉加载
			reachBottom() {
				console.log("调用页面组件");
				this.$refs.divComponentlist.reachBottom();
			},

			// 新人专享
			async getNewcustomerPopData(id) {
				const res = await getNewcustomerPop({
					id
				});
				this.newCustomerData = res?.data || null;
				if (this.newCustomerData?.activityType == 1 && this.newCustomerData?.newCustomerGoodsList?.length) {
					this.showNewcustomerDialog = true;
					this.showLotteryDialog = false;
				} else if (this.newCustomerData?.activityType == 2) {
					this.showNewcustomerDialog = false;
					this.newCustomerLotteryId = this.newCustomerData.actId;
					this.showLotteryDialog = true;
				} else {
					this.showNewcustomerDialog = false;
					this.showLotteryDialog = false;
				}
			},

			// 劵链路
			async getCouponnumPopData() {
				const res = await getCouponnum({
					type:1
				});
				this.couponNum = res?.data || null;
				// this.couponNum = 5
			},

			openRulePopup(info) {
				this.showRuleData = info
				this.showRuleDialog = true
			},
			closeRulePopup() {
				this.showRuleDialog = false
			}
		},
	};
</script>
<style>
	@import '@/components/div-components/index.css';
</style>