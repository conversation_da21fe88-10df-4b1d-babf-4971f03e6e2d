<template>
	<view :style="{ backgroundColor: newData.background }" style="padding: 10upx 20upx;">
		<view class="flex justify-center">
			<view style="flex: 1;">
				<view class="search-form round flex" :style="{
            height: `40px`,
            lineHeight: '100%',
            marginRight:' 20rpx',
            marginLeft:' 0',
            border:' 1rpx solid #EAEAEA',
            background: '#fff',
          }" style="align-items: center;padding-left: 30upx;">
					<input class="uni-input" v-model="eventId" type="text" confirm-type="search"
						@confirm="activeAccntNo" placeholder="请输入兑换码" :style="{ flex: 1 }" />
					<image :style="{  maxHeight: `48rpx`,width:`4rpx`}" mode="aspectFit"
						src="https://img.songlei.com/share/scan.png" style="height: 60rpx;width: 20rpx;"></image>
					<text @tap="scanCode" class="cuIcon-scan"
						style="font-size: 32rpx;padding: 0rpx 20rpx 0rpx 16rpx;"></text>
				</view>
			</view>
			<view class="activation-btn" @click="activeAccntNo" :style="{
          backgroundColor: newData.btn_background,
          color: newData.color
        }">兑换</view>
		</view>
		<view :style="{ color: newData.detail_color }" style="padding: 0 10upx;">{{ newData.detail }}</view>
	</view>
</template>

<script>
	import util from '@/utils/util.js'
	import {
		activeAccntNo
	} from '@/api/coupon.js';
	// #ifdef APP-PLUS
	const mpaasScanModule = uni.requireNativePlugin("Mpaas-Scan-Module");
	// #endif
	export default {
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {
						background: `#ffffff`,
						btn_background: '#CDAD90',
						detail_color: '#999999',
						detail: '',
						btn_color: '#999999'
					}
				}
			}
		},
		watch: {
			value: {
				handler(newData) {
					this.newData = newData;
				},
				deep: true,
				immediate: true
			}
		},
		data() {
			return {
				eventId: '',
				newData: {}
			}
		},
		methods: {
			scanCode() {
				// 允许从相机和相册扫码
				// #ifdef MP-WEIXIN
				uni.scanCode({
					scanType: ["barCode", "qrCode"], //所扫码的类型 barCode	一维码 qrCode	二维码
					success: (res) => {
						if (res.result) {
							const code = res.result;
							if (code.startsWith('https://shopapi.songlei.com/slshop-h5/ma/couponActivation')) {
								const id = util.UrlParamHash(code.split('?')[1], 'id');
								this.eventId = id;
								this.activeAccntNo()
							} else {
								uni.showToast({
									title: `二维码不正确`,
								});
							}
						} else {
							uni.showToast({
								title: `请重新扫描`,
							});
							return false;
						}
					}
				})
				// #endif   

				// #ifdef APP-PLUS
				// 允许从相机和相册扫码
				mpaasScanModule.mpaasScan({
					// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
					'scanType': ['qrCode', 'barCode'],
					// 是否隐藏相册，默认false不隐藏
					'hideAlbum': false
				}, (res) => {
					if (res.resp_code == 1000 && res.resp_message == 'success') {
						const code = res.resp_result; //result	所扫码的内容
						if (code) {
							if (code.startsWith('https://shopapi.songlei.com/slshop-h5/ma/couponActivation')) {
								const id = util.UrlParamHash(code.split('?')[1], 'id');
								this.eventId = id;
								this.activeAccntNo()
							} else {
								uni.showToast({
									title: `二维码不正确`,
								});
							}
						} else {
							uni.showToast({
								title: `未识别到内容`,
								icon:"none",
								duration: 2000
							});
						}
					} else {
						// uni.showToast({
						// 	title: `未识别到信息`,
						// 	duration: 2000
						// });
					}
				})
				// #endif   

			},
			activeAccntNo() {
				const that = this;
				const user_info = uni.getStorageSync('user_info');
				const {
					eventId
				} = this;
				const {
					id,
					erpCid
				} = user_info;
				if (!id || !erpCid) {
					this.showModel('您当前还未登录，请先登录后在使用该功能')
					return;
				}
				if (!eventId) {
					this.showModel('请先输入券编码')
					return;
				}
				activeAccntNo(eventId).then(res => {
					const {
						code,
						msg
					} = res;
					if (+code === 0) {
						// this.showModel('激活成功');
						uni.showModal({
							title: '提示',
							content: '激活成功',
							cancelText: '确定',
							confirmText: '去使用',
							success(res) {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/coupon/coupon-user-list/index?navTabCur=1'
									})
								}
							}
						})
						this.eventId = '';
					} else {
						this.showModel(msg);
					}
				})
			},
			showModel(content) {
				uni.showModal({
					title: '提示',
					content,
					showCancel: false
				})
			},

			//报错说方法没定义，先定义出来
			searchClick() {

			}
		}
	}
</script>

<style scoped>
	.activation-btn {
		width: 180upx;
		height: 40px;
		font-size: 28upx;
		display: flex;
		justify-content: center;
		align-items: center;
		/* background: linear-gradient(90deg, #CDAD90 0%, #CDA185 100%); */
		margin: 0;
		color: #fff;
		border-radius: 40upx;
	}
</style>