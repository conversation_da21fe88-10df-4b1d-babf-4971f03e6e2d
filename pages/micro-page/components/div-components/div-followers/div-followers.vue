<template>
  <!-- 涨粉组件 -->
    <view
      :style="{
        marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
        marginTop: `${newData.marginTopSpacing * 2}rpx`,
        marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
        marginRight: `${newData.marginRightSpacing * 2}rpx`,
      }"
    >
      <official-account></official-account>
    </view>
</template>

<script>
const app = getApp();
export default {
  name: 'div-followers',
  props: {
    value: {
      type: Object,
      default: function () {
        return {
        }
      }
    }
  },
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
    };
  },
  methods: {
  }
}
</script>

<style scoped lang="scss">
</style>
