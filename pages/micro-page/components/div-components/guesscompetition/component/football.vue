<template>
  <view>
    <view class="competition-rule">
			<view class="rule" @click="handleShowRule">活动规则</view>
			<view class="result" @click="handeMyCompetion">竞猜结果</view>
		</view>

		<scroll-view scroll-x="true" class="prize-layout" :style="{
		        backgroundSize: 'cover',
		        backgroundImage: newData.bgImageTop?`url(${newData.bgImageTop})`:`url(https://img.songlei.com/live/bgImageTop.png)`,
		      }">
			<template v-if="prizeList&&prizeList.length>0">
				<view v-for="(item, index) in prizeList" :key="index" class="prize-item">
					<image mode="aspectFill" :src="item.url"></image>
					<view class="prize-txt">{{item.prizeName}}</view>
				</view>
			</template>
		</scroll-view>

		<view :style="{
	  		   backgroundSize: 'cover',
	  		   backgroundImage: newData.bgImageBottom?`url(${newData.bgImageBottom})`:`url(https://img.songlei.com/live/bgImageBottom.png)`,
	  		   height: '534rpx'
	  }">
			<view class="times-layout">
				<view @click="handlePre" :style="{color: matchIndex==0?'#aaa':'a#000'}">
					{{'<'}}上一场
				</view>
				<view>{{matchList[matchIndex].teamA.name||''}}VS{{matchList[matchIndex].teamB.name||''}}</view>
				<view @click="handleNext" :style="{color: matchIndex>=matchList.length-1?'#aaa':'#000'}">下一场></view>
			</view>
			<view class="end-txt"> {{matchList[matchIndex].guessEndTime?matchList[matchIndex].guessEndTime+'结束竞猜':'/'}}
			</view>
			<view class="vs-layout">
				<image mode="aspectFill" style="background-color: lightgray;" :src="matchList[matchIndex].teamA.img">
				</image>
				<view class="vs-txtlayout">
					<view class="vs-txt">VS</view>
					<view class="vs-txt">{{matchList[matchIndex]?'点击选项进行竞猜':'暂无比赛'}}</view>
				</view>
				<image mode="aspectFill" style="background-color: lightgray;" :src="matchList[matchIndex].teamB.img">
				</image>
			</view>
			<view class="guess-layout" v-if="matchList[matchIndex].isGuess==1">
				<view class="win" @click="handleChoose('1')">{{matchList[matchIndex].teamA.name}}胜</view>
				<view class="draw" @click="handleChoose('0')">平局</view>
				<view class="lose" @click="handleChoose('2')">{{matchList[matchIndex].teamB.name}}胜</view>
			</view>
			<view v-else>
				<view class="guess-layout" style="margin-top: 20rpx;margin-bottom: 2rpx;">
					<view class="win-enable">{{matchList[matchIndex].teamA.name||''}}胜</view>
					<view class="draw-enable">平局</view>
					<view class="lose-enable">{{matchList[matchIndex].teamB.name||''}}胜</view>
				</view>
				<view style="text-align: center;font-weight: 800;font-size: 22rpx;color: #FF1606;"> *您已经参与竞猜,请耐心等待竞猜结果
				</view>
			</view>
		</view>

		<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''"
			style="width: 100vw;height: 100vh;">
			<view class="cu-dialog"
				style="background-color: transparent;position: fixed;left: 50%;top: 50%;transform: translate(-50%, -50%);width: auto;">
				<view class="cuIcon-roundclose"
					style="margin-bottom: 30rpx;font-size: 60rpx;color: #fff;line-height: 1;"
					@tap.stop="handleCloseSuccess"></view>
				<view
					:style="{ width: `100%`,display: 'flex',justifyContent:' flex-end',marginBottom:'20rpx',position:'relative',transform: 'translateX(-50%)',left: '50%'} ">
					<view class="modal-content">
						<view>{{matchList[matchIndex].matchEndTime}} 以后</view>
						<view>打开【松鼠美淘】小程序首页可查竞猜结果</view>
						<view class="btn-layout">
							<view class="red-btn" @click="handeMyCompetion">我的竞猜</view>
							<div-base-navigator :pageUrl="newData.pageUrl" hover-class="none">
								<view class="blue-btn">逛逛会场</view>
								<!-- @click="handeToHome" -->
							</div-base-navigator>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="cu-modal" catchtouchmove='true' v-if="showRuleModal" :class="showRuleModal?'show':''"
			style="width: 100vw;height: 100vh;">
			<view class="cu-dialog"
				style="background-color: transparent;position: fixed;left: 50%;top: 50%;transform: translate(-50%, -50%);width: auto;">

				<view class="rule-modal-content">
					每天参与欧洲杯比赛竞猜，赢欧洲杯门票、国家队球衣、大牌品牌券！</br>
					活动时间：6.29-7.15, </br>
					猜中用户请于7月15日前，前往我的竞猜完成抽奖，否则视为放弃本次权益。
				</view>

				<view class="cuIcon-roundclose" style="margin-top: 20rpx;font-size: 60rpx;color: #fff;line-height: 1;"
					@tap.stop="handleClose">
				</view>
			</view>
		</view>
  </view>
</template>

<script>
import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
import { senTrack, getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'
import {
		getCompetitionPrize,
		getCompetitionGame,
		getCompetitionBetting
	} from "@/api/activity.js"
export default {
	components: {
			divBaseNavigator
		},

    props: {
			newData: {
				type: Object | null,
				default: function() {
					return {}
				}
			},
		},

    data() {
			return {
				prizeList: [],
				//当前是哪个一场索引
				matchIndex: 0,
				matchList: [],
				showModal: false,
				showRuleModal: false,
				// 神策埋点
				trackParams: {
					page_name: getCurrentTitle(0),
					page_level: '一级',
					activity_id: "",
					activity_name: "",
					activity_type_first: "营销活动",
					activity_type_second: "足球竞猜",
				},
				enterTime: 0,
				matchInfo: {},
			};
		},

    mounted() {
			// 组件刚创建时记录进入时间（不推荐，可能太早）
			this.enterTime = new Date().getTime();
      getCompetitionPrize().then(res => {
				if (res && res.data) {
					this.prizeList = res.data;
				}
			})
			getCompetitionGame().then(res => {
				if (res && res.data) {
					if (res.data.historyMatchs && res.data.historyMatchs.length > 0) {
						this.matchList = [
							...res.data.historyMatchs
						]
					}
					if (res.data.matchInfo) {
						this.matchInfo = res.data.matchInfo
						senTrack("ActivityPageView", {
							...this.trackParams,
							activity_id: res.data.matchInfo.id,
							activity_name: res.data.matchInfo.name,
							forward_source: getCurrentTitle(0),
						});
						this.matchList.push(res.data.matchInfo);
						this.matchIndex = this.matchList.length - 1;
					}
					if (res.data.followMatchs && res.data.followMatchs.length > 0) {
						this.matchList = [
							...this.matchList,
							...res.data.followMatchs
						]
					}
				}
			})
    },

		beforeDestroy() {
			const leaveTime = new Date().getTime()
			// 组件销毁前记录离开时间
			const durationSeconds = Math.floor((leaveTime - this.enterTime) / 1000);
			console.log("组件生命周期内停留时长（秒）：", durationSeconds);
			if (this.matchInfo) {
				senTrack("ActivityPageLeave", {
					...this.trackParams,
					activity_id: this.matchInfo.id,
					activity_name: this.matchInfo.name,
					forward_source: getCurrentTitle(0),
					stay_duration: durationSeconds
				});
			}
		},

    methods: {
			handlePre() {
				console.log("===handlePre=====", this.matchIndex, this.matchList.length - 1)
				if (this.matchIndex > 0) {
					this.matchIndex--;
				}
			},

			handleNext() {
				console.log("===handleNext=====", this.matchIndex, this.matchList[this.matchIndex])
				if (this.matchIndex < this.matchList.length - 1) {
					this.matchIndex++;
				}
			},

			handleChoose(type) {
				getCompetitionBetting({
					matchId: this.matchList[this.matchIndex].id,
					supportTeam: type
				}).then(res => {
					if (res && res.ok) {
						senTrack("ActivityClick", {
							...this.trackParams,
							activity_id: this.matchList[this.matchIndex].id,
							activity_name: this.matchList[this.matchIndex].name,
						});
						this.showModal = true;
						this.$set(this.matchList, this.matchIndex, {
							...this.matchList[this.matchIndex],
							isGuess: 0
						});
					}
				})
			},

			handeMyCompetion() {
				uni.navigateTo({
					url: "/pages/activity/mycompetition/index"
				})
			},

			handeToHome() {
				this.handleClose();
				// uni.switchTab({
				// 	url: "/pages/home/<USER>"
				// })
			},

			handleShowRule() {
				this.showRuleModal = true;
			},

			handleClose() {
				this.showRuleModal = false;
			},

			handleCloseSuccess() {
				this.showModal = false;
			}
		}
}
</script>

<style scoped lang="scss">
	.prize-layout {
		height: 192rpx;
		padding: 38rpx 40rpx 14rpx 80rpx;
		white-space: nowrap;
	}

	.prize-item {
		width: 126rpx;
		height: 126rpx;
		display: inline-block;
		margin: 16rpx;
		background-image: url(https://img.songlei.com/live/competition/prize-item-bg.png);
		background-size: 100% auto;
		background-repeat: no-repeat;

		image {
			width: 126rpx;
			height: 86rpx;
		}

		.prize-txt {
			font-size: 16rpx;
			line-height: 20rpx;
			color: #FFFFFF;
			text-align: center;
		}
	}

	.times-layout {
		display: flex;
		justify-content: space-between;
		padding: 90rpx 24rpx 20rpx 24rpx;
		font-weight: 800;
		font-size: 26rpx;
		color: #000000;
		line-height: 24rpx;
	}

	.end-txt {
		font-weight: 500;
		font-size: 22rpx;
		color: #9A5F1E;
		display: flex;
		justify-content: center;
		margin-top: 10rpx;
	}

	.vs-layout {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 30rpx 110rpx 0;

		image {
			width: 110rpx;
			height: 110rpx;
		}
	}

	.vs-txtlayout {
		.vs-txt {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			text-align: center;
			color: #000000;
			font-weight: 600;
			font-size: 25rpx;
		}
	}

	.guess-layout {
		display: flex;
		justify-content: space-between;
		margin: 30rpx 40rpx;

		.win,
		.lose,
		.draw {
			background-image: url(https://img.songlei.com/live/competition/win-bg.png);
			background-size: 100% auto;
			background-repeat: no-repeat;
			width: 224rpx;
			height: 94rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 800;
			font-size: 30rpx;
			color: #FFFFFF;
		}

		.lose {
			background-image: url(https://img.songlei.com/live/competition/lose-bg.png);
		}

		.draw {
			background-image: url(https://img.songlei.com/live/competition/draw-bg.png);
		}

		.win-enable,
		.lose-enable,
		.draw-enable {
			background-image: url(https://img.songlei.com/live/competition/competition-win-enable.png);
			background-size: 100% auto;
			background-repeat: no-repeat;
			width: 224rpx;
			height: 94rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 800;
			font-size: 30rpx;
			color: #FFFFFF;
		}

		.lose-enable {
			background-image: url(https://img.songlei.com/live/competition/competition-lose-enable.png);
		}

		.draw-enable {
			background-image: url(https://img.songlei.com/live/competition/competition-draw-enable.png);
		}

	}

	.modal-content {
		background-image: url(https://img.songlei.com/live/competition/choose-bg.png);
		background-size: 100% auto;
		background-repeat: no-repeat;
		width: 600rpx;
		height: 572rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 34rpx;
		padding-top: 250rpx;

		.btn-layout {
			display: flex;
			justify-content: space-between;
			width: 100%;

			.red-btn,
			.blue-btn {
				width: 254rpx;
				height: 70rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 50rpx;
				background-image: url(https://img.songlei.com/live/competition/red-btn.png);
				background-size: 100% auto;
				background-repeat: no-repeat;
				font-weight: 800;
				font-size: 30rpx;
				color: #FFFFFF;
			}

			.blue-btn {
				background-image: url(https://img.songlei.com/live/competition/blue-btn.png);
			}
		}
	}

	.competition-rule {
		width: 100%;
		height: 372rpx;
		background-image: url(https://img.songlei.com/live/competition/competition-rule-bg2.png);
		background-size: 100% auto;
		background-repeat: no-repeat;
		position: relative;

		.rule,
		.result {
			position: absolute;
			top: 20rpx;
			right: 0;
			width: 120rpx;
			height: 52rpx;
			line-height: 52rpx;
			background: #FFFFFF;
			border-radius: 26rpx 0 0 26rpx;
			opacity: 0.7;
			font-weight: 500;
			font-size: 26rpx;
			color: #000000;
			text-align: center;
		}


		.result {
			top: 88rpx;
			text-align: center;
		}
	}

	.rule-modal-content {
		background-image: url(https://img.songlei.com/live/competition/competion-rule.png);
		background-size: 100% auto;
		background-repeat: no-repeat;
		width: 600rpx;
		height: 572rpx;
		padding: 34rpx;
		padding-top: 276rpx;
		font-family: PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #000000;
		line-height: 44rpx;
		text-align: left;
	}
</style>