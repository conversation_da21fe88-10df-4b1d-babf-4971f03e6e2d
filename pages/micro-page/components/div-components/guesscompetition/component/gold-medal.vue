<template>
  <view>
    <view class="competition-rule" style="padding-top: 312rpx;">
			<scroll-view scroll-x="true" class="prize-layout" :style="{
							backgroundSize: 'cover',
							width: '96%',
    					margin: '0 auto',
							backgroundImage: newData.bgImageTop?`url(${newData.bgImageTop})`:`url(https://img.songlei.com/live/gold-medal-rize.png)`,
						}">
				<template v-if="prizeList&&prizeList.length>0">
					<view v-for="(item, index) in prizeList" :key="index" class="prize-item">
						<image mode="aspectFill" :src="item.url"></image>
						<view class="prize-txt">{{item.prizeName}}</view>
					</view>
				</template>
			</scroll-view>

			<view :style="{
						backgroundSize: 'cover',
						width: '95%',
    				margin: '0 auto',
						marginTop: '15rpx',
						paddingTop: '10rpx',
						backgroundImage: newData.bgImageBottom?`url(${newData.bgImageBottom})`:`url(https://img.songlei.com/live/gold-medal.png)`,
						height: '600rpx'
				}">
				<view class="flex justify-end" style="width: 95%;">
					<view class="rule" style="margin-right: 10rpx;" @click="handleShowRule">活动规则</view>
					<view class="result" @click="handeMyCompetion">我的竞猜</view>
				</view>

				<view class="padding-lr-sm padding-tb-sm">
					<view class="flex justify-between" style="padding: 0 58rpx;">
						<view class="text-center text-bold text-df" style="color: #263989;">
							{{goldMedalInfo.lastResultDate?`${goldMedalInfo.lastResultDate}赛况`:'昨日赛况'}}
						</view>

						<view class="text-center text-bold text-df" style="color: #263989;">数量</view>
						<view class="text-center text-bold text-df" style="color: #263989;">竞猜结果</view>
					</view>

					<view class="flex">
						<view>
							<view 
								class="flex text-black text-sm"
								style="width: 468rpx;height: 54rpx;line-height: 54rpx;background: #FFFFFF;border-radius: 19rpx; margin-bottom: 8rpx;"
								>
								<view class="padding-left-lg">昨日赛事结果</view>
								<view style="padding-left: 138rpx;">{{ goldMedalInfo.yesterdayGoldNum || '— —' }}</view>
							</view>

							<view 
								class="flex text-black text-sm"
								style="width: 468rpx;height: 54rpx;line-height: 54rpx;background: #FFFFFF;border-radius: 19rpx;"
								>
								<view class="padding-left-lg">昨日我的竞猜</view>
								<view style="padding-left: 138rpx;">
									<view v-if="goldMedalInfo.yesterdayGuess">
										<view v-if="goldMedalInfo.yesterdayGuess.type=='0'">{{ goldMedalInfo.yesterdayGuess.min }}-{{ goldMedalInfo.yesterdayGuess.max }}</view>
										<view v-if="goldMedalInfo.yesterdayGuess.type=='1'">{{ goldMedalInfo.yesterdayGuess.max }}以上</view>
									</view>
									<view v-else>{{ '— —' }}</view>
								</view>
							</view>
						</view>

						<view 
							style="
								width: 177rpx;
								height: 115rpx;
								line-height: 115rpx;
								text-align: center;
								background: #FFFFFF;
								margin-left: 8rpx;
								border-radius: 19rpx;"
							:style="{
								color:goldMedalInfo.yesterdayIsWin==1?'#FF0000':'#B3B3B3'
							}"
							>
							{{goldMedalInfo.yesterdayIsWin=='1'?'恭喜猜中':goldMedalInfo.yesterdayIsWin=='2'?'未猜中':'未出结果'}}
						</view>
					</view>

					<view class="flex" style="margin: 24rpx 0 15rpx;">
						<view class="text-bold text-df" style="padding-left:52rpx">今日竞猜</view>
						<view 
							class="text-bold text-df padding-left" 
							v-if="goldMedalInfo&&goldMedalInfo.matchDate">
								猜{{dateStr(goldMedalInfo.matchDate)}}的金牌数量
						</view>
					</view>

					<view 
						class="flex text-black text-sm" 
						style="
							width: 650rpx;
							height: 93rpx;
							line-height: 93rpx;
							background: #FFFFFF;
							border-radius: 27rpx;"
						>
						<view style="padding-left: 35rpx;padding-right: 32rpx;">您的竞猜</view>
						<view style="border-left: 2rpx dashed grey;height: 38rpx;padding: 0 22rpx;margin-top: 25rpx;"></view>
						<view class="flex" @click="showGoldMedalOption">
							<view style="padding-right: 312rpx;">
								<view v-if="myGuess==null">请选择</view>
								<view v-else>
									<view v-if="myGuess.type =='0'">{{ myGuess.min }}-{{ myGuess.max }}</view>
									<view v-if="myGuess.type =='1'">{{ myGuess.max }} 以上</view>
								</view>
							</view>

							<view class="cuIcon-right" style="color: #cccccc;"></view>
						</view>
					</view>

					<view class="text-center" style="margin-top: 22rpx;">
						<button
							v-if="goldMedalInfo.myGuess==null"
							class="cu-btn"
							@click="handleChoose()"
							style="
								width: 410rpx;
								height: 78rpx;
								color: #ffffff;
								background: linear-gradient(0deg, #F51100 0%, #FF5C5C 100%);
								box-shadow: 0rpx 5rpx 0rpx 0rpx #9D0C00, 0rpx 7rpx 0rpx 0rpx #6B0800;
								border-radius: 27rpx;
								border-image: linear-gradient(0deg, #720900, #F51606, #FFFFFF) 1 1;"
						>确认</button>

						<button
							v-else
							class="cu-btn"
							disabled="true"
							style="
								width: 410rpx;
								height: 78rpx;
								color: #ffffff;
								background: linear-gradient(0deg, #A8A8A8 0%, #C3C2C2 100%);
								box-shadow: 0rpx 5rpx 0rpx 0rpx #696969, 0rpx 7rpx 0rpx 0rpx #4D4D4D;
								border-radius: 27rpx;
								border-image: linear-gradient(0deg, #505050, #A8A8A7, #FFFFFF) 1 1;"
						>已参与竞猜</button>
					</view>

					<view class="text-center" style="color: #263989;margin-top: 25rpx;">
						<view class="text-xss">确认后不可修改</view>
						<view class="text-xss">{{dateStr(goldMedalInfo.guessResultDate)}}10点以后，打开【松鼠美淘小程序首页】可查竞猜结果</view>
					</view>
				</view>
				
			</view>
		</view>

		<!--金牌选着弹框-->
		<view class="cu-modal" catchtouchmove='true' v-if="showGoldMedals" :class="showGoldMedals?'show':''"
			style="width: 100vw;height: 100vh;">
			<view class="cu-dialog"
				style="background-color: transparent;position: fixed;left: 50%;top: 50%;transform: translate(-50%, -50%);width: auto;">
				<view class="flex justify-end">
					<view class="cuIcon-roundclose"
						style="margin: 0rpx;font-size: 60rpx;color: #fff;line-height: 1;margin: 15rpx 0;"
						@tap.stop="handleCloseSuccess">
					</view>
				</view>

				<view
					:style="{ width: `100%`,display: 'flex',justifyContent:' flex-end',marginBottom:'20rpx',position:'relative',transform: 'translateX(-50%)',left: '50%'} ">
					<view 
						style="width: 600rpx;
							height: 572rpx;
							display: flex;
							flex-direction: column;
							padding: 34rpx;
							background: #ffffff;
							border-radius: 5%;"
					>
						<view>
							<view 
								class="text-xsm" 
								style="width: 410rpx;
									margin: 0 auto;
									color: #0A1954;
									padding: 20rpx 40rpx;
									border-bottom: 2rpx solid #ccc;"
							>
								请选择您竞猜的奖牌数
							</view>
						</view>

						<view class="padding-top">
							<scroll-view 
								class="medal"
								scroll-y="true"
							>
								<view 
									v-for="(item, index) in goldMedalInfo.guessOption" 
									:key="index"
									style="width: 391rpx;
										height: 78rpx;
										line-height: 78rpx;
										margin: 10rpx auto;
										background: linear-gradient(0deg, #FFD834 0%, #FFF666 100%);
										border-radius: 39rpx;"
									>
										<view v-if="item.type=='0'" @click="handeMedal(item)">{{ item.min }}-{{ item.max }}</view>
										<view v-if="item.type=='1'" @click="handeMedal(item)">{{ item.max }} 以上</view>
								</view>
							</scroll-view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 成功参与弹框 --> 
		<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''"
			style="width: 100vw;height: 100vh;">
			<view class="cu-dialog"
				style="background-color: transparent;position: fixed;left: 50%;top: 50%;transform: translate(-50%, -50%);width: auto;">

				<view class="flex justify-end">
					<view class="cuIcon-roundclose"
						style="margin: 0rpx;font-size: 60rpx;color: #fff;line-height: 1;margin: 15rpx 0;"
						@tap.stop="handleSuccess">
					</view>
				</view>

				<view
					:style="{ width: `100%`,display: 'flex',justifyContent:' flex-end',marginBottom:'20rpx',position:'relative',transform: 'translateX(-50%)',left: '50%'} ">
					<view class="modal-content">
						<view style="padding: 70rpx 0 62rpx;">
							<view class="text-lg" style="color: #FF0000;">恭喜您</view>
							<view class="text-lg" style="color: #FF0000;">参与竞猜成功！</view>
						</view>

						<view style="color:#263989">{{dateStr(goldMedalInfo.guessResultDate)}}10点以后，打开松鼠美淘小程序</view>
						<view style="color:#263989">首页可查竞猜结果</view>

						<view class="margin-top-xl" style="width: 466rpx;height: 1rpx;border: 2rpx solid #EEEEEE;"></view>
						<view class="btn-layout">
							<view class="red-btn" @click="handeMyCompetion">我的竞猜</view>
							<div-base-navigator :pageUrl="newData.pageUrl" hover-class="none">
								<view class="blue-btn">逛逛会场</view>
							</div-base-navigator>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 活动规则 -->
		<view class="cu-modal" catchtouchmove='true' v-if="showRuleModal" :class="showRuleModal?'show':''"
			style="width: 100vw;height: 100vh;">
			<view class="cu-dialog"
				style="background-color: transparent;position: fixed;left: 50%;top: 50%;transform: translate(-50%, -50%);width: auto;">

				<view class="rule-modal-content">
					<view 
						class="text-lg" 
						style="width: 410rpx;
							margin: 0 auto;
							text-align: center;
							color: #0A1954;
							padding: 20rpx 40rpx;
							border-bottom: 2rpx solid #ccc;"
					>
						活动规则
					</view>

					<view class="margin-tb-lg margin-lr-sm">
						每天参与奥运会中国队金牌榜竞猜，赢安踏冠军服、国家队球衣、大牌品牌券！</br>
						活动时间：7.26-8.11 </br>
						猜中用户请于8月15日前，前往我的竞猜完成抽奖，否则视为放弃本次权益。
					</view>

					<view 
						@tap.stop="handleClose"
						style="width: 391rpx;
							height: 78rpx;
							line-height: 78rpx;
							text-align: center;
							margin: 10rpx auto;
							background: linear-gradient(0deg, #FFD834 0%, #FFF666 100%);
							border-radius: 39rpx;"
						>
							关闭
					</view>
				</view>
			</view>
		</view>
  </view>
</template>

<script>
import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
import { senTrack, getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'

import {
		getActprizeGameInfo,
		getGoldGameInfo,
		getUserGoldGame,
	} from "@/api/activity.js"
export default {
	components: {
			divBaseNavigator
		},

    props: {
			newData: {
				type: Object | null,
				default: function() {
					return {}
				}
			},
		},

    data() {
			return {
				prizeList: [],//奖品列表
				goldMedalInfo:{},//金牌活动
				showModal: false,
				showRuleModal: false,
				showGoldMedals: false,
				myGuess:null,//我的竞猜选项
				// 神策埋点
				trackParams: {
					page_name: getCurrentTitle(0),
					page_level: '一级',
					activity_id: "",
					activity_name: '金牌竞猜',
					activity_type_first: "营销活动",
					activity_type_second: "金牌竞猜",
				},
				enterTime: 0,
			};
		},

    mounted() {
			// 组件刚创建时记录进入时间（不推荐，可能太早）
			this.enterTime = new Date().getTime();
			// 获取竞猜奖品列表
			this.getGoldGameInfo()
			// 获取竞猜金牌信息
			this.getActprizeGameInfo()
    },

		beforeDestroy() {
			const leaveTime = new Date().getTime()
			// 组件销毁前记录离开时间
			const durationSeconds = Math.floor((leaveTime - this.enterTime) / 1000);
			console.log("组件生命周期内停留时长（秒）：", durationSeconds);
			senTrack("ActivityPageLeave", {
				...this.trackParams,
				activity_id: this.goldMedalInfo.id,
				forward_source: getCurrentTitle(0),
				stay_duration: durationSeconds
			});
		},

    methods: {
			// 金牌选项
			handeMedal(e){
				console.log("e=====>",e);
				this.myGuess = e
				this.handleCloseSuccess()
			},
			
			// 时间格式化
			dateStr(dateStr){
				if(dateStr){
					// 将字符串日期转换成Date对象
					const date = new Date(dateStr);
					// 格式化输出日期
					const year = date.getFullYear();
					const month = (date.getMonth() + 1).toString().padStart(2, '0');
					const day = date.getDate().toString().padStart(2, '0');
					const formattedDate = `${month}月${day}日`;
					return formattedDate
				}
				return "— —"
			},

			// 获取竞猜金牌信息
			getGoldGameInfo(){
				getGoldGameInfo().then(res => {
					if (res && res.data) {
						console.log("res.data===>",res.data);
						this.goldMedalInfo = res.data
						senTrack("ActivityPageView", {
							...this.trackParams,
							activity_id: this.goldMedalInfo.id,
							forward_source: getCurrentTitle(0),
						});
						this.myGuess = res.data.myGuess
					}
				})
			},

			// 获取竞猜奖品列表
			getActprizeGameInfo(){
				getActprizeGameInfo().then(res => {
					if (res && res.data) {
						this.prizeList = res.data;
					}
				})
			},

			// 提交用户竞猜
			handleChoose() {
				senTrack("ActivityClick", {
					...this.trackParams,
					activity_id: this.goldMedalInfo.id,
				});
				getUserGoldGame({
					id: this.goldMedalInfo.id,
					guessOption: this.myGuess
				}).then(res => {
					if (res && res.ok) {
						this.showModal = true;
						this.getGoldGameInfo()
					}
				})
			},

			// 我的竞猜
			handeMyCompetion() {
				uni.navigateTo({
					url: "/pages/activity/my-gold-medal/index"
				})
			},

			// 活动规则
			handleShowRule() {
				this.showRuleModal = true;
			},

			// 活动规则关闭
			handleClose() {
				this.showRuleModal = false;
			},

			// 金牌弹框关闭
			handleCloseSuccess() {
				this.showGoldMedals = false;
			},

			// 成功参与弹框
			handleSuccess(){
				this.showModal = false;
			},

			// 金牌选项 已参与不要弹框
			showGoldMedalOption(){
				if (this.goldMedalInfo.myGuess==null) {
					this.showGoldMedals = true
				}
			}
		}
}
</script>

<style scoped lang="scss">
	.medal{
		height: 358rpx;
	}

	.prize-layout {
		height: 157rpx;
		padding: 14rpx 10rpx 10rpx 80rpx;
		white-space: nowrap;
	}

	.prize-item {
		width: 126rpx;
		height: 127rpx;
		display: inline-block;
		margin: 5rpx;
		background-image: url(https://img.songlei.com/live/competition/prize-item-bg.png);
		background-size: 100% auto;
		background-repeat: no-repeat;

		image {
			width: 126rpx;
			height: 86rpx;
		}

		.prize-txt {
			font-size: 16rpx;
			line-height: 20rpx;
			color: #FFFFFF;
			text-align: center;
		}
	}

	.modal-content {
		background:#FFFFFF;
		border-radius: 15rpx;
		width: 600rpx;
		height: 572rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 34rpx;

		.btn-layout {
			display: flex;
			justify-content: space-between;
			width: 100%;

			.red-btn,
			.blue-btn {
				background:#FFFFFF;
				border-radius: 15rpx;
				width: 254rpx;
				height: 72rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 50rpx;
				background-image: url(https://img.songlei.com/live/competition/gold-medal-btn.png);
				background-size: 100% auto;
				background-repeat: no-repeat;
				font-size: 30rpx;
				color: #263989;
			}

			.blue-btn {
				background-image: url(https://img.songlei.com/live/competition/gold-medal-btn.png);
			}
		}
	}

	.competition-rule {
		width: 100%;
		padding-bottom: 20rpx;
		background-image: url(https://img.songlei.com/live/competition/gold-medal.png);
		background-size: 100% auto;
		background-repeat: no-repeat;
		position: relative;

		.rule,
		.result {
			width: 136rpx;
			height: 42rpx;
			line-height: 42rpx;
			background: #FFFFFF;
			border-radius: 26rpx;
			font-weight: 500;
			font-size: 26rpx;
			color: #263989;
			text-align: center;
		}


		.result {
			top: 88rpx;
			text-align: center;
		}
	}

	.rule-modal-content {
		background:#FFFFFF;
		border-radius: 15rpx;
		width: 600rpx;
		height: 572rpx;
		padding: 34rpx;
		font-family: PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #000000;
		line-height: 44rpx;
		text-align: left;
	}
</style>