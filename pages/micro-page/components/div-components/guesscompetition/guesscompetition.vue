<template>
	<!-- 竞猜组件 -->
	<view :style="{
        marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
        marginTop: `${newData.marginTopSpacing * 2}rpx`,
        marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
        marginRight: `${newData.marginRightSpacing * 2}rpx`,
        paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
        paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
        paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
        paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
      }" :class="
	    newData.background&& newData.background.length>0 && newData.background!=undefined
	      ? newData.background
	      : ''
	  ">
		
		<!-- 足球竞猜 -->
		<view v-if="newData.guessingType=='football'">
			<Football :newData="newData"/>
		</view>

		<!-- 金牌竞猜 -->
		<view v-if="newData.guessingType=='goldMedal'">
			<GoldMedal :newData="newData"/>
		</view>
	</view>
</template>

<script>
	import Football from './component/football.vue'
	import GoldMedal from './component/gold-medal.vue'
	const app = getApp();

	export default {
		name: 'guesscompetition',
		components: {
			Football,
			GoldMedal
		},

		props: {
			value: {
				type: Object | null,
				default: function() {
					return {
						pageUrl: ``,
						imageUrl: '',
						height: 100,
					}
				}
			},
		},

		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
			};
		},

		created() {
		},
	}
</script>