<template>
  <!-- 顶部导航栏组件 -->
  <!-- <div-base-navigator v-if="isStatusBar" :pageUrl="newData.pageUrl"> -->
  <cu-custom
    v-if="isStatusBar"
    :bgColor="newData.backgroundColor"
    :bgImage="newData.bgImage"
    :isBack="isBack"
    :hideMarchContent="true"
  >
    <block slot="content">
      <text :style="{
	         color: newData.color,
	         fontSize:
	           newData.fontSize * 2 + 'rpx',
	       }">{{newData.title }}</text>
    </block>
  </cu-custom>
  <!-- </div-base-navigator> -->

  <div-base-navigator
    v-else
    :pageUrl="newData.pageUrl"
  >
    <view :style="{
			backgroundColor:newData.backgroundColor,
			minHeight:'80rpx',
			backgroundSize:'cover', 
			backgroundImage: `url(${newData.bgImage})`,
			 display: 'flex',
			        justifyContent: 'center',
			        alignItems: 'center',
		}">
      <text :style="{
           color: newData.color,
           fontSize:
            newData.fontSize * 2 + 'rpx',
         }">{{ newData.title }} </text>
    </view>
  </div-base-navigator>
</template>

<script>
import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
const app = getApp();
export default {
  name: "div-head",
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {
          backgroundColor: ``,
          pageUrl: '',
          bgImage: "",
        };
      },
    },
    isStatusBar: {
      type: Boolean,
      default: false
    },
    isBack: {
      type: Boolean,
      default: true,
    }

  },
  components: {
    divBaseNavigator,
  },
  data () {
    return {
      newData: this.value,
    };
  },
  methods: {

  },
};
</script>

<style scoped lang="scss">
</style>
