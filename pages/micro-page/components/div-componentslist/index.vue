<template>
	<view>
		<block v-for="(temp, index) in componentsLists" :key="index">
			<template v-if="temp.componentName === 'headdivComponent'">
				<div-head v-model="temp.data" :isStatusBar="index == 0" :isBack="isBack" />
			</template>
			<template v-else-if="temp.componentName === 'goodsCategoryComponent'">
				<div-goods-category :pageSelectPoi="pageSelectPoi" @getpage="(e) => getpage(e, temp.id)" :isStatusBar="index == 0" v-model="temp.data"></div-goods-category>
			</template>
			<template v-if="temp.componentName === 'searchComponent'">
				<div-search v-model="temp.data" :isStatusBar="index == 0" :isBack="isBack" />
			</template>
			<template v-else-if="temp.componentName === 'advertComponent'">
				<div-advert v-model="temp.data" :isStatusBar="index == 0"></div-advert>
			</template>
			
			<!-- <template v-else-if="temp.componentName === 'imageComponent'">
				<div-image v-model="temp.data"></div-image>
			</template> -->

			<template v-else-if="temp.componentName === 'articleListComponent'">
				<div-article-list v-model="temp.data"></div-article-list>
			</template>
			<!-- #ifdef MP-WEIXIN -->
			<template v-else-if="temp.componentName === 'livingListComponent'">
				<div-living-list v-model="temp.data"></div-living-list>
			</template>
			<!-- #endif -->
			<template v-else-if="temp.componentName === 'imageListComponent'">
				<div-image-list v-model="temp.data"></div-image-list>
			</template>

			<template v-else-if="temp.componentName === 'swiperComponent'">
				<div-swiper v-model="temp.data"></div-swiper>
			</template>
			<template v-else-if="temp.componentName === 'navButtonComponent'">
				<div-nav-button v-model="temp.data"></div-nav-button>
			</template>
			<template v-else-if="temp.componentName === 'noticeComponent'">
				<div-notice v-model="temp.data"></div-notice>
			</template>
			<template v-else-if="temp.componentName === 'titleTextComponent'">
				<div-title-text v-model="temp.data"></div-title-text>
			</template>

			<template v-else-if="showHeavy2 && temp.componentName === 'goodsComponent'">
				<div-goods v-model="temp.data"></div-goods>
			</template>
			<template v-else-if="showHeavy1 && temp.componentName === 'goodsRowComponent' && !showScreen">
				<div-goods-row v-model="temp.data"></div-goods-row>
			</template>
			<template v-else-if="temp.componentName === 'shopComponent'">
				<div-shop v-model="temp.data"></div-shop>
			</template>
			
			<template v-else-if="temp.componentName === 'activateComponent'">
				<div-activate v-model="temp.data"></div-activate>
			</template>

			<template v-else-if="temp.componentName === 'couponComponent'">
				<div-coupon v-model="temp.data"></div-coupon>
			</template>
			<template v-else-if="temp.componentName === 'bargainComponent'">
				<div-bargain v-model="temp.data"></div-bargain>
			</template>
			<template v-else-if="temp.componentName === 'grouponComponent'">
				<div-groupon v-model="temp.data"></div-groupon>
			</template>
			<template v-else-if="temp.componentName === 'seckillComponent'">
				<div-seckill v-model="temp.data"></div-seckill>
			</template>

			<template v-else-if="showHeavy2 && temp.componentName === 'goodsGroupingComponent'">
				<div-goods-groups v-model="temp.data" ref="qwe"></div-goods-groups>
			</template>
			<template v-else-if="temp.componentName === 'newPersonComponent'">
				<div-new-person v-model="temp.data" @openRulePopup="openRulePopup(temp.data)" />
			</template>

			<template v-if="temp.componentName === 'guesscompetitionComponent'">
				<div-guesscompetition v-model="temp.data" />
			</template>
			
			<template v-if="temp.componentName === 'tiktokLivingComponent'">
				<divTiktokLiving v-if="temp.componentName === 'tiktokLivingComponent'" v-model="temp.data" />
			</template>
			
			<template v-if="temp.componentName === 'showRedPackage'">
				<showRedPackage v-if="temp.componentName === 'showRedPackage'" v-model="temp.data" />
			</template>
			
			<!-- <template v-if="temp.componentName === 'redPackageComponent'">
				<divRedPackage v-if="temp.componentName === 'redPackageComponent'" v-model="temp.data" />
			</template> -->
			
			<!-- #ifdef MP-WEIXIN -->
			<songleiMap v-if="temp.componentName === 'songleiMapComponent'" v-model="temp.data" />
			<followers v-if="temp.componentName === 'followersComponent'" />
			<!-- #endif -->
		</block>
	</view>
</template>

<script>
const app = getApp();
import divSearch from '@/components/div-components/div-search/index.vue';
import divHead from '../div-components/div-head/div-head.vue';
import divImage from '../div-components/div-image/div-image.vue';
import divImageList from '@/components/div-components/div-image-list/div-image-list.vue';
import divAdvert from '@/components/div-components/div-advert/index.vue';
import divSwiper from '@/components/div-components/div-swiper/div-swiper.vue';
import divNavButton from '@/components/div-components/div-nav-button/div-nav-button.vue';
import divNotice from '../div-components/div-notice/div-notice.vue';
import divTitleText from '../div-components/div-title-text/div-title-text.vue';
import divGoods from '@/components/div-components/div-goods/index.vue';
import divShop from '../div-components/div-shop/div-shop.vue';
import divGoodsRow from '@/components/div-components/div-goods-row/div-goods-row.vue';
import divGoodsCategory from '@/components/div-components/div-goods-category/index.vue';
import divCoupon from '../div-components/div-coupon/div-coupon.vue';
import divBargain from '../div-components/div-bargain/div-bargain.vue';
import divGroupon from '../div-components/div-grouponinfo/div-grouponinfo.vue';
import divSeckill from '@/components/div-components/div-seckill/div-seckill.vue';
import divGoodsGroups from '@/components/div-components/div-goods-groups/div-goods-groups.vue';
import divLivingList from '@/components/div-components/div-living-list/div-living-list.vue';
import divArticleList from '@/components/div-components/div-article-list/div-article-list.vue';
import followers from '../div-components/div-followers/div-followers';
import divActivate from '../div-components/activation-coupon/index.vue';
// import divRedPackage from '../div-components/red-package/index.vue';
import divNewPerson from '@/components/div-components/div-new-person/div-new-person.vue';
import divGuesscompetition from '../div-components/guesscompetition/guesscompetition.vue';
import divTiktokLiving from '../div-components/tiktok-living/tiktok-living.vue';
import songleiMap from '../div-components/songlei-map/songlei-map.vue';
import showRedPackage from '../div-components/show-redpackage/show-redpackage.vue';

export default {
	components: {
		divHead,
		divSearch,
		// divImage,
		divImageList,
		divAdvert,
		divSwiper,
		divNavButton,
		divNotice,
		divTitleText,
		divGoods,
		divShop,
		divGoodsRow,
		divGoodsCategory,
		divCoupon,
		divBargain,
		divGroupon,
		divSeckill,
		divGoodsGroups,
		divLivingList,
		divArticleList,
		followers,
		divActivate,
		divNewPerson,
		divGuesscompetition,
		// divRedPackage,
		divTiktokLiving,
		songleiMap,
		showRedPackage
	},

	props: {
		componentsList: {
			type: Array,
			default: []
		},
		isBack: {
			type: Boolean,
			default: true
		},

		showScreen: {
			type: Boolean,
			default: false
		},
		showMinutes: {
			type: Number,
			default: -1
		},
		//设置对象方便更新
		pageSelectPoi: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			showOpenScreen: app.globalData.showOpenScreen,
			showHeavy1: true,
			showHeavy2: true,
			componentsLists: []
		};
	},

	watch: {
		showScreen: {
			handler(newVal, oldVal) {
				let that = this;
				if (newVal) {
					that.showHeavy1 = false;
					that.showHeavy2 = false;
					setTimeout(function () {
						that.showHeavy1 = true;
					}, this.showMinutes * 1000);
					setTimeout(function () {
						that.showHeavy2 = true;
					}, this.showMinutes * 1000 + 200);
				}
			},
			immediate: true
		},
		componentsList: {
			handler(newVal, oldVal) {
				if (newVal) {
					// 这里主要是保证组件太多的时候首屏能够加载出来，之后在加载其他数据
					if (newVal.length > 20) {
						this.componentsLists = newVal.slice(0, 10);
						setTimeout(() => {
							this.componentsLists = newVal;
						}, 100);
					} else {
						this.componentsLists = newVal;
					}
				}
			},
			immediate: true
		}
	},

	methods: {
		getpage(e, id) {
			this.$emit('getpage', {
				...e,
				componentId: id
			});
		},
		// 上滑加载
		pullDownRefresh() {
			if (this.$refs.divGoodsGroups) {
				this.$refs.divGoodsGroups.pullDownRefresh();
			}
		},
		// 下拉加载
		reachBottom() {
			//
			if (this.$refs.qwe) {
				console.log('调用商品分组', this.$refs.qwe.TabCur);
				// this.$refs.qwe.pullDownRefresh();
			}
		},
		openRulePopup(info) {
			this.$emit('openRulePopup', info);
		}
	}
};
</script>
