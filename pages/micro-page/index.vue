<template>
  <view>
    <divComponents
      v-if="scene!=1154"
      ref="divComponents"
      :microId="id"
      divType="micro"
      :showPage="showPage"
      :isBack="true"
	  @canMicroPageShare="handleCanShare"
    />
    <share-single-page v-if="scene==1154" />
  </view>
</template>

<script>
import divComponents from "./components/div-components/index.vue";
import shareSinglePage from "@/components/share-single-page/index.vue";

import {
  EventBus
} from '@/utils/eventBus.js'
const util = require("utils/util.js");
export default {
  components: {
    divComponents,
    shareSinglePage
  },
  data () {
    return {
      id: "",
      showPage: true,
      scene: '',
	  sharer_user_code:'',
      //有数统计使用
      page_title: '微页面'
    };
  },

  onLoad (options) {
    // #ifdef MP-WEIXIN  
    let getLaunchOptions = uni.getLaunchOptionsSync()
    this.scene = getLaunchOptions.scene
    //场景值等于 1154 分享单页模式
    if (this.scene && this.scene == 1154) return
    // #endif
	util.saveSharerUserCode(options);
    let id;
    if (options.scene) {
      //接受二维码中参数
      let scenes = decodeURIComponent(options.scene).split('&');
      id = scenes[0];
    } else {
      id = options.id;
    }
    this.id = id
  },

  onShow() {
    // 再次进入页面时，刷新新人专享数据
    EventBus.$emit("refreshNewCustomerDialog")
  },


  onShareAppMessage: function () {
    let share = this.$refs.divComponents.shares;
    return share
  },

  // #ifdef MP-WEIXIN
  //朋友圈
  onShareTimeline (res) {
    let share = this.$refs.divComponents.shares;
    return share
  },
  // #endif

  onPageScroll (res) {
    uni.$emit('vonPageScroll', res);
    uni.$emit('vonVideoPageScroll', res);
  },
  onPullDownRefresh () {
    this.$refs.divComponents.pullDownRefresh();
  },


  onReachBottom () {
	//装修组件的上滑加载下一页
    EventBus.$emit("divGoodsGroupsReachBottom", "true")
  },

  onUnload () {
    EventBus.$off('divGoodsGroupsReachBottom')
    
    // 新人专享弹框
    EventBus.$off('refreshNewCustomerDialog')
  },
  
  methods:{
  	handleCanShare(value){
		console.log("===handleCanShare==",value)
  		if(value==2){
  			 uni.hideShareMenu()
  		}
  	}
  },
 
};
</script>
