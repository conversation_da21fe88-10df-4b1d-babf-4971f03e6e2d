<template>
  <view style="width: 100vw;height: 100vh;background-color: #f1f1f1;">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">物流详情</block>
    </cu-custom>
    <view class="logistics-status">{{ orderDetails.logisticsStatusDesc }}</view>
    <view class="logistics-box">
      <view class="logistics-code">
        <text>{{ orderDetails.logisticsNo }}</text> 
        <!-- <image src="" ></image> -->
      </view>
      <view class="all">
        <view class="item" v-for="(item, index) in orderDetails.fullTraceDetails" :key="index">
          <view class="dian">
            <view :class="'round-dot ' + (index === 0 ? 'hover' : '' )"></view>
            <view v-if="index !== orderDetails.fullTraceDetails.length - 1" class="lines">
            </view>
          </view>
          <view class="content">
            <view :class="'text ' + (index === 0 ? 'hover' : '' )">{{ item.routeInfo }}</view>
            <view class="time">{{ item.routeDate }}</view>
          </view>
        </view>
      </view>
    </view>
    <view style="height: 10rpx;"></view>
   </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import util from 'utils/util'
// #ifdef MP-WEIXIN
const plugin = requirePlugin("logisticsPlugin")
// #endif

export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      orderLogistics: null,
      orderDetails: {},
      id: null,
      //有数统计使用
      page_title:'物流详情'
    };
  },

  components: {},
  props: {},

  onShow () { },

  onLoad (options) {
    this.id = options.id;
    app.initPage().then(res => {
      this.orderLogisticsGet(this.id);
      // this.getWayBillTokenByOrderId(options.id)
    });
  },

  methods: {
	  getWayBillTokenByOrderId(id){		
		  api.getWayBillTokenByOrderId(id).then(res=>{
        // #ifdef MP-WEIXIN
			  plugin.openWaybillTracking({
			      waybillToken: res.data.waybill_token
			   });
        // #endif
		  })
	  },
    orderLogisticsGet (id) {
      let that = this;
      api.orderLogisticsGet(id).then(res => {
        let orderLogistics = res.data;
        console.log("orderLogistics==>", orderLogistics);
        if (orderLogistics && orderLogistics.logisticsNo.length > 0) {
          let logisticsNo = orderLogistics.logisticsNo
          api.getLogisticsRouteBy({ logisticsNo: logisticsNo }).then(response => {
            console.log("response", response);
            if(response.data) {
              // orderLogistics.listOrderLogisticsDetail = response.data.fullTraceDetails
              this.orderDetails = response.data;
            }
          })
        }
        this.orderLogistics = orderLogistics;
      });

    },
    //复制内容
    copyData (e) {
      uni.setClipboardData({
        data: e.currentTarget.dataset.data,
        success: function () {
          uni.showToast({
            title: '复制成功',
          });
        }
      });
    }

  }
};
</script>

<style scoped>
.Logistics-information {
  margin-left: 10rpx;
  /* margin-left: 160rpx; */
}

.logisticsTime {
  /* margin-left: -280rpx; */
  margin-top: -20rpx;
}

.logisticsAddress {
  margin-top: -74rpx;
  margin-left: -30rpx;
}

.without {
  margin-top: -20rpx;
  margin-left: -30rpx;
}

.address {
  width: 90%;
  /* padding-left: 210rpx; */
  padding-left: 60rpx;
  padding-bottom: 20rpx;
}
.logistics-box {
  box-sizing: border-box;
  margin: 20rpx;
  background-color: #fff;
  min-height: 100rpx;
  border-radius: 20rpx;
  padding: 20rpx;
}
.logistics-status {
  margin: 20rpx;
  background-color: rgba(44, 181, 111, 1);
  height: 300rpx;
  border-radius: 20rpx;
  color: #fff;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}
.all {
  padding-top: 20rpx;
}
.item {
  display: flex;
  font-size: 28rpx;
}
.dian {
  width: 60rpx;
  display: flex;
  align-items: flex-start;
  position: relative;
}
.content {
  color: #999;
  padding-bottom: 20rpx;
  flex: 1;
}
.time {
  font-size: 26rpx;
}
.text {
  padding-bottom: 10rpx;
}
.text.hover {
  color: #333;
}
.round-dot {
  width: 20rpx;
  height: 20rpx;
  background-color: #999;
  border-radius: 50%;
  position: absolute;
  left: 30%;
  transform: translateX(-50%);
  top: 15rpx;
  z-index: 1;
}
.round-dot.hover {
  background-color: rgba(44, 181, 111, 1);
}
.lines {
  position: absolute;
  height: 100%;
  width: 1rpx;
  background-color: #999;
  left: 30%;
  transform: translateX(-50%);
  top: 15rpx;
}
.logistics-code {
  color: #999;
  font-size: 28rpx;
  padding-left: 10rpx;
}
</style>
