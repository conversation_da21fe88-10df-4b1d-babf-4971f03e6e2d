<template>
  <view style="position: relative;">
    <cu-custom
      :bgColor="parameter.orderType == 6 ? '#8cd62e' : 'bg-'+theme.backgroundColor" 
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">{{parameter.orderType==6?'接龙':''}}订单列表</block>
    </cu-custom>
    <view class="bg-gray nav fixed" >
      <view class="flex justify-between align-center order-search" v-if="parameter.orderType != 6">
        <input
          class="input"
          placeholder="搜索我的订单"
          v-model="searchParams.name"
          confirm-type="search"
          @confirm="searchMyOrder"
          type="text"
          @focus="searchFocus"
        />

        <view class="dressing" v-if="searchPage" @tap="searchMyOrder">搜索</view>
        <view class="dressing" v-else @tap="searchAsync = 'show'">筛选</view>

      </view>
      <scroll-view scroll-x class="" v-if="!searchPage">
        <view class="flex text-center">
          <view 
            class="cu-item flex-sub text-lg"
            :class="index == tabCur ? 'text-' + theme.themeColor : ''"
            style="padding: 0rpx 20rpx; height: 77rpx; line-height: 77rpx;"
            v-for="(item, index) in orderStatus"
            :key="index"
            @tap="tabSelect"
            :data-index="index"
            :data-key="item.key"
          >{{ item.value }}</view>
        </view>
      </scroll-view>
    </view>

    <view :style="{marginTop: parameter.orderType!=6 ? '190rpx' : '80rpx'}">
      <checkbox-group>
        <view class="cu-card article" style="overflow:inherit;">
          <view
            v-for="(item, index) in orderList"
            :key="index"
            class="cu-item"
            style="margin: 20rpx 12rpx; border-radius: 24rpx; padding: 24rpx; position: relative; overflow: initial;"
          >
            <checkbox
              class="round red scale"
              :value="item"
              :checked="item.checked"
              @tap="checkboxChange(item)"
              style="position: absolute; top: 20rpx; left: 24rpx;"
              v-if="parameter.status == '0'"
            />

            <orderList
              :orderInfo="item"
              :index="index"
              :parameter="parameter"
              @countDownDone="countDownDone($event)"
              @orderDel="orderDel($event, index)"
              @orderCancel="orderCancel($event, index)"
              @orderReceive="orderCancel($event, index)"
              @orderLogistics="orderLogistics($event, index)"
            />
          </view>
        </view>
      </checkbox-group>
      <view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
    </view>

    <!-- 待支付状态 多选进行合单支付 -->
    <view
      class="footer"
      v-if="parameter.status == '0' && selectValue.length > 0 && !payAsync"
    >
      <button
        class="cu-btn round line-red margin-right"
        @tap="openPay"
        :loading="loading"
        :disabled="loading"
        type
      >合并付款</button>
    </view>
	

    <!-- 支付方式弹框 -->
    <view
      class="cu-modal bottom-modal"
      style="overflow: hidden;"
      :class="payAsync ? 'show' : ''"
      @tap.stop="payAsync = false"
    >
      <view class="cu-dialog bg-white" style="padding-bottom: 80rpx; position: absolute; bottom: 0; left: 0;" @tap.stop>
			  <pay-components ref="pay" :payType="paymentType" :bargainId="bargainId" :orderType="orderType" />
        <button
          style="width:90%"
          class="cu-btn round bg-red margin-top-lg text-white"
          @tap="$noMultipleClicks(goPay)"
          :loading="loading"
          :disabled="loading"
          type
          id="goPay"
        >立即支付</button>
      </view>
    </view>

    <!-- 搜索筛选 -->
    <view
      class="cu-modal bottom-modal"
      :class="searchAsync"
      @tap.stop="searchAsync = ''"
    >
      <view class="cu-dialog bg-white search-order-dialog" style="height: 60%;" @tap.stop>
        <view class="title">
          订单筛选
          <text class="cuIcon-close text-lg close" @tap="searchAsync = ''"></text>
        </view>
        <view class="detail">
          <view class="item" v-for="(item, index) in dressObj" :key="index">
            <view class="text-left">{{ item.title }}</view>
            <view class="flex align-center margin-top-sm">
              <view
                class="value"
                :class="item.checked == val.id ? 'active' : ''"
                v-for="(val, i) in item.value"
                :key="i"
                @click="changeDress(item, index, val, i)"
              >{{ val.name }}</view>
            </view>
          </view>
        </view>
        <view class="flex justify-between footer">
          <button
            class="button reset"
            @tap="searchReset"
            type
          >重置</button>
          <button
            class="button submit"
            @tap="searchsub"
            type
          >确认</button>
        </view>
      </view>
    </view>
    <!-- 搜索名字页面 -->
    <view class="search-page" v-if="searchPage">
      <view class="search-history" v-if="searchMyOrderList.length">
        <view class="search-title flex justify-between">
          <view>历史搜索</view>
          <text class="cuIcon-delete lg text-gray" @tap="deleteSearchHistory"></text>
        </view>
        <view class="flex margin-top-sm" style="flex-flow: row wrap; ">
          <view
            class="margin-right-sm search-name"
            v-for="(item, index) in searchMyOrderList"
            :key="index"
            @tap="searchNameFn(item.name)"
          >{{ item.name }}</view> 
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const QR = require("utils/wxqrcode.js");
const util = require("utils/util.js");
// #ifdef MP-WEIXIN
const plugin = requirePlugin("logisticsPlugin")
// #endif
const app = getApp();

import api from 'utils/api'
import { dateFormat } from '@/utils/util.js'

import payComponents from '@/components/pay-components/pay-components.vue';
import orderList from '@/components/order-list/index.vue';

export default {
  components: {
    payComponents,
    orderList,
  },
  
  data () {
    const oneM = 30 * 24 * 60 * 60 * 1000;
    const threeM = 90 * 24 * 60 * 60 * 1000;
    const sixM = 180 * 24 * 60 * 60 * 1000;
    const now = new Date().getTime();
    return {
      payAsync: false,
      searchAsync: '',
      searchPage: false,
      theme: app.globalData.theme, //全局颜色变量
      tabCur: 0,
      noClick: true,
      orderStatus: [
        { value: '全部', key: '' },
        { value: '待付款', key: '0' }, 
        { value: '待发货', key: '1' },
        { value: '待收货', key: '2' },
        { value: '待评价', key: '4' }
      ],
      page: {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: '',
        //升序字段
        descs: 'create_time'
      },
      parameter: {},
      loadmore: true,
      orderList: [],
      loading: false,
      selectValue: [],
      listOrderInfo: [],
      //勾选的合并支付的订单
      //有数统计使用
      page_title: '订单列表',
      // 筛选
      dressObj: [{
        title: '下单时间',
        type: 'date',
        checked: '',
        value: [{
          name: '近一个月',
          id: '1',
          beginTime: dateFormat(new Date(now - oneM)),
          endTime: dateFormat(new Date(now))
        }, {
          name: '近三个月', 
          id: '3',
          beginTime: dateFormat(new Date(now - threeM)),
          endTime: dateFormat(new Date(now)) 
        }, {
          name: '近六个月',
          id: '6',
          beginTime: dateFormat(new Date(now - sixM)),
          endTime: dateFormat(new Date(now)) 
        }]
      }, {
        title: '来源',
        type: 'source',
        checked: '',
        value: [
          { name: '松鼠美妆', id: 'beauty', orderSource: 'beauty' },
          { name: '松鼠好物', id: 'good', orderSource: 'good' }
        ]
      }],
      searchParams: {
        name: '',
        
      },
      chioceParams: {},
      // searchName: '',
      searchMyOrderList: [],
      //是否第一次执行onShow,第一次执行不用调refresh
      isFirstOnShow: true,
      paymentType: '',
      bargainId: '',
      orderType: '',
    };
  },
  onShow () {
    this.searchMyOrderList = uni.getStorageSync('searchMyOrderList') ? uni.getStorageSync('searchMyOrderList') : [];
	if(!this.isFirstOnShow) this.refresh();
	this.isFirstOnShow = false;
  },
  onLoad: function (options) {
    if (options.orderType) {
		 // 只有是查询接龙的订单页面才传参数orderType=6  其他不传，查询所有订单
      this.parameter.orderType = options.orderType==6?6:'';
    }
    if (options.status || options.status == 0) {
      let that = this;
      this.parameter.status = options.status;
      this.orderStatus.forEach(function (status, index) {
        if (status.key == options.status) {
          that.tabCur = index;
        }
      });
    } else if (options.scene) {
      const qrCodeScene = decodeURIComponent(options.scene);
      if (qrCodeScene) {
        //接受二维码中参数  参数sf=XXX&id=XXX
        // const qrCodeSceneArray = qrCodeScene.split('&');
        const status = util.UrlParamHash(qrCodeScene, 'status');
        if (status || status == 0) {
          this.parameter.status = status;
          this.orderStatus.forEach((item, index) => {
            console.log(item.key)
            if (item.key == status) {
              this.tabCur = index;
            }
          });
        }
      }
    }
    app.initPage().then(res => {
      this.loadmore = true;
      this.orderList = [];
      this.page.current = 1;
      this.orderPage();
    });
  },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.orderPage();
    }
  },

  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框
    uni.hideNavigationBarLoading(); // 停止下拉动作
    uni.stopPullDownRefresh();
  },

  watch: {
    listOrderInfo: {
      handler (newVal) {
        newVal.length && newVal.forEach(item => {
          this.orderType = item.orderType
          this.bargainId = item.bargainId
          if (item.paymentType == '4') {
            this.paymentType = '4'
          } else {
            this.paymentType = '1'
          }
        })
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    //数组传参
    parameterArr () {
      let data = {
        listOrderItem: [{
          isRefund: 1
        }]
      };

      function parseJson (data, newData, prefix, isArray) {
        if (!prefix) {
          prefix = '';
        }
        // 循环所有键
        for (var key in data) {
          var element = data[key];
          if (element.length > 0 && typeof (element) == "object") {
            var tempPrefix;
            if (isArray) {
              tempPrefix = prefix + '.';
            }
            if (prefix) {
              tempPrefix = tempPrefix ? tempPrefix : prefix + '.' + key;
            } else {
              tempPrefix = key;
            }
            parseJson(element, newData, tempPrefix, true);
          } else if (typeof (element) == "object") {
            var tempPrefix;
            if (isArray) {
              tempPrefix = prefix + '[' + key + ']';
            } else if (prefix) {
              tempPrefix = prefix + '.' + key;
            } else {
              tempPrefix = key;
            }
            parseJson(element, newData, tempPrefix, false);
          } else {
            if (typeof (element) == 'undefined' || !element) {
              continue;
            }
            if (!prefix) {
              newData[key] = element;
            } else if (isArray) {
              newData[prefix + '[' + key + "]"] = element;
            } else {
              newData[prefix + '.' + key] = element;
            }
          }
        }
      }
      var newData = {};
      parseJson(data, newData);
      return newData
    },

    orderPage () {
	    let parameter = {...this.parameter}
      // if (parameter.status && parameter.status == "5") {
      //   // 测试查询状态数据5，退换售后状态
      //   delete parameter.status
      //   parameter = this.parameterArr()
      //   // console.log("parameter===>", parameter);
      // }else {
		    delete parameter.listOrderItem
	    // }
      let params = Object.assign({}, this.page, util.filterForm(parameter), this.searchParams);
			const payMode = getApp().globalData.payMode;
      if (payMode) {
        // 视频号跳转过来
        params = Object.assign({scene: 'localLive'}, this.page, util.filterForm(parameter), this.searchParams);
      } 
      api.orderPage(params).then(res => {
        let orderList = res.data.records;
        orderList.forEach((item) => {
          if(item.deliveryWay=='2' && item.status=='1'){
            item.qrImg = QR.createQrCodeImg('/pages/mall/order/detail?id=' + item.id, {
              size: parseInt(300) //二维码大小  
            });
          }
        })
		if(this.page.current == 1) {
		  this.orderList = orderList;
		}else {
		  this.orderList = [...this.orderList, ...orderList];
		}
        if (orderList.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },

    refresh () {
      this.loadmore = true;
      this.orderList = [];
      this.page.current = 1;
      this.orderPage();
    },

    tabSelect (e) {
      let dataset = e.currentTarget.dataset;
      if (dataset.index != this.tabCur) {
        this.tabCur = dataset.index;
        this.parameter.status = dataset.key;
        this.refresh();
      }
    },

    //单个商品勾选点击
    checkboxChange (item) {
      item.checked = !item.checked;
      let index = this.selectValue.indexOf(item.id);
      if (item.checked === true) {
        if (index === -1) {
          this.selectValue.push(item.id);
          this.listOrderInfo.push(item)
        }
      } else {
        if (index !== -1) {
          this.selectValue.splice(index, 1);
          this.listOrderInfo.splice(index, 1)
        }
      }
    },

    //上报有数
    compareUploadParams (orderInfo) {
      const sub_orders = [];
      sub_orders.push({
        "sub_order_id": orderInfo.orderNo,
        "order_amt": Number(orderInfo.salesPrice),
        "pay_amt": Number(orderInfo.paymentPrice),
      })
      return {
        sub_orders,
        "page_title": this.page_title,
        "order": {
          "order_id": orderInfo.orderNo,
          "order_time": new Date(util.getDateTimeForIOS(orderInfo.createTime)).getTime() || Date.parse(new Date()),
          "order_status": "pay"
        },
      }
    },
    openPay() {
      this.payAsync = true
    },
    // 合并支付
    goPay () {
      if (!this.listOrderInfo || this.listOrderInfo.length == 0) {
        uni.showToast({
          title: '请先选择合并支付的订单',
          icon: 'none',
          duration: 2000
        });
        return
      }
      let totalPrice = 0
      const paramsListOrderInfo = [];
      this.listOrderInfo.forEach(item => {
        item.listOrderItem.forEach(productItem => {
          totalPrice += Number(item.paymentPrice)
          paramsListOrderInfo.push(productItem)
        })
      })
      const orderInfo = {
        listOrderInfo: paramsListOrderInfo,
        paymentPrice: totalPrice.toFixed(2)
      }
      let orderType = ''
      if (this.parameter.orderType) {
        orderType = this.parameter.orderType
      }
      this.$refs.pay?.payOrder(orderInfo, true, orderType)
    },

    // 删除订单
    orderDel (item, index) {
      console.log(index)
      this.orderList.splice(index, 1);
    },
    // 查看物流
    orderLogistics(event, index) {
      // #ifdef MP-WEIXIN
      plugin.openWaybillTracking({
        waybillToken: event.data.waybill_token
      });
      // #endif
      console.log(this.orderList[index]);
      // #ifndef MP-WEIXIN
      uni.navigateTo({
        url: '/pages/order/order-logistics/index?id=' + this.orderList[index].orderLogistics.id
      });
      // #endif
    },
    // 倒计时结束
    countDownDone (index) {
      this.$set(this.orderList[index], 'orderStatusDesc', "交易关闭")
      this.$set(this.orderList[index], 'statusDesc', "交易关闭")
      this.$set(this.orderList[index], 'status', "5")
      
      const findIndex = this.orderList[index].actionButtonList.findIndex(item => item == 'needPay')
      if (findIndex != -1) {
        this.orderList[index].actionButtonList.splice(findIndex, 1, 'buyAgain')
      }


    },
    // 取消订单
    orderCancel (item, index) {
      api.orderGet(this.orderList[index].id).then(res => {
        this.$set(this.orderList, index, res.data);
      });
    },
    // 选择筛选条件
    changeDress (fathObj, fathInd, childObj, childInd) {
      if (this.dressObj[fathInd].checked == childObj.id) {
        this.dressObj[fathInd].checked = ''
      } else {
        this.dressObj[fathInd].checked = childObj.id
      }
      // chioceParams
      if (this.dressObj[fathInd].checked) {
        if (this.dressObj[fathInd].type == 'date') {
          this.chioceParams.beginTime = childObj.beginTime ? childObj.beginTime : ''
          this.chioceParams.endTime = childObj.endTime ? childObj.endTime : ''
        }
        if (this.dressObj[fathInd].type == 'source') {
          this.chioceParams.orderSource = childObj.orderSource ? childObj.orderSource : ''
        }
      } else {
          this.chioceParams.beginTime = ''
          this.chioceParams.endTime = ''
          this.chioceParams.orderSource = ''
      }
    },
    // 重置
    searchReset() {
      this.chioceParams = ''
      this.dressObj.forEach(item => {
        item.checked = ''
      })
      this.refresh();
      this.searchAsync = ''
    },
    // 筛选--确认
    searchsub() {
      this.searchParams = JSON.parse(JSON.stringify(this.chioceParams))
      this.tabCur = 0
      this.parameter.status = ''
      this.refresh();
      this.searchAsync = ''
    },
    // 搜索名字
    searchFocus() {
      this.searchPage = true
    },
    // 搜索我的订单--缓存本地
    searchMyOrder(e) {
      if (this.searchMyOrderList.length >= 9) {
        this.searchMyOrderList.pop()
      } else {
        if (this.searchParams.name) this.searchMyOrderList.unshift({name: this.searchParams.name.trim()})
      }
      uni.setStorageSync('searchMyOrderList', this.searchMyOrderList);
      this.searchPage = false
      this.tabCur = 0
      this.parameter.status = ''
      this.refresh();
    },
    // 删除历史记录
    deleteSearchHistory() {
      uni.showModal({
        content: '确认删除全部历史记录？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',
        success: (res) => {
          if (res.confirm) {
            this.searchMyOrderList = []
            uni.setStorageSync('searchMyOrderList', []);
          }
        }

      });
      
    },
    searchNameFn(name) {
      this.searchPage = false
      this.searchParams.name = name;
      this.tabCur = 0
      this.parameter.status = '';
      this.refresh();
    },
  }
};
</script>
<style scoped lang="scss">
.order-search{
  padding: 20rpx 26rpx 10rpx;
  .input{
    width: 614rpx;
    height: 68rpx;
    background: #FFFFFF;
    border-radius: 34rpx;
    font-size: 30rpx;
    color: #828282;
    padding: 0 40rpx;
  }
  .dressing{
    font-size: 30rpx;
    color: #000000;
  }
}
.nav {
  top: unset !important;
}

.scale {
  margin-right: 5rpx;
  transform: scale(0.8);
}

.footer {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 80rpx;
  z-index: 9999;
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.search-order-dialog {
  padding: 30rpx 34rpx 80rpx;
  position: relative;
  .title{
    font-size: 33rpx;
    font-weight: 800;
    color: #000000;
    padding-bottom: 30rpx;
    border-bottom: 2rpx dotted #000;
  }
  .close{
    position: absolute;
    top: 32rpx;
    right: 36rpx;
    color: rgb(140, 140, 140);
  }
  .detail {
    margin-top: 28rpx;
    color: #000;
    font-size: 28rpx;
    .item{
      margin-top: 40rpx;
      &:first-child{
        margin-top: 0;
      }
      .value{
        padding: 12rpx 45rpx;
        background: #F5F5F5;
        border-radius: 33rpx;
        margin-left: 16rpx;
        border: 1rpx solid #F5F5F5;
      }
      .active{
        background: #fff;
        color: #C09979;
        border: 1rpx solid #C09979;
      }
    }
  }
  .footer{
    padding-bottom: 80rpx;
    .button{
      width: 340rpx;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
    }
    .reset{
      background: #fff;
      color: #C09979;
      border: 2rpx solid #C09979;
    }
    .submit{
      background: #C09979;
      color: #fff;
      border: 2rpx solid #C09979;
    }
  }
}
.search-page{
  width: 100%;
  height: 100%;
  position: absolute;
  top: 300rpx;
  left: 0;
  background: #F1F1F1;
  
  .search-history{
    padding: 30rpx 36rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
  }
  .search-title{
    font-size: 33rpx;
    color: #000000;
  }
  .search-name{
    padding: 20rpx 27rpx;
    background: #F6F6F6;
    border-radius: 33rpx;
    font-size: 28rpx;
    color: #666666;
  }
}
</style>
