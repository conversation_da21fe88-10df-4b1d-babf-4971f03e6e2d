<template>
  <page-meta :page-style="'overflow:'+(popupShow?'hidden':'visible')"></page-meta>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">订单支付结果</block>
    </cu-custom>

    <view class="result-box">
      <view class="padding flex flex-direction">
        <view class="solid-bottom text-xxl padding text-center text-success">
          <image src="https://img.songlei.com/1/material/cd7b3e12-c17b-4645-89bd-50d0d4860af2.png"></image>
          <text class="color-white">支付成功</text>
        </view>

        <view class="margin-left-sm margin-top flex color-white">
          <text class="margin-left flex-sub text-sm">实付</text>
          <!-- <view class="flex-twice text-sm text-gray">{{orderInfo.deliveryWay == '1' ? '普通快递' : orderInfo.deliveryWay == '2' ? '上门自提' : ''}}</view> -->
          <view
            class="flex-twice text-sm text-price"
            style="flex: 5;"
          >{{paymentPrice}}
          </view>
        </view>

        <view class="margin-left-sm margin-top flex color-white">
          <text class="margin-left flex-sub text-sm">微信支付</text>
          <!-- <view class="flex-twice text-sm text-gray">{{orderInfo.orderNo}}</view> -->
        </view>

        <view class="user-line"></view>

        <navigator
          v-if="payment && payment.linkUrl"
          class="result-img"
          :url="payment.linkUrl"
        >
          <image
            class="img"
            mode="widthFix"
            :src="payment.imgUrl | formatImg750"
          ></image>
        </navigator>

        <navigator
          open-type="redirect"
          class="result-btn"
          url="/pages/order/order-list/index"
        >查看订单
        </navigator>
      </view>
    </view>

    <view
      v-if="giveCouponList.length>0"
      style="position: relative; height: 380rpx;"
    >
      <view class="card">
        <view class="giveText">恭喜您！获得支付奖励</view>

        <view class="giveCoupon" :style="{
              'text-align':'center'
              }">
          <scroll-view
            class="prefer-scroll"
            scroll-x="true"
          >
            <block
              v-for="(item, index) in giveCouponList"
              :key="index"
            > 
              <view
                class="giveList"
                v-if="item.couponType == '3'" 
                :style="{ 'border' : giveCouponList.length==1 ? 'none' : (giveCouponList.length==2 && index==1 ? 'none' : '')}"
              >
                <view style="position: relative;">
                  <image
                    class="giveImg"
                    style="width: 85rpx; height: 66rpx;"
                    src="https://img.songlei.com/live/bonus-points.png"
                  />
                </view>
                <view class="giveCouponName overflow-1" style="display:block">{{item.name}}</view>
                <view class="gvieCouponText overflow-1" v-if="item.couponTypeCode">
                  {{item.couponTypeCode}}
                </view>
              </view>
              <view v-else 
                class="giveList" 
                :style="{'border' : giveCouponList.length==1 ? 'none' : (giveCouponList.length==2 && index==1 ? 'none' : '')}"
              >
                <view style="position: relative;">
                  <image
                    class="giveImg"
                    src="https://img.songlei.com/coupon/give-coupon.png"
                  >
                  </image>

                  <view class="givePay">
                    <text style="font-size: 20rpx;color: #FFFFFF;">￥</text>
                    <text style="font-size: 60rpx;color: #FFFFFF;">{{item.reduceAmount |numFilter}}</text>
                  </view>
                </view>

                <view
                  class="giveCouponName overflow-1"
                  style="display:block"
                >{{item.name}}--{{item.couponId}}--{{item.couponTypeCode?item.couponTypeCode:''}}</view>

                <view class="gvieCouponText overflow-1">{{item.premiseAmount!='0.00'?`订单满${item.premiseAmount}元可使用`:"无门槛"}}</view>

                <view
                  class="gvieBtn"
                  @tap="toGoodsList(item.couponId,(item.couponTypeCode?item.couponTypeCode:''))"
                >去使用</view>
              </view>
            </block>
          </scroll-view>
        </view>
      </view>
    </view>
    <recommendComponents canLoad />
    <!-- 新人消费奖励 -->
    <newPeople v-if="newPeopleShow" :giftList="giftList" @close="newPeopleClose"/>

    <!-- 送券通知 -->
		<template>
			<coupon-notification ref="topMessage"></coupon-notification>
		</template>
  </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import util from 'utils/util'
import recommendComponents from "components/recommend-components/index";
import newPeople from "../components/newPeople/index";
import couponNotification from "@/components/coupon-notification/index";

export default {
  data () {
    return {
      popupShow: false, //弹层下面的页面滚动
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      orderInfo: {},
      paymentPrice: "", //实付金额
      payment: {},//广告
      //有数统计使用
      page_title: '订单支付结果页',
      orderMainId: '',//支付订单生成id
      giveCouponList: [
        // {
        //   name: '40元美妆会员礼金',
        //   pay: 30,
        //   full: 200,
        //   id: '1516596758373904385',
        //   suitType: '4',
        //   premiseAmount: '800.00',
        //   reduceAmount: '400.00',
        //   type: "1",
        //   status: '2',
        //   couponId: "1514522811224244226"
        // },
        // {
        //   name: '1元松鼠美妆优惠劵',
        //   pay: 20,
        //   full: 200,
        //   id: "1558012695138037762",
        //   suitType: '1',
        //   premiseAmount: '111.00',
        //   reduceAmount: '1.00',
        //   type: "1",
        //   status: '0',
        //   couponId: "1557958739363434498"
        // },
        // {
        //   name: '2元松鼠美妆优惠劵',
        //   pay: 10,
        //   full: 200,
        //   id: '1558012695700074498',
        //   suitType: '1',
        //   premiseAmount: '111.00',
        //   reduceAmount: '1.00',
        //   type: "1",
        //   status: '0',
        //   couponId: "1557958939188465666"
        // },
        // {
        //   name: '3元松鼠美妆优惠劵',
        //   pay: 5,
        //   full: 200,
        //   id: "1558012696044007426",
        //   suitType: '1',
        //   premiseAmount: '111.00',
        //   reduceAmount: '1.00',
        //   type: "1",
        //   status: '0',
        //   couponId: "1557959721564573698"
        // },
        // {
        //   name: '松鼠美妆优惠劵',
        //   pay: 40,
        //   full: 200,
        //   id: 5
        // }
      ],
      newPeopleShow: false,
      giftList: []
    };
  },

  components: {
    recommendComponents,
    newPeople,
    couponNotification
  },

  props: {},

  onShow () {

  },

  filters: {
    numFilter (value) {
      // 截取当前数据到小数点后三位
      // let transformVal = Number(value).toFixed(2)
      // let realVal = transformVal.substring(0, transformVal.length - 1)
      // num.toFixed(3)获取的是字符串
      // return Number(realVal)
      return parseInt(value)
    }
  },

  onLoad (options) {
    
    this.paymentPrice = options.paymentPrice; // 实付金额
    if (options.orderMainId) {
      this.orderMainId = options.orderMainId
    }
    app.initPage().then(res => {
      this.advertisement("PAY_PAGE")
      if (this.orderMainId) {
        this.giveUserRecord()
      }
      // 场景类型(1:大转盘 2:订单支付 3: 商品预览)
      this.$refs.topMessage.setMessage(2, 5000); // 将数据传递给顶部信息提示组件，并设置持续时间为0ms
    });
  },

  methods: {
    //去商品列表
    toGoodsList (id, couponTypeId) {
		// 以下是优化之后的方式
    //有couponId的。是线上劵走线上列表 或者有couponId和couponTypeId是线下绑定劵走线上列表
    console.log("id",id,'couponTypeId',couponTypeId);
    
      if (id) {
        uni.navigateTo({
          url: "/pages/goods/goods-list/index?couponId="+id
        });
      }else if(couponTypeId){
        //有couponTypeId是线下劵走线下详情页
        uni.navigateTo({
          url: "/pages/coupon/coupon-offline-detail-plus/index?id="+couponTypeId
        });
      }
    },

    giveUserRecord () {
      api.giveUserRecord(this.orderMainId).then(res => {
        if (res.data) {
          this.giveCouponList = [];
          this.giftList = [];
          res.data.forEach((u) => {
            if (u.extraInfo) {
              //新人三单礼
              u.extraInfo = JSON.parse(u.extraInfo);
              if ("Y" == u.extraInfo.isPopup) {
                this.giftList.push(u);
              }
            } else {
              //支付有礼
              this.giveCouponList.push(u);
            }
          });
          if (this.giftList.length) {
            this.popupShow = true
            this.newPeopleShow = true
          } 
        }
      }).catch(e => {

      })
    },

    orderGet (id) {
      let that = this;
      api.orderGet(id).then(res => {
        let orderInfo = res.data
        if (!orderInfo) {
          uni.redirectTo({
            // url: '/pages/order/order-list/index'
          });
        }
        this.orderInfo = orderInfo
      });
    },

    //获取广告
    advertisement (id) {
      let that = this;
      api.advertisementinfo({
        bigClass: id
      }).then(res => {
        console.log("res", res.data);
        this.payment = res.data;
      });
    },

    newPeopleClose(){
      this.popupShow = false
      this.newPeopleShow = false
    },
  }
};
</script>
<style scoped>
.border-none{
  border: none;
}
.giveList {
  display: inline-block;
  padding-left: 35rpx;
  padding-right: 40rpx;
  border-right: dashed 1rpx #8b8b8b;
  text-align: center;
}
.prefer-scroll {
  white-space: nowrap;
  width: 100%;
}
.gvieLine {
  width: 1rpx;
  height: 158rpx;
  border: 1rpx dashed #8b8b8b;
  margin: 0rpx 35rpx;
}
.givePay {
  position: absolute;
  top: 0rpx;
  left: 0rpx;
  bottom: 0rpx;
  right: 0rpx;
}
.giveImg {
  width: 153rpx;
  height: 77rpx;
}
.gvieBtn {
  background: #fe5909;
  width: 103rpx;
  height: 29rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
  color: #ffffff;
  text-align: center;
  margin: auto;
}
.gvieCouponText {
  width: 150rpx;
  font-size: 18rpx;
  color: #777777;
  margin-bottom: 6rpx;
  margin-top: 3rpx;
}
.giveCouponName {
  font-size: 22rpx;
  color: #000000;
  width: 150rpx;
}
.giveCoupon {
  width: 676rpx;
  height: 260rpx;
  padding: 50rpx 0rpx 39rpx 0rpx;
  background: #fff4ea;
  border-radius: 10rpx;
  margin: auto;
}
.giveText {
  width: 305rpx;
  font-size: 30rpx;
  font-weight: 400;
  color: #f72a12;
  margin: auto;
  padding: 25rpx 0 18rpx;
}
.card {
  width: 100%;
  height: 380rpx;
  background: #ffffff;
  border-radius: 30rpx;
  position: absolute;
  top: -20rpx;
}
.result-box {
  width: 750rpx;
  background: linear-gradient(120deg, #ff7841 0%, #ff3a10 100%);
  padding-bottom: 30rpx;
}

.result-btn {
  width: 50%;
  height: 80rpx;
  border: 1px solid #ffffff;
  border-radius: 31px;
  margin: 0 auto;
  text-align: center;
  line-height: 80rpx;
  color: #fff;
  font-size: 34rpx;
}

.result-img {
  width: 95%;
  margin: 0 auto;
  margin-top: 15rpx;
  margin-bottom: 15rpx;
  overflow: hidden;
}

.img {
  /* height: 296rpx; */
  width: 100%;
  height: auto;
  border-radius: 20rpx;
}

.user-line {
  margin: 14rpx auto;
  width: 699rpx;
  border-bottom: 2rpx dashed #eee;
}

.color-white {
  color: #fff;
}

.color-white .text-sm {
  font-size: 30rpx !important;
}

.text-success {
  display: flex;
  margin: 0 auto;
}

.text-success image {
  width: 70rpx;
  height: 70rpx;
  margin-right: 20rpx;
}
</style>
