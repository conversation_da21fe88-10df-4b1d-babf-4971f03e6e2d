<template>
  <view class="newPeople-page">
    <uni-popup
      ref="popup"
      @maskClick="close"
      maskBackgroundColor="rgba(0, 0, 0, 0.6)"
    >
      <view
        class="wrapone-box"
        v-if="giftInfo && 1 == giftInfo.rulePopTemplate"
      >
        <view class="core-box">
          <image class="tou-box" :src="iconPic.tou" />
          <view class="ming-box" :style="{ 'margin-bottom': '30rpx' }">
            恭喜您获得奖励
          </view>
          <view class="kuai-outbox">
            <block v-for="(u, i) in giftList" :key="i">
              <view @click="toUse(u)">
                <view class="kuai-box">
                  <view class="qita-box">
                    <view class="qita qita3" v-if="3 == u.couponType">
                      积分
                    </view>
                    <view class="qita qita2" v-else>券</view>
                  </view>
                  <image class="tu" :src="iconPic.kuai" />
                  <view class="wen" v-if="3 == u.couponType">
                    {{ u.reduceAmount }}分
                  </view>
                  <block v-else>
                    <!--type 3:线下卷（满减券+红包） 4:积分 5：线下卷折扣券 6：线下卷停车券 -->
                    <view class="wen" v-if="2 == u.couponType && 5 == u.type">
                      {{ u.reduceAmount | discountsConvert }}折
                    </view>
                    <view class="wen" v-else>¥{{ u.reduceAmount }}</view>
                  </block>
                </view>
                <view class="btn-box">去使用</view>
              </view>
            </block>
          </view>
          <image class="bg" :src="iconPic.bg" />
        </view>
        <view
          class="more-box"
          v-if="giftInfo && giftInfo.rulePic"
          @click="goJump"
        >
          <image class="tu" :src="giftInfo.rulePic" />
        </view>
        <view class="guan-box">
          <icon type="cancel" size="30" color="#fff" @click="close" />
        </view>
      </view>
      <view class="wraptwo-box" v-else>
        <view class="core-box">
          <view class="one-box">
            <view class="xian"></view>
            <view class="wen">新人专享</view>
            <view class="xian"></view>
          </view>
          <view class="two-box">新人大礼包</view>
          <view class="three-box">
            <block v-for="(u, i) in giftList" :key="i">
              <view class="biaoti" v-if="3 == u.couponType">-积分-</view>
              <view class="biaoti" v-else>-优惠券-</view>
              <view class="cell-box" @click="toUse(u)">
                <view class="cell">
                  <image
                    class="zoutu"
                    :src="iconPic.jifen"
                    v-if="3 == u.couponType"
                  />
                  <block v-else>
                    <!--type 3:线下卷（满减券+红包） 4:积分 5：线下卷折扣券 6：线下卷停车券 -->
                    <view class="zouzi" v-if="2 == u.couponType && 5 == u.type">
                      {{ u.reduceAmount | discountsConvert }}折
                    </view>
                    <view class="zouzi" v-else>¥{{ u.reduceAmount }}</view>
                  </block>
                  <view class="you">
                    <div class="shang" v-if="3 == u.couponType">
                      {{ u.reduceAmount }}分
                    </div>
                    <div class="shang" v-else>
                      {{
                        u.premiseAmount != "0.00"
                          ? `订单满${u.premiseAmount}元可使用`
                          : "无门槛"
                      }}
                    </div>
                    <view class="xia">去使用</view>
                  </view>
                </view>
                <image class="tu" :src="iconPic.tiao" />
              </view>
            </block>
          </view>
          <image class="bg" :src="iconPic.bg2" />
        </view>
        <view
          class="more-box"
          v-if="giftInfo && giftInfo.rulePic"
          @click="goJump"
        >
          <image class="tu" :src="giftInfo.rulePic" />
        </view>
        <view class="guan-box">
          <icon type="cancel" size="30" color="#fff" @click="close" />
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年12月4日
 **/
import { accMul } from "utils/numberUtil.js";

export default {
  props: {
    giftList: {
      type: Array,
      require: true,
      default() {
        return [];
      },
    },
  },

  data() {
    return {
      giftInfo: null,
      iconPic: {
        bg: "http://img.songlei.com/newPeople/indexone/bg.png",
        tou: "http://img.songlei.com/newPeople/indexone/tou.png",
        kuai: "http://img.songlei.com/newPeople/indexone/kuai.png",

        bg2: "http://img.songlei.com/newPeople/indextwo/bg2.png",
        tiao: "http://img.songlei.com/newPeople/indextwo/tiao.png",
        jifen: "http://img.songlei.com/newPeople/indextwo/jifen.png",
      },
    };
  },
  filters: {
    discountsConvert(val) {
      return accMul(val, 10);
    },
  },
  mounted() {
    this.giftInfo = this.giftList[0].extraInfo;
    this.$nextTick(() => {
      this.$refs.popup.open("center");
    });
  },
  methods: {
    toUse(info) {
      if (1 == info.couponType) {
        //线上券
        uni.navigateTo({
          url: "/pages/goods/goods-list/index?couponId=" + info.couponId,
        });
      } else if (2 == info.couponType) {
        //线下券
        //type 3:线下卷（满减券+红包） 4:积分 5：线下卷折扣券 6：线下卷停车券
        //停车券有金额券，北京还有小时券
        uni.navigateTo({
          url: `/pages/coupon/coupon-offline-detail-plus/index?id=${info.couponTypeCode}`,
        });
      } else {
        //积分
        uni.navigateTo({
          url: "/pages/signrecord/signrecord-info/index",
        });
      }
    },
    goJump() {
      uni.navigateTo({
        url: this.giftInfo.ruleLinkUrl,
      });
    },
    close() {
      this.$refs.popup.close();
      this.$emit("close");
    },
  },
};
</script>
<style scoped lang="scss">
.newPeople-page {
  .wrapone-box {
    .core-box {
      position: relative;
      width: 600rpx;
      height: 700rpx;
      text-align: center;
      margin-top: 120rpx;
      .tou-box {
        margin-top: -110rpx;
        width: 450rpx;
        height: 224rpx;
      }
      .ming-box {
        font-size: 50rpx;
        
        color: #fff;
      }
      .kuai-outbox {
        height: 438rpx;
        overflow: auto;
        .kuai-box {
          position: relative;
          margin: auto;
          margin-top: 20rpx;
          width: 400rpx;
          height: 139rpx;
          .qita-box {
            width: 400rpx;
            text-align: right;
            .qita {
              display: inline-block;
              font-size: 25rpx;
              
              font-weight: 400;
              color: #c7895b;
              transform: rotate(45deg);
            }
            .qita2 {
              width: 38rpx;
              margin-right: 18rpx;
            }
            .qita3 {
              width: 64rpx;
              margin-right: 6rpx;
            }
          }
          .wen {
            margin-top: -28rpx;
            font-size: 63rpx;
            
            font-weight: 400;
            color: #ea4939;
          }
          .tu {
            position: absolute;
            z-index: -1;
            top: 0;
            left: 0;
            width: 400rpx;
            height: 139rpx;
          }
        }
        .btn-box {
          margin: auto;
          margin-top: -32rpx;
          width: 262rpx;
          height: 52rpx;
          text-align: center;
          line-height: 52rpx;
          background: #ffdc7d;
          border-radius: 26rpx;
          font-size: 31rpx;
          
          font-weight: 400;
          color: #681f0e;
        }
      }
      .bg {
        z-index: -10;
        position: absolute;
        top: 0;
        right: 0;
        width: 600rpx;
        height: 700rpx;
      }
    }
    .more-box {
      margin-top: 10rpx;
      text-align: center;
      .tu {
        width: 600rpx;
        height: 120rpx;
      }
    }
    .guan-box {
      margin-top: 28rpx;
      text-align: center;
    }
  }
  .wraptwo-box {
    .core-box {
      position: relative;
      width: 600rpx;
      height: 800rpx;
      text-align: center;
      margin-top: 100rpx;
      padding-top: 50rpx;
      .one-box {
        display: flex;
        justify-content: center;
        .xian {
          width: 60rpx;
          height: 1rpx;
          background-color: #fff;
          margin: auto 20rpx;
        }
        .wen {
          font-size: 38rpx;
          
          font-weight: 400;
          color: #ffffff;
        }
      }
      .two-box {
        font-size: 60rpx;
        
        font-weight: 400;
        color: #ffffff;
      }
      .three-box {
        margin: auto;
        margin-top: 30rpx;
        width: 550rpx;
        height: 530rpx;
        overflow: auto;
        background: #fff1e7;
        border: 8rpx solid #ffdc7d;
        border-radius: 10rpx;
        .biaoti {
          margin-top: 10rpx;
          font-size: 30rpx;
          
          font-weight: 400;
          color: #ff494b;
        }

        .cell-box {
          position: relative;
          margin: auto;
          margin-top: 10rpx;
          width: 500rpx;
          height: 132rpx;
          .cell {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 500rpx;
            height: 132rpx;
            padding-top: 16rpx;
            padding-bottom: 16rpx;
            padding-right: 20rpx;
            .zouzi {
              width: 176rpx;
              text-align: center;
              font-size: 58rpx;
              
              font-weight: 400;
              color: #ffffff;
            }
            .zoutu {
              margin-left: 42rpx;
              width: 100rpx;
              height: 100rpx;
            }

            .you {
              width: 290rpx;
              text-align: center;
              .shang {
                font-size: 28rpx;
                
                font-weight: 400;
                color: #000000;
                margin-bottom: 10rpx;
              }
              .xia {
                margin: auto;
                width: 188rpx;
                height: 38rpx;
                text-align: center;
                line-height: 38rpx;
                background: #ff494b;
                border-radius: 18rpx;
                font-size: 23rpx;
                
                font-weight: 400;
                color: #ffffff;
              }
            }
          }

          .tu {
            position: absolute;
            top: 0;
            right: 0;
            width: 500rpx;
            height: 132rpx;
          }
        }
      }
      .bg {
        z-index: -10;
        position: absolute;
        top: 0;
        right: 0;
        width: 600rpx;
        height: 800rpx;
      }
    }
    .more-box {
      margin-top: 10rpx;
      text-align: center;
      .tu {
        width: 600rpx;
        height: 120rpx;
      }
    }
    .guan-box {
      margin-top: 28rpx;
      text-align: center;
    }
  }
}
</style>
