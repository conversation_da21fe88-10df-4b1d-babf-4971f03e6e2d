<template>
  <view>
    <!-- 店铺信息、订单状态 -->
    <view class="bg-white flex justify-between align-center">
      <!-- 店铺信息：logo、名称 -->
      <navigator
        class="flex align-center shop-info"
        hover-class="none"
        :url="'/pages/shop/shop-detail/index?id=' + info.orderInfo.shopInfo.id"
      >
        <text class="cu-avatar logo" />
        <text class="shop-name">
          {{info.orderInfo.shopInfo.name.length > 16 ? info.orderInfo.shopInfo.name.slice(0, 16) + '...' : info.orderInfo.shopInfo.name}}
        </text>
        <text class="cuIcon-right text-sm" />
      </navigator>
      <!-- 订单状态 -->
      <view class="order-status">
        <text>{{ info.refundStatusDesc }}</text>
      </view>
    </view>
    <!-- 商品信息 -->
    <navigator
      hover-class="none"
      :url="'/pages/order/order-refunds/form/index?orderItemId=' + info.orderItem.id"
      class="cu-item commodity"
    >
      <!-- 物流信息 -->
      <view v-if="info.orderItem.logisticsTrackDesc" class="logistics-info">
        {{ info.orderItem.logisticsTrackDesc }}
      </view>
      <view class="content" style="padding: 0;">
        <!-- 商品图片 -->
        <image
          :src="info.orderItem.picUrl ? info.orderItem.picUrl : 'https://img.songlei.com/live/img/no_pic.png'"
          mode="aspectFill"
          class="product-img margin-top-xs"
        />
        <view style="flex: 1;">
          <!-- 商品信息：类型、名称、规格、价格、数量 -->
          <view class="flex justify-between">
            <!-- 商品类型、商品名称 -->
            <view style="width: 320rpx;">
              <view class="text-black margin-top-xs overflow-2">
                <!-- 0--普通订单，1--砍价，2--拼团，3--秒杀，4--积分 -->
                <text
                  class="cu-tag bg-red sm margin-right-xs"
                  v-if="orderTypeAscs(info.orderInfo.orderType)"
                >{{ info.orderInfo.orderType | orderTypeText }}</text>
                <text class="text-df" style="font-weight: 600">{{ info.orderItem.spuName }}</text>
              </view>
              <!-- 规格信息 -->
              <view
                class="text-gray text-sm overflow-2 margin-top-xs"
                v-if="info.orderItem.specInfo"
              >{{ info.orderItem.specInfo }}</view>
            </view>
            <!-- 积分、价格、数量 -->
            <view style="flex: 1; text-align: right;">
              <view class="margin-top-xs">
                <view
                  v-if="info.orderInfo.orderType == 4 || info.orderInfo.orderType == 5"
                  style="font-size: 32rpx;"
                  class="text-df text-red margin-top-sm"
                >{{ info.orderItem.paymentPoints || '暂无' }}积分</view>
      
                <format-price 
                  styleProps="font-weight:bold"  
                  v-else
                  signFontSize="20rpx" 
                  smallFontSize="24rpx" 
                  priceFontSize="30rpx"
                  color="#000000"
                  :price="info.orderItem.salesPrice"
                />
                <view class="quantity" >x{{ info.orderItem.quantity }}</view>
              </view>
            </view>
          </view>
          <!-- 发货时间 -->
          <view v-if="info.orderItem.estimateExpressTime" class="delivery-time">
            {{ info.orderItem.estimateExpressTime }}
          </view>
          <!-- 退款金额 -->
          <view class="flex text-black justify-end margin-top-lg" style="font-size: 28rpx;">
            <text style="color: #999; margin-right: 7rpx;">共{{ info.orderItem.quantity }}件</text>
            <view class="flex text-weight" style="font-weight:bold;">
              <view>退款金额</view>
              <format-price 
                styleProps="font-weight:bold"
                signFontSize="20rpx" 
                smallFontSize="24rpx" 
                priceFontSize="30rpx"
                color="#000000"
                :price="info.orderItem.paymentPrice"
              />
            </view>
          </view>
        </view>
      </view>
    </navigator> 
    <!-- 退换货信息 -->
    <view class="return-info" v-if="info.refundDesc">
      {{ info.refundDesc }}
    </view>
    <!-- 操作按钮 -->
    <buttonList 
      v-if="info.actionButtonList && info.actionButtonList.length"
      :orderInfo="{
        actionButtonList: info.actionButtonList,
        qrImg: info.qrImg,
        orderId: info.orderId,
        refundId: info.id,
        orderItemId: info.orderItem.id,
        status: info.orderItem.status,
        listOrderItem: [info.orderItem],
        orderNo: info.orderInfo.orderNo,
        createTime: info.orderItem.createTime,
        salesPrice: info.orderItem.salesPrice,
        paymentPrice: info.orderItem.paymentPrice,
        orderLogistics: info.orderItem.orderLogistics,
        shopInfo: info.orderInfo.shopInfo,
      }"
      @orderDel="orderDel($event, index)"
      @replaceOrder="replaceOrder($event, index)"
    />
    <!-- 自提地址、时间 -->
    <view 
      class="self-delivery"
      v-if="info.actionButtonList && info.actionButtonList.indexOf('takeQrCode') != -1"
    >
      <view class="flex justify-between align-center" @tap="addressNavigation">
        <view>
          <view
            class="text-black"
            v-if="info.orderLogistics && info.orderLogistics.address"
          >自提地址：{{ info.orderLogistics.address }}</view>
          <view class="text-black" v-else>自提地址：{{ info.shopInfo.address }}</view>
          <view
            class="time" 
            v-if="info.orderLogistics && info.orderLogistics.takeTime"
          >自提时间：{{ info.orderLogistics.takeTime }}</view>
          <view class="time" v-else>自提时间：{{ info.oneselfTime }}</view>
        </view>
        <text class="cuIcon-right text-sm" />
      </view>
    </view>
  </view>
</template>

<script>
import buttonList from '@/components/order-list/btns.vue';
import formatPrice from "@/components/format-price/index.vue";
import countDown from "@/components/count-down/index";

import QQMapWX from 'public/jweixin-module/qqmap-wx-jssdk.min.js';
import __config from 'config/env';
let qqmapsdk;

export default {
  props: {
    info: {
      type: Object,
      default: () => {}
    },
    index: {
      type: Number,
      default: 0
    },
    parameter: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    countDown,
	  formatPrice,
    buttonList
  },
  filters: {
    orderTypeText: function(val) {
      switch(val) {
        case '0':
          return '普通';
        case '1':
          return '砍价';
        case '2':
          return '拼团';
        case '3':
          return '秒杀';
        case '4':
          return '积分';
        case '5':
          return '付费优惠券';
        case '6':
          return '团购';
        default:
          return '';
      }
    }
  },
  data() {
    return {

    }
  },
  created() {
    // 实例化API核心类
    qqmapsdk = new QQMapWX({
      key: __config.mapKey
    });
  },
  methods: {
    orderTypeAscs(val) {
      switch(val) {
        case '0': // '普通'
          return false;
        case '1': // '砍价'
          return true;
        case '2': // '拼团'
          return true;
        case '3': // '秒杀'
          return true;
        case '4': // '积分'
          return false;
        case '5': // '付费优惠券'
          return false;
        case '6': // '团购'
          return false;
        default:
          return false;
      }
    },
    orderDel(event, index) {
      this.$emit('orderDel', event, index)
    },
    replaceOrder(event, index) {
      this.$emit('replaceOrder', event, index)
    },
    // 地图导航
    addressNavigation() {
      const name = this.info.shopInfo.name;
      if(this.info.orderLogistics) {
        const {latitude, longitude, address} = this.info.orderLogistics;
        if (latitude && longitude) {
          this.openLocation(latitude, longitude, name, address);
        } else {
          this.qqmapsdk(name, address);
        }
      } else {
        const address = this.info.shopInfo.address;
        this.qqmapsdk(name, address);
      }
    },
    // 通过地址解析经纬度--打开地图
    qqmapsdk(name, address) {
      qqmapsdk.search({
        keyword: address,
        success: (res) => {
          console.log("根据地址解析经纬度==",res);
          if (res && res.data.length > 0) {
            const latitude = res.data[0].location.lat;
            const longitude = res.data[0].location.lng;
            this.openLocation(latitude, longitude, name, address);
          }
        },
        fail: function(res) { },
        complete: function(res) { }
      });
    },
    // 打开地图
    openLocation(latitude, longitude, name, address) {
      uni.openLocation({
          type: "gcj02",
          latitude: Number(latitude),
          longitude: Number(longitude),
          // scale: 15,
          name: name,
          address: address,
          success: (result)=>{},
          fail: ()=>{},
          complete: ()=>{}
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.shop-info{
  height: 44rpx;
  line-height: 44rpx;
  flex: 1;
  .logo {
    width: 26rpx;
    background: url(https://img.songlei.com/live/shop/storeIcon.png) no-repeat
      center;
    background-size: 100% auto;
    margin-right: 10rpx;
  }
  .shop-name {
    font-size: 33rpx;
    font-weight: 800;
    color: #000;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}


.order-status{
  font-size: 28rpx;
  height: 44rpx;
  line-height: 44rpx;
  width: 240rpx;
  text-align: right;
  color: #EE112B;
  .count-down-box {
    width: 100%;
    height: 100%;
    border-radius: 24rpx;
    background: #ffe3e6;
    display: flex;
    align-items: center;
    .status {
      display: block;
      padding: 0 12rpx;
      height: 44rpx;
      line-height: 40rpx;
      color: #ffffff;
      background: #ee132b;
      border-radius: 24rpx 24rpx 8rpx 24rpx;
    }
  }
}

.commodity{
  .logistics-info {
    border-radius: 21rpx; 
    background: #F9F9F9; 
    font-size: 26rpx; 
    color: #999999; 
    padding: 20rpx; 
    margin: 0 0 10rpx;
  }
  .product-img {
    width: 200rpx !important;
    height: 200rpx !important;
    border-radius: 10rpx;
  } 
  .quantity {
    color: #999;
    font-size: 28rpx;
  }
  .delivery-time{
    font-size: 28rpx;
    color: #EF9416;
  }
  
}

.return-info{
  border-radius: 21rpx;
  background: #F9F9F9;
  font-size: 26rpx; 
  padding: 20rpx;
  margin-top: 30rpx;
  color: #000000;
}
.self-delivery{
  border-radius: 21rpx;
  background: #F9F9F9;
  font-size: 26rpx;
  padding: 20rpx;
  margin-top: 25rpx;
  .time{
    color: #999;
  }
}


</style>