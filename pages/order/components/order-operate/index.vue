<template>
	<view class="flex justify-end" v-if="orderInfo">
		<view class="cu-btn round line-green margin-right" @click="handleCusServiceClick">
			<view class="cuIcon-servicefill">客服</view>
		</view>
		<button class="cu-btn round line-grey margin-right" @tap="orderDel" :loading="loading" :disabled="loading" type v-if="orderInfo.status == '5' && orderInfo.isPay == '0'">
			删除订单
		</button>
		<button
			class="cu-btn round line-grey margin-right"
			@tap="$noMultipleClicks(orderCancel)"
			:loading="loading"
			:disabled="loading"
			type
			v-if="orderInfo.isPay == '0' && !orderInfo.status && orderInfo.paymentType != '4'"
		>
			取消订单
		</button>
		<button
			class="cu-btn round line-grey margin-right"
			@tap="orderLogistics"
			:loading="loading"
			:disabled="loading"
			type
			v-if="orderInfo.deliveryWay == '1' && (orderInfo.status == '2' || orderInfo.status == '3' || orderInfo.status == '4')"
		>
			查看物流
		</button>
		<!-- id="goPay" -->
		<button class="cu-btn round line-red margin-right" @tap="openPay" :loading="loading" :disabled="loading" type v-if="orderInfo.isPay == '0' && !orderInfo.status">
			立即付款
		</button>
		<!-- <button
      class="cu-btn round line-red margin-right"
      bindtap="urgeOrder"
      loading="{{loading}}"
      disabled="{{loading}}"
      type=""
      wx:if="{{orderInfo.status == '1'}}"
    >提醒卖家发货</button> -->
		<button class="cu-btn round line-red margin-right" @tap="orderReceive" :loading="loading" :disabled="loading" type v-if="orderInfo.status == '2'">确认收货</button>
		<button
			class="cu-btn round line-red margin-right"
			@tap="orderAppraise"
			:loading="loading"
			:disabled="loading"
			type
			v-if="orderInfo.status == '3' && orderInfo.appraisesStatus == '0'"
		>
			评价
		</button>

		<!-- 支付方式弹框 -->
		<view class="cu-modal bottom-modal" style="overflow: hidden" :class="payAsync ? 'show' : ''" @tap.stop="payAsync = false">
			<view class="cu-dialog bg-white" style="padding-bottom: 80rpx; position: absolute; bottom: 0; left: 0" @tap.stop>
				<pay-components ref="pay" :payType="orderInfo.paymentType" :orderType="orderInfo.orderType" :bargainHallId="orderInfo.bargainHallId"></pay-components>
				<button
					style="width: 90%"
					class="cu-btn round bg-red margin-top-lg text-white"
					@tap.stop="$noMultipleClicks(goPay)"
					:loading="loading"
					:disabled="loading"
					type
					id="goPay"
				>
					立即支付
				</button>
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp();	
import api from 'utils/api';
import util from 'utils/util';
import __config from '@/config/env';
import { getDateTimeForIOS } from '@/utils/common.js';

import payComponents from '@/components/pay-components/pay-components.vue';
// #ifdef MP-WEIXIN
const plugin = requirePlugin('logisticsPlugin');
// #endif
export default {
	components: {
		payComponents
	},
	data() {
		return {
			loading: false,
			noClick: true,
			payAsync: false
		};
	},
	props: {
		orderInfo: {
			type: Object,
			default: () => ({})
		},
		callPay: {
			type: Boolean,
			default: false
		},
		contact: {
			type: Boolean,
			default: false
		},
		pageTitle: {
			type: String,
			default: '订单支付'
		}
	},

	mounted() {
		let that = this;
		setTimeout(function () {
			if (that.callPay && that.orderInfo.paymentPrice > 0) {
				that.goPay();
			}
		}, 1000);
	},

	methods: {
		async handleCusServiceClick() {
			let wxCustomerUrl = this.orderInfo.shopInfo.wxCustomerUrl || app.globalData.wxCustomerUrl;
			if (!wxCustomerUrl) {
				const res = await api.getLivingTag();
				if (res && res.data) {
					wxCustomerUrl = res.data.platWxCustomerUrl;
					app.globalData.wxCustomerUrl = res.data.platWxCustomerUrl;
				}
			}
			if (wxCustomerUrl) {
				// #ifdef MP
				wx.openCustomerServiceChat({
					extInfo: {
						url: wxCustomerUrl
					},
					corpId: __config.chatId,
					showMessageCard: true,
					sendMessageTitle: '咨询订单id:' + this.orderInfo.id,
					sendMessagePath: 'pages/order/order-detail/index?id=' + this.orderInfo.id,
					sendMessageImg: '',
					success(res) {}
				});
				// #endif
				// #ifdef APP
				uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					openCustomerServiceChat: true,
					corpid: __config.chatId,
					customerUrl: wxCustomerUrl,
					fail(err) {
						console.log('打开客服错误', err);
						uni.makePhoneCall({
							phoneNumber: '**********'
						});
					}
				});
				// #endif
			} else {
				uni.showToast({
					title: '请店铺客服先配置下客服链接',
					icon: 'none',
					duration: 2000
				});
			}
		},

		orderReceive() {
			let that = this;
			uni.showModal({
				content: '是否确认收货吗？',
				cancelText: '我再想想',
				confirmColor: '#ff0000',
				success(res) {
					if (res.confirm) {
						let id = that.orderInfo.id;
						that.$noMultipleClicks(that.orderReceiveApi, id);
					}
				}
			});
		},

		orderReceiveApi(id) {
			let that = this;
			api.orderReceive(id).then((res) => {
				console.log('res=orderReceiveApi==>', res);
				that.$emit('orderReceive', res);
			});
		},

		orderCancel() {
			let that = this;
			uni.showModal({
				content: '确认取消该订单吗？',
				cancelText: '我再想想',
				confirmColor: '#ff0000',
				success(res) {
					if (res.confirm) {
						let id = that.orderInfo.id;
						api.orderCancel(id).then((res) => {
							that.$emit('orderCancel', res);
							
						});
					}
				}
			});
		},

		orderDel() {
			let that = this;
			uni.showModal({
				content: '确认删除该订单吗？',
				cancelText: '我再想想',
				confirmColor: '#ff0000',
				success(res) {
					if (res.confirm) {
						let id = that.orderInfo.id;
						console.log('==orderDelApi==111==');
						that.$noMultipleClicks(that.orderDelApi, id);
					}
				}
			});
		},

		orderDelApi(id) {
			console.log('==orderDelApi====');
			let that = this;
			api.orderDel(id).then((res) => {
				that.$emit('orderDel', res);
			});
		},

		openPay() {
			this.payAsync = true;
		},
		async goPay() {
			try {
				const orderInfo = {
					listOrderInfo: this.orderInfo.listOrderItem,
					paymentPrice: this.orderInfo.paymentPrice
				};
				await this.$refs.pay?.payOrder(orderInfo, true);
			} catch (e) {
				console.log(e, '======error==========');
			} finally {
			}
		},

		urgeOrder() {
			uni.showToast({
				title: '已提醒卖家发货',
				icon: 'success',
				duration: 2000
			});
		},

		orderLogistics() {
			// #ifdef MP-WEIXIN
			api.getWayBillTokenByOrderId(this.orderInfo.id).then((res) => {
				plugin.openWaybillTracking({
					waybillToken: res.data.waybill_token
				});
			});
			// #endif
			// #ifndef MP-WEIXIN
			uni.navigateTo({
				url: '/pages/order/order-logistics/index?id=' + this.orderInfo.orderLogistics.id
			});
			// #endif
		},

		orderAppraise() {
			uni.navigateTo({
				url: '/pages/appraises/form/index?orderId=' + this.orderInfo.id
			});
		}
	}
};
</script>
<style></style>
