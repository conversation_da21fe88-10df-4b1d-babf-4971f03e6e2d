<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">订单详情</block>
		</cu-custom>

		<view class="margin-bottom-bar" v-if="orderInfo != null">
			<!-- 礼品卡电子类型不展示提货信息 -->
			<view v-if="orderInfo.productType != '2'">
				<view class="bg-white padding" style="font-weight: 500">
					<view :class="
							'text-red  text-sxl cuIcon-' +
							(orderInfo.status == null && orderInfo.isPay == '0'
								? 'pay'
								: orderInfo.status == '1'
								? 'send'
								: orderInfo.status == '2'
								? 'deliver'
								: orderInfo.status == '3'
								? 'evaluate'
								: orderInfo.status == '4'
								? 'upstage'
								: orderInfo.status == '5'
								? 'roundclose'
								: '')
						">
						<text class="margin-left-xs">{{ orderInfo.statusDesc }}</text>
					</view>

					<view class="text-black text-xdf margin-top-xs" v-if="orderInfo.isPay == '0' && !orderInfo.status">
						请在
						<count-down fontSize="40rpx" :outTime="1000 * orderInfo.outTime" @countDownDone="countDownDone"
							connectorColor="#f00"></count-down>
						内付款，超时订单将自动取消
					</view>

					<view class="text-black text-xdf margin-top-xs" v-if="orderInfo.status == '2'">
						还剩
						<count-down fontSize="40rpx" :outTime="1000 * orderInfo.outTime"
							@countDownDone="countDownDone"></count-down>
						自动确认
					</view>
				</view>

				<view class="cu-list cu-card no-card menu-avatar margin-top-xs">
					<view class="cu-item address-bg" @click="goToLogic"
						v-if="orderInfo.deliveryWay == '1' && (orderInfo.status == '2' || orderInfo.status == '3' || orderInfo.status == '4')">
						<!-- 
						:url="
					'/pages/order/order-logistics/indefx?id=' +
					orderInfo.orderLogistics.id
				" -->
						<view class="cu-avatar round cuIcon-deliver_fill bg-red align-center"></view>
						<view class="loc-content align-center">
							<view class="flex align-center">
								<view class="text-blue">
									{{ orderInfo.orderLogistics.statusDesc || '' }}
								</view>
								<view class="text-black text-df padding-left-sm">
									{{ orderInfo.orderLogistics.logisticsDesc }}
								</view>
								<view class="cuIcon-right text-black text-xs"></view>
							</view>
							<view class="text-black text-df overflow-2 loc-info padding-right-sm"
								v-if="orderInfo.orderLogistics.message">
								{{ orderInfo.orderLogistics.message }}
							</view>
						</view>
					</view>

					<view class="cu-item address-bg" style="margin-top: 10rpx"
						v-if="orderInfo.deliveryWay == '1' && orderInfo.orderLogistics">
						<view class="cu-avatar round cuIcon-locationfill bg-orange align-center"></view>
						<view class="loc-content align-center">
							<view class="flex align-center">
								<view class="text-black text-lg">
									{{ orderInfo.orderLogistics.userName }}
								</view>
								<view class="text-black text-df margin-left-sm">
									{{ orderInfo.orderLogistics.telNum }}
								</view>
							</view>
							<view class="text-black text-df overflow-2 loc-info">
								{{ orderInfo.orderLogistics.address }}
							</view>
						</view>
					</view>

					<view class="cu-item address-bg" v-if="orderInfo.deliveryWay == '2'">
						<view class="cu-avatar round cuIcon-locationfill bg-orange align-center"></view>
						<view class="loc-content align-center">
							<view class="flex align-center">
								<view class="cu-tag radius line-red sm">上门自提</view>
								<view v-if="orderInfo.orderLogistics" class="text-black text-df margin-left-sm">
									{{ orderInfo.orderLogistics.telNum }}
									<text class="cuIcon-phone margin-left-sm"
										@tap="clickPhone(orderInfo.orderLogistics.telNum)"></text>
								</view>
								<view v-else class="text-black text-df margin-left-sm">
									{{ orderInfo.shopInfo.phone }}
									<text class="cuIcon-phone margin-left-sm"
										@tap="clickPhone(orderInfo.shopInfo.phone)"></text>
								</view>
							</view>
							<view v-if="orderInfo.orderLogistics"
								class="text-black text-df margin-top-xs overflow-2 loc-info">
								{{ orderInfo.orderLogistics.address }}
							</view>
							<view v-else class="text-black text-df margin-top-xs overflow-2 loc-info">
								{{ orderInfo.shopInfo.address }}
							</view>
						</view>
					</view>
				</view>


				<view v-if="orderInfo.deliveryWay == '2' && orderInfo.status == '1'"
					style="display: flex; justify-content: center; margin: 10rpx 0; background-color: #ffffff; padding: 20rpx 0; flex-direction: column; align-items: center">
					<text>自提码</text>
					<image style="width: 320rpx; height: 320rpx; margin-top: 32rpx" mode="aspectFit" :src="qrImg">
					</image>
					<view class="text-gray text-df margin-left-sm margin-xs">为保障您的权益，未到店提货前请不要将二维码/数字劵号提供给商家</view>
				</view>
			</view>

			<view class="cu-card no-card article margin-top-xs">
				<view class="cu-item" style="position: relative">
					<navigator class="flex padding-lr padding-top padding-bottom-sm align-center" hover-class="none"
						:url="'/pages/shop/shop-detail/index?id=' + orderInfo.shopInfo.id">
						<image :src="orderInfo.shopInfo.imgUrl" style="width: 48rpx" mode="widthFix"></image>
						<text class="text-xdf text-black" style="margin-left: 14rpx">
							{{ orderInfo.shopInfo.name }}
						</text>
						<text class="cuIcon-right text-gray text-df"></text>
					</navigator>

					<checkbox-group>
						<view class="cu-list menu">
							<view style="position: relative" v-for="(item, index) in orderInfo.listOrderItem"
								:key="index">
								<view class="align-center">
									<checkbox v-if="orderInfo.isPay == '1' && item.status == '0'"
										class="round margin-left-sm" style="position: absolute; top: 50rpx"
										:class="item.checked ? theme.themeColor + ' checked ' : ''"
										@tap="checkboxChange(item)" :value="item.id"
										:disabled="orderInfo.isPay == '1' && item.status != '0'"
										:checked="item.checked"></checkbox>

									<navigator hover-class="none"
										:url="'/pages/goods/goods-detail/index?id=' + item.spuId" class="cu-item">
										<view class="flex margin-top-sm">
											<view class="content response align-center"
												style="padding: 0 30rpx 0 90rpx">
												<image
													:src="item.picUrl ? item.picUrl : 'https://img.songlei.com/live/img/no_pic.png'"
													mode="aspectFill" class="row-img"></image>

												<view class="desc row-info block">
													<view class="flex justify-between align-center">
														<view class="text-black text-df overflow-2" style="flex: 1;">
															{{ item.spuName }}
														</view>
														<view class="text-gray text-df" style="text-align: right;">
															<view
																v-if="orderInfo.orderType == 4 || orderInfo.orderType == 5"
																style="font-size: 32rpx;"
																class="text-df text-red margin-top-sm">
																{{ item.paymentPoints || '暂无' }}积分
															</view>

															<format-price styleProps="font-weight:bold" v-else
																signFontSize="20rpx" smallFontSize="24rpx"
																priceFontSize="30rpx" color="#000000"
																:price="item.salesPrice" />
															<view class="quantity">x{{ item.quantity }}</view>
														</view>
													</view>

													<view class="text-gray margin-top-xs text-df overflow-2"
														v-if="item.specInfo">
														{{ item.specInfo }}
													</view>

													<view class="mt-sm">
														<view v-if="orderInfo.orderType == 4" style="font-size: 32rpx"
															class="text-df text-red margin-top-sm">
															{{ item.paymentPoints || '暂无' }}积分
														</view>
														<view v-else-if="orderInfo.orderType == 5"
															class="text-xl text-bold text-red">
															{{ item.paymentPoints }}积分
														</view>
														<view v-else class="flex  align-center">
															<view class="text-gray font-md width-110">实付款:</view>
															<format-price color="#000" styleProps="font-weight:bold"
																signFontSize="20rpx" smallFontSize="20rpx"
																priceFontSize="30rpx"
																:price="item.paymentPrice"></format-price>
														</view>
														<view v-if="item.paymentCouponPrice" class="flex align-center">
															<view class="text-red font-md width-110">优惠金额:</view>
															<view class="margin-left-xs text-red">
																<format-price color="#e54d42"
																	styleProps="font-weight:bold" signFontSize="20rpx"
																	smallFontSize="20rpx" priceFontSize="26rpx"
																	:price="item.paymentCouponPrice"></format-price>
															</view>
														</view>
														<view v-if="item.paymentPointsPrice" class="flex align-center">
															<view class="text-red font-md width-110">积分抵扣:</view>
															<view class="margin-left-xs text-red">
																<format-price color="#e54d42"
																	styleProps="font-weight:bold" signFontSize="20rpx"
																	smallFontSize="20rpx" priceFontSize="26rpx"
																	:price="item.paymentPointsPrice"></format-price>
															</view>
														</view>
													</view>
												</view>
											</view>
										</view>
									</navigator>
									<view class="cu-item text-right padding-sm margin-right-xs">
										<navigator class="cu-btn line sm"
											:url="'/pages/order/order-refunds/submit/index?orderItemId=' + item.id"
											v-if="
												orderInfo && orderInfo.paymentType != '4' && orderInfo.isPay == '1' && item.status == '0' && orderInfo.visitId === orderInfo.userId
											">
											申请售后
										</navigator>
										<navigator class="cu-btn line sm text-orange"
											:url="'/pages/order/order-refunds/form/index?orderItemId=' + item.id"
											v-if="orderInfo.isPay == '1' && item.status != '0'">
											{{ item.statusDesc }}
										</navigator>
									</view>
								</view>
							</view>
							<!-- <view class="cu-item">
							<view class="">
								<text class="text-gray text-df">订单金额</text>
							</view>
							<view class="text-df text-gray">
								<text class="text-price">{{orderInfo.salesPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs">
							<view class="text-df text-gray">
								<text class="text-gray text-df">运费</text>
							</view>
							<view class="text-df text-gray">+<text class="text-price">{{orderInfo.freightPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs" v-if="orderInfo.paymentCouponPrice">
							<view class="">
								<text class="text-gray text-df">优惠券抵扣金额</text>
							</view>
							<view class="text-df text-gray">-<text class="text-price">{{orderInfo.paymentCouponPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs" v-if="orderInfo.paymentPoints">
							<view class="">
								<text class="text-gray text-df">积分抵扣金额</text>
							</view>
							<view class="text-df text-gray">-<text class="text-price">{{orderInfo.paymentPointsPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs">
							<view class="">
								<text class="text-gray text-df">支付金额</text>
							</view>
							<view class="margin-top-xs">
								<text class="text-gray text-df" v-if="orderInfo.orderType != '0'">{{orderInfo.orderType == '1' ? '砍价后' : orderInfo.orderType == '2' ? '拼团价' : orderInfo.orderType == '3' ? '秒杀价' : ''}}</text>
								<text class="text-price text-red text-df text-bold margin-left-sm">{{orderInfo.paymentPrice}}</text>
							</view>
						</view> -->
						</view>
					</checkbox-group>
				</view>
			</view>
			<view v-if="orderInfo.paymentType != '4' && selectValue.length > 0 && orderInfo.listOrderItem.length > 1"
				class="cu-card no-card article">
				<view class="cu-item">
					<view class="flex align-center">
						<checkbox-group @change="checkboxAllChange">
							<checkbox :disabled="isAllDisabled > 0" class="round margin-left-sm"
								:class="isAllSelect ? theme.themeColor + ' checked' : ''" value="all"
								:checked="isAllSelect"></checkbox>
						</checkbox-group>
						<view class="text-lg margin-left-sm" @click="checkboxAllClick">全选</view>
						<view v-if="orderInfo.isPay == '1' && selectValue.length > 1"
							class="cu-btn line sm margin-left-sm margin-left-lg" style="color: #39b54a"
							@tap="batchAftermarket">
							批量申请售后
						</view>
					</view>
				</view>
			</view>

			<view class="cu-card no-card margin-top-xs">
				<view class="cu-item padding-bottom-lg">
					<!-- <view class="cu-bar bg-white">
						<view class="text-df margin-left-sm">
							<text class="cuIcon-titles" :class="'text-'+theme.themeColor"></text>订单信息</view>
					</view> -->
					<view class="margin-left-sm margin-top-xs flex align-center" style="padding-top: 30rpx">
						<text class="margin-left flex-sub text-df text-gray">订单编号:</text>
						<view class="flex-four text-df text-black">
							{{ orderInfo.orderNo }}
							<button class="cu-btn sm margin-left-xl" @tap="copyData(orderInfo.orderNo)"
								:data-data="orderInfo.orderNo">复制</button>
						</view>
					</view>

					<view class="margin-left-sm flex align-center">
						<text class="margin-left flex-sub text-df text-gray">配送方式:</text>
						<view class="flex-four text-df text-black">
							{{
								orderInfo.deliveryWay == '1'
									? '普通快递'
									: orderInfo.deliveryWay == '2'
									? '上门自提'
									: orderInfo.deliveryWay == '3'
									? '同城配送'
									: orderInfo.deliveryWay == '4'
									? '自动发货'
									: ''
							}}
						</view>
					</view>
					<view class="margin-left-sm flex align-center" v-if="orderInfo.deliveryWay == '3'">
						<text class="margin-left flex-sub text-df text-gray">配送时间:</text>
						<view class="flex-four text-df text-black">
							{{ orderInfo.orderLogistics ? orderInfo.orderLogistics.deliveryTime || '立即送出' : '立即送出' }}
						</view>
					</view>

					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-df text-gray">创建时间:</text>
						<view class="flex-four text-df text-black">
							{{ orderInfo.createTime }}
						</view>
					</view>

					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.paymentTime">
						<text class="margin-left flex-sub text-df text-gray">付款时间:</text>
						<view class="flex-four text-df text-black">
							{{ orderInfo.paymentTime }}
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.oneselfTime">
						<text class="margin-left flex-sub text-df text-gray">自提时间:</text>
						<view class="flex-four text-df text-black">
							{{ orderInfo.oneselfTime }}
						</view>
					</view>

					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-df text-gray">订单来源:</text>
						<view class="flex-four text-df text-black">
							{{
								orderInfo.appType == 'MA'
									? '小程序'
									: orderInfo.appType == 'H5-WX'
									? '公众号H5'
									: orderInfo.appType == 'H5'
									? '普通H5'
									: orderInfo.appType == 'APP'
									? 'APP'
									: orderInfo.appType == 'H5-PC'
									? 'PC端H5'
									: ''
							}}
						</view>
					</view>

					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.guideOrder">
						<text class="margin-left flex-sub text-df text-gray">导购电话:</text>
						<view class="flex-four text-df text-blue" @tap="clickPhone(orderInfo.guidePhone)">
							{{ orderInfo.guidePhone || '暂无' }}
						</view>
					</view>

					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.paymentType">
						<text class="margin-left flex-sub text-df text-gray">支付类型:</text>
						<view class="flex-four text-df text-black">
							{{ orderInfo.paymentType == '1' ? '微信' : orderInfo.paymentType == '2' ? '支付宝' : orderInfo.paymentType == '4' ? '对公转账' : '' }}
						</view>
					</view>

					<view v-if="orderInfo.orderInvoice" class="margin-left-sm margin-top-xs flex align-center">
						<view class="margin-left flex-sub text-df text-gray">
							<text>发票信息:</text>
							<text class="text-black"
								style="padding-left: 20rpx">{{ orderInfo.orderInvoice.status | getInvoiceStatusText }}</text>
						</view>
						<!-- goInvoiceDesc(orderInfo.orderInvoice.id) -->
						<view v-if="orderInfo.orderInvoice.status != 6 && orderInfo.orderInvoice.status != 8"
							class="flex-sub text-md text-right" style="padding-right: 38rpx"
							@tap="goInvoiceDesc(orderInfo.orderInvoice.id)">
							<text>查看</text>
							<text class="cuIcon-right text-col"></text>
						</view>
					</view>

					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-df text-gray">商品总额:</text>
						<view class="flex-twice text-df text-black" style="text-align: right; padding-right: 40rpx">
							<!-- ￥{{ orderInfo.salesPrice }} -->
							<format-price color="#000" styleProps="font-weight:bold" signFontSize="20rpx"
								smallFontSize="20rpx" priceFontSize="34rpx"
								:price="orderInfo.salesPrice"></format-price>
						</view>
					</view>

					<view
						v-if="orderInfo.paymentPromotionsPrice != null && orderInfo.paymentPromotionsPrice && orderInfo.paymentPromotionsPrice != 0"
						class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-df text-gray">商品折扣金额:</text>
						<view class="flex-twice text-df text-black"
							style="text-align: right; padding-right: 40rpx; display: flex; flex-direction: row; justify-content: flex-end; align-items: center">
							-
							<!-- ￥{{ orderInfo.paymentPromotionsPrice }} -->
							<format-price color="#000" styleProps="font-weight:bold" signFontSize="20rpx"
								smallFontSize="20rpx" priceFontSize="28rpx"
								:price="orderInfo.paymentPromotionsPrice"></format-price>
						</view>
					</view>

					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.freightPrice">
						<text class="margin-left flex-sub text-df text-gray">运费:</text>
						<view class="flex-twice text-df text-black"
							style="text-align: right; padding-right: 40rpx; display: flex; flex-direction: row; justify-content: flex-end; align-items: center">
							+
							<!-- <text class="text-price">{{ orderInfo.freightPrice }}</text> -->
							<format-price color="#000" styleProps="font-weight:bold" signFontSize="20rpx"
								smallFontSize="20rpx" priceFontSize="28rpx"
								:price="orderInfo.freightPrice"></format-price>
						</view>
					</view>

					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.paymentPursePrice">
						<text class="margin-left flex-sub text-df text-gray">零钱:</text>
						<view class="flex-twice text-df text-black"
							style="text-align: right; padding-right: 40rpx; display: flex; flex-direction: row; justify-content: flex-end; align-items: center">
							-
							<!-- <text class="text-price">{{ orderInfo.freightPrice }}</text> -->
							<format-price color="#000" styleProps="font-weight:bold" signFontSize="20rpx"
								smallFontSize="20rpx" priceFontSize="28rpx"
								:price="orderInfo.paymentPursePrice"></format-price>
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center"
						v-if="orderInfo.listOrderItem && orderInfo.listOrderItem.length">
						<text class="margin-left flex-sub text-df text-gray">已退金额:</text>
						<view class="flex-twice text-df text-black"
							style="text-align: right; padding-right: 40rpx; display: flex; flex-direction: row; justify-content: flex-end; align-items: center">
							-
							<!-- <text class="text-price">{{ orderInfo.listOrderItem[0].refundAmount}}</text> -->
							<format-price color="#000" styleProps="font-weight:bold" signFontSize="20rpx"
								smallFontSize="20rpx" priceFontSize="28rpx"
								:price="totalRefundAmount(orderInfo.listOrderItem)"></format-price>
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.paymentCouponPrice">
						<text class="margin-left flex-sub text-df text-gray">优惠金额:</text>
						<view class="flex-twice text-df text-black"
							style="text-align: right; padding-right: 40rpx; display: flex; flex-direction: row; justify-content: flex-end; align-items: center">
							-
							<!-- <text class="text-price">
								{{ orderInfo.paymentCouponPrice }}
							</text> -->
							<format-price color="#000" styleProps="font-weight:bold" signFontSize="20rpx"
								smallFontSize="20rpx" priceFontSize="28rpx"
								:price="orderInfo.paymentCouponPrice"></format-price>
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.paymentPointsPrice">
						<text class="margin-left flex-sub text-df text-gray">积分抵扣金额:</text>
						<view class="flex-twice text-df text-black"
							style="text-align: right; padding-right: 40rpx; display: flex; flex-direction: row; justify-content: flex-end; align-items: center">
							-
							<!-- <text class="text-price">
								{{ orderInfo.paymentPointsPrice }}
							</text> -->
							<format-price color="#000" styleProps="font-weight:bold" signFontSize="20rpx"
								smallFontSize="20rpx" priceFontSize="28rpx"
								:price="orderInfo.paymentPointsPrice"></format-price>
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-df text-gray"></text>
						<view class="flex-twice text-df text-black" style="
								text-align: right;
								padding-right: 40rpx;
								padding-bottom: 70rpx;
								display: flex;
								flex-direction: row;
								justify-content: flex-end;
								align-items: center;
							">
							实际付款:
							<text style="padding-left: 10rpx" class="text-red text-lg"
								v-if="orderInfo.orderType != '0'">
								{{ orderInfo.orderType == '1' ? '砍价后' : orderInfo.orderType == '2' ? '拼团价' : orderInfo.orderType == '3' ? '秒杀价' : '' }}
							</text>
							<format-price styleProps="font-weight:bold" signFontSize="20rpx" smallFontSize="24rpx"
								priceFontSize="44rpx" :price="orderInfo.paymentPrice"></format-price>
							<!-- <text class="text-red text-df text-bold margin-left-sm"> -->
							<!-- {{ orderInfo.paymentPrice }} -->
							<!-- </text> -->
						</view>
					</view>
				</view>
			</view>

			<view class="cu-card no-card margin-top-xs" v-if="orderInfo.userMessage">
				<view class="cu-item cu-form-group order-information align-start">
					<view class="cu-bar bg-white">
						<view class="text-df">给卖家留言:</view>
					</view>
					<view class="text-df text-black">
						{{ orderInfo.userMessage }}
					</view>
				</view>
			</view>
		</view>
		<view class="wk_text_img">
			<view class="text-lg">
				<view class="jion">加入松雷会员群~</view>
				<view class="">领取专属福利 享受更多优惠</view>
				<view class="txt_savecode">长按保存右侧二维码</view>
			</view>
			<view class="qrcode_wk" v-if="wkCode">
				<image class="qrcode_wk" :src="wkCode" @click="previewImage"></image>
			</view>
		</view>
		<view class="" style="height: 200rpx"></view>
		<view class="cu-bar bg-white border foot" v-if="orderInfo && orderInfo.visitId === orderInfo.userId"
			style="display: flex; align-items: center">
			<view class="" v-if="orderInfo.orderLogistics.statusDesc == '完成退款'" @click="tkorder" style="
					width: 202rpx;
					padding: 11rpx;
					text-aling: center;
					background: red;
					border-radius: 27rpx;
					color: white;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-left: 39rpx;
				">
				取消退款
			</view>
			<order-operate class="response" :orderInfo="orderInfo" :callPay="callPay" :contact="true"
				@orderCancel="orderCancel" @orderReceive="orderCancel" @orderDel="orderDel"
				:pageTitle="page_title"></order-operate>
		</view>
		<uni-popup ref="perpopup" type="center" :mask-click="false">
			<view class="permissions_box">当您使用APP时，拨打电话咨询商家时候需要拨打电话权限，不授权上述权限，不影响APP其他功能使用。</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api';
	import orderOperate from '../components/order-operate/index';
	import countDown from 'components/count-down/index';
	import util from 'utils/util';
	const QR = require('utils/wxqrcode.js');
	import formatPrice from '@/components/format-price/index.vue';
	// #ifdef MP-WEIXIN
	const plugin = requirePlugin('logisticsPlugin');
	// #endif
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderInfo: null,
				shoppingCartData: [], // 选中商品
				isAllSelect: false, // 全选按钮 是否选中
				selectValue: [], // 选中商品id集合
				settlePrice: 0, // 结算金额
				active: 1,
				list2: [{
						title: '买家下单',
						desc: '2018-11-11'
					},
					{
						title: '卖家发货',
						desc: '2018-11-12'
					},
					{
						title: '买家签收',
						desc: '2018-11-13'
					},
					{
						title: '交易完成',
						desc: '2018-11-14'
					}
				],
				id: null,
				qrImg: null,
				callPay: false, //是否直接调起支付
				//有数统计使用
				page_title: '订单详情',
				wkCode: '',
				waybill_token: ''
			};
		},

		filters: {
			getInvoiceStatusText(status) {
				let statusText = '';
				switch (status) {
					case 0:
						statusText = '开票中';
						break;
					case 1:
						statusText = '已开票';
						break;
					case 2:
						statusText = '开票失败';
						break;
					case 3:
						statusText = '申请中';
						break;
					case 4:
						statusText = '申请中';
						break;
					case 5:
						statusText = '申请中';
						break;
					case 6:
						statusText = '已红冲';
						break;
					case 8:
						statusText = '红冲中';
						break;
					case 8:
						statusText = '待手动重推';
						break;
				}
				return statusText;
			}
		},

		components: {
			orderOperate,
			countDown,
			formatPrice
		},
		props: {},
		computed: {
			//退款总金额
			totalRefundAmount() {
				return (Arrefunamount) => {
					let refundAmountAll = 0;
					if (Arrefunamount && Arrefunamount.length > 0) {
						for (var i = 0; i < Arrefunamount.length; i++) {
							refundAmountAll += Arrefunamount[i].refundAmount;
						}
						return refundAmountAll;
					}
				};
			}
		},

		onShow() {
			app.initPage().then((res) => {
				this.orderGet(this.id);
				this.selectValue = [];
				api.wkCode().then((res) => {
					console.log(res, 'res');
					this.wkCode = res.data.imgUrl ? res.data.imgUrl :
						'https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20221130105430.png';
				});
			});
		},

		onLoad(options) {
			console.log("===order==onLoad=======");
			this.id = options.id;
			if (options.callPay) {
				this.callPay = true;
			}
			//兼容通过扫码商品小票上面的二维码进来
			if (options.q) {
				const q = decodeURIComponent(options.q); // 获取到二维码原始链接内容
				if (q && q.length > 0 && q != 'undefined') {
					this.id = util.getUrlParam(q, 'id');
				}
			}
		},
		watch: {
			selectValue(val) {
				if (this.selectValue && this.selectValue.length < 0) {
					this.isAllSelect = false;
				}
			}
		},
		methods: {
			goToLogic() {
				// #ifdef MP-WEIXIN
				api.getWayBillTokenByOrderId(this.id).then((res) => {
					plugin.openWaybillTracking({
						waybillToken: res.data.waybill_token
					});
				});
				// #endif
				// #ifndef MP-WEIXIN
				uni.navigateTo({
					url: '/pages/order/order-logistics/index?id=' + this.orderInfo.orderLogistics.id
				});
				// #endif
			},
			// 长按识别
			previewImage() {
				uni.previewImage({
					// 需要预览的图片链接列表
					urls: [this.wkCode],
					// 为当前显示图片的链接/索引值
					current: this.wkCode,
					// 图片指示器样式
					indicator: 'default',
					// 是否可循环预览
					loop: false,
					// 长按图片显示操作菜单，如不填默认为保存相册
					// longPressActions:{
					// 	itemList:[this.l('发送给朋友'),this.l]
					// },
					success: (res) => {
						console.log('res', res);
					},
					fail: (err) => {
						onsole.log('err', err);
					}
				});
			},
			//申请退款
			tkorder() {
				// orderItemId   订单详情id
				// status     订单详情状态
				// id 退款单id
				// api.tkorder({this.orderInfo}).then(res => {
				// })
			},
			//批量申请售后
			batchAftermarket() {
				console.log('batchAftermarket=>', this.selectValue);
				if (this.selectValue) {
					let orderId = this.selectValue.join(',');
					console.log('orderId', orderId);
					uni.navigateTo({
						url: '/pages/order/order-refunds/submit/index?orderItemId=' + orderId
					});
				}
			},
			// 判断全选按钮是否选中
			checkboxHandle(selectValue) {
				console.log('判断全选按钮是否选中=>', selectValue);
				let that = this;
				//获取订单数量
				let shoppingCartData = this.orderInfo.listOrderItem;
				let isAllSelect = false;
				let _leng = 0;
				_leng += Object.keys(shoppingCartData).length;
				if (_leng == selectValue.length) {
					isAllSelect = true;
				}
				this.isAllSelect = isAllSelect;
				this.selectValue = selectValue;
				// this.countSelect();
			},

			// 全选
			checkboxAllChange(e) {
				console.log('全选==>', e);
				var value = e.detail.value;
				if (value.length > 0) {
					this.isAllSelect = true;
					this.setAllSelectValue(true);
				} else {
					this.isAllSelect = false;
					this.setAllSelectValue(false);
				}
			},

			//单个商品勾选点击
			checkboxChange(item) {
				console.log('单个商品勾选点击==>', item);
				item.checked = !item.checked;
				let index = this.selectValue.indexOf(item.id);
				console.log('index==>', index);
				let shoppingCartData = this.shoppingCartData;
				console.log('item.checked', item.checked === true);
				if (item.checked === true) {
					if (index === -1) {
						this.selectValue.push(item.id);
						shoppingCartData.push(item);
					}
				} else {
					if (index !== -1) {
						this.selectValue.splice(index, 1);
						shoppingCartData.splice(index, 1);
					}
				}
				this.shoppingCartData = shoppingCartData;
				this.checkboxHandle(this.selectValue);
			},

			// 设置全选全不选
			setAllSelectValue(status) {
				console.log('status', status);
				let shoppingCartData = this.orderInfo.listOrderItem;
				let selectValue = [];
				let that = this;
				let _shoppingCartList = []; // 选中的商品数组
				if (shoppingCartData.length > 0) {
					console.log(status, '======');
					if (status) {
						shoppingCartData.forEach((item) => {
							item.checked = true;
							selectValue.push(item.id);
							_shoppingCartList.push(item);
						});
					} else {
						shoppingCartData.forEach((item) => {
							item.checked = false;
						});
					}
					this.shoppingCartData = _shoppingCartList;
					this.checkboxHandle(selectValue);
				}
			},

			//点击全选
			checkboxAllClick() {
				if (this.isAllDisabled) {
					this.isAllSelect = false;
				} else {
					this.isAllSelect = !this.isAllSelect;
					this.setAllSelectValue(this.isAllSelect);
				}
			},
			clickPhone(num) {
				// #ifdef MP-WEIXIN
				this.callFun(num);
				// #endif 
				// #ifdef APP-PLUS
				let that = this;
				var platform = uni.getSystemInfoSync().platform;
				if (platform == 'android') {
					plus.android.checkPermission(
						'android.permission.CALL_PHONE',
						granted => {
							if (granted.checkResult == -1) {
								//弹出
								that.$refs.perpopup.open('top')
							} else {
								//执行你有权限后的方法
								that.callFun();
							}
						},
						error => {
							console.error('Error checking permission:', error.message);
						}
					);
					plus.android.requestPermissions(['android.permission.CALL_PHONE'],
						(e) => {
							//关闭
							that.$refs.perpopup.close()
							if (e.granted.length > 0) {
								//执行你有权限后的方法
								that.callFun();
							}
						})

				} else {
					//执行你有权限后的方法 ios
					that.callFun(num);
				}
				// #endif 
			},
			callFun(num) {
				if (num > 0) {
					uni.makePhoneCall({
						phoneNumber: num
					})
				} else {
					uni.makePhoneCall({
						phoneNumber: '4006171819'
					})
				}
			},

			orderGet(id) {
				let that = this;
				api.orderGet(id).then((res) => {
					let orderInfo = res.data;
					// if(res.data.orderLogistics){
					//  this.waybillToken = res.data.orderLogistics.waybillToken
					// }
					console.log('orderInfo', orderInfo);
					if (!orderInfo) {
						uni.redirectTo({
							url: '/pages/order/order-list/index'
						});
					}
					this.orderInfo = orderInfo;
					this.qrImg = QR.createQrCodeImg('/pages/mall/order/detail?id=' + id, {
						size: parseInt(300) //二维码大小
					});
					setTimeout(function() {
						that.callPay = false;
					}, 4000);
				});
			},

			//复制内容
			copyData(value) {
				uni.setClipboardData({
					data: value,
					success: function() {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						});
					}
				});
			},

			orderCancel() {
				let id = this.orderInfo.id;
				this.orderGet(id);
			},

			orderDel() {
				uni.navigateBack({
					delta: 1
				});
			},

			countDownDone() {
				this.orderGet(this.id);
			},

			goInvoiceDesc(id) {
				uni.navigateTo({
					url: `/pages/invoice/invoiceCenter/invoiceDesc?id=${id}`
				});
			}
		}
	};
</script>

<style lang="scss">
	.status-btn {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 92rpx;
		margin: 30rpx;
		background-color: #000000;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 15px;
		flex-direction: row;
	}
</style>

<style>
	.address-bg {
		min-height: 180rpx;
	}

	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx;
	}

	.loc-content {
		width: 84% !important;
	}

	.loc-info {
		line-height: 1.4em;
	}

	.cu-list.menu>.cu-item:after {
		border-bottom: unset !important;
	}

	.cu-list.menu>.cu-item {
		min-height: unset !important;
	}

	.order-information {
		padding-bottom: 100rpx;
	}

	.cu-item .margin-left-sm {
		margin-top: 15rpx;
	}

	.wk_text_img {
		display: flex;
		justify-content: space-around;
		margin-bottom: 200rpx;
		width: 100%;
		background: white;
		align-items: center;
	}

	.qrcode_wk {
		width: 272rpx;
		height: 292rpx;
		padding-top: 10rpx;
		padding-bottom: 27rpx;
	}

	.txt_savecode {
		background: #cda98d;
		color: white;
		text-align: center;
		border-radius: 43rpx;
		margin-top: 6rpx;
	}

	.jion {
		text-align: center;
	}
</style>