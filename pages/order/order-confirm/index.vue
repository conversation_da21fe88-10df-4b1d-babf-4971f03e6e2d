<template>
	<view>
		<cu-custom :bgColor="paramOrderConfirm.source == 6 ? '#8cd62e' : 'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">订单确认</block>
		</cu-custom>
		<view style="padding-bottom: 180rpx">
			<!-- 随机展示下单人数、加购人数 -->
			<view class="cu-card">
				<swiper
					class="cu-item bg-white"
					style="height: 60rpx; line-height: 60rpx; text-align: center; font-weight: 500; font-size: 30rpx; color: #e74d45"
					circular
					:autoplay="true"
					:interval="3000"
					:duration="500"
					:vertical="true"
					easing-function="linear"
				>
					<swiper-item>{{ Math.floor(Math.random() * (1000 - 20 + 1)) + 20 }}+人加购</swiper-item>
					<swiper-item>{{ Math.floor(Math.random() * (1000 - 20 + 1)) + 20 }}+人下单</swiper-item>
				</swiper>
			</view>

			<!--availableDistributionModes 用来校验礼品卡电子类型为[],不展示 -->
			<view v-if="availableDistributionModes && availableDistributionModes.length">
				<view class="cu-card" style="background: linear-gradient(#8cd62e 0%, #efefef 100%)" v-if="paramOrderConfirm.source == 6">
					<view class="cu-item bg-white cu-item-box">
						<view class="padding-lr">
							<text class="text-light-gray text-df margin-right-sm">提货时间:</text>
							<text class="text-black text-xsm">{{ groupHallVo.pickupTime }}</text>
						</view>
						<view class="padding-lr margin-top-xl">
							<text class="text-light-gray text-df margin-right-sm">提货地址:</text>
							<text class="text-black text-xsm">{{ groupHallVo.deliveryPlace }}</text>
						</view>
						<view class="pickup-tips padding-lr text-sm">请在提货时间内到提货地点提货，过期将自动取消订单</view>
					</view>
				</view>

				<view v-else class="cu-list cu-card menu-avatar">
					<view class="distributionBox">
						<view
							v-for="(item, index) in distributionMode"
							:key="item.type"
							:class="'flex-1 ' + (distributionModeIndex == index ? 'hover' : '')"
							@click="distributionTypeChange(index, item.type)"
						>
							{{ item.name }}
						</view>
					</view>

					<!-- 快递发货 -->
					<navigator
						v-if="distributionMode[distributionModeIndex].type != 1"
						:url="'/pages/user/user-address/list/index?select=true&selectId=' + (userAddress && userAddress.id)"
						:class="'cu-item location-bg ' + (userAddress && (userAddress.userName || userAddress.nickName) ? 'addressBackg' : '')"
						style="padding: 20rpx 0 20rpx"
					>
						<view class="content loc-content" v-if="userAddress && userAddress.userName">
							<view class="text-gray text-sm overflow-2">
								<view class="cu-tag sm bg-green radius margin-right-xs text-xs" v-if="userAddress.isDefault == '1'">默认</view>
								<text class="regionBox">{{ userAddress.provinceName }}{{ userAddress.cityName }}{{ userAddress.countyName }}</text>
							</view>
							<view class="detailedAddressBox">
								{{ userAddress.detailInfo }}
							</view>
							<view class="personalInfoBox">
								<text>{{ userAddress.userName }}</text>
								<text style="padding-left: 24rpx">{{ userAddress.telNum }}</text>
							</view>
						</view>
						<!-- 导购地址 -->
						<view class="content loc-content" v-else-if="shoppingGuideOrderId && userAddress && userAddress.nickName">
							<view class="text-gray text-sm overflow-2">
								<text class="regionBox">{{ userAddress.province }}{{ userAddress.city }}{{ userAddress.county }}</text>
							</view>
							<view class="detailedAddressBox">
								{{ userAddress.addr }}
							</view>
							<view class="personalInfoBox">
								<text>{{ userAddress.nickName }}</text>
								<text style="padding-left: 24rpx">{{ userAddress.phone }}</text>
							</view>
						</view>

						<view class="content loc-content margin-right" v-else>请选择收货地址</view>
						<view class="action margin-right" style="width: auto">
							<text class="cuIcon-right text-gray"></text>
						</view>
					</navigator>
					<view class="cu-card">
						<view
							class="radius-sm cu-item padding bg-white"
							v-if="distributionMode[distributionModeIndex].type == 2 || distributionMode[distributionModeIndex].type == 1"
						>
							<delivery-timeperiod
								@change="handleDeliveryTime"
								:useraddresid="userAddress && userAddress.id"
								:shopId="shopList[0].spuList[0].shopId"
								ref="timeperiod"
								:title="title"
								:type="distributionMode[distributionModeIndex].type"
							/>
						</view>

						<!-- 到店自提 占位用-->
						<view style="margin-top: 30rpx"></view>
					</view>
				</view>
			</view>

			<view class="cu-card" style="margin-top: -24upx" v-for="(shopInfo, shopIndex) in shopList" :key="shopInfo.id">
				<view v-if="availableDistributionModes && availableDistributionModes.length">
					<view v-if="selfLimitDay && distributionMode[distributionModeIndex].type == '1' && shopInfo.isAutoRefund == '1' && paramOrderConfirm.source != 6" class="Tips">
						<view class="Tips-title">温馨提示:</view>
						<view class="Tips-text">
							请您购买后及时提货，商品自您选择的自提时间起，
							<text>{{ selfLimitDay }}</text>
							日为自提期限到期未提货的，系统自动取消订单并给您办理退款。
						</view>
					</view>
				</view>

				<view
					class="cu-item padding"
					:style="
						selfLimitDay
							? distributionMode[distributionModeIndex] && distributionMode[distributionModeIndex].type == '1'
								? shopInfo.isAutoRefund == '1'
									? 'margin-top:0rpx;border-top-left-radius: 0rpx;border-top-right-radius: 0rpx;'
									: ''
								: ''
							: ''
					"
				>
					<view>
						<view class="cu-avatar sm radius" :style="'background-image:url(' + shopInfo.logo + ')'"></view>
						<text class="text-black margin-left-sm">{{ shopInfo.shopName }}</text>
					</view>
					<!-- 商品列表 -->
					<view class="margin-top" v-for="(item, index) in shopInfo.spuList" :key="index">
						<view class="flex flex-wrap align-center">
							<image :src="item.logo ? item.logo : 'https://img.songlei.com/live/img/no_pic.png'" mode="aspectFill" class="row-img basis-3" />
							<view class="desc block basis-7 padding-left-sm overflow-2" style="height: 180rpx">
								<view class="flex">
									<view v-show="item.giveaway == 1" class="text-red text-sm" style="margin-right: 6rpx">
										<text style="border: 1rpx solid red; padding: 0 2rpx 0 4rpx">赠</text>
									</view>
									<view class="text-black text-sm overflow-2" style="display: inline-block">
										{{ item.spuName }}
									</view>
								</view>
								<!-- 规格信息 -->
								<text class="text-gray text-sm margin-top-xs overflow-2 margin-top-sm" v-if="item.specInfo">{{ item.specInfo }}</text>
								<!-- 价格、积分、数量 -->
								<view class="flex text-gray align-center margin-top-xs">
									<view class="flex-twice">
										<!-- 积分兑换商品 -->
										<view v-if="paramOrderConfirm.source == 4 || paramOrderConfirm.source == 5" style="font-size: 32rpx" class="text-df text-red margin-top-xs">
											<text>
												{{ item.availableDeductionPoint >= 0 ? item.availableDeductionPoint : '暂无' }}
											</text>
											积分
										</view>
										<view v-else class="text-price text-df text-red">
											{{ item.originalPrice }}
										</view>
									</view>
									<view class="flex-sub text-right margin-right-sm">x{{ item.quantity }}</view>
								</view>
								<text class="text-red" style="font-size: 20rpx; margin-top: 20rpx">
									{{ item.spuId | promotionsInfoFilter(promotionsInfoMap) }}
								</text>
							</view>
						</view>
						<!-- 商品折扣金额 -->
						<view class="cu-bar flex response margin-top" style="min-height: 60upx" v-show="item.promotionsPrice && item.promotionsPrice != 0">
							<view class="flex-sub">
								<text class="text-gray text-sm">商品折扣金额</text>
							</view>
							<view class="flex-twice text-df text-right margin-right-sm text-red">
								-
								<text class="text-price">{{ item.promotionsPrice }}</text>
							</view>
						</view>
					</view>
					<view>
						<view
							class="cu-bar flex response margin-top-xs"
							style="min-height: 60upx"
							v-show="distributionMode[distributionModeIndex] && distributionMode[distributionModeIndex].type != 1"
						>
							<view class="flex-sub">
								<text class="text-gray text-sm">运费金额</text>
							</view>
							<view class="flex-twice text-df text-right margin-right-sm">
								<view class="text-price text-red">{{ shopInfo.freight }}</view>
							</view>
						</view>
						<!-- paramOrderConfirm.source == 6 接龙下单 -->
						<view
							v-if="distributionMode[distributionModeIndex] && distributionMode[distributionModeIndex].type == 1 && paramOrderConfirm.source != 6"
							class="selfAddressBox"
							style="margin: 20rpx 0; font-size: 24rpx; line-height: 40rpx"
						>
							<view>提货地址：</view>
							<view class="flex" @click="navHoldTor(shopIndex, `${shopInfo && shopInfo.shopHoldObj && shopInfo.shopHoldObj.id}`)">
								<view class="flex-sub" v-if="shopInfo.shopTakeAddressList && shopInfo.shopTakeAddressList.length">
									<template v-if="shopInfo && shopInfo.shopHoldObj && shopInfo.shopHoldObj.id">
										【下单后{{ shopInfo.shopId == '1549975071225999362' ? '请等待老师送货' : '30分钟可提货' }}】{{
											`${shopInfo.shopHoldObj.provinceName || ''}${shopInfo.shopHoldObj.cityName || ''}${shopInfo.shopHoldObj.countyName || ''}${
												shopInfo.shopHoldObj.detailInfo
											}` || ''
										}}
									</template>
									<template v-else>
										<text class="text-xsm margin-right-xs" style="color: #999">请选择自提地址</text>
									</template>
								</view>
								<view v-else class="flex-sub">
									<text class="text-xsm margin-right-xs" style="color: #999">当前店铺没有自提点</text>
								</view>
								<text v-if="shopInfo.shopTakeAddressList && shopInfo.shopTakeAddressList.length > 1" class="cuIcon-right margin-left-sm text-col"></text>
							</view>
						</view>

						<view class="cu-bar flex response" style="min-height: 60upx">
							<view class="flex-sub">
								<view class="text-gray text-sm">备注</view>
							</view>
							<view class="flex-twice text-df">
								<view class="cu-item align-start text-sm">
									<input @blur="userMessageInput" :data-shopInfo="shopInfo" placeholder="给卖家留言" />
								</view>
							</view>
						</view>

						<view class="flex align-center justify-end margin-top-xs margin-right-sm">
							<view class="text-df text-sm">
								<text class="text-gray text-sm">共{{ shopInfo.spuList.length }}件</text>
								<text class="margin-left-xs text-sm">小计：</text>
								<text v-if="paramOrderConfirm.source == 4 || paramOrderConfirm.source == 5" class="text-lg text-bold text-red">
									{{ shopInfo.availableDeductionPoint }}积分
								</text>
								<view style="display: inline-block">
									<format-price signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx" :price="shopInfo.totalPrice"></format-price>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 无法购买的商品 -->
			<view class="cu-card" style="margin-top: -24upx" v-if="unableListSpuList.length > 0">
				<view class="cu-item cu-item-box">
					<view class="prompt" @tap="showModalService">以下商品无法一起购买，不支持当前配送方式，请选择其他方式</view>
					<view class="cu-bar flex unqualified-goods">
						<view class="prompt-goods-list">
							<scroll-view class="scroll-view_H" scroll-x="true">
								<view class="" style="display: flex">
									<view class="prompt-goods-img-box" v-for="goodsInfo in unableListSpuList" :key="goodsInfo.spuId">
										<image class="prompt-goods-img" :src="goodsInfo.logo ? goodsInfo.logo : 'https://img.songlei.com/live/img/no_pic.png'" mode=""></image>
									</view>
								</view>
							</scroll-view>
						</view>
						<view class="prompt-goods-length">
							共{{ unableListSpuList.length }}件
							<text class="cuIcon-right text-gray"></text>
						</view>
					</view>
				</view>
			</view>

			<!-- 发票 -->
			<view class="cu-card mar-top-30" v-if="shopList.length > 0">
				<view class="cu-item bg-white cu-item-box">
					<view class="cu-bar flex response">
						<view class="flex-sub text-sm">
							<view class="text-gray margin-left-sm text-height text-col text-df">发票</view>
						</view>
						<view class="flex-sub text-sm text-right">
							<view @tap="invoiceOpen">
								<text v-if="invoiceState">电子普通发票</text>
								<text v-else>不开发票</text>
								<text class="cuIcon-right margin-right-sm text-col"></text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 优惠展示 -->
			<view class="cu-card mar-top-30">
				<view class="cu-item bg-white">
					<view class="cu-bar flex response">
						<view class="flex-twice text-sm">
							<view class="text-gray margin-left-sm text-height text-col text-df">
								商品金额
								<text class="margin-left-sm" style="color: #b6b5b5">共{{ totalQuantity }}件</text>
							</view>
						</view>
						<view class="flex-twice text-df text-right margin-right-sm text-gray arrow-width">
							<format-price color="black" styleProps="font-weight: 900;" signFontSize="26rpx" smallFontSize="26rpx" priceFontSize="30rpx" :price="originalPrice" />
						</view>
					</view>
					<!-- 折扣金额 -->
					<view v-if="promotionsPrice && promotionsPrice != 0" class="cu-bar flex response">
						<view class="flex-sub text-sm">
							<view class="text-gray margin-left-sm text-height text-col text-df">折扣金额</view>
						</view>
						<view class="flex justify-end align-center flex-twice text-df text-right margin-right-sm text-red arrow-width">
							-
							<format-price signFontSize="26rpx" smallFontSize="26rpx" priceFontSize="30rpx" :price="promotionsPrice" />
						</view>
					</view>
					<!-- 平台券 -->
					<view class="cu-bar flex response" v-if="orderCoupon.spuCoupons.length > 0 || (orderCoupon.spuCoupons.length && groupHallVo && groupHallVo.pointsDeductSwitch)">
						<view class="flex-sub text-sm">
							<view class="text-gray margin-left-sm text-height text-col text-df">
								平台券
								<text v-if="couponIds.length" style="border: 1px solid red; padding: 2rpx 5rpx" class="text-xs margin-left-xs text-red">
									已选{{ couponIds.length }}张
								</text>
							</view>
						</view>

						<view class="flex-sub text-sm">
							<text class="text-gray" v-if="!platformCoupons">{{ orderCoupon.spuCoupons.length > 0 ? '有可用优惠券' : '无可用优惠券' }}</text>
						</view>

						<view class="flex-sub text-sm text-right">
							<view class="flex-sub flex justify-end align-center" @tap="showPlatformModalCoupon">
								<view v-if="platformCoupons > 0" class="text-red flex justify-end align-center">
									-
									<format-price signFontSize="26rpx" smallFontSize="26rpx" priceFontSize="30rpx" :price="platformCoupons" />
								</view>
								<text v-else>选择券</text>
								<!-- {{platformCoupons > 0 ? '优惠￥' + platformCoupons :'选择券'}} -->
								<text class="cuIcon-right margin-right-sm"></text>
							</view>
						</view>
					</view>

					<!-- 积分抵扣商品不能积分抵扣 可用积分>0 -->
					<view
						class="cu-bar flex justify-between response"
						v-if="
							(totalPointsDeduct > 0 && paramOrderConfirm.source != 4 && paramOrderConfirm.source != 5) ||
							(totalPointsDeduct && groupHallVo && groupHallVo.pointsDeductSwitch)
						"
					>
						<view style="min-width: 60rpx" class="text-gray margin-left-sm text-height text-col text-df">积分</view>
						<view class="text-sm" style="padding-left: 30rpx">
							<text class="text-black">可用{{ totalPointsDeduct }}积分抵{{ totalPointsDeductPriceTemp }}元</text>
						</view>
						<view class="text-df text-gray text-right margin-right-sm">
							<switch :checked="availableIntegralState" @change="changeAvailableIntegral($event, 0)" class="brown" />
						</view>
						<view class="flex justify-end align-center text-df text-right margin-right-sm arrow-width">
							<text v-if="availableIntegralState" class="text-red">-</text>
							<format-price v-if="availableIntegralState" signFontSize="26rpx" smallFontSize="26rpx" priceFontSize="30rpx" :price="totalPointsDeductPriceTemp" />
							<text v-else class="text-df">不使用</text>
						</view>
					</view>

					<view
						class="cu-bar flex justify-between response"
						v-else-if="
							(freightPoints > 0 && totalPointsDeduct > 0 && paramOrderConfirm.source != 5) ||
							(freightPoints && totalPointsDeduct && groupHallVo && groupHallVo.pointsDeductSwitch)
						"
					>
						<view class="text-sm">
							<view class="text-gray margin-left-sm text-height text-col text-df">积分</view>
						</view>
						<view class="text-sm">
							<text class="text-black">可用{{ freightPoints }}积分抵运费{{ shopList[0].freight ? shopList[0].freight : 0 }}元</text>
						</view>
						<view class="text-df text-gray text-right margin-right-sm arrow-width">
							<switch :checked="freightPointsSwitch" @change="changeAvailableIntegral($event, 1)" class="brown" />
						</view>
						<view class="text-df flex justify-end text-right margin-right-sm arrow-width">
							<text v-if="freightPointsSwitch" class="text-red">-</text>
							<format-price v-if="freightPointsSwitch" signFontSize="26rpx" smallFontSize="26rpx" priceFontSize="30rpx" :price="comPFreight" />
							<text v-else class="text-df">不使用</text>
						</view>
					</view>

					<!-- 使用零钱  -->
					<view v-if="payMode && payMode.includes(1)">
						<view v-if="purseStatus">
							<smallchange
								ref="smallchange"
								:purseDeductPrice="purseDeductPrice.toString()"
								:pursePrice="pursePrice"
								:newChangprop="parseInt(changprop)"
								:purseStatus="purseStatus"
								:paymentPursePrice="paymentPursePrice"
								@radioPurseDeductPrice="radioPurseDeductPrice($event)"
								:classstyle="[
									'text-gray margin-left-sm text-height text-col',
									'flex justify-end  align-center flex-twice text-right margin-right-sm text-sm lines-red',
									'flex-sub text-df cu-bar text-black '
								]"
							></smallchange>
						</view>
					</view>

					<!-- 礼品卡支付 -->
					<view v-if="payMode && payMode.includes(2)" class="text-sm" style="height: 78%">
						<gift-modal
							:giftCardPrice="giftCardPrice"
							:giftDeductPrice="giftDeductPrice"
							:newGiftPrice="giftPrice"
							:top="'475rpx'"
							@giftOrderConfirm="giftOrderConfirm"
							@giftPriceValue="giftPriceValue"
						></gift-modal>
					</view>

					<!-- 积分赠送 -->
					<view class="cu-bar flex response solid-top" v-if="givePoints">
						<view class="flex-sub text-sm">
							<view class="text-gray margin-left-sm text-height text-col text-df">积分赠送</view>
						</view>
						<view class="flex-twice text-df text-right margin-right-sm arrow-width">
							{{ givePoints }}
						</view>
					</view>

					<!-- 赠品 -->
					<view class="gifts-box" v-if="orderCoupon && orderCoupon.gifts && orderCoupon.gifts.length > 0">
						<view class="gifts-title text-col">赠品</view>
						<view class="text-right gifts-list">
							<view v-for="(item, index) in orderCoupon.gifts" :key="index" class="gift-name" @click="navigatorPage(item.id)">
								<text class="name">{{ item.spuName }}</text>
								<text class="cuIcon-right margin-right-sm"></text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 支付方式 -->
			<view class="cu-card mar-top-30">
				<view class="cu-item bg-white cu-item-box">
					<pay-components ref="pay" :orderType="paramOrderConfirm.source" :bargainHallId="paramOrderConfirm.bargainHallId"></pay-components>
				</view>
			</view>
		</view>

		<view class="cu-bar bg-white border foot padding flex">
			<view class="flex-sub">
				<view class="flex cu-item justify-end align-center">
					<view style="font-size: 28rpx; color: #666; font-weight: bold; margin-right: 16rpx">共{{ totalQuantity }}件</view>
					<text style="font-size: 28rpx; color: black; font-weight: bold">合计:</text>
					<template v-if="paramOrderConfirm.source == 4 || paramOrderConfirm.source == 5">
						<text v-if="availableDeductionPoint > 0" class="text-lg text-bold text-red text-price text-price-two">{{ availableDeductionPoint }}积分</text>
					</template>
					<format-price v-else style="float: left" signFontSize="28rpx" smallFontSize="32rpx" priceFontSize="40rpx" :price="totalPice" />
				</view>
				<view class="flex align-center text-gray justify-end text-sm">
					共省
					<format-price color="gray" signFontSize="24rpx" smallFontSize="24rpx" priceFontSize="24rpx" :price="reductionPrice" />
				</view>
			</view>
			<button
				v-if="AuthorInformation"
				class="cu-btn round shadow-blur lg margin-left-sm"
				:class="'bg-' + theme.themeColor"
				@tap="$noMultipleClicks(orderSub)"
				:loading="loading"
				:disabled="loading"
				type
			>
				提交订单
			</button>
			<button
				v-if="!AuthorInformation"
				class="cu-btn round shadow-blur lg margin-left-sm"
				:class="'bg-' + theme.themeColor"
				open-type="getUserInfo"
				@getuserinfo="agreeGetUser"
				@tap="$noMultipleClicks(orderSub)"
				:loading="loading"
				:disabled="loading"
				type
			>
				提交订单
			</button>
		</view>

		<!-- 无法购买 -->
		<view :class="'cu-modal bottom-modal ' + modalService" @tap="hideModalCoupon">
			<view class="cu-dialog bg-white" @tap.stop>
				<view class="padding-xl">
					<view>该商品无法一起购买，只支持到店自提或者只支持快递发货</view>
					<view class="padding">
						<button class="cu-btn response lg" :class="'bg-' + theme.themeColor" @tap="hideModalCoupon">确定</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 发票信息 弹框 -->
		<make-invoice @close="invoiceClose" v-show="invoiceFlag" ref="invoice" :spuType="spuType" />

		<!-- 平台优惠券 -->
		<view :class="'cu-modal bottom-modal ' + modalCoupon1" @tap="hideModalCoupon">
			<view class="cu-dialog bg-white" @tap.stop>
				<view class="">
					<view class="text-lg text-center padding">优惠券</view>
					<scroll-view scroll-y scroll-with-animation style="max-height: 70vh">
						<checkbox-group class="block" @change="checkboxChange">
							<view class="cu-item padding flex flex-wrap justify-between" v-for="(item, index) in orderCoupon.spuCoupons" :key="index">
								<coupon-user-info-platform class="basis-xl" :couponUserInfo="item" :toUse="false"></coupon-user-info-platform>
								<checkbox
									class="round red text-center vertical-center"
									:value="item.couponId"
									:disabled="loading || !!item.disabled"
									:checked="item.checked"
								></checkbox>
							</view>
						</checkbox-group>
					</scroll-view>
					<view class="padding">
						<button class="cu-btn response lg" :class="'bg-' + theme.themeColor" @tap="hideModalCoupon">确定</button>
					</view>
				</view>
			</view>
		</view>

		<modelchangge ref="modelchangge" :showchange="showchange" @changeModal="changeModal"></modelchangge>

		<modal :visible.sync="show"></modal>

		<!-- 礼品卡密码提示|验密 -->
		<view v-if="isModalDialog">
			<gift-dialog :modalPwdDialog="modalDialog" :giftPwdHeight="'65%'" ref="giftPwdDialog" @giftOrderConfirm="giftOrderSub()"></gift-dialog>
		</view>
	</view>
</template>

<script>
const app = getApp();
import api from 'utils/api';
import numberUtil from 'utils/numberUtil.js';
import couponUserInfo from '@/components/coupon-user-info/index';
import formatPrice from '@/components/format-price/index.vue';
import smallchange from '@/components/smallchange/smallchange';
import modelchangge from '@/components/modelchangge/modelchangge';
import Modal from '@/components/getphonecode/getphonecode';

const util = require('utils/util.js');
const version = require('utils/version.js');
import payComponents from '@/components/pay-components/pay-components.vue';

import giftDialog from '@/components/goods-sku/gift-dialog.vue';
import giftModal from '@/components/goods-sku/gift-modal.vue';
import price from 'utils/price-max-min';

import makeInvoice from '@/components/invoice/makeInvoice/index.vue';
import { senTrack, handleSenOrder } from '@/public/js_sdk/sensors/utils.js';

export default {
	data() {
		return {
			UsagerulesShow: false,
			usetext: '',
			checkoutvalue: '',
			usershow: false,
			changprop: '-1',
			getpice: 0,
			showchange: false,
			show: false,
			changestatus: '',
			title: '到店自提',
			noClick: true,
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			numberUtil: numberUtil,
			showModal: false,
			allDistributionModes: [
				{
					// 配送方式  0快递发货  1到店自提  2同城配送
					type: '0',
					name: '快递发货',
					parameterName: 'distributionVo'
				},
				{
					type: '2',
					name: '同城配送',
					parameterName: 'sameCityVo'
				},
				{
					type: '1',
					name: '到店自提',
					parameterName: 'selfMentionVo'
				}
			],
			//当前商品支持的配送方式
			distributionMode: [],
			distributionModeIndex: -1, // 选中哪个配送方式 索引
			totalPice: 0, // 总价
			availableDeductionPoint: 0, //积分兑换需要的积分
			totalPointsDeduct: 0, //可用积分
			totalPointsDeductPriceTemp: 0, //积分可抵的金额
			availableIntegralState: true, // 积分可用状态
			paramOrderConfirm: uni.getStorageSync('param-orderConfirm') || {}, // 购买的商品购物车id集合
			orderCoupon: {
				// 平台券
				// optimalCom: [], // 最佳组合
				spuCoupons: [], // 平台券列表
				gifts: [], // 赠品
				available: [] // 可用优惠券集合
			},
			couponIds: [], //选中平台券
			platformCoupons: 0, // 平台优惠价格
			shopList: [], //店铺集合
			unableListSpuList: [], // 不可购买集合
			promotionsPrice: '', //折扣金额
			modalCoupon1: '', // 平台优惠券弹框
			modalService: '', // 无法购买
			invoiceFlag: false, // 发票信息弹框
			invoiceState: false, // 开不开发票
			promotionsInfoMap: [], //折扣活动，需要遍历商品使用哪个折扣，然后展示 key是spuId
			userAddress: {},
			orderSubParm: {
				deliveryWay: '1',
				isPointsBeDeducted: 1, // 是否开启 使用积分抵扣
				invoiceRemark: '' // 发票信息
			},
			loading: true,
			userMessageList: {}, //  买家留言集合
			orderKey: null, // 订单 key
			//有数统计使用
			page_title: '订单确认',
			srOrderObj: {},
			listOrderInfo: [],
			freightPoints: 0,
			freightPointsSwitchs: '',
			freightPointsSwitch: false,
			isMsg: false, //平板是否填写备注
			spuId: ['1569247911741689857', '1569245956264890370', '1569244412563886081'], //平板id
			couponId: '', // 传递id
			AuthorInformation: true,
			takeTime: '', // 自提时间 上门提货
			selfLimitDay: '', //自提天数
			sureDeliveryTime: '',
			givePoints: 0, //赠送积分
			shoppingGuideOrderId: null, //导购订单id
			shopIdAdreess: 0, // 提货地址回调时的shopId
			groupHallVo: {}, //拼团信息
			pursePrice: 0, //零钱余额
			purseDeductPrice: 0, //零钱抵扣金额
			purseStatus: '', //零钱没开通钱包：0；开通没签约：1；开通并签约：2
			giftCardPrice: '', //可用礼品卡金额
			giftDeductPrice: '', //礼品卡抵扣金额
			giftPrice: null, //礼品卡支付金额
			giftPwd: '', //礼品卡支付密码
			modalDialog: '', //礼品卡密码弹框
			isModalDialog: false, //礼品卡密码弹框开关
			payMode: [], //判断显示不显示 1 钱包 2 礼品卡
			paymentPursePrice: 0, //零钱 自定义返显默认值
			radioChange: null, //零钱标记用户默认0还是最高抵扣1
			needUserCertified: '0', //礼品卡商品是否需要实名认证 0不需要  1 需要
			userCertifiedPrompt: '', //未实名认证做提示词
			availableDistributionModes: null, //用来校验礼品卡电子类型为[],不展示
			originalPrice: 0, // 原价
			reductionPrice: 0, // 减免金额
			prizeId: null, //松鼠乐园赠品中奖id
			spuType: null ,//商品类别
		};
	},

	watch: {
		totalPice(newVal, oldVal) {
			app.globalData.money = newVal;
		},

		// 监听配送方式，同城配送或者到店自提展示对应的时间格式
		distributionModeIndex: {
			handler(newVal, oldVal) {
				console.log(newVal, 'newVal');
				// console.log(this.distributionMode,'distributionMode')
				// console.log(this.distributionMode[newVal].type)
				let that = this;
				if (this.distributionMode[newVal]?.type == '2') {
					this.title = '同城配送';
					setTimeout(function () {
						that.$nextTick(() => {
							that.$EventBus.$emit('eventName', '2');
						});
					}, 1000);
				} else if (this.distributionMode[newVal]?.type == '1') {
					this.title = '上门自提';
					// console.log(11111);
					setTimeout(function () {
						that.$nextTick(() => {
							that.$EventBus.$emit('eventName', '1');
						});
					}, 1000);
				}
			},
			deep: true,
			immediate: true // 第一次执行
		}
	},
	computed: {
		comPFreight() {
			return this.shopList && this.shopList[0] && this.shopList[0].freight ? this.shopList[0].freight : 0;
		},
		// 无法购买的集合
		unableList() {
			return [];
			let key = this.distributionMode[this.distributionModeIndex].parameterName;
			let list = this.goodsList[this.distributionMode[this.distributionModeIndex].parameterName].unableList ? this.goodsList[key].unableList : [];
			return list;
		},
		// 购买总商品数据
		totalQuantity() {
			let quantity = 0;
			this.shopList.forEach((shopInfo) => {
				if (shopInfo.spuList && shopInfo.spuList.length > 0) {
					shopInfo.spuList.forEach((spu) => {
						quantity += spu.quantity || 0;
					});
				}
			});

			return quantity;
		},
		// 按钮提交是否能点击
		btnDisable() {
			const title = this.title;
			const distributionModeIndex = this.distributionModeIndex;
			const distributionMode = this.distributionMode;
			const type = distributionMode[distributionModeIndex]?.type;
			let disable = -1;
			if (+type === 1) {
				for (let i = 0; i < this.shopList.length; i++) {
					const shopObj = this.shopList[i].shopHoldObj;
					if (!(shopObj && shopObj.id)) {
						disable = i;
						break;
					}
				}
			}
			return disable;
		}
	},

	filters: {
		promotionsInfoFilter(spuId, promotionsInfoMap) {
			if (promotionsInfoMap && promotionsInfoMap[spuId] && promotionsInfoMap[spuId].discount >= 0) {
				return promotionsInfoMap[spuId].discount + '折优惠';
			}
			return '';
		}
	},

	components: {
		couponUserInfo,
		formatPrice,
		payComponents,
		smallchange,
		modelchangge,
		Modal,
		giftDialog,
		giftModal,
		makeInvoice
	},
	props: {},
	created(option) {
		//解决一直累加进行请求
		this.$EventBus.$off('showgetchange');

		//礼品卡验密弹框关闭
		this.$EventBus.$on('gift-pwd-dialog', (blo) => {
			if (!blo) {
				this.isModalDialog = '';
			}
			this.modalDialog = '';
			console.log('===gift-pwd-dialog=====');
		});

		//获取礼品卡金额
		this.$EventBus.$on('gift-price-value', (v) => {
			this.giftPrice = v;
			console.log('gift-price-value', this.giftPrice);
		});

		// 获取礼品卡密码
		this.$EventBus.$on('gift-pwd-value', (v) => {
			this.giftPwd = v;
			console.log('vvvvv', this.giftPwd);
		});

		this.$EventBus.$on('Displayphone', (v) => {
			this.show = v;
		});

		this.$EventBus.$on('showchange', (v) => {
			this.showchange = v.modalchange;
		});

		this.$EventBus.$on('showgetchange', (v) => {
			console.log('===showgetchange==created==', v);
			//礼品卡金额重置 0
			this.giftPrice = null;
			this.radioChange = v[1];
			this.changprop = v[0];
			this.orderConfirmDo();
		});
		// this.$EventBus.$on("smallchange", (v) => {
		//   console.log(this.usershow,'usershow')
		//   this.usershow = v;
		// });

		// this.pursepages();
	},
	onShow() {
		console.log(version.getversion(), 'version.getversion()', this.loading);
		// 获取实名认证返回条件 true 认证 false 未认证
		console.log('AuthenticationType====detail===>');
		console.log('app.globalData===>datail', app.globalData.AuthenticationType);
		if (app.globalData.AuthenticationType) {
			this.needUserCertified = app.globalData.AuthenticationType;
		}
		if (version.getversion()) {
			if (version.getversion() == '1') {
				this.AuthorInformation = true;
			} else if (version.getversion() == '2') {
				this.AuthorInformation = false;
			} else if (version.getversion() == '3') {
				this.AuthorInformation = false;
			}
			//用来校验礼品卡电子类型为[],不校验地址 自提
			if (JSON.stringify(this.availableDistributionModes) === '[]') {
				this.loading = false;
			}
		}
		console.log('this.loading', this.loading);
	},
	onLoad: function (option) {
		this.couponId = option?.couponId;
		this.couponId > 0 ? this.couponIds.push(this.couponId) : (this.couponIds = []);
		this.numberUtil = numberUtil;
		if (option) {
			// 导购订单
			const qrCodeScene = decodeURIComponent(option.scene);
			// 导购订单 id
			const qrCodeId = util.UrlParamHash(qrCodeScene, 'id');
			// 云Pos场景值
			const sayPosScene = getApp().globalData.sf;
			console.log('qrCodeId', sayPosScene);
			// 云PoS通过二维码进入
			if (qrCodeId && sayPosScene) {
				if (sayPosScene == 'sayPos') {
					this.paramOrderConfirm.source = 1;
				}
				this.shoppingGuideOrderId = qrCodeId;
			} else if (option.id) {
				this.shoppingGuideOrderId = option.id;
			} else if (option.prizeId) {
				this.paramOrderConfirm.source = 8; //松鼠乐园领取赠品类型
				this.prizeId = option.prizeId; //松鼠乐园赠品中奖id
			}
			console.log('option===>', qrCodeId, this.shoppingGuideOrderId);
		}

		//礼品卡金额重置 0
		this.giftPrice = null;

		//获取导购订单
		if (this.shoppingGuideOrderId) {
			console.log('22222222222222');
			this.orderConfirmDo();
			this.getSelfLimitDay();
		} else {
			console.log('11111111');
			this.userAddressPage();
		}
		// this.pointsConfigGet();
		this.userInfoGet();
	},
	methods: {
		//零钱选着最高抵扣
		async radioPurseDeductPrice(purseDeductPrice, radioChange) {
			console.log('=========', purseDeductPrice);
			if (purseDeductPrice && radioChange == '1') {
				this.changprop = purseDeductPrice;
			}
		},

		//获取礼品卡输入金额
		giftPriceValue(val) {
			this.giftPrice = val;
			console.log('giftPriceValue===>', this.giftPrice);
		},
		//礼品卡调用预下单
		giftOrderConfirm() {
			console.log('-------11111------');
			this.orderConfirmDo();
		},

		//礼品卡输入密码立即支付
		giftOrderSub() {
			console.log('-------111112222------');

			this.orderSub();
		},

		//获取自提天数
		getSelfLimitDay() {
			let that = this;
			api.getLivingTag().then((res) => {
				console.log('==自提天数========', res, res.data.selfLimitDay);
				if (res && res.data) {
					that.selfLimitDay = res.data.selfLimitDay ? res.data.selfLimitDay : null;
				}
			});
		},

		agreeGetUser(e) {
			app.initPage(true).then((res) => {
				if (e.detail.errMsg == 'getUserInfo:ok') {
					console.log('=获取头像昵称的数据getUserInfo==', e.detail);
					api.userInfoUpdateByMa(e.detail)
						.then((res) => {
							this.userInfo = res.data;
							uni.setStorageSync('user_info', this.userInfo);
							this.userInfoGet();
						})
						.catch((e) => {
							console.log('=更新头像昵称出错', e);
						});
				}
			});
		},

		// 切换配送方式
		distributionTypeChange(ind, type) {
			if (this.distributionModeIndex == ind) {
				return;
			}
			this.distributionModeIndex = ind;
			//礼品卡金额重置 0
			this.giftPrice = null;

			if (type == '2') {
				this.title = '同城配送';
				this.$nextTick(function () {
					// this.$refs.timeperiod?.computedTime()
					this.$EventBus.$emit('eventName', '2');
				});
			} else if (type == '1') {
				this.title = '上门自提';
				this.$nextTick(function () {
					this.$EventBus.$emit('eventName', '1');
				});
			}
			this.orderConfirmDo();
		},

		//获取默认收货地址
		userAddressPage() {
			//礼品卡金额重置 0
			this.giftPrice = null;
			api.userAddressPage({
				searchCount: false,
				current: 1,
				size: 1,
				isDefault: '1'
			}).then((res) => {
				let records = res.data.records;
				if (records && records.length > 0) {
					this.userAddress = records[0];
				}
				this.orderConfirmDo();
				this.getSelfLimitDay();
			});
		},

		//获取商城用户信息
		userInfoGet() {
			api.userInfoGet().then((res) => {
				this.userInfo = res.data;
			});
		},

		/**
		 *  获取订单里的商品
		 * 	获取缓存中的商品集合
		 *  把商品集合上送后台 返回对应的 以配送方式区分 店铺为维度的商品集合
		 * */
		async orderConfirmDo(val) {
			this.loading = true;
			uni.showLoading();
			// 本地获取参数信息
			let that = this;
			// let _paramOrderConfirm = uni.getStorageSync("param-orderConfirm");
			if (this.distributionModeIndex > -1 && this.distributionMode[this.distributionModeIndex].type == 2) {
				if (this.userAddress.id > 0 && !(this.userAddress.longitude > 0 && this.userAddress.latitude > 0)) {
					uni.showToast({
						title: '请先完善当前收货地址的经纬度',
						icon: 'none',
						duration: 2000
					});
					return;
				} else if (!this.userAddress.id) {
					uni.showToast({
						title: '请填写收货地址',
						icon: 'none',
						duration: 2000
					});
					return;
				}
			}

			let _query = {
				orderType: this.paramOrderConfirm.source, // 订单类型 1-普通；2-立即购买；3-秒杀；4-积分商城 5 付费优惠券  8 奖品订单
				distributionMode: this.distributionModeIndex > -1 ? this.distributionMode[this.distributionModeIndex].type : -1, // 配送方式：0-快递；1 自提  2同城配送
				isOpenPoint: this.availableIntegralState ? 1 : 0, //是否开启积分兑换：1-开始；0-不开启
				couponIds: this.couponIds, // 选择的优惠券列表
				// orderKey: this.orderKey, // 订单key
				userAddressId: this.userAddress.id,
				bargainHallId: this.paramOrderConfirm.bargainHallId
			};

			//抵扣运费
			if (val == 1) {
				_query.freightPointsSwitch = this.freightPointsSwitch ? 1 : this.freightPointsSwitchs;
			} else {
				delete _query.freightPointsSwitch;
			}

			//判断是否有零钱
			console.log('====this.changprop====', this.changprop, this.radioChange);
			if (this.radioChange && this.radioChange == '1') {
				_query.paymentPursePrice = '-1';
			} else {
				_query.paymentPursePrice = this.changprop;
			}

			//礼品卡金额，giftPrice  礼品卡支付金额 暂不使用 不传，
			if (this.giftPrice && (this.giftPrice != null || this.giftPrice != 'null')) {
				_query.giftPrice = this.giftPrice;
			} else {
				delete _query.giftPrice;
			}

			//礼品卡密码
			if (this.giftPwd) {
				_query.giftPwd = this.giftPwd;
			} else {
				delete _query.giftPwd;
			}
			//  paramOrderConfirm 缓存中的数据来源 source： 1-购物车；2-直接购买；3-秒杀；4-积分商城
			// if (this.paramOrderConfirm.source == 1) {
			// 	_query.shoppingCarItemIds = this.paramOrderConfirm.shoppingCarItemIds; // 购物车明细列表
			// } else {
			// 	_query.singleSpu = this.paramOrderConfirm.shoppingCarItemIds
			// }
			console.log('this.shoppingGuideOrderId==>', this.paramOrderConfirm.source, this.shoppingGuideOrderId);
			switch (this.paramOrderConfirm.source) {
				case 1:
					if (this.shoppingGuideOrderId) {
						_query.shoppingGuideOrderId = this.shoppingGuideOrderId; // 导购订单
					} else if (this.paramOrderConfirm.bargainId > 0) {
						_query.bargainId = this.paramOrderConfirm.bargainId; //砍价id
					} else {
						_query.shoppingCarItemIds = this.paramOrderConfirm.shoppingCarItemIds; // 购物车明细列表
					}
					break;
				case 2:
					_query.singleSpu = this.paramOrderConfirm.shoppingCarItemIds; // 直接购买
					break;
				case 4:
					_query.singleSpu = this.paramOrderConfirm.shoppingCarItemIds; // 积分商城
					break;
				case 3:
					_query.seckillId = this.paramOrderConfirm.shoppingCarItemIds; // 秒杀购买
					break;
				case 5:
					_query.singleSpu = this.paramOrderConfirm.shoppingCarItemIds; // 5付费优惠券
					break;
				case 6:
					if (this.paramOrderConfirm.type == 1) {
						// 详情购买
						_query.singleSpu = this.paramOrderConfirm.shoppingCarItemIds; // 商品id
						_query.groupPurchaseIds = this.paramOrderConfirm.groupPurchaseIds; // 拼团购-会场商品id
					} else if (this.paramOrderConfirm.type == 2) {
						// 购物车
						_query.shoppingCarItemIds = this.paramOrderConfirm.shoppingCarItemIds; // 购物车明细列表
					}
					_query.groupPurchaseHallId = this.paramOrderConfirm.groupPurchaseHallId; // 拼团购会场id
					_query.distributionMode = 1;
					break;
				case 7:
					_query.shoppingCarItemIds = this.paramOrderConfirm.shoppingCarItemIds; // 购物车明细列表
					break;
				case 8:
					_query.marketPrizeUserId = this.prizeId; // 松鼠乐园赠品中奖id
					break;
			}

			await api
				.getOrderGenerate(_query)
				.then((res) => {
					let _data = res.data;
					if (val != 1) {
						//服务端返回当前配送方式
						this.distributionMode = [];
						console.log(res.data, '=====');
						const { selectDistributionMode, availableDistributionModes, spuVosCache } = _data;
						console.log(selectDistributionMode, availableDistributionModes, 'availableDistributionModes');
						//针对开票获取商品，礼品卡商品类型 spuType== 6
						if (spuVosCache && spuVosCache.length > 0) {
							console.log('spuVosCache', spuVosCache[0].spuType);

							this.spuType = spuVosCache[0].spuType;
						}
						// 用来校验礼品卡电子类型为[],不展示
						this.availableDistributionModes = availableDistributionModes;
						//导购订单快递发货地址返显
						if (this.shoppingGuideOrderId && selectDistributionMode == '0' && res.data.userInfo) {
							this.userAddress = res.data.userInfo;
						}
						if (availableDistributionModes && availableDistributionModes.length > 0) {
							this.allDistributionModes.forEach((item, index) => {
								if (availableDistributionModes.indexOf(item.type) > -1) {
									this.distributionMode.push(item);
								}
							});
							console.log(this.distributionMode, 'this.distributionMode');
							// if(this.distributionMode[0].type=='2'){
							// 	 this.distributionModeIndex = 0
							// 	 // this.distributionTypeChange(0,'同城配送')
							// 	 this.title = '同城配送'
							// 	 this.$nextTick(function(){
							// 	 		this.$refs.timeperiod.computedTime()
							// 	 })
							// }else{

							// }
						}
						if (this.distributionMode && this.distributionMode.length > 0) {
							for (let i = 0; i < this.distributionMode.length; i++) {
								if (this.distributionMode[i].type == selectDistributionMode) {
									this.distributionModeIndex = i;
									if (this.distributionMode[i].type == '2') {
										this.title = '同城配送';
										this.$nextTick(function () {
											that.$EventBus.$emit('eventName', '2');
										});
									} else if (this.distributionMode[i].type == '1') {
										this.title = '上门自提';
										this.$nextTick(function () {
											that.$EventBus.$emit('eventName', '1');
										});
									}
									break;
								}
							}
						}
						if (this.paramOrderConfirm.source == '4') {
							// 0 置灰  1开启  2 关闭
							if (res.data.freightPointsSwitch == '0') {
								this.freightPointsSwitch == true;
							} else {
								this.freightPoints = res.data.freightPoints;
								this.freightPointsSwitch = false;
								this.freightPointsSwitchs = res.data.freightPointsSwitch;
							}
						}
					} else {
						this.freightPoints = res.data.freightPoints;
						this.freightPointsSwitchs = res.data.freightPointsSwitch;
					}

					this.totalPice = _data.totalPrice; // 订单总价
					console.log('this.totalPice', _data.totalPrice);
					this.promotionsPrice = _data.promotionsPrice; //折扣金额
					this.availableDeductionPoint = _data.availableDeductionPoint; // 积分兑换需要花费的积分
					this.shopList = _data.shopList; // 店铺集合

					this.unableListSpuList = _data.unableListSpuList; // 不可购买集合
					this.promotionsInfoMap = _data.promotionsInfoMap;
					this.totalPointsDeduct = _data.availableDeductionPoint; // 可用积分
					this.totalPointsDeductPriceTemp = _data.availableExchangePrice; // 积分可抵扣价格
					this.platformCoupons = _data.discountPrice; // 优惠价格
					this.available = _data.availableSelectCoupons; // 可选优惠券
					this.givePoints = _data.givePoints; // 积分赠送
					this.orderKey = _data.orderKey;

					this.groupHallVo = _data.groupHallVo;

					this.giftCardPrice = _data.giftCardPrice; // 可用礼品卡金额
					this.giftDeductPrice = _data.giftDeductPrice; //礼品卡抵扣金额

					this.needUserCertified = _data.needUserCertified; //礼品卡商品是否需要实名认证 0不需要  1 需要
					this.userCertifiedPrompt = _data.userCertifiedPrompt; //未实名认证提示词

					// 判断是否显示零钱或者礼品卡 1是零钱 2是礼品卡 没有就不展示
					this.payMode = _data.payMode; ////判断显示不显示1钱包2礼品卡

					this.pursePrice = _data.pursePrice; //零钱可用余额
					this.purseDeductPrice = _data.purseDeductPrice; //零钱抵扣金额
					this.purseStatus = _data.purseStatus; //零钱 没开通钱包：0；开通没签约：1；开通并签约：2
					this.paymentPursePrice = _data.paymentPursePrice; //零钱 自定义返显默认值
					this.originalPrice = _data.originalPrice; // 商品总价
					this.reductionPrice = _data.reductionPrice; // 共省价格

					// 传给零钱兄弟组件
					this.$EventBus.$emit('purse-price', this.pursePrice);
					this.$EventBus.$emit('purse-deduct-price', this.purseDeductPrice);
					console.log('order-confirm-orderConfirmDo===>', this.pursePrice, this.purseDeductPrice, this.purseStatus);

					if (_data.spuCoupons && _data.spuCoupons.length > 0) {
						_data.spuCoupons.forEach((item) => {
							if (!_data.availableSelectCoupons.includes(item.couponId) && !this.couponIds.includes(item.couponId)) {
								item.disabled = true;
							}
							if (item.couponId === this.couponId || this.couponIds.includes(item.couponId)) {
								item.checked = true;
							}
						});
						this.orderCoupon.spuCoupons = _data.spuCoupons; // 平台券列表
					}

					// 礼品卡电子类型不校验地址 提货方式
					if (this.availableDistributionModes && this.availableDistributionModes.length > 0) {
						if (val != 1) {
							if (+this.distributionMode[this.distributionModeIndex].type === 1) {
								// 判断是否是到店自提
								this.shopList = _data.shopList.map((item) => {
									if (item.shopTakeAddressList && item.shopTakeAddressList.length === 1) {
										item.shopHoldObj = item.shopTakeAddressList[0];
									}
									return item;
								});
							}
						}
					}

					this.orderCoupon.gifts = _data.giveGoodsSpu; // 赠送商品
					this.loading = false;
					// this.$EventBus.$emit('Calculateprice',this.totalPice)
					uni.hideLoading();
				})
				.catch((e) => {
					this.loading = true;
				});
		},

		changeModal(value) {
			this.showchange = false;
		},
		// 无法购买
		showModalService() {
			this.modalService = 'show';
		},
		// 使用积分开关

		changeAvailableIntegral(e, num) {
			// val 0 抵扣金额 1是抵扣运费
			let val = num;
			//礼品卡金额重置 0
			this.giftPrice = null;
			if (e) {
				if (val == 1) {
					this.freightPointsSwitch = e.detail.value;
				} else {
					this.availableIntegralState = e.detail.value;
				}
			}

			this.orderConfirmDo(val);
		},

		// 平台优惠券
		showPlatformModalCoupon() {
			this.modalCoupon1 = 'show';
		},

		// 关闭 无法购买或者优惠券弹窗
		hideModalCoupon() {
			this.modalCoupon1 = '';
			this.modalService = '';
		},

		// 发票信息弹框
		invoiceOpen() {
			this.invoiceFlag = true;
			// 获取make-invoice组件的引用
			const makeInvoice = this.$refs.invoice;
			// 获取uni-popup组件的引用
			const uniPopup = makeInvoice.$refs.invoicePopup;
			// 调用uni-popup组件中的open方法
			uniPopup.open('bottom');
		},
		invoiceClose(val) {
			this.invoiceFlag = false;
			if (2 == val) {
				this.invoiceState = false;
			}
			if (3 == val) {
				this.invoiceState = true;
			}
		},

		// 选中平台券
		checkboxChange(e) {
			this.couponIds = e.detail.value;
			this.couponId = '';
			//礼品卡金额重置 0
			this.giftPrice = null;
			this.orderConfirmDo();
		},

		// 判断两个数组是否有相同的元素
		arrayIsEqual(arr1, arr2) {
			let arrFlag = false;
			arr1.forEach((element, index) => {
				if (arr1.indexOf(arr2[index]) != -1 && arr2.indexOf(element) != -1) {
					arrFlag = true;
				}
			});
			return arrFlag;
		},

		/**
		 * 判断对象的值是不是全为空
		 */
		objectValueAllEmpty(object) {
			var isEmpty = false;

			Object.keys(object).forEach(function (x) {
				if (object[x].includes('')) {
					isEmpty = true;
				}
			});
			return isEmpty;
		},

		// //提交订单
		async orderSub() {
			let that = this;
			let userAddress = this.userAddress;
			let id;
			if (this.paramOrderConfirm.shoppingCarItemIds) {
				id = this.paramOrderConfirm.shoppingCarItemIds;
			} else {
				id = uni.getStorageSync('param-orderConfirm').shoppingCarItemIds;
			}
			let goodsIds = this.paramOrderConfirm.goodsIds;
			if (goodsIds) {
				goodsIds.forEach((e) => {
					if (this.arrayIsEqual(goodsIds, this.spuId) && this.userMessageList[e] == '') {
						uni.showToast({
							title: '请选择填写备注',
							icon: 'none',
							duration: 3000
						});
						return;
					}
				});

				if (this.arrayIsEqual(goodsIds, this.spuId) && Object.keys(that.userMessageList).length == 0) {
					uni.showToast({
						title: '请选择填写备注',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (this.arrayIsEqual(goodsIds, this.spuId) && Object.keys(that.userMessageList).length != 0 && this.isMsg) {
					uni.showToast({
						title: '请选择填写备注',
						icon: 'none',
						duration: 3000
					});
					return;
				}
			}

			if (id?.spuId && this.spuId.includes(id.spuId) && Object.keys(that.userMessageList).length == 0) {
				uni.showToast({
					title: '请选择填写备注',
					icon: 'none',
					duration: 3000
				});
				return;
			}

			if (id?.spuId && this.spuId.includes(id.spuId) && Object.keys(that.userMessageList).length != 0 && this.isMsg) {
				uni.showToast({
					title: '请选择填写备注',
					icon: 'none',
					duration: 3000
				});
				return;
			}

			// if (this.orderSubParm.deliveryWay != '2' && (userAddress == null || !userAddress.userName)) {
			// 礼品卡电子类型不校验地址 提货方式
			if (this.availableDistributionModes && this.availableDistributionModes.length > 0) {
				//导购订单不校验
				if (!this.shoppingGuideOrderId) {
					console.log(
						'1111111',
						(this.distributionMode[this.distributionModeIndex].type == 0 || this.distributionMode[this.distributionModeIndex].type == 2) &&
							(userAddress == null || !userAddress.userName)
					);
					if (
						(this.distributionMode[this.distributionModeIndex].type == 0 || this.distributionMode[this.distributionModeIndex].type == 2) &&
						(userAddress == null || !userAddress.userName)
					) {
						uni.showToast({
							title: '请选择收货地址',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				} else if (this.shoppingGuideOrderId && this.distributionMode[this.distributionModeIndex].type == 0 && (userAddress == null || !userAddress.nickName)) {
					console.log(
						'22222',
						this.shoppingGuideOrderId && this.distributionMode[this.distributionModeIndex].type == 0 && (userAddress == null || !userAddress.nickName)
					);
					uni.showToast({
						title: '请选择收货地址',
						icon: 'none',
						duration: 2000
					});
					return;
				}
			}
			//超市接龙不需要选自提点
			if (this.btnDisable !== -1 && this.paramOrderConfirm.source != 6) {
				uni.showToast({
					title: `请选择${this.shopList[this.btnDisable].shopName}的提货地址`,
					icon: 'none',
					duration: 2000
				});
				return;
			}
			/**
			 * 是否可以使用积分抵扣(0:否；1:是)：
			 * 没有积分 isPointsBeDeducted 为0； 有积分 isPointsBeDeducted 为1  */
			// if (!this.orderSubParm || this.orderSubParm < 0) {
			// 	this.orderSubParm.isPointsBeDeducted = 0
			// }
			this.loading = true;

			const schoolUserInfo = util.getStorage(2, 'school_user_info');

			if (this.shopList && this.shopList.length > 0 && schoolUserInfo && schoolUserInfo.name && schoolUserInfo.name != 'undefined') {
				this.shopList.forEach((item) => {
					if (this.userMessageList[item.shopId]) {
						this.userMessageList[
							item.shopId
						] += ` 性别:${schoolUserInfo.gender};姓名:${schoolUserInfo.name};学生Id:${schoolUserInfo.student_id};联系方式:${schoolUserInfo.parent_contact}`;
					} else {
						this.userMessageList[
							item.shopId
						] = `性别:${schoolUserInfo.gender};姓名:${schoolUserInfo.name};学生Id:${schoolUserInfo.student_id};联系方式:${schoolUserInfo.parent_contact}`;
					}
				});
			}

			console.log('===提交订单支付==》', this.giftPrice);
			/**
			 * 礼品卡
			 * */
			// 1校验有没有设置密码 isActive 0：未开通 1：已开通支付密码
			// a 没有设置去设置
			// b 设置密码判断有没有设置免密 isPasswordless 0：未开通 1：已开通免密支付
			// b1 设置免密直接支付  passwordlessAmount  免密金额
			// b2 没有设置免密弹框输入密码
			let giftPassword = uni.getStorageSync('paypaymentuserpassword');
			console.log('giftPassword', giftPassword, this.giftPrice);
			if (this.giftPrice) {
				that.modalDialog = '';
				that.isModalDialog = false;
				if (giftPassword) {
					if (giftPassword.isActive == '0') {
						uni.navigateTo({
							url: `/pages/gift/set-password/code/index`
						});
						return;
					}
					// 输入金额大于免密金额 去输入密码
					let deductPrice = price.numberCompare(this.giftPrice, giftPassword.passwordlessAmount);
					if (giftPassword.isPasswordless == '1' && deductPrice !== 'greater') {
						console.log('开启免密小于免密金额===>', this.giftPrice);
						if (this.giftPrice != '0') {
							await that.orderConfirmDo();
						}
					} else {
						console.log('关闭免密或大于免密金额===>', this.giftPrice);
						if (!this.giftPwd) {
							that.isModalDialog = true;
							that.modalDialog = 'cu-modal show ';
							that.$nextTick(() => {
								that.$refs.giftPwdDialog.countdown();
							});
							that.loading = false;
							return;
						}
					}
				}
			}

			if (this.needUserCertified == '1') {
				let that = this;
				uni.showModal({
					title: '实名认证',
					content: this.userCertifiedPrompt,
					confirmText: '实名认证',
					confirmColor: '#ff4444',
					success(res) {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/user/user-authentication/index'
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
							that.loading = false;
						}
					}
				});
				return;
			}
			// availableDistributionModes 为空就是礼品卡电子类型约定传 '1'
			let distribution = this.availableDistributionModes && this.availableDistributionModes.length > 0 ? this.distributionMode[this.distributionModeIndex].type : '1';
			let _query = {
				userAddressId: that.orderSubParm.deliveryWay == '1' ? userAddress.id : null,
				couponIds: that.couponIds,
				// orderTotalPrice: numberUtil.numberSubtract(that.paymentPrice, that.platformCoupons)
				orderTotalPrice: this.totalPice,
				buyerMessage: that.userMessageList,

				orderType: this.paramOrderConfirm.source, // 订单类型 1-普通；2-立即购买；3-秒杀；4-积分商城
				distributionMode: distribution, // 配送方式：1-快递；2 自提
				isOpenPoint: this.availableIntegralState ? 1 : 0, //是否开启积分兑换：1-开始；0-不开启
				// orderType: this.paramOrderConfirm.source, // 订单类型 1-普通；2-立即购买；3-秒杀；4-积分商城
				isOpenPoint: this.availableIntegralState ? 1 : 0, //是否开启积分兑换：1-开始；0-不开启
				orderKey: this.orderKey,
				relationId: '',
				invoiceRemark: that.orderSubParm.invoiceRemark
				// deliveryTime: this.sureDeliveryTime,
				// takeTime:this.takeTime
			};

			//礼品卡金额，giftPrice  礼品卡支付金额 暂不使用 不传，
			if (this.giftPrice && (this.giftPrice != null || this.giftPrice != 'null')) {
				_query.giftPrice = this.giftPrice;
			} else {
				delete _query.giftPrice;
			}

			//礼品卡密码
			if (this.giftPwd) {
				_query.giftPwd = this.giftPwd;
			} else {
				delete _query.giftPwd;
			}

			//判断是否有零钱
			if (this.changprop && this.changprop != '-1' && this.changprop != '0') {
				_query.paymentPursePrice = this.changprop;
			} else if (this.changprop == '-1' && this.purseDeductPrice && this.purseDeductPrice != '0') {
				_query.paymentPursePrice = this.purseDeductPrice;
			} else {
				delete _query.paymentPursePrice;
			}

			if (this.sureDeliveryTime && this.sureDeliveryTime.indexOf('undefined') == -1) {
				_query.deliveryTime = this.sureDeliveryTime;
			}
			if (this.takeTime && this.takeTime.indexOf('undefined') == -1) {
				_query.takeTime = this.takeTime;
			}
			switch (this.paramOrderConfirm.source) {
				case 1:
					if (this.shoppingGuideOrderId) {
						_query.shoppingGuideOrderId = this.shoppingGuideOrderId; // 导购订单
					} else if (this.paramOrderConfirm.bargainId > 0) {
						_query.bargainId = this.paramOrderConfirm.bargainId; //砍价id
					} else {
						_query.shoppingCarItemIds = this.paramOrderConfirm.shoppingCarItemIds; // 购物车明细列表
					}
					break;
				case 2:
					_query.singleSpu = this.paramOrderConfirm.shoppingCarItemIds; // 直接购买
					break;
				case 3:
					_query.seckillId = this.paramOrderConfirm.shoppingCarItemIds; // 秒杀购买
					break;
				case 6:
					if (this.paramOrderConfirm.type == 1) {
						// 详情购买
						_query.singleSpu = this.paramOrderConfirm.shoppingCarItemIds; // 商品id
						_query.groupPurchaseIds = this.paramOrderConfirm.groupPurchaseIds; // 拼团购-会场商品id
					} else if (this.paramOrderConfirm.type == 2) {
						// 购物车
						_query.shoppingCarItemIds = this.paramOrderConfirm.shoppingCarItemIds; // 购物车明细列表
					}
					_query.groupPurchaseHallId = this.paramOrderConfirm.groupPurchaseHallId; // 拼团购会场id
					_query.distributionMode = 1;
					break;
				case 7:
					_query.shoppingCarItemIds = this.paramOrderConfirm.shoppingCarItemIds; // 购物车明细列表
					break;
				case 8:
					_query.marketPrizeUserId = this.prizeId; // 松鼠乐园赠品中奖id
					_query.relationId = this.prizeId; // 松鼠乐园赠品中奖id
					break;
			}

			if (this.paramOrderConfirm.relationId > 0 && null == this.prizeId) {
				_query.relationId = this.paramOrderConfirm.relationId; //砍价时候需要,秒杀时需要
			}

			let shopTakeAddressIdMap = {};
			const distributionModeIndex = this.distributionModeIndex;
			const distributionMode = this.distributionMode;
			const type = distributionMode[distributionModeIndex]?.type;
			if (+type === 1 && this.paramOrderConfirm.source != 6) {
				this.shopList.forEach((item) => {
					shopTakeAddressIdMap[item.shopHoldObj.shopId] = item.shopHoldObj.id;
				});
				_query.shopTakeAddressIdMap = shopTakeAddressIdMap;
			}

			// 找人代付为 5，使用的是微信支付
			_query.paymentType = this.$refs.pay?.paymentType == 5 ? 1 : this.$refs.pay?.paymentType;
			console.error("=======orderSub=======");
			api.orderSub(_query)
				.then((res) => {
					that.orderSubAfter(res.data);
					const { listOrderInfo, paymentType } = res.data;
					that.listOrderInfo = listOrderInfo;
					if (that.listOrderInfo && that.listOrderInfo.length > 0) {
						that.listOrderInfo.forEach((order) => {
							const sub_orders = [];
							// 开票需要订单id
							let orders = [];
							orders.push({
								orderId: order.id,
								spuType: this.spuType
							});
							//神策锚点 发送提交订单
							handleSenOrder(order, paymentType,  _query.seckillId);
							that.changeInvoicing(true, orders);
						});
					}
				})
				.catch((error) => {
					console.error(error)
					that.changeInvoicing(false);
					this.loading = false;
				});
			return;
		},


		//是否开票
		changeInvoicing(bool, ord) {
			console.log('=====id===', bool, ord);
			if (bool && this.invoiceState) {
				this.$refs?.invoice.onlineOrderPreInvoiceOpen(ord);
			}
		},

		//订单提交成功后处理
		orderSubAfter(data) {
			let that = this;
			//提交接龙订单成功后发通知，告知接龙页面购物车已选中的商品全部移除，清空seleckGoods数组，否则报错
			if (that.paramOrderConfirm.source == 6) {
				uni.$emit('creatJielongOrder');
			}

			data.paymentPrice = this.totalPice;
			// #ifdef MP-WEIXIN
			//微信小程序订阅消息
			api.wxTemplateMsgList({
				enable: '1',
				useTypeList: ['2', '3']
			}).then((res) => {
				let tmplIds = [];
				res.data.forEach((item) => {
					tmplIds.push(item.priTmplId);
				});
				uni.requestSubscribeMessage({
					tmplIds: tmplIds,
					success(res) {},
					complete() {
						that.toOrderPage(data);
					}
				});
			});
			// #endif
			// #ifndef MP-WEIXIN
			//非微信小程序
			this.toOrderPage(data);
			// #endif
		},

		// 支付
		toOrderPage(data) {
			try {
				this.loading = true;
				this.$refs.pay?.payOrder(data, false, this.paramOrderConfirm.source); //this.paramOrderConfirm.source 用于判断是否跳转到接龙订单列表
			} catch (e) {
			} finally {
				this.loading = false;
			}
		},

		// 配送地址选择
		setUserAddress(obj) {
			//礼品卡金额重置 0
			this.giftPrice = null;
			this.userAddress = obj;
			this.orderConfirmDo();
		},

		// 买家留言
		userMessageInput(e) {
			let _key = e.target.dataset.shopinfo.shopId,
				_value = e.detail.value;
			if (this.userMessageList[_key]) {
				this.userMessageList[_key] += _value;
			} else {
				this.userMessageList[_key] = _value;
			}
			let goodsList = e.target.dataset.shopinfo.spuList;
			let cartSpuListId = goodsList.map((item) => item.spuId);

			if (this.arrayIsEqual(cartSpuListId, this.spuId) && this.userMessageList[_key] == '') {
				this.isMsg = true;
				return;
			} else {
				this.isMsg = false;
			}
		},

		handleDeliveryTime(e, type) {
			if (type == '1') {
				this.sureDeliveryTime = '';
				this.takeTime = e;
			} else if (type == '2') {
				this.takeTime = '';
				this.sureDeliveryTime = e;
			} else {
				this.takeTime = '';
				this.sureDeliveryTime = '';
			}
			console.log(e);
		},
		// 更改店铺地址
		updateShopAddress(obj) {
			const { shopIdAdreess } = this;
			const shopObj = this.shopList.find((item) => item.shopId === shopIdAdreess);
			this.$set(shopObj, 'shopHoldObj', obj);
			// this.shopHoldObj = obj;
			// this.$parent.shopHoldObj = this.shopHoldObj;
		},
		// 选择提货地址
		navHoldTor(shopIndex, id) {
			const { shopId, shopTakeAddressList = [] } = this.shopList[shopIndex];
			const { length } = shopTakeAddressList;
			if (length > 1) {
				this.shopIdAdreess = shopId;
				let url = '/pages/user/user-hold-address/index?shopId=' + shopId;
				if (+id) {
					url += `&addressId=${id}`;
				}
				uni.navigateTo({
					url
				});
			}
		},

		// 银行卡刷新
		updataBankList() {
			this.$refs.pay?.getBankList();
		}
	}
};
</script>

<style>
checkbox::before {
	right: 2px !important;
	/* top: 10px !important; */
}

/* checkbox .wx-checkbox-input,
	checkbox .uni-checkbox-input {
		width: 20px;
		height: 20px;
	} */
</style>
<style scoped lang="scss">
.pickup-tips {
	width: 650rpx;
	height: 55rpx;
	background: #f1fed1;
	border-radius: 28rpx;
	line-height: 55rpx;
	text-align: center;
	color: #fd7e47;
	margin: 23rpx auto 0;
}

.Tips {
	display: flex;
	background-color: #fdf7f7;
	margin: 30rpx 30rpx 0rpx;
	padding: 30rpx 25rpx;
	border-top-left-radius: 15rpx;
	border-top-right-radius: 15rpx;

	.Tips-title {
		font-size: 22rpx;
		width: 30%;
		color: #e54d42;
		font-weight: 700;
	}

	.Tips-text {
		font-size: 20rpx;
		color: #555555;
	}
}

.location-bg {
	height: 180rpx !important;
	background-repeat: repeat-x;
	background-position: left bottom;
	background-size: auto 4rpx;
}

.addressBackg {
	background-image: url('https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/14999eea-9c3b-4617-914e-b37c1cdae3ab.png');
}

.row-img {
	width: 180rpx !important;
	height: 180rpx !important;
	border-radius: 10rpx;
}

.text-price-two {
	float: left;
	line-height: 84rpx;
}

.specification {
	white-space: normal;
	height: auto;
}

.loc-content {
	width: calc(100% - 96rpx - 60rpx - 80rpx) !important;
	left: 30rpx !important;
}

.loc-info {
	line-height: 1.4em;
}

.cu-list.menu > .cu-item:after {
	border-bottom: unset !important;
}

.cu-list.menu-avatar > .cu-item {
	height: auto;
	padding: 20rpx;
	min-height: unset !important;
}

.cu-list.menu > .cu-item {
	min-height: unset !important;
}

.delivery-way {
	justify-content: unset !important;
}

.cu-item-box {
	padding: 16rpx 0;

	.cu-bar {
		min-height: auto;
		padding-bottom: 30rpx;
	}

	.cu-bar:last-child {
		padding-bottom: 0;
	}
}

.gifts-box {
	display: flex;

	.gifts-title {
		font-size: 24rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
	}

	.gifts-list {
		flex: 1;
		width: calc(100% - 40rpx - 64rpx);

		// text-align: right;
		.gift-name {
			font-size: 24rpx;
			line-height: 42rpx;
			display: flex;

			.name {
				display: block;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				width: calc(100% - 44rpx);
			}
		}
	}
}

// 配送方式
.distributionBox {
	// width: 100%;
	justify-content: space-between;
	position: relative;
	display: flex;
	padding-right: 10rpx;
	align-items: center;
	padding: 0 20rpx 0;

	.flex-1 {
		flex: 1;
		height: 80rpx;
		border-radius: 80rpx;
		background: #fff;
		color: #ff4444;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: 10rpx;
	}

	.flex-1.hover {
		background-color: #ff4444;
		color: #fff;
	}
}

.regionBox {
	font-size: 26rpx;
	color: #000000;
	line-height: 26rpx;
}

.detailedAddressBox {
	font-size: 28rpx;
	font-weight: bold;
	color: #242223;
	line-height: 30rpx;
	padding: 20rpx 0;
}

.personalInfoBox {
	font-size: 22rpx;
	color: #000000;
	line-height: 22rpx;
	padding-bottom: 20rpx;
}

.cu-card > .cu-item {
	border-radius: 20rpx;
}

.text-height {
	line-height: 60rpx;
}

.text-col {
	color: #000000;
}

.prompt {
	// height: 25px;
	font-size: 24rpx;
	color: #000000;
	padding-left: 26rpx;
	padding-bottom: 16rpx;
}

.unqualified-goods {
	padding: 0 20rpx;
}

.prompt-goods-list {
	display: flex;
	min-width: 0;
	overflow: hidden;

	.prompt-goods-img-box {
		margin-right: 20rpx;

		.prompt-goods-img {
			width: 160rpx;
			height: 160rpx;
		}

		&:last-child {
			margin-right: 0;
		}
	}
}

.prompt-goods-length {
	padding-left: 20rpx;
	white-space: nowrap;
}

// 自提地址
.selfAddressBox {
	width: 100%;
	box-sizing: border-box;
	display: flex;

	> view:last-child {
		flex: 1;
	}
}

.arrow-width {
	padding-right: 27rpx;
}
</style>
<style lang="scss">
.makeInvoice-page {
	.uni-popup {
		// 防止底部提交订单遮挡
		z-index: 1500;
	}
}
</style>
