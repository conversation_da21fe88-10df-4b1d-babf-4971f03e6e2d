<template>
  <view style="position: relative;">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">退换/售后</block>
    </cu-custom>

    <view>
        <view class="cu-card article">
          <view
            class="cu-item"
            style="margin: 20rpx 12rpx; border-radius: 24rpx; padding: 24rpx; position: relative; overflow: initial;"
            v-for="(item, index) in orderList"
            :key="index"
          >
            <orderRefundsList
              :info="item"
              :index="index"
              :parameter="parameter"
              @orderDel="orderDel($event, index)"
              @replaceOrder="replaceOrder($event, index)"
            />
          </view>
        </view>
      <view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
    </view>
  </view>
</template>

<script>
const QR = require("utils/wxqrcode.js");
const util = require("utils/util.js");
const app = getApp();

import api from 'utils/api'

import orderRefundsList from '../components/refunds.vue';

export default {
  components: { orderRefundsList, },
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      noClick: true,
      page: {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: '',
        //升序字段
        descs: 'create_time'
      },
      parameter: {},
      loadmore: true,
      orderList: [],
      listOrderInfo: [],
      //勾选的合并支付的订单
      //有数统计使用
      page_title: '订单列表',
    };
  },
  onShow () {
    app.initPage().then(res => {
      this.loadmore = true;
      this.orderList = [];
      this.page.current = 1;
      this.orderPage();
    });
  },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.orderPage();
    }
  },

  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框
    uni.hideNavigationBarLoading(); // 停止下拉动作
    uni.stopPullDownRefresh();
  },

  methods: {
    orderPage () {
      api.orderRefundsList(Object.assign({}, this.page)).then(res => {
        let orderList = res.data.records;
        orderList.forEach((item) => {
          if(item.deliveryWay == '2' && item.status == '1'){
            item.qrImg = QR.createQrCodeImg('/pages/mall/order/detail?id=' + item.id, {
              size: parseInt(300) //二维码大小  
            });
          }
        })
        this.orderList = [...this.orderList, ...orderList];
        if (orderList.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },

    refresh () {
      this.loadmore = true;
      this.orderList = [];
      this.page.current = 1;
      this.orderPage();
    },
    //上报有数
    compareUploadParams (orderInfo) {
      const sub_orders = [];
      sub_orders.push({
        "sub_order_id": orderInfo.orderNo,
        "order_amt": Number(orderInfo.salesPrice),
        "pay_amt": Number(orderInfo.paymentPrice),
      })
      return {
        sub_orders,
        "page_title": this.page_title,
        "order": {
          "order_id": orderInfo.orderNo,
          "order_time": new Date(util.getDateTimeForIOS(orderInfo.createTime)).getTime() || Date.parse(new Date()),
          "order_status": "pay"
        },
      }
    },

    // 删除订单
    orderDel (item, index) {
      this.orderList.splice(index, 1);
    },
    // 查询订单数据并替换列表对应数据
    replaceOrder (item, index) {
      api.orderRefundsGet(this.orderList[index].id).then(res => {
        this.$set(this.orderList, index, res.data)
      });
    }
  }
};
</script>
<style scoped lang="scss">
.order-search{
  padding: 20rpx 26rpx 10rpx;
  .input{
    width: 614rpx;
    height: 68rpx;
    background: #FFFFFF;
    border-radius: 34rpx;
    font-size: 30rpx;
    color: #828282;
    padding: 0 40rpx;
  }
  .dressing{
    font-size: 30rpx;
    color: #000000;
  }
}
.nav {
  top: unset !important;
}

.scale {
  margin-right: 5rpx;
  transform: scale(0.8);
}

.footer {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 80rpx;
  z-index: 9999;
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.search-order-dialog {
  padding: 30rpx 34rpx 80rpx;
  position: relative;
  .title{
    font-size: 33rpx;
    font-weight: 800;
    color: #000000;
    padding-bottom: 30rpx;
    border-bottom: 2rpx dotted #000;
  }
  .close{
    position: absolute;
    top: 32rpx;
    right: 36rpx;
    color: rgb(140, 140, 140);
  }
  .detail {
    margin-top: 28rpx;
    color: #000;
    font-size: 28rpx;
    .item{
      margin-top: 40rpx;
      &:first-child{
        margin-top: 0;
      }
      .value{
        padding: 12rpx 50rpx;
        background: #F5F5F5;
        border-radius: 33rpx;
        margin-left: 16rpx;
        border: 1rpx solid #F5F5F5;
      }
      .active{
        background: #fff;
        color: #C09979;
        border: 1rpx solid #C09979;
      }
    }
  }
  .footer{
    padding-bottom: 80rpx;
    .button{
      width: 340rpx;
      height: 80rpx;
      border-radius: 40rpx;
    }
    .reset{
      background: #fff;
      color: #C09979;
      border: 2rpx solid #C09979;
    }
    .submit{
      background: #C09979;
      color: #fff;
      border: 2rpx solid #C09979;
    }
  }
}
.search-page{
  width: 100%;
  height: 100%;
  position: absolute;
  top: 300rpx;
  left: 0;
  background: #F1F1F1;
  
  .search-history{
    padding: 30rpx 36rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
  }
  .search-title{
    font-size: 33rpx;
    color: #000000;
  }
  .search-name{
    // display: block;
    padding: 20rpx 27rpx;
    background: #F6F6F6;
    border-radius: 33rpx;
    font-size: 28rpx;
    color: #666666;
  }
}
</style>
