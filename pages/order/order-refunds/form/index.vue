<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">订单退款</block>
    </cu-custom>

    <view class="cu-bar bg-white solid-bottom">
      <view class="action">
        <text class="cuIcon-titles" :class="'text-' + theme.themeColor"></text
        >退款信息
      </view>
    </view>

    <view v-if="status != 1" class="cu-card no-card article margin-bottom-bar">
      <view class="cu-item">
        <view class="content response align-center">
          <image
            :src="
              orderItem.picUrl
                ? orderItem.picUrl
                : 'https://img.songlei.com/live/img/no_pic.png'
            "
            mode="aspectFill"
            class="row-img margin-top-xs"
          ></image>
          <view class="desc row-info block">
            <view class="text-black margin-top-sm overflow-2">{{
              orderItem.spuName
            }}</view>
            <view
              class="text-gray text-sm margin-top-sm overflow-2"
              v-if="orderItem.specInfo"
            >
              {{ orderItem.specInfo }}</view
            >
            <view class="flex justify-between align-center">
              <view class="text-price text-gray text-sm margin-top-sm">{{
                orderItem.salesPrice
              }}</view>
              <view class="text-gray text-sm margin-top-sm padding-lr-sm"
                >x{{ orderItem.quantity }}</view
              >
            </view>
          </view>
        </view>
      </view>

      <view
        class="cu-item solid-top"
        v-for="(item, index) in orderRefundsList"
        :key="index"
      >
        <view class="padding-lr padding-tb-xs text-gray"
          >发起时间：{{ item.createTime }}</view
        >
        <view
          class=""
          v-if="
            orderItem.listOrderRefunds[0].orderLogistics &&
            (orderItem.status != '1' || orderItem.status != '2')
          "
        >
          <view
            class="cu-item solid-top"
            style="padding-bottom: -60rpx; !important"
            v-if="
              (orderItem.listOrderRefunds[0].orderInfo.deliveryWay == '1' &&
                orderItem.listOrderRefunds[0].status == '21') ||
              item.statusDesc == '收到退货同意退款'
            "
          >
            <view
              class="padding-lr padding-tb-xs"
              style="
                background: white;
                padding-left: 30rpx;
                font-size: 30rpx;
                font-weight: 700;
              "
            >
              退款收货地址 :
              {{ orderItem.listOrderRefunds[0].orderLogistics.address }}
            </view>
          </view>
          <view
		  v-if="
            orderItem.listOrderRefunds[0].orderLogistics &&
            (orderItem.status != '1' || orderItem.status != '2')
          "
            class="padding-lr padding-tb-xs"
            style="
              padding-left: 30rpx;
              display: flex;
              align-items: center;
              background: white;
              font-weight: 700;
            "
          >
            物流单号 :
            <input
              type="text"
              style="display: flex; align-items: center"
              v-model="logisticsNo"
              placeholder="请输入物流单号"
              :disabled="item.statusDesc == '收到退货同意退款' ? true : false"
            />
            <view
              v-if="item.statusDesc == '收到退货同意退款' ? false : true"
              class=""
              hover-class="none"
              hover-stop-propagation="false"
              @click="LogisticsorderNo"
              style="
                width: 140rpx;
                padding: 11rpx;
                background: red;
                border-radius: 27rpx;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 39rpx;
                margin-right: 10rpx;
                height: 42rpx;
              "
            >
              提交
            </view>
          </view>
        </view>
        <view class="padding-lr padding-tb-xs"
          >退款状态：<text class="cu-tag radius line-red">{{
            item.statusDesc
          }}</text>
        </view>
        <view class="padding-lr padding-tb-xs"
          >退款金额：<text class="text-price text-bold text-red">{{
            item.refundAmount
          }}</text>
        </view>
        <view class="padding-lr padding-tb-xs"
          >退款积分：<text class="text-bold text-red">{{
            orderItem.paymentPoints
          }}</text>
        </view>
        <view class="padding-lr padding-tb-xs"
          >退款数量：x{{ orderItem.quantity }}</view
        >
        <view class="padding-lr padding-tb-xs" v-if="index == 0"
          >是否到账：<text
            :class="
              'cu-tag radius line-' +
              (orderItem.isRefund == '1' ? 'green' : 'red')
            "
            >{{ orderItem.isRefund == "1" ? "是" : "否" }}</text
          >
        </view>
        <view class="padding-lr padding-tb-xs" v-if="item.refuseRefundReson"
          >拒绝原因：{{ item.refuseRefundReson }}
        </view>
        <view class="padding-lr padding-tb-xs">
          <view>退款原因：</view>
          <view>{{ item.refundReson === null ? "无" : item.refundReson }}</view>
        </view>
      </view>
    </view>
    <view v-else class="cu-card no-card article margin-bottom-bar">
      <view
        v-for="(item, index) in orderItem"
        :key="index"
        class="cu-card no-card article"
      >
        <view class="cu-item">
          <view class="content response align-center">
            <image
              :src="item.picUrl ? item.picUrl : 'https://img.songlei.com/live/img/no_pic.png'"
              mode="aspectFill"
              class="row-img margin-top-xs"
            ></image>
            <view class="desc row-info block">
              <view class="text-black margin-top-sm overflow-2">{{
                item.spuName
              }}</view>
              <view
                class="text-gray text-sm margin-top-sm overflow-2"
                v-if="item.specInfo"
              >
                {{ item.specInfo }}</view
              >
              <view class="flex justify-between align-center">
                <view class="text-price text-gray text-sm margin-top-sm">{{
                  item.salesPrice
                }}</view>
                <view class="text-gray text-sm margin-top-sm padding-lr-sm"
                  >x{{ item.quantity }}</view
                >
              </view>
            </view>
          </view>
        </view>

        <view class="cu-item solid-top">
          <view class="padding-lr padding-tb-xs text-gray"
            >发起时间：{{ item.createTime }}</view
          >
          <view class="padding-lr padding-tb-xs"
            >退款状态：<text class="cu-tag radius line-red">{{
              item.statusDesc
            }}</text>
          </view>
          <view class="padding-lr padding-tb-xs"
            >退款金额：<text class="text-price text-bold text-red">{{
              item.paymentPrice
            }}</text>
          </view>
          <view class="padding-lr padding-tb-xs" v-if="orderItem.paymentPoints"
            >退款积分：<text class="text-bold text-red">{{
              item.paymentPoints
            }}</text>
          </view>
          <view class="padding-lr padding-tb-xs"
            >退款数量：x{{ item.quantity }}</view
          >
          <view
            class="padding-lr padding-tb-xs"
            v-if="item.isRefund == '1' || item.isRefund == '0	'"
            >是否到账：<text
              :class="
                'cu-tag radius line-' + (item.isRefund == '1' ? 'green' : 'red')
              "
              >{{ item.isRefund == "1" ? "是" : "否" }}</text
            >
          </view>
          <view class="padding-lr padding-tb-xs" v-if="item.refuseRefundReson"
            >拒绝原因：{{ item.refuseRefundReson }}
          </view>
          <view class="padding-lr padding-tb-xs">
            <view>退款原因：</view>
            <view>{{ item.refundReson }}</view>
          </view>
        </view>
      </view>
    </view>

    <view class="cu-bar bg-white tabbar foot justify-end">
      <!-- <navigator
        class="cu-btn round margin-right shadow-blur"
        open-type="navigate"
        :url="'/pages/customer-service/customer-service-list/index?shopId='+orderItem.shopId"
      >
        <view class="cuIcon-servicefill">联系客服</view>
      </navigator> -->
      <view
        class=""
        @click="tkorder"
        v-if="orderItem.status == '1' || orderItem.status == '2'"
        style="
          width: 202rpx;
          padding: 11rpx;
          text-aling: center;
          background: red;
          border-radius: 27rpx;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 39rpx;
          margin-right: 10rpx;
        "
      >
        取消退款</view
      >
      <view
        class="cu-btn round margin-right shadow-blur"
        @click="handleCusServiceClick"
      >
        <view class="cuIcon-servicefill">联系客服</view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
const util = require("utils/util.js");
import api from "utils/api";
import __config from "@/config/env";

export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      orderItem: {
        quantity: 0,
      },
      orderRefundsList: [],
      status: "", //批量状态
      createTime: "", //发起时间
      refundAmount: "", //退款金额
      statusDesc: "", //退款状态
      paymentPoints: "", //退款积分
      quantity: "", //退款数量
      isRefund: "", //是否到账 1 是
      refuseRefundReson: "", //拒绝原因
      refundReson: "", //退款原因
      orderItemId: "",
      wxCustomerUrl: "",
      //有数统计使用
      page_title: "订单退款",
      logisticsNo: "",
    };
  },

  components: {},
  props: {},

  onShow() {},

  onLoad(options) {
    let orderItemId = options.orderItemId;
    if (options.wxCustomerUrl && options.orderInfoId) {
      this.orderItemId = options.orderInfoId;
      this.wxCustomerUrl = options.wxCustomerUrl;
    }
    if (decodeURIComponent(orderItemId).indexOf(",") != -1) {
      // console.log("options.orderItemId111", options.orderItemId);
      this.orderItemId = options.orderItemId;
      this.status = 1;
      app.initPage().then((res) => {
        this.orderItemListGet(this.orderItemId);
      });
      return;
    } else {
      this.orderItemId = options.orderItemId;
      // console.log("options.orderItemId2222", options.orderItemId);
      app.initPage().then((res) => {
        this.orderItemGet(this.orderItemId);
      });
    }
  },

  methods: {
    
    shopInfoGet (shopId) {
      api.shopInfoGet(shopId).then(res => {
        console.log("wxCustomerUrl",res.data.wxCustomerUrl);
        this.wxCustomerUrl= res.data.wxCustomerUrl;
      });
    },

    // 物流单号
    LogisticsorderNo() {
      console.log(this.logisticsNo != "");
      if (this.logisticsNo) {
        api
          .LogisticsorderNo({
            logisticsNo: this.logisticsNo,
            id: this.orderItem.listOrderRefunds[0].orderLogistics.id,
          })
          .then((res) => {
            console.log(res);
            uni.showToast({
              title: "提交成功",
            });
          });
      }
    },
    tkorder() {
      // console.log(111)
      // orderItemId   订单详情id
      // status     订单详情状态
      // id 退款单id
      let that = this;
      api
        .tkorder({
          orderItemId: this.orderItem.id,
          status: this.orderItem.status,
          id: this.orderItem.listOrderRefunds[0].id,
        })
        .then((res) => {
          console.log(res, "RES");
          // if(res.ok=='true'){
          that.orderItemGet(this.orderItemId);
		  uni.navigateBack({
		  		delta:1,//返回层数，2则上上页
		  	})
          // }
        });
    },
    //客服orderItemId=1499352830332084225&wxCustomerUrl=https://work.weixin.qq.com/kfid/kfca15f05795d133cc4&orderInfoId=1499352830281752577
    async handleCusServiceClick() {
      let wxCustomerUrl = this.wxCustomerUrl || app.globalData.wxCustomerUrl;
      if (!wxCustomerUrl) {
      	const res = await api.getLivingTag();
      	if (res && res.data) {
      		wxCustomerUrl = res.data.platWxCustomerUrl;
      		app.globalData.wxCustomerUrl = res.data.platWxCustomerUrl;
      	}
      }
      if (this.orderItemId && wxCustomerUrl) {
        // #ifdef MP
        wx.openCustomerServiceChat({
          extInfo: {
            url: wxCustomerUrl,
          },
          corpId: __config.chatId,
          showMessageCard: true,
          sendMessageTitle: "咨询订单id:" + this.orderItemId,
          sendMessagePath: "pages/order/order-detail/index?id=" + this.orderItemId,
          sendMessageImg: "",
          success(res) {},
        });
        // #endif
        // #ifdef APP
        uni.share({
        	provider: 'weixin',
        	scene: 'WXSceneSession',
        	openCustomerServiceChat: true,
        	corpid: __config.chatId,
        	customerUrl: wxCustomerUrl,
        	fail(err) {
        		console.log("打开客服错误", err);
            uni.makePhoneCall({
              phoneNumber: '**********'
            })
        	}
        })
        // #endif
      } else {
        uni.showToast({
          title: "请店铺客服先配置下客服链接",
          icon: "none",
          duration: 2000,
        });
      }
    },
    //批量查询
    orderItemListGet(id) {
      console.log("id", decodeURIComponent(id));
      api
        .orderItemListGet({
          ids: decodeURIComponent(id),
        })
        .then((res) => {
          // console.log("orderItemListGet", res.data);
          this.orderItem = res.data;
          // console.log("this.orderItem====>", this.orderItem);
          //退款金额合计
          let paymentPriceAll = 0;
          // 退款积分合计
          let paymentPointsAll = 0;
          //退款数量合计
          let quantityAll = 0;
          this.orderItem.map((item) => {
            if (!isNaN(item.paymentPrice)) {
              paymentPriceAll += item.paymentPrice;
            }
            if (!isNaN(item.paymentPoints)) {
              paymentPointsAll += item.paymentPoints;
            }
            console.log("item.quantity", item.quantity);
            // if (!isNaN(item.quantityAll)) {
            quantityAll += item.quantity;
            // }
          });
          //toFixed 方法保留两位浮点
          this.refundAmount = paymentPriceAll.toFixed(2);
          this.paymentPoints = paymentPointsAll;
          this.quantity = quantityAll;
        });
    },
    //单个查询
    orderItemGet(id) {
      let that = this;
      api.orderItemGet(id).then((res) => {
        let orderItem = res.data;
        this.orderItem = orderItem;
        let shopId = res.data.shopId;
        this.shopInfoGet(shopId)
        this.orderRefundsList = orderItem.listOrderRefunds;
        this.logisticsNo =
          orderItem.listOrderRefunds[0]?.orderLogistics?.logisticsNo;
      });
    },
  },
};
</script>
<style>
.row-img {
  width: 200rpx !important;
  height: 200rpx !important;
  border-radius: 10rpx;
}
</style>
