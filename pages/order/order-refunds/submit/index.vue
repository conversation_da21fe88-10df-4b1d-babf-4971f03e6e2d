<template>
  <view class="jz-wrap">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">订单退款</block>
    </cu-custom>

    <!-- 退款原因 -->
    <view class="jz_cont">
      <view class="action border-bottom"> 退款信息 </view>
      <!-- 商品信息 -->

      <!-- 只退一个订单 -->
      <view v-if="status != 1" class="cu-card no-card article">
        <view class="cu-item">
          <view class="content1 response">
            <image
              :src="
                orderItem.picUrl
                  ? orderItem.picUrl
                  : 'https://img.songlei.com/live/img/no_pic.png'
              "
              mode="aspectFill"
              class="row-img"
            ></image>
            <view class="desc block">
              <view class="text-black overflow-2">{{ orderItem.spuName }}</view>
              <view
                class="text-gray text-sm overflow-2"
                v-if="orderItem.specInfo"
              >
                {{ orderItem.specInfo }}
              </view>
              <view class="flex justify-between align-center">
                <view class="text-price text-gray text-sm margin-top-sm">{{
                  orderItem.salesPrice
                }}</view>
                <view class="text-gray text-sm margin-top-sm"
                  >x{{ orderItem.marginQuantity }}</view
                >
              </view>
            </view>
          </view>
        </view>
        <view class="cu-item">
          <view class="padding solid-top" style="text-align: center">
            <radio-group @change="radioChange" style="transform: scale(0.9)">
              <radio
                class="red paddingRight"
                :class="theme.themeColor"
                value="1"
              ></radio
              >退款
              <radio
                class="red paddingRight paddingLeft"
                :class="theme.themeColor"
                value="2"
              >
              </radio>
              退货退款
              <radio
                class="red paddingRight paddingLeft"
                :class="theme.themeColor"
                value="4"
              >
              </radio>
              退运费
            </radio-group>
          </view>
          <view class="solid-top size" style="padding-top: 20rpx"
            >退款金额：<text class="text-price text-bold text-red text-size">{{
              tkstatus == "4" ? orderItem.freightPrice : orderItem.marginPrice
            }}</text>
          </view>
          <view class="solid-top size" v-if="orderItem.paymentPoints"
            >退款积分：<text class="text-bold text-red text-size">{{
              orderItem.paymentPoints
            }}</text>
          </view>
          <view class="size">退款数量：x{{ orderItem.marginQuantity }}</view>
          <view class="size"
            >已退款金额：<text class="text-price text-red text-size">{{
              orderItem.refundPrice
            }}</text>
          </view>
          <view class="size">已退款数量：x{{ orderItem.refundQuantity }}</view>
        </view>
      </view>

      <!-- 退多个订单 -->
      <view v-else class="cu-card no-card article">
        <view v-for="(item, index) in orderItem" :key="index">
          <view class="cu-item">
            <view class="content1 response">
              <image
                :src="
                  item.picUrl ? item.picUrl : 'https://img.songlei.com/live/img/no_pic.png'
                "
                mode="aspectFill"
                class="row-img"
              ></image>
              <view class="desc block">
                <view class="text-black overflow-2">{{ item.spuName }}</view>
                <view class="text-gray text-sm overflow-2" v-if="item.specInfo">
                  {{ item.specInfo }}
                </view>
                <view class="flex justify-between align-center">
                  <view class="text-price text-gray text-sm margin-top-sm">{{
                    item.salesPrice
                  }}</view>
                  <view class="text-gray text-sm margin-top-sm"
                    >x{{ item.quantity }}</view
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="cu-item">
          <view class="padding solid-top" style="text-align: center">
            <radio-group @change="radioChange" style="transform: scale(0.9)">
              <radio
                class="red paddingRight"
                :class="theme.themeColor"
                value="1"
              ></radio
              >退款
              <radio
                class="red paddingRight paddingLeft"
                :class="theme.themeColor"
                value="2"
              >
              </radio>
              退货退款
            </radio-group>
          </view>

          <!-- 
          >退款金额：<text class="text-price text-bold text-red text-size">{{ tkstatus =='4'?orderItem.freightPrice:orderItem.marginPrice}}</text>
          </view>
          <view
            class="solid-top size"
            v-if="orderItem.paymentPoints"
          >退款积分：<text class="text-bold text-red text-size">{{orderItem.paymentPoints}}</text>
          </view>
          <view class="size">退款数量：x{{orderItem.marginQuantity}}</view>
		  <view
		  class="size"
		    >已退款金额：<text class="text-price 	 text-red text-size">{{ orderItem.refundPrice}}</text>
		  </view>
		  <view class="size">已退款数量：x{{orderItem.refundQuantity}}</view>
        </view> -->

          <view
            class="solid-top size"
            style="padding-top: 20rpx"
            v-if="marginPrice"
            >退款金额：<text class="text-price text-bold text-red text-size">{{
              (marginPrice).toFixed(2)
            }}</text>
          </view>
          <view class="solid-top size" v-if="paymentPoints"
            >退款积分：<text class="text-bold text-red text-size">{{
              paymentPoints
            }}</text>
          </view>
          <view class="size" v-if="marginQuantity"
            >退款数量：x{{ marginQuantity }}</view
          >
          <view class="size"
            >已退款金额：<text class="text-price text-red text-size">{{
              (refundPrice).toFixed(2)
            }}</text>
          </view>
          <view class="size">已退款数量：x{{ refundQuantity }}</view>
        </view>
      </view>
    </view>
    <view class="cu-card no-card jz_cont"  style="padding-bottom: 160rpx;">
      <view
        class="action border-bottom flex justify-between"
        @tap="showModalService"
      >
        退款原因
        <text class="cuIcon-more margin-right-sm" v-if="tkstatus != '4'"></text>
      </view>

      <textarea
        v-if="clickRefund.id && clickRefund.id == '12'"
        class="textarea"
        @input="resonInput"
        placeholder="请输入退款原因"
        :value="orderRefunds.refundReson"
      ></textarea>
      <textarea
        v-else-if="tkstatus == '4'"
        cols="30"
        rows="10"
        placeholder="请输入退运费原因"
        style="padding: 6px"
      >
      </textarea>

      <view
        v-if="clickRefund.id && clickRefund.id !== '12'"
        class="cu-item cu-form-group align-start"
      >
        <view class="title">{{ orderRefunds.refundReson }}</view>
      </view>
    </view>

    <view class="cu-bar bg-white tabbar foot justify-end">
      <navigator
        class="cu-btn round margin-right shadow-blur"
        open-type="navigate"
        :url="
          '/pages/customer-service/customer-service-list/index?shopId=' +
          orderRefunds.shopId
        "
      >
        <view class="cuIcon-servicefill">联系客服</view>
      </navigator>
      <button
        class="cu-btn round margin-right shadow-blur"
        :class="'bg-' + theme.themeColor"
        @tap="wxTemplate"
      >
        确认并提交
      </button>
    </view>

    <view :class="'cu-modal bottom-modal ' + modalService">
      <view class="cu-dialog" style="background-color: white">
        <view class="padding-xl">
          <view class="text-lg text-center">请选择其它退款原因</view>

          <scroll-view
            scroll-y
            scroll-with-animation
            style="max-height: 450rpx"
          >
            <view class="cu-list text-left solid-bottom">
              <view
                class="cu-item"
                v-for="(item, index) in refundList"
                :key="index"
              >
                <view class="content padding-tb-sm">
                  <view @tap="choiceRefundChange(item)">
                    <text
                      class="text-orange"
                      :class="
                        clickRefund.id == item.id ? 'cuIcon-roundcheckfill' : ''
                      "
                    >
                    </text
                    >{{ item.name }}
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>

          <button
            class="cu-btn margin-top response lg"
            :class="'bg-' + theme.themeColor"
            @tap="hideModalService"
          >
            确定
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
const util = require("utils/util.js");
import api from "utils/api";
import { getWxTemplate } from "@/api/message.js"

export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      orderItem: {
        quantity: 0,
      },
      orderRefunds: {},
      //退款原因
      refundList: [
        {
          id: "1",
          name: "多拍，错拍，不想要",
        },
        {
          id: "2",
          name: "不喜欢，效果不好",
        },
        {
          id: "3",
          name: "材质，面料与商品描述不符",
        },
        {
          id: "4",
          name: "大小尺寸与商品描述不符",
        },
        {
          id: "5",
          name: "颜色，款式，图案与描述不符",
        },
        {
          id: "6",
          name: "质量问题",
        },
        {
          id: "7",
          name: "做工粗糙，有瑕疵",
        },
        {
          id: "8",
          name: "功能故障",
        },
        {
          id: "9",
          name: "收到商品少件（含少配件）",
        },
        {
          id: "10",
          name: "商品破损或污渍",
        },
        {
          id: "11",
          name: "商家发错货",
        },
        {
          id: "12",
          name: "其他",
        },
      ],
      modalService: "", //控制弹框
      clickRefund: {
        id: "",
        name: "",
      }, //选择退款的id
      orderItemId: "",
      status: "", //批量状态  1 标识批量
      paymentPrice: "", //退款金额
      paymentPoints: "", //退款积分
      quantity: "", //退款数量
      //有数统计使用
      page_title: "订单退款提交",
      tkstatus: "",
      marginPrice: 0, //商品总金额
      marginQuantity: 0, // 商品总数量
      paymentPoints: 0, // 商品总积分
      refundPrice: 0, //退款总金额
      refundQuantity: 0, // 退款总数量
    };
  },

  components: {},
  props: {},

  onShow() {},

  onLoad(options) {
    console.log("options", options.orderItemId, this.clickRefund);
    let orderItemId = options.orderItemId;

    // 如果多个订单号
    if (decodeURIComponent(orderItemId).indexOf(",") != -1) {
      console.log("options.orderItemId", options.orderItemId);
      this.orderItemId = options.orderItemId;
      this.orderRefunds.orderItemId = options.orderItemId;
      this.status = 1;
      app.initPage().then((res) => {
        this.orderItemListGet(this.orderItemId);
      });
      return;
    } else {
      this.orderItemId = options.orderItemId;
      this.orderRefunds.orderItemId = options.orderItemId;
      app.initPage().then((res) => {
        this.orderItemGet(this.orderItemId);
      });
    }
  },

  methods: {
    //选择退款原因
    choiceRefundChange(e) {
      console.log("choice===>", e);
      if (e.name) {
        this.clickRefund.id = e.id;
        this.clickRefund.name = e.name;
        if (e.id == "12") {
          this.orderRefunds.refundReson = "";
          return;
        }
        this.orderRefunds.refundReson = e.name;
      }
    },

    //显示退款框
    showModalService() {
      this.modalService = "show";
    },

    //隐藏退款框
    hideModalService() {
      this.modalService = "";
    },

    //批量查询
    orderItemListGet(id) {
      api.orderItemListGet({ ids: decodeURIComponent(id) }).then((res) => {
        console.log("orderItemListGet", res.data);
        this.orderItem = res.data;
        this.orderItem.forEach((item) => {
          this.marginPrice += item.marginPrice;
          this.marginQuantity += item.marginQuantity;
          this.paymentPoints += item.paymentPoints;
          this.refundPrice += item.refundPrice;
          this.refundQuantity += item.refundQuantity;
        });
      });
    },

    //单个查询
    orderItemGet(id) {
      api.orderItemGet(id).then((res) => {
        console.log("orderItemGet", res.data);
        this.orderItem = res.data;
      });
    },

    resonInput(e) {
      this.orderRefunds.refundReson = e.detail.value;
    },

    radioChange(e) {
      this.tkstatus = e.detail.value;
      this.orderRefunds.status = e.detail.value;
    },

    //确认提交
    subRefunds() {
      if (!this.orderRefunds.status) {
        uni.showToast({
          title: "请选择退款类型",
          icon: "none",
          duration: 2000,
        });
        return;
      }
      if (this.orderRefunds.status == "4") {
      } else {
        if (!this.orderRefunds.refundReson) {
          uni.showToast({
            title: "请输入退款原因",
            icon: "none",
            duration: 2000,
          });
          return;
        }
      }

      let that = this;
      uni.showModal({
        content: "确认提交退款申请吗？",
        cancelText: "我再想想",
        confirmColor: "#ff0000",

        success(res) {
          if (res.confirm) {
            if (that.status != 1) {
              console.log("that.orderRefunds!=1", that.orderRefunds);
              api.orderRefundsSave(that.orderRefunds).then((res) => {
                console.log("res", res);
                uni.redirectTo({
                  url:
                    "/pages/order/order-refunds/form/index?orderItemId=" +
                    that.orderItemId,
                });
              });
            } else {
              console.log("that.orderRefunds", that.orderRefunds);
              // return
              api.orderRefundsSave(that.orderRefunds).then((res) => {
                console.log("res", res);
                uni.redirectTo({
                  url:
                    "/pages/order/order-refunds/form/index?orderItemId=" +
                    that.orderItemId,
                });
              });
            }
          }
        },
      });
    },
    wxTemplate(e) {
			// #ifdef MP
      getWxTemplate({ type: 2 }).then(res => {
        uni.requestSubscribeMessage({
          tmplIds: res.data,
          complete:() => {
            this.subRefunds(e)
          }
        })
      })
      // #endif
      // #ifndef MP
      this.subRefunds(e)
			// #endif
    },
  },
};
</script>
<style scoped lang="scss">
.row-img {
  width: 200rpx !important;
  height: 200rpx !important;
  border-radius: 10rpx;
}

.jz-wrap {
  padding-top: 12rpx;
}

.jz_cont {
  margin: 0 20rpx 12rpx;
  padding: 0 24rpx;
  background: #ffffff;
  border-radius: 23rpx;

  .action {
    height: 72rpx;
    font-size: 26rpx;
    color: #000000;
    line-height: 72rpx;
  }

  .border-bottom {
    border-bottom: 1px solid #f2f2f2;
  }

  .content1 {
    display: flex;
    padding: 20rpx 0 24rpx;

    .row-img {
      width: 160rpx;
      height: 160rpx;
      margin-right: 20rpx;
    }
  }

  .desc {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    justify-content: space-around;
  }
}

.size {
  font-size: 24rpx;
  color: #231815;
  line-height: 56rpx;
}

.text-size {
  font-size: 26rpx;
}
.paddingLeft {
  margin-left: 80rpx;
}
.paddingRight {
  margin-right: 10rpx;
}
.textarea {
  width: 100%;
  height: 100rpx;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}
</style>
