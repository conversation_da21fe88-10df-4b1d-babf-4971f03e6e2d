<!-- 新的线下优惠券详情页面，信息简单，原来的内容详细的线下优惠券详情页面在这里coupon-offline-detail -->
<template>
	<view>
		<cu-custom bgColor="#FF3242" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">优惠券详情</block>
		</cu-custom>

		<view v-if="couponInfo && couponInfo.couponTypeCode" class="coupon-layout p-xxxsm">
			<view class="bg-white radius-sm ptb-xxxxsm pl-xxxxsm">
				<!-- 页面顶部券样式 -->
				<view class="flex coupon-container">
					<!-- 左侧券二维码 -->
					<view>
						<!-- 核销二维码 -->
						<view>
							<image v-if="couponInfo.qrcode && couponInfo.status === '01'" class="qr-img"
								mode="heightFix" :src="qrImg" @click="() => previewQrImg(1)"></image>
							<view v-else class="qrcode-layout">
								<image class="qr-img" mode="heightFix" :src="$imgUrl('live/coupon/blurry-qrcode‌.png')">
								</image>
								<image class="qrcode-status" :src="statusPic[couponInfo.status]"></image>
							</view>
						</view>

						<!-- 以下文案提示 -->
						<view class="font-md text-center" v-if="couponInfo.qrcode && couponInfo.status === '07'"
							style="color: #a4a4a4">该券赠送中无法核销</view>
						<view v-if="couponInfo.qrcode && couponInfo.status === '01'" @click="handleFreshData"
							class="font-md text-light-gray" style="text-decoration: underline; text-align: center">
							刷新核销码
						</view>
					</view>
					<!-- 右侧券信息 -->
					<view class="ml-xxxsm flex flex-direction justify-between coupon-right" style="flex: 1">
						<!-- 券价值 券类型 -->
						<view class="flex justify-between "
							:class="couponInfo.useType == '12'?'align-center':'align-start'">
							<!-- 券价值   -->
							<text class="text-bold overflow-1 font-lg pt-sss" style="color: #ff3242; line-height: 1;"
								v-if="couponInfo.useType === '03'&&couponInfo.showValue == 'N'">
								{{ couponInfo.typeDesc || ''  }}
							</text>
							<text v-else-if="couponInfo.amount && (couponInfo.showValue !== 'N')"
								class="text-bold overflow-1 font-54" style="color: #ff3242; line-height: 1">
								<block v-if="couponInfo.useType == '04'">
									<text v-if="couponInfo.amount > 0">{{ couponInfo.amount | accMulPrice }}</text>
									<text v-else>{{ couponInfo.facevalue | accMulPrice }}</text>
									<text class="font-md">折</text>
								</block>
								<block v-else-if="couponInfo.useType == '09'">
									<text class="font-md">{{ couponInfo.facemode == '2' ? '￥' : '' }}</text>
									{{ couponInfo.facevalue || '' }}
									<text class="font-md">{{ couponInfo.facemode == '3' ? '小时' : '' }}</text>
								</block>
								<block v-else-if="couponInfo.useType == '12'">
									<text class="font-xl">{{ couponInfo.amount || '' }} 积分</text>
								</block>
								<block v-else>
									<text class="font-md">￥</text>
									{{ couponInfo.amount || '' }}
								</block>
							</text>

							<!-- 券类型 -->
							<text class="cu-tag text-orange font-md mr-xxxxsm"
								style="background: #feeeef; border-radius: 3px">{{ couponInfo.useTypeName || '' }}</text>
						</view>
						<!-- 券名称 -->
						<text class="font-xxmd text-black text-bold overflow-1 mr-xxxxsm"
							style="line-height: 1.3">{{ couponInfo.couponName || '' }}</text>

						<!-- 券使用门槛 -->
						<view class="text-black font-xmd mr-xxxxsm">
							<view v-if="couponInfo && couponInfo.condAmount">单笔满{{ couponInfo.condAmount || '' }}元可用
							</view>
							<view v-else>无门槛</view>
						</view>
						<view class="text-light-gray font-xxxxsm mr-xxxxsm" style="line-height: 1;">券编码:
							{{ couponInfo.couponTypeCode || '' }}
						</view>

						<!-- 券使用状态 -->
						<!-- <view class="round margin-center" style="color: #cccccc" v-if="couponInfo.status == '02'">已使用</view>

							<view class="round margin-center" style="color: #cccccc" v-if="couponInfo.status == '03'">已过期</view>
							<view class="round margin-center" style="color: #cccccc" v-if="couponInfo.status == '04'">已作废</view>

							<view class="round margin-center" style="color: #cccccc" v-if="couponInfo.status == '05'">未生效</view>

							<view class="round margin-center" style="color: #cccccc" v-if="couponInfo.status == '08'">未激活</view> -->

						<!-- 有效期 -->
						<view v-if="couponInfo.effDate || couponInfo.expDate"
							class="text-light-gray font-xxxxsm overflow-1" style="line-height: 1;">
							{{ couponInfo.effDate }}—{{ couponInfo.expDate }}
						</view>
						<view><text v-if="couponInfo.marketDim3"
								class="margin-top-sm text-sm text-blue">{{ couponInfo.marketDim3 }}</text>
						</view>
					</view>
					<image v-if="couponInfo.status == '01'" class="can-use mr-xxxxsm"
						:src="statusPic[couponInfo.status]"></image>
				</view>
				<view class="flex justify-between align-center mr-xxxxsm mt-md">
					<!-- 左侧更多操作区域 -->
					<view class="action-container flex">
						<!-- 右侧箭头区域 -->
						<view class="arrow-area" @click="toggleDropdown" v-if="
								couponInfo.canRefund === 'Y' ||
								(couponInfo.status === '01' && couponInfo.canPresent === 'Y' && couponInfo.status !== '07') ||
								couponInfo.status === '07'
							">
							<text class="font-xmd text-black">更多</text>
							<view class="cuIcon-right text-light-gray" style="transition: transform 0.3s ease"
								:class="{ arrowRight: !isDropdownShow, arrowDown: isDropdownShow }"></view>
						</view>

						<!-- 下拉菜单 -->
						<view class="dropdown-menu pb-xxxxsm" v-if="isDropdownShow">
							<view class="menu-item" v-if="couponInfo.canRefund === 'Y'" @click="refundBtn">退款</view>
							<view class="menu-item"
								v-if="couponInfo.status === '01' && couponInfo.canPresent === 'Y' && couponInfo.status !== '07'"
								@click="handleGive">
								转赠好友
							</view>
							<view v-if="couponInfo.status === '07'" class="menu-item" @click="cancelGive">取消转赠</view>
						</view>
					</view>

					<!-- 右侧按钮区域 -->
					<view class="flex align-center">
						<view class="font-xmd radius-sm pay-btn menu-btn ptb-sm plr-lg text-black"
							@click="handleInstructions">使用须知</view>
						<view class="ml-xxxsm font-xmd radius-sm  ptb-sm plr-lg " @click="() => previewQrImg(0)"
							style="color: #fff; text-align: center; color: #ffffff; background: linear-gradient(270deg, #c09979 0%, #ccac92 0%, #dabda7 100%)">
							会员码
						</view>
						<view class="ml-xxxsm font-xmd radius-sm  ptb-sm plr-lg "
							v-if="couponInfo.status === '01' && couponInfo.useType === '09'" @click="navTor"
							style="color: #fff; text-align: center; color: #ffffff; background: linear-gradient(-90deg, #ff3545 0%, #ff6068 100%)">
							去使用
						</view>
						<!-- #ifdef MP -->
						<view v-else @click="gotoCard" class="font-xmd radius-sm pay-btn ml-xxxsm   ptb-sm plr-lg">微信支付
						</view>
						<!-- #endif -->
					</view>
				</view>
			</view>
		</view>

		<view class="ZZC" v-if="redpacketModel || packageModel || giveModel"></view>
		<view class="redpacket-model" v-if="giveModel">
			<text class="cuIcon-close text-xml text-white close-position" @click="close"></text>
			<view class="gift-word padding">
				<view class="text-bold text-xml padding-bottom text-center" style="color: #a4785b">送上您的转赠语</view>
				<view class="flex align-center justify-between margin-lr-lg margin-bottom-lg padding-bottom-lg text-xdf"
					style="border-bottom: 1rpx solid #a4785b" @click="check = 1">
					<view class="text-xdf">送一份心意只想和你分享</view>
					<view class="check-more">
						<image v-if="check === 1"
							src="https://img.songlei.com/-1/material/909454ee-2d0c-439c-b7c6-8d09654a68b7.png"></image>
					</view>
				</view>
				<view class="flex align-center justify-between margin-lr-lg margin-bottom-lg padding-bottom-lg text-xdf"
					style="border-bottom: 1rpx solid #a4785b" @click="check = 2">
					<view class="text-xdf">有爱的礼物送给可爱的你~</view>
					<view class="check-more">
						<image v-if="check === 2"
							src="https://img.songlei.com/-1/material/909454ee-2d0c-439c-b7c6-8d09654a68b7.png"></image>
					</view>
				</view>
				<view class="flex align-center justify-between margin-lr-lg margin-bottom-lg text-xdf"
					@click="check = 3">
					<view class="text-xdf">自定义祝福语({{ presentDesc.length }}/30)</view>
					<view class="check-more">
						<image v-if="check === 3"
							src="https://img.songlei.com/-1/material/909454ee-2d0c-439c-b7c6-8d09654a68b7.png"></image>
					</view>
				</view>
				<view class="margin-lr-lg text-left text-xdf">
					<textarea :value="presentDesc" class="textarea padding-sm" cols="30" rows="10" placeholder="点击此处输入"
						@input="changeInput"></textarea>
				</view>
				<view>
					<view class="next-btn text-center" @click="lastBtn">下一步</view>
				</view>
			</view>
		</view>
		<view class="redpacket-model" v-if="redpacketModel">
			<view class="redpacket-title">选择退款批次</view>
			<view class="redpacket-content">
				<block v-for="item in redpacketList" :key="item.batchId">
					<view class="redpacket-item" :class="item.hover ? 'hover' : ''" @click="redpacketBtn(item)">
						{{ item.balance }}元({{ item.effDate }} - {{ item.expDate }})
					</view>
				</block>
			</view>
			<view class="redpacket-footer">
				<view class="redpacket-btn" style="border-right: 1upx solid #ccc" @click="redpacketModel = false">取消
				</view>
				<view class="redpacket-btn" @click="redpacketSumit">确定</view>
			</view>
		</view>
		<view class="redpacket-model" v-if="packageModel">
			<view class="redpacket-title">券包退款失败的券种</view>
			<view class="redpacket-content">
				<block v-for="item in packageList" :key="item.id">
					<view class="redpacket-item">{{ item.shouldRefundAmt }}元({{ item.typeName }} -
						{{ item.useTypeName }})
					</view>
				</block>
			</view>
			<view class="redpacket-footer">
				<view class="redpacket-btn" style="border-right: 1upx solid #ccc" @click="packageModel = false">取消
				</view>
				<view class="redpacket-btn" @click="packageModel = false">确定</view>
			</view>
		</view>

		<!-- 使用须知和明细弹框 -->
		<uni-popup ref="detailPopup" type="center">
			<view class="detailpopup-content" v-if="couponInfo">
				<view v-if="couponInfo && couponInfo.detailUsageDesc" class="text-df-px margin-top-xs">
					使用须知:
					<view v-if="couponInfo.detailUsageDesc" class="text-sm-px padding-left text-gray">
						<text>{{ couponInfo.detailUsageDesc.replace(/\\n/g, '\n') }}</text>
					</view>
					<view v-else class="text-sm">暂无须知</view>
				</view>

				<view v-if="couponInfo.detailUsageRule" class="margin-top-sm">
					使用明细:
					<view v-if="couponInfo.detailUsageRule" class="text-sm-px padding-left text-gray">
						<!-- <rich-text :nodes="couponInfo.detail_usage_rule"></rich-text> -->
						<text>{{ couponInfo.detailUsageRule.replace(/\\n/g, '\n') }}</text>
					</view>
					<view v-else class="text-sm">暂无明细</view>
				</view>

				<!-- 关闭按钮 -->
				<button class="cu-btn bg-gray close-btn text-df-px" @click="closeDetailPopup">关闭</button>
			</view>
		</uni-popup>

		<!-- <divComponents ref="divComponents" /> -->
	</view>
</template>

<script>
	const util = require('utils/util.js');
	import {
		EventBus
	} from '@/utils/eventBus.js'
	const app = getApp();
	import api from 'utils/api';
	import {
		refundApply,
		refundCheckpackage,
		refundCheckbalance
	} from '@/pages/coupon/api/coupon.js';
	// import divComponents from './components/micro-com';

	const QR = require('utils/wxqrcode.js');
	const brCode = require('utils/barcode.js');
	import {
		getEvtscdDetails
	} from '@/pages/coupon/api/coupon.js';
	import {
		createpresentbill,
		cancelpresentbill
	} from '@/pages/coupon/api/coupon-give.js';
	import {
		accMul
	} from '@/utils/numberUtil';

	export default {
		components: {
			// divComponents,
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				id: '',
				couponInfo: {},
				//有数统计使用
				page_title: '优惠券详情',
				loadingImg: 'data:image/png;base64,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',
				qrImg: null,
				redpacketModel: false,
				redpacketList: [],
				packageModel: false,
				packageList: [],
				presentDesc: '',
				check: 1,
				giveModel: false,
				// memberBarcode:'',//会员条码
				isDropdownShow: false, // 控制下拉菜单显示状态
				statusPic: {
					'01': this.$imgUrl('live/coupon/status01.png'),
					'02': this.$imgUrl('live/coupon/status02.png'),
					'03': this.$imgUrl('live/coupon/status03.png'),
					'04': this.$imgUrl('live/coupon/status04.png'),
					'05': this.$imgUrl('live/coupon/status05.png'),
					'07': this.$imgUrl('live/coupon/status07.png'),
					'08': this.$imgUrl('live/coupon/status08.png')
				},
			};
		},

		filters: {
			accMulPrice: function(value) {
				return accMul(value, 10);
			}
		},

		onLoad: function(options) {
			if (options.id) {
				this.id = options.id;
			}
			app.initPage().then((res) => {
				this.getCouponInfo();
			});
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框

			uni.hideNavigationBarLoading(); // 停止下拉动作

			uni.stopPullDownRefresh();
		},

		onReachBottom() {
			EventBus.$emit("divGoodsGroupsReachBottom", "true")
		},

		methods: {
			// 二维码刷新
			handleFreshData() {
				this.getCouponInfo();
			},

			// #ifdef MP-WEIXIN
			gotoCard() {
				const userInfo = uni.getStorageSync('user_info');
				wx.navigateToMiniProgram({
					appId: 'wxeb490c6f9b154ef9',
					path: 'pages/card_open/card_open',
					extraData: {
						create_card_appid: 'wx367d0a494ee763f3',
						card_id: 'pNsNa5KxRumYouxMfbP1FzWVI5YA',
						card_code: userInfo.erpCid,
						activate_type: 'ACTIVATE_TYPE_NORMAL'
					}
				});
			},
			// #endif

			// 获取劵详情
			getCouponInfo() {
				this.qrImg = this.loadingImg;
				getEvtscdDetails({
						couponNo: this.id
					})
					.then((res) => {
						console.log('res', res);
						this.couponInfo = res.data;
						this.qrImg = QR.createQrCodeImg(this.couponInfo.qrcode, {
							size: parseInt(1200) //二维码大小
						});
					})
					.catch((e) => {});
			},

			refresh() {
				// this.loadmore = true;
				// this.couponInfoList = [];
				// this.page.current = 1;
				// this.couponInfoPage();
			},
			refundBtn() {
				const that = this;
				that.toggleDropdown();
				const {
					useType,
					combcouponcode = '',
					groupId,
					couponTypeCode
				} = this.couponInfo;
				if (useType === '01') {
					const params = {
						groupId: groupId,
						typeId: couponTypeCode
					};
					// 红包券单独做处理
					refundCheckbalance(params).then((res) => {
						const {
							canRefund,
							batchList = []
						} = res.data;
						const batchLists = batchList.filter((item) => item.canRefund === 'Y');
						if (!batchLists.length) {
							this.uniModel('当前优惠券不支持退款');
						} else if (batchLists.length === 1) {
							const {
								srcOrderno = '', batchId = ''
							} = batchLists[0];
							uni.showModal({
								title: '提示',
								content: '确定是否退款?',
								success(res) {
									if (res.confirm) {
										uni.showLoading({
											title: '退款中',
											mask: true
										});
										that.refundApply({
											oriOrderId: srcOrderno,
											batchId
										});
									}
								}
							});
						} else {
							this.redpacketModel = true;
							batchLists.forEach((item, index) => {
								item.hover = false;
								if (!index) {
									item.hover = true;
								}
							});
							this.redpacketList = batchLists;
						}
					});
				} else {
					if (combcouponcode) {
						uni.showModal({
							title: '提示',
							content: '券包优惠券退款需要整体退款，是否继续退款?',
							success(res) {
								if (res.confirm) {
									uni.showLoading({
										title: '退款中',
										mask: true
									});
									that.refundCheckpackage();
								}
							}
						});
					} else {
						uni.showModal({
							title: '提示',
							content: '确定是否退款',
							success(res) {
								if (res.confirm) {
									uni.showLoading({
										title: '退款中',
										mask: true
									});
									that.refundApply();
								}
							}
						});
					}
				}
			},
			refundCheckpackage() {
				const {
					srcOrderno = '', combcouponcode = ''
				} = this.couponInfo;
				const params = {
					oriOrderId: srcOrderno,
					packageId: combcouponcode
				};
				// 券包单独处理
				refundCheckpackage(params).then((res) => {
					const {
						canRefund,
						packageList = []
					} = res.data;
					if (canRefund === 'Y') {
						// 可以退款
						this.refundApply();
					} else {
						// 不可以退款
						// this.uniModel('当前券包不支持退款');
						// 展示
						this.packageModel = true;
						this.packageList = packageList.filter((item) => item.canRefund !== 'Y');
					}
				});
			},
			uniModel(content, cancel) {
				uni.showModal({
					title: '提示',
					content,
					showCancel: false,
					success() {
						cancel && cancel();
					}
				});
			},
			refundApply(obj = {}) {
				const {
					batchId = '', srcOrderno = '', couponNo = '', combcouponcode = ''
				} = this.couponInfo;
				const params = {
					accntNo: couponNo,
					oriOrderId: srcOrderno,
					packageId: combcouponcode,
					batchId,
					...obj
				};
				refundApply(params).then((res) => {
					this.uniModel('退款成功', () => {
						uni.navigateBack({
							delta: 1
						});
					});
				});
			},
			redpacketBtn(obj) {
				const redpacketList = this.redpacketList;
				console.log(obj);
				this.redpacketList = redpacketList.map((item) => {
					item.hover = false;
					if (item === obj) {
						item.hover = true;
					}
					return item;
				});
			},
			redpacketSumit() {
				const redpacketList = this.redpacketList;
				const obj = redpacketList.find((item) => item.hover);
				const {
					srcOrderno = '', batchId = ''
				} = obj;
				uni.showLoading({
					title: '退款中'
				});
				this.redpacketModel = false;
				this.refundApply({
					oriOrderId: srcOrderno,
					batchId
				});
			},
			navTor() {
				uni.navigateTo({
					url: '/pages/parkinglot/parking-home/index'
				});
			},
			lastBtn() {
				const {
					check
				} = this;
				const presentMode = '2';
				let presentDesc = this.presentDesc;
				if (+check === 1) {
					presentDesc = '送一份心意只想和你分享';
				} else if (+check === 2) {
					presentDesc = '有爱的礼物送给可爱的你~';
				} else if (+check === 3 && !presentDesc) {
					uni.showToast({
						title: '自定义祝福语不能为空',
						icon: 'none'
					});
					return;
				}
				uni.showLoading({
					title: '加载中'
				});
				const accntNo = [{
					accntNo: this.couponInfo.couponNo
				}];
				createpresentbill({
					presentMode,
					presentDesc,
					details: accntNo
				}).then((res) => {
					uni.hideLoading();
					this.close();
					this.getCouponInfo();
					uni.navigateTo({
						url: '/pages/coupon/coupon-give-details/index?id=' + res.data
					});
				});
			},
			close() {
				this.redpacketModel = false;
				this.ackageModel = false;
				this.giveModel = false;
			},
			changeInput(e) {
				const value = e.detail.value;
				const presentDesc = this.presentDesc.slice(0, 30);
				this.presentDesc = value;
				if (value.length > 30) {
					this.$nextTick(() => {
						this.presentDesc = presentDesc;
					});
					return;
				}
			},
			cancelGive() {
				const that = this;
				uni.showModal({
					title: '温馨提示',
					content: '确认取消转赠?',
					success(res) {
						if (res.confirm) {
							that.cancelpresentbill();
						}
					}
				});
			},
			cancelpresentbill() {
				const that = this;
				cancelpresentbill({
					accntNo: this.couponInfo.couponNo
				}).then((res) => {
					that.getCouponInfo();
					uni.showModal({
						title: '提示',
						content: '取消成功',
						showCancel: false
					});
				});
			},

			previewQrImg(index) {
				if (index == 0) {
					// 在需要预览图片的地方调用customImage-preview页面
					uni.navigateTo({
						url: `/pages/customImage-preview/index?id=${this.id}`
					});
					return;
				} else {
					uni.previewImage({
						current: 0,
						urls: [this.qrImg],
						sizeType: ['original']
					});
				}
			},

			// 以下是更多操作的方法
			// 切换下拉菜单显示状态
			toggleDropdown() {
				this.isDropdownShow = !this.isDropdownShow;
			},

			//转增好友
			handleGive() {
				this.toggleDropdown();
				this.giveModel = true;
			},

			// 点击使用须知
			handleInstructions() {
				this.$refs.detailPopup.open();
			},

			closeDetailPopup() {
				this.$refs.detailPopup.close();
			}
		}
	};
</script>
<style scoped lang="scss">
	@import 'public/colorui/media.scss';

	.coupon-layout {
		background: linear-gradient(180deg, #ff3242 0%, #ff6168 100%);
	}

	.coupon-container {
		position: relative;
	}

	.amount-size {
		font-size: 51rpx;
		line-height: 51rpx;
	}

	.can-use {
		position: absolute;
		right: 0;
		bottom: 80rpx;
		width: 90rpx;
		height: 90rpx;
	}

	.qrcode-layout {
		position: relative;
	}

	.qrcode-status {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 90rpx;
		height: 90rpx;
	}

	.arrowDown {
		transform: rotate(90deg);
	}

	.margin-center {
		margin: auto;
		text-align: center;
	}

	.refund-btn {
		width: 210upx;
		height: 60upx;
		display: flex;
		justify-content: center;
		align-items: center;
		border: 1upx solid #999;
		color: #000;
		border-radius: 30upx;
		margin: 10rpx 0;
		margin-left: auto;
	}

	.ZZC {
		position: fixed;
		width: 100vw;
		height: 100vh;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1;
	}

	.redpacket-model {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		margin: auto;
		width: 80vw;
		background-color: #fff;
		border-radius: 20upx;
		z-index: 2;
	}

	.redpacket-title {
		padding: 30upx 0 20upx;
		text-align: center;
		font-size: 32upx;
		border-bottom: 1upx solid #ccc;
	}

	.redpacket-content {
		text-align: center;
		font-size: 28upx;
		padding: 20upx 0;
	}

	.redpacket-item {
		padding: 10upx 0;
	}

	.redpacket-item.hover {
		background-color: rgba(90, 256, 220, 0.5);
	}

	.redpacket-footer {
		display: flex;
		height: 80upx;
		border-top: 1upx solid #ccc;
	}

	.redpacket-footer .redpacket-btn {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32upx;
	}

	.check-more {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		border: 1rpx solid #bfbfbf;
		background-color: #eeeeee;
		position: relative;
	}

	.check-more image {
		width: 40rpx;
		height: 40rpx;
		position: absolute;
		top: -1rpx;
		left: 0rpx;
	}

	.textarea {
		width: 100%;
		height: 190rpx;
		border: 1rpx solid #e6d5c8;
		border-radius: 27rpx;
	}

	.next-btn {
		width: 370rpx;
		height: 80rpx;
		color: #fff;
		background: linear-gradient(180deg, #ff4e00, #ff8463);
		line-height: 80rpx;
		border-radius: 40rpx;
		margin: 50rpx auto 20rpx;
	}

	.color-red {
		color: #ff0000;
	}

	.close-position {
		position: absolute;
		top: -60rpx;
		right: 10rpx;
	}

	.action-container {
		position: relative;
	}

	.arrow-area {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		border-radius: 5px;
	}

	.cuIcon-right {
		font-size: 16px;
		color: #333;
	}

	.dropdown-menu {
		position: absolute;
		top: 16px;
		left: -17px;
		width: 150px;
		border-radius: 5px;
		z-index: 10;
		background-size: 100% 100%;
		background-image: url(https://img.songlei.com/live/coupon/action-bg.png);
		padding-top: 14px;
	}

	.menu-item {
		padding: 10px 15px;
		text-align: center;
		font-size: 14px;
		color: #333;
		border-bottom: 1px solid #eee;
	}

	.menu-item:last-child {
		border-bottom: none;
	}

	.detailpopup-content {
		width: 80vw;
		background: #fff;
		border-radius: 10px;
		padding: 20px;
		border-radius: 40rpx;
	}

	.close-btn {
		width: 100%;
		margin-top: 20px;
		color: #fff;
		background: linear-gradient(-90deg, #ff3242 0%, #ff6067 100%);
	}

	.pay-btn {
		color: #fff;
		background: #07c160;
		text-align: center;
	}

	.menu-btn {
		background: #ececec;
		color: #000;
	}

	.qr-img {
		width: 220rpx;
		height: 220rpx;
	}

	@media (min-width: $breakpoint-medium) and (max-width: $breakpoint-large) {
		.qr-img {
			width: 160rpx;
			height: 160rpx;
		}

		.qrcode-status {
			width: 54rpx;
			height: 54rpx;
		}


		.can-use {
			position: absolute;
			right: 20rpx;
			bottom: 50rpx;
			width: 54rpx;
			height: 54rpx;
		}

		.amount-size {
			font-size: 31rpx;
			line-height: 11rpx;
		}
	}
</style>

<style lang="scss" scoped>
	/deep/ .u-checkbox-group--row {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}
</style>

<style lang="scss" scoped>
	::v-deep .u-checkbox-group--row {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}
</style>