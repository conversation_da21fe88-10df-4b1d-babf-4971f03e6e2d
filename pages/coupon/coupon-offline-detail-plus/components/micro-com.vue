<template>
	<view>
		<block v-for="(temp, index) in componentsList" :key="index">
			<template v-if="temp.componentName === 'advertComponent'">
				<div-advert v-model="temp.data" :isStatusBar="false"></div-advert>
			</template>
			<template v-else-if="temp.componentName === 'goodsComponent'">
				<div-goods v-model="temp.data"></div-goods>
			</template>
		</block>
	</view>
</template>

<script>
import divAdvert from '@/components/div-components/div-advert/index.vue';
import divGoods from '@/components/div-components/div-goods/index.vue';
import api from '@/utils/api';
export default {
	components: {
		divAdvert,
		divGoods
	},
	data() {
		return {
			componentsList: []
		};
	},

	created() {
		this.initData();
	},

	methods: {
		initData() {
			const tabBar = uni.getStorageSync('tabBar');
			if (tabBar && tabBar[0] && tabBar[0].pagePathId > 0) {
				if (tabBar[0].pagePathId) {
					api.pagedevisePage(tabBar[0].pagePathId).then((res) => {
						let pageDivData = res.data;
						if (pageDivData && pageDivData.pageComponent && pageDivData.pageComponent.componentsList) {
							if (pageDivData.pageComponent.componentsList[5]) {
								this.componentsList.push(pageDivData.pageComponent.componentsList[5]);
							}
							if (pageDivData.pageComponent.componentsList[6]) {
								this.componentsList.push(pageDivData.pageComponent.componentsList[6]);
							}
							// if (pageDivData.pageComponent.componentsList[8]) {
							// 	this.componentsList.push(pageDivData.pageComponent.componentsList[8]);
							// }
							
							if (pageDivData.pageComponent.componentsList[pageDivData.pageComponent.componentsList.length-1]) {
								this.componentsList.push(pageDivData.pageComponent.componentsList[pageDivData.pageComponent.componentsList.length-1]);
							}
						}
						console.error('=this.componentsList========', this.componentsList);
					});
				}
			}
		}
	}
};
</script>

<style></style>
