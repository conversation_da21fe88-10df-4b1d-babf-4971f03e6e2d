<template>
  <view>
    <cu-custom
      :bgColor="'bg-'+theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">领取优惠券</block>
    </cu-custom>

    <!-- 导航按钮 -->
    <!-- <scroll-view
      scroll-x
      class="bg-white nav fixed collection-types"
      style="box-shadow: none"
    >
      <view
        class="flex text-center"
        style="background-color: #eee"
      >
        <view
          :class="
            'btn flex-sub ' +
            (index == tabCur ? 'cur bg-' + theme.backgroundColor : '')
          "
          v-for="(item, index) in collectType"
          :key="index"
          @tap="tabSelect"
          :data-index="index"
          :data-key="item.key"
        >{{ item.value }}</view>
      </view>
    </scroll-view>

    <view class="cu-list bg-white padding-top-xs padding-bottom-sm coupon-list"> -->
    <view class="cu-list bg-white padding-top-xs padding-bottom-sm">
      <view
        class="cu-item padding-tb-xs padding-lr margin-top-sm"
        v-for="(item, index) in couponInfoList"
        :key="index"
      >
        <coupon-info
          :couponInfo="item"
          @receiveCoupon="receiveCouponChange($event,index)"
        ></coupon-info>
      </view>
    </view>
    <view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
    
  </view>
</template>

<script>
const util = require("utils/util.js");
const app = getApp();
import api from 'utils/api'
export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      page: {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: 'sort',
        //升序字段
        descs: ''
      },
      parameter: {},
      loadmore: true,
      couponInfoList: [],
      shopId: null,
      //有数统计使用
      page_title: '领取优惠券',
      type: '1',
      tabCur: 0,
      // 导航栏
      collectType: [{
        value: '松鼠美淘',
        key: '1'
      }, {
        value: '松雷商业',
        key: '2'
      }],
    };
  },

  props: {},

  onShow () { },

  onLoad: function (options) {
    if (options.shopId) {
      this.shopId = options.shopId;
    }
    app.initPage().then(res => {
      this.couponInfoPage();
    });
  },

  onReachBottom () {
    if (this.loadmore) {
      this.page.current = this.page.current + 1;
      this.couponInfoPage();
    }
  },

  onPullDownRefresh () {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框

    uni.hideNavigationBarLoading(); // 停止下拉动作

    uni.stopPullDownRefresh();
  },

  methods: {
    // 导航栏切换
    tabSelect (e) {
      let dataset = e.currentTarget.dataset;
      if (dataset.index != this.tabCur) {
        this.tabCur = dataset.index;
        this.type = dataset.key;
        this.refresh();
      }
    },
    receiveCouponChange (item, index) { //更新单条数据
      this.couponInfoList[index] = item;
      this.couponInfoList.splice(); //确保页面刷新成功
      //强制刷新
      // uni.redirectTo({
      //   url: '/pages/coupon/coupon-list/index'
      // });
    },
    couponInfoPage () {
      if (this.shopId) {
        this.page.shopId = this.shopId
      }
      api.couponInfoPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
        let couponInfoList = res.data.records;
        //isShow 等于1 的显示
        let newCouponInfoList = couponInfoList.filter(item => item.isShow == '1')
        this.couponInfoList = [...this.couponInfoList, ...newCouponInfoList];
        if (couponInfoList.length < this.page.size) {
          this.loadmore = false;
        }
        //  else {
        //   this.loadmore = false;
        // }
      });
    },

    refresh () {
      this.loadmore = true;
      this.couponInfoList = [];
      this.page.current = 1;
      this.couponInfoPage();
    }

  }
};
</script>
<style scoped>
.collection-types {
  top: unset !important;
}
.btn {
  height: 50rpx;
  width: 352rpx;
  line-height: 50rpx;
  margin: 18rpx 7rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  background-color: #fff;
}
.coupon-list {
  margin-top: 75rpx;
}
</style>
