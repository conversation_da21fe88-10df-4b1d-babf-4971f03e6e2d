<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">优惠券详情</block>
		</cu-custom>

		<view style="background-color: #c93130; padding: 50rpx 40rpx">
			<view v-if="couponInfo.type == '1'" style="background-color: #ffffff; padding: 30rpx; display: flex; flex-direction: column">
				<text class="text-df margin-bottom-sm padding-bottom-xs" style="border-bottom: dashed 1px #ccc">{{ couponInfo.name }}</text>

				<view class="ticket margin-bottom-xs margin-center" style="color: #000000">代金券</view>

				<text v-if="couponInfo.giveType == '1'" class="text-sl text-bold overflow-1 number margin-bottom-xs" style="margin: 30rpx auto">
					{{ couponInfo.reduceAmount }}
					<text style="font-size: 23rpx; font-weight: 400">元</text>
				</text>

				<view class="line"></view>

				<text v-if="couponInfo.giveType == '2'" class="text-xl overflow-1 margin-left-xs margin-center">满赠</text>

				<view class="line"></view>

				<view class="margin-center">
					<view class="text-xs overflow-1 margin-bottom-xs">订单满{{ couponInfo.premiseAmount }}元可使用</view>
					<view class="text-xs margin-bottom-xs">
						{{
							couponInfo.suitType == '1'
								? '全部商品可用'
								: couponInfo.suitType == '2'
								? '指定商品可用'
								: couponInfo.suitType == '3'
								? '指定商品不可用'
								: couponInfo.suitType == '4'
								? '指定商品可用'
								: ''
						}}
					</view>
				</view>

				<!-- <view class="ticket margin-bottom-xs" style="color: #000000">
          代金券
        </view> -->
				<view v-if="couponInfo.rangeType != '1'" class="text-sm margin-bottom-xs margin-center" style="color: #000000">
					<!-- <text class="cuIcon-shop"></text>{{couponInfo.shopInfo.name}} -->
					<text v-if="couponInfo.rangeType != '1'" class="cuIcon-shop"></text>
					{{ couponInfo.rangeType != '1' ? couponInfo.shopInfo.name : couponInfo.name }}
				</view>

				<view v-if="couponUser.status != 1 && couponUser.status != 2" class="validity margin-center">
					<view class="text-xs" style="color: #000" v-if="couponInfo.expireType == '1'">领取后{{ couponInfo.validDays }}天有效</view>
					<view class="text-xs" style="color: #000" v-if="couponInfo.expireType == '2'">{{ couponInfo.validBeginTime }}至{{ couponInfo.validEndTime }}</view>
				</view>

				<view v-if="couponUser.status != 1 && couponUser.status != 2" class="margin-center" style="margin: 30rpx">
					<button
						class="cu-btn bg-white round sm margin"
						:style="{
							color: '#ffffff',
							backgroundColor: couponInfo.enable == '0' ? '#ccc' : '#ff5147',
							width: '275rpx',
							margin: 'auto'
						}"
						v-if="!couponUser.status"
						:disabled="couponInfo.enable == '0' ? true : false"
						@tap.stop="couponUserSave"
					>
						{{ couponInfo.enable == '0' ? '已过期' : '立即领取' }}
					</button>

					<view v-if="couponUser.status" style="position: relative">
						<view class="padding-xs text-bold" style="color: #cccccc; width: 120rpx" v-if="!toUse">已领取</view>

						<view class="cu-btn bg-white text-xs round sm" v-if="toUse" @tap.stop="toGoodsList(couponUser.couponId, couponUser.id)">
							已领取，去使用
							<text class="cuIcon-right"></text>
						</view>
					</view>
				</view>

				<view class="round margin-center" style="color: #cccccc" v-if="couponUser.status == 1">已使用</view>

				<view class="round margin-center" style="color: #cccccc" v-if="couponUser.status == 2">已过期</view>

				<view>
					使用须知:
					<text style="color: #ccc">
						{{ couponInfo.remarks || '暂无' }}
					</text>
				</view>
			</view>

			<view v-if="couponInfo.type == '2'" style="background-color: #ffffff; padding: 30rpx; display: flex; flex-direction: column">
				<text class="text-df margin-bottom-sm padding-bottom-xs" style="border-bottom: dashed 1px #ccc">{{ couponInfo.name }}</text>

				<view class="ticket margin-bottom-xs margin-center" style="color: #000000">折扣劵</view>

				<text v-if="couponInfo.giveType == '2'" class="text-sl text-bold overflow-1 number margin-bottom-xs" style="margin: 30rpx auto">
					{{ couponInfo.discount }}
					<text style="font-size: 23rpx; font-weight: 400">折</text>
				</text>

				<view class="line"></view>

				<text v-if="couponInfo.giveType == '2'" class="text-xl overflow-1 margin-left-xs margin-center">满赠</text>

				<view class="line"></view>

				<view class="margin-center">
					<view class="text-xs overflow-1 margin-bottom-xs">订单满{{ couponInfo.premiseAmount }}元可使用</view>
					<view class="text-xs margin-bottom-xs">
						{{
							couponInfo.suitType == '1'
								? '全部商品可用'
								: couponInfo.suitType == '2'
								? '指定商品可用'
								: couponInfo.suitType == '3'
								? '指定商品不可用'
								: couponInfo.suitType == '4'
								? '指定商品可用'
								: ''
						}}
					</view>
				</view>

				<!-- <view class="ticket margin-bottom-xs" style="color: #000000">
          代金券
        </view> -->
				<view v-if="couponInfo.rangeType != '1'" class="text-sm margin-bottom-xs margin-center" style="color: #000000">
					<!-- <text class="cuIcon-shop"></text>{{couponInfo.shopInfo.name}} -->
					<text v-if="couponInfo.rangeType != '1'" class="cuIcon-shop"></text>
					{{ couponInfo.rangeType != '1' ? couponInfo.shopInfo.name : couponInfo.name }}
				</view>

				<view v-if="couponUser.status != 1 && couponUser.status != 2" class="validity margin-center">
					<view class="text-xs" style="color: #000" v-if="couponInfo.expireType == '1'">领取后{{ couponInfo.validDays }}天有效</view>
					<view class="text-xs" style="color: #000" v-if="couponInfo.expireType == '2'">{{ couponInfo.validBeginTime }}至{{ couponInfo.validEndTime }}</view>
				</view>

				<view v-if="couponUser.status != 1 && couponUser.status != 2" class="margin-center" style="margin: 30rpx">
					<button
						class="cu-btn bg-white round sm margin"
						:style="{
							color: '#ffffff',
							backgroundColor: couponInfo.enable == '0' ? '#ccc' : '#ff5147',
							width: '275rpx',
							margin: 'auto'
						}"
						v-if="!couponUser.status"
						:disabled="couponInfo.enable == '0' ? true : false"
						@tap.stop="couponUserSave"
					>
						{{ couponInfo.enable == '0' ? '已过期' : '立即领取' }}
					</button>

					<view v-if="couponUser.status" style="position: relative">
						<view class="padding-xs text-bold" style="color: #cccccc; width: 120rpx" v-if="!toUse">已领取</view>

						<navigator @tap.stop="toGoodsList(couponUser.couponId, couponUser.id)" class="cu-btn bg-white text-xs round sm" v-if="toUse">
							已领取，去使用
							<text class="cuIcon-right"></text>
						</navigator>
					</view>
				</view>

				<view class="round margin-center" style="color: #cccccc" v-if="couponUser.status == 1">已使用</view>

				<view class="round margin-center" style="color: #cccccc" v-if="couponUser.status == 2">已过期</view>

				<view>
					使用须知:
					<text style="color: #000">
						{{ couponInfo.remarks || '暂无' }}
					</text>
				</view>
			</view>

			<view v-if="id" style="background-color: #ffffff; padding: 30rpx; display: flex; flex-direction: column; align-items: center">
				<view>劵号: {{ id || '暂无' }}</view>
			</view>
		</view>
		<recommendComponents canLoad />
	</view>
</template>

<script>
const util = require('utils/util.js');
const app = getApp();
import api from 'utils/api';
import recommendComponents from 'components/recommend-components/index';

export default {
	components: {
		recommendComponents
	},
	data() {
		return {
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			id: '',
			couponInfo: {},
			toUse: this.toUse,
			couponUser: {},
			//有数统计使用
			page_title: '优惠券详情'
		};
	},

	props: {},

	onLoad: function (options) {
		if (options.scene) {
			//接受二维码中参数
			let scenes = decodeURIComponent(options.scene).split('&');
			console.error('scenes====>', scenes);
			this.id = scenes[0];
			this.toUse = scenes[1];
			if (scenes[2]) {
				this.couponUser = JSON.parse(scenes[2]) || {};
			}
		} else {
			if (options.id) {
				this.id = options.id;
				//couponUser目前是从外层传过了的，如果不传的话就是单纯的线上优惠券详情，不是我的优惠券详情。
				// 我的优惠券详情，couponUser需要后端出个接口，现在后端认为可以领取多张券，不知道具体查看的哪种，问题卡在这里，
				// 需要商量下具体怎么展示
				this.toUse = options.toUse;
				if (options.couponUser) {
					this.couponUser = JSON.parse(options.couponUser) || {};
				}
				if (options.status) {
					this.couponInfo.status = options.status;
				}
			}
		}
		app.initPage().then((res) => {
			console.error("=====getCouponInfo========");
			this.getCouponInfo();
		});
	},

	onPullDownRefresh() {
		// 显示顶部刷新图标
		uni.showNavigationBarLoading();
		this.refresh(); // 隐藏导航栏加载框
		uni.hideNavigationBarLoading(); // 停止下拉动作
		uni.stopPullDownRefresh();
	},

	methods: {
		//去使用的跳转 通过优惠劵详情 获取 对应商品列表
		toGoodsList(id, couponUserId) {
			uni.navigateTo({
				url: '/pages/goods/goods-list/index?couponId=' + id
			});
		},

		couponUserSave() {
			api.couponUserSave({
				couponId: this.couponInfo.id
			}).then((res) => {
				uni.showToast({
					title: '领取成功',
					icon: 'success',
					duration: 2000
				});
				this.couponInfo.couponUser = res.data;
				this.getCouponInfo();
				// console.log(" this.couponInfo.couponUser ", this.couponInfo.couponUser);
				this.$emit('receiveCoupon', this.couponInfo);
			});
		},

		//查询商品可用电子券
		// couponInfoPage (spuId) {
		//   api.couponInfoPage({
		//     current: 1,
		//     size: 50,
		//     descs: 'create_time',
		//     spuId: spuId
		//   }).then(res => {
		//     this.couponInfoList = res.data.records;
		//   });
		// },

		getCouponInfo() {
			api.getCouponInfo(this.id)
				.then((res) => {
					this.couponInfo = res.data;
					console.error(" this.couponInfo.couponUser ", this.couponInfo.couponUser);

					// this.couponUserSave()
					// console.log("this.couponInfo", this.couponInfo);
				})
				.catch((e) => {
					console.error("==e===", e)
				});
			// if(this.shopId){
			// 	this.page.shopId = this.shopId
			// }
			// api.couponInfoPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
			// 	let couponInfoList = res.data.records;
			// 	this.couponInfoList = [...this.couponInfoList, ...couponInfoList];
			// 	if (couponInfoList.length < this.page.size) {
			// 		this.loadmore = false;
			// 	}
			// });
		},

		refresh() {
			// this.loadmore = true;
			// this.couponInfoList = [];
			// this.page.current = 1;
			// this.couponInfoPage();
		}
	}
};
</script>
<style scoped>
.margin-center {
	margin: auto;
	text-align: center;
}
</style>
