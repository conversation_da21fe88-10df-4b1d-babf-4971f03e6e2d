<template>
	<view class="coupon-centre padding-bottom-xl">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">活动详情</block>
		</cu-custom>
		<view class="coupon-page">
			<view class="coupon-header">
				<view class="title">{{ optionsData.eventTypeName || '' }}</view>
				<view class="price">
					<block v-if="optionsData.useType === '03'&&optionsData.showValue == 'N'">
						<text class="font-xxl">{{ optionsData.typeDesc  }}</text>
					</block>
					<block v-else-if="optionsData.useType === '04'">
						<text>{{ optionsData.faceValue * 10 }}</text>
						<text class="icon" style="bottom: 0; top: auto">折</text>
					</block>
					
					<block v-else>
						<text class="icon">￥</text>
						<text>{{ optionsData.faceValue || 0 }}</text>
					</block>
				</view>
				<view class="threshold" v-if="optionsData.condAmount != -1">
					<block v-if="optionsData.condAmount">（满{{ optionsData.condAmount || 0 }}可用）</block>
					<block v-else>（无门槛）</block>
				</view>
				<view class="fff-box">
					<view class="all-price">
						<view>
							价值:
							<text v-if="optionsData.useType === '04'" style="text-decoration: line-through">{{ optionsData.faceValue * 10 }}折</text>
							<text v-else style="text-decoration: line-through">￥{{ optionsData.faceValue || 0 }}</text>
						</view>
						<view>
							到手价:
							<text style="color: #ff0000" v-if="optionsData.eventTypeCode !== '4'">￥</text>
							<text class="text-bold" v-if="optionsData.eventTypeCode === '1'" style="color: #ff0000; font-size: 40upx; line-height: 40upx">
								{{ optionsData.cash }}
							</text>
							<text
								class="text-bold"
								v-if="optionsData.eventTypeCode === '3' || optionsData.eventTypeCode === '2'"
								style="color: #ff0000; font-size: 40upx; line-height: 40upx"
							>
								{{ optionsData.cash }}
							</text>
							<text class="text-bold" v-if="optionsData.eventTypeCode === '4'" style="color: #ff0000; font-size: 40upx; line-height: 40upx">
								{{ optionsData.points }}
							</text>
							<text class="text-bold" v-if="optionsData.eventTypeCode === '5'" style="color: #ff0000; font-size: 40upx; line-height: 40upx">
								{{ optionsData.cash }}+{{ optionsData.points }}
							</text>
							<text v-if="optionsData.eventTypeCode === '4' || optionsData.eventTypeCode === '5'">积分</text>
						</view>
					</view>
					<view class="progress">
						<view class="speed-progress" :style="handleProgress"></view>
						<block v-if="optionsData.restNumDay != 0">
							<view style="white-space: nowrap">剩余{{ optionsData.restNumDay || 0 }}</view>
						</block>
						<block v-else>
							<view style="white-space: nowrap" v-if="optionsData.restNum == 0">已领券{{ optionsData.grabbedNum || 0 }}</view>
							<view style="white-space: nowrap" v-else>剩余{{ optionsData.restNum || 0 }}</view>
						</block>
					</view>
				</view>
			</view>
			<view class="content">
				<view class="active-time" v-if="optionsData.eventEffDate">活动时间：{{ optionsData.eventEffDate }} - {{ optionsData.eventExpDate }}  {{optionsData.weekDesc?'每'+optionsData.weekDesc:''}}</view>
				<view class="price-info" v-if="optionsData.eventTypeCode !== '3' && optionsData.status === 'Y'">
					<view class="price-flex">
						<text class="flex-title">购买方式:</text>
						<text class="flex-cont" v-if="optionsData.eventTypeCode === '1'">人民币</text>
						<text class="flex-cont" v-if="optionsData.eventTypeCode === '4'">积分</text>
						<text class="flex-cont" v-if="optionsData.eventTypeCode === '5'">人民币+积分</text>
					</view>
					<view class="price-flex">
						<text class="flex-title">购买数量:</text>
						<view class="flex-cont" style="display: flex; align-items: center">
							<text style="font-size: 26upx; color: #666; margin-right: 10upx; flex: 1">
								<block v-if="optionsData.custRestNum > 0">每人限买{{ optionsData.custRestNum || 0 }}张</block>
								<block v-else-if="optionsData.status === 'Y'">不限量</block>
							</text>
							<base-stepper customClass="align-center" :disabled="false" :stNum="cartNum" :min="1" :max="custRestNum" @numChange="numChange" />
						</view>
					</view>
					<template v-if="optionsData.useType !== '04'">
						<view class="price-flex" v-if="optionsData.eventTypeCode === '1'">
							<text class="flex-title">购券总额:</text>
							<text class="flex-cont">{{ parseFloatTwo(optionsData.faceValue * cartNum) }} 元</text>
						</view>
						<view class="price-flex" v-if="optionsData.eventTypeCode === '1'">
							<text class="flex-title">优惠立减:</text>
							<text class="flex-cont">{{ parseFloatTwo((optionsData.faceValue - optionsData.cash) * cartNum) }}元</text>
						</view>
					</template>
					<view class="price-flex">
						<text class="flex-title">实际应付:</text>
						<text v-if="optionsData.eventTypeCode === '1'" class="flex-cont text-xl" style="color: #ff0000">
							<text class="text-bold">{{ parseFloatTwo(optionsData.cash * cartNum) }}</text>
							<text style="font-size: 30upx">元</text>
						</text>
						<text v-if="optionsData.eventTypeCode === '4'" class="flex-cont text-xl" style="color: #ff0000">
							<text class="text-bold">{{ optionsData.points * cartNum }}</text>
							<text style="font-size: 30upx">积分</text>
						</text>
						<text v-if="optionsData.eventTypeCode === '5'" class="flex-cont text-xl" style="color: #ff0000">
							<text class="text-bold">￥{{ parseFloatTwo(optionsData.cash * cartNum) }}+{{ optionsData.points * cartNum }}</text>
							<text style="font-size: 30upx">积分</text>
						</text>
					</view>
				</view>
				<view class="price-info" style="text-align: left; border: none">
					<view class="price-flex" v-if="optionsData.validityDesc">
						<text class="flex-title">券有效期:</text>
						<text class="flex-cont">{{ optionsData.validityDesc }}</text>
					</view>
					<view class="price-flex padding-bottom-xs" style="line-height: 40upx">
						<text class="flex-title">详细说明:</text>
						<view class="flex-cont">
							<block v-for="(item, index) in optionsData.simpleUsageRule && optionsData.simpleUsageRule.split('\n')" :key="index">
								<view>{{ item }}</view>
							</block>
						</view>
					</view>
					<view class="price-flex" style="line-height: 40upx">
						<text class="flex-title">使用描述:</text>
						<view class="flex-cont">
							<block v-for="(item, index) in optionsData.detailUsageDesc && optionsData.detailUsageDesc.split('\n')" :key="index">
								<view>{{ item }}</view>
							</block>
						</view>
					</view>
				</view>
			</view>
			<image
				src="https://img.songlei.com/-1/material/a2d2c21b-1a99-4899-8ce9-a380303b3fa7.png"
				style="width: 100%; height: 20upx; position: absolute; bottom: -20upx; left: 0"
				mode="aspectFill"
			></image>
		</view>
		<template v-if="optionsData.status === 'Y'">
			<view class="one-btn" @click="btnCoupon" style="background: linear-gradient(-90deg, #ed5279 0%, #ff0043 100%); color: #fff" v-if="optionsData.eventTypeCode === '1'">
				立即购买
			</view>
			<view
				class="one-btn"
				@click="btnCoupon"
				style="background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%); color: #fff"
				v-if="optionsData.eventTypeCode === '3' || optionsData.eventTypeCode === '2'"
			>
				立即领取
			</view>
			<view class="one-btn" @click="btnCoupon" v-if="optionsData.eventTypeCode === '4'">立即兑换</view>
			<view class="one-btn" @click="btnCoupon" v-if="optionsData.eventTypeCode === '5'">立即购买</view>
		</template>
		<template v-else>
			<view class="one-btn" style="background: #ddd; color: #fff">{{ optionsData.statusDesc }}</view>
		</template>
		<view class="toast-pay" v-if="toastHide">
			<pay-components ref="pay" :pageTitle="pageTitle" :callBack="true" @success="successBack" @fail="failBack"></pay-components>
			<view @click="$noMultipleClicks(payOrder)" class="one-btn">确定</view>
		</view>
		<view class="ZZC" @click="toastClose" v-if="toastHide"></view>

		<view
			style="border-bottom: #aaa solid 2px; width: 499rpx; margin: 50rpx auto 30rpx; text-align: center; color: #ff0000"
			v-if="optionsData.pendingOrders && optionsData.pendingOrders.length > 0"
			 @tap.stop="goToOrders"
		>
			当前活动您有{{ optionsData.pendingOrders.length }}个订单未支付，去看看 =>
		</view>
	</view>
</template>

<script>
const app = getApp();
import { getActiveDetails, submitInfo, nopayCancel, nopayList } from '@/pages/coupon/api/coupon.js';
import { handleTime } from '@/utils/util.js';
import util from 'utils/util';
import baseStepper from '@/components/base-stepper/index';
export default {
	data() {
		return {
			theme: app.globalData.theme,
			optionsData: {}, // 活动数据
			cartNum: 1, // 购买数量
			eventId: '', // 优惠券id
			progress: '', // 进度条
			pageTitle: '领券中心',
			toastHide: false, //支付选择弹框
			loading: false, // loading是否
			custRestNum: 0,
			noClick: true,
			sharer_user_code: ''
		};
	},
	components: {
		baseStepper
	},
	computed: {
		handleProgress() {
			return `--s-progress: ${this.progress};`;
		}
	},
	// #ifdef MP-WEIXIN
	onShareTimeline: function () {
		let optionsData = this.optionsData;
		let title = optionsData.eventTypeName || '活动详情';
		// let sharePic = '';
		// if (goodsSpu.sharePic) {
		// 	sharePic = goodsSpu.sharePic;
		// }
		// let imageUrl = goodsSpu.picUrls[0];
		// let imageUrl = sharePic ? sharePic : goodsSpu.picUrls[0];

		const userInfo = uni.getStorageSync('user_info');
		let userCode = userInfo ? '&type=1&sharer_user_code=' + userInfo.userCode : '';
		let path = 'id=' + this.eventId + userCode;
		// this.dataController({
		// 	bhv_type: "share",
		// 	item_id: this.goodsSpu.id
		// });
		// const app = getApp();
		// if (app.globalData.gdtVid) {
		// 	api.postTimelineAdvertAction({
		// 		action: 'SHARE',
		// 		clickId: app.globalData.gdtVid || ''
		// 	});
		// }

		return {
			title: title,
			query: path,
			// imageUrl: imageUrl,
			success: function (res) {
				uni.showToast({
					title: '分享成功'
				});
			},
			fail: function (res) {
				// 转发失败
				uni.showToast({
					title: '分享失败',
					icon: 'none'
				});
			}
		};
	},
	// #endif
	onLoad(options) {
		// 保存别人分享来的 userCode
		util.saveSharerUserCode(options);
		if (options.scene) {
			const qrCodeScene = decodeURIComponent(options.scene);
			if (qrCodeScene) {
				//接受二维码中参数  参数sf=XXX&id=XXX
				const qrCodeSceneArray = qrCodeScene.split('&');
				const id = util.UrlParamHash(qrCodeScene, 'id');
				this.eventId = id;
				return;
			}
		}

		if (options.q) {
			const qrCodeScene = decodeURIComponent(options.q);
			if (qrCodeScene) {
				//接受二维码中参数  参数sf=XXX&id=XXX
				const id = util.UrlParamHash(qrCodeScene.split('?')[1], 'id');
				this.eventId = id;
				return;
			}
		}

		if (options.id) {
			this.eventId = options.id;
		}
		this.handleUrlParams('id');
	},

	onShow() {
		this.loopUser();
	},
	methods: {
		handleUrlParams(params) {
			var pages = getCurrentPages(); //获取页面栈
			var currPage = pages[pages.length - 1]; // 当前页面
			const options = currPage.options;
			for (let i in options) {
				console.log(i, options[i]);
			}
			// 二维码扫码进入
			if (options.q) {
			}
			// 小程序码进入
			if (options.scene) {
			}
		},
		loopUser() {
			const that = this;
			const user_info = uni.getStorageSync('user_info');
			if (!user_info) {
				setTimeout(() => {
					this.loopUser();
				}, 0);
				return;
			}
			const { id, erpCid } = user_info;
			if (!id) {
				uni.navigateTo({
					url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-centre-details/index?id=' + this.eventId)
				});
				return;
			}
			if (!erpCid) {
				app.doLogin().then((res) => {
					if (res === 'success') {
						const user = uni.getStorageSync('user_info');
						if (user.erpCid) {
							this.getActiveDetails();
						} else {
							uni.showModal({
								title: '提示',
								content: '登录错误',
								showCancel: false,
								success() {
									uni.navigateTo({
										url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-centre-details/index?id=' + that.eventId)
									});
								}
							});
						}
					} else {
						uni.navigateTo({
							url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-centre-details/index?id=' + this.eventId)
						});
					}
				});
				return;
			}
			this.getActiveDetails();
		},
		numChange(val) {
			this.cartNum = val;
		},
		getActiveDetails() {
			getActiveDetails({
				eventId: this.eventId,
				inviter: this.sharer_user_code
			}).then((res) => {
				const { code, data = {}, msg } = res;
				if (+code === 0) {
					this.optionsData = data;
					// 获取进度条
					const { grabbedNum, totalNum, eventEffDate, eventExpDate, custRestNum, status, restNumDay } = this.optionsData;
					this.custRestNum = custRestNum;
					if (+custRestNum <= 0 && status !== 'Y') {
						this.cartNum = 0;
						this.custRestNum = 0;
					} else if (+custRestNum === 0 && status === 'Y') {
						this.custRestNum = 1000;
					}
					if (+restNumDay) {
						this.progress = (+grabbedNum / (+restNumDay + +grabbedNum)) * 100 + '%';
					} else if (+totalNum) {
						this.progress = (+grabbedNum / +totalNum) * 100 + '%';
					} else {
						this.progress = +grabbedNum ? '10%' : '0';
					}
					// 处理时间
					this.optionsData.eventEffDate = handleTime(eventEffDate, 'yyyy-MM-dd');
					this.optionsData.eventExpDate = handleTime(eventExpDate, 'yyyy-MM-dd');
				} else {
					uni.showModal({
						title: '提示',
						content: msg,
						showCancel: false,
						success() {
							uni.switchTab({
								url: '/pages/home/<USER>'
							});
						}
					});
				}
			});
		},
		btnCoupon() {
			if (this.loading) {
				return;
			}
			this.loading = true;
			uni.showLoading({
				title: '领取中.....',
				mask: true
			});
			const { eventId, optionsData, cartNum } = this;
			const { points, cash, eventTypeCode, custRestNum, status, eventPicUrl } = optionsData;
			const params = {
				eventId,
				points: points * cartNum || 0,
				num: cartNum,
				amount: cash * cartNum || 0,
				sceneCode: app.globalData.sf,
				inviter: this.sharer_user_code
			};
			if (+custRestNum <= 0 && status !== 'Y') {
				uni.showModal({
					title: '提示',
					content: '您的限购数量不足',
					showCancel: false
				});
				this.loading = false;
				uni.hideLoading();
				return;
			}
			submitInfo(params)
				.then((res) => {
					if (res && res.code === 0) {
						if (+eventTypeCode === 1 || +eventTypeCode === 5) {
							this.toastHide = true;
							this.payParams = {};
							this.payParams = res.data.payRequest;
						} else if (+eventTypeCode === 3) {
							this.update();
							wx.showModal({
								title: '提示',
								content: '领取成功',
								icon: 'none',
								cancelText: '继续领券',
								confirmText: '去使用',
								success(res) {
									if (res.confirm) {
										uni.navigateTo({
											url: eventPicUrl ? eventPicUrl : '/pages/coupon/coupon-user-list/index?navTabCur=1'
										});
									} else if (res.cancel) {
										uni.navigateBack({
											delta: 1
										});
									}
								}
							});
						} else {
							this.update();
							wx.showModal({
								title: '提示',
								content: '兑换成功',
								icon: 'none',
								cancelText: '继续领券',
								confirmText: '去使用',
								success(res) {
									if (res.confirm) {
										uni.navigateTo({
											url: eventPicUrl ? eventPicUrl : '/pages/coupon/coupon-user-list/index?navTabCur=1'
										});
									} else if (res.cancel) {
										uni.navigateBack({
											delta: 1
										});
									}
								}
							});
						}
					} else if (res.code === 90001) {
						uni.showModal({
							title: '提示',
							content: (res && res.msg) || '有未支付订单，取消后在支付',
							showCancel: false,
							success() {
								uni.navigateTo({
									url: '/pages/coupon/coupon-nopay-list/index'
								});
							}
						});
					}
				})
				.finally(() => {
					uni.hideLoading();
					setTimeout(() => {
						this.loading = false;
					}, 1000);
				});
		},
		payOrder() {
			uni.showLoading({
				title: '支付中',
				mask: true
			});
			this.$refs.pay?.payOrder(this.payParams);
		},
		parseFloatTwo(num) {
			if (num) {
				return num.toFixed(2);
			} else {
				return 0;
			}
		},
		toastClose() {
			this.toastHide = false;
		},
		// 银行卡刷新
		updataBankList() {
			this.$refs.pay?.getBankList();
			this.loading = false;
		},
		// 支付成功的回调
		successBack() {
			const that = this;
			const eventPicUrl = this.optionsData?.eventPicUrl;
			wx.showModal({
				title: '提示',
				content: '支付成功',
				icon: 'none',
				cancelText: '继续领券',
				confirmText: '去使用',
				success(res) {
					if (res.confirm) {
						uni.navigateTo({
							url: eventPicUrl ? eventPicUrl : '/pages/coupon/coupon-user-list/index'
						});
						that.toastClose();
					} else if (res.cancel) {
						uni.navigateBack({
							delta: 1
						});
						that.toastClose();
					}
				}
			});
		},
		update() {
			let pages = getCurrentPages(); // 拿到页面栈
			if (pages.length === 1) {
				return;
			}
			let beforePage = pages[pages.length - 2];
			//updateTime为上一个页面的刷新数据函数；
			if (beforePage?.route === 'pages/coupon/coupon-centre/index') {
				beforePage?.$vm?.statusChange(this.eventId);
			}
		},
		// 支付失败的回调
		failBack() {
			uni.showToast({
				title: '支付失败',
				icon: 'none'
			});
			this.loading = false;
			this.toastHide = false;
			// 跳转到券待支付列表页面
			uni.navigateTo({
				url: '/pages/coupon/coupon-nopay-list/index'
			});
		},
		
		goToOrders(){
			uni.navigateTo({
			  url: '/pages/coupon/coupon-nopay-list/index'
			})
		}
	}
};
</script>

<style scoped>
.coupon-page {
	position: relative;
	text-align: center;
	margin: 30upx;
	background-color: #fff;
	min-height: 600upx;
	background: radial-gradient(circle at left 367upx, transparent 13upx, #ffffff 0) 0 20upx/50% 100% no-repeat,
		radial-gradient(circle at right 367upx, transparent 13upx, #ffffff 0) 100% 20upx/50% 100% no-repeat;
}

.coupon-page .content {
	padding: 30upx 20upx;
	text-align: left;
	font-size: 26upx;
}

.coupon-page .content .active-time {
	padding-bottom: 30upx;
	border-bottom: 1upx dotted #999;
}

.coupon-header {
	width: 100%;
	height: 387upx;
	background: url('https://img.songlei.com/-1/material/bd34a3b4-a208-46c7-9fe3-2205d0711161.png') no-repeat;
	background-size: 100%;
	padding: 20upx;
}

.speed-progress {
	width: 478upx;
	height: 18upx;
	border-radius: 9upx;
	background: #f7ebdd;
	position: relative;
	overflow: hidden;
}

.speed-progress::after {
	content: '';
	position: absolute;
	height: 100%;
	width: var(--s-progress);
	left: 0;
	border-radius: 9upx;
	background: linear-gradient(0deg, #c09979 0%, #d4b69e 0%, #f4decc 100%);
}

.progress {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 10upx;
}

.title {
	font-size: 32upx;
	padding-top: 10upx;
}

.price {
	font-size: 90upx;
	font-weight: bold;
	color: #ff4015;
	vertical-align: top;
	line-height: 70upx;
	padding-top: 20upx;
}

.price .icon {
	font-size: 40upx;
	position: relative;
	top: -30upx;
}

.threshold {
	font-size: 26upx;
	padding-bottom: 20upx;
}

.fff-box {
	width: 652upx;
	height: 128upx;
	background-color: #fff;
	border-radius: 28upx;
	padding: 20upx;
	font-size: 26upx;
	color: #666;
}

.all-price {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 10upx;
	border-bottom: 1upx dotted #e1c6b2;
}

.price-info {
	padding: 30upx 0;
	border-bottom: 1upx dotted #999;
	text-align: right;
}

.price-flex {
	display: flex;
	align-items: flex-start;
	line-height: 70upx;
}

.flex-cont {
	flex: 1;
	padding-left: 10upx;
	position: relative;
}

.count-box {
	width: 56upx;
	height: 56upx;
	border-radius: 10upx;
	background-color: #f4f4f4;
	display: inline-block;
	text-align: center;
	line-height: 56upx;
}

.one-btn {
	width: 570upx;
	margin: 50upx auto 30upx;
	text-align: center;
	line-height: 90upx;
	background: linear-gradient(-90deg, #ffbf00 0%, #ffdd00 100%);
	border-radius: 45upx;
}

.toast-pay {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100vw;
	background-color: #fff;
	padding: 30upx 0 20upx;
	border-radius: 20upx 20upx 0 0;
	z-index: 10;
}

.ZZC {
	position: fixed;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.5);
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 2;
}
</style>
