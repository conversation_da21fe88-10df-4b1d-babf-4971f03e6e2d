<template>
	<view class="flex flex-direction" style="height: 100vh;">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">我的优惠券</block>
		</cu-custom>

		<!-- list页面 -->
		<scroll-view scroll-y refresher-enabled class="flex-sub" :refresher-triggered="triggered"
			@scrolltolower="reachBottom" @refresherrefresh="refresher" style="overflow: hidden;">
			<view class="cu-list padding-bottom-sm">
				<template>
					<view v-if="status != '04'" class="search flex justify-between align-center padding-xs">
						<input :value="oldScreeParams.keyword" type="text"
							class="search-input flex-sub padding-lr padding-tb-xs text-xsm" placeholder="搜索我的优惠券"
							@input="changeSearchInput" @confirm="searchConfirm" />
						<view class="margin-left-sm" @click="showScreen">筛选</view>
					</view>
					<view class="cu-item padding-xs bg-white">
						<view v-if="bannerObj && bannerObj.linkUrl" hover-class="none" :url="bannerObj.linkUrl"
							@click="handleBanner(bannerObj.linkUrl)" style="
							  background-color: #ffffff;
							  padding: 0 20rpx 0 20rpx;
							  position: relative;
							">
							<image mode="widthFix" style="width: 100%; height: 280rpx;border-radius: 20rpx;"
								:src="bannerObj.imgUrl"></image>
						</view>
						<template v-for="(item, index) in couponUserList">
							<coupon-infos-list v-if="status == '04'" :couponUserInfo="item"
								:key="index"></coupon-infos-list>
							<coupon-offline-user-info v-else :couponUserInfo="item"
								:key="index"></coupon-offline-user-info>
						</template>
					</view>
					<view :class="'cu-load  ' + (loadmore ? 'loading' : 'over')"></view>
				</template>

			</view>
		</scroll-view>

		<view class="dialog-search padding oldScreenAnimate" v-if="showScreenZZC"
			:class="showScreenAnimate ? 'screenAnimate' : ''">
			<view class="dialog-title text-lg text-bold padding-bottom-sm" style="line-height: 40rpx;">筛选</view>
			<view style="max-height: 60vh;overflow: auto;">
				<view class="dialog-content" v-for="item in screen">
					<view class="text-bold text-df">{{ item.name }}</view>
					<view class="margin-tb-sm justify-between flex flex-wrap dialog-type">
						<template v-if="item.children&&item.children.length>6">
							<template v-if="item.showAll">
								<template v-for="items in item.children">
									<view class="margin-bottom-sm margin-right-sm dialog-item text-df"
										:class="`${screenParams[item.value] || ''}` == items.value ? 'hover' : '' "
										@click="screeClick(item.value, items.value)">{{ items.name }}</view>
								</template>
							</template>
							<template v-else>
								<template v-for="items in  item.children.slice(0,7) ">
									<view class="margin-bottom-sm margin-right-sm dialog-item text-df"
										:class="`${screenParams[item.value] || ''}` == items.value ? 'hover' : '' "
										@click="screeClick(item.value, items.value)">{{ items.name }}</view>
								</template>
								<view>查看更多</view>
							</template>
						</template>
						<template v-else>
							<template v-for="items in item.children">
								<view class="margin-bottom-sm margin-right-sm dialog-item text-df"
									:class="`${screenParams[item.value] || ''}` == items.value ? 'hover' : '' "
									@click="screeClick(item.value, items.value)">{{ items.name }}</view>
							</template>
						</template>

					</view>
				</view>
			</view>
			<view class="dialog-footer flex justify-center align-center padding">
				<view class="dialog-bottom" @click="resotre"
					style="border-top-left-radius: 35rpx;border-bottom-left-radius: 35rpx;background-color: rgba(244, 213, 163, 1);">
					重置</view>
				<view class="dialog-bottom" @click="completeSreach"
					style="border-top-right-radius: 35rpx;border-bottom-right-radius: 35rpx;">完成</view>
			</view>
		</view>
		<view class="ZZC" v-if="showZZC" @click="close"></view>
	</view>
</template>
<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import couponOfflineUserInfo from "../components/coupon-offline-user-info/index"
	import couponInfosList from "../components/coupon-info-list/index"
	import {
		getEvtscdList,
		vipTicket
	} from "@/pages/coupon/api/coupon.js"
	import {
		getByType
	} from "@/api/base.js"
	import {
		mapState
	} from 'vuex'
	import {
		gotoPage
	} from "@/components/div-components/div-base/div-page-urls.js";
	import {
		getCurrentTitle,
		senTrack
	} from '@/public/js_sdk/sensors/utils.js';

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				loadmore: true, // 判断是否还有下一页
				couponUserList: [], // 我的劵列表
				showScreenZZC: false, // 筛选遮罩层
				showZZC: false, // 遮罩层
				showScreenAnimate: false, // 筛选动画
				bannerObj: null,
				// 转赠基础选项
				screen: [{
					name: '类目',
					value: "marketCategory",
					showAll: false,
					children: [{
							name: '全部类目',
							value: ''
						},
						{
							name: '百货',
							value: '01'
						},
						{
							name: '美妆',
							value: '02'
						},
						{
							name: '超市',
							value: '03'
						},
						{
							name: '餐饮',
							value: '04'
						},
						{
							name: '电影',
							value: '05'
						}, {
							name: '女装',
							value: '06'
						},
						{
							name: '礼品',
							value: '07'
						},
						{
							name: '黄金珠宝',
							value: '08'
						},
						{
							name: '运动',
							value: '09'
						},
						{
							name: '鞋品',
							value: '10'
						}, {
							name: '儿童',
							value: '11'
						},
						{
							name: '停车',
							value: '12'
						}, {
							name: '内衣',
							value: '13'
						},
						{
							name: '潮品',
							value: '14'
						},
						{
							name: '男装',
							value: '15'
						},
						{
							name: '权益',
							value: '16'
						}, {
							name: '品牌',
							value: '17'
						}, {
							name: '精品',
							value: '18'
						},
					]
				}, {
					name: '类型',
					value: 'useType',
					showAll: false,
					children: [{
							name: '全部类型',
							value: ''
						},
						{
							name: '满减券',
							value: '000001'
						},
						{
							name: '折扣券',
							value: '000003'
						},
						{
							name: '礼品券',
							value: '000002'
						},
						{
							name: '停车券',
							value: '000004'
						},
						{
							name: '运费券',
							value: '000007'
						},
						{
							name: '红包',
							value: '000005'
						},
					]
				}, {
					name: '门店',
					value: 'marketStore',
					showAll: false,
					children: [{
							name: '全部门店',
							value: ''
						},
						{
							name: '线下南岗店',
							value: '201'
						},
						{
							name: '线下香坊店',
							value: '203'
						},
						{
							name: '线上商城',
							value: '288'
						}
					]
				}, {
					name: '排序',
					value: 'orderFields',
					showAll: false,
					children: [{
							name: '综合排序',
							value: ''
						},
						{
							name: '面值大到小',
							value: 'face_value'
						},
						{
							name: '新到优先',
							value: 'issue_date'
						},
						{
							name: '快到期优先',
							value: 'exp_date'
						}
					]
				}],
				screenParams: {}, // 筛选参数
				oldScreeParams: {}, // 筛选里面参数
				triggered: false, // 下拉加载
				status: '',
			};
		},
		components: {
			couponOfflineUserInfo,
			couponInfosList
		},
		computed: {
			...mapState([
				'CustomBar',
			]),
		},
		props: {},
		onLoad(options) {
			if (!app.isLogin()) {
				return;
			}
			this.status = options.id
			app.initPage().then(res => {
				if (options.id == '04') {
					this.offlineUserCoupon();
				} else {
					this.activeCoupon({
						status: this.status,
						...this.oldScreeParams,
						excGroupId: '09'
					}, () => {});

				}
			});
			this.getByType();
		},
		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.oldScreeParams = {};
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		methods: {
			handleBanner(page) {
				gotoPage(page);
			},
			advertisement(id) {
				api.advertisementinfo({
					bigClass: 'MY_COUPON'
				}).then(res => {
					console.log("advertisement===>", res.data);
					this.bannerObj = res.data;
				});
			},
			reachBottom() {
				if (this.loadmore) {
					this.page.current = this.page.current + 1;
					this.activeCoupon({
						status: this.status,
						...this.oldScreeParams,
						excGroupId: '09'
					}, () => {});
				}
			},
			refresher() {
				if (this._freshing) return;
				this._freshing = true;
				this.triggered = true;
				this.loadmore = true;
				this.page.current = 1;
				this.activeCoupon({
					status: this.status,
					...this.oldScreeParams,
					excGroupId: '09'
				}, () => {
					this.triggered = false;
					this._freshing = false;
				});
			},
			getByType() {
				getByType().then(res => {
					const arr = res.data;
					arr.forEach(item => {
						if (item.typeName === "appmkt") {
							let marketStore = [{
								name: '全部门店',
								value: ''
							}];
							item.records.forEach(items => {
								marketStore.push({
									name: items.name,
									value: items.code
								})
							})
							this.screen[2].children = marketStore;
						}
						if (item.typeName === "appcat") {
							let omptype = [{
								name: '全部类目',
								value: ''
							}];
							item.records.forEach(items => {
								omptype.push({
									name: items.name,
									value: items.code
								})
							})
							this.screen[0].children = omptype;
						}
						if (item.typeName === "usetype") {
							let useType = [{
								name: '全部类型',
								value: ''
							}];
							item.records.forEach(items => {
								useType.push({
									name: items.name,
									value: items.code
								})
							})
							this.screen[1].children = useType;
						}
					})
				})
			},
			// 活动接口
			activeCoupon(data = {}, callback, fallback) {
				const {
					current,
					size
				} = this.page;
				let params = {
					pageNo: current,
					pageSize: size,
					...data
				}
				if (this.status == '03') {
					//已过期
					params.orderDirection = 'DESC'
					params.orderFields = 'exp_date'
				} else if (this.status == '02,07' || this.status == '02') {
					//已使用
					params.orderFields = 'last_date DESC'
				}
				getEvtscdList(params).then(res => {
					if (res.data != null) {
						let couponUserList = res.data.datalist
						this.couponUserList = [...this.couponUserList, ...couponUserList];
						this.loadmore = !(couponUserList.length < this.page.size)
						callback && callback();
					} else {
						this.loadmore = false;
						fallback && fallback()
					}
					if (current == 1) {
						this.handleSenTrack(res.data.rowsCount);
					}
				}).catch(err => {
					this.loadmore = false;
					fallback && fallback()
				})
			},
			//通道券
			offlineUserCoupon(callback) {
				const page = {
					...this.page
				};
				delete page.group
				vipTicket(Object.assign({}, page, {
					status: 0,
				})).then(res => {
					if (res.data != null) {
						let couponUserList = res.data.cust_accnts || [];
						this.couponUserList = [...this.couponUserList, ...couponUserList];
						this.loadmore = !(couponUserList.length < this.page.size)
						callback && callback()
					} else {
						this.loadmore = false;
					}
					if (page.pageNo == 1) {
						this.handleSenTrack(res.data.rowsCount);
					}
				})
			},
			// 重置数据
			refresh() {
				this.loadmore = true;
				this.couponUserList = [];
				this.page.current = 1;
				this.activeCoupon({
					status: this.status,
					...this.oldScreeParams,
					excGroupId: '09'
				}, () => {});
			},
			screeClick(params, value) {
				if (params === 'orderFields' && value === 'face_value') {
					this.$set(this.screenParams, 'orderDirection', 'DESC')
				} else {
					delete this.screenParams.orderDirection
				}
				if (!value) {
					this.$set(this.screenParams, params, value)
					this.$nextTick(() => {
						delete this.screenParams[params]
					})
					return;
				}
				this.$set(this.screenParams, params, value)
			},
			showScreen() {
				this.showZZC = true;
				this.showScreenZZC = true;
				this.$nextTick(() => {
					this.showScreenAnimate = true;
				})
				this.screenParams = this.oldScreeParams;
			},
			changeSearchInput(e) {
				this.$set(this.oldScreeParams, 'keyword', e.detail.value)
			},
			searchConfirm() {
				this.refresh();
			},
			resotre() {
				this.screenParams = {};
			},
			completeSreach() {
				this.oldScreeParams = this.screenParams;
				this.close();
				this.refresh()
			},
			close() {
				this.showScreenAnimate = false;
				this.screenParams = {};
				setTimeout(() => {
					this.showScreenZZC = false;
					this.showZZC = false;
				}, 200)
			},

			handleSenTrack(num) {
				const params = {
					coupon_btype: '线下券',
					coupon_number: num
				}
				params.coupon_status = this.status == '02,07' ? '已使用' : this.status == '03' ? '已过期' : this.status == '04' ?
					'礼金' : '';
				senTrack('CouponListPageView', params)
			}
		}
	};
</script>

<style scoped>
	.search {
		background-color: #fff;
		position: sticky;
		top: 0;
		z-index: 2;
	}

	.search-input {
		border: 1rpx solid #999;
		border-radius: 50rpx;
		height: 30rpx;
		box-sizing: content-box;
		line-height: 1;
	}

	.ZZC {
		position: fixed;
		top: 0;
		left: 0;
		background-color: rgba(0, 0, 0, .5);
		width: 100%;
		height: 100%;
		z-index: 10;
	}

	.dialog-search {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		overflow: auto;
		background-color: #f1f1f1;
		z-index: 11;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
	}

	.dialog-title {
		text-align: center;
	}

	.dialog-item {
		width: 200rpx;
		height: 60rpx;
		border-radius: 30rpx;
		text-align: center;
		background-color: #ddd;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.dialog-content .hover {
		color: #CDAD90;
		border: 1rpx solid #CDAD90;
		background-color: rgba(205, 170, 141, .1);
	}

	.dialog-type::after {
		content: "";
		width: 200rpx;
		margin-right: 20rpx;
	}

	.dialog-bottom {
		width: 330rpx;
		height: 70rpx;
		background-color: #CDAD90;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #333;
	}

	.screenAnimate {
		height: calc(60vh + 250rpx) !important;
	}

	.oldScreenAnimate {
		height: 0;
		transition: all 0.2s linear;
	}
</style>