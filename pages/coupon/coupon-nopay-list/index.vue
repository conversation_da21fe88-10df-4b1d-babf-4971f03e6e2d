<template>
  <view class="flex-warp-all">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true">
      <block slot="backText">返回</block>
      <block slot="content">券订单</block>
    </cu-custom>
    <view class="heder-nav">
      <view class="nav-item" v-for="(item, index) in navList" @click="navCheck(index)" :key="index">
        <view :class="index === navIndex ? 'hover': ''">{{item.label}}</view>
        <i v-if="index === navIndex" class="nav-icon"></i>
      </view>
    </view>
    <scroll-view
      class="flex1"
      :scroll-y="true"
      refresher-enabled
      :refresher-triggered="triggered"
      @scrolltolower="scrollBottom"
      @refresherrefresh="refrePull"
    >
      <view style="overflow: hidden;color: #282627;">
        <view
          v-for="item in list"
          :key="item.id"
          class="margin-sm bg-box radius-lg"
          @click="navTor(item.id)"
        >
          <view class="header padding-sm">
            <view class="flex align-center">
              <text style="line-height: 40upx;">订单编号：{{item.id}}</text>
              <image @click.native.stop="copy(item)" src="https://img.songlei.com/-1/material/fc6a68d4-3ec6-48c9-a083-fbd1ff90b592.png" style="width: 40upx;height: 40upx;"></image>
            </view>
            <text style="color: #b99b7b;">{{orderStatus[item.orderStatus]}}</text>
          </view>
          <view class="content padding-sm">
            <view class="content-img">
              <image 
                :src="item.couponPicUrl || 'https://img.songlei.com/-1/material/5ba00ad5-c236-41fa-93ea-a4f4fb89a5e8.png'" 
                mode="aspectFill" 
                style="width: 100%;height: 100%;"
              ></image>
            </view>
            <view class="content-goods">
              <view class="content-title">{{ item.typeName }}</view>
              <view class="content-price">
                <text style="font-weight: bold;color: #b39977;font-size: 32upx;">
                  <!-- {{ item.faceValue }} -->
                  <block v-if="item.useType === '04'">
                    <text>{{ item.faceValue * 10 }}</text>
                    <text class="icon" style="bottom: 0;top: auto;font-size: 80%;">折</text>
                  </block>
                  <block v-else>
                    <text class="icon" style="font-size: 80%;">￥</text>
                    <text>{{item.faceValue || 0}}</text>
                  </block>
                </text>
                <text class="text-coupon-btn">券礼包</text>
              </view>
              <view style="font-size: 24upx;color: #999;">活动专用</view>
            </view>
          </view>
          <view class="footer padding-sm">
            <view class="couponInfo">
              <text v-if="item.eventTypeCode === '1'">{{item.amount}}元</text>
              <text v-if="item.eventTypeCode === '3'">免费</text>
              <text v-if="item.eventTypeCode === '4'">{{item.points}}积分</text>
              <text v-if="item.eventTypeCode === '5'">{{item.amount}}元 + {{item.points}}积分</text>
              ({{item.num}}张)
            </view>
            <view class="flex">
              <view class="couponBtn" style="margin-right: 20upx;" @click.native.stop="cancel(item.id)" v-if="item.orderStatus === 'N' || item.orderStatus === 'P'">取消</view>
              <view class="couponBtn" @click.native.stop="btnCoupon(item.id)" v-if="item.orderStatus === 'N' || item.orderStatus === 'P'">去支付</view>            
            </view>
          </view>
        </view>
      </view>
      <view class="loading">{{ params.pageNo >= params.pageCount ? '暂时没有更多了~~' : '上拉加载更多' }}</view>
    </scroll-view>
    <view class="toast-pay" v-if="toastHide">
      <pay-components
        ref="pay"
        :pageTitle="pageTitle"
        :callBack="true"
        @success="successBack"
        @fail="failBack"
      ></pay-components>
      <view @click="$noMultipleClicks(payOrder)" class="one-btn">确定</view>
    </view>
    <view class="ZZC" @click="toastClose" v-if="toastHide"></view>
  </view>
</template>

<script>
const app = getApp()
import { nopayCancel, nopayList, payorder } from "@/pages/coupon/api/coupon.js"
import { navCoupon } from "../components/utils.js"
export default {
  data() {
     return {
       theme: app.globalData.theme,
       list: [],
       orderStatus: {
         'N': '未支付',
         'Y': '已完成',
         'C': '已取消',
         'A': '已取消',
         'AA': '已取消',
         'R': '退款中',
         'E': '已部分退款',
         'T': '退款中',
         'F': '已退款',
         'P': '待支付'
       },
       params: {
         pageNo: 1,
         pageCount: 0,
         orderStatus: 'N,P'
       },
       navList: [{
         label: '待支付',
         value: 'N,P'
       }, {
         label: '已完成',
         value: 'Y'
       }, {
         label: '已取消',
         value: 'C,E,F'
       }],
       toastHide: false,
       pageTitle: '券订单',
       navIndex: 0,
       triggered: false,
       noClick: true
     }
  },
  onLoad() {
  },
  onShow() {
    this.params.pageNo = 1;
    this.params.pageCount = 0;
    this.list = [];
    this.nopayList();
  },
  //触底函数
  onReachBottom(){
    return;
    if(this.params.pageCount <= this.params.pageNo) {
      return;
    }
    this.params.pageNo += 1;
    this.nopayList();
  },
  methods: {
    navTor(id) {
      uni.navigateTo({
        url: '/pages/coupon/coupon-nopay-details/index?id=' + id
      })
    },
    nopayList(callback) {
      nopayList(this.params).then(res=> {
        const { datalist = [] } = res.data;
        const list = datalist.map(item => {
          const { couponPicUrl } = item
          item.couponPicUrl = couponPicUrl && couponPicUrl.split(',')[0];
          return item;
        });
        this.list = [...this.list, ...list];
        this.params.pageCount = res.data.pageCount;
        callback && callback(list)
      })
    },
    cancel(id) {
      const that = this;
      uni.showModal({
        title: '提示',
        content: '确定取消该订单?',
        success(res) {
          if (res.confirm) {
            nopayCancel({ orderId: id}).then(res => {
              uni.showModal({
                title: '提示',
                content: '取消成功',
                icon: 'none',
                cancelText: '继续领券',
                confirmText: '回首页',
                success(res) {
                  if (res.confirm) {
                    uni.switchTab({
                      url: '/pages/home/<USER>'
                    })
                  } else if (res.cancel) {
                    navCoupon('pages/coupon/coupon-centre/index')
                  }
                }
              })
            })
          }
        }
      })
    },
    btnCoupon(id) {
      if(this.loading) {
        return;
      }
      this.loading = true;
      uni.showLoading({
        title: '领取中.....',
        mask: true
      })
      payorder({ orderId: id }).then(res => {
        this.toastHide = true;
        this.payParams = {};
        this.payParams = res.data.payRequest
      })
    },
    payOrder() {
      uni.showLoading({
        title: '支付中',
        mask: true
      });
      this.$refs.pay?.payOrder(this.payParams)
    },
    // 支付成功的回调
    successBack() {
      const that = this;
      uni.showModal({
        title: '提示',
        content: '支付成功',
        icon: 'none',
        cancelText: '继续领券',
        confirmText: '去使用',
        success(res) {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/coupon/coupon-user-list/index'
            })
            that.toastClose();
          } else if (res.cancel) {
            that.toastClose();
            navCoupon('pages/coupon/coupon-centre/index');
          }
        }
      })
    },
    // 银行卡刷新
    updataBankList() {
      this.$refs.pay?.getBankList()
    },
    // 支付失败的回调
    failBack() {
      uni.showToast({
        title: '支付失败',
        icon: 'none'
      })
      // this.toastHide = false;
      this.loading = false;
    },
    toastClose() {
      this.toastHide = false;
      this.loading = false;
    },
    navCheck(index) {
      if(this.navIndex !== +index) {
        this.navIndex = +index;
        this.list = [];
        this.params.pageNo = 1;
        this.params.orderStatus = this.navList[index].value;
        this.nopayList()
      }
    },
    scrollBottom() {
      if(this.params.pageCount <= this.params.pageNo) {
        return;
      }
      this.params.pageNo += 1;
      this.nopayList();
    },
    refrePull() {
      if (this._freshing) return;
      this._freshing = true;
      this.triggered = true;
      this.params.pageNo = 1;
      this.nopayList((data) => {
        this._freshing = false;
        this.triggered = false;
        this.list = data;
      });
    },
    // 复制
    copy(item) {
      uni.setClipboardData({
        data: item.id, //要被复制的内容
        success: () => { //复制成功的回调函数
          uni.showToast({ //提示
            title: '复制成功'
          })
        }
      });
    }
  }
}
</script>

<style scoped>
.heder-nav {
  height: 90upx;
  width: 100vw;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
}
.nav-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.nav-item > i {
  width: 100%;
  height: 4upx;
  background-color: #e53c43;
  position: absolute;
  bottom: 0;
  left: 0;
}
.nav-item > .hover {
  color: #e53c43;
}
.bg-box {
  background-color: #ffffff;
}
.header{
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24upx;
}
.content {
  display: flex;
  justify-content: space-between;
}
.content-price {
  display: flex;
  align-items: center;
}
.content-img {
  width: 200upx;
  height: 200upx;
  border: 1upx solid #ddd;
  border-radius: 10upx;
}
.content-goods {
  flex: 1;
  padding: 10upx 0;
  padding-left: 20upx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.content-title {
  height: 80upx;
  line-height: 40upx;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 这里是超出几行省略 */
  font-size: 28upx;
  margin-bottom: 10upx;
}
.text-coupon-btn {
  border: 1upx solid #b3ae93;
  border-radius: 10upx;
  margin-left: 20upx;
  font-size: 22upx;
  color: #b3ae93;
  width: 80upx;
  height: 40upx;
  line-height: 40upx;
  display: inline-block;
  text-align: center;
}
.couponBtn {
  min-width: 150upx;
  border: 1px solid #b3ae93;
  height: 60upx;
  text-align: center;
  line-height: 60upx;
  border-radius: 30upx;
  color: #b3ae93;
}
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1upx solid #ddd;
}
.loading {
  text-align: center;
  color: #999;
  padding-bottom: 30upx;
  padding-top: 30upx;
}
.toast-pay {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  background-color: #fff;
  padding: 30upx 0 20upx;
  border-radius: 20upx 20upx 0 0;
  z-index: 10;
}
.one-btn {
  width: 570upx;
  margin: 50upx auto 30upx;
  text-align: center;
  line-height: 90upx;
  background: linear-gradient(-90deg, #FFBF00 0%, #FFDD00 100%);
  border-radius: 45upx;
}
.ZZC {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, .5);
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}
.flex-warp-all {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}
.flex1 {
  flex: 1;
  overflow: hidden;
}
</style>
