<template>
  <view>
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
    	<block slot="backText">返回</block>
    	<block slot="content">券转赠</block>
    </cu-custom>
    <view class="give-history flex align-center justify-between padding-right padding-left-lg text-df" :style="{ top: CustomBar + 'px' }">
      <view class="text-bold text-gold">请选择需要转赠的礼券</view>
      <view @click="navTor('history')">历史记录<text class="cuIcon-right margin-left-xs"></text></view>
    </view>
    <view class="list padding-xs" style="margin-bottom: 150rpx;margin-top: 120rpx;">
      <template v-for="(item, index) in list">
        <coupon-offline-user-check :couponUserInfo="item" :key="index" @check="selectCheck">
        </coupon-offline-user-check>
      </template>
      <view :class="'cu-load  ' + (loadmore ? 'loading' : 'over')"></view>
    </view>
    <view class="give-footer flex align-center justify-between padding-lr-xl text-xdf">
      <view>已选择: <text class="padding-right-xs" style="color: #FF0000;" v-if="allValue">￥{{ allValue }}</text> (共<text class="text-bold" style="color: #FF0000;padding-right: 4rpx;">{{ checkList.length }}</text>张)</view>
      <view class="btn" @click="nextBtn">立即赠送</view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import couponOfflineUserCheck from "../components/coupon-offline-user-check/index.vue"
import { getcanpresentlist } from "@/pages/coupon/api/coupon.js"
export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      page: {
      	current: 1,
      	size: 10,
      	//升序字段
      },
      list: [],
      loadmore: true,
      checkList: [],
    }
  },
  onLoad() {
    this.getcanpresentlist()
  },
  onPullDownRefresh() {
    this.page.current = 1;
    this.list = [];
    this.getcanpresentlist(() => {
      uni.stopPullDownRefresh();
    });
  },
  computed: {
    allValue() {
      let value = 0
      this.checkList.map(item => {
        value += +item.amount
      })
      return (+value).toFixed(2)
    }
  },
  components: {
  	couponOfflineUserCheck
  },
  methods: {
    getcanpresentlist(callback) {
      const { current, size } = this.page;
      getcanpresentlist({ pageNo: current, pageSize: size, canPresent: 'Y' }).then(res => {
        const { datalist, pageCount } = res.data;
        this.list = datalist;
        this.page.pageCount = pageCount;
        this.loadmore = false
        callback && callback()
      }).catch(err => {
        this.loadmore = false
      })
    },
    selectCheck(check, info) {
      if(check) {
        this.checkList.push(info);
      } else {
        this.checkList = this.checkList.filter(item => item !== info);
      }
    },
    nextBtn() {
      if(this.checkList.length) {
        uni.navigateTo({
          url: '/pages/coupon/coupon-give-info-load/index'
        })
      }
    },
    navTor() {
      uni.navigateTo({
        url: '/pages/coupon/coupon-give-history/index'
      })
    }
  }
}
</script>

<style>
.give-history {
  margin: 20rpx;
  background-color: #fff;
  height: 80rpx;
  border-radius: 40rpx;
  position: fixed;
  z-index: 2;
  width: 710rpx;
}
.list {
  background-color: #fff;
}
.give-footer {
  position: fixed;
  height: 130rpx;
  width: 100vw;
  bottom: 0;
  left: 0;
  z-index: 1;
  background-color: #fff;
  box-shadow:0 0 20rpx #ddd;
}
.give-footer .btn {
  width: 233rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #FF8463, #FF4E00);
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
}
</style>