<template>
  <view class="coupon-centre">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      id="cuCustom"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">领券中心</block>
    </cu-custom>
    <image src="https://img.songlei.com/-1/material/2c6a4778-47e6-4311-87ba-25c2bbd560a2.png" :style="{top: (CustomBar || 0) - 2 + 'px' }" class="bg-pic" mode="aspectFill"></image>
    <view class="margin-sm">
      <image src="https://img.songlei.com/1/material/a875a3f8-23af-4147-a591-a2246b1a21c0.png" class="radius-lg" style="background-color: #ddd;height: 200upx;width: 100%;" mode="aspectFill"/>
    </view>
    <view class="flex padding-lr-sm align-center justify-between">
      <!-- <view class="coupon-store">为您推荐</view> -->
      <scroll-view
        scroll-x
        enable-flex
        scroll-with-animation
        :scroll-into-view="scollView"
        class="coupon-store-list flex"
        style="justify-content: space-between;">
        <block v-for="(item, index) in options" :key="item.groupCode">
          <view :id="`A${item.groupCode}`" style="padding: 0 30upx;" class="coupon-store" :class="{ 'hover': index === current }" @click="storeBtn(index)">{{item.groupName}}</view>
        </block>
      </scroll-view>
    </view>
    <view class="coupon-order" :class="'bg-' + theme.backgroundColor" @click="navTors">
      <image src="https://img.songlei.com/-1/material/31de3fd5-02b2-47bf-978e-e890da45a1b7.png" mode="aspectFill" style="width: 34upx;height: 34upx;"></image>
      <view style="margin-top: 10upx;">订单</view>
    </view>
    <swiper class="swiper" :current="current" @change="swiperIndex">
      <swiper-item class="swiper-item"  v-for="n in options" :key="n.groupCode">
          <scroll-view
            class="type-goods-list"
            style="padding-top: 20upx;"
            refresher-enabled
            :scroll-y="true"
            :refresher-triggered="triggered"
            @scrolltolower="scrollBottom"
            @refresherrefresh="refrePull"
          >
            <view>
              <block v-for="(item, i) in n.goodsList" :key="i">
                <coupon-centre-goods
                  :goodsInfo = "item"
                  @success="(e) => {handleJump(e, n, i)}"
                />
              </block>
            </view>
            <view class="text-center text-sm padding-top-xs padding-bottom-xs" style="color: #999;">
              {{ n.current >= n.pageCount ? '已经到底了' : '上拉查看更多' }}
            </view>
          </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import { debounce } from '@/utils/util.js'
const app = getApp();
import { getCentreType, getTypeDetail, submitInfo } from "@/pages/coupon/api/coupon.js"
import couponCentreGoods from '../components/coupon-centre-goods/index'
import {
		mapState,
	} from 'vuex'
export default {
  data() {
    return {
      theme: app.globalData.theme,
      options: [], // 整体数据储存
      current: 0,  // 当前门店index
      typeSwiper: [], // 临时充当得数据
      loading: false,
      triggered: false
    }
  },
  components: {
    couponCentreGoods
  },
  computed: {
    ...mapState([
      'CustomBar'
    ]),
    scollView() {
      return `A${this.options[this.current]?.groupCode}`
    }
  },
  mounted() {
    getCentreType().then(res => {
      const { data } = res;
      // this.options = this.transOption(data);
      this.options = this.temporary(data);
      // this.loopUser();
      app.loopUser().then(() => {
        this.storeBtn(0);
      });
    });
    
    this._freshing = false;
  },
  methods: {
    loopUser() {
      const user_info = uni.getStorageSync('user_info');
      if(!user_info) {
        setTimeout(() => {
          this.loopUser();
        }, 0)
        return;
      }
      const { id } = user_info;
      if(!id) {
        uni.navigateTo({
          url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-centre/index')
        })
        return;
      }
      this.storeBtn(0);
    },
    // 临时处理
    temporary(data) {
      const options = this.transOption(data);
      const typeList =  options.filter(item => item.groupCode === '201')
      const typeSwiper = typeList[0].children;
      return typeSwiper.map(item => ({
        ...item,
        groupCode: item.categoryCode,
        groupName: item.categoryName,
        children: [item],
        goodsList: [],
        pageCount: 1,
        current: 1
      }))
    },
    getTypeDetail(item, callback) {
      const { current } = this;
      const { current: pageNo } = this.options[current];
      const params = {
        pageNo,
        position: '01'
      }
      if (item.categoryCode) {
        params.category = item.categoryCode
      }
      getTypeDetail(params).then(res => {
        const goodsList = this.options[this.current].goodsList;
        const dataList = res.data.dataList.map(item => {
          return item;
        })
        this.options[this.current].goodsList = [...(goodsList || []), ...res.data.dataList]
        this.options[this.current].pageCount = res.data.pageCount
        callback && callback()
      });
    },
    // 转化为
    transOption(data) {
      const obj = {};
      const arr = []
      data.map(item => {
        if (obj[item.groupCode]) {
          obj[item.groupCode].push(item);
        } else {
          obj[item.groupCode] = [item];
        }
      });
      for(const i in obj) {
        const item = obj[i];
        const { groupName, groupCode } = item[0];
        arr.push({
          groupName,
          groupCode,
          children: [{
            categoryCode: '',
            categoryName: '全部'
          }, ...item],	
          goodsList: [],
          pageCount: 1,
          current: 1
        })
      }
      return arr
    },
    storeBtn(index) {
      this.current = index;
      this.options = this.options.map((item, indexs) => {
        item.hover = false;
        if (indexs === index) {
          item.hover = true;
          if(!item.isShow) {
            item.isShow = true;
            this.typeSelect(item.children[0]);
          }
        }
        return item;
      })
    },
    swiperIndex(e) {
      const { current } = e.detail;
      this.storeBtn(current);
    },
    typeSelect(currentItem) {
      const arr = this.options[this.current].children;
      this.options[this.current].current = 1;
      this.options[this.current].children = arr.map(item => {
        item.hover = false;
        if(item === currentItem) {
          item.hover = true;
          this.options[this.current].goodsList = []
          this.getTypeDetail(item);
        }
        return item;
      })
    },
    handleJump(e, n, i) {
      const { eventTypeCode, eventId } = e;
      if(this.loading) {
        return;
      }
      this.loading = true;
      // 购买
      if (+eventTypeCode === 1 ||  +eventTypeCode === 4 || +eventTypeCode === 5) {
        uni.navigateTo({
          url: '/pages/coupon/coupon-centre-details/index?id=' + eventId
        })
        this.loading = false;
      } else {
        this.submitInfoClick(e)
      }
    },

    submitInfoClick(e){
      const { eventTypeCode, eventId, eventPicUrl } = e;
      const params = {
        eventId
      }
      uni.showLoading({
        title: '领取中'
      })
      submitInfo(params).then(res => {
        uni.hideLoading()
        this.loading = false;
        if (+res.code === 0) {
          uni.showModal({
            title: '提示',
            content: +eventTypeCode === 3 ? '领取成功' : '抢券成功',
            icon: 'none',
            cancelText: '继续领券',
            confirmText: '去使用',
            success(res) {
              if (res.confirm) {
                uni.navigateTo({
                  url: eventPicUrl || '/pages/coupon/coupon-user-list/index?navTabCur=1'
                })
              } else if (res.cancel) {
                console.log('用户点击取消')
              }
            }
          })
          this.statusChange(eventId);
        } else if(+res.code === 90001) {
          uni.showModal({
            title: '提示',
            content: (res && res.msg) || '有未支付订单，取消后在支付',
            showCancel: false,
            success() {
              uni.navigateTo({
                url: '/pages/coupon/coupon-nopay-list/index'
              })
            }
          })
        }
      }).catch(err => {
        // console.log(err)
        uni.hideLoading();
        this.loading = false;
      })
    },
    // 领取回调显示
    statusChange(id) {
      const { current } = this;
      const { goodsList } = this.options[current]
      this.options[current].goodsList = goodsList.map(item => {
        if(item.eventId === id) {
          item.status = 'A'
        }
        return item;
      })
    },
    scrollBottom() {
      const { current } = this;
      const { children, current: index, pageCount } = this.options[current];
      if(+index === +pageCount) {
        return;
      }
      const item = children.find(item => item.hover);
      this.options[current].current += 1;
      this.getTypeDetail(item);
    },
    navTors() {
      uni.navigateTo({
        url: '/pages/coupon/coupon-nopay-list/index'
      })
    },
    refrePull() {
      if (this._freshing) return;
      this._freshing = true;
      this.triggered = true;
      const { current } = this;
      const { children, current: index, pageCount } = this.options[current];
      const item = children.find(item => item.hover);
      this.options[current].goodsList = [];
      this.options[current].current = 1;
      this.getTypeDetail(item, () => {
        this.triggered = false;
        this._freshing = false;
      });
    },
  }
}
</script>

<style scoped>
.coupon-order {
  position: absolute;
  left: 0;
  bottom: 300upx;
  width: 100upx;
  height: 100upx;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 24upx;
}
.coupon-centre {
  display: flex;
  flex-direction: column;
  position: fixed;
  width: 100vw;
  height: 100vh;
}
.coupon-type {
  width: 173upx;
  height: 50upx;
  background: #fff;
  border-radius: 25upx;
  text-align: center;
  line-height: 50upx;
  color: #646464;
  font-size: 24upx;
  margin-right: 8upx;
  display: inline-block;
}
.coupon-type.hover {
  color: #fff;
  background: #CDAC90;
}
.coupon-store-list {
  flex: 1;
  white-space: nowrap;
  overflow: auto;
  height: 50upx;
}
.coupon-store {
  text-align: center;
  font-size: 26upx;
  color: #191919;
  height: 50upx;
  line-height: 40upx;
  padding: 0 20upx;
  display: inline-block;
}
.coupon-store.hover {
  font-weight: bold;
  position: relative;
}
.coupon-store.hover::after {
  content: '';
  height: 5upx;
  width: 109upx;
  background-color: #CDAD90;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.bg-fff {
  background-color: #ffffff;
}
.scroll-view {
  width: 100%;
  height: 154upx;
  display: inline-block;
  white-space: nowrap;
}
.swiper {
  flex: 1;
}
.swiper-item {
  height: 100%;
}
.bg-pic{
  position: absolute;
  left: 0;
  top: 119upx;
  width: 100vw;
  height: 170upx;
  z-index: -1;
}
.store-type {
  position: absolute;
  width: 100vw;
  z-index: 1;
}
.type-goods-list {
  padding-top: 80upx;
  height: 100%;
}
</style>