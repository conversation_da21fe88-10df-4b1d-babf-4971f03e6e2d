<template>
  <view>
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
      <block slot="backText">返回</block>
      <block slot="content">转赠详情</block>
    </cu-custom>
    <view class="coupon-info margin padding-tb-lg padding-lr-sm bg-white">
      <view class="flex padding-bottom">
        <view class="margin-right-sm" style="width: 110rpx;height: 110rpx;">
          <image :src="giveInfo.presentHeadUrl"
            style="width: 100%;height: 100%;background-color: #ddd;border-radius: 50%;"></image>
        </view>
        <view class="flex-sub flex flex-direction">
          <view class="text-df"><text style="color: #D79F76;">{{ giveInfo.presentNickname }}</text> 赠送礼品券快去领取吧~</view>
          <view class="flex flex-sub align-center">
            <view v-if="giveInfo.presentMode === '1'" class="coupon-icon text-xsm margin-right-sm">拼手气</view>
            <view
              style="background-image: url('https://img.songlei.com/-1/material/9715966a-0270-43f4-a839-b5ba9e8ec775.png');"
              v-if="giveInfo.presentMode === '2'" class="coupon-icon text-xsm">普通送</view>
          </view>
        </view>
      </view>
      <view class="text-df padding-lr-sm padding-tb-sm" style="color: #D79F76;border-top: 1rpx dashed #ddd;">
        {{ giveInfo.presentDesc }}
      </view>
      <view class="bg-video">
        <view class="flex flex-direction align-center justify-center"
          style="height: 254rpx;background: url('https://img.songlei.com/-1/material/bb885964-709b-4673-8505-25c680a8e74c.png') no-repeat;background-size: 100% 100%;padding: 40rpx 50rpx;">
          <template v-if="detailsValueList.length === 1">
            <view class="color-red">
              <text class="text-xdf">￥</text>
              <text class="text-xsl line-height-1 text-bold" style="vertical-align: top;">{{
                detailsValueList[0].faceValue }}</text>
            </view>
            <view class="text-bold text-xl">{{ detailsValueList[0].typeName }}</view>
            <view class="text-df" style="color: #767676;">{{ detailsValueList[0].simpleUsageRule }}</view>
          </template>
          <template v-else>
            <view class="color-red">
              <text class="text-xdf">￥</text>
              <text class="text-xsl line-height-1 text-bold" style="vertical-align: top;">{{ allValue }}</text>
            </view>
            <view class="text-xl" style="color: #000000;">(共<text class="color-red">{{ detailsValueList.length
                }}</text>张券)</view>
          </template>
        </view>
        <view v-if="detailsValueList.length === 1" class="text-df text-center margin-top-sm" style="color: #767676;">
          有效期: {{
            detailsValueList[0].expDate }}</view>
      </view>
    </view>
    <view class="coupon-info margin padding bg-white" style="color: #000000;">
      <view class="flex align-center justify-between text-xdf padding-bottom-sm"
        style="border-bottom: 1rpx solid #ddd;">
        <view class="padding-left-xs">礼品券{{ giveInfo.couponGetNum }}/{{ giveInfo.couponTotalNum }}</view>
        <view>{{ statusParmes[giveInfo.status] }}</view>
      </view>
      <template v-if="giveInfo.couponGetNum != 0">
        <template v-for="(item, index) in detailsValueList">
          <view class="flex padding-top-sm align-start" v-if="item.receiveNickName">
            <view class="margin-right-sm margin-top-xs" style="width: 74rpx;height: 74rpx;border-radius: 50%;">
              <image :src="item.receiveHeadUrl"
                style="width: 100%;height: 100%;border-radius: 50%;background-color: #ddd;"></image>
            </view>
            <view class="flex-sub padding-bottom" style="border-bottom: 1rpx solid #f1f1f1;">
              <view class="flex">
                <view class="flex-sub">{{ item.receiveNickName }}</view>
                <view class="">{{ item.typeName }}</view>
              </view>
              <view class="flex" style="color: #9A9A9A;">
                <view class="flex-sub">{{ item.createDate }}</view>
                <view class="">【松鼠商城】</view>
              </view>
            </view>
          </view>
        </template>
      </template>
      <template v-else>
        <view class="flex justify-center padding-top" style="color: #999999;">暂无人领取~</view>
      </template>
    </view>
    <view class="flex justify-between margin" v-if="giveInfo.custId === userInfo.erpCid && giveInfo.status === '2'">
      <view class="btn text-xdf" style="border: 1rpx solid #BFBFBF;" @click="cancelBtn">取消转赠</view>
      <button class="btn text-xdf" style="color: #ffffff;background: linear-gradient(-90deg, #FF4E00, #FF8463);"
        open-type="share">立即分享</button>
    </view>
    <view style="height: 20rpx;"></view>
  </view>
</template>

<script>
const app = getApp()
import { getpresentorder, receivepresentcoupon, cancelpresentbill } from "@/pages/coupon/api/coupon-give.js"

export default {
  data () {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      check: 1,
      showZZC: false,
      checkList: [],
      presentDesc: '',
      giveInfo: {},
      detailsValueList: [],
      userInfo: {},
      statusParmes: {
        '1': '已领完',
        '2': '领取中',
        '3': '已取消'
      }
    }
  },
  computed: {
    allValue () {
      let value = 0
      this.detailsValueList.map(item => {
        value += +item.faceValue
      })
      return (+value).toFixed(2)
    }
  },
  onLoad (options) {
    if (options.id) {
      this.id = options.id
      this.loopUser()
    }
  },
  onShareAppMessage () {
    return {
      title: this.giveInfo.presentDesc,
      path: '/pages/coupon/coupon-give-receive/index?id=' + this.id,
      imageUrl: 'https://img.songlei.com/-1/material/fc67cabb-a3a3-4476-9cd2-f6f40966fa21.png'
    }
  },
  onPullDownRefresh () {
    this.getActiveDetails(() => {
      uni.stopPullDownRefresh()
    })
  },
  methods: {
    loopUser () {
      const that = this
      const user_info = uni.getStorageSync('user_info')
      if (!user_info) {
        setTimeout(() => {
          this.loopUser()
        }, 0)
        return
      }
      const { id, erpCid } = user_info
      this.userInfo = user_info
      if (!id) {
        uni.navigateTo({
          url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-give-details/index?id=' + this.id)
        })
        return
      }
      if (!erpCid) {
        app.doLogin().then(res => {
          if (res === 'success') {
            const user = uni.getStorageSync('user_info')
            if (user.erpCid) {
              this.getActiveDetails()
            } else {
              uni.showModal({
                title: '提示',
                content: '登录错误',
                showCancel: false,
                success () {
                  uni.navigateTo({
                    url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-give-details/index?id=' + this.id)
                  })
                }
              })
            }
          } else {
            uni.navigateTo({
              url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-give-details/index?id=' + this.id)
            })
          }
        })
        return
      }
      this.getActiveDetails()
      // 领取券
      // this.receivepresentcoupon();
    },
    getActiveDetails (callback) {
      getpresentorder({ id: this.id, custId: '' }).then(res => {
        this.giveInfo = res.data
        this.detailsValueList = res.data.details
        callback && callback()
      })
    },
    receivepresentcoupon () {
      receivepresentcoupon({ orderId: this.id }).then(res => {
        console.log(res)
      })
    },
    cancelBtn () {
      cancelpresentbill({ id: this.id }).then(res => {
        uni.showModal({
          title: '提示',
          content: '取消成功',
          showCancel: false,
          success () {
            uni.navigateBack({
              delta: 1,
              success () {

              },
              fail () {
                uni.switchTab({
                  url: '/pages/home/<USER>'
                })
              }
            })
          }
        })
      })
    }
  }
}
</script>

<style>
.coupon-info {
  border-radius: 40rpx;
}

.coupon-icon {
  width: 136rpx;
  height: 45rpx;
  line-height: 45rpx;
  text-align: center;
  color: #ffffff;
  background-image: url('https://img.songlei.com/-1/material/f3587399-6b21-488d-9cf8-2118041b9cb5.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.color-red {
  color: #FF0000;
}

.btn {
  width: 340rpx;
  height: 80rpx;
  background-color: #ffffff;
  color: #090908;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
}

.bg-zzc {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.7);
}
</style>