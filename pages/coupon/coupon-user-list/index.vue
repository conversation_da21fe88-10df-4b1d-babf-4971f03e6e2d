<template>
	<view class="flex flex-direction" style="height: 100vh;">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">我的优惠券</block>
		</cu-custom>

		<!-- 导航按钮 -->
		<!-- 一级新方式 -->
		<scroll-view :scroll-into-view="'A' + type" scroll-with-animation scroll-x>
			<view class="bg-white collection-types text-center flex"
				:class="couponArr.length > 4 ? '' : 'justify-between'" enable-flex
				style="box-shadow: none;z-index: 1;height: 90rpx;background-color: #eee;">
				<view :id="'A' + item.key"
					:class="'btn ' + (item.key == type ? 'cur bg-' + theme.backgroundColor : '') + ' ' + (couponArr.length > 4 ? 'min-height-nav' : '')"
					v-for="(item, index) in couponArr" :key="index" @tap="navTabSelect" :data-index="index"
					:data-key="item.key" :data-value="item.value">{{ item.value }}</view>
			</view>
		</scroll-view>

		<!-- 二级新方式 -->
		<template>
			<template v-for="(items, index) in couponArr">
				<scroll-view :key="index" v-if="items.key == type" scroll-x
					class="bg-white nav scroll-nav coupon-list collection-types">
					<view class="flex text-center">
						<view
							:class="'cu-item padding-tb-sm ' + (index == twoTabCur ? 'cur text-' + theme.themeColor : '')"
							v-for="(item, index) in items.children" :key="index" @tap="tabSelect" :data-index="index"
							:data-key="item.key" :data-marketCategory="item.marketCategory || ''"
							:data-value="item.value">
							<text>{{ item.value }}</text>
							<view></view>
						</view>
					</view>
				</scroll-view>
			</template>
		</template>
		<!--  -->
		<template v-if="type == '2'">
			<view class="flex justify-between bg-white align-center">
				<view class="flex  align-center" style="margin-left: 20rpx;">
					<!-- <text @click="handleToPage">已使用</text> ｜ -->
					<navigator class="margin-right-sm" url='/pages/coupon/coupon-offline-gebraucht/index?id=02,07'
						hover-class="none">已使用</navigator> |
					<navigator class="margin-sm" url='/pages/coupon/coupon-offline-gebraucht/index?id=03'
						hover-class="none">已过期</navigator> |
					<navigator class="margin-left-sm" url='/pages/coupon/coupon-offline-gebraucht/index?id=04'
						hover-class="none">礼金</navigator>
				</view>
				<view class="flex align-center" style="margin-right: 20rpx;">
					<input :value="oldScreeParams.keyword" type="text"
						class="search-input flex-sub padding-lr padding-tb-xs text-xsm"
						style="width: 240rpx; height: 54rpx; padding-top: 0; padding-bottom: 0;" placeholder="搜索我的优惠券"
						@input="changeSearchInput" @confirm="searchConfirm" />
					<view class="margin-left-sm" @click="showScreen">筛选</view>
				</view>
			</view>

		</template>


		<!-- list页面 -->
		<scroll-view scroll-y refresher-enabled class="flex-sub" :refresher-triggered="triggered"
			@scrolltolower="reachBottom" @refresherrefresh="refresher" style="overflow: hidden;">
			<view class="cu-list padding-bottom-sm">
				<view v-for="(item, i) in couponArr" :key="'A' + item.key">
					<view v-for="(items, j) in item.children" :key="'B' + items.key">
						<template v-if="item.key == type && j == twoTabCur">
							<template v-if="item.key == '1'">
								<view class="cu-item padding-xs bg-white" style="margin-bottom: 10upx;"
									v-for="(item, index) in items.list" :key="index">
									<coupon-user-info showNum :couponUserInfo="item"></coupon-user-info>
								</view>
							</template>
							<template v-else-if="item.key == '2'">
								<template v-if="items.key == '04'">
									<view class="cu-item padding-xs bg-white " style="margin-bottom: 10upx;"
										v-for="(item, index) in items.list" :key="index">
										<coupon-infos-list :couponUserInfo="item"></coupon-infos-list>
									</view>
								</template>
								<template v-else-if="items.key == '01,05,08'">
									<view class="cu-item padding-xs bg-white">
										<view v-if="bannerObj && bannerObj.linkUrl" hover-class="none"
											:url="bannerObj.linkUrl" @click="handleBanner(bannerObj.linkUrl)" style="
											  background-color: #ffffff;
											  padding: 0 20rpx 0 20rpx;
											  position: relative;
											">
											<image mode="widthFix"
												style="width: 100%; height: 280rpx;border-radius: 20rpx;"
												:src="bannerObj.imgUrl"></image>
										</view>
										<template v-for="(item, index) in items.list">
											<view :key="index">
												<view
													style="display: flex; space-between; height:60upx; line-height: 60upx; border: 1upx solid #cdaa8e; border-radius: 20rpx; text-align: center; font-size: 30upx; color: #cdaa8e; margin-bottom: 20upx;"
													:style="item.display ? 'color: #cdaa8e; background: #fff' : 'color: #fff; background: #cdaa8e'"
													@tap.stop="handleIsShowList" :data-index="index">
													<text style="flex: 6">{{ item.title }}</text>
													<text
														:class="item.display ? 'cuIcon-triangledownfill' : 'cuIcon-triangleupfill'"
														style="flex:1; font-size: 30upx;"
														:style="{color: item.display ? '#cdaa8e' : '#fff'}"></text>
												</view>
												<view v-show="item.display">
													<view v-for="(mount, mountIndex) in item.arr">
														<coupon-offline-user-info :couponUserInfo="mount"
															:key="mountIndex"></coupon-offline-user-info>
													</view>
												</view>
											</view>
										</template>
									</view>
								</template>
								<template v-else>
									<view class="cu-item padding-xs bg-white">
										<view v-if="bannerObj && bannerObj.linkUrl" hover-class="none"
											:url="bannerObj.linkUrl" @click="handleBanner(bannerObj.linkUrl)" style="
											  background-color: #ffffff;
											  padding: 0 20rpx 0 20rpx;
											  position: relative;
											">
											<image mode="widthFix"
												style="width: 100%; height: 280rpx;border-radius: 20rpx;"
												:src="bannerObj.imgUrl"></image>
										</view>
										<template v-for="(item, index) in items.list">
											<coupon-offline-user-info :couponUserInfo="item" :key="index">
											</coupon-offline-user-info>
										</template>
									</view>
								</template>
							</template>
							<template v-else style="z-index: 9999;">
								<view class="search flex justify-between align-center padding-xs">
									<input :value="oldScreeParams.keyword" type="text"
										class="search-input flex-sub padding-lr padding-tb-xs text-xsm"
										placeholder="搜索我的优惠券" @input="changeSearchInput" @confirm="searchConfirm" />
									<view class="margin-left-sm" @click="showScreen">筛选</view>
								</view>
								<view class="cu-item padding-xs bg-white">
									<view v-if="bannerObj && bannerObj.linkUrl" hover-class="none"
										:url="bannerObj.linkUrl" @click="handleBanner(bannerObj.linkUrl)" style="
										  background-color: #ffffff;
										  padding: 0 20rpx 0 20rpx;
										  position: relative;
										">
										<image mode="widthFix" style="width: 100%; height: 280rpx;border-radius: 20rpx;"
											:src="bannerObj.imgUrl"></image>
									</view>
									<template v-for="(item, index) in items.list">
										<coupon-offline-user-info :couponUserInfo="item" :key="index">
										</coupon-offline-user-info>
									</template>
								</view>
							</template>

							<template v-if="type == 2">
								<view v-if="couponLoading && loadmore" class="cu-load  loading"></view>
								<view v-if="!couponLoading && !loadmore" class="cu-load  over"></view>
							</template>

							<template v-else>
								<view :class="'cu-load  ' + (loadmore ? 'loading' : 'over')"></view>
							</template>

						</template>
					</view>
				</view>
			</view>
		</scroll-view>


		<view class="position-label">
			<view v-if="type === '2'" class="coupon-order" :class="'bg-' + theme.backgroundColor" @click="moreTypeBtn">
				<view class="lines"></view>
				<view class="lines"></view>
				<view class="lines" style="margin-bottom: 0;"></view>
				<i v-if="couponGiveHover === 1" class="coupon-hover"></i>
			</view>
			<view v-if="type === '2' && showZZC" class="coupon-item text-df" :class="'bg-' + theme.backgroundColor"
				@click="navTors" :style="{ top: showLabels ? '-110rpx': '8rpx', right: showLabels ? '80rpx': '15rpx' }">
				激活
			</view>
			<view v-if="type === '2' && showZZC" class="coupon-item text-df" :class="'bg-' + theme.backgroundColor"
				:style="{ top: showLabels ? '5rpx': '8rpx', right: showLabels ? '150rpx': '15rpx' }"
				@click="navTors(1)">
				转赠
				<i v-if="couponGiveHover === 1" class="coupon-hover"></i>
			</view>
			<!-- <view v-if="type === '2' && showZZC" class="coupon-item text-df" :class="'bg-' + theme.backgroundColor"
				@click="navTors(2)" :style="{ top: showLabels ? '125rpx': '8rpx', right: showLabels ? '120rpx': '15rpx' }">
				<view class="text-center" style="width: 80rpx;word-break: break-all;line-height: 35rpx;">
					抖音
					兑换
				</view>
			</view> -->
		</view>

		<view class="position-label" style="bottom: 420rpx;z-index: 3;" v-if="type == '2'">
			<view class="coupon-order"
				:style="{ 'background': 'url(https://img.songlei.com/coupon/tiktok.gif) no-repeat', 'backgroundSize': '100% 100%' }"
				@click="couponNav(2)">
			</view>
		</view>

		<view class="position-label" style="bottom: 180rpx;z-index: 3;" v-if="type !== '1'">
			<view class="coupon-order"
				:style="{ 'background': 'url(http://slshop-file.oss-cn-beijing.aliyuncs.com/client/mp/coupon-center.gif) no-repeat', 'backgroundSize': '100% 100%' }"
				@click="couponNav">
			</view>
		</view>

		<view class="dialog-search padding oldScreenAnimate" v-if="showScreenZZC"
			:class="showScreenAnimate ? 'screenAnimate' : ''">
			<view class="dialog-title text-lg text-bold padding-bottom-sm" style="line-height: 40rpx;">筛选</view>
			<view style="max-height: 60vh;overflow: auto;">
				<view class="dialog-content" v-for="item in screen">
					<view class="text-bold text-df">{{ item.name }}</view>
					<view class="margin-tb-sm justify-between flex flex-wrap dialog-type">
						<template v-if="item.children&&item.children.length>6">
							<template v-if="item.showAll">
								<template v-for="items in item.children">
									<view class="margin-bottom-sm margin-right-sm dialog-item text-df"
										:class="`${screenParams[item.value] || ''}` == items.value ? 'hover' : '' "
										@click="screeClick(item.value, items.value)">{{ items.name }}</view>
								</template>
							</template>
							<template v-else>
								<template v-for="items in  item.children.slice(0,7) ">
									<view class="margin-bottom-sm margin-right-sm dialog-item text-df"
										:class="`${screenParams[item.value] || ''}` == items.value ? 'hover' : '' "
										@click="screeClick(item.value, items.value)">{{ items.name }}</view>
								</template>
								<view>查看更多</view>
							</template>
						</template>
						<template v-else>
							<template v-for="items in item.children">
								<view class="margin-bottom-sm margin-right-sm dialog-item text-df"
									:class="`${screenParams[item.value] || ''}` == items.value ? 'hover' : '' "
									@click="screeClick(item.value, items.value)">{{ items.name }}</view>
							</template>
						</template>

					</view>
				</view>
			</view>
			<view class="dialog-footer flex justify-center align-center padding">
				<view class="dialog-bottom" @click="resotre"
					style="border-top-left-radius: 35rpx;border-bottom-left-radius: 35rpx;background-color: rgba(244, 213, 163, 1);">
					重置</view>
				<view class="dialog-bottom" @click="completeSreach"
					style="border-top-right-radius: 35rpx;border-bottom-right-radius: 35rpx;">完成</view>
			</view>
		</view>
		<view class="ZZC" v-if="showZZC" @click="close"></view>
	</view>
</template>
<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import couponUserInfo from "components/coupon-user-info/index";
	import couponOfflineUserInfo from "../components/coupon-offline-user-info/index"
	import couponInfosList from "../components/coupon-info-list/index"
    import { getCurrentTitle, senTrack } from '@/public/js_sdk/sensors/utils.js';
    
	import {
		getpopevtscd,
		getEvtscdList,
		vipTicket
	} from "@/pages/coupon/api/coupon.js"
	import {
		getpresentnoticeqty,
		readpresentnotice
	} from "@/pages/coupon/api/coupon-give.js"
	import {
		getByType
	} from "@/api/base.js"
	import {
		mapState
	} from 'vuex'
	import {
		gotoPage
	} from "@/components/div-components/div-base/div-page-urls.js";

	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				twoTabCur: 0, // 二级标签的index
				twoStatus: '', // 二级标签下得key
				initNav: '', // 优惠券初始时的进来的参数
				initKey: '', // 初始化key值
				type: '1', //一级券type
				searchKeyword: '',
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				loadmore: true, // 判断是否还有下一页
				couponUserList: [], // 我的劵列表
				Unlock: true, //判断首次进入
				showZZC: false, // 遮罩层
				showScreenZZC: false, // 筛选遮罩层
				showScreenAnimate: false, // 筛选动画
				showLabels: false, // 转赠动画
				bannerObj: null,
				// 转赠基础选项
				screen: [{
					name: '类目',
					value: "marketCategory",
					showAll: false,
					children: [{
							name: '全部类目',
							value: ''
						},
						{
							name: '百货',
							value: '01'
						},
						{
							name: '美妆',
							value: '02'
						},
						{
							name: '超市',
							value: '03'
						},
						{
							name: '餐饮',
							value: '04'
						},
						{
							name: '电影',
							value: '05'
						}, {
							name: '女装',
							value: '06'
						},
						{
							name: '礼品',
							value: '07'
						},
						{
							name: '黄金珠宝',
							value: '08'
						},
						{
							name: '运动',
							value: '09'
						},
						{
							name: '鞋品',
							value: '10'
						}, {
							name: '儿童',
							value: '11'
						},
						{
							name: '停车',
							value: '12'
						}, {
							name: '内衣',
							value: '13'
						},
						{
							name: '潮品',
							value: '14'
						},
						{
							name: '男装',
							value: '15'
						},
						{
							name: '权益',
							value: '16'
						}, {
							name: '品牌',
							value: '17'
						}, {
							name: '精品',
							value: '18'
						},
					]
				}, {
					name: '类型',
					value: 'useType',
					showAll: false,
					children: [{
							name: '全部类型',
							value: ''
						},
						{
							name: '满减券',
							value: '000001'
						},
						{
							name: '折扣券',
							value: '000003'
						},
						{
							name: '礼品券',
							value: '000002'
						},
						{
							name: '停车券',
							value: '000004'
						},
						{
							name: '运费券',
							value: '000007'
						},
						{
							name: '红包',
							value: '000005'
						},
					]
				}, {
					name: '门店',
					value: 'marketStore',
					showAll: false,
					children: [{
							name: '全部门店',
							value: ''
						},
						{
							name: '线下南岗店',
							value: '201'
						},
						{
							name: '线下香坊店',
							value: '203'
						},
						{
							name: '线上商城',
							value: '288'
						}
					]
				}, {
					name: '排序',
					value: 'orderFields',
					showAll: false,
					children: [{
							name: '综合排序',
							value: ''
						},
						{
							name: '面值大到小',
							value: 'face_value'
						},
						{
							name: '新到优先',
							value: 'issue_date'
						},
						{
							name: '快到期优先',
							value: 'exp_date'
						}
					]
				}],
				screenParams: {}, // 筛选参数
				oldScreeParams: {}, // 筛选里面参数
				triggered: false, // 下拉加载
				couponGiveHover: 0, // 赠送未读标志
				// 优惠券所有数据
				couponArr: [{
					value: '线上劵',
					key: '1',
					twoTabCur: 0,
					children: [{
						value: '新到',
						key: '-1',
						params: {},
						list: []
					}, {
						value: '全部',
						key: '0',
						params: {},
						list: [],
					}, {
						value: '已使用',
						key: '1',
						params: {},
						list: [],
					}, {
						value: '已过期',
						key: '2',
						params: {},
						list: [],
					}]
				}, {
					value: '线下劵',
					key: '2',
					twoTabCur: 0,
					children: [{
							value: '全部',
							key: '01,05,08',
							params: {},
							list: [],
						},
						// {
						// 	value: '已使用',
						// 	key: '02,07',
						// 	params: {},
						// 	list: [],
						// }, {
						// 	value: '已过期',
						// 	key: '03',
						// 	params: {},
						// 	list: [],
						// }, 
						// {
						// 	value: '礼金',
						// 	key: '04',
						// 	params: {},
						// 	list: [],
						// },
						{
							value: '百货',
							key: '01,05,08',
							marketCategory: '01',
							params: {},
							list: [],
						},
						{
							value: '美妆',
							key: '01,05,08',
							marketCategory: '02',
							params: {},
							list: [],
						},
						{
							value: '超市',
							key: '01,05,08',
							marketCategory: '03',
							params: {},
							list: [],
						},
						{
							value: '餐饮',
							key: '01,05,08',
							marketCategory: '04',
							params: {},
							list: [],
						},
						{
							value: '电影',
							key: '01,05,08',
							marketCategory: '05',
							params: {},
							list: [],
						},
					]
				}, {
					value: '停车劵',
					key: '3',
					twoTabCur: 0,
					children: [{
						value: '未使用',
						key: '01,05',
						params: {},
						list: [],
					}, {
						value: '已使用',
						key: '02,07',
						params: {},
						list: [],
					}, {
						value: '已失效',
						key: '03',
						params: {},
						list: [],
					}]
				}],
				couponLoading: false
			};
		},
		components: {
			couponUserInfo,
			couponOfflineUserInfo,
			couponInfosList
		},
		computed: {
			...mapState([
				'CustomBar',
			]),
			collectTypeWidth() {
				const length = this.couponArr.length;
				if (length <= 4) {
					return '750rpx'
				} else {
					return 187.5 * length + 'rpx'
				}
			}
		},
		onLoad(options) {
			if (!app.isLogin()) {
				return;
			}
			app.initPage().then(res => {
				if (this.Unlock) {
					this.loadmore = true;
					//外联查看我的线下优惠劵
					if (options.navTabCur || 0 === options.navTabCur) {
						this.initNav = `${options.navTabCur}`;
					} else if (options.scene) {
						const qrCodeScene = decodeURIComponent(options.scene);
						if (qrCodeScene) {
							//接受二维码中参数  参数sf=XXX&id=XXX
							const qrCodeSceneArray = qrCodeScene.split('&');
							const navTabCur = util.UrlParamHash(qrCodeScene, 'navTabCur');
							const key = util.UrlParamHash(qrCodeScene, 'key');
							if (navTabCur) {
								this.initNav = `${navTabCur}`;
							}
							if (key) {
								this.initKey = key;
							}
						}
					} else if (options.key) {
						this.initKey = options.key
					}
					this.initFn();
					this.advertisement();
				}
			});
			this.getByType();
			getpresentnoticeqty().then(res => {
				this.couponGiveHover = +res.data;
			})
		},
		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.oldScreeParams = {};
			this.setDatas([], true);
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		methods: {
			
			handleBanner(page) {
				gotoPage(page);
			},
			advertisement(id) {
				api.advertisementinfo({
					bigClass: 'MY_COUPON'
				}).then(res => {
					this.bannerObj = res.data;
				});
			},
			reachBottom() {
				if (this.loadmore) {
					this.page.current = this.page.current + 1;
					this.couponUserPage();
				}
			},
			refresher() {
				if (this._freshing) return;
				this._freshing = true;
				this.triggered = true;
				this.setDatas([], true);
				this.loadmore = true;
				this.page.current = 1;
				this.couponUserPage(() => {
					this.triggered = false;
					this._freshing = false;
				})
			},
			// 初始化函数
			initFn() {
				// 查看是否有活动，先来一个默认
				let length = 0;
				getpopevtscd().then(res => {
					const {
						code,
						data = []
					} = res;
					if (data.length) {
						data.forEach(item => {
							this.couponArr.unshift({
								value: item.evtscdName,
								key: item.evtscdId,
								twoTabCur: 0,
								children: []
							})
							this.couponArr[0].children = [{
									value: '全部',
									key: '',
									params: {},
									list: []
								},
								...item.categorys.map(items => ({
									value: items.name,
									key: items.code,
									params: {},
									list: []
								}))
							]
						})
						length = data.length;
					}
				}).finally(err => {
					// 看看有没有活动，是不是固定跳转,暂时不改
					if (length && !this.initKey && !this.initNav) {
						// this.getEvtscdList(length)
						const {
							couponArr
						} = this;
						const evtscd = couponArr[0].key;
						this.type = evtscd;
						this.activeCoupon({
							evtscd,
							marketCategory: ''
						}, () => {
							if (this.getDates().length) {
								this.type = evtscd;
							} else {
								this.initFn1(length)
							}
						}, () => {
							this.initFn1(length)
						})
					} else {
						this.initFn1(length)
					}
				})
			},

			initFn1(length) {
				// 判断数据
				const {
					initKey
				} = this;
				if (initKey) {
					// 如果有值的话，通过key值查询
					const find = this.couponArr.find(item => {
						if (item.key === initKey) {
							return true;
						}
						return false
					})
					if (find) {
						this.type = initKey;
						this.couponUserPage()
						return;
					}
				}
				if (this.initNav || 0 === this.initNav) {
					this.type = this.couponArr[+this.initNav + +length]?.key;
					this.twoStatus = this.getNavData().key
					this.couponUserPage();
					return;
				}
				this.initNav = this.initNav || 1;
				this.type = this.couponArr[+this.initNav + +length]?.key;
				this.jugeCont()
			},
			
			
			// 内容判断当前是否跳转
			async jugeCont() {
				if (+this.type === 1 && +this.twoTabCur === 0) {
					const resA = await api.couponUserPage(Object.assign({}, this.page, {
						status: this.getNavData().key,
						group: '1'
					}))
					let couponUserListA = resA.data.records;
					this.setDatas(couponUserListA);
					if (couponUserListA.length) {
						if (couponUserListA.length < this.page.size) {
							this.loadmore = false;
						}
					} else {
						this.twoTabCur = 1;
						this.twoStatus = '0'
						this.jugeCont();
					}
				} else if (+this.type === 2) {
					this.activeCoupon({
						status: this.getNavData().key,
						excGroupId: '09'
					}, () => {
						if (!this.getDates().length) {
							this.type = '1';
							this.twoTabCur = 0;
							this.jugeCont();
						}
					});
				} else {
					this.couponUserPage()
				}
			},
			getByType() {
				getByType().then(res => {
					const arr = res.data;
					arr.forEach(item => {
						if (item.typeName === "appmkt") {
							let marketStore = [{
								name: '全部门店',
								value: ''
							}];
							item.records.forEach(items => {
								marketStore.push({
									name: items.name,
									value: items.code
								})
							})
							this.screen[2].children = marketStore;
						}
						if (item.typeName === "appcat") {
							let omptype = [{
								name: '全部类目',
								value: ''
							}];
							item.records.forEach(items => {
								omptype.push({
									name: items.name,
									value: items.code
								})
							})
							this.screen[0].children = omptype;
							this.couponArr[1].children = omptype.map((omptype, index) => {
								const obj = {
									value: omptype.name == '全部类目' ? '全部' : omptype.name,
									key: '01,05,08',
									marketCategory: omptype.value,
									params: {},
									list: [],
								}
								return obj;
							})
						}
						if (item.typeName === "usetype") {
							let useType = [{
								name: '全部类型',
								value: ''
							}];
							item.records.forEach(items => {
								useType.push({
									name: items.name,
									value: items.code
								})
							})
							this.screen[1].children = useType;
						}
					})
				})
			},
			// 导航栏切换
			navTabSelect(e) {
				let dataset = e.currentTarget.dataset;
				const {
					couponArr
				} = this;
				if (dataset.key != this.type) {
					this.setDatasPames();
					couponArr.find(item => item.key == this.type).twoTabCur = this.twoTabCur;
					this.type = dataset.key;
					this.oldScreeParams = {};
					this.screenParams = {}
					const couponItem = couponArr.find(item => item.key == dataset.key);
					this.twoTabCur = couponItem.twoTabCur;
					this.twoStatus = couponItem.children[this.twoTabCur].key;
					this.getDatasPames();
					if (!this.getDates().length) {
						this.refresh();
					}
				}
			},
			// 二级nav选择
			tabSelect(e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.index != this.twoTabCur) {
					this.setDatasPames();
					this.twoTabCur = dataset.index;
					this.twoStatus = dataset.key;
					this.getDatasPames();
					if (!this.getDates().length) {
						this.refresh();
					}
				}
			},
			// 不同接口选择不同链接
			couponUserPage(callback) {
				const {
					type
				} = this;
				if (+type === 1) {
					this.page.group = 1
					this.onLineUserCoupon(callback);
					this.Unlock = false;
				} else if (+type === 2) {
					if (this.getNavData().key === '04') {
						this.offlineUserCoupon(callback);
					} else {
						this.activeCoupon({
							status: this.getNavData().key,
							...this.oldScreeParams,
							marketCategory: this.getNavData().marketCategory,
							excGroupId: '09'
						}, callback);
					}
				} else if (+type === 3) {
					this.activeCoupon({
						status: this.getNavData().key,
						...this.oldScreeParams,
						groupId: '09'
					}, callback);
				} else {
					const evtscd = this.couponArr.find(item => item.key === this.type)?.key;
					const marketCategory = this.getNavData().key;
					//  活动的档期
					this.activeCoupon({
						evtscd,
						marketCategory,
						...this.oldScreeParams
					}, callback);
				}
			},
			// 活动接口
			activeCoupon(data = {}, callback, fallback) {
				this.couponLoading = true;
				const {
					current,
					size
				} = this.page;
				getEvtscdList({
					pageNo: current,
					pageSize: size,
					...data
				}).then(res => {
					if(current==1){
						this.handleSenTrack(res.data.rowsCount);
					}
					if (res.data != null) {
						let couponUserList = res.data.datalist
						this.setDatas(couponUserList);
						this.loadmore = !(couponUserList.length < this.page.size)
						callback && callback();
					} else {
						this.loadmore = false;
						fallback && fallback()
					}
					this.couponLoading = false;
				}).catch(err => {
					this.loadmore = false;
					fallback && fallback()
					this.couponLoading = false;
				})
			},
			//通道券
			offlineUserCoupon(callback) {
				const page = {
					...this.page
				};
				delete page.group
				vipTicket(Object.assign({}, page, {
					status: 0,
				})).then(res => {
					if (res.data != null) {
						let couponUserList = res.data.cust_accnts || [];
						this.setDatas(couponUserList);
						if (couponUserList.length < this.page.size) {
							this.loadmore = false;
						}
						callback && callback()
					} else {
						this.loadmore = false;
					}
					if(page.pageNo==1){
						this.handleSenTrack(res.data.rowsCount);
					}					
				})
			},
			//线上接口
			onLineUserCoupon(callback) {
				api.couponUserPage(Object.assign({}, this.page, {
					status: this.getNavData().key
				})).then(res => {
					let couponUserList = res.data.records;
					// this.couponUserList = [...this.couponUserList, ...couponUserList];
					this.setDatas(couponUserList);
					if (couponUserList.length < this.page.size) {
						this.loadmore = false;
					}
					callback && callback();
					if(this.page.current==1){
						this.handleSenTrack(res.data.total);
					}
				});
			},
			// 设置数据
			setDatas(arr, types = false) {
				const {
					type,
					twoTabCur,
					couponArr
				} = this;
				if (+type == 2) {
					const oneIndex = couponArr.findIndex(item => +item.key === +type);
					const list = couponArr[oneIndex].children[twoTabCur].list;
					let resList = this.categorizeCouponsByMonth(arr);
					if (types) {
						this.couponArr[oneIndex].children[twoTabCur].list = resList;
					} else {
						//数据按月分开
						this.couponArr[oneIndex].children[twoTabCur].list = list.length ? this.mergeCoupons(list,
							resList) : resList;
					}
				} else {
					const oneIndex = couponArr.findIndex(item => +item.key === +type);
					const list = couponArr[oneIndex].children[twoTabCur].list;
					this.couponArr[oneIndex].children[twoTabCur].list = types ? arr : [...list, ...arr];
				}
			},
			// 处理数据，按照年月日来进行数据分类
			categorizeCouponsByMonth(coupons) {
				const categorizedCoupons = {};
				const currentDate = new Date();
				const currentYear = currentDate.getFullYear();
				const currentMonth = currentDate.getMonth() + 1;
				const couponIds = []
				// Helper function to add coupons to the categorized result
				const addToCategorized = (coupon, year, month) => {
					const monthStr = `${year}-${String(month).padStart(2, '0')}`;

					if (!couponIds.includes(coupon.rowNo)) {
						couponIds.push(coupon.rowNo);

						if (!categorizedCoupons[monthStr]) {
							categorizedCoupons[monthStr] = {
								title: monthStr,
								arr: [],
								display: true
							};
						}

						categorizedCoupons[monthStr].arr.push(coupon);
					}
				};

				// Iterate through each coupon
				coupons.forEach(coupon => {
					const effDate = new Date(coupon.effDate);
					const expDate = new Date(coupon.expDate);
					const startYear = effDate.getFullYear();
					const startMonth = effDate.getMonth() + 1; // Month starts from 0
					const endYear = expDate.getFullYear();
					const endMonth = expDate.getMonth() + 1;

					for (let year = startYear; year <= endYear; year++) {

						let monthStart = (year === startYear) ? startMonth : 1;
						let monthEnd = (year === endYear) ? endMonth : 12;

						for (let month = monthStart; month <= monthEnd; month++) {
							// addToCategorized(coupon, year, month);
							if (year < currentYear || (year === currentYear && month < currentMonth)) {
								// Merge all data before the current month into the current month
								addToCategorized(coupon, currentYear, currentMonth);
							} else {
								addToCategorized(coupon, year, month);
							}
						}
					}
				});

				// Convert the categorized object to an array and sort by month
				const resultArray = Object.values(categorizedCoupons);
				resultArray.sort((a, b) => new Date(a.title + '-01') - new Date(b.title + '-01'));

				return resultArray;
			},
			// 合并分页数据
			mergeCoupons(arr1, arr2) {
				const merged = {};

				// Helper function to add coupons to the merged result
				const addToMerged = (arr) => {
					arr.forEach(item => {
						if (!merged[item.title]) {
							merged[item.title] = {
								title: item.title,
								arr: [],
								display: true
							};
						}
						merged[item.title].arr = merged[item.title].arr.concat(item.arr);
					});
				};

				// Add both arrays to the merged result
				addToMerged(arr1);
				addToMerged(arr2);

				// Convert the merged object back to an array and sort it by title
				return Object.values(merged).sort((a, b) => a.title.localeCompare(b.title));
			},
			setDatasPames() {
				const {
					type,
					twoTabCur,
					couponArr
				} = this;
				const oneIndex = couponArr.findIndex(item => +item.key === +type);
				this.couponArr[oneIndex].children[twoTabCur].params = this.oldScreeParams;
			},
			getDates() {
				return this.getNavData().list;
			},
			getNavData() {
				const {
					type,
					twoTabCur,
					couponArr
				} = this;
				const oneIndex = couponArr.findIndex(item => +item.key === +type);
				return couponArr[oneIndex].children[twoTabCur];
			},
			getDatasPames() {
				this.oldScreeParams = this.getNavData().params;
			},
			// 重置数据
			refresh() {
				this.loadmore = true;
				this.couponUserList = [];
				this.page.current = 1;
				this.couponUserPage();
				const {
					type,
					twoTabCur,
					couponArr
				} = this;
				
			},
			// 跳转激活页面
			navTors(type) {
				// 定义目标页面的 URL
				let targetUrl = '/pages/coupon/coupon-activation/index'; // 默认页面
				if (+type === 1) {
					readpresentnotice();
					this.couponGiveHover = 0;
					targetUrl = '/pages/coupon/coupon-give/index'; // 设置为优惠券赠送页面
				} else if (+type === 2) {
					targetUrl = '/pages/coupon/coupon-tiktok/index'; // 设置为抖音兑换页面
				}
				// 跳转到目标页面
				uni.navigateTo({
					url: targetUrl
				});
				this.moreTypeBtn()
			},
			couponNav(type) {
				// 定义目标页面的 URL
				let targetUrl = '/pages/coupon/coupon-centre/index'; // 默认页面
				if (this.type === '1') {
					targetUrl = '/pages/micro-page/index'
				} else if (+type === 2) {
					targetUrl = '/pages/coupon/coupon-tiktok/index'; // 设置为抖音兑换页面
				}
				// 跳转到目标页面
				uni.navigateTo({
					url: targetUrl
				});
			},
			screeClick(params, value) {
				console.log(params, value)
				if (params === 'orderFields' && value === 'face_value') {
					this.$set(this.screenParams, 'orderDirection', 'DESC')
				} else {
					delete this.screenParams.orderDirection
				}
				if (!value) {
					this.$set(this.screenParams, params, value)
					this.$nextTick(() => {
						delete this.screenParams[params]
					})
					return;
				}
				this.$set(this.screenParams, params, value)
			},
			showScreen() {
				this.showZZC = true;
				this.showScreenZZC = true;
				this.$nextTick(() => {
					this.showScreenAnimate = true;
				})
				this.screenParams = this.oldScreeParams;
			},
			changeSearchInput(e) {
				this.$set(this.oldScreeParams, 'keyword', e.detail.value)
			},
			searchConfirm() {
				this.setDatas([], true);
				this.refresh();
			},
			resotre() {
				this.screenParams = {};
			},
			completeSreach() {
				this.oldScreeParams = this.screenParams;
				this.close();
				this.setDatas([], true)
				this.refresh()
			},
			close() {
				this.showScreenAnimate = false;
				this.showLabels = false;
				this.screenParams = {};
				setTimeout(() => {
					this.showScreenZZC = false;
					this.showZZC = false;
				}, 200)
			},
			moreTypeBtn() {
				if (this.showZZC) {
					this.close();
				} else {
					this.showZZC = !this.showZZC;
					this.$nextTick(() => {
						this.showLabels = true;
					})
				}
			},
			handleIsShowList(e) {
				const {
					type,
					twoTabCur,
					couponArr
				} = this;
				let dataset = e.currentTarget.dataset;
				const oneIndex = couponArr.findIndex(item => +item.key === +type);
				const list = couponArr[oneIndex].children[twoTabCur].list;
				list[dataset.index].display = !list[dataset.index].display;
			},
			
			handleSenTrack(num){
				const types = {
					'1': '线上券',
					'2': '线下券',
					'3':  '停车券'
				}
				const params = {
					coupon_btype: types[this.type],
					coupon_number: num
				}
				if(this.type ==1 || this.type == 3){
					params.coupon_status = this.getNavData().value;
				}else {
					params.coupon_status = "全部";
					params.coupon_belong_format = this.getNavData().value;
				}
				senTrack('CouponListPageView', params)
			}
			
		}
	};
</script>

<style scoped>
	.margin-top-bars {
		margin-top: 175rpx;
	}

	.collection-types {
		top: unset !important;
	}

	.btn {
		height: 50rpx;
		/* width: 352rpx; */
		min-width: 173rpx;
		line-height: 50rpx;
		margin: 18rpx 7rpx 20rpx;
		border-radius: 25rpx;
		font-size: 24rpx;
		background-color: #fff;
		flex: 1;
	}

	.coupon-list {
		/* margin-top: 86rpx; */
	}

	.position-label {
		position: fixed;
		right: 15rpx;
		bottom: 300rpx;
		z-index: 11;
	}

	.coupon-order {
		width: 100upx;
		height: 100upx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		font-size: 24upx;
		z-index: 1;
		position: relative;
	}

	.position-label .coupon-item {
		width: 90upx;
		height: 90upx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		font-size: 24upx;
		position: absolute;
		top: -110rpx;
		right: 80rpx;
		transition: all 0.2s linear;
	}

	.position-label .coupon-hover {
		width: 20rpx;
		height: 20rpx;
		background-color: #F20000;
		border-radius: 50%;
		position: absolute;
		top: 2rpx;
		right: 2rpx;
	}

	.coupon-order .lines {
		width: 50rpx;
		height: 8rpx;
		border-radius: 4rpx;
		background-color: #fff;
		margin-bottom: 9rpx;
	}

	.fixed {
		z-index: 1;
	}

	.search {
		background-color: #fff;
		position: sticky;
		top: 0;
		z-index: 2;
	}

	.search-input {
		border: 1rpx solid #999;
		border-radius: 50rpx;
		height: 30rpx;
		box-sizing: content-box;
		line-height: 1;
	}

	.ZZC {
		position: fixed;
		top: 0;
		left: 0;
		background-color: rgba(0, 0, 0, .5);
		width: 100%;
		height: 100%;
		z-index: 10;
	}

	.dialog-search {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		overflow: auto;
		background-color: #f1f1f1;
		z-index: 11;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
	}

	.dialog-title {
		text-align: center;
	}

	.dialog-item {
		width: 200rpx;
		height: 60rpx;
		border-radius: 30rpx;
		text-align: center;
		background-color: #ddd;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.dialog-content .hover {
		color: #CDAD90;
		border: 1rpx solid #CDAD90;
		background-color: rgba(205, 170, 141, .1);
	}

	.dialog-type::after {
		content: "";
		width: 200rpx;
		margin-right: 20rpx;
	}

	.dialog-bottom {
		width: 330rpx;
		height: 70rpx;
		background-color: #CDAD90;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #333;
	}

	.screenAnimate {
		height: calc(60vh + 250rpx) !important;
	}

	.oldScreenAnimate {
		height: 0;
		transition: all 0.2s linear;
	}

	.min-height-nav {
		min-width: 200rpx;
	}
</style>