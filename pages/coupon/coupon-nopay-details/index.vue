<template>
  <view class="padding-bottom-sm">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true">
      <block slot="backText">返回</block>
      <block slot="content">券订单详情</block>
    </cu-custom>
    <view class="margin-sm bg-box" style="border-radius: 20upx 20upx 0 0;">
      <view class="header padding-sm">
        <view class="flex align-center">
          <text style="line-height: 40upx;">订单编号：{{details.id}}</text>
          <image @click="copy" src="https://img.songlei.com/-1/material/fc6a68d4-3ec6-48c9-a083-fbd1ff90b592.png" style="width: 40upx;height: 40upx;"></image>
        </view>
        <text style="color: #b99b7b;" >{{orderStatus[details.orderStatus]}}</text>
      </view>
      <view class="content padding-sm">
        <view class="content-img">
          <image 
            :src="details.couponPicUrl || 'https://img.songlei.com/-1/material/5ba00ad5-c236-41fa-93ea-a4f4fb89a5e8.png'" 
            mode="aspectFill" 
            style="width: 100%;height: 100%;"
          ></image>
        </view>
        <view class="content-goods">
          <view class="content-title">{{ details.typeName }}</view>
          <view class="content-price">
            <text style="font-weight: bold;color: #b39977;font-size: 32upx;">
              <block v-if="details.useType === '04'">
                <text>{{ details.faceValue * 10 }}</text>
                <text class="icon" style="bottom: 0;top: auto;font-size: 80%;">折</text>
              </block>
              <block v-else>
                <text class="icon" style="font-size: 80%;">￥</text>
                <text>{{details.faceValue || 0}}</text>
              </block>
            </text>
            <text class="text-coupon-btn">券礼包</text>
          </view>
          <view style="font-size: 24upx;color: #999;">活动专用</view>
        </view>
      </view>
      <view class="footer padding-sm">
        <view class="flex" style="width: 100%;">
          <view>数量：</view>
          <view class="flex-sub" style="text-align: right;">{{details.num}}</view>
        </view>
        <view class="flex" style="width: 100%;" v-if="details.eventTypeCode !== '3' || details.eventTypeCode !== '2'">
          <view>共计：</view>
          <view class="flex-sub" style="text-align: right;" v-if="details.eventTypeCode === '1'">{{details.amount}}元</view>
          <view class="flex-sub" style="text-align: right;" v-if="details.eventTypeCode === '3'">0元</view>
          <view class="flex-sub" style="text-align: right;" v-if="details.eventTypeCode === '4'">{{details.points}}积分</view>
          <view class="flex-sub" style="text-align: right;" v-if="details.eventTypeCode === '5'">{{details.amount}}元 + {{details.points}}积分</view>
        </view>
        <view class="flex" style="width: 100%;">
          <view class="flex-sub" style="text-align: right;">
            下单时间：{{ details.createDate || '暂无时间' }}
          </view>
        </view>
      </view>
    </view>
    <view class="margin-sm footer-box padding-sm radius-sm">
      <view class="flex" style="width: 100%;">
        <view>详情说明：</view>
        <view class="flex-sub padding-left padding-bottom-xs">
          <view v-for="(item, index) in details.detailUsageRule && details.detailUsageRule.split('\n')" :key="index">
            {{item}}
          </view>
        </view>
      </view>
      <view class="flex" style="width: 100%;">
        <view>使用描述：</view>
        <view class="flex-sub padding-left">
          <view v-for="(item, index) in details.detailUsageDesc && details.detailUsageDesc.split('\n')" :key="index">
            {{item}}
          </view>
        </view>
      </view>
    </view>
    <view v-if="details.useType === '08' && details.orderStatus === 'Y'" class="margin-sm footer-box padding-sm radius-sm text-df">
      <view class="padding-bottom-sm text-lg text-bold">券列表</view>
      <view 
        :class="details.details.length - 1 !== index ? 'padding-bottom-xs margin-bottom-sm' : ''"
        v-for="(item, index) in details.details"
        :key="item.accntNo"
        :style="{
          'border-bottom': `${details.details.length - 1 !== index && '1rpx solid #eee'}`
        }"
      >
        <view class="flex padding-bottom-xs">
          <view class="">券名称：</view>
          <view class="flex-sub text-cut">{{item.typeName}}</view>
        </view>
        <view class="flex" style="color: #999;">
          <view class="flex flex-sub">
            <view class="">金额：</view>
            <view class="flex-sub">{{ item.amount >= 1 ? item.amount + '元' : item.amount * 10 + '折' }}</view>
          </view>
          <view class="flex flex-sub">
            <view class="">券类型：</view>
            <view class="flex-sub">{{item.useTypeName}}</view>
          </view>
        </view>
        <view class="flex" style="color: #999;">
          <view class="flex flex-sub">
            <view class="">开始时间：</view>
            <view class="flex-sub">{{ item.effDate }}</view>
          </view>
          <view class="flex flex-sub">
            <view class="">结束时间：</view>
            <view class="flex-sub">{{item.expDate}}</view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="details.orderStatus === 'N' || details.orderStatus === 'P'">
      <view class="one-btn" @click="btnCoupon" v-if="details.eventTypeCode === '5' || details.eventTypeCode === '1'">支付</view>
      <view class="one-btn" @click="cancel" style="background: linear-gradient(-90deg, #ED5279 0%, #FF0043 100%);color: #fff;">取消</view>
    </view>
    <view v-if="details.orderStatus === 'Y' && details.eventTypeCode !== '3' && details.canRefund === 'Y'">
      <view class="one-btn" @click="refund">退款</view>
    </view>
    <view class="toast-pay" v-if="toastHide">
      <pay-components
        ref="pay"
        :pageTitle="pageTitle"
        :callBack="true"
        @success="successBack"
        @fail="failBack"
      ></pay-components>
      <view @click="$noMultipleClicks(payOrder)" class="one-btn">确定</view>
    </view>
    <view class="ZZC" @click="toastClose" v-if="toastHide"></view>
  </view>
</template>

<script>
const app = getApp()
import { nopayDetail, payorder, nopayCancel, refund } from "@/pages/coupon/api/coupon.js"
import { navCoupon } from "../components/utils.js"

export default {
  data() {
    return {
      theme: app.globalData.theme,
      orderStatus: {
        'N': '未支付',
        'Y': '已完成',
        'C': '已取消',
        'A': '已取消',
        'AA': '已取消',
        'R': '退款中',
        'E': '已部分退款',
        'T': '退款中',
        'F': '已退款',
        'P': '待支付'
      },
      toastHide: false,
      details: {},
      payParams: {},
      noClick: true
    }
  },
  onLoad(options) {
    if(options.id) {
      this.nopayDetail(options.id)
    }
  },
  methods: {
    nopayDetail(id) {
      nopayDetail(id).then(res => {
        const { data } = res;
        this.details = data;
        this.details.couponPicUrl = data.couponPicUrl && data.couponPicUrl.split(',')[0]
      })
    },
    btnCoupon() {
      if(this.loading) {
        return;
      }
      this.loading = true;
      uni.showLoading({
        title: '领取中.....',
        mask: true
      })
      const { id } = this.details;
      payorder({ orderId: id }).then(res => {
        this.toastHide = true;
        this.payParams = {};
        this.payParams = res.data.payRequest
      })
    },
    payOrder() {
      uni.showLoading({
        title: '支付中',
        mask: true
      });
      this.$refs.pay?.payOrder(this.payParams)
    },
    cancel() {
      const { id } = this.details;
      const that = this;
      uni.showModal({
        title: '提示',
        content: '确定取消该订单?',
        success(res) {
          if (res.confirm) {
            nopayCancel({ orderId: id}).then(res => {
              uni.showModal({
                title: '提示',
                content: '取消成功',
                icon: 'none',
                cancelText: '继续领券',
                confirmText: '回首页',
                success(res) {
                  if (res.confirm) {
                    uni.switchTab({
                      url: '/pages/home/<USER>'
                    })
                    that.toastClose();
                  } else if (res.cancel) {
                    navCoupon('pages/coupon/coupon-centre/index');
                    that.toastClose();
                  }
                }
              })
            })
          }
        }
      })
    },
    // 支付成功的回调
    successBack() {
      const that = this;
      uni.showModal({
        title: '提示',
        content: '支付成功',
        icon: 'none',
        cancelText: '继续领券',
        confirmText: '去使用',
        success(res) {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/coupon/coupon-user-list/index'
            })
            that.toastClose();
          } else if (res.cancel) {
            navCoupon('pages/coupon/coupon-centre/index');
            that.toastClose();
          }
        }
      })
    },
    refund() {
      console.log(1111);
      const { id } = this.details;
      const that = this;
      uni.showModal({
        title: '提示',
        content: '确定要退款该订单?',
        success(res) {
          if(res.confirm) {
            refund({ oriOrderId: id }).then(res => {
              uni.showModal({
                title: '提示',
                content: '退款成功',
                icon: 'none',
                showCancel: false,
                confirmText: '确定',
                success(res) {
                  uni.navigateBack({
                    delta: 1
                  })
                }
              })
            })
          }
        }
      })
    },
    // 支付失败的回调
    failBack() {
      uni.showToast({
        title: '支付失败',
        icon: 'none'
      })
      // this.toastHide = false;
      this.loading = false;
    },
    // 银行卡刷新
    updataBankList() {
      this.$refs.pay?.getBankList()
    },
    toastClose() {
      this.toastHide = false;
      this.loading = false;
    },
    // 复制
    copy() {
      uni.setClipboardData({
        data: this.details.id, //要被复制的内容
        success: () => { //复制成功的回调函数
          uni.showToast({ //提示
            title: '复制成功'
          })
        }
      });
    }
  }
}
</script>

<style scoped>
.bg-box {
  background-color: #ffffff;
}
.header{
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24upx;
}
.content {
  display: flex;
  justify-content: space-between;
}
.content-price {
  display: flex;
  align-items: center;
}
.content-img {
  width: 200upx;
  height: 200upx;
  border: 1upx solid #ddd;
  border-radius: 10upx;
}
.content-goods {
  flex: 1;
  padding: 10upx 0;
  padding-left: 20upx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.content-title {
  height: 80upx;
  line-height: 40upx;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 这里是超出几行省略 */
  font-size: 28upx;
  margin-bottom: 10upx;
}
.text-coupon-btn {
  border: 1upx solid #b3ae93;
  border-radius: 10upx;
  margin-left: 20upx;
  font-size: 22upx;
  color: #b3ae93;
  width: 80upx;
  height: 40upx;
  line-height: 40upx;
  display: inline-block;
  text-align: center;
}
.couponBtn {
  min-width: 150upx;
  border: 1px solid #b3ae93;
  height: 60upx;
  text-align: center;
  line-height: 60upx;
  border-radius: 30upx;
  color: #b3ae93;
}
.footer {
  border-top: 1upx solid #ddd;
}
.footer-box {
  background-color: #fff;
}
.one-btn {
  width: 570upx;
  margin: 50upx auto 30upx;
  text-align: center;
  line-height: 90upx;
  background: linear-gradient(-90deg, #FFBF00 0%, #FFDD00 100%);
  border-radius: 45upx;
}
.toast-pay {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  background-color: #fff;
  padding: 30upx 0 20upx;
  border-radius: 20upx 20upx 0 0;
  z-index: 10;
}
.ZZC {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, .5);
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}
</style>
