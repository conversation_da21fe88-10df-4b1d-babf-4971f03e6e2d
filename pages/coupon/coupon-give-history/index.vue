<template>
  <view class="">
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
    	<block slot="backText">返回</block>
    	<block slot="content">历史记录</block>
    </cu-custom>
    <scroll-view scroll-x class="padding-lr head-tab" :style="{ top: CustomBar + 'px' }">
      <view class="flex">
        <view 
          class="margin-tb margin-right" 
          :class="tabIndex === index ? 'hover' : '' " 
          style="word-break: keep-all" 
          v-for="(item, index) in headList"
          @click="tabClick(index)"
        >{{ item.label }}</view>
      </view>
    </scroll-view>
	
    <view class="padding-lr padding-bottom" :style="{ paddingTop: '120rpx' }">
      <template v-if="list.length">
        <view class="box-item padding-lr padding-tb-sm margin-bottom" v-for="item in list" @click="navTor(item)">
          <view class="flex align-center justify-between padding-bottom">
            <view class="coupon-icon" style="width: 174rpx;background-image: url('https://img.songlei.com/-1/material/3f30c4e5-adaa-4d2b-81d1-3d3b471c4652.png');" v-if="item.presentMode === '1'">拼手气券包</view>
            <view class="coupon-icon" v-if="item.presentMode === '2' && item.details.length !== 1">普通券包</view>
            <view class="text-df" v-if="item.presentMode === '2' && item.details.length === 1">【松鼠商城】{{ item.details[0].typeName }}</view>
            <view>已领取 {{ item.couponGetNum }}/{{ item.couponTotalNum }}</view>
          </view>
          <view class="padding-bottom-sm margin-bottom-sm" style="border-bottom: 1rpx solid #ddd;color: #9A9A9A;">{{ item.createDate }}</view>
          <view class="flex">
            <view class="flex-sub">
              <view :class="item.couponGetNum == 0 ? '' : 'padding-bottom-sm'">赠给好友</view>
              <template v-for="items in item.details">
                <image class="receive-headUrl" :src="items.receiveHeadUrl" v-if="items.receiveCustId"></image>
              </template>
            </view>
            <view class="flex align-end text-bold" style="color: #EE112B;">
              {{ paramStatus[item.status] }}
            </view>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="text-center margin-top-sm" style="color: #9A9A9A;">暂无数据~</view>
      </template>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { getpresentpage } from "@/pages/coupon/api/coupon-give.js"

export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      page: {
      	current: 1,
      	size: 10,
        total: 0
      	//升序字段
      },
      list: [],
      loadmore: true,
      checkList: [],
      tabIndex: 0,
      headList: [{
        label: '全部',
        params: '0'
      },{
        label: '已收到',
        params: '1'
      },{
        label: '已送出',
        params: '2'
      },{
        label: '赠送中',
        params: '3'
      },{
        label: '已领完',
        params: '4'
      },{
        label: '已退回',
        params: '5'
      },{
        label: '已取消',
        params: '6'
      }],
      paramStatus: {
        '1': '已领完',
        '2': '赠送中',
        '3': '已退回'
      }
    }
  },
  onLoad() {
    this.getpresentpage();
  },
  onPullDownRefresh() {
    this.page.current = 1;
    this.list = [];
    this.getpresentpage(() => {
      uni.stopPullDownRefresh();
    });
  },
  onReachBottom() {
    if(+this.page.current * 10 > +this.page.total) {
      return;
    }
    this.page.current += 1;
    this.getpresentpage();
  },
  methods: {
    tabClick(index) {
      this.tabIndex = +index;
      this.list = [];
      this.page.current = 1;
      this.getpresentpage();
    },
    getpresentpage(callback) {
      getpresentpage({ pageNo: this.page.current, status: this.headList[this.tabIndex].params }).then(res => {
        this.list = [...this.list, ...res.data.records];
        this.page.total = res.data.total;
        callback && callback();
      })
    },
    navTor(row) {
      uni.navigateTo({
        url: '/pages/coupon/coupon-give-details/index?id=' + row.id
      })
    }
  }
}
</script>

<style>
.head-tab {
  position: fixed;
  background-color: #fff;
  z-index: 1;
}
.head-tab .hover {
  font-weight: bold;
  color: #EE112B;
}
.box-item {
  border-radius: 40rpx;
  background-color: #ffffff;
}
.receive-headUrl {
  width: 74rpx;
  height: 74rpx;
  background-color: #ddd;
  border-radius: 50%;
  margin-left: -10rpx;
}
.coupon-icon {
  width: 147rpx;
  height: 45rpx;
  line-height: 45rpx;
  text-align: center;
  color: #ffffff;
  background-image: url('https://img.songlei.com/-1/material/9715966a-0270-43f4-a839-b5ba9e8ec775.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>