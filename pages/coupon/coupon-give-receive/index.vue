<template>
  <view v-if="load">
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
    	<block slot="backText">返回</block>
    	<block slot="content">领取礼券</block>
    </cu-custom>
    <view class="coupon-info margin padding-tb-lg padding-lr-sm bg-white">
      <view class="flex padding-bottom">
        <view class="margin-right-sm" style="width: 110rpx;height: 110rpx;"><image :src="giveInfo.presentHeadUrl" style="width: 100%;height: 100%;background-color: #ddd;border-radius: 50%;"></image></view>
        <view class="flex-sub flex flex-direction">
          <view class="text-df"><text style="color: #D79F76;">{{ giveInfo.presentNickname }}</text> 赠送礼品券快去领取吧~</view>
          <view class="flex flex-sub align-center">
            <view v-if="giveInfo.presentMode === '1'" class="coupon-icon text-xsm margin-right-sm">拼手气</view>
            <view style="background-image: url('https://img.songlei.com/-1/material/9715966a-0270-43f4-a839-b5ba9e8ec775.png');" v-if="giveInfo.presentMode === '2'" class="coupon-icon text-xsm">普通送</view>
          </view>
        </view>
      </view>
      <view class="text-df padding-lr-sm padding-top-sm margin-bottom" style="color: #D79F76;border-top: 1rpx dashed #ddd;">
        {{ giveInfo.presentDesc }}
      </view>
      <view class="bg-video">
        <view
          class="flex flex-direction align-center justify-center" 
          style="height: 254rpx;background: url('https://img.songlei.com/-1/material/bb885964-709b-4673-8505-25c680a8e74c.png') no-repeat;background-size: 100% 100%;padding: 40rpx 50rpx;">
          <template v-if="detailsValueList.length === 1">
            <view class="color-red">
              <text class="text-xdf">￥</text>
              <text class="text-xsl line-height-1 text-bold" style="vertical-align: top;">{{ detailsValueList[0].faceValue }}</text>
            </view>
            <view class="text-bold text-xl">{{ detailsValueList[0].typeName }}</view>
            <view class="text-df" style="color: #767676;">{{ detailsValueList[0].simpleUsageRule }}</view>
          </template>
          <template v-else>
            <view class="color-red">
              <text class="text-xdf">￥</text>
              <text class="text-xsl line-height-1 text-bold" style="vertical-align: top;">{{ allValue }}</text>
            </view>
            <view class="text-xl" style="color: #000000;">(共<text class="color-red">{{ detailsValueList.length }}</text>张券)</view>
          </template>
        </view>
        <view v-if="detailsValueList.length === 1" class="text-df text-center margin-sm" style="color: #767676;">有效期: {{ detailsValueList[0].expDate }}</view>
        <view
          class="bg-zzc"
          v-if="!receiveSuceess"
          :style="{ 'background-image': 'url(' + paramsStatus[giveInfo.status] + ')' }"
        ></view>
      </view>
      <view class="margin-top margin-bottom-lg">
        <view v-if="giveInfo.status === '2' && receiveSuceess" class="flex align-center justify-center" style="background-color: #FAECDC;padding: 4rpx;width: 410rpx;line-height: 60rpx;border-radius: 40rpx;margin: 0 auto;">
          <image src="https://img.songlei.com/-1/material/7d186abe-b098-493b-8ab7-90f628c2d80e.png" style="width: 45rpx;height: 45rpx;margin-right: 6rpx;"></image>
          <view style="color: #FF0000;">礼券已领取，快去查看吧！</view>
        </view>
      </view>
      <view class="margin-top padding-top" style="border-top: 1rpx solid #ddd;">
        <template v-if="giveInfo.status === '1'">
          <view class="padding-tb text-center" style="color: #000000;">“亲，您来晚了，礼品券已领完~”</view>
          <view style="width: 216rpx;height: 212rpx;margin: 0 auto 50rpx;"><image src="https://img.songlei.com/-1/material/a9f557c4-3bbe-4510-899b-0080e5aa9934.png" style="width: 100%;height: 100%;"></image></view>
          <button class="btn text-xdf" style="color: #ffffff;background: linear-gradient(-90deg, #FF4E00, #FF8463);" @click="navTor">查看详情</button>
        </template>
        <template v-if="giveInfo.status === '2' && !receiveSuceess">
          <view class="btn text-xdf" style="color: #ffffff;background: linear-gradient(-90deg, #FF4E00, #FF8463);" @click="receivepresentcoupon">拆开看看</view>
        </template>
        <template v-if="giveInfo.status === '2' && receiveSuceess">
          <view class="flex align-center justify-between">
            <view class="btn text-xdf" style="color: #ffffff;width: 320rpx;background-color: #fff;color: #000000;border: 1rpx solid #BFBFBF;" @click="close">关闭</view>
            <view class="btn text-xdf" style="color: #ffffff;background: linear-gradient(-90deg, #FF4E00, #FF8463);width: 320rpx;" @click="navTor">查看优惠券</view>
          </view>
        </template>
        <template v-if="giveInfo.status === '3'">
          <view class="padding-tb-sm text-center margin-bottom">您居然拒绝我，我很伤心555</view>
          <button class="btn text-xdf" style="color: #ffffff;background: linear-gradient(-90deg, #FF4E00, #FF8463);" @click="close">关闭</button>
        </template>
      </view>
    </view>
    <view style="height: 20rpx;"></view>
    <view class="text-df text-center" v-if="giveInfo.status === '2'">24小时内未领取，将<text>退还</text>给对方。</view>
  </view>
</template>

<script>
const app = getApp();
import { getpresentorder, receivepresentcoupon } from "@/pages/coupon/api/coupon-give.js"

export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      check: 1,
      load: false,  // 前置加载，等加载完之后在现实
      showZZC: false,
      checkList: [],
      presentDesc: '',
      giveInfo: {},
      userInfo: {},
      detailsValueList: [],
      receiveSuceess: false, // 是否领取成功
      paramsStatus: {
        '1': 'https://img.songlei.com/-1/material/d3604358-732d-4b3a-b3b6-f075ce391879.png',
        '2': 'https://img.songlei.com/-1/material/8e0cf165-ba82-4146-89ca-992f3e376f5f.png',
        '3': 'https://img.songlei.com/-1/material/d5e9ce7c-d34a-4ec5-973c-46b385e22fec.png'
      }
    }
  },
  computed: {
    allValue() {
      let value = 0
      this.detailsValueList.map(item => {
        value += +item.faceValue
      })
      return (+value).toFixed(2)
    }
  },
  onLoad(options) {
    if(options.id) {
      this.id = options.id
      this.loopUser()
      uni.showLoading({ title: '加载中' });
    }
  },
  onShareAppMessage() {
    return {
      title: this.giveInfo.presentDesc,
      path: '/pages/coupon/coupon-give-details/index?id='  + this.id,
      imageUrl: 'https://img.songlei.com/-1/material/fc67cabb-a3a3-4476-9cd2-f6f40966fa21.png'
    }
  },
  onPullDownRefresh() {
    this.getActiveDetails(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    loopUser() {
      const that = this;
      const user_info = uni.getStorageSync('user_info');
      if(!user_info) {
        setTimeout(() => {
          this.loopUser();
        }, 0)
        return;
      }
      const { id, erpCid } = user_info;
      this.userInfo = user_info;
      if(!id) {
        uni.navigateTo({
          url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-give-details/index?id=' + this.id)
        })
        return;
      }
      if(!erpCid) {
        app.doLogin().then(res => {
          if(res === 'success') {
            const user = uni.getStorageSync('user_info');
            if(user.erpCid) {
              this.getActiveDetails();
            } else {
              uni.showModal({
                title: '提示',
                content: '登录错误',
                showCancel: false,
                success() {
                  uni.navigateTo({
                    url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-give-details/index?id=' + this.id)
                  })
                }
              })
            }
          } else {
            uni.navigateTo({
              url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-give-details/index?id=' + this.id)
            })
          }
        })
        return;
      }
      this.getActiveDetails();
      // 领取券
      // this.receivepresentcoupon();
    },
    getActiveDetails(callback) {
      getpresentorder({ id: this.id, custId: '' }).then(res => {
        this.giveInfo = res.data;
        this.detailsValueList = res.data.details;
        const { erpCid } = this.userInfo;
        uni.hideLoading();
        const find = this.detailsValueList.find(item => {
          if(item.receiveCustId && item.receiveCustId === erpCid) {
            this.navTor();
            return true
          }
          return false;
        })
        if(!find) {
          this.load = true;
        }
        callback && callback();
      })
    },
    receivepresentcoupon() {
      uni.showLoading({ title: '领取中' });
      receivepresentcoupon({ orderId: this.id }).then(res => {
        uni.hideLoading();
        this.receiveSuceess = true;
      })
    },
    navTor() {
      uni.redirectTo({
        url: '/pages/coupon/coupon-give-details/index?id=' + this.id
      })
    },
    close() {
      uni.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  }
}
</script>

<style>
.coupon-info {
  border-radius: 40rpx;
}
.coupon-icon {
  width: 136rpx;
  height: 45rpx;
  line-height: 45rpx;
  text-align: center;
  color: #ffffff;
  background-image: url('https://img.songlei.com/-1/material/f3587399-6b21-488d-9cf8-2118041b9cb5.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.color-red {
  color: #FF0000;
}
.btn {
  width: 340rpx;
  height: 80rpx;
  background-color: #ffffff;
  color: #090908;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  margin: 0 auto;
}
.bg-zzc {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.7);
  background-image: url('https://img.songlei.com/-1/material/8e0cf165-ba82-4146-89ca-992f3e376f5f.png');
  background-repeat: no-repeat;
  background-size: 200rpx 200rpx;
  background-position: 50% 50%;
}
</style>