<template>
	<view class="page-carnumber-test">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">
				<view class="location" @click="getlocation">
					<view
						class=""
						style="display: flex; justify-content: center; align-items: center; font-size: 32rpx;  font-weight: 400; color: #000000"
					>
						<image
							v-if="selectstore.storeName"
							style="width: 30rpx; height: 37rpx; margin-right: 8rpx"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%AE%9A%E4%BD%8D%402x.png"
						></image>
						{{ selectstore && selectstore.storeName ? selectstore.storeName : '请选择门店' }}
					</view>
				</view>
			</block>
		</cu-custom>

		<!-- 扫码输入 -->
		<view class="carmi padding-left-sm padding-right-sm" style="">
			<input
				style="width: 475rpx; height: 80rpx; text-align: center"
				type="text"
				placeholder="请输入抖音券码或扫抖音券二维码"
				placeholder-style="color: #CCCCCC;font-size: 26rpx;margin-left:20rpx"
				@input="getCoupon($event)"
				v-model="couponId"
				maxlength="29"
			/>
			<view class="cuIcon-camera icon" @tap="scanCode" style="color: #dab691"></view>
		</view>

		<!-- 查询跳转按钮 -->
		<view class="btn" style="margin-top: 70rpx">
			<button @click="query" style="width: 100%; background: linear-gradient(88deg, #c7ae93 0%, #c5a288 100%); color: #fff" class="cu-btn round">查询</button>
		</view>

		<uni-popup ref="perpopup" type="center" :mask-click="false">
			<view class="permissions_box">当您使用APP时，获取最近的门店需要位置权限，不授权上述权限，不影响APP其他功能使用。</view>
		</uni-popup>
	</view>
</template>

<script>
import api from 'utils/api';
import __config from 'config/env';
const app = getApp();
import distanceUtil from 'utils/distanceUtil';
import utils from 'utils/util.js';
// #ifdef APP-PLUS
const mpaasScanModule = uni.requireNativePlugin('Mpaas-Scan-Module');
// #endif

export default {
	components: {},
	data() {
		return {
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			storelist: [], //门店列表
			selectstore: '',
			open: false,
			//从后台生成的二维码或者链接里面获取sf里面传递过来的门店号和停车场参数
			sf: '',
			couponId: '' //券id
		};
	},
	watch: {
		selectstore: {
			handler: function (newVal) {
				if (newVal && newVal.mustLogin == 'Y') {
					//必须登录，这时候跳转到登录
					app.isLogin('/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-tiktok/index?item=' + JSON.stringify(newVal)));
				}
			},
			deep: true
		}
	},

	onLoad(options) {
		if (options.item) {
			this.selectstore = JSON.parse(decodeURIComponent(options.item));
			console.log('this.selectstore===>', this.selectstore);
		} else if (options.sf) {
			this.sf = options.sf;
		} else if (options.scene) {
			const qrCodeScene = decodeURIComponent(options.scene);
			if (qrCodeScene) {
				//接受二维码中参数  参数sf=XXX
				// 缴费二维码能带上门店和停车场参数，都可选
				// pages/parkinglot/parking-home/index?sf=&nGo=
				// sf=HSY,290,100029797
				this.sf = utils.UrlParamHash(qrCodeScene, 'sf');
			}
		}

		if (app.isLogin(false)) {
			if (!this.selectstore || !this.selectstore.store) {
				this.getstorelist();
			}
		} else {
			app.doLogin().then((res) => {
				if (!this.selectstore || !this.selectstore.store) {
					this.getstorelist();
				}
			});
		}
	},

	onShow() {
		if (app.isLogin(true)) {
		}
	},

	methods: {
		// 查询跳转
		query() {
			if (!this.couponId) {
				uni.showToast({
					title: '抖音券码不能为空！',
					icon: 'none',
					duration: 3000
				});
				return;
			}
			if (!this.selectstore || !this.selectstore.store) {
				uni.showToast({
					title: '请选择门店！',
					icon: 'none',
					duration: 3000
				});
				return;
			}
			uni.navigateTo({
				url: `/pages/tiktok/voucher?store=${this.selectstore.store}&coupon=${this.couponId}`
			});
		},

		//扫码
		scanCode() {
			let that = this;
			if (!this.selectstore || !this.selectstore.store) {
				uni.showToast({
					title: '请选择门店！',
					icon: 'none',
					duration: 3000
				});
				return;
			}
			// #ifdef MP-WEIXIN
			// 允许从相机和相册扫码
			uni.scanCode({
				scanType: ['barCode', 'qrCode'], //所扫码的类型 barCode	一维码 qrCode	二维码
				success: (res) => {
					if (res.result) {
						const code = res.result; //result	所扫码的内容 测试：6902902006811
						console.log('code===', code);
						if (code) {
							uni.navigateTo({
								url: `/pages/tiktok/voucher?store=${this.selectstore.store}&coupon=${code}`
							});
						} else {
							uni.showToast({
								title: `该条码不符合要求`,
								icon: 'none',
								duration: 2000
							});
						}
					} else {
						uni.showToast({
							title: `请重新扫描`,
							icon: 'none',
							duration: 2000
						});
						console.log('请重新扫描');
						return false;
					}
				},
				fail: (res) => {
					uni.showToast({
						title: `未识别到二维码`,
						icon: 'none',
						duration: 2000
					});
					console.log('未识别到二维码');
				}
			});
			// #endif

			// #ifdef APP-PLUS
			// 允许从相机和相册扫码
			mpaasScanModule.mpaasScan(
				{
					// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
					scanType: ['qrCode', 'barCode'],
					// 是否隐藏相册，默认false不隐藏
					hideAlbum: false
				},
				(res) => {
					if (res.resp_code == 1000 && res.resp_message == 'success') {
						const code = res.resp_result; //result	所扫码的内容
						if (code) {
							if (code) {
								uni.navigateTo({
									url: `pages/tiktok/voucher?store=${this.selectstore.store}&coupon=${code}`
								});
							} else {
								// this.bindCard = code
								uni.showToast({
									title: `该条码不符合要求`,
									icon: 'none',
									duration: 2000
								});
							}
						} else {
							uni.showToast({
								title: `未识别到内容`,
								icon: 'none',
								duration: 2000
							});
						}
					} else {
						// uni.showToast({
						// 	title: `未识别到信息`,
						// 	icon:"none",
						// 	duration: 2000
						// });
					}
				}
			);
			// #endif
		},

		//绑定新卡
		getCoupon(e) {
			console.log('e===>', e.detail.value);
			this.$nextTick(() => {
				this.couponId = e.detail.value;
			});
		},

		getlocation(e) {
			uni.navigateTo({
				url: '/pages/parkinglot/select-store/index'
			});
		},

		getstorelist() {
			let that = this;
			api.getstorelist().then((data) => {
				that.storelist = data.data;
				//如果sf有值就是从链接带进了门店和停车场参数
				if (that.sf && that.storelist && this.storelist.length > 0) {
					const storeCode = that.sf.split(',')[1];
					for (let i = 0; i < that.storelist.length; i++) {
						if (that.storelist[i].store == storeCode) {
							that.selectstore = that.storelist[i];
							return;
						}
					}
					// 否则就定位获取最近的停车场
				} else {
					that.checkPosPermission();
				}
			});
		},

		checkPosPermission() {
			// #ifdef MP-WEIXIN
			this.getCurrentPos();
			// #endif
			// #ifdef APP-PLUS
			let that = this;
			var platform = uni.getSystemInfoSync().platform;
			if (platform == 'android') {
				plus.android.checkPermission(
					'android.permission.ACCESS_FINE_LOCATION',
					(granted) => {
						if (granted.checkResult == -1) {
							//弹出
							that.$refs.perpopup.open('top');
						} else {
							//执行你有权限后的方法
							that.getCurrentPos();
						}
					},
					(error) => {
						console.error('Error checking permission:', error.message);
					}
				);
				plus.android.requestPermissions(['android.permission.ACCESS_FINE_LOCATION'], (e) => {
					//关闭
					that.$refs.perpopup.close();
					if (e.granted.length > 0) {
						//执行你有权限后的方法
						that.getCurrentPos();
					}
				});
			} else {
				//执行你有权限后的方法 ios
				that.getCurrentPos();
			}
			// #endif
		},

		getCurrentPos() {
			let that = this;
			uni.getLocation({
				type: 'gcj02',
				// 开启高精度定位
				// isHighAccuracy: true,
				// 是否解析地址信息
				geocode: true,
				success: (res) => {
					const nearDistance = distanceUtil.getNearestDistance(res.latitude, res.longitude, that.storelist);
					if (nearDistance) that.selectstore = nearDistance;
				},
				fail() {
					//定位失败
					that.selectstore = null;
				}
			});
		}
	}
};
</script>

<style scoped lang="less">
.content {
	background: white;
	border-radius: 50rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	margin-left: 20rpx;
	margin-right: 20rpx;
}

.location {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 80rpx;
}

.permissions_box {
	padding: 200rpx 30rpx 50rpx;
	background-color: #fff;
	color: #000000;
}

.carmi {
	position: relative;
	margin: 165rpx auto;
	margin-bottom: 30rpx;
	width: 611rpx;
	height: 80rpx;
	background: #ffffff;
	border-radius: 40rpx;
}

.icon {
	color: #dab691;
	width: 100rpx;
	height: 42rpx;
	line-height: 42rpx;
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	text-align: center;
	font-size: 40rpx;
	border-left: dotted;
}

.btn {
	width: 343rpx;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 40rpx;
	margin: 0 auto;
}
</style>
