import {
	requestApi as request
} from '@/utils/api.js'
import {
	senTrack,
	getCurrentTitle
} from "@/public/js_sdk/sensors/utils.js"

// 获取领券中心门店和分类
export const getCentreType = (data) => {
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.info.getcategory',
		method: 'post',
		data: {
			channel: 'SONGSHU'
		}
	})
}
// 获取分类下的列表
export const getTypeDetail = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid,
		erpCustType
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.info.getlist',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			pageSize: 10,
			custId: erpCid,
			custType: erpCustType,
			...data
		}
	})
}

// 获取活动详情
export const getActiveDetails = (data = {}, isSenTrack = true) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid,
		erpCustType
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.info.getinfo',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}
	}).then(res => {
		// 上传神策埋点
		const coupon = res.data;
		if (isSenTrack) {
			const params = {
				page_name: getCurrentTitle(0), // 建议页面传
				coupon_id: coupon.typeId || '',
				coupon_name: coupon.typeName || '',
				coupon_type: coupon.useTypeName || '',
				coupon_use_method: coupon.eventTypeName || '',
				effect_time: coupon.eventEffDatetime || '',
				expire_time: coupon.eventExpDatetime || '',
				coupon_threshold_amount: coupon.condAmount > 0 ? coupon.condAmount : 0,
				coupon_amount: coupon.faceValue,
			}
			if (coupon.eventTypeCode == '5' || coupon.eventTypeCode == '4' || coupon
				.eventTypeCode == '1') {
				params.coupon_buy_method = coupon.eventTypeCode == '5' ? '人民币+积分' : coupon
					.eventTypeCode == '4' ? '积分' : coupon.eventTypeCode == '1' ? '人民币' : '';
				params.coupon_buy_integral = coupon.points * data.num;
				params.coupon_buy_amount = coupon.cash  * data.num;
			}

			senTrack('CouponDetailPageView', params);
		}
		return res;
	}).catch(err => {
		console.error(err);
		throw err;
	})
}

// 提交信息
export const submitInfo = (data = {}, errModalHide = false) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid,
		erpCustType,
		phone
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.submit',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			mobile: phone,
			num: 1,
			amount: 0,
			points: 0,
			...data
		},
		errModalHide
	}).then(res => {

		// 埋点字段从接口返回值里取
		module.exports.getActiveDetails({
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}).then(res => {
			const coupon = res.data;
			const parmas = {
				page_name: getCurrentTitle(0), // 建议页面传
				coupon_id: coupon.typeId || '',
				coupon_name: coupon.typeName || '',
				coupon_type: coupon.useTypeName || '',
				coupon_use_method: coupon.eventTypeName || '',
				effect_time: coupon.eventEffDatetime || '',
				expire_time: coupon.eventExpDatetime || '',
				coupon_buy_number: data.num,
				coupon_threshold_amount: coupon.condAmount > 0 ? coupon.condAmount : 0,
				coupon_amount: coupon.faceValue,
				is_success: res && res.code === 0,
				fail_reason: ''
			}
			if (coupon.eventTypeCode == '5' || coupon.eventTypeCode == '4' || coupon
				.eventTypeCode == '1') {
				parmas.coupon_buy_method = coupon.eventTypeCode == '5' ? '人民币+积分' : coupon
					.eventTypeCode == '4' ? '积分' : coupon.eventTypeCode == '1' ? '人民币' : '';
				parmas.coupon_buy_integral = coupon.points;
				parmas.coupon_buy_amount = coupon.cash;
			}
			senTrack('CouponReceive', parmas);
		}).catch(e => {
			console.error(e)
		});

		return res;
	}).catch(err => {
		// 埋点失败
		// 获取优惠券信息
		module.exports.getActiveDetails({
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}).then(res => {
			const coupon = res.data;
			const parmas = {
				page_name: getCurrentTitle(0), // 建议页面传
				coupon_id: coupon.typeId || '',
				coupon_name: coupon.typeName || '',
				coupon_type: coupon.useTypeName || '',
				coupon_use_method: coupon.eventTypeName || '',
				effect_time: coupon.eventEffDatetime || '',
				expire_time: coupon.eventExpDatetime || '',
				coupon_threshold_amount: coupon.condAmount > 0 ? coupon.condAmount : 0,
				coupon_amount: coupon.faceValue,
				coupon_buy_number: data.num,
				is_success: false,
				fail_reason: (err && err.msg) || (err && err.message) || JSON.stringify(err)
			}
			if (coupon.eventTypeCode == '5' || coupon.eventTypeCode == '4' || coupon
				.eventTypeCode == '1') {
				parmas.coupon_buy_method = coupon.eventTypeCode == '5' ? '人民币+积分' : coupon
					.eventTypeCode == '4' ? '积分' : coupon.eventTypeCode == '1' ? '人民币' : '';
				parmas.coupon_buy_integral = coupon.points * data.num;
				parmas.coupon_buy_amount = coupon.cash * data.num;
			}
			senTrack('CouponReceive', params);
		}).catch(e => {
			console.error(e)
		})

		return err;
	});
}

// 未支付取消回调
export const nopayCancel = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid,
		erpCustType,
		phone
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.cancel',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			mobile: phone,
			orderId: "1597836726593224704",
			cancelSource: '1',
			cancelRemark: '顾客自行取消',
			...data
		}
	})
}

// 未支付列表
export const nopayList = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.getorderlist',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			pageNo: 1,
			pageSize: 20,
			...data
		}
	})
}
// 未支付详情
export const nopayDetail = (id) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.getorderdetail',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			orderId: id
		}
	})
}

// 券激活接口
export const activeAccntNo = (id) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-coupon-admin/batchcoupon/activebyaccntno',
		method: 'post',
		data: {
			custId: erpCid,
			channel: 'SONGSHU',
			accntNo: id
		}
	})
}

// 退款申请
export const refundApply = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.refund_apply',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}
	})
}

// 券包退款所需接口
export const refundCheckpackage = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.refund_checkpackage',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}
	})
}

// 红包退款所需接口
export const refundCheckbalance = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.refund_checkbalance',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}
	})
}


// 二次支付调用(使用订单号)
export const payorder = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.payorder',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}
	})
}

// 通过订单退款
export const refund = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.refund_order',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}
	})
}


// 券列表显示活动
export const getpopevtscd = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-coupon-admin/evtscd/getpopevtscd',
		method: 'get',
		data: {
			entId: 0
		}
	})
}

// 券列表显示活动
export const getEvtscdList = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	data.entId = '0';
	data.custId = erpCid;
	data.status = data.status || '01,02,05';
	data.channel = 'SONGSHU'
	return request({
		url: '/sco-accnt-api/rest?method=sco.coupon.getlist',
		method: 'post',
		data
	})
}

// 可以转增的券
export const getcanpresentlist = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	data.entId = '0';
	data.custId = erpCid;
	data.channel = 'SONGSHU'
	return request({
		url: '/sco-accnt-api/rest?method=sco.coupon.getcanpresentlist',
		method: 'post',
		data
	})
}

// 券详情展示
export const getEvtscdDetails = (data = {}) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	data.custId = erpCid;
	data.channel = 'SONGSHU'
	return request({
		url: '/sco-accnt-api/rest?method=sco.coupon.getinfo',
		method: 'post',
		data
	})
}

// 通道券列表
export const vipTicket = (data = {}) => {
	return request({
		url: '/mallapi/couponuser/vipTicket',
		method: 'get',
		data
	})
}