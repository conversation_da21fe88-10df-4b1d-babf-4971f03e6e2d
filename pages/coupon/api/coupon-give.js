import { requestApi as request } from '@/utils/api.js'

// 创建赠送订单
export const createpresentbill = (data) => {
  const userInfo = uni.getStorageSync('user_info');
  const { erpCid, erpCustType, phone, headimgUrl, nickName } = userInfo;
  return request({
    url: '/sco-accnt-api/rest?method=sco.coupon.createpresentbill',
    method: 'post',
    data: {
      channel: 'SONGSHU',
      custId: erpCid,
      entId: '0',
      presentNickName: nickName,
      presentHeadUrl: headimgUrl,
      ...data
    }
  })
}

// 获取券转赠订单详情
export const getpresentorder = (data) => {
  const userInfo = uni.getStorageSync('user_info');
  const { erpCid, erpCustType, phone, headimgUrl, nickName } = userInfo;
  return request({
    url: '/sco-accnt-api/rest?method=sco.coupon.getpresentorder',
    method: 'post',
    data: {
      custId: erpCid,
      ...data
    }
  })
}

// 赠送订单列表
export const getpresentpage = (data) => {
  const userInfo = uni.getStorageSync('user_info');
  const { erpCid } = userInfo;
  return request({
    url: '/sco-accnt-api/rest?method=sco.coupon.getpresentpage',
    method: 'post',
    data: {
      custId: erpCid,
      pageSize: 10,
      ...data
    }
  })
}

// 领取分享起来的，领券
export const receivepresentcoupon = (data) => {
  const userInfo = uni.getStorageSync('user_info');
  const { erpCid, headimgUrl, nickName } = userInfo;
  return request({
    url: '/sco-accnt-api/rest?method=sco.coupon.receivepresentcoupon',
    method: 'post',
    data: {
      custId: erpCid,
      channel: 'SONGSHU',
      headUrl: headimgUrl,
      nickName,
      ...data
    }
  })
}

// 取消转赠领券
export const cancelpresentbill = (data) => {
  const userInfo = uni.getStorageSync('user_info');
  const { erpCid } = userInfo;
  return request({
    url: '/sco-accnt-api/rest?method=sco.coupon.cancelpresentbill',
    method: 'post',
    data: {
      custId: erpCid,
      ...data
    }
  })
}

// 获取券赠送未读信息
export const getpresentnoticeqty = () => {
  const userInfo = uni.getStorageSync('user_info');
  const { erpCid } = userInfo;
  return request({
    url: '/sco-accnt-api/rest?method=sco.coupon.getpresentnoticeqty',
    method: 'post',
    data: {
      custId: erpCid,
    }
  })
}

// 设置信息已读
export const readpresentnotice = () => {
  const userInfo = uni.getStorageSync('user_info');
  const { erpCid } = userInfo;
  return request({
    url: '/sco-accnt-api/rest?method=sco.coupon.readpresentnotice',
    method: 'post',
    data: {
      custId: erpCid
    }
  })
}




