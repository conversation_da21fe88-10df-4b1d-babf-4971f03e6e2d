<template>
  <view>
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
    	<block slot="backText">返回</block>
    	<block slot="content">转赠信息</block>
    </cu-custom>
    <view class="padding">
      <view class="padding-left-sm text-xdf">您将要转赠给好友：</view>
      <view class="text-center margin-top" style="position: relative;">
        <image src="https://img.songlei.com/-1/material/a6e7df29-d47f-4d86-98e2-3fd7c390373f.png" style="height: 378rpx;width: 664rpx;position: absolute;top: 0;left: 50%;transform: translateX(-50%);z-index: -1;"></image>
        <view class="flex flex-direction align-center justify-center" style="height: 210rpx;">
          <view class="color-red">
            <text class="text-xdf">￥</text>
            <text class="text-ssl line-height-1 text-bold" style="vertical-align: top;">{{ allValue }}</text>
          </view>
          <view>(共<text class="color-red">{{ checkList.length }}</text>张券)</view>
        </view>
        <view class="gift-word padding">
          <view class="text-bold text-xml padding-bottom" style="color: #A4785B;">送上您的转赠语</view>
          <view class="flex align-center justify-between margin-lr-lg margin-bottom-lg padding-bottom-lg text-xdf"  style="border-bottom: 1rpx solid #A4785B;" @click="check = 1">
            <view class="text-xdf">送一份心意只想和你分享</view>
            <view class="check-more">
              <image v-if="check === 1" src="https://img.songlei.com/-1/material/909454ee-2d0c-439c-b7c6-8d09654a68b7.png"></image>
            </view>
          </view>
          <view class="flex align-center justify-between margin-lr-lg margin-bottom-lg padding-bottom-lg text-xdf"  style="border-bottom: 1rpx solid #A4785B;" @click="check = 2">
            <view class="text-xdf">有爱的礼物送给可爱的你~</view>
            <view class="check-more">
              <image v-if="check === 2" src="https://img.songlei.com/-1/material/909454ee-2d0c-439c-b7c6-8d09654a68b7.png"></image>
            </view>
          </view>
          <view class="flex align-center justify-between margin-lr-lg margin-bottom-lg text-xdf" @click="check = 3">
            <view class="text-xdf">自定义祝福语({{ presentDesc.length }}/30)</view>
            <view class="check-more">
              <image v-if="check === 3" src="https://img.songlei.com/-1/material/909454ee-2d0c-439c-b7c6-8d09654a68b7.png"></image>
            </view>
          </view>
          <view class="margin-lr-lg text-left text-xdf">
            <textarea :value="presentDesc" class="textarea padding-sm" cols="30" rows="10" placeholder="点击此处输入" @input="changeInput"></textarea>
          </view>
          <view>
            <view class="next-btn" @click="nextBtn">下一步</view>
          </view>
        </view>
      </view>
    </view>
    <view class="show-zzc" v-if="showZZC" @click.self="close">
      <view @click.stop="" class="red-envelope-type">
        <view>
          <view 
            class="next-btn text-center text-xdf" 
            style="width: 660rpx;margin-top: 0;margin-bottom: 0;height: 108rpx;line-height: 108rpx;"
            @click.stop="lastBtn('1')"
          >拼手气<text class="text-xsm">（每人随机限领一张）</text></view>
          <view class="text-center padding-tb-xs" style="border-bottom: 1rpx solid #ddd;color: #FF6932;">适合赠送群好友们</view>
        </view>
        <view class="padding-tb-sm" style="border-bottom: 1rpx solid #ddd;">
          <view 
            class="next-btn text-center padding-bottom-sm text-xdf" 
            style="width: 660rpx;margin-top: 0rpx;margin-bottom: 0rpx;height: 108rpx;line-height: 108rpx;background: linear-gradient(180deg, #C09979, #CDA389, #D3B398);"
            @click.stop="lastBtn('2')"
          >普通送<text class="text-xsm">（先抢先得单人全领）</text></view>
        </view>
        <view class="padding-tb-sm">
          <view 
            @click="showZZC = false;"
            class="next-btn text-center padding-bottom-sm text-xdf" 
            style="margin-top: 0;margin-bottom: 0;background: #FAFAFA;color: #000000;border: 1rpx solid #F0F0F0;">取消</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { createpresentbill } from "@/pages/coupon/api/coupon-give.js"

export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
      check: 1,
      showZZC: false,
      checkList: [],
      presentDesc: ''
    }
  },
  onLoad() {
    var pages = getCurrentPages(); // 获取页面栈
    var currPage = pages[pages.length - 1]; // 当前页面
    var prevPage = pages[pages.length - 2]; // 上一个页面
    if(prevPage) {
      this.checkList = prevPage.$vm.checkList;
    }
  },
  computed: {
    allValue() {
      let value = 0
      this.checkList.map(item => {
        value += +item.amount
      })
      return (+value).toFixed(2)
    }
  },
  methods: {
    nextBtn() {
      this.showZZC = true;
    },
    lastBtn(presentMode) {
      const { check } = this;
      let presentDesc = this.presentDesc;
      if(+check === 1) {
        presentDesc = '送一份心意只想和你分享'
      } else if(+check === 2) {
        presentDesc = '有爱的礼物送给可爱的你~'
      } else if(+check === 3 && !presentDesc) {
        uni.showToast({
          title: '自定义祝福语不能为空',
          icon: 'none'
        })
        return;
      }
      uni.showLoading({ title: '加载中' })
      const accntNo = this.checkList.map(item => ({ accntNo: item.couponNo }))
      createpresentbill({
        presentMode,
        presentDesc,
        details: accntNo
      }).then(res => {
        uni.hideLoading();
        this.close();
        uni.navigateTo({
          url: '/pages/coupon/coupon-give-details/index?id=' + res.data
        })
      })
    },
    close() {
      this.showZZC = false;
    },
    changeInput(e) {
      const value = e.detail.value;
      const presentDesc = this.presentDesc.slice(0, 30);
      this.presentDesc = value;
      if(value.length > 30) {
        this.$nextTick(() => {
          this.presentDesc = presentDesc;
        })
        return;
      }
    }
  }
}
</script>

<style>
.gift-word {
  background-color: #fff;
  border-radius: 38rpx;
  position: relative;
  z-index: 1;
}
.check-more {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1rpx solid #BFBFBF;
  background-color: #eeeeee;
  position: relative;
}
.check-more image {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  top: -1rpx;
  left: 0rpx;
}
.textarea {
  width: 100%;
  height: 190rpx;
  border: 1rpx solid #E6D5C8;
  border-radius: 27rpx;
}
.next-btn {
  width: 370rpx;
  height: 80rpx;
  color: #fff;
  background: linear-gradient(180deg, #FF4E00, #FF8463);
  line-height: 80rpx;
  border-radius: 40rpx;
  margin: 50rpx auto 20rpx;
}
.color-red {
  color: #FF0000;
}
.show-zzc {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, .5);
  z-index: 10;
}
.red-envelope-type {
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
  border-radius: 40rpx 40rpx 0 0;
  padding: 40rpx;
  background-color: #ffffff;
  z-index: 1;
}
</style>