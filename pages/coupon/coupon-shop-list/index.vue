<template>
	<view class="flex flex-direction" style="width: 100vw;height: 100vh;">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">店铺优惠券</block>
		</cu-custom>
		<scroll-view
      class="flex-sub"
      refresher-enabled
      :scroll-y="true"
      :refresher-triggered="triggered"
      @refresherrefresh="refrePull"
    >
			<view class="cu-list padding-bottom-sm padding-top-sm">
				<template v-if="list.length">
					<view class="cu-item padding-xs bg-white" style="margin-bottom: 10upx;" v-for="(item, index) in list" :key="index">
						<coupon-offline-user-info :couponUserInfo="item" :status="offlineStatus">
						</coupon-offline-user-info>
					</view>
				</template>
			</view>
			<view :class="'cu-load  ' + (loadmore ? 'loading' : 'over')"></view>
		</scroll-view>
	</view>
</template>
<script>
	const app = getApp();
	import couponOfflineUserInfo from "../components/coupon-offline-user-info/index"
  import { getEvtscdList } from "@/pages/coupon/api/coupon.js"

	export default {
		data() {
			return {
        CustomBar: this.CustomBar,
        theme: app.globalData.theme, //全局颜色变量
				page: {
					current: 1,
					size: 10,
          counter: ''
				},
        triggered: false,
        list: []
			};
		},
		components: {
			couponOfflineUserInfo
		},
		props: {},
    onLoad(options) {
      if(options.counter) {
        this.page.counter = options.counter;
      }
			app.initPage().then(res => {
				this.getEvtscdList();
			});
		},
		onReachBottom() {
	
		},
		onPullDownRefresh() {
		
		},
		methods: {
      navTors() {
        uni.navigateTo({
          url: '/pages/coupon/coupon-activation/index'
        })
      },
      getEvtscdList(callback) {
        const { current, size, counter } = this.page;
        getEvtscdList({ current, size, status: '01', tenantCode: counter }).then(res => {
          let recordList = res.data.datalist.map(item => ({
            ...item, coupon_name: item.couponName,
            facevalue: item.balance || item.facevalue,
            usertype: item.useType,
            coupon_type_code: item.typeId,
            exp_date: item.expDate,
            eff_date: item.effDate,
            coupon_no: item.couponNo,
            simple_usage_rule: item.simpleUsageRule
          }));
          this.list = [...this.list, ...recordList];
          callback && callback(recordList)
        })
      },
      refrePull() {
        if (this._freshing) return;
        this._freshing = true;
        this.triggered = true;
        this.page.current = 1;
        this.getEvtscdList((data) => {
          this.list = data;
          this.triggered = false;
          this._freshing = false;
        });
      },
		}
	};
</script>

<style scoped>
  
</style>
