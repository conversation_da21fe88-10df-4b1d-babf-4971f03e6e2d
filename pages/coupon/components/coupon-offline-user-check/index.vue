<template>
  <view class="padding-tb-xs" @click="allBtn">
    <view class="flex radius text-white main electronic-coupons">
      <view class="discount-padding-sm radius t2-r" style="position: relative;z-index: 1;">
        <image
          class="discount-img"
          style="height: 178rpx;"
          :src="couponUserInfo.couponPicUrl || 'https://img.songlei.com/coupon/discount-offline.png'"
        ></image>
        <view class="flex">
          <view class="flex-sub" style="text-align: center">
            <view class="flex-sub flex">
              <view style="text-align: center; margin: 0 auto;height: 130rpx;line-height: 130rpx;">
                <text class="text-sl overflow-1 number text-bold margin-left-xs" v-if="couponUserInfo.useType === '04'">
                  {{ (couponUserInfo.amount  || couponUserInfo.facevalue) * 10 }}
                  <text style="font-size: 23rpx; font-weight: 400"> 折 </text>
                </text>
                <text class="text-xxl overflow-1 number text-bold" v-else-if="couponUserInfo.useType === '03' && couponUserInfo.showValue === 'N'">
                  {{ couponUserInfo.typeDesc.slice(0, 4)}}
                </text>
                <text class="text-sl overflow-1 number text-bold margin-left-xs" v-else>
                  {{ (couponUserInfo.amount | numFilter) || couponUserInfo.facevalue }}
                  <text style="font-size: 23rpx; font-weight: 400"> 元 </text>
                </text>
              </view>
            </view>

            <view class="line"></view>

            <view>
              <view class="text-xs overflow-1 padding-left-xs">
                {{ couponUserInfo.simpleUsageRule || '暂无规则' }}
              </view>
              <view class="text-xs" v-if="couponUserInfo.simple_usage_desc">
                {{couponUserInfo.simple_usage_desc || '暂无详情'}}
              </view>
            </view>
          </view>
        </view>
      </view>

      <view
        class="flex-sub discount-padding-sm shadow-blur radius t2-l"
        style="background: #fff"
      >
        <view
          class="text-df"
          style="color: #000000"
        >
          {{ couponUserInfo.couponName}}
        </view>
        
        <view
          class="text-sm margin-top-xs"
          style="color: #999"
        >
          {{
           couponUserInfo.useType == '02'
            ? '面值券'
            : couponUserInfo.useType == '03'
            ? '礼品券'
            : couponUserInfo.useType == '04'
            ? '折扣券'
            : couponUserInfo.useType == '05'
            ? '满减券'
            : couponUserInfo.useType == '01'
            ? '红包券'
            : couponUserInfo.useType == '09'
            ? '停车券'
            : couponUserInfo.useType == '10'
            ? '运费券'
            : ''
        }}
        </view>
        
        <view
          class="round"
          style="color: #cccccc"
          v-if="couponUserInfo.status == '02'"
        >已使用
        </view>

        <view
          class="round"
          style="color: #cccccc"
          v-if="couponUserInfo.status == '03'"
        >已过期
        </view>

        <view class="check-more">
          <image v-if="check" src="https://img.songlei.com/-1/material/909454ee-2d0c-439c-b7c6-8d09654a68b7.png"></image>
        </view>
        <view class="validity margin-top-sm">
          <view
            class="text-xs"
            style="color: #9b9c9d"
          >
            有效期至{{ couponUserInfo.expDate }}
          </view>
        </view>

        <image
          v-if="couponUserInfo.status == '2'"
          class="Invalid-img"
          src="https://img.songlei.com/coupon/Invalid.png"
        ></image>

        <view class="toUse">
        </view>
      </view>
    </view>
  </view>
  
</template>

<script>
import api from 'utils/api'

export default {
  data () {
    return {
      check: false
    };
  },

  components: {},
  props: {
    couponUserInfo: {
      type: Object,
      default: () => ({})
    }
  },
  filters: {
    numFilter (value) {
      // 截取当前数据到小数点后两位
      let realVal = Number(value).toFixed(1)
      // num.toFixed(2)获取的是字符串
      return Number(realVal) || 0
    },
  },
  methods: {
    allBtn() {
      this.check = !this.check;
      this.$emit('check', this.check, this.couponUserInfo);
    }
  }
};
</script>
<style>
.check-more {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #BFBFBF;
  position: absolute;
  right: 40rpx;
  top: 50%;
  transform: translateY(-50%);
  background-color: #eeeeee;
}
.check-more image {
  width: 36rpx;
  height: 36rpx;
  position: absolute;
  top: -1rpx;
  left: 0rpx;
}
</style>
