<template>
  <view class="t2-l radius" style="color: #000;">
    <view class="main electronic-coupons padding-lr padding-tb-sm">
      <view class="flex text-lg shadow-blur radius justify-between text-bold" style="width: 100%;">
        <view class="padding-bottom-xs">{{ couponUserInfo.type_name }}</view>
        <view>￥{{ couponUserInfo.balance }}</view>
      </view>
      <view class="flex-sub text-sm" style="color: #999;">
        <view>有效期 {{ couponUserInfo.eff_date }} ~ {{ couponUserInfo.exp_date }}</view>
      </view>
    </view>
  </view>
  
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  props: {
    couponUserInfo: {
      type: Object,
      default: () => ({})
    }
  },
}
</script>

<style scoped>

</style>