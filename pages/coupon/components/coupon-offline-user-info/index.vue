<template>
	<!-- <navigator :url="'/pages/coupon/coupon-offline-detail/index?id=' + couponUserInfo.couponNo" hover-class="none"> -->
	<view class="padding-tb-xs" @click.stop="handleToPage">
		<view class="flex radius text-white main electronic-coupons align-center">
			<view class="discount-padding-sm shadow-blur radius" style="width: 294rpx; z-index: 1; padding-right: 0">
				<image class="discount-img" style="height: 178rpx; width: 294rpx"
					:src="couponUserInfo.couponPicUrl || defaultImg" />
				<image v-if="isUseCoupon" src="https://img.songlei.com/live/coupon/new.png"
					style="position: absolute; top: 2rpx; left: 4rpx; width: 84rpx; height: 75rpx" />
				<view style="height: 130rpx; text-align: center">
					<view class="text-xxxl overflow-1 number text-bold" style="padding: 8rpx 0 0 140rpx"
						v-if="couponUserInfo.useType === '04'">
						<text v-if="couponUserInfo.amount > 0">{{ couponUserInfo.amount | accMulPrice }}</text>
						<text v-else>{{ couponUserInfo.facevalue | accMulPrice }}</text>
						<text style="font-size: 23rpx; font-weight: 400">折</text>
					</view>
					<view class="text-xxxl overflow-1 number text-bold" style="padding: 8rpx 0 0 140rpx"
						v-else-if="couponUserInfo.useType === '09'">
						<text>{{ couponUserInfo.facevalue }}</text>
						<text style="font-size: 23rpx; font-weight: 400">
							{{ couponUserInfo.facemode == '2' ? '元' : couponUserInfo.facemode == '3' ? '小时' : '' }}
						</text>
					</view>
					<view class="text-xdf text-bold" style="line-height: 36rpx"
						:style="couponUserInfo.typeDesc.length <= 4 ? 'padding: 24rpx 0 0 138rpx;' : 'text-align: left;padding: 0 0 0 148rpx;'"
						v-else-if="couponUserInfo.useType === '03' && couponUserInfo.showValue === 'N'">
						{{ couponUserInfo.typeDesc }}
					</view>

					<view class="text-xxxl overflow-1 number text-bold" style="padding: 0 0 0 140rpx"
						v-else-if="couponUserInfo.useType=='12'">
						<text :style="{
							fontSize: couponUserInfo.amount>1000?'38rpx': couponUserInfo.amount>100?'42rpx': '46rpx'
						}">{{ couponUserInfo.amount }}</text> <text style="font-size: 20rpx;">积分</text>
					</view>
					<view class="text-xxxl overflow-1 number text-bold" style="padding: 8rpx 0 0 140rpx" v-else>
						<text
							style="font-size: 24rpx; font-weight: 400; vertical-align: super; padding: 0 2rpx 0 0">¥</text>
						<text>{{ redPacketCoupon }}</text>
					</view>
				</view>

				<view style="text-align: center">
					<view class="text-xs overflow-1 padding-left-xs">
						{{ couponUserInfo.simpleUsageRule || '暂无规则' }}
					</view>
					<view class="text-xs" v-if="couponUserInfo.simple_usage_desc">
						{{ couponUserInfo.simple_usage_desc || '暂无详情' }}
					</view>
				</view>
			</view>

			<view class="flex-sub discount-padding-sm shadow-blur radius t2-l flex flex-direction justify-between"
				style="background: #fff">
				<view class="flex justify-between align-center">
					<view class="text-df overflow-2" style="color: #000000">
						{{ couponUserInfo.couponName }}
					</view>
					<text v-if="couponUserInfo.marketDim3"
						class="text-sm text-blue">{{ couponUserInfo.marketDim3 }}</text>
				</view>
				<view class="flex justify-between align-center">
					<view>
						<text v-if="useTypeText" class="text-sm margin-right-sm"
							style="color: #999">{{ useTypeText }}</text>
						<text class="text-sm" style="color: #cccccc" v-if="couponUserInfo.status == '02'">已使用</text>
						<text class="text-sm" style="color: #cccccc"
							v-else-if="couponUserInfo.status == '03'">已过期</text>
						<text class="text-sm" style="color: #cccccc"
							v-else-if="couponUserInfo.status == '05'">未生效</text>
						<text class="text-sm" style="color: #cccccc"
							v-else-if="couponUserInfo.status == '08'">未激活</text>
						<!-- <text class="text-sm" style="color: #cccccc" v-else>使用中</text> -->
						<!-- <text class="label" style="background: #CF2D45">线下劵</text> -->
						<text v-if="couponUserInfo.status === '01' && couponUserInfo.presentTimes"
							class="label-give text-df">赠</text>
						<text v-if="couponUserInfo.status === '07'" class="label-give-load text-xsm">赠送中</text>
						<text
							v-if="couponUserInfo.status !== '01' && couponUserInfo.status !== '07' && couponUserInfo.presentTimes"
							class="label-give text-df" style="background-color: #d0d0d0">
							赠
						</text>
					</view>
					<view v-if="couponUserInfo.expiringAmount" class="text-sm text-bold text-red"
						style="margin-right: 4rpx">{{couponUserInfo.useType=='12'? `${couponUserInfo.expiringAmount}积分即将过期`:`￥${couponUserInfo.expiringAmount}即将过期`}}</view>
				</view>

				<view class="action-layout flex justify-between" style="align-items: center; color: #9b9c9d">
					<view class="text-xs">
						<!-- 有效期至{{ couponUserInfo.expDate }} -->
						<view>{{ couponUserInfo.effDate.split(' ')[0] }}~{{ couponUserInfo.expDate.split(' ')[0] }}
						</view>
						<view>{{ couponUserInfo.effDate.split(' ')[1] }}~{{ couponUserInfo.expDate.split(' ')[1] }}
						</view>
					</view>
					<view class="to-use-btn" v-if="couponUserInfo.status === '01'">去使用</view>
					<view class="to-use-btn" v-if="couponUserInfo.status === '05'">使用规则</view>
					<!-- <view class="to-use-btn"  v-if="couponUserInfo.status === '08'" @click.stop="handleUseRules">
							激活说明
						</view> -->
				</view>

				<image v-if="couponUserInfo.status == '2'" class="Invalid-img"
					src="https://img.songlei.com/coupon/Invalid.png" />

				<view class="toUse"></view>
			</view>
		</view>
	</view>
	<!-- </navigator> -->
</template>

<script>
	import {
		accMul
	} from '@/utils/numberUtil';
	export default {
		data() {
			return {};
		},

		props: {
			couponUserInfo: {
				type: Object,
				default: () => ({})
			}
		},
		computed: {
			redPacketCoupon() {
				//业务要求按真实金额展示，不要截取
				// let realVal = Number(this.couponUserInfo.amount).toFixed(2)
				return this.couponUserInfo.amount || this.couponUserInfo.facevalue;
			},
			defaultImg() {
				const {
					status
				} = this.couponUserInfo;
				if (status == '01' || status == '05') {
					return 'https://img.songlei.com/coupon/discount-offline.png';
				}
				if (status == '02' || status == '03' || status == '04' || status == '06' || status == '07') {
					return 'https://img.songlei.com/coupon/discount-offline2.png';
				}
			},
			useTypeText() {
				const {
					useType
				} = this.couponUserInfo;
				if (useType == '01') {
					return '红包券';
				} else if (useType == '02') {
					return '面值券';
				} else if (useType == '03') {
					return '礼品券';
				} else if (useType == '04') {
					return '折扣券';
				} else if (useType == '05') {
					return '满减券';
				} else if (useType == '09') {
					return '停车券';
				} else if (useType == '10') {
					return '运费券';
				} else if (useType == '12') {
					return '定向积分';
				} else {
					return '';
				}
			},
			isUseCoupon() {
				const {
					status,
					srcDate
				} = this.couponUserInfo;
				if (status != '01') return false;
				if (status == '01' && !srcDate) return false;
				const startTime = new Date(srcDate).getTime();
				const currentTime = new Date().getTime();
				const timeElapsed = (currentTime - startTime) / (1000 * 60 * 60); // 计算时间差（小时）
				return timeElapsed <= 72; // 判断时间是否小于等于72小时
			}
		},
		filters: {
			accMulPrice: function(value) {
				return accMul(value, 10);
			}
		},
		methods: {
			handleToPage() {
				if (this.couponUserInfo.status === '08' && this.couponUserInfo.activeDesc) {
					uni.showModal({
						title: '提示',
						content: this.couponUserInfo.activeDesc || '请仔细查看激活说明',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/coupon/coupon-offline-detail-pus/index?id=' + this
										.couponUserInfo.couponNo
								});
							}
						}
					});
				} else {
					uni.navigateTo({
						url: '/pages/coupon/coupon-offline-detail-plus/index?id=' + this.couponUserInfo.couponNo
					});
				}
			}
		}
	};
</script>
<style scoped>
	.to-use-btn {
		width: 152rpx;
		background: #ffffff;
		border-radius: 28rpx;
		border: 0.9px solid #939395;
		font-weight: 500;
		font-size: 22rpx;
		color: #4d4d4d;
		line-height: 48rpx;
		padding: 2rpx 0;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.label-give {
		position: absolute;
		width: 62rpx;
		height: 51rpx;
		background-color: #d2a688;
		text-align: center;
		border-radius: 6rpx;
		line-height: 44rpx;
		clip-path: polygon(0 0, 100% 0, 100% 100%, 50% 42rpx, 0 100%);
		top: 0;
		right: 0;
	}

	.label-give-load {
		position: absolute;
		width: 106rpx;
		height: 40rpx;
		top: 0;
		right: 0;
		background-color: #d0d0d0;
		border-radius: 0 6rpx 0 20rpx;
		text-align: center;
	}
</style>