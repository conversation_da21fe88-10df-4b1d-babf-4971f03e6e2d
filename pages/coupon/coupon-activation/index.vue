<template>
	<view class="">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" id="cuCustom" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">激活</block>
		</cu-custom>
		<view style="width: 600upx;margin: 100upx auto 0;">
			<view class="search-form round flex" :style="{
          height: `40px`,
          lineHeight: '100%',
          marginRight:' 20rpx',
          marginLeft:' 0',
          border:' 1rpx solid #EAEAEA',
          background: '#fff',
        }" style="align-items: center;padding-left: 30upx;">
				<input class="uni-input" v-model="eventId" type="text" confirm-type="search" @confirm="activeAccntNo"
					placeholder="请输入要激活的券码号" :style="{ flex: 1 }" />
				<image :style="{  maxHeight: `48rpx`,width:`4rpx`}" mode="aspectFit"
					src="https://img.songlei.com/share/scan.png" style="height: 60rpx;width: 20rpx;"></image>
				<text @tap="scanCode" class="cuIcon-scan"
					style="font-size: 32rpx;padding: 0rpx 20rpx 0rpx 16rpx;"></text>
			</view>
		</view>
		<view class="activation-btn" @click="activeAccntNo">激活</view>
	</view>
</template>

<script>
	const app = getApp();
	import {
		activeAccntNo
	} from '@/pages/coupon/api/coupon.js'
	import util from '@/utils/util.js';
	// #ifdef APP-PLUS
	const mpaasScanModule = uni.requireNativePlugin("Mpaas-Scan-Module");
	// #endif
	import {
		mapState,
	} from 'vuex'
	export default {
		computed: {
			...mapState([
				'windowWidth',
				'HeightBar',
				'CustomBar',
				'menuWidth',
				'leftMenuWidth',
				'pixelRatio'
			])
		},
		data() {
			return {
				theme: app.globalData.theme,
				eventId: '',
				isTo: false,
				ishide: false
			}
		},
		onLoad(options) {
			if (options.scene) {
				const qrCodeScene = decodeURIComponent(options.scene);
				if (qrCodeScene) {
					const id = util.UrlParamHash(qrCodeScene, 'id');
					if (id) {
						this.eventId = id;
						this.isTo = true;
						this.activeAccntNo()
					} else {
						uni.showModal({
							title: '提示',
							content: '二维码错误，请选择正确的二维码',
							showCancel: false,
							success() {
								uni.switchTab({
									url: '/pages/home/<USER>'
								})
							}
						})
					}
					return;
				}
			}
			if (options.q) {
				const qrCodeScene = decodeURIComponent(options.q);
				if (qrCodeScene) {
					const id = util.UrlParamHash(qrCodeScene.split('?')[1], 'id');
					if (id) {
						this.eventId = id;
						this.isTo = true;
						this.activeAccntNo()
					} else {
						uni.showModal({
							title: '提示',
							content: '二维码错误，请选择正确的二维码',
							showCancel: false,
							success() {
								uni.switchTab({
									url: '/pages/home/<USER>'
								})
							}
						})
					}
					return;
				}
			}
			if (options.id) {
				this.eventId = options.id;
				this.isTo = true;
				this.activeAccntNo()
			}
		},
		methods: {
			scanCode() {
				// 允许从相机和相册扫码
				// #ifdef MP-WEIXIN
				uni.scanCode({
					scanType: ["barCode", "qrCode"], //所扫码的类型 barCode	一维码 qrCode	二维码
					success: (res) => {
						if (res.result) {
							const code = res.result;
							if (code.startsWith('https://shopapi.songlei.com/slshop-h5/ma/couponActivation')) {
								const id = util.UrlParamHash(code.split('?')[1], 'id');
								this.eventId = id;
								this.activeAccntNo()
							} else {
								uni.showToast({
									title: `二维码不正确`,
								});
							}
						} else {
							uni.showToast({
								title: `请重新扫描`,
							});
							return false;
						}
					}
				})
				// #endif   

				// #ifdef APP-PLUS
				// 允许从相机和相册扫码
				mpaasScanModule.mpaasScan({
					// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
					'scanType': ['qrCode', 'barCode'],
					// 是否隐藏相册，默认false不隐藏
					'hideAlbum': false
				}, (res) => {
					if (res.resp_code == 1000 && res.resp_message == 'success') {
						const code = res.resp_result; //result	所扫码的内容
						if (code && code.startsWith('https://shopapi.songlei.com/slshop-h5/ma/couponActivation')) {
							const id = util.UrlParamHash(code.split('?')[1], 'id');
							this.eventId = id;
							this.activeAccntNo()
						} else {
							uni.showToast({
								title: `未识别到内容`,
								icon:"none",
								duration: 2000
							});
						}
					} else {
						// uni.showToast({
						// 	title: `未识别到信息`,
						// 	duration: 2000
						// });
					}
				})
				// #endif   

			},
			activeAccntNo() {
				const that = this;
				const user_info = uni.getStorageSync('user_info');
				if (!user_info) {
					setTimeout(() => {
						this.activeAccntNo();
					}, 0)
					return;
				}
				const {
					id,
					erpCid
				} = user_info;
				if (!id) {
					uni.navigateTo({
						url: '/pages/login/index?reUrl=' + encodeURIComponent(
							'/pages/coupon/coupon-activation/index?id=' + this.eventId)
					})
					return;
				}
				if (!erpCid) {
					
					app.doLogin().then(res => {
						if (res === 'success') {
							const user = uni.getStorageSync('user_info');
							if (user.erpCid) {
								this.activeAccntNo();
							} else {
								uni.showModal({
									title: '提示',
									content: '登录错误',
									showCancel: false,
									success() {
										uni.navigateTo({
											url: '/pages/login/index?reUrl=' + encodeURIComponent(
												'/pages/coupon/coupon-activation/index?id=' +
												this.eventId)
										})
									}
								})
							}
						} else {
							uni.navigateTo({
								url: '/pages/login/index?reUrl=' + encodeURIComponent(
									'/pages/coupon/coupon-activation/index?id=' + this.eventId)
							})
						}
					})
					return;
				}
				const {
					eventId
				} = this;
				if (!eventId) {
					uni.showToast({
						title: '请先输入要激活的券号',
						icon: 'none'
					})
					return;
				}
				uni.showLoading({
					title: '激活中',
					mask: true
				})
				if (this.ishide) {
					return;
				}
				this.ishide = true;
				activeAccntNo(eventId).then(res => {
					const {
						code,
						msg,
						data = []
					} = res;
					if (+code === 0) {
						uni.showModal({
							title: '提示',
							content: '激活成功',
							showCancel: false,
							success() {
								if (that.isTo) {
									const datas = data.find(item => +item.popFlag);
									if (datas && +datas.popFlag === 1) {
										const {
											evtscd
										} = datas;
										uni.redirectTo({
											url: '/pages/coupon/coupon-user-list/index?key=' +
												evtscd
										})
									} else if (data.length === 1 && data[0].useType === '09') {
										uni.redirectTo({
											url: '/pages/coupon/coupon-user-list/index?navTabCur=2'
										})
									} else {
										uni.redirectTo({
											url: '/pages/coupon/coupon-user-list/index?navTabCur=1'
										})
									}
								}
							}
						})
					} else {
						uni.showModal({
							title: '提示',
							content: msg,
							showCancel: false,
							success() {
								if (that.isTo) {
									uni.switchTab({
										url: '/pages/coupon/coupon-user-list/index'
									})
								}
							}
						})
					}
				}).finally(() => {
					uni.hideLoading();
					this.ishide = false
				})
			}
		},
	}
</script>

<style>
	.activation-btn {
		width: 500upx;
		height: 70upx;
		font-size: 32upx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(90deg, #CDAD90 0%, #CDA185 100%);
		margin: 50upx auto;
		color: #fff;
		border-radius: 40upx;
	}
</style>