<template>
  <view
    class="goods-btn goods-use text-center"
    @click="btnNavCouponList"
    v-if="status === 'A' || status==='01'"
  >去使用</view>
  <view 
    v-else 
    class="goods-btn text-center" 
    :class="status !== 'Y' ? 'disable' : ''"
    @click="$noMultipleClicks(btnConvert)" 
    :style="styles"
    >
		<block v-if="status === 'N'">即将开抢</block>
		<block v-else-if="status === '03'">已达上限</block>
		<block v-else-if="status === '04' || status === '05' ">明天再来</block>
		<block v-else>{{codedict[eventTypeCode].name}}</block>
  </view>
</template>

<script>
export default {
  props: {
    eventPicUrl: {
      type: String,
      default: () => ''
    },
    status: {
      type: String,
      default: () => 'Y'
    },
    eventTypeCode: {
      type: String,
      default: () => '1'
    },
    style: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    styles() {
      const { bg, color } = this.codedict[this.eventTypeCode];
      let style = {
        background: bg,
        color
      }
      style = { ...style, ...(this.style || {}) }
      let styleStr = ''
      for(let i in style) {
        styleStr += `${i}: ${style[i]};`
      }
      return styleStr
    }
  },
  data() {
    return {
      codedict: {
        '1': {
          name: '立即购买',
          bg: 'linear-gradient(-90deg, #ED5279 0%, #FF0043 100%)',
          color: '#fff'
        },
        '2': {
          name: '立即抢券',
          bg: 'linear-gradient(-90deg, #27C085 0%, #29CA9F 100%)',
          color: '#fff'
        },
        '3': {
          name: '立即领取',
          bg: 'linear-gradient(90deg, #FF0245 1%, #ED5077 100%)',
          color: '#fff'
        },
        '4': {
          name: '立即兑换',
          bg: 'linear-gradient(90deg, #FFDE00 0%, #FFBD00 100%)',
          color: '#000'
        },
        '5': {
          name: '立即兑换',
          bg: 'linear-gradient(90deg, #FFDE00 0%, #FFBD00 100%)',
          color: '#000'
        },
      },
      noClick: true,
    }
  },
  methods: {
    btnConvert() {
      this.$emit('btnConvert');
    },
    btnNavCouponList() {
      uni.navigateTo({
        url: this.eventPicUrl ? this.eventPicUrl : '/pages/coupon/coupon-user-list/index?navTabCur=1'
      })
    }
  }
}
</script>

<style scoped>
.goods-btn {
  width: 150upx;
  height: 50upx;
  background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
  border-radius: 25upx;
  color: #fff;
  line-height: 50upx;
  font-size: 24upx;
}
.goods-use {
  background: #fff;
  border: 1upx solid #f00;
  color: #FF0000;
}
.disable {
  background: #ddd !important;
  color: #fff !important;
  pointer-events: none;
}
</style>