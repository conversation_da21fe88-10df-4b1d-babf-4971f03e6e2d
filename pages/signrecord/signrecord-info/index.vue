<template>
	<view>
		<cu-custom bgImage="https://img.songlei.com/live/point-sign/top.png" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">积分签到</block>
		</cu-custom>

		<view style="position: relative; height: 380rpx;">
			<image mode="widthFix" style="width: 750rpx" src="https://img.songlei.com/live/point-sign/bg.png">
			</image>
			<view class="integral-head">
				<view class="signrecord-top">
					<view class="flex" style="align-items: end;">
						<view class="cu-avatar round text-xl flex" style="width: 76rpx; height: 76rpx" :style="
							 userInfo.headimgUrl
							   ? 'background-image:url(' + userInfo.headimgUrl + ')'
							   : ''
						   ">
							{{ !userInfo.headimgUrl ? '头' : '' }}
						</view>
						<view class="text-sm text-white" style="margin-left: 20rpx;">
							<view v-if="userInfo" class="overflow-1">{{userInfo.erpCustTypename ||''}}:
								{{userInfo.nickName ||''}}
							</view>
							<navigator class="flex align-center" url="/pages/user/user-points-record/index"
								hover-class="none">
								<span>
									积分:
								</span>
								<view class="text-xl text-white" style="line-height: 40rpx;">
									{{ userInfo&&userInfo.pointsCurrent>-1?userInfo.pointsCurrent:0 }}
								</view>
							</navigator>
						</view>
					</view>
					<view>
						<view class="flex text-xsm text-white justify-end" style="margin-top: 3rpx;">
							<navigator class="rule-bg" url="/pages/user/user-points-record/index" hover-class="none">
								明细
							</navigator>
							<navigator class="rule-bg" url="/pages/micro-page/index?id=1624942147010723842"
								hover-class="none">
								规则
							</navigator>
						</view>
						
						<view class="text-sm" style="color: #634C38; margin-top: 18rpx;padding-right: 16rpx;">
							{{expireinfo||'积分清零日期：每年12月31日'}}
						</view>
					</view>
				</view>


				<view class="card">
					<view class="cu-steps steps-bottom signrecord-day">
						<view class="cu-item" v-for="(item, index) in signConfigList" :key="index"
							:id="'scroll-' + index">
							<view :class="index > scroll ? 'sign-bg' : 'sign-bg-selected'">
								<view class="text-xs">{{ item.posts }}</view>
								<!-- <view class="num" style="margin: 0 auto"></view> -->
								<image style="width: 36rpx; height: 45rpx;"
									:src="index > scroll ?'https://img.songlei.com/live/point-sign/gray-gold.png':'https://img.songlei.com/live/point-sign/gold.png'">
								</image>
							</view>
							<text class="font-weight text-xs" :class="index > scroll ?'text-black':'text-gray'"
								style="font-size: 22rpx">
								{{index > scroll ?  item.name :'已签到' }}
							</text>
						</view>
					</view>
					<view class="signrecord-btn text-sm" @tap="userSign">立即签到</view>
				</view>
			</view>
		</view>
		<view class="cu-modal" :class="showModal ? 'show' : ''">
			<view class="cu-dialog dialog-bg">
				<view class="bg-img">
					<view class="cu-bar justify-end text-white">
						<view class="action" @tap="hideModal">
							<text class="cuIcon-close"></text>
						</view>
					</view>

					<view class="sign-succeed">
						<view>
							<text class="text-red" style="font-size: 50rpx; color: #000000">
								恭喜您
							</text>
						</view>
						<view style="margin: 20rpx">
							获得
							<text class="text-red text-lg text-bold" style="font-size: 50rpx; padding: 10rpx">
								{{ signConfig.posts }}
							</text>
							积分
						</view>
					</view>

					<view class="action">
						<button class="cu-btn bg-yellow round shadow flex succeed-btn" @tap="hideModal">
							知道了
						</button>
					</view>
				</view>
			</view>
		</view>
		<!-- <recommendComponents canLoad /> -->


		<!-- 轮播 -->
		<view class="all-orders" v-if="moreImages&&moreImages.length">
			<swiper class="swiper" style="height:200rpx;" circular :indicator-dots="false" autoplay :interval="3000">
				<swiper-item v-for="(item,index) in moreImages" :key="index">
					<view v-if="item && item.linkUrl" @click="checkLogin(item.linkUrl)">
						<image :src="item.imgUrl" mode="widthFix"
							style="width: 100%;height:200rpx;border-radius: 20rpx;">
						</image>
					</view>
				</swiper-item>
			</swiper>
		</view>

    <view class="signrecord-name">
      <view class="line"></view>
      <view style="padding: 0 10rpx;">积分兑换专区</view>
      <view class="line"></view>
    </view>

		<view class="tabPiont" :style="{
      '--scale': tabindex == '0' ? 1: -1
    }">
      <view class="nav-tab">
        <view :class="tabindex == '0' ? 'hover': ''"  class="nav-tab-item" @click="tabactives('0')">积分兑礼</view>
        <view :class="tabindex != '0' ? 'hover': ''" class="nav-tab-item" @click="tabactives('-1')">积分兑券</view>
      </view>
			<scroll-view class="uni-swiper-tab" enable-flex scroll-x v-if="tabindex != '0'">
				<view style="display: flex;justify-content: space-around;">
					<!-- <view :class="[tabindex=='0' ? 'activetabstyle' : 'tabstyle']" @click="tabactive('0')">
						积分兑礼
					</view> -->
					<view :class="[tabindex=='-1' ? 'activetabstyle' : 'tabstyle']" @click="tabactive('-1')">
						松鼠美淘劵
					</view>
					<view :class="[tabindex=='1' ? 'activetabstyle' : 'tabstyle']" @click="tabactive('1')">
						松雷南岗店劵
					</view>
					<view :class="[tabindex=='-2' ? 'activetabstyle' : 'tabstyle']" @click="tabactive('-2')">
						松雷香坊店劵
					</view>
				</view>
			</scroll-view>

			<view class="model_scrollx flex_row">
				<scroll-view class="uni-swiper-tab" enable-flex scroll-x :scroll-into-view="'A_' + listScollIndex">
					<view 
						class="scrollx_items" 
						v-for="(item, index) in pointsGradinglist"
						:key="index"
            :id="'A_' + index"
					>
						<view
							:class="index === listScollIndex ? 'guige-active' : 'guige'"
							@click="tabindex == '1' || tabindex == '-2' ? getpointlevelList(item, index) : getPionList(item, index)" 
							:key="index"
						>{{item}}</view>
					</view>
				</scroll-view>
			</view>

			<scroll-view class="uni-swiper-tab" enable-flex scroll-x v-if="tabindex == '0'">
				<view style="display: flex;justify-content: space-around;">
					<view 
						v-for="(item, index) in integralStatus"
						:class="[index==tabCur ? 'activetabstyle' : 'tabstyle']"
            :key="index"
            @tap="tabSelect"
            :data-index="index"
            :data-key="item.key"
          >{{ item.value }}</view>
				</view>
			</scroll-view>
		</view>

		<view v-if="tabindex=='0'||tabindex=='-1'">
			<!-- 商品列表 -->
			<view class="cu-list shop-list">
				<view class="cu-item shop-item" v-for="(item, index) in pointsRecord" :key="index">
					<navigator class="content-ex" hover-class="none"
						:url="'/pages/goods/goods-detail/index?id=' + item.id + '&source_module=签到推荐'">
						<image mode="aspectFit" class="shop-img" :src="item.picUrls[0]"></image>
						<member-icon  v-if="item.memberLevelLimit&&item.memberLevelLimit!='101'" :memberLevelLimit = "item.memberLevelLimit"
						/>
						<view class="content">
							<view class="text-sm content-text">{{ item.name }}</view>
							<view
								style="border-radius: 20rpx;padding-left: 10rpx;padding-right: 10rpx;padding-bottom: 10rpx;position: relative;">
								<image class="image-jt"
									src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/jiPoitn.png" mode="">
								</image>
								<view class="flex space-between align-center "
									style="justify-content: space-between;padding-left: 27rpx; padding-right: 22rpx;">
									<view class="text-gray text-xsm" style="text-decoration: line-through;">
										{{ item.priceOriginalPoints?item.priceOriginalPoints:0 }}积分
									</view>
									<view style="display: flex;margin-top: 8rpx;margin-left: 30rpx;">
										<view class="text-lg" style="color: #FF5B01;">
											已省
										</view>
										<image
											src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%9B%BE%E5%B1%82%2099.png"
											mode="" style="width: 26rpx;height: 30rpx;margin-top: 4rpx;"></image>
									</view>
								</view>

								<view class="flex  align-center"
									style="justify-content: space-between;padding-left: 27rpx;padding-right: 48rpx;">
									<view class="flex" style="colro:#FF5B01;align-items: center;">
										<view class="text-xl" style="color: #FF5B01;font-weight: 700;font-size: 38rpx;">
											{{item.goodsPoints?item.goodsPoints:0 }}
										</view> <text style="color: #FF5B01;color: 26rpx;"> 积分</text>
									</view>
									<view>
										<view class="text-lg" style="color: #FF0000;font-weight: 700;">
											<text style="font-size: 20rpx;">￥</text>
											{{ item.provisionedPrice ? item.provisionedPrice : 0 }}
										</view>
										<!-- <text class="text-xs text-gray">积分</text> -->
									</view>
								</view>
							</view>
						</view>
					</navigator>
				</view>
			</view>
		</view>
		<view v-else style="margin-top: 26rpx;">
			<view class="flex padding-lr-sm align-center justify-between">
				<scroll-view scroll-x enable-flex scroll-with-animation :scroll-into-view="scollView"
					class="coupon-store-list flex" style="justify-content: space-between;">
					<block v-for="(item, index) in options" :key="item.groupCode">
						<view :id="`A${item.groupCode}`" style="padding: 0 30upx;" class="coupon-store"
							:class="{ 'hover': index === current }" @click="storeBtn(index)">{{item.groupName}}</view>
					</block>
				</scroll-view>
			</view>
			<view class="coupon-order" :class="'bg-' + theme.backgroundColor" @click="navTors">
				<image src="https://img.songlei.com/-1/material/31de3fd5-02b2-47bf-978e-e890da45a1b7.png"
					mode="aspectFill" style="width: 34upx;height: 34upx;"></image>
				<view style="margin-top: 10upx;">订单</view>
			</view>
			<view class="swiper_footer">
				<view class="swiper" :current="current" style="margin-top: 20rpx;">
					<view class="swiper-item" v-for="(n,index) in options" :key="n.groupCode">
						<!-- <scroll-view class="type-goods-list" style="padding-top: 20upx;" :scroll-y="true"
							 @scrolltolower="scrollBottom"> -->
						<view v-if="current==index">
							<block v-for="(item, i) in n.goodsList" :key="i">
								<coupon-centre-goods :goodsInfo="item" @success="(e) => {handleJump(e, n, i)}" />
							</block>
						</view>
					</view>
					<view :class="'cu-load bg-gray ' + (loadmores ? 'loading' : 'over')"></view>
				</view>
			</view>
		</view>
		<view v-if="tabindex=='0'" :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
	</view>
</template>

<script>
	// const util = require("utils/util.js");
	import {
		getpointlevel,
		pointsGrading
	} from "@/pages/shop/api/point";
	import recommendComponents from "components/recommend-components/index";
	import couponCentreGoods from '../coupon-centre-goods/index.vue'
	import memberIcon from '@/components/member-icon/index.vue';

	const util = require("utils/util.js");
	import {
		getCentreType,
		getTypeDetail,
		submitInfo
	} from "@/pages/coupon/api/coupon.js"

	const app = getApp();
	import api from '@/utils/api';
  
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				scroll: 0,
				signRecord: {},
				signConfigList: [],
				signConfig: {},
				// countTotal: '',//可用余额
				switch: true,
				canSign: false,
				showModal: false,
				userInfo: {},
				shareUser: {}, //分享
				//有数统计使用
				page_title: '积分签到',
				expireinfo: '',
				tabindex: "0",
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				pointsRecord: [],
				listScollIndex: 0, // 设置分档的下标
				pointsGradinglist: [],
				loadmore: true,
				loadmores: true,
				// ----积分兑换劵
				options: [], // 整体数据储存
				current: 0, // 当前门店index
				typeSwiper: [], // 临时充当得数据
				loading: false,
				triggered: false,
				val: '',
				couponIndex: 0,
				points: '',
				mktid: '',
				moreImages: '',
				tabCur: 0,
				integralStatus: [
					{ value: '全部', key: '',id:'' },
					{ value: '金卡专享', key: '102',id:'0' }, 
					{ value: '钻石专享', key: '103',id:'1' },
					{ value: '黑钻专享', key: '108',id:'2' },
					// { value: '积分专享', key: '101',id:'3' }
				],
			}
		},

		//分享朋友
		onShareAppMessage: function() {
			let shareUser = this.shareUser;
			let title = shareUser.name;
			let linkUrl = shareUser.linkUrl
			let imageUrl = shareUser.imgUrl + '-jpg_w360_q90' || '';
			const userInfo = uni.getStorageSync('user_info')
			let userCode = userInfo ? '&type=1&sharer_user_code=' + userInfo.userCode : ''
			let path = `${linkUrl}?userId=` + this.userInfo.id + userCode;
			console.log("onShareAppMessagepath", path);

			return {
				title: title ? title : null,
				path: linkUrl ? path : null,
				imageUrl: imageUrl ? imageUrl : null,
				success: function(res) {
					console.log(res.errMsg);
					uni.showToast({
						title: '分享成功'
					})
				},
				fail: function(res) { // 转发失败
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					})
				}
			};
		},

		components: {
			recommendComponents,
			couponCentreGoods,
			memberIcon
		},

		mounted() {
			getCentreType().then(res => {
				const {
					data
				} = res;
				this.jifen = JSON.parse(JSON.stringify(data));
				// this.options = this.transOption(data);
				this.options = this.temporary(data);
				// this.loopUser();
			});
			const customFiled = JSON.parse(this.theme.customFiled);
			console.log("this.theme.integralTab",customFiled);
			this.tabactive(customFiled.integralTab);
			
		},

		onLoad(options) {
			if (options.scene) {
				const qrCodeScene = decodeURIComponent(options.scene);
				if (qrCodeScene) {
					//接受二维码中参数  参数sf=XXX&id=XXX
					// const qrCodeSceneArray = qrCodeScene.split('&');
					const tabindex = util.UrlParamHash(qrCodeScene, 'tabindex');
					if (tabindex || tabindex == '0') {
						this.tabindex = tabindex
					} else {
						this.tabindex = '1'
					}
				}
			} else if (options.tabindex || options.tabindex == '0') {
				this.tabindex = options.tabindex
			} else {
				this.tabindex = '1'
			}

			this.getSignRecord();
			this.pointsRecordPage();
			this.signConfigPage()
			//分享朋友
			this.advertisement("SHARE_SIGNRECORD_INFO")
			this.getPointsgrading();
			this.getMoreImages();
		},

		onShow() {
			app.initPage().then(res => {
				this.userInfoGet();
			});
		},

		onReachBottom() {
			if ((this.loadmore && this.tabindex == '0') || this.loadmore && this.tabindex == '-1') {
				this.page.current = this.page.current + 1;
				this.pointsRecordPagelist(this.pointsGradinglist[this.listScollIndex], this.listScollIndex);
			} else {
				const {
					current
				} = this;
				const {
					children,
					current: index,
					pageCount
				} = this.options[current];
				if (+index === +pageCount) {
					return;
				}
				const item = children.find(item => item.hover);
				this.options[current].current += 1;
				this.getTypeDetail(item);
			}
		},

		methods: {
			tabSelect (e) {
				let dataset = e.currentTarget.dataset;
				console.log("dataset",dataset);
				
				if (dataset.index != this.tabCur) {
					this.tabCur = dataset.index;
					this.page.memberLevelLimit = dataset.key;
					this.refresh();
				}
			},

			refresh () {
				this.loadmore = true;
				this.pointsRecord = [];
				this.page.current = 1;
				this.listScollIndex = 0
				this.getPointsgrading()
			},

			handleJump(e, n, i) {
				const {
					eventTypeCode,
					eventId
				} = e;
				if (this.loading) {
					return;
				}
				this.loading = true;
				// 购买
				if (+eventTypeCode === 1 || +eventTypeCode === 4 || +eventTypeCode === 5) {
					uni.navigateTo({
						url: '/pages/coupon/coupon-centre-details/index?id=' + eventId
					})
					this.loading = false;
				} else {
					this.submitInfoClick(e)
				}
			},

			submitInfoClick(e) {
				const {
					eventTypeCode,
					eventId,
					eventPicUrl
				} = e;
				const params = {
					eventId
				}
				uni.showLoading({
					title: '领取中'
				})
				submitInfo(params).then(res => {
					uni.hideLoading()
					this.loading = false;
					if (+res.code === 0) {
						wx.showModal({
							title: '提示',
							content: +eventTypeCode === 3 ? '领取成功' : '抢券成功',
							icon: 'none',
							cancelText: '继续领券',
							confirmText: '去使用',
							success(res) {
								if (res.confirm) {
									uni.navigateTo({
										url: eventPicUrl ||
											'/pages/coupon/coupon-user-list/index?navTabCur=1'
									})
								} else if (res.cancel) {
									console.log('用户点击取消')
								}
							}
						})
						this.statusChange(eventId);
					} else if (+res.code === 90001) {
						uni.showModal({
							title: '提示',
							content: (res && res.msg) || '有未支付订单，取消后在支付',
							showCancel: false,
							success() {
								uni.navigateTo({
									url: '/pages/coupon/coupon-nopay-list/index'
								})
							}
						})
					}
				}).catch(err => {
					uni.hideLoading();
					this.loading = false;
				})
			},

			navTors() {
				uni.navigateTo({
					url: '/pages/coupon/coupon-nopay-list/index'
				})
			},

			//积分兑换劵点击事件
			getpointlevelList(val, index) {
				// 分档样式
				this.listScollIndex = index
				this.loadmores = true;
				this.val = val
				var jifen = JSON.parse(JSON.stringify(this.jifen))
				this.options = this.temporary(jifen);
				this.storeBtn(this.couponIndex);
			},

			getPionList(val, index) {
				// 分档样式
				if (this.tabindex == '0') {
					this.page.spuType = '3'
					this.page.current = 1
					this.pointsRecord = []
					this.listScollIndex = index
					this.pointsRecordPagelist(val, index)
					this.loadmore = true;
				} else if (this.tabindex == '-1') {
					this.page.current = 1
					this.pointsRecord = []
					this.listScollIndex = index
					this.pointsRecordPagelist(val, index)
					this.loadmore = true;
					this.page.spuType = '5'
				}
			},

			// 获取积分挡
			getPointsgrading() {
				let parms = {}
				if (this.tabindex == '0') {
					parms.spuType = '3'

				} else if (this.tabindex == '-1') {
					parms.spuType = '5'
				}
				pointsGrading(parms).then(res => {
					if (res.data) {
						// this.pointsGradinglist = ['全部']
						this.pointsGradinglist = ['全部', ...res.data]
						console.log(this.pointsRecord, 'pointsRecord')
						this.points = '全部'
						this.pointsRecordPagelist('全部', 0)
					}
				})
			},

			//积分兑换接口
			pointsRecordPagelist(val, index) {
				let value = val
				const _this = this
				// console.log(val, this.points, this.listScollIndex)
				if (this.listScollIndex != '0') {
					if (val != '全部' || this.points != '全部') {
						this.page.points = val || this.points
					} else {
						delete this.page.points
					}
				} else {
					delete this.page.points
				}

				api.pointsShoppingMallPage(Object.assign({}, this.page)).then(res => {
					let pointsRecord = res.data.records;
					if (this.listScollIndex == index) {
						this.pointsRecord = [...this.pointsRecord, ...pointsRecord];
						if (pointsRecord.length < _this.page.size) {
							this.loadmore = false;
						}
					}
				});
			},

      tabactives(index) {
        if(+this.tabindex === +index) {
          return;
        } else if(+this.tabindex !== 0 && +index !== 0) {
          return;
        }
        this.tabactive(index);
      },

			tabactive(index) {
					this.tabindex = index;
					const actions = {
							'0': {
									loadmore: true,
									page: { current: 1, spuType: 3, size: 10, searchCount: false, descs: 'create_time'},
									pointsRecord: [],
									listScollIndex: 0,
									tabCur:0,
									action: this.getPointsgrading
							},
							'1': {
									mktid: 201,
									loadmore: true,
									couponIndex: 0,
									current: 0,
									pointsRecord: [],
									pointsGradinglist: [],
									listScollIndex: 0,
									action: this.getpointlevel
							},
							'-1': {
									loadmore: true,
									page: { current: 1, spuType: 5, size: 10, searchCount: false, descs: 'create_time'},
									pointsRecord: [],
									listScollIndex: 0,
									action: this.getPointsgrading
							},
							'-2': {
									mktid: 203,
									loadmore: true,
									couponIndex: 0,
									current: 0,
									pointsRecord: [],
									pointsGradinglist: [],
									listScollIndex: 0,
									action: this.getpointlevel
							}
					};

					const action = actions[index];
					if (action) {
							Object.assign(this, action);
							action.action();
					}
			},

			// tab切换
			// tabactive(index) {
			// 	this.tabindex = index
			// 	if (index == '0') {
			// 		this.loadmore = true
			// 		this.page.current = 1
			// 		this.page.spuType = 3
			// 		this.pointsRecord = []
			// 		this.listScollIndex = 0
			// 		this.getPointsgrading()
			// 	} else if (index == '1') {
			// 		this.mktid = 201
			// 		this.loadmore = true
			// 		this.couponIndex = 0
			// 		this.current = 0
			// 		this.pointsRecord = []
			// 		this.pointsGradinglist = []
			// 		this.listScollIndex = 0
			// 		this.getpointlevel()
			// 	} else if (index == '-1') {
			// 		this.loadmore = true
			// 		this.page.current = 1
			// 		this.page.spuType = 5
			// 		this.pointsRecord = []
			// 		this.listScollIndex = 0
			// 		this.getPointsgrading()

			// 	} else if (index == '-2') {
			// 		this.mktid = 203
			// 		this.loadmore = true
			// 		this.couponIndex = 0
			// 		this.current = 0
			// 		this.pointsRecord = []
			// 		this.pointsGradinglist = []
			// 		this.listScollIndex = 0
			// 		this.getpointlevel()
			// 	}
			// },

			//获取劵的积分档
			getpointlevel() {
				let userInfo = uni.getStorageSync('user_info')
				getpointlevel({
					"channel": "SONGSHU",
					"custId": userInfo.erpCid,
					"position": "05",
					"eventTypeCode": "4,5",
					'mktid': this.mktid
				}).then(res => {
					if (res.data) {
						// this.pointsGradinglist=['全部']
						this.pointsGradinglist = ['全部', ...res.data]
						this.val = '全部'
						this.storeBtn(this.couponIndex);
					} else {
						this.pointsGradinglist = []
					}
				})
			},

			// 临时处理
			temporary(data) {
				const options = this.transOption(data);
				const typeList = options.filter(item => item.groupCode === '201')
				const typeSwiper = typeList[0].children;
				return typeSwiper.map(item => ({
					...item,
					groupCode: item.categoryCode,
					groupName: item.categoryName,
					children: [item],
					goodsList: [],
					pageCount: 1,
					current: 1
				}))
			},

			loopUser() {
				const user_info = uni.getStorageSync('user_info');
				if (!user_info) {
					setTimeout(() => {
						this.loopUser();
					}, 0)
					return;
				}
				const {
					id
				} = user_info;
				if (!id) {
					uni.navigateTo({
						url: '/pages/login/index?reUrl=' + encodeURIComponent('/pages/coupon/coupon-centre/index')
					})
					return;
				}
				this.storeBtn(0);
			},

			storeBtn(index) {
				console.log('执行了', index, this.options)
				this.current = index;
				this.options = this.options.map((item, indexs) => {
					item.hover = false;
					// console.log(indexs === index,'indexs,index')
					if (indexs === index) {
						item.hover = true;
						// if (!item.isShow) {
						item.isShow = true;
						this.typeSelect(item.children[0]);
						// }
					}
					return item;
				})
				// if(this.current == index){
				// 	this.storeBtn(index);
				// 	return
				// }
			},

			typeSelect(currentItem) {
				// console.log('执行了2')
				const arr = this.options[this.current].children;
				this.options[this.current].current = 1;
				this.options[this.current].children = arr.map(item => {
					item.hover = false;
					if (item === currentItem) {
						item.hover = true;
						this.options[this.current].goodsList = []
						this.getTypeDetail(item);
					}
					return item;
				})
			},

			// 获取积分兑换劵平台
			getTypeDetail(item, callback) {
				const {
					current
				} = this;
				const {
					current: pageNo
				} = this.options[current];
				const params = {
					pageNo,
					position: '05',
					"eventTypeCode": "4,5",
					'mktid': this.mktid
				}
				if (this.val != '全部') {
					params.minPoint = this.val
					params.maxPoint = this.val
				}
				if (item.categoryCode) {
					params.category = item.categoryCode
				}
				getTypeDetail(params).then(res => {
					const goodsList = this.options[this.current].goodsList;
					const dataList = res.data.dataList.map(item => {
						return item;
					})
					this.options[this.current].goodsList = [...(goodsList || []), ...res.data.dataList]
					this.options[this.current].pageCount = res.data.pageCount
					console.log(this.options[this.current].goodsList.length, res.data.rowsCount, 'options')
					if (this.options[this.current].goodsList.length <= res.data.rowsCount) {
						this.loadmores = false;
					}
					callback && callback()
				});
			},

			// 转化为
			transOption(data) {
				const obj = {};
				const arr = []
				data.map(item => {
					if (obj[item.groupCode]) {
						obj[item.groupCode].push(item);
					} else {
						obj[item.groupCode] = [item];
					}
				});
				for (const i in obj) {
					const item = obj[i];
					const {
						groupName,
						groupCode
					} = item[0];
					arr.push({
						groupName,
						groupCode,
						children: [{
							categoryCode: '',
							categoryName: '全部'
						}, ...item],
						goodsList: [],
						pageCount: 1,
						current: 1
					})
				}
				return arr
			},

			//分享
			advertisement(id) {
				api.advertisementinfo({
					bigClass: id
				}).then(res => {
					this.shareUser = res.data;
				});
			},

			//查询商城用户信息
			userInfoGet() {
				api.userInfoGet().then(res => {
					this.userInfo = res.data;
				});
				//分销设置
				api.distributionConfig().then(res => {
					if (res.data) {
						this.distributionConfig = res.data
					}
				});
			},

			//签到记录查询
			getSignRecord() {
				api.getSignRecord().then(res => {
					let signRecord = res.data;
					let str = signRecord.cumulateDays.toString()
					let j = 4 - str.length
					let beginStr = ''
					for (var i = 0; i < j; i++) {
						beginStr = beginStr + '0'
					}
					str = beginStr + str
					signRecord.cumulateDays = str
					this.signRecord = signRecord
					this.scroll = signRecord.continuDays - 1
					let updateTime = this.$moment(this.signRecord.updateTime).format("YYYY-MM-DD")
					let curDate = this.$moment().format("YYYY-MM-DD")
					console.log("curDate != updateTime", curDate != updateTime, "curDate==>", curDate,
						"updateTime==>", updateTime);
					if (curDate != updateTime) {
						this.canSign = true
					} else {
						this.canSign = false
					}
				});
			},

			pointsRecordPage() {
				api.erpPointsRecordPage({
					page: 1,
					pageSize: 15
				}).then(res => {
					this.expireinfo = res.data.expireinfo; //积分过期信息展示
					console.log(this.expireinfo, '积分清除信息------')
				});
			},

			//获取签到积分记录
			signConfigPage() {
				api.signConfigPage({
					searchCount: false,
					current: 1,
					size: 100,
					ascs: 'sort'
				}).then(res => {
					if (res.data.records) {
						this.signConfigList = res.data.records;
						console.log("signConfigList==>", this.signConfigList);
					}
				});
			},

			//点击签到
			userSign() {
				if (this.canSign && this.switch) {
					//开关锁 防止重复点击
					this.switch = false
					//throttle 节流函数
					util.throttle(this.getUserSign(), 1500)
				} else {
					uni.showToast({
						title: '今天签到已完成,请明天再来',
						icon: 'none'
					})
				}
			},

			//积分签到
			getUserSign() {
				api.userSign().then(res => {
					if (res.data) {
						this.signConfig = res.data
						this.switch = true
						this.getSignRecord()
						this.signConfigPage()
						this.showModal = true
					}
				});
			},

			hideModal() {
				this.showModal = false
			},

			// 获取多张图片
			getMoreImages() {
				api.listScoreimg().then(res => {
					this.moreImages = res.data
				})
			},

			checkLogin(url) {
				if (!util.isUserLogin()) {
					uni.navigateTo({
						url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
					})
					return
				} else {
					uni.navigateTo({
						url
					})
				}
			}

		}
	}
</script>
<style scoped>
	.image-jt {
		/* width: 338rpx;height: 108rpx;position: absolute;z-index: -11;
		  */

		width: 338rpx;
		height: 108rpx;
		position: absolute;
		z-index: -11;
		right: 88rpx;
		left: 10rpx;
	}

	.store-item {
		position: absolute;
		top: 0rpx;
		z-index: 999;
	}

	.list-top {
		display: flex;
		justify-content: space-between;
		margin: 26rpx;
	}



	.coupon-order {
		position: fixed;
		left: 0;
		bottom: 300upx;
		width: 100upx;
		height: 100upx;
		z-index: 2;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		font-size: 24upx;
	}

	.coupon-centre {
		display: flex;
		flex-direction: column;
		position: fixed;
		width: 100vw;
		height: 100vh;
	}

	.coupon-type {
		width: 173upx;
		height: 50upx;
		background: #fff;
		border-radius: 25upx;
		text-align: center;
		line-height: 50upx;
		color: #646464;
		font-size: 24upx;
		margin-right: 8upx;
		display: inline-block;
	}

	.coupon-type.hover {
		color: #fff;
		background: #CDAC90;
	}

	.coupon-store-list {
		flex: 1;
		white-space: nowrap;
		overflow: auto;
		height: 50upx;
	}

	.coupon-store {
		text-align: center;
		font-size: 26upx;
		color: #191919;
		height: 50upx;
		line-height: 40upx;
		padding: 0 20upx;
	}

	.coupon-store.hover {
		font-weight: bold;
		position: relative;
	}

	.coupon-store.hover::after {
		content: '';
		height: 5upx;
		width: 109upx;
		background-color: #CDAD90;
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
	}

	.bg-fff {
		background-color: #ffffff;
	}

	.scroll-view {
		width: 100%;
		height: 154upx;
		display: inline-block;
		white-space: nowrap;
	}

	.swiper {
		flex: 1;
	}

	.swiper-item {
		height: 100%;
	}

	.bg-pic {
		position: absolute;
		left: 0;
		top: 119upx;
		width: 100vw;
		height: 170upx;
		z-index: -1;
	}

	.store-type {
		position: absolute;
		width: 100vw;
		z-index: 1;
	}

	.type-goods-list {
		padding-top: 80upx;
		height: 100%;
	}
</style>
<style scoped lang="scss">
	.guige-active {
		color: red;
	}

	.banner-bg {
		position: relative;
		height: 220rpx;
		background-color: #fff;
		// background-image: url(https://img.songlei.com/user-point/integral-bg.png);
	}

	// .banner-img {
	//   width: 710rpx;
	//   // height: 210rpx;
	//   margin: 10rpx 0 0;
	// }
	.shop-list {
		display: flex;
		flex-wrap: wrap;
		padding: 0rpx 10rpx;

		.shop-item {
			background-color: #fff;
			border-radius: 30rpx;
			margin: 5rpx;

			.content-box {
				margin-bottom: 24rpx;

				.content-ex {
					width: 216rpx;
					height: 50rpx;
					background: linear-gradient(90deg, #fc0932 0%, #fc5560 100%);
					border-radius: 25rpx;
					text-align: center;
					font-size: 24rpx;
					color: #f7f7f7;
					line-height: 50rpx;
					margin: 0 auto;
				}
			}

			.content-text {
				width: 315rpx;
				height: 50rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				font-size: 28rpx;
				font-weight: 500;
				margin: 0 18rpx 18rpx;
				border-bottom: 1rpx dashed #eee;
				color: #000000;
			}

			.content-Price {
				color: #666666;
				font-size: 20rpx;
				height: 48rpx;
				padding-left: 18rpx;
				margin-right: 50rpx;
				text-decoration: line-through;
			}

			.shop-img {
				width: 356rpx;
				height: 356rpx;
				border-radius: 10px;
			}
		}
	}

	.text-size {
		font-size: 46rpx;

		.text-position {
			font-size: 22rpx;
			margin-left: 10rpx;
			position: relative;
			top: -6rpx;
		}
	}

	/* 二级菜单设置左右滑动 */
	.uni-swiper-tab {
		white-space: nowrap;
    background-color: #fff;
	}

	.model_scrollx {
		justify-content: space-between;
		/* background-color: #F1EFEF; */
		align-items: center;
		padding-left: 20rpx;
		padding-right: 20rpx;
    background-color: #fff;
    padding-bottom: 12rpx;
	}

	.scrollx_items {
		text-align: center;
		display: inline-block;
		min-width: 90rpx;
		box-sizing: border-box;
		// margin-left: 30rpx;
		margin-top: 3px;
		padding: 0 10rpx;
	}

	.scrollx_items:last-child {
		margin-right: 30rpx;
	}

	.scrollx_items image {
		width: 70rpx;
		height: 66rpx;
	}

	.tgyx_title {
		font-size: 28rpx;
		color: #333333;
		margin-top: 30rpx;
	}

	.tgyx_desc {
		font-size: 24rpx;
		margin-top: 10rpx;
	}
</style>

<style scoped>
	.activetabstyle {
		color: red;
		font-size: 28rpx;
	}

	.tabstyle {
		color: #000000;
		font-size: 28rpx;
	}

	.tabPiont {
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		/* background: white; */
		margin-top: 20rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		border-radius: 20rpx;
		/* padding-bottom: 12rpx; */
		margin-bottom: 20rpx;
    position: relative;
    --scale: 1;
    overflow: hidden;
	}
  .tabPiont::before {
    content: '';
    position: absolute;
    top: 0;
    background-image: url('http://img.songlei.com/client/mp/signrecord-bg.png');
    background-repeat: no-repeat;
    background-size: 100%;
    width: 100%;
    height: 100%;
    transform: scaleX(var(--scale));
  }

	.integral-head {
		position: absolute;
		top: 0;
		width: 710rpx;
		background-image: url("https://img.songlei.com/live/point-sign/sign-bg.png");
		background-size: 100% auto;
		background-repeat: no-repeat;
		margin: 20rpx;
		padding: 10rpx 10rpx 30rpx;
	}

	.signrecord-top {
		display: flex;
		justify-content: space-between;
		height: 100rpx;
	}

	.signrecord-bg {
		height: 1000rpx;
	}

	.signrecord-image {
		width: 750rpx;
		height: 443rpx;
	}

	.personal-information {
		margin-top: -250rpx;
	}

	.head {
		margin: 40rpx auto 0 auto;
	}

	.number-bg-top {
		width: 80rpx;
		height: 60rpx;
		border-radius: 10rpx 10rpx 0rpx 0rpx;
		background-color: #e7e1dd;
	}

	.number-bg-bottom {
		width: 80rpx;
		height: 60rpx;
		border-radius: 0rpx 0rpx 10rpx 10rpx;
		background-color: #ffffff;
	}

	.number-day {
		position: absolute;
		font-size: 3em;
		margin: 10rpx 0rpx 0rpx -22rpx;
	}

	.font-weight {
		font-weight: 300;
	}

	.signrecord-bg-2 {
		width: 94%;
		height: 620rpx;
		margin: -250rpx auto;
		border-radius: 15rpx;
	}

	.integral {
		width: 384rpx;
		height: 322rpx;
		margin: -150rpx auto 0rpx auto;
	}

	.signrecord-day {
		padding-bottom: 20rpx;
		overflow: scroll;
		padding-top: 22rpx;
	}

	.signrecord-btn {
		width: 329rpx;
		height: 60rpx;
		line-height: 60rpx;
		background: linear-gradient(-90deg, #FFBC00 0%, #FFDF00 100%);
		border-radius: 30rpx;
		text-align: center;
		margin: 0 auto 30rpx;
	}

	.check-record {
		margin: auto;
		font-weight: 300;
	}

	.dialog-bg {
		background-color: unset;
	}

	.bg-img {
		background-image: url('https://img.songlei.com/live/popover.png');
		height: 805rpx;
	}

	.sign-succeed {
		margin-top: 440rpx;
	}

	.succeed-btn {
		width: 50%;
		margin: 10rpx auto 0 auto;
	}

	.cu-steps.steps-bottom .cu-item::before,
	.cu-steps.steps-bottom .cu-item::after {
		left: calc(0px - (100% - 80rpx) / 2);
	}

	.rule-bg {
		width: 100rpx;
		height: 46rpx;
		line-height: 46rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 22rpx;
		text-align: center;
		margin-left: 20rpx;
	}

	.sign-bg {
		width: 90rpx;
		height: 90rpx;
		background: #F3F3F3;
		border-radius: 20rpx;
		color: #505050;
		padding-top: 4rpx;
	}

	.sign-bg-selected {
		width: 90rpx;
		height: 90rpx;
		background: linear-gradient(0deg, #624737 0%, #8C634A 100%);
		border-radius: 20rpx;
		color: #fff;
		padding-top: 4rpx;
	}

	.all-orders {
		width: 94% !important;
		margin: auto !important;
		margin-top: 15rpx !important;
		border-radius: 20rpx !important;
	}
  .signrecord-name {
    font-weight: bold;
    color: #000;
    text-align: center;
    margin-top: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
  }
  .signrecord-name .line {
    width: 30rpx;
    height: 3rpx;
    background-color: #000;
  }
  .nav-tab {
    height: 70rpx;
    display: flex;
    margin-bottom: 10rpx;
    align-items: center;
    justify-content: space-between;
    position: relative;
  }
  .nav-tab-item {
    color: #fff;
    width: 49.5%;
    text-align: center;
    font-weight: bold;
  }
  .nav-tab-item.hover {
    color: red;
  }
</style>