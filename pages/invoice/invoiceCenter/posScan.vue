<template>
  <view class="posScan-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">开票中心</block>
    </cu-custom>

    <view class="whole-box">
      <image class="tu" :src="iconPic.wait" />
      <view class="wen">获取订单开票信息中，请耐心等待~</view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/
const app = getApp();

export default {
  name: "posScan",
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量

      iconPic: {
        wait: "http://img.songlei.com/invoice/wait.png",
      },
    };
  },
  onLoad(options) {},
};
</script>

<style scoped lang="scss">
.posScan-page {
  .whole-box {
    width: 700rpx;
    height: 708rpx;
    background: #ffffff;
    border-radius: 30rpx;
    margin: auto;
    margin-top: 46rpx;
    text-align: center;
    .tu {
      margin-top: 144rpx;
      width: 149rpx;
      height: 159rpx;
    }
    .wen {
      margin-top: 60rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #000000;
    }
  }
}
</style>
