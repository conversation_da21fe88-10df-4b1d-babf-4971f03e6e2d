<template>
  <view class="invoiceDesc-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">发票详情</block>
    </cu-custom>

    <view class="whole-box">
      <div class="yanse-box" :class="'bg-' + theme.backgroundColor">
        <view class="wen">{{ stateTitle }}</view>
      </div>
      <view class="core-box">
        <view class="hang" v-for="(u, i) in list" :key="i">
          <view class="zou">{{ u.text || '暂无' }}</view>
          <view class="you">{{ u.val || '暂无' }}</view>
        </view>
        <view class="hang" v-if="2 == type">
          <view class="zou">失败原因</view>
          <view class="you" style="color: rgb(243, 38, 52)">{{ remark }}</view>
        </view>
      </view>
      
      <view class="yulan-box" v-if="1 == type" @click="goDownload">
        点击下载发票
      </view>
      <view class="btn-box">
        <!-- <block v-if="1 == type">
          <view class="btn btn2" style="width: 502rpx" @click="goWriteInvoice(2)">
            申请换开
          </view>
        </block> -->
        <block v-if="1 == type">
          <view class="btn btn2" style="width: 338rpx" @click="goInvoiceCenter">
            返回首页
          </view>
          <view class="btn btn2" style="width: 338rpx" @click="goAgainInvoice">
            重发发票
          </view>
        </block>
        <block v-if="2 == type">
          <view class="btn btn3" style="width: 502rpx" @click="goWriteInvoice()">
            申请开票
          </view>
        </block>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/
const app = getApp();
import { detailApi } from "@/pages/invoice/api/userinvoice.js";

export default {
  name: "invoiceDesc",
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      type: 0,
      stateTitle: "",
      id: "", //发票id
      list: [],
      callbackParameter:''
    };
  },
  onLoad(options) {
    this.id = options.id;
    this.getDetail();
  },
  methods: {
    getDetail() {
      detailApi(this.id).then((res) => {
        let {
          invoiceType,
          invoiceTitle,
          invoiceTaxNo,
          vatCompanyAddress,
          vatTelphone,
          vatBankName,
          vatBankAccount,
          invoiceAmount,
          notifyPhone,
          email,
          createTime,
          status,
          remark,
          invoicePdfUrl,
          scene,
          invoiceClassification,
        } = res.data;

        if (1 == invoiceType) {
          this.list = [
            { text: "发票抬头:", val: invoiceTitle },
            { text: "发票金额:", val: `￥${invoiceAmount}` },
            { text: "手机号码:", val: notifyPhone },
            { text: "电子邮箱:", val: email },
            { text: "开票时间:", val: createTime },
          ];
        } else {
          this.list = [
            { text: "发票抬头:", val: invoiceTitle },
            { text: "纳税人识别号: ", val: invoiceTaxNo },
            { text: "注册地址:", val: vatCompanyAddress },
            { text: "注册电话:", val: vatTelphone },
            { text: "开户银行:", val: vatBankName },
            { text: "开户行账号:", val: vatBankAccount },
            { text: "发票金额:", val: `￥${invoiceAmount}` },
            { text: "手机号码:", val: notifyPhone },
            { text: "电子邮箱:", val: email },
            { text: "开票时间:", val: createTime },
          ];
        }
        switch (status) {
          case 0:
          this.stateTitle = "开票中";
            break;
          case 1:
          this.stateTitle = "已开票";
            break;
          case 2:
          this.stateTitle = "开票失败";
            break;
          case 3:
          this.stateTitle = "申请中";
            break;
          case 4:
          this.stateTitle = "申请中";
            break;
          case 5:
          this.stateTitle = "申请中";
            break;
          case 6:
          this.stateTitle = "已红冲";
            break;
          case 8:
          this.stateTitle = "红冲中";
            break;
          case 9:
          this.stateTitle = "待手动重推";
          break;
        }
        this.type = status;
        this.remark = remark;
        let obj = {
          xid: this.id,
          invoiceAmount: invoiceAmount,
          scene:scene,
          orderInvoiceType:invoiceClassification
        };
        this.callbackParameter = JSON.stringify(obj);
        this.invoicePdfUrl = invoicePdfUrl;
      });
    },

    goDownload() {
      uni.showLoading({
        title: "加载中",
        mask: true,
      });
      uni.downloadFile({
        url: this.invoicePdfUrl,
        success(res) {
          uni.openDocument({
            filePath: res.tempFilePath,
            fileType: "pdf",
            showMenu: true,
            success() {
              uni.hideLoading();
            },
            fail(err) {
              uni.hideLoading();
              console.log("fail:" + JSON.stringify(err));
            },
          });
        },
        fail(err) {
          uni.hideLoading();
          console.log("fail:" + JSON.stringify(err));
        },
      });
    },

    goInvoiceCenter() {
      uni.navigateTo({
        url: "/pages/invoice/invoiceCenter/index",
      });
    },

    goAgainInvoice() {
      const idx = this.list.length - 2;
      const obj = this.list[idx];
      uni.navigateTo({
        url: `/pages/invoice/parkApply/againInvoice?invoiceId=${this.id}&toEmail=${obj.val}`,
      });
    },

    goWriteInvoice() {
      // 3和4,5都是申请中
      if (this.type ==3 || this.type ==4 || this.type ==5) {
				uni.showToast({
					title: '发票申请中，请勿重复申请！',
					icon: 'none',
					duration: 2000
				});
				return
			}
      // 失败转态
       // 将selData字符串转换为JSON对象
      let selDataObj = JSON.parse(this.callbackParameter);
      if(this.type==2){
        selDataObj.orderStatus = this.type
        this.callbackParameter = JSON.stringify(selDataObj)
      }
      uni.navigateTo({
        url: `/pages/invoice/invoiceCenter/writeInvoice?callbackParameter=${this.callbackParameter}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.invoiceDesc-page {
  .whole-box {
    .yanse-box {
      width: 750rpx;
      height: 180rpx;
      border-radius: 0 0 375rpx 375rpx / 0 0 60rpx 60rpx;
      .wen {
        padding-top: 26rpx;
        margin-left: 59rpx;
        font-size: 36rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #ffffff;
      }
    }
    .core-box {
      width: 700rpx;
      min-height: 556rpx;
      background: #ffffff;
      border-radius: 22rpx;
      padding: 20rpx 34rpx;
      margin: auto;
      margin-top: -80rpx;
      .hang {
        display: flex;
        margin-bottom: 34rpx;
        &:last-child {
          margin-bottom: 4rpx;
        }
        .zou {
          min-width: 190rpx;
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #8e8e90;
        }
        .you {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #000000;
        }
      }
    }
    .yulan-box {
      width: 502rpx;
      height: 100rpx;
      text-align: center;
      line-height: 100rpx;
      background: #ffffff;
      border-radius: 50rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #f22633;
      margin: auto;
      margin-top: 44rpx;
    }
    .btn-box {
      position: fixed;
      bottom: 0;
      width: 750rpx;
      height: 100rpx;
      display: flex;
      .btn {
        margin: auto;
        height: 80rpx;
        text-align: center;
        line-height: 80rpx;
        border-radius: 40rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 500;
      }
      .btn2 {
        background: #fff;
        color: #666;
      }
      .btn3 {
        background: #f42634;
        color: #fff;
      }
    }
  }
}
</style>
