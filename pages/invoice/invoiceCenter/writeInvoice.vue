<template>
  <view class="writeInvoice-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">申请开票</block>
    </cu-custom>

    <view class="whole-box">
      <view class="tou-box">
        <image class="yin" mode="aspectFit" :src="orderImg?orderImg:iconPic.tip" />
        <view class="xinxi">
          <view class="up">订单编号 {{ xid }}</view>
          <view class="down">
            <view class="wen">开票金额</view>
            <view class="xiao">￥</view>
            <view class="da">{{ invoiceAmount }}</view>
            <image class="tip" :src="iconPic.tip" @click="tipOpen" />
          </view>
        </view>
      </view>
      <view class="core-box">
        <view class="hang">
          <view class="qian">发票类型</view>
          <view class="hou">
            <view class="btn sel" style="width: 214rpx">电子普通发票</view>
          </view>
        </view>
        <view class="hang">
          <view class="qian">发票内容</view>
          <view class="hou">
            <block v-if="contentFlag">
              <view
                v-if="contentVal=='3'"
                class="btn"
                style="width: 162rpx"
                :class="[3 == contentVal ? 'sel' : 'unsel']"
                @click="toggleContent(3)"
              >
                礼品卡
              </view>
              <view
                v-if="contentVal!='3'"
                class="btn"
                style="width: 162rpx"
                :class="[1 == contentVal ? 'sel' : 'unsel']"
                @click="toggleContent(1)"
              >
                商品明细
              </view>
              <view
                class="btn"
                v-if="contentVal!='3'"
                style="width: 162rpx"
                :class="[2 == contentVal ? 'sel' : 'unsel']"
                @click="toggleContent(2)"
              >
                商品类别
              </view>
            </block>
            <block v-else>
              <view class="btn sel" style="width: 140rpx">礼品卡</view>
            </block>
          </view>
        </view>
        <view class="hang">
          <view class="qian">抬头类型</view>
          <view class="hou">
            <view
              class="btn"
              style="width: 116rpx"
              :class="[2 == invoiceType ? 'sel' : 'unsel']"
              @click="toggleType(2)"
            >
              单位
            </view>
            <view
              class="btn"
              style="width: 116rpx"
              :class="[1 == invoiceType ? 'sel' : 'unsel']"
              @click="toggleType(1)"
            >
              个人
            </view>
          </view>
        </view>
        <sel-title
          :invoiceType="invoiceType"
          @select="selectInvoiceTitle"
          @input="inputInvoiceTitle"
        />
        <view class="hang" v-if="2 == invoiceType">
          <view class="qian"><text style="color: red">*</text>单位税号</view>
          <view class="hou">
            <input
              class="wen"
              placeholder="请输入发票单位税号"
              v-model="invoiceTaxNo"
            />
          </view>
        </view>

        <block v-if="2 == invoiceType && moreFlag">
          <view class="kuai">更多信息</view>
          <view class="hang">
            <view class="qian">注册地址</view>
            <view class="hou">
              <input
                class="wen"
                placeholder="选填"
                v-model="vatCompanyAddress"
              />
            </view>
          </view>
          <view class="hang">
            <view class="qian">注册电话</view>
            <view class="hou">
              <input class="wen" placeholder="选填" v-model="vatTelphone" />
            </view>
          </view>
          <view class="hang">
            <view class="qian">开户银行</view>
            <view class="hou">
              <input class="wen" placeholder="选填" v-model="vatBankName" />
            </view>
          </view>
          <view class="hang">
            <view class="qian">银行账号</view>
            <view class="hou">
              <input class="wen" placeholder="选填" v-model="vatBankAccount" />
            </view>
          </view>
        </block>
        <view class="kuai">开票通知</view>
        <view class="hang">
          <view class="qian"><text style="color: red">*</text>手机号码</view>
          <view class="hou">
            <input
              class="wen"
              placeholder="请输入手机号码"
              v-model="notifyPhone"
            />
          </view>
        </view>
        <view class="hang">
          <view class="qian">
            <!-- <text style="color: red">*</text> -->
            电子邮箱
          </view>
          <view class="hou">
            <input
              class="wen"
              placeholder="请输入电子邮箱账号"
              v-model="email"
            />
          </view>
        </view>
        <view
          class="gengduo"
          v-if="2 == invoiceType && !moreFlag"
          @click="convert"
        >
          <text>展开</text>
          <image class="jt" :src="iconPic.jiantou" />
        </view>
      </view>
      <view
        class="gengduo2"
        v-if="2 == invoiceType && moreFlag"
        @click="convert"
      >
        <text>收起</text>
        <image class="jt" :src="iconPic.jiantou2" />
      </view>
      <view class="annniu" @click="saveOpen">提交申请</view>
      <view class="annniu2" @click="goback">不开发票</view>
    </view>

    <uni-popup
      ref="tipPopup"
      @maskClick="tipClose"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="neirong-box">
        <view class="toubu">发票须知</view>
        <view class="neirong">
          <view>
            1.开票金额为消费者实付款金额，优惠券礼金、积分、红包、礼品卡等不在开票范围内。
          </view>
          <view>2.如果订单发生退货退款，开票金额将变更为最终实付款金额。</view>
        </view>
        <view class="annniu" @click="tipClose">我知道了</view>
      </view>
    </uni-popup>

    <submit-confirm
      :invoiceData="invoiceData"
      @close="saveClose"
      v-if="saveFlag"
    />
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/
const app = getApp();
import selTitle from "@/components/invoice/selTitle/index.vue";
import submitConfirm from "@/components/invoice/submitConfirm/index.vue";
import { onlineOrderInvoice,offlineOrderInvoice,changeInvoice } from "@/pages/invoice/api/userinvoice.js";
import validate from 'utils/validate'

export default {
  name: "writeInvoice",
  components: {
    selTitle,
    submitConfirm,
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      xid: "",
      invoiceAmount: 0,

      contentFlag: true,
      contentVal: 1,
      invoiceType: 2,
      invoiceTitle: "",
      invoiceTaxNo: "",
      notifyPhone: "",
      email: "",
      vatCompanyAddress: "",
      vatTelphone: "",
      vatBankName: "",
      vatBankAccount: "",

      moreFlag: false,
      invoiceData: "",
      saveFlag: false,
      orderImg:'',
      iconPic: {
        tip: "http://img.songlei.com/gift/shop.png",
        jiantou: "http://img.songlei.com/invoice/jiantou.png",
        jiantou2: "http://img.songlei.com/invoice/jiantou2.png",
      },
      orderInvoice:{},//开票订单信息
      scene:'1',//开票场景
      offLineOrderId:'',//线下订单id
      replace:false,//换开
      orderStatus:'',//订单状态
      offLineOrder: ''
    };
  },
  onLoad(options) {
    if (options.callbackParameter) {
      // xid订单id, 
      // invoiceAmount 开票金额  
      // orderInvoiceType 订单商品类型 
      // orderImg 订单开票图片
      // orderStatus 订单状态

      const { xid, invoiceAmount,orderInvoiceType,orderImg,scene,replace,orderStatus} = JSON.parse(options.callbackParameter);

      // uni.showToast({title: xid})
      // let dataArray = xid.split(",");
      let dataArray;
      if (typeof xid === 'string') {
          dataArray = xid.split(",");
      } else {
          // 如果xid不是字符串类型，可以先将其转换为字符串
          let xidString = String(xid);
          dataArray = xidString.split(",");
      }

      console.log("dataArray",dataArray);
      // 多个订单开票
      if (dataArray.length > 1) {
        // this.xid = dataArray[0]
        // uni.showToast({title: xid})
        console.log(dataArray, '============dataArray===========')
        this.offLineOrderId = xid
        console.log("this.offLineOrderId",this.offLineOrderId);
      }else{
        this.xid = xid;
        console.log(xid, '============xid===========')
      }
      // 可开票金额 
      if(invoiceAmount){
        this.invoiceAmount = invoiceAmount;
      }
      // 订单图片
      if(orderImg){
        this.orderImg = orderImg;
      }
      // 场景值1：线上订单2：线下订单3：停车场订单
      if (scene) {
        this.scene = scene
      }
      // 换开票
      if(replace){
        this.replace = replace
      }

      // 失败转态传id 正常不需要
      if(orderStatus){
        this.orderStatus = orderStatus
      }

      // 传过来2就是礼品卡订单 
      if(orderInvoiceType){
        if(orderInvoiceType==='2'){
          this.contentVal = 3;//接口传参默认是3.对应礼品卡订单
        }else{
          this.contentVal = orderInvoiceType
        }
      }
    }
  },
  methods: {
    goback() {
      uni.navigateBack();
    },
    toggleContent(val) {
      this.contentVal = val;
    },
    toggleType(val) {
      this.invoiceType = val;
      this.invoiceTitle = "";
      this.invoiceTaxNo = "";
      this.vatCompanyAddress = "";
      this.vatTelphone = "";
      this.vatBankName = "";
      this.vatBankAccount = "";
      this.moreFlag = false;
    },

    //抬头名称
    selectInvoiceTitle(info) {
      if (2 == this.invoiceType) {
        this.invoiceTaxNo = "";
        this.vatCompanyAddress = "";
        this.vatTelphone = "";
        this.vatBankName = "";
        this.vatBankAccount = "";
        if (info) {
          const {
            invoiceTitle,
            invoiceTaxNo,
            vatCompanyAddress,
            vatTelphone,
            vatBankName,
            vatBankAccount,
          } = info;
          this.invoiceTitle = invoiceTitle;
          this.invoiceTaxNo = invoiceTaxNo;
          this.vatCompanyAddress = vatCompanyAddress;
          this.vatTelphone = vatTelphone;
          this.vatBankName = vatBankName;
          this.vatBankAccount = vatBankAccount;
        }
      } else {
        this.invoiceTitle = info.invoiceTitle;
      }
    },

    inputInvoiceTitle(val) {
      this.invoiceTitle = val;
    },

    // 展开收起
    convert() {
      this.moreFlag = !this.moreFlag;
    },

    // 发票须知
    tipOpen() {
      this.$refs.tipPopup.open();
    },

    tipClose() {
      this.$refs.tipPopup.close();
    },

    //提交
    saveOpen() {
      let str = "";
      if (!this.invoiceTitle.trim()) {
        str = "抬头名称不能为空哦~"; // 有抬头肯定就有单位税号
      } else  if (!validate.validateMobile(this.notifyPhone)) {
        str = "请先输入正确手机号码哦~";
      }else if (this.email) {
        // str = "请先输入电子邮箱哦~";
        const regex = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;
        if (!regex.test(this.email)) {
          str = "电子邮箱格式不正确哦~";
        }
      }
      if (str) {
        uni.showToast({
          title: str,
          icon: "none",
        });
        return;
      }

      this.invoiceData = {
        invoiceTitle: this.invoiceTitle,
        invoiceType: this.invoiceType,
        invoiceTaxNo: this.invoiceTaxNo,
        notifyPhone: this.notifyPhone,
        email: this.email,
      };
      this.saveFlag = true;
    },

    saveClose(flag) {
      this.saveFlag = flag;
      let that = this;
      if (flag) {
          let params = {
          scene: this.scene,
          invoiceType: this.invoiceType,
          invoiceAmount: this.invoiceAmount,
          callbackParameter: this.callbackParameter,
          invoiceTitle: this.invoiceTitle,
          notifyPhone: this.notifyPhone,
          email: this.email,
          invoiceClassification:this.contentVal
        };

        if (2 == this.invoiceType) {
          params.invoiceTaxNo = this.invoiceTaxNo;
          params.vatCompanyAddress = this.vatCompanyAddress;
          params.vatTelphone = this.vatTelphone;
          params.vatBankName = this.vatBankName;
          params.vatBankAccount = this.vatBankAccount;
        }

      // 线上开票
        if (this.scene==1) {
           // 只有失败和换开药传id
          if (this.orderStatus==2 || this.replace) {
            params.id = this.xid
          }
          
          //换开票
          if (this.replace) {
            this.handleInvoiceRequest(params, changeInvoice)
            return
          }
          
          if (uni.getStorageSync('orderInvoice')) {
            this.orderInvoice = uni.getStorageSync('orderInvoice')
          }

          // 正常和失败同一个接口，区分id是否传
          params.listOrderInfo = this.orderInvoice,
          this.handleInvoiceRequest(params, onlineOrderInvoice)

        }else

      // 线下开票
        if (this.scene==2) {
          params.callbackParameter = this.offLineOrderId || this.xid,
          this.handleInvoiceRequest(params, offlineOrderInvoice)
        }
      }
    },

    // 处理开票请求
    handleInvoiceRequest(params, invoiceFunction) {
      invoiceFunction(params)
        .then(() => {
          uni.navigateTo({
            url: "/pages/invoice/invoiceCenter/applyFinish",
          });
        })
        .catch(() => {
          this.saveFlag = false;
        });
    }
  },
};
</script>

<style scoped lang="scss">
.writeInvoice-page {
  .whole-box {
    .tou-box {
      width: 700rpx;
      height: 158rpx;
      margin: auto;
      margin-top: 22rpx;
      padding: 30rpx 24rpx;
      background: #ffffff;
      border-radius: 22rpx;
      display: flex;
      .yin {
        width: 96rpx;
        height: 96rpx;
        margin-right: 20rpx;
      }
      .xinxi {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .up {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #000000;
        }
        .down {
          display: flex;
          align-items: flex-end;
          .wen {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #000000;
            margin-right: 14rpx;
          }
          .xiao {
            font-size: 20rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #f12533;
          }
          .da {
            font-size: 40rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #f12533;
            height: 46rpx;
            line-height: 46rpx;
          }
          .tip {
            width: 27rpx;
            height: 27rpx;
            margin-left: 8rpx;
            margin-bottom: 4rpx;
          }
        }
      }
    }
    .core-box {
      margin: auto;
      margin-top: 20rpx;
      width: 700rpx;
      min-height: 806rpx;
      padding: 20rpx 24rpx;
      background: #ffffff;
      border-radius: 22rpx;
      .hang {
        display: flex;
        margin-top: 20rpx;
        .qian {
          text-align: right;
          width: 130rpx;
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #888888;
          margin-right: 22rpx;
        }
        .hou {
          width: 500rpx;
          height: 72rpx;
          border-bottom: 1rpx dashed #333;
          display: flex;
          .btn {
            height: 56rpx;
            line-height: 56rpx;
            text-align: center;
            border-radius: 28rpx;
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 500;
            margin-right: 22rpx;
          }
          .sel {
            background: #f12533;
            color: #fff;
          }
          .unsel {
            background: #f4f2f7;
            color: #000000;
          }
          .wen {
            width: 490rpx;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #000000;
          }
        }
      }
      .kuai {
        margin-top: 18rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: #888888;
      }
      .gengduo {
        width: 220rpx;
        height: 50rpx;
        text-align: center;
        line-height: 50rpx;
        background: #ffffff;
        border: 1px solid #999999;
        border-radius: 25rpx;
        margin: auto;
        margin-top: 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .jt {
          margin-left: 6rpx;
          width: 24rpx;
          height: 9rpx;
        }
      }
    }
    .gengduo2 {
      width: 220rpx;
      height: 50rpx;
      text-align: center;
      line-height: 50rpx;
      background: #ffffff;
      border: 1px solid #999999;
      border-radius: 25rpx;
      margin: auto;
      margin-top: 30rpx;
      margin-bottom: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .jt {
        margin-left: 6rpx;
        width: 24rpx;
        height: 9rpx;
      }
    }
    .annniu {
      margin: auto;
      margin-top: 20rpx;
      width: 700rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: linear-gradient(90deg, #f32633 0%, #ce1a26 100%);
      color: #ffffff;
      border-radius: 40rpx;
    }
    .annniu2 {
      margin: auto;
      margin-top: 20rpx;
      width: 700rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: #ffffff;
      color: #f32634;
      border-radius: 40rpx;
    }
  }
  .neirong-box {
    width: 630rpx;
    height: 411rpx;
    background: #ffffff;
    border-radius: 23rpx;
    padding-top: 30rpx;
    .toubu {
      text-align: center;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #000000;
    }
    .neirong {
      padding: 0 42rpx;
      margin-top: 28rpx;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #000000;
    }
    .annniu {
      margin: auto;
      margin-top: 48rpx;
      width: 335rpx;
      height: 60rpx;
      text-align: center;
      line-height: 60rpx;
      background: #ff0000;
      border-radius: 30rpx;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
    }
  }
}
</style>
