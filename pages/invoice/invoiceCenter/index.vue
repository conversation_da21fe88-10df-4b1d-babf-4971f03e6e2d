<template>
  <view class="invoiceCenter-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">发票中心</block>
    </cu-custom>

    <view class="whole-box">
      <view class="all-orders" v-if="MinImages && MinImages.length">
        <swiper
          class="swiper"
          style="height: 196rpx"
          circular
          :indicator-dots="false"
          autoplay
          :interval="3000"
        >
          <swiper-item v-for="(item, index) in MinImages" :key="index">
            <view v-if="item && item.linkUrl" @click="checkLogin(item.linkUrl)">
              <image
                :src="item.imgUrl"
                mode="widthFix"
                style="width: 100%; height: 196rpx; border-radius: 20rpx"
              >
              </image>
            </view>
          </swiper-item>
        </swiper>
      </view>
      <view class="core-box">
        <view class="ming">开发票</view>
        <view class="tu-outbox">
          <view class="tu-box">
            <image class="tu" :src="iconPic.xsdd" @click="goModule(1)" />
            <view class="zi">线上订单</view>
          </view>
          <view class="tu-box">
            <image class="tu" :src="iconPic.xxdd" @click="goModule(2)" />
            <view class="zi">线下订单</view>
          </view>
          <view class="tu-box">
            <image class="tu" :src="iconPic.tcjf" @click="goModule(3)" />
            <view class="zi">停车缴费</view>
          </view>
        </view>
      </view>
      <view class="core-box">
        <view class="ming">发票服务</view>
        <view class="tu-outbox">
          <view class="tu-box">
            <image class="tu" :src="iconPic.kpls" @click="goModule(4)" />
            <view class="zi">开票历史</view>
          </view>
          <view class="tu-box">
            <image class="tu" :src="iconPic.ttgl" @click="goModule(5)" />
            <view class="zi">抬头管理</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/
const app = getApp();
import api from "utils/api";
import util from "utils/util";

export default {
  name: "invoiceCenter",
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量

      MinImages: "",

      iconPic: {
        xsdd: "http://img.songlei.com/invoice/xsdd.png",
        xxdd: "http://img.songlei.com/invoice/xxdd.png",
        tcjf: "http://img.songlei.com/invoice/tcjf.png",
        kpls: "http://img.songlei.com/invoice/kpls.png",
        ttgl: "http://img.songlei.com/invoice/ttgl.png",
      },
    };
  },
  onShow() {
    // 登录控制
    app.initPage().then((res) => {
      this.MinImage();
    });
  },
  methods: {
    // 获取多张图片
    MinImage() {
      api.listMineimg().then((res) => {
        this.MinImages = res.data;
      });
    },
    checkLogin(url) {
      if (!util.isUserLogin()) {
        uni.navigateTo({
          url: "/pages/login/index?reUrl=" + encodeURIComponent(url),
        });
        return;
      } else {
        uni.navigateTo({
          url,
        });
      }
    },
    goModule(val) {
      let url = "";
      switch (val) {
        case 1:
          url = "/pages/invoice/invoiceCenter/onlineOrder";
          break;
        case 2:
          url = "/pages/invoice/invoiceCenter/offlineOrder";
          break;
        case 3:
          url = "/pages/invoice/parkApply/index";
          break;
        case 4:
          url = "/pages/invoice/parkApply/history";
          break;
        case 5:
          url = "/pages/invoice/userInvoice/index";
          break;
      }
      uni.navigateTo({
        url,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.invoiceCenter-page {
  .whole-box {
    .all-orders {
      width: 94% !important;
      margin: auto !important;
      margin-top: 15rpx !important;
      border-radius: 20rpx !important;
    }
    .core-box {
      width: 700rpx;
      margin: auto;
      margin-top: 20rpx;
      .ming {
        margin-left: 48rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #000000;
      }
      .tu-outbox {
        margin-top: 16rpx;
        width: 700rpx;
        height: 200rpx;
        background: #ffffff;
        border-radius: 22rpx;
        display: flex;
        align-items: center;
        .tu-box {
          margin-left: 36rpx;
          text-align: center;
          .tu {
            width: 70rpx;
            height: 70rpx;
          }
          .zi {
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #4a4a4a;
          }
        }
      }
    }
  }
}
</style>
