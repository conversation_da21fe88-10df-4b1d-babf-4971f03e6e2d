<template>
  <view class="offlineOrder-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">线下订单</block>
    </cu-custom>
    <view class="main-box" v-if="list && list.length > 0">
      <scroll-view
        scroll-y
        :style="{ 'max-height': `calc(100vh - ${CustomBar}px - 50px)` }"
        @scrolltolower="offlineGoodsList"
      >
        <checkbox-group @change="checkboxChange">
          <label class="cell-outbox" v-for="(u, i) in list" :key="i">
            <checkbox
              class="round red"
              :class="u.checked ? theme.themeColor + 'checked ':''"
              style="transform: scale(0.6)"
              :value="u.shbillno"
              :disabled="u.isno=='是'"
            />
            <view class="cell-box">
              <view class="shang-box">
                <view class="hang">
                  <view class="qian">购物门店</view>
                  <view class="hou2">{{ u.marketName }}</view>
                </view>
                <view class="hang">
                  <view class="qian">小票日期</view>
                  <view class="hou">{{ u.shsenddate }}</view>
                </view>
                <view v-if="u.moreFlag" class="hang">
                  <view class="qian">收银机号</view>
                  <view class="hou">{{ u.shsyjid }}</view>
                </view>
                <view v-if="u.moreFlag" class="hang">
                  <view class="qian">小票号</view>
                  <view class="hou">{{ u.shinvno }}</view>
                </view>
                <view class="hang">
                  <view class="qian">实付金额</view>
                  <view class="hou">
                    <format-price
                      styleProps="font-weight:bold"
                      signFontSize="20rpx"
                      smallFontSize="24rpx"
                      priceFontSize="28rpx"
                      color="#000000"
                      :price="u.shoughtpay"
                    />
                  </view>
                </view>
                <view class="hang">
                  <view class="qian">商品件数</view>
                  <view class="hou">{{ u.sdsl || 0 }}</view>
                </view>
                <block v-if="u.moreFlag">
                  <div
                    style="margin-top: 40rpx"
                    v-for="(u2, i2) in u.selldetailZtnewListGroup"
                    :key="i2"
                  >
                    <view class="hang">
                      <view class="qian">商品明细</view>
                      <view class="hou">{{ u2.gbcname }}</view>
                    </view>
                    <view class="hang">
                      <view class="qian">单价</view>
                      <view class="hou">
                        <format-price
                          styleProps="font-weight:bold"
                          signFontSize="20rpx"
                          smallFontSize="24rpx"
                          priceFontSize="28rpx"
                          color="#000000"
                          :price="u2.sdprice"
                        />
                      </view>
                    </view>
                    <view class="hang">
                      <view class="qian">数量</view>
                      <view class="hou">{{ u2.sdsl }}</view>
                    </view>
                    <!-- <view class="hang">
                      <view class="qian">折扣</view>
                      <view class="hou">{{ u2.sdzkje }}</view>
                    </view> -->
                    <view class="hang">
                      <view class="qian">小计</view>
                      <view class="hou">
                        <format-price
                          styleProps="font-weight:bold"
                          signFontSize="20rpx"
                          smallFontSize="24rpx"
                          priceFontSize="28rpx"
                          color="#000000"
                          :price="u2.sdcjje"
                        />
                      </view>
                    </view>
                  </div>
                  <!-- <div style="margin-top: 40rpx">
                    <view class="hang">
                      <view class="qian">交易类型</view>
                      <view class="hou">
                        {{ u.sellpaymentZtnewList[0].name }}
                      </view>
                    </view>
                    <view class="hang">
                      <view class="qian">收款机号</view>
                      <view class="hou">{{ u.shsyjid }}</view>
                    </view>
                    <view class="hang">
                      <view class="qian">实付金额</view>
                      <view class="hou">
                        <format-price
                          styleProps="font-weight:bold"
                          signFontSize="20rpx"
                          smallFontSize="24rpx"
                          priceFontSize="28rpx"
                          color="#000000"
                          :price="u.shoughtpay"
                        />
                      </view>
                    </view>
                  </div> -->
                  <view class="flex justify-between">
                      <view class="text-sm text-bold">可开票金额：</view>
                      <view class="hou">
                        <format-price
                          styleProps="font-weight:bold"
                          signFontSize="20rpx"
                          smallFontSize="24rpx"
                          priceFontSize="28rpx"
                          color="#000000"
                          :price="u.amountToBeInvoiced"
                        />
                      </view>
                  </view>
                </block>
              </view>
              <view class="xia-box">
                <view class="xian"></view>
                <view class="gengduo-box">
                  <view
                    class="gengduo"
                    v-if="!u.moreFlag"
                    @click.stop="convert(u, i)"
                  >
                    <text>{{ u.itemCount }}件商品</text>
                    <image class="jt" :src="iconPic.jiantou" />
                  </view>
                  <view
                    class="gengduo"
                    v-if="u.moreFlag"
                    @click.stop="convert(u, i)"
                  >
                    <text>收起</text>
                    <image class="jt" :src="iconPic.jiantou2" />
                  </view>
                  <view class="qita">待开发票</view>
                </view>
              </view>
            </view>
          </label>
        </checkbox-group>
        <view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')">
        </view>
      </scroll-view>
      <view class="anniu-box">
        <button
          class="anniu"
          :disabled="isShow"
          :style="{
            background: isShow
              ? '#E4E4E4':'linear-gradient(90deg, #f42634 0%, #ce1a26 100%)',
          }"
          @click="goWriteInvoice"
        >
          申请开票
        </button>
      </view>
    </view>
    <view v-else class="kong-box">
      <image :src="iconPic.empty"></image>
      <view class="tip">暂无可开发票的订单记录</view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/
const app = getApp();
import formatPrice from "@/components/format-price/index.vue";
import { offlineGoodsListApi, offlineGoodsDetail, getBills} from "@/pages/invoice/api/userinvoice.js";

export default {
  name: "offlineOrder",
  components: {
    formatPrice,
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,

      current: 1,
      size: 10,
      list: [],
      loadmore: true,
      callbackParameter: "",

      iconPic: {
        jiantou: "http://img.songlei.com/invoice/jiantou.png",
        jiantou2: "http://img.songlei.com/invoice/jiantou2.png",
      },
      selectedData:[],//选中数据
      selectedDataId:'',//选中商品id集合
      isShow:true,
      resData:0,
    };
  },
  onShow() {
    this.current = 1;
    this.list = [];
    this.loadmore = true;
    this.callbackParameter = "";
    app.initPage().then(() => {
      this.offlineGoodsList();
    })
  },

  watch: {
	},

  methods: {
     //控制按钮禁用
		selectedDataShow() {
      console.log("selectedData",this.selectedData.length,this.resData);
			if (this.selectedData.length > 0 && typeof this.resData !== 'undefined' && this.resData !== 0) {
				this.isShow = false;	
			} else {
				this.isShow = true;
			}
		},

    //获取显现可开票金额
    async getOfflineBills(){
      // 如果选中的商品数量大于1，则取合计总可开票金额
      // let data = this.selectedDataId.join(",");
      let data = this.selectedData.length ? this.selectedData.map(item => {
        return `${item.shmarket}-${item.shbillno}`
      }).join(",") : '';
      console.log("data", data);
      // 封装getBills方法为Promise
      const getBillsPromise = new Promise((resolve, reject) => {
          getBills({shBillNos: data})
              .then(res => {
                  console.log("getBills", res.data);
                  resolve(res.data);
              })
              .catch(error => {
                  reject(error);
              });
      });

      // 等待getBillsPromise的结果返回后再执行后续代码
      this.resData = await getBillsPromise;
      // 判断数据返回0，
      if (this.resData === 0) {
          uni.showToast({
              title: "订单可开金额为0，不能开取发票",
              icon: 'none'
          });
          this.isShow = true;
      } else {  
        // 将selData字符串转换为JSON对象
        let selDataObj = JSON.parse(this.selectedData[0].selData);
        // 修改invoiceAmount的值为新值（假设新值为200）保留两位数
        selDataObj.invoiceAmount = this.resData.toFixed(2);
        selDataObj.xid = data;
        console.log("data", selDataObj.xid);
  
        // 将修改后的JSON对象转换回字符串
        this.callbackParameter = JSON.stringify(selDataObj);
      }
      this.selectedDataShow()
    },

    offlineGoodsList() {
      if (this.loadmore) {
        const params = {
          current: this.current,
          size: this.size,
        };
        offlineGoodsListApi(params).then((res) => {
          // res.data.rows = [
          //   {
          //       isbill: 0,
          //       itemCount: 0,
          //       jygs: "002",
          //       marketName: "松雷-香坊店",
          //       sdsl: null,
          //       selldetailZtnewListGroup:null,
          //       shbillno: "6373695",
          //       shcustno: "0000019316",
          //       shdate: "2024-03-28",
          //       shdjlb: "1",
          //       shinvno: 1000128,
          //       shmarket: "203",
          //       shoughtpay: 499,
          //       shsenddate: "2024-03-28",
          //       shsyjid: "2320086",
          //   },
          //   {
          //     isbill: 0,
          //     itemCount: 0,
          //     jygs: "002",
          //     marketName: "松雷-香坊店",
          //     sdsl: null,
          //     selldetailZtnewListGroup: null,
          //     shbillno: '6373758',
          //     shcustno: "0000019316",
          //     shdate: "2024-03-28",
          //     shdjlb: "1",
          //     shinvno: 1000156,
          //     shmarket: "203",
          //     shoughtpay: 378,
          //     shsenddate: "2024-03-28",
          //     shsyjid: "2320066",
          //   },
          //   {
          //     isbill: 0,
          //     itemCount: 0,
          //     jygs: "002",
          //     marketName: "松雷-香坊店",
          //     sdsl: null,
          //     selldetailZtnewListGroup: null,
          //     shbillno: '6373684',
          //     shcustno: "0000019316",
          //     shdate: "2024-03-28",
          //     shdjlb: "1",
          //     shinvno: 1000049,
          //     shmarket: "203",
          //     shoughtpay: 240,
          //     shsenddate: "2024-03-28",
          //     shsyjid: "2320071",
          //   },
          //   {
          //     isbill: 0,
          //     itemCount: 0,
          //     jygs: "002",
          //     marketName: "松雷-香坊店",
          //     sdsl: null,
          //     selldetailZtnewListGroup: null,
          //     shbillno: '6373725',
          //     shcustno: "0000019316",
          //     shdate: "2024-03-28",
          //     shdjlb: "1",
          //     shinvno: 1000020,
          //     shmarket: "203",
          //     shoughtpay: 450,
          //     shsenddate: "2024-03-28",
          //     shsyjid: "2310067",
          //   }
          // ];
        console.log("res.data",res.data);
          res.data.rows.forEach((u) => {
            let obj = {
              xid: u.shbillno,
              invoiceAmount: u.amountToBeInvoiced,
              scene:2
            };
            u.selData = JSON.stringify(obj);
            u.moreFlag = false;
          });
          let newArray = JSON.parse(JSON.stringify(res.data.rows));
          let list = newArray;
          console.log("list", list);
          this.list = [...this.list, ...list];
          if (list.length < this.size) {
            this.loadmore = false;
          } else {
            this.current++;
          }
        });
      }
    },

    //单个商品勾选点击
		async checkboxChange(e) {
      console.log("");
      const items = this.list
      const data = e.detail.value
      let values = data.map(item => parseInt(item));
      this.selectedDataId = values
      console.log("e", e, values,items);
      for (let i = 0, lenI = items.length; i < lenI; ++i) {
        items[i].checked = false

        for (let j = 0, lenJ = values.length; j < lenJ; ++j) {
          if (items[i].shbillno === values[j]) {
            items[i].checked = true
            break
          }
        }
      }
      // const selectedIds = e.detail.value;
      console.log("1111",this.selectedData);
      this.selectedData = this.list.filter(item => values.includes(item.shbillno) && item.checked);
      if (this.selectedData && this.selectedData.length>0) {
        this.getOfflineBills()
      }else{
        this.isShow = true;
      }
      console.log("2222",this.selectedData);
      console.log("list==>items",items,this.callbackParameter);
    },

    convert(info, idx) {
      console.log("info",info,'idx',idx);
      this.list.forEach((u) => {
        if (u.moreFlag !== info.moreFlag) {
          u.moreFlag = false;
        }
      });
      info.moreFlag = !info.moreFlag;
      if (info.moreFlag) {
        this.offlineGoodsDetail(info, idx);
      }
    },

    async offlineGoodsDetail(info, index) {
      const params = {
        id: info.shbillno, 
        shmarket: info.shmarket
      }
      const res = await offlineGoodsDetail(params);
      console.log("res",res.data);
      let dataList = res.data;
      // dataList ={
      //   amountToBeInvoiced: 378,
      //   isbill: 0,
      //   itemCount: null,
      //   jygs: "002",
      //   marketName: null,
      //   sdsl: 1,
      //   selldetailZtnewListGroup: [
      //     {
      //       amountToBeInvoiced: 378,
      //       gbcname: "曼妮芬 订金码",
      //       gbspec: "",
      //       gmpxstax: 0.13,
      //       jygs: "002",
      //       sdcatid: "104002",
      //       sdcjje: 378,
      //       sdgdid: "236824",
      //       sdprice: 378,
      //       sdsl: 1,
      //       shbillno: "6373758",
      //       shmarket: "203",
      //       vsellpaymentZtnewList:[]
      //     },
      //     {
      //       amountToBeInvoiced: 378,
      //       gbcname: "曼妮芬 订金码",
      //       gbspec: "",
      //       gmpxstax: 0.13,
      //       jygs: "002",
      //       sdcatid: "104002",
      //       sdcjje: 378,
      //       sdgdid: "236824",
      //       sdprice: 378,
      //       sdsl: 1,
      //       shbillno: "6373758",
      //       shmarket: "203",
      //       vsellpaymentZtnewList:[]
      //     },
      //   ],
      //   shbillno: 6373758,
      //   shcustno: "0000019316",
      //   shdate: "2024-03-28",
      //   shdjlb: "1",
      //   shinvno: 1000156,
      //   shmarket: "203",
      //   shoughtpay: 378,
      //   shsenddate: "2024-03-28",
      //   shsyjid: "2320066",
      // } 
      let {selldetailZtnewListGroup,amountToBeInvoiced,shbillno} = dataList;
      let obj = {
              xid: shbillno,
              invoiceAmount: amountToBeInvoiced,//开票金额
              orderInvoiceType:'3',// 3 线下订单
            };
      let selData = JSON.stringify(obj);
      this.$set(this.list[index], "selData", selData);
      this.$set(this.list[index], "selldetailZtnewListGroup", selldetailZtnewListGroup);//商品明细
      this.$set(this.list[index], "amountToBeInvoiced", amountToBeInvoiced); //可开票金额

    },

    goWriteInvoice() {
      if(this.callbackParameter){
        uni.navigateTo({
          url: `/pages/invoice/invoiceCenter/writeInvoice?callbackParameter=${this.callbackParameter}`,
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.offlineOrder-page {
  .main-box {
    padding-bottom: 165rpx;
    .cell-outbox {
      margin-top: 24rpx;
      display: flex;
      align-items: center;
      .cell-box {
        width: 666rpx;
        min-height: 350rpx;
        background: #ffffff;
        border-radius: 22rpx;
        .shang-box {
          padding: 24rpx;
          padding-bottom: 6rpx;
          .hang {
            display: flex;
            margin-bottom: 8rpx;
            .qian {
              min-width: 160rpx;
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #888888;
            }
            .hou {
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #000000;
            }
            .hou2 {
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 800;
              color: #000000;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
        .xia-box {
          margin-bottom: 20rpx;
          .xian {
            width: 618rpx;
            border-top: 2rpx dotted #333;
            margin: auto;
            margin-bottom: 18rpx;
          }
          .gengduo-box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            .gengduo {
              width: 253rpx;
              height: 62rpx;
              line-height: 62rpx;
              background: #ffffff;
              border: 1px solid #999999;
              border-radius: 31rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #000000;
              .jt {
                margin-left: 6rpx;
                width: 24rpx;
                height: 9rpx;
              }
            }
            .qita {
              margin: 0 24rpx;
              width: 190rpx;
              height: 62rpx;
              text-align: center;
              line-height: 62rpx;
              background: #ffffff;
              border: 1px solid #999999;
              border-radius: 31rpx;
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #000000;
            }
          }
        }
      }
    }
    .anniu-box {
      position: fixed;
      bottom: 0;
      width: 750rpx;
      height: 100rpx;
      .anniu {
        margin: auto;
        margin-top: 10rpx;
        width: 700rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #fff;
      }
    }
  }

  .kong-box {
    margin-top: 130rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    image {
      width: 250rpx;
      height: 250rpx;
    }

    .tip {
      font-size: 28rpx;
      margin-top: 28rpx;
    }
  }
}
</style>
