<template>
  <view class="onlineOrder-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">线上订单</block>
    </cu-custom>

    <scroll-view
      scroll-x
      class="bg-white nav collection-types"
      style="box-shadow: none"
    >
      <view
        class="flex text-center"
        style="background-color: #eee"
      >
        <view
          class="radius-lg margin-tb margin-lr-sm padding-lr-lg bg-white-black"
          :class="'btn' +
            (index == tabCur ? 'cur bg-' + theme.backgroundColor : '')
          "
          v-for="(item, index) in collectType"
          :key="index"
          @tap="tabSelect"
          :data-index="index"
          :data-key="item.key"
        >{{ item.value }}</view>
      </view>
    </scroll-view>

    <view class="main-box" v-if="list && list.length > 0">
      <scroll-view
        scroll-y
        :style="{ 'max-height': `calc(100vh - ${CustomBar}px - 50px)` }"
        @scrolltolower="onLineOrder"
      >
        <checkbox-group @change="checkboxChange">
          <label class="cell-outbox" v-for="(u, i) in list" :key="i">
            <checkbox
              class="round red"
              :class="u.checked ? theme.themeColor + 'checked ':''" 
              style="transform: scale(0.6)"
             
              :value="u.id"
            />
            <view class="cell-box">
              <view class="shang-box">
                <view class="hang">
                  <view class="qian">购物店铺</view>
                  <view class="hou2">{{ u.shopInfo.name }}</view>
                </view>
                <view class="hang">
                  <view class="qian">下单日期</view>
                  <view class="hou">{{ u.createTime }}</view>
                </view>
                <view class="hang">
                  <view class="qian">实付金额</view>
                  <view class="hou">
                    <format-price
                      styleProps="font-weight:bold"
                      signFontSize="20rpx"
                      smallFontSize="24rpx"
                      priceFontSize="28rpx"
                      color="#000000"
                      :price="u.paymentPrice"
                    />
                  </view>
                </view>
                <view class="hang" v-if="u.moreFlag">
                  <view class="qian">运 费</view>
                  <view class="hou">
                    <format-price
                      styleProps="font-weight:bold"
                      signFontSize="20rpx"
                      smallFontSize="24rpx"
                      priceFontSize="28rpx"
                      color="#000000"
                      :price="u.freightPrice"
                    />
                  </view>
                </view>
                <view class="hang">
                  <view class="qian">商品件数</view>
                  <view class="hou">{{ u.quantity}}</view>
                </view>
                <block v-if="u.moreFlag">
                  <div
                    style="margin-top: 40rpx"
                    v-for="(u2, i2) in u.listOrderItem"
                    :key="i2"
                  >
                    <view class="hang">
                      <view class="qian">商品明细</view>
                      <view class="hou">{{ u2.spuName }}</view>
                    </view>
                    <view class="hang">
                      <view class="qian">单价</view>
                      <view class="hou">{{ u2.salesPrice }}</view>
                    </view>
                    <view class="hang">
                      <view class="qian">数量</view>
                      <view class="hou">{{ u2.quantity }}</view>
                    </view>
                    <view class="hang">
                      <view class="qian">小计</view>
                      <view class="hou">
                        <format-price
                          styleProps="font-weight:bold"
                          signFontSize="20rpx"
                          smallFontSize="24rpx"
                          priceFontSize="28rpx"
                          color="#000000"
                          :price="u2.amountToBeInvoiced"
                        />
                      </view>
                    </view>
                  </div>

                  <view class="flex justify-between">
                      <view class="text-sm text-bold">可开票金额：</view>
                      <view class="hou">
                        <format-price
                          styleProps="font-weight:bold"
                          signFontSize="20rpx"
                          smallFontSize="24rpx"
                          priceFontSize="28rpx"
                          color="#000000"
                          :price="u.amountToBeInvoiced"
                        />
                      </view>
                  </view>
                </block>
              </view>
              <view class="xia-box">
                <view class="xian"></view>
                <view class="gengduo-box">
                  <view
                    class="gengduo"
                    v-if="!u.moreFlag"
                    @click.stop="convert(u)"
                  >
                    <text>{{ u.listOrderItem.length }}件商品</text>
                    <image class="jt" :src="iconPic.jiantou" />
                  </view>
                  <view
                    class="gengduo"
                    v-if="u.moreFlag"
                    @click.stop="convert(u)"
                  >
                    <text>收起</text>
                    <image class="jt" :src="iconPic.jiantou2" />
                  </view>
                  <view class="qita">待开发票</view>
                </view>
              </view>
            </view>
          </label>
        </checkbox-group>

        <view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')">
        </view>
      </scroll-view>

      <view class="anniu-box">
        <button
          class="anniu"
          :disabled="isShow"
          :style="{
            background: isShow
              ? '#E4E4E4':'linear-gradient(90deg, #f42634 0%, #ce1a26 100%)',
          }"
          @click="goWriteInvoice"
        >
          申请开票
        </button>
      </view>
    </view>

    <view v-else class="kong-box">
      <image :src="iconPic.empty"></image>
      <view class="tip">暂无可开发票的订单记录</view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/
const app = getApp();
import formatPrice from "@/components/format-price/index.vue";
import { onlineOrderPage } from "@/pages/invoice/api/userinvoice.js";

export default {
  name: "onlineOrder",
  components: {
    formatPrice,
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,

      list: [],
      loadmore: true,
      callbackParameter: {},

      iconPic: {
        jiantou: "http://img.songlei.com/invoice/jiantou.png",
        jiantou2: "http://img.songlei.com/invoice/jiantou2.png",
      },
      collectType: [{
        value: '购物订单',
        key: '1'
      }, {
        value: '礼品卡订单',
        key: '2'
      }],
      tabCur: 0,
      parameter:{
        pageNo: 1,
        pageSize: 10,
        orderInvoiceType:'1'//1 是购物订单
      },
      orderInfo:{} ,//订单及商品信息
      selectedData:[],//选中数据
      isShow:true,
    };
  },

  onShow() {
    this.parameter.pageNo = 1;
    this.list = [];
    this.loadmore = true;
    this.callbackParameter = {};
    app.initPage().then(() => {
      this.onLineOrder();
    })
  },

  watch: {
    //控制按钮禁用
		selectedData() {
			if (this.selectedData.length > 0 && this.resData!=0) {
				this.isShow = false;	
			} else {
				this.isShow = true;
			}
		}
	},

  methods: {
    // 导航栏切换
		tabSelect (e) {
      let dataset = e.currentTarget.dataset;
      console.log("dataset",dataset,dataset.index != this.tabCur);
      if (dataset.index != this.tabCur) {
        this.tabCur = dataset.index;
        this.parameter.orderInvoiceType = dataset.key;
        this.refresh()
      }
    },

    refresh () {
      this.loadmore = true;
      this.list = [];
      this.parameter.pageNo = 1;
      this.orderInfo = {},
      this.selectedData = []
      this.onLineOrder();
    },

    onLineOrder() {
      if (this.loadmore) {
        onlineOrderPage(this.parameter).then((res) => {
          res.data.records.forEach((u) => {
            // u.checked = false;
            let obj = {
              xid: u.id,
              invoiceAmount: u.amountToBeInvoiced,//开票金额
              orderInvoiceType: this.parameter.orderInvoiceType,
              orderImg:u.listOrderItem[0].picUrl,
              scene:1
            };
            u.selData = JSON.stringify(obj);
            u.moreFlag = false;
          });
          let list = res.data.records;
          this.list = [...this.list, ...list];
          if (list.length < this.parameter.pageSize) {
            this.loadmore = false;
          } else {
            this.pageNo++;
          }
        });
      }
    },

    //单个商品勾选点击
		checkboxChange(e) {
      console.log("e===>",e);
      const items = this.list
      const values = e.detail.value
      console.log("values===>",values);
      for (let i = 0, lenI = items.length; i < lenI; ++i) {
        items[i].checked = false

        for (let j = 0, lenJ = values.length; j < lenJ; ++j) {
          if (items[i].id === values[j]) {
            items[i].checked = true
            break
          }
        }
      }
      const selectedIds = e.detail.value;
      this.selectedData = this.list.filter(item => selectedIds.includes(item.id) && item.checked);
      console.log('this.selectedData',this.selectedData);
      this.orderInfo = this.selectedData
      if (this.selectedData.length > 0) {
          this.callbackParameter = this.selectedData[0].selData;
      }

      // 如果选中的商品数量大于1，则取合计总可开票金额
      if (this.selectedData.length > 0) {
        let totalPrice = 0;
        this.selectedData.forEach(item => {
          totalPrice += item.amountToBeInvoiced;
        });
        // 将selData字符串转换为JSON对象
        let selDataObj = JSON.parse(this.selectedData[0].selData);
        // 修改invoiceAmount的值为新值（假设新值为200）保留两位数
        selDataObj.invoiceAmount = totalPrice.toFixed(2);
        // 将修改后的JSON对象转换回字符串
        this.callbackParameter = JSON.stringify(selDataObj);
      }
      console.log("list==>items",items);
    },

    convert(info) {
      this.list.forEach((u) => {
        if (u.moreFlag !== info.moreFlag) {
          u.moreFlag = false;
        }
      });
      info.moreFlag = !info.moreFlag;
    },

    goWriteInvoice() {
      console.log("callbackParameter",this.callbackParameter,this.orderInfo);
      uni.setStorageSync('orderInvoice',this.orderInfo)
      uni.navigateTo({
        url: `/pages/invoice/invoiceCenter/writeInvoice?callbackParameter=${this.callbackParameter}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.onlineOrder-page {
  .main-box {
    padding-bottom: 165rpx;
    .cell-outbox {
      margin-top: 24rpx;
      display: flex;
      align-items: center;
      .cell-box {
        width: 666rpx;
        min-height: 350rpx;
        background: #ffffff;
        border-radius: 22rpx;
        .shang-box {
          padding: 24rpx;
          padding-bottom: 6rpx;
          .hang {
            display: flex;
            margin-bottom: 8rpx;
            .qian {
              min-width: 160rpx;
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #888888;
            }
            .hou {
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #000000;
            }
            .hou2 {
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 800;
              color: #000000;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
        .xia-box {
          margin-bottom: 20rpx;
          .xian {
            width: 618rpx;
            border-top: 2rpx dotted #333;
            margin: auto;
            margin-bottom: 18rpx;
          }
          .gengduo-box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            .gengduo {
              width: 253rpx;
              height: 62rpx;
              line-height: 62rpx;
              background: #ffffff;
              border: 1px solid #999999;
              border-radius: 31rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #000000;
              .jt {
                margin-left: 6rpx;
                width: 24rpx;
                height: 9rpx;
              }
            }
            .qita {
              margin: 0 24rpx;
              width: 190rpx;
              height: 62rpx;
              text-align: center;
              line-height: 62rpx;
              background: #ffffff;
              border: 1px solid #999999;
              border-radius: 31rpx;
              font-size: 28rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #000000;
            }
          }
        }
      }
    }
    .anniu-box {
      position: fixed;
      bottom: 0;
      width: 750rpx;
      height: 100rpx;
      .anniu {
        margin: auto;
        margin-top: 10rpx;
        width: 700rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #fff;
      }
    }
  }

  .kong-box {
    margin-top: 130rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    image {
      width: 250rpx;
      height: 250rpx;
    }

    .tip {
      font-size: 28rpx;
      margin-top: 28rpx;
    }
  }
}
</style>
