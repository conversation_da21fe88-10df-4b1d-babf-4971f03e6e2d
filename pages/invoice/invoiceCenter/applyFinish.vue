<template>
  <view class="applyFinish-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">申请开票</block>
    </cu-custom>

    <view class="main-box">
      <image class="tu" :src="iconPic.success" />
      <view class="wen">提交成功</view>
      <view class="hang">您的发票正在开出，请注意查收邮箱； </view>
      <view class="hang">您可以前往开票历史页面查看您的开票进度。</view>
    </view>
    <view class="btn-box">
      <view class="annniu" @click="goHistory">开票历史</view>
      <view class="annniu2" @click="goInvoiceCenter">开票中心</view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/

const app = getApp();

export default {
  name: "applyFinish",
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      iconPic: {
        success: "http://img.songlei.com/invoice/success.png",
      },
    };
  },
  onLoad() {
      uni.onNavigationBarButtonTap({
          position: 'left',
          index: 0,
          success: function () {
              uni.navigateBack({
                  delta: 2 // 返回两层页面
              });
          }
      });
  },
  
  methods: {
    goHistory() {
      uni.navigateTo({
        url: "/pages/invoice/parkApply/history",
      });
    },
    goInvoiceCenter() {
      uni.navigateTo({
        url: "/pages/invoice/invoiceCenter/index",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.applyFinish-page {
  .main-box {
    margin: auto;
    margin-top: 30rpx;
    width: 700rpx;
    height: 443rpx;
    text-align: center;
    background: #ffffff;
    border-radius: 30rpx;
    .tu {
      margin-top: 40rpx;
      width: 136rpx;
      height: 136rpx;
    }
    .wen {
      margin-top: 45rpx;
      margin-bottom: 50rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 800;
      color: #000000;
    }
    .hang {
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #000000;
      margin-bottom: 10rpx;
    }
  }
  .btn-box {
    position: fixed;
    bottom: 0rpx;
    width: 750rpx;
    padding-top: 10rpx;
    padding-bottom: 30rpx;
    background-color: #ffffff;
    .annniu {
      margin: auto;
      margin-top: 30rpx;
      width: 700rpx;
      height: 74rpx;
      line-height: 74rpx;
      text-align: center;
      background: #ffffff;
      border: 1px solid #000000;
      border-radius: 37rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #000000;
    }
    .annniu2 {
      margin: auto;
      margin-top: 30rpx;
      width: 700rpx;
      height: 74rpx;
      line-height: 74rpx;
      text-align: center;
      background: #f32634;
      border-radius: 37rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
    }
  }
}
</style>
