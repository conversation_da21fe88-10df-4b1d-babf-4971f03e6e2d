import { requestApi as request } from "@/utils/api.js";

// 查询可开票列表
export function restApi(data) {
  return request({
    url: "/sco-park-api/rest?method=sco.park.invoice.getlist",
    method: "post",
    showLoading: false,
    data,
  });
}

// 发票抬头模糊搜索
export function fuzzyQueryInvoiceTitleApi(data) {
  return request({
    url: "/mallapi/orderinvoice/fuzzyQueryInvoiceTitle",
    method: "post",
    data,
  });
}

// 发票抬头模糊详情搜索
export function getInvoiceTitleInfoApi(data) {
  return request({
    url: "/mallapi/orderinvoice/getInvoiceTitleInfo",
    method: "post",
    data,
  });
}

// 开发票
export function applyInvoiceApi(data) {
  return request({
    url: "/mallapi/orderinvoice/applyInvoice",
    method: "post",
    data,
  });
}

// 发票历史
export function pageApi(data) {
  return request({
    url: "/mallapi/orderinvoice/page",
    method: "get",
    data,
  });
}

// 发票详情
export function detailApi(id) {
  return request({
    url: `/mallapi/orderinvoice/${id}`,
    method: "get",
  });
}

// 查询开票订单
export function restTwoApi(data) {
  return request({
    url: "/sco-park-api/rest?method=sco.park.invoice.getbyinvoice",
    method: "post",
    data,
  });
}

// 重发发票
export function sendEmailApi(data) {
  return request({
    url: "/mallapi/orderinvoice/sendEmail",
    method: "get",
    data,
  });
}

// 用户发票新增接口
export const addUserInvoice = (obj = {}) => {
  return request({
    url: "/mallapi/userinvoice/",
    method: "post",
    data: obj,
  });
};

// 用户发票表分页列表接口
export const getUserInvoicePage = (data = {}) => {
  return request({
    url: "/mallapi/userinvoice/page",
    method: "get",
    data,
    showLoading: false,
  });
};

//用户发票表删除接口
export function delUserInvoice(id) {
  return request({
    url: `/mallapi/userinvoice/${id}`,
    method: "delete",
  });
}

// 用户发票表修改接口
export function putUserInvoice(obj) {
  return request({
    url: "/mallapi/userinvoice",
    method: "put",
    data: obj,
  });
}

//用户发票单个返显接口
export function editUserInvoice(id) {
  return request({
    url: `/mallapi/userinvoice/${id}`,
    method: "get",
    showLoading: false,
  });
}

// 用户线下商品发票列表接口
export function offlineGoodsListApi(data) {
  return request({
    url: "/mallapi/offline/page",
    method: "get",
    data,
  });
}

//用户线下商品发票详情接口
export function offlineGoodsDetail(data) {
  return request({
    url: `/mallapi/offline/get`,
    method: "get",
    data,
  });
}

// 线上订单分页列表
export function onlineOrderPage(data) {
  return request({
    url: "/mallapi/orderinvoice/onlineOrder-page",
    method: "get",
    data,
  });
}

// 线上提交申请开发票
export function onlineOrderInvoice(data) {
  return request({
    url: "/mallapi/orderinvoice/onlineOrderInvoice",
    method: "post",
    data,
  });
}

// 线下提交申请开发票
export function offlineOrderInvoice(data) {
  return request({
    url: "/mallapi/orderinvoice/offlineOrderInvoice",
    method: "post",
    data,
  });
}

// 线上商品详情预开发票
export function onlineOrderPreInvoice(data) {
  return request({
    url: "/mallapi/orderinvoice/onlineOrderPreInvoice",
    method: "post",
    data,
  });
}

// 线下订单可开发票统计
export function getBills(data) {
  return request({
    url: "/mallapi/offline/getBills",
    // showLoading: false,
    method: "get",
    data,
  });
}

// 线上商品详情预开发票
export function changeInvoice(data) {
  return request({
    url: "/mallapi/orderinvoice/changeInvoice",
    method: "post",
    data,
  });
}
