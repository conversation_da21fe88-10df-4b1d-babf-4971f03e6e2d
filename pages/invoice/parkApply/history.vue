<template>
  <view class="history-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">开票历史</block>
    </cu-custom>

    <view class="core-outbox" v-if="list && list.length > 0">
      <scroll-view
        scroll-y
        :style="{ 'max-height': `calc(100vh - ${CustomBar}px)` }"
        @scrolltolower="getMoreList"
      >
        <view class="core-box" v-for="(u, i) in list" :key="i">
          <view class="hang">
            <view class="zou">开票时间</view>
            <view class="you">{{ u.createTime }}</view>
          </view>
          <view class="hang">
            <view class="zou">实付金额</view>
            <view class="you">
              <text style="font-size: 18rpx">￥</text>
              {{ u.invoiceAmount }}
            </view>
          </view>
          <view class="hang2" @click="goInvoiceDetail(u)">
            <view class="zou">开票详情</view>
            <view class="you">&gt;</view>
          </view>
          <view class="hang2">
            <view class="zou" style="color: #d49879">
              {{ u.status | convertStatus }}
            </view>

            <view class="you2" v-if="1 == u.status" @click="goAgainInvoice(u)">
              重发发票
            </view>
            <view class="you" v-else></view>
          </view>
        </view>
        <view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')">
        </view>
      </scroll-view>
    </view>
    <view v-else class="kong-box">
      <image :src="iconPic.empty"></image>
      <view class="tip">暂无开票记录</view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月10日
 **/

const app = getApp();
import { pageApi } from "@/pages/invoice/api/userinvoice.js";

export default {
  name: "history",

  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,

      list: [],
      loadmore: true,

      page: {
        current: 1,
        size: 10,
      },

      iconPic: {
        empty: "https://img.songlei.com/live/invoice/empty.png",
      },
    };
  },
  
  filters: {
    convertStatus(val) {
      let str = "";
      switch (val) {
        case 0:
          str = "开票中";
          break;
        case 1:
          str = "已开票";
          break;
        case 2:
          str = "开票失败";
          break;
        case 3:
          str = "申请中";
          break;
        case 4:
          str = "申请中";
          break;
        case 5:
          str = "申请中";
          break;
        case 6:
				str = "已红冲";
					break;
				case 8:
				str = "红冲中";
					break;
        case 9:
        str = "待手动重推";
          break;
      }
      return str;
    },
  },
  onShow() {
    app.initPage().then(() => {
      this.getMoreList();
    })
  },

  methods: {
    getMoreList() {
      if (this.loadmore) {
        const params = {
          current: this.page.current,
          size: this.page.size,
        };
        pageApi(params).then((res) => {
          let list = res.data.records;
          this.list = [...this.list, ...list];
          if (list.length < this.page.size) {
            this.loadmore = false;
          } else {
            this.page.current++;
          }
        });
      }
    },
    onPullDownRefresh() {
      this.list = [];
      this.loadmore = true;
      this.page.current = 1;
      this.getMoreList();
      uni.stopPullDownRefresh();
    },
    goInvoiceDetail(info) {
      uni.navigateTo({
        url: `/pages/invoice/parkApply/invoiceDetail?id=${info.id}`,
      });
    },
    goAgainInvoice(info) {
      uni.navigateTo({
        url: `/pages/invoice/parkApply/againInvoice?invoiceId=${info.id}&toEmail=${info.email}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.history-page {
  .core-outbox {
    .core-box {
      margin: auto;
      margin-top: 25rpx;
      padding: 25rpx;
      width: 700rpx;
      height: 276rpx;
      background: #ffffff;
      border-radius: 30rpx;
      .hang {
        margin-bottom: 18rpx;
        display: flex;
        .zou {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #888888;
        }
        .you {
          margin-left: 54rpx;
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #000000;
        }
      }
      .hang2 {
        margin-bottom: 18rpx;
        display: flex;
        justify-content: space-between;
        .zou {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #888888;
        }
        .you {
        }
        .you2 {
          width: 190rpx;
          height: 50rpx;
          line-height: 50rpx;
          text-align: center;
          background: #ffffff;
          border: 1px solid #999999;
          border-radius: 25rpx;
        }
      }
    }
  }
  .kong-box {
    margin-top: 130rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    image {
      width: 250rpx;
      height: 250rpx;
    }

    .tip {
      font-size: 28rpx;
      margin-top: 28rpx;
    }
  }
}
</style>
