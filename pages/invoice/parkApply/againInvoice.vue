<template>
  <view class="againInvoice-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">重发发票</block>
    </cu-custom>

    <view class="main-box">
      <view class="yin">电子邮箱</view>
      <input class="wen" v-model="toEmail" />
    </view>
    <view class="annniu-box" @click="sendEmail">提交</view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月10日
 **/

const app = getApp();
import { sendEmailApi } from "@/pages/invoice/api/userinvoice.js";

export default {
  name: "againInvoice",
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      invoiceId: "",
      toEmail: "",
    };
  },
  onLoad(options) {
    this.invoiceId = options.invoiceId;
    this.toEmail = options.toEmail;
  },
  methods: {
    sendEmail() {
      const params = {
        invoiceId: this.invoiceId,
        toEmail: this.toEmail,
      };
      sendEmailApi(params).then((res) => {
        uni.showModal({
          content: res.data,
          showCancel: false,
          success(res) {
            if (res.confirm) {
              uni.navigateBack();
            }
          },
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.againInvoice-page {
  .main-box {
    display: flex;
    align-items: center;
    margin: auto;
    margin-top: 40rpx;
    padding-left: 32rpx;
    width: 700rpx;
    height: 122rpx;
    background: #ffffff;
    border-radius: 30rpx;
    .yin {
      margin-right: 38rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #888888;
    }
    .wen {
      width: 490rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #000000;
    }
  }
  .annniu-box {
    position: fixed;
    left: 135rpx;
    bottom: 50rpx;
    width: 480rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background: #f32634;
    border-radius: 37rpx;
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #ffffff;
  }
}
</style>
