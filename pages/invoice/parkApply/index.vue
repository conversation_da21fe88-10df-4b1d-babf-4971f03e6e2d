<template>
  <view class="parkApply-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">停车发票</block>
    </cu-custom>
    <view class="top-content">
      <view class="tip-title">开票说明</view>
      <view class="tip-content">
        <view>1、支付中使用各种形式的打折优惠金额不支持开具发票；</view>
        <view>
          2、电子发票发放有可能存在延时，一般在提交48小时内
          送达您预留的电子邮箱。
        </view>
      </view>
      <view class="tip-content2">祝您购物愉快！</view>
    </view>

    <view class="bottom-action">
      <view class="invoice-history" @click="goHistory"> 开票历史 </view>
      <view class="invoice-open" @click="goOrderList"> 立即开票 </view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月10日
 **/
const app = getApp();

export default {
  name: "parkApply",
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
    };
  },
  methods: {
    goHistory() {
      uni.navigateTo({
        url: "/pages/invoice/parkApply/history",
      });
    },
    goOrderList() {
      uni.navigateTo({
        url: "/pages/invoice/parkApply/orderList",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.parkApply-page {
  height: 100%;
  .top-content {
    background: #ffffff;
    border-radius: 30rpx;
    margin: 30rpx 26rpx;
    padding: 30rpx 30rpx 58rpx;

    .tip-title {
      text-align: center;
      font-size: 30rpx;
      font-weight: 800;
      color: #000000;
    }

    .tip-content,
    .tip-content2 {
      font-size: 26rpx;
      font-weight: 500;
      color: #000000;
      line-height: 48rpx;
      margin-top: 40rpx;
    }

    .tip-content2 {
      text-align: right;
    }
  }

  .bottom-action {
    position: fixed;
    bottom: 0;
    background: #ffffff;
    padding: 30rpx 30rpx 58rpx;
    display: flex;
    justify-content: space-between;
    width: 100%;

    .invoice-history {
      flex: 1;
      display: flex;
      justify-content: center;
      height: 74rpx;
      line-height: 74rpx;
      background: #ffffff;
      border: 1px solid #000000;
      border-radius: 37rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: #000000;
      text-align: center;
      margin-right: 10rpx;
    }

    .invoice-open {
      flex: 1;
      display: flex;
      justify-content: center;
      height: 74rpx;
      line-height: 74rpx;
      background: #f32634;
      border-radius: 37px;
      color: #ffffff;
      text-align: center;
      margin-left: 10rpx;
    }
  }
}
</style>
