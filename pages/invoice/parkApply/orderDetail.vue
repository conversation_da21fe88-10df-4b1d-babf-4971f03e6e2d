<template>
  <view class="orderDetail-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">订单详情</block>
    </cu-custom>
    <view class="main-box" v-if="list && list.length > 0">
      <scroll-view
        scroll-y
        :style="{ 'max-height': `calc(100vh - ${CustomBar}px)` }"
        @scrolltolower="getMoreList"
      >
        <view class="cell-box" v-for="(u, i) in list" :key="i">
          <view class="zou-box">
            <view class="wen">{{ u.storeName }}</view>
            <view class="wen2">{{ u.plateNo }}</view>
          </view>
          <view class="you-box">
            <view class="hang">
              <view class="qian">订单生成时间：</view>
              <view class="hou">{{ u.costTime }}</view>
            </view>
            <view class="hang">
              <view class="qian">付款金额：</view>
              <view class="hou">￥{{ u.costMoney }}</view>
            </view>
            <view class="hang">
              <view class="qian"></view>
              <view class="hou" style="color: #d49879">
                {{ u.invoiceFlag | convertStatus }}
              </view>
            </view>
          </view>
        </view>
        <view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')">
        </view>
      </scroll-view>
    </view>
    <view v-else class="kong-box">
      <image :src="iconPic.empty"></image>
      <view class="tip">暂无记录</view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月10日
 **/
const app = getApp();
import { restTwoApi } from "@/pages/invoice/api/userinvoice.js";

export default {
  name: "orderDetail",
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,

      invoiceId: "",
      list: [],
      page: {
        pageNo: 1,
        pageSize: 10,
      },
      loadmore: true,

      iconPic: {
        empty: "https://img.songlei.com/live/invoice/empty.png",
      },
    };
  },
  filters: {
    convertStatus(val) {
      let str = "";
      switch (val) {
        case "P":
          str = "开票中";
          break;
        case "Y":
          str = "已开票";
          break;
        case "F":
          str = "开票失败";
          break;
      }
      return str;
    },
  },
  onLoad(options) {
    this.invoiceId = JSON.parse(decodeURIComponent(options.invoiceId));
    this.getMoreList();
  },

  methods: {
    getMoreList() {
      if (this.loadmore) {
        const params = {
          channel: "SONGSHU",
          custId: uni.getStorageSync("user_info").erpCid||uni.getStorageSync("user_info").openId,
          invoiceId: this.invoiceId,
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
        };
        restTwoApi(params).then((res) => {
          let list = res.data.dataList;
          this.list = [...this.list, ...list];
          if (list.length < this.page.pageSize) {
            this.loadmore = false;
          } else {
            this.page.pageNo++;
          }
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.orderDetail-page {
  .main-box {
    .cell-box {
      margin: auto;
      margin-top: 24rpx;
      display: flex;
      width: 697rpx;
      height: 176rpx;
      background: #ffffff;
      border-radius: 14rpx;
      .zou-box {
        text-align: center;
        width: 176rpx;
        height: 176rpx;
        background: linear-gradient(0deg, #ccac92 0%, #d0a089 0%, #d0ae93 100%);
        border-radius: 14rpx;
        .wen {
          margin-top: 42rpx;
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #ffffff;
        }
        .wen2 {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 800;
          color: #ffffff;
        }
      }
      .you-box {
        flex: 1;
        padding: 20rpx;
        .hang {
          display: flex;
          justify-content: space-between;
          .qian {
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #000000;
          }
          .hou {
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #000000;
          }
        }
      }
    }
  }

  .kong-box {
    margin-top: 130rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    image {
      width: 250rpx;
      height: 250rpx;
    }

    .tip {
      font-size: 28rpx;
      margin-top: 28rpx;
    }
  }
}
</style>
