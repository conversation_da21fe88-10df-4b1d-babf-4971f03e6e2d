<template>
  <view class="orderList-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">停车发票</block>
    </cu-custom>
    <view class="main-box" v-if="list && list.length > 0">
      <scroll-view
        scroll-y
        :style="{ 'max-height': `calc(100vh - ${CustomBar}px - 83px)` }"
        @scrolltolower="getMoreList"
      >
        <checkbox-group>
          <view
            class="cell-outbox"
            v-for="(u, i) in list"
            :key="i"
            @click="selChange(u)"
          >
            <checkbox
              class="round red text-center vertical-center"
              style="transform: scale(0.6)"
              :value="u.orderId"
              :checked="u.checked"
            />
            <view class="cell-box">
              <view class="zou-box">
                <view class="wen">{{ u.storeName }}</view>
                <view class="wen2">{{ u.plateNo }}</view>
              </view>
              <view class="you-box">
                <view class="hang">
                  <view class="qian">订单生成时间：</view>
                  <view class="hou">{{ u.costTime }}</view>
                </view>
                <view class="hang">
                  <view class="qian">付款金额：</view>
                  <view class="hou">￥{{ u.costMoney }}</view>
                </view>
                <view class="hang">
                  <view class="qian"></view>
                  <view class="hou" style="color: #d49879"> 未开票 </view>
                </view>
              </view>
            </view>
          </view>
        </checkbox-group>
        <view :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')">
        </view>
      </scroll-view>
      <view class="dibu-box">
        <view class="dibu">
          <view class="shang">
            <text style="color: #f32634">{{ sum }}</text>
            个记录，共
            <text style="color: #f32634">{{ invoiceAmount }}</text>
            元
          </view>
          <radio-group>
            <view class="xia">
              <view class="xuan">
                <radio
                  class="blue"
                  style="transform: scale(0.6)"
                  :value="1"
                  :checked="radioSelect == 1 ? true : false"
                  @click="optionChange(1)"
                />
                <view>本页面全选</view>
              </view>
              <view class="xuan">
                <radio
                  class="blue"
                  style="transform: scale(0.6)"
                  :value="2"
                  :checked="radioSelect == 2 ? true : false"
                  @click="optionChange(2)"
                />
                <view>全部全选</view>
              </view>
            </view>
          </radio-group>
        </view>
        <view
          class="btn"
          :style="{ background: callbackParameter ? '#f32634' : '#E4E4E4' }"
          @click="goOpenInvoice"
        >
          下一步
        </view>
      </view>
    </view>
    <view v-else class="kong-box">
      <image :src="iconPic.empty"></image>
      <view class="tip">暂无可开发票的支付记录</view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月10日
 **/
const app = getApp();
import { restApi } from "@/pages/invoice/api/userinvoice.js";
import { accAdd } from "utils/numberUtil.js";

export default {
  name: "orderList",
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      CustomBar: this.CustomBar,

      list: [],
      callbackParameter: "",
      licensePlateNums:"",
      page: {
        pageNo: 1,
        pageSize: 10,
        total: "",
        totalCostMoney: "",
        totalOrderId: "",
        licensePlateNums:"",
      },
      sum: 0,
      invoiceAmount: "0.00",
      loadmore: true,
      radioSelect: 0,

      iconPic: {
        empty: "https://img.songlei.com/live/invoice/empty.png",
      },
    };
  },
  onShow() {
    this.list = [];
    this.callbackParameter = "";
    this.page.pageNo = 1;
    this.sum = 0;
    this.invoiceAmount = "0.00";
    this.loadmore = true;
    this.radioSelect = 0;
    this.getMoreList();
  },

  methods: {
    getMoreList() {
      if (this.loadmore) {
        const params = {
          channel: "SONGSHU",
          custId: uni.getStorageSync("user_info").erpCid||uni.getStorageSync("user_info").openId,
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
        };
        restApi(params).then((res) => {
          if (res.data.dataList) {
            res.data.dataList.forEach((u) => {
              if (2 == this.radioSelect) {
                u.checked = true;
              } else {
                u.checked = false;
              }
            });
          }
          let list = res.data.dataList;
          this.page.totalOrderId = res.data.totalOrderId;
          this.page.licensePlateNums = res.data.totalPlateNo

          this.page.total = res.data.total;
          this.page.totalCostMoney = res.data.totalCostMoney;
          this.list = [...this.list, ...list];
          if (list.length < this.page.pageSize) {
            this.loadmore = false;
          } else {
            this.page.pageNo++;
          }
        });
      }
    },
    selChange(info) {
      console.log("info",info,"this.radioSelect",this.radioSelect);
      if (this.radioSelect) {
        this.radioSelect = 0;
        this.list.forEach((u) => {
          u.checked = false;
        });
        this.invoiceAmount = "0.00";
        this.callbackParameter = "";
        this.licensePlateNums = "";
        this.sum = 0;
      } else {
        info.checked = !info.checked;
        let arr = [];
        let licensePlateNums = []
        if (this.callbackParameter) {
          arr = this.callbackParameter.split(",");
        }
        if (this.licensePlateNums) {
          console.log("this.licensePlateNums",this.licensePlateNums);
          licensePlateNums = this.licensePlateNums.split(",");
        }
        if (info.checked) {
          arr.push(info.orderId);
          licensePlateNums.push(info.plateNo)
        } else {
          let idx = arr.findIndex((v) => {
            return v == info.orderId;
          });
          arr.splice(idx, 1);
          let numx = licensePlateNums.findIndex((v) => {
            return v == info.plateNo;
          });
          licensePlateNums.splice(numx, 1);
        }
        this.callbackParameter = arr.toString();
        this.licensePlateNums = licensePlateNums.toString();
        this.sum = arr.length;
        this.invoiceAmount = 0;
        arr.forEach((u) => {
          let obj = this.list.find((v) => {
            return v.orderId == u;
          });
          this.invoiceAmount = accAdd(this.invoiceAmount, obj.costMoney);
        });
      }
      console.log(
        "this.invoiceAmount--11",this.invoiceAmount,
        "this.callbackParameter",this.callbackParameter,
        "this.licensePlateNums",this.licensePlateNums
      );
    },
    optionChange(e) {
      console.log("e",e,"this.radioSelect",this.radioSelect);
      if (e == this.radioSelect) {
        
        this.radioSelect = 0;
        this.list.forEach((u) => {
          u.checked = false;
        });
        this.invoiceAmount = "0.00";
        this.callbackParameter = "";
        this.licensePlateNums = "";
        this.sum = 0;
        console.log("0000");
      } else {
        console.log("1111");
        this.radioSelect = e;
        if (1 == e) {
          let arr = [];
          let licensePlateNums = []
          this.invoiceAmount = 0;
          this.list.forEach((u) => {
            u.checked = true;
            arr.push(u.orderId);
            licensePlateNums.push(u.plateNo)
            this.invoiceAmount = accAdd(this.invoiceAmount, u.costMoney);
          });
          this.callbackParameter = arr.toString();
          this.licensePlateNums = licensePlateNums.toString();
          this.sum = this.list.length;
          console.log("2222");
        } else {
          this.list.forEach((u) => {
            u.checked = true;
          });
          this.invoiceAmount = this.page.totalCostMoney;
          this.callbackParameter = this.page.totalOrderId;
          this.licensePlateNums = this.page.licensePlateNums
          this.sum = this.page.total;
          console.log("3333");
        }
      }
      console.log("this.list",this.list);
      console.log(
        "this.invoiceAmount--22",this.invoiceAmount,
        "this.callbackParameter",this.callbackParameter,
        "this.licensePlateNums",this.licensePlateNums
      );
    },

    goOpenInvoice() {
      if (this.callbackParameter) {
        // 将字符串拆分为数组
        const plateNumsArray = this.licensePlateNums.split(",");
        // 去重
        const uniqueArray = [...new Set(plateNumsArray)];
        uni.showModal({
          content: `您本次选择的待开票记录${this.sum}个，金额${this.invoiceAmount}元，请核对。`,
          success: (res) => {
            if (res.confirm) {
              const obj = {
                invoiceAmount: this.invoiceAmount,
                callbackParameter: this.callbackParameter,
                totalPlateNo:uniqueArray.toString()
              };
              const params = encodeURIComponent(JSON.stringify(obj));
              uni.navigateTo({
                url: `/pages/invoice/parkApply/openInvoice?params=${params}`,
              });
            }
          },
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.orderList-page {
  .main-box {
    padding-bottom: 165rpx;
    .cell-outbox {
      margin-top: 24rpx;
      display: flex;
      align-items: center;
      .cell-box {
        display: flex;
        width: 666rpx;
        height: 176rpx;
        background: #ffffff;
        border-radius: 14rpx;
        .zou-box {
          text-align: center;
          width: 176rpx;
          height: 176rpx;
          background: linear-gradient(
            0deg,
            #ccac92 0%,
            #d0a089 0%,
            #d0ae93 100%
          );
          border-radius: 14rpx;
          .wen {
            margin-top: 42rpx;
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #ffffff;
          }
          .wen2 {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 800;
            color: #ffffff;
          }
        }
        .you-box {
          padding: 20rpx;
          .hang {
            display: flex;
            justify-content: space-between;
            .qian {
              font-size: 26rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #000000;
            }
            .hou {
              font-size: 26rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #000000;
            }
          }
        }
      }
    }
    .dibu-box {
      position: fixed;
      bottom: 0;
      width: 748rpx;
      height: 165rpx;
      padding-left: 60rpx;
      background-color: #ffffff;
      display: flex;
      .dibu {
        margin-top: 35rpx;
        .shang {
        }

        .xia {
          margin-left: -12rpx;
          display: flex;
          .xuan {
            display: flex;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            margin-right: 30rpx;
          }
        }
      }

      .btn {
        margin-top: 43rpx;
        width: 257rpx;
        height: 74rpx;
        line-height: 74rpx;
        text-align: center;
        background: #f32634;
        border-radius: 37rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }

  .kong-box {
    margin-top: 130rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    image {
      width: 250rpx;
      height: 250rpx;
    }

    .tip {
      font-size: 28rpx;
      margin-top: 28rpx;
    }
  }
}
</style>
