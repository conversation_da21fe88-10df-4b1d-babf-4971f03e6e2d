<template>
  <view class="invoiceDetail-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">发票详情</block>
    </cu-custom>

    <view class="core-outbox">
      <div class="yanse-box" :class="'bg-' + theme.backgroundColor">
        <view class="wen">{{ stateTitle }}</view>
      </div>

      <view class="one-box">
        <view class="hang" v-for="(u, i) in list" :key="i">
          <view class="zou">{{ u.text || '暂无' }}</view>
          <view class="you">{{ u.val || '暂无' }}</view>
        </view>
      </view>

      <view v-if="3 == scene" class="two-box">
        <view class="zou">1张发票，含{{ sum }}个订单 </view>
        <view class="you" @click="goOrderDetail">查看 &gt;</view>
      </view>

      <view
        class="three-box"
        style="line-height: 50rpx"
        @click="goDownload"
        v-if="1 == status && 3 == scene"
      >
        <view class="zou">下载发票</view>
        <view class="you">&gt;</view>
      </view>

      <view class="three-box" v-if="2 == status">
        <view class="zou">开票失败原因</view>
        <view class="you" style="color:rgb(243,38,52)">{{ remark }}</view>
      </view>

      <view class="annniu-box" v-if="1 == status && 3 == scene" @click="goAgainInvoice">
        重发发票
      </view>

      <view class="yulan-box" v-if="3 != scene&&1 == status" @click="goDownload">
        点击下载发票
      </view>
      <view class="btn-box"  v-if="3 != scene">
        <block v-if="(4 == status || 3 == status )&& 1 ==scene">
          <view class="btn btn2" style="width: 502rpx" @click="goWriteInvoice(2)">
            申请换开
          </view>
        </block>
        <block v-if="1 == status">
          <view class="btn btn2" style="width: 338rpx" @click="goInvoiceCenter">
            返回首页
          </view>
          <view class="btn btn2" style="width: 338rpx" @click="goAgainInvoice">
            重发发票
          </view>
        </block>
        <block v-if="2 == status">
          <view class="btn btn3" style="width: 502rpx" @click="goWriteInvoice(1)">
            申请开票
          </view>
        </block>
      </view>

    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月10日
 **/

const app = getApp();
import { detailApi } from "@/pages/invoice/api/userinvoice.js";

export default {
  name: "invoiceDetail",
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      stateTitle: "",
      id: "", //发票id
      list: [],
      status: "",//3和4都是申请中 0开票中 1已开票 2开票失败
      sum: "",
      remark: "",
      orderId: "",
      scene:'',//开票场景1：线上订单2：线下订单3：停车场订单
      invoicePdfUrl: "",
      callbackParameter:''
    };
  },
  
  onLoad(options) {
    this.id = options.id;
    this.getDetail();
  },

  methods: {
    // 申请开票或者换开
    goWriteInvoice(type) {
      // 3和4,5都是申请中
      if (this.status ==3 || this.status ==4 || this.status ==5) {
				uni.showToast({
					title: '发票申请中，请勿重复申请！',
					icon: 'none',
					duration: 2000
				});
				return
			}

      // 将selData字符串转换为JSON对象
      let selDataObj = JSON.parse(this.callbackParameter);
      if(type==2){
        selDataObj.scene = 1;
        selDataObj.replace = true;
        // 将修改后的JSON对象转换回字符串
        this.callbackParameter = JSON.stringify(selDataObj);
        console.log("this.callbackParameter",this.callbackParameter);
        uni.navigateTo({
          url: `/pages/invoice/invoiceCenter/writeInvoice?callbackParameter=${this.callbackParameter}`,
        });
      }else{
        // 失败转态
        if(this.status==2){
          selDataObj.orderStatus = this.status
          this.callbackParameter = JSON.stringify(selDataObj)
        }
        uni.navigateTo({
          url: `/pages/invoice/invoiceCenter/writeInvoice?callbackParameter=${this.callbackParameter}`,
        });
      }
    },
    // 返回首页
    goInvoiceCenter() {
      uni.navigateTo({
        url: "/pages/invoice/invoiceCenter/index",
      });
    },
    getDetail() {
      detailApi(this.id).then((res) => {
        let {
          invoiceType,
          invoiceTitle,
          invoiceTaxNo,
          vatCompanyAddress,
          vatTelphone,
          vatBankName,
          vatBankAccount,
          invoiceAmount,
          notifyPhone,
          email,
          createTime,
          status,
          remark,
          callbackParameter,
          orderNum,
          orderId,
          invoicePdfUrl,
          scene,
          invoiceClassification
        } = res.data;

        if (1 == invoiceType) {
          this.list = [
            { text: "发票抬头", val: invoiceTitle },
            { text: "发票金额", val: `￥${invoiceAmount}` },
            { text: "手机号码", val: notifyPhone },
            { text: "电子邮箱", val: email },
            { text: "开票时间", val: createTime },
          ];
        } else {
          this.list = [
            { text: "发票抬头", val: invoiceTitle },
            { text: "纳税人识别号", val: invoiceTaxNo },
            { text: "注册地址", val: vatCompanyAddress },
            { text: "注册电话", val: vatTelphone },
            { text: "开户银行", val: vatBankName },
            { text: "开户行账号", val: vatBankAccount },
            { text: "发票金额", val: `￥${invoiceAmount}` },
            { text: "手机号码", val: notifyPhone },
            { text: "电子邮箱", val: email },
            { text: "开票时间", val: createTime },
          ];
        }
        switch (status) {
          case 0:
					this.stateTitle = "开票中";
						break;
					case 1:
					this.stateTitle = "已开票";
						break;
					case 2:
					this.stateTitle = "开票失败";
						break;
					case 3:
					this.stateTitle = "申请中";
						break;
					case 4:
					this.stateTitle = "申请中";
						break;
          case 5:
					this.stateTitle = "申请中";
						break;
          case 6:
					this.stateTitle = "已红冲";
						break;
          case 8:
					this.stateTitle = "红冲中";
						break;
          case 9:
          this.stateTitle = "待手动重推"
            break;
        }
        this.status = status;
        this.remark = remark;
        this.scene = scene
        let obj = {
          xid: this.id,
          invoiceAmount: invoiceAmount,
          scene:scene,
          orderInvoiceType:invoiceClassification
        };
        this.callbackParameter = JSON.stringify(obj);
        // 只有停车场订单使用split(",").length取订单数量
        if (this.scene == 3 && callbackParameter) {
          this.sum = callbackParameter.split(",").length;
        }else{
          this.sum = orderNum || 0
        }
        this.orderId = orderId;
        this.invoicePdfUrl = invoicePdfUrl;
      });
    },
    goOrderDetail() {
      const invoiceId = encodeURIComponent(JSON.stringify(this.orderId));
      uni.navigateTo({
        url: `/pages/invoice/parkApply/orderDetail?invoiceId=${invoiceId}`,
      });
    },
    goDownload() {
      uni.showLoading({
        title: "加载中",
        mask: true,
      });
      uni.downloadFile({
        url: this.invoicePdfUrl,
        success(res) {
          uni.openDocument({
            filePath: res.tempFilePath,
            fileType: "pdf",
            showMenu: true,
            success() {
              uni.hideLoading();
            },
            fail(err) {
              uni.hideLoading();
              console.log("fail:" + JSON.stringify(err));
            },
          });
        },
        fail(err) {
          uni.hideLoading();
          console.log("fail:" + JSON.stringify(err));
        },
      });
    },
    goAgainInvoice() {
      const idx = this.list.length - 2;
      const obj = this.list[idx];
      uni.navigateTo({
        url: `/pages/invoice/parkApply/againInvoice?invoiceId=${this.id}&toEmail=${obj.val}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.invoiceDetail-page {
  .core-outbox {
    .yanse-box {
      width: 750rpx;
      height: 180rpx;
      border-radius: 0 0 375rpx 375rpx / 0 0 60rpx 60rpx;
      .wen {
        padding-top: 26rpx;
        margin-left: 59rpx;
        font-size: 36rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #ffffff;
      }
    }
    .one-box {
      width: 700rpx;
      min-height: 556rpx;
      background: #ffffff;
      border-radius: 22rpx;
      padding: 20rpx 34rpx;
      margin: auto;
      margin-top: -80rpx;
      .hang {
        display: flex;
        margin-bottom: 40rpx;
        &:last-of-type {
          margin-bottom: 20rpx;
        }
        .zou {
          width: 180rpx;
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #8e8e90;
        }
        .you {
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #000000;
        }
      }
    }
    .two-box {
      margin: auto;
      margin-top: 20rpx;
      padding-left: 25rpx;
      padding-right: 25rpx;
      display: flex;
      justify-content: space-between;
      width: 700rpx;
      height: 100rpx;
      line-height: 100rpx;
      background: #ffffff;
      border-radius: 22rpx;
      .zou {
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #8e8e90;
      }
      .you {
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #8e8e90;
      }
    }
    .three-box {
      margin: auto;
      margin-top: 20rpx;
      padding: 25rpx;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      width: 700rpx;
      min-height: 50rpx;
      background: #ffffff;
      border-radius: 22rpx;
      .zou {
        min-width: 170rpx;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #8e8e90;
      }
      .you {
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #8e8e90;
      }
    }
    .annniu-box {
      margin: auto;
      margin-top: 30rpx;
      width: 542rpx;
      height: 73rpx;
      line-height: 73rpx;
      text-align: center;
      background: #f32634;
      border-radius: 37rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
    }
    .yulan-box {
      width: 502rpx;
      height: 100rpx;
      text-align: center;
      line-height: 100rpx;
      background: #ffffff;
      border-radius: 50rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #f22633;
      margin: auto;
      margin-top: 44rpx;
    }
    .btn-box {
      position: fixed;
      bottom: 0;
      width: 750rpx;
      height: 100rpx;
      display: flex;
      .btn {
        margin: auto;
        height: 80rpx;
        text-align: center;
        line-height: 80rpx;
        border-radius: 40rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 500;
      }
      .btn2 {
        background: #fff;
        color: #666;
      }
      .btn3 {
        background: #f42634;
        color: #fff;
      }
    }
  }
}
</style>
