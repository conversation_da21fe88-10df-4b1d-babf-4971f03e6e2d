<template>
  <view class="openInvoice-page">
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">申请开票</block>
    </cu-custom>

    <view class="zhu-box">
      <view class="tou-box">
        <view class="zou">开票金额</view>
        <view class="you">
          <text style="font-size: 20rpx">￥</text>
          <text>{{ invoiceAmount }}</text>
        </view>
      </view>
      <view class="core-box">
        <view class="hang">
          <view class="qian">发票类型</view>
          <view class="hou">
            <view class="btn sel" style="width: 214rpx">电子普通发票</view>
          </view>
        </view>
        <view class="hang">
          <view class="qian">抬头类型</view>
          <view class="hou">
            <view
              class="btn"
              style="width: 116rpx"
              :class="[2 == invoiceType ? 'sel' : 'unsel']"
              @click="toggleType(2)"
            >
              单位
            </view>
            <view
              class="btn"
              style="width: 116rpx"
              :class="[1 == invoiceType ? 'sel' : 'unsel']"
              @click="toggleType(1)"
            >
              个人
            </view>
          </view>
        </view>
        <sel-title
          :invoiceType="invoiceType"
          @select="selectInvoiceTitle"
          @input="inputInvoiceTitle"
        />
        <view class="hang" v-if="2 == invoiceType">
          <view class="qian"><text style="color: red">*</text>单位税号</view>
          <view class="hou">
            <input
              class="wen"
              placeholder="请输入发票单位税号"
              v-model="invoiceTaxNo"
            />
          </view>
        </view>

        <block v-if="2 == invoiceType">
          <view class="kuai">更多信息</view>
          <view class="hang">
            <view class="qian">注册地址</view>
            <view class="hou">
              <input
                class="wen"
                placeholder="选填"
                v-model="vatCompanyAddress"
              />
            </view>
          </view>
          <view class="hang">
            <view class="qian">注册电话</view>
            <view class="hou">
              <input class="wen" placeholder="选填" v-model="vatTelphone" />
            </view>
          </view>
          <view class="hang">
            <view class="qian">开户银行</view>
            <view class="hou">
              <input class="wen" placeholder="选填" v-model="vatBankName" />
            </view>
          </view>
          <view class="hang">
            <view class="qian">银行账号</view>
            <view class="hou">
              <input class="wen" placeholder="选填" v-model="vatBankAccount" />
            </view>
          </view>
        </block>
        <view class="kuai">开票通知</view>
        <view class="hang">
          <view class="qian"><text style="color: red">*</text>手机号码</view>
          <view class="hou">
            <input
              class="wen"
              placeholder="请输入手机号码"
              v-model="notifyPhone"
            />
          </view>
        </view>
        <view class="hang">
          <view class="qian">
            <!-- <text style="color: red">*</text> -->
            电子邮箱
          </view>
          <view class="hou">
            <input
              class="wen"
              placeholder="请输入电子邮箱账号"
              v-model="email"
            />
          </view>
        </view>
      </view>
      <view class="annniu" @click="saveOpen">提交申请</view>
      <view class="annniu2" @click="goback">不开发票</view>
    </view>
    <submit-confirm
      :invoiceData="invoiceData"
      @close="saveClose"
      v-if="saveFlag"
    />
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月10日
 **/

const app = getApp();
import selTitle from "@/components/invoice/selTitle/index.vue";
import submitConfirm from "@/components/invoice/submitConfirm/index.vue";
import { applyInvoiceApi } from "@/pages/invoice/api/userinvoice.js";
import validate from 'utils/validate'

export default {
  name: "openInvoice",
  components: {
    selTitle,
    submitConfirm,
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      invoiceAmount: "",
      callbackParameter: "",
      totalPlateNo:"",//车牌号

      invoiceType: 2,
      invoiceTitle: "",
      invoiceTaxNo: "",
      notifyPhone: "",
      email: "",
      vatCompanyAddress: "",
      vatTelphone: "",
      vatBankName: "",
      vatBankAccount: "",

      invoiceData: "",
      saveFlag: false,
    };
  },
  onLoad(options) {
    if (options.params) {
      const res = JSON.parse(decodeURIComponent(options.params));
      this.invoiceAmount = res.invoiceAmount;
      this.callbackParameter = res.callbackParameter;
      this.totalPlateNo = res.totalPlateNo;
    }
  },
  methods: {
    goback() {
      uni.navigateBack();
    },
    toggleType(val) {
      this.invoiceType = val;
      this.invoiceTitle = "";
      this.invoiceTaxNo = "";
      this.vatCompanyAddress = "";
      this.vatTelphone = "";
      this.vatBankName = "";
      this.vatBankAccount = "";
    },

    //抬头名称
    selectInvoiceTitle(info) {
      if (2 == this.invoiceType) {
        this.invoiceTaxNo = "";
        this.vatCompanyAddress = "";
        this.vatTelphone = "";
        this.vatBankName = "";
        this.vatBankAccount = "";
        if (info) {
          const {
            invoiceTitle,
            invoiceTaxNo,
            vatCompanyAddress,
            vatTelphone,
            vatBankName,
            vatBankAccount,
          } = info;
          this.invoiceTitle = invoiceTitle;
          this.invoiceTaxNo = invoiceTaxNo;
          this.vatCompanyAddress = vatCompanyAddress;
          this.vatTelphone = vatTelphone;
          this.vatBankName = vatBankName;
          this.vatBankAccount = vatBankAccount;
        }
      } else {
        this.invoiceTitle = info.invoiceTitle;
      }
    },
    inputInvoiceTitle(val) {
      this.invoiceTitle = val;
    },

    //提交
    saveOpen() {
      let str = "";
      if (!this.invoiceTitle.trim()) {
        str = "抬头名称不能为空哦~"; // 有抬头肯定就有单位税号
      } else if (!validate.validateMobile(this.notifyPhone)) {
        str = "请先输入正确手机号码哦~";
      }
      else if (this.email) {
        // str = "请先输入电子邮箱哦~";
        const regex = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;
        if (!regex.test(this.email)) {
          str = "电子邮箱格式不正确哦~";
        }
      } 
      if (str) {
        uni.showToast({
          title: str,
          icon: "none",
        });
        return;
      }
      this.invoiceData = {
        invoiceTitle: this.invoiceTitle,
        invoiceType: this.invoiceType,
        invoiceTaxNo: this.invoiceTaxNo,
        notifyPhone: this.notifyPhone,
        email: this.email,
      };
      this.saveFlag = true;
    },
    saveClose(flag) {
      this.saveFlag = true;
      if (flag) {
        const params = {
          scene: 3,
          invoiceType: this.invoiceType,
          invoiceAmount: this.invoiceAmount,
          callbackParameter: this.callbackParameter,
          licensePlateNum:this.totalPlateNo,
          invoiceTitle: this.invoiceTitle,
          notifyPhone: this.notifyPhone,
          email: this.email,
        };
        if (2 == this.invoiceType) {
          params.invoiceTaxNo = this.invoiceTaxNo;
          params.vatCompanyAddress = this.vatCompanyAddress;
          params.vatTelphone = this.vatTelphone;
          params.vatBankName = this.vatBankName;
          params.vatBankAccount = this.vatBankAccount;
        }

        applyInvoiceApi(params).then(() => {
          uni.navigateTo({
            url: "/pages/invoice/parkApply/applyRes",
          });
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.openInvoice-page {
  .zhu-box {
    .tou-box {
      margin: auto;
      margin-top: 24rpx;
      width: 700rpx;
      height: 100rpx;
      padding-left: 24rpx;
      padding-right: 24rpx;
      line-height: 100rpx;
      background: #ffffff;
      border-radius: 22rpx;
      display: flex;
      justify-content: space-between;
      .zou {
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #000000;
      }
      .you {
        font-size: 40rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #f12533;
      }
    }
    .core-box {
      margin: auto;
      margin-top: 20rpx;
      width: 700rpx;
      min-height: 867rpx;
      padding: 20rpx 24rpx;
      background: #ffffff;
      border-radius: 22rpx;
      .hang {
        display: flex;
        margin-top: 20rpx;
        .qian {
          text-align: right;
          width: 130rpx;
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #888888;
          margin-right: 22rpx;
        }
        .hou {
          width: 500rpx;
          height: 72rpx;
          border-bottom: 1rpx dashed #333;
          display: flex;
          .btn {
            height: 56rpx;
            line-height: 56rpx;
            text-align: center;
            border-radius: 28rpx;
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 500;
            margin-right: 22rpx;
          }
          .sel {
            background: #f12533;
            color: #fff;
          }
          .unsel {
            background: #f4f2f7;
            color: #000000;
          }
          .wen {
            width: 490rpx;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #000000;
          }
        }
      }
      .kuai {
        margin-top: 18rpx;
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: #888888;
      }
    }
    .annniu {
      margin: auto;
      margin-top: 20rpx;
      width: 700rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: linear-gradient(90deg, #f32633 0%, #ce1a26 100%);
      color: #ffffff;
      border-radius: 40rpx;
    }
    .annniu2 {
      margin: auto;
      margin-top: 20rpx;
      width: 700rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: #ffffff;
      color: #f32634;
      border-radius: 40rpx;
    }
  }
}
</style>
