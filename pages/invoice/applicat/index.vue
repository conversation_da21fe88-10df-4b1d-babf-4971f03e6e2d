<template>
  <view>
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">申请发票</block>
		</cu-custom>

    <view v-if="isShow">
      <!-- 商品订单 -->
      <view class="flex justify-around align-center padding-sm bg-white radius-lg" style="width:95%;height:100%;margin:20rpx auto;">
				<view>
					<image style="width:100%;height:100%;border-radius:15rpx;"
						:src="goodsOrder.picUrl || $imgUrl('live/img/no_pic.png')" mode="aspectFill"
						class="row-img">
					</image>
				</view>

				<view class="padding-lr-sm" style="flex:1;">
					<view class="flex align-center margin-bottom-xs">
						<view class="text-black text-sm">
							订单编号
						</view>
						<view class="text-gray text-sm margin-left-sm">
							{{goodsOrder.orderIds}}
						</view>
					</view>
					<view class="flex align-center" @tap="modalInvoice='show'">
						<view class="text-black text-sm margin-right-sm">
							开票金额
						</view>
						<format-price signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx" :price="goodsOrder.remainSecond" ></format-price>
            <view class="cuIcon-info margin-left-xs"></view>
					</view>
				</view>
			</view>

      <!--添加和编辑  v-if="isShowEdit&&form"-->
      <view class="margin-sm" v-if="form&&individual&&individual.length>0">
        <addEdit :headType="'1'" :setData="form" :source="'1'" :isShowInvoiceSelect="false" :newIndividual="individual" @handle-invoice-data="handleCallback"></addEdit>
      </view>

      <!-- 发票说明弹框 -->
      <view :class="'cu-modal bottom-modal ' + modalInvoice" @tap.stop="modalInvoice = ''">
				<view class="cu-dialog bg-white " style="padding-bottom:500upx" @tap.stop>
          <view class="margin-top align-center" style="color: #888888;">
            <view class="flex justify-center margin-bottom margin">
              <text style="font-weight: 500;color: #000000;font-size: 30rpx;margin: auto;">开票金额说明</text>
              <text class="cuIcon-close " @tap.stop="modalInvoice = ''"></text>
            </view>
            <view class="text-sxm margin-xs align-center">
              <view class="margin-left-xs" style="display: inline-block; text-align: left"> 
                ·开票金额为消费者实付款金额, 优惠券、礼金、积分、红包 等不在开票范围内。
              </view>
              <view class="margin-top-sm"> ·如果订单发生退货退款，开票金额将变更为最终实付金额</view>
            </view>
          </view>
				</view>
			</view>
    </view> 

    <!-- 开票确认 -->
    <view v-else>
		  <invoice-info @handle-confirm-sub="handleConfirmSub" @handle-back = "handleRework"></invoice-info>
    </view>
  </view>
</template>

<script>
const app = getApp();
import formatPrice from "@/components/format-price/index.vue";
import addEdit from "../components/add-edit/index";
import invoiceInfo from "../components/invoice-info/index";
import {getUserInvoicePage } from "@/pages/invoice/api/userinvoice.js";

export default {
  components: {
			formatPrice,
      addEdit,
      invoiceInfo
		},
  data(){
    return{
      theme: app.globalData.theme, //全局颜色变量
      modalInvoice:'',//发票弹框说明
      isShow:false,
      goodsOrder:{
        createTime: "2022-08-15 10:39:04",
        deliveryTime: null,
        deliveryWay: "1",
        logistics: null,
        logisticsId: "1559006729232048129",
        logisticsNo: null,
        orderIds: "1559006729034915842",
        orderItemList: null,
        orderStatus: "1",
        picUrl: "https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/70fb0c81-5d28-4f61-b0d5-d70070bef892.png",
        remainSecond: 0,
        routeDate: "2022-08-15 10:39:04",
        routeInfo: "商家备货中。",
        status: "1",
        statusDesc: "备货中",
        transactionId:null ,
      },
      form: {
					id:'',
					isVat:'0',
					invoiceTitle: '',//发票抬头
					invoiceTaxNo: '',//单位税号
					vatCompanyAddress: '',//公司地址
					vatTelphone: '',//注册电话
					vatBankName: '',//开户银行
					invoiceType: '1',// 发票类型（1：个人 2：公司）
					vatBankAccount: '',//银行账号
					email:'',//电子信箱
					invoiceContent:'发票内容'
				},
				individual:[],//初始个人下拉数据
    }
  },
  created() {
			this.getUserInvoicePage();
		},
  methods: {
    //修改
    handleRework(){
      this.isShow = true
    },
    //确认提交
    handleConfirmSub(){
      uni.navigateTo({
        url: '/pages/invoice/detail/index?back=true'
      });
    },
    //处理回调
    handleCallback(parameter,bool){
      if (parameter) {
				console.log("parameter=====>",parameter);
				this.form = parameter
        this.isShow = bool
			}

    },

    //抬头列表管理接口
    async getUserInvoicePage(){
      const res =await getUserInvoicePage()
      if (res.data&&res.data.length>0) {
        let data =res.data
        let newIndividual = []
        let newCorporationData = []
          data.forEach(el => {
            if (el.invoiceType=='1') {
              newIndividual.unshift(el)
            }
            if (el.invoiceType=='2') {
              newCorporationData.unshift(el)
            }
          });
          newCorporationData= Array.from(new Set(newCorporationData))
          newIndividual= Array.from(new Set(newIndividual))
          this.form=newIndividual[0]
          this.individual=newIndividual
      }
    },
  }
}
</script>

<style scoped lang="scss">
.btn-size{
  width:542upx;
  background: #F32634;
  color: #FFFFFF;
  margin-top: 14upx;
}
.btn-red-size{
  width:542upx;
  background: #ffffff;
  color: #F32634;
  margin-top: 14upx;
}
.btn-bottom{
  width:100%;
  height: 165upx;
  position:fixed;
  bottom:0;
  left: 0;
}
.row-img {
  width: 96rpx !important;
  height: 96rpx !important;
  border-radius: 10rpx;
}

</style>