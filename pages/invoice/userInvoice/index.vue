<template>
  <view>
    <cu-custom
      :bgColor="'bg-' + theme.backgroundColor"
      :isBack="true"
      :hideMarchContent="true"
    >
      <block slot="backText">返回</block>
      <block slot="content">抬头管理</block>
    </cu-custom>

    <!-- 导航按钮 -->
    <!-- <scroll-view scroll-x class="bg-white nav fixed collection-types" style="box-shadow: none">
			<view class="flex text-center" style="background-color: #FFFFFF;" :style="{ width: collectTypeWidth }">
				<view :class="'btn ' +(item.key == type ? 'cur bg-' + theme.backgroundColor : 'bg-black-ash')"
					v-for="(item, index) in collectType" :key="index" @tap="navTabSelect" :data-index="index"
					:data-key="item.key">{{ item.value }}</view>
			</view>
		</scroll-view> -->
    <!-- 数据列表 '' 线上订单 1 线下订单 2 我的发票  3 抬头管理 -->
    <view :class="type !== '' ? 'margin-top-bars' : 'margin-top-content'">
      <view class="cu-list padding-bottom-sm">
        <!-- 线下订单 -->
        <template v-if="type == '1' && dataList">
          <view class="offline-margin">
            <offline-order :navTabType="type" :dataList="dataList" />
          </view>
        </template>

        <!-- 我的发票 -->
        <template v-else-if="type == '2'">
          <view class="offline-margin">
            <offline-order :navTabType="type" />
          </view>
        </template>

        <!-- 抬头管理 -->
        <template v-else-if="type == '3' && dataList">
          <view class="offline-margin">
            <head-admin
              :navTabType="type"
              :dataList="dataList"
              @head-refresh="refresh"
            />
          </view>
        </template>

        <!-- 全部订单 -->
        <template v-else="type == '' && dataList">
          <view class="offline-margin">
            <view class="cu-card article">
              <view
                v-for="(item, index) in dataList"
                :key="index"
                class="cu-item"
                style="
                  margin: 20rpx 12rpx;
                  border-radius: 24rpx;
                  padding: 24rpx;
                  position: relative;
                  overflow: initial;
                "
              >
                <orderList
                  :orderInfo="item"
                  :index="index"
                  :parameter="parameter"
                  @countDownDone="countDownDone($event)"
                  @orderDel="orderDel($event, index)"
                  @orderCancel="orderCancel($event, index)"
                  @orderReceive="orderCancel($event, index)"
                  @orderLogistics="orderLogistics($event, index)"
                />
              </view>
            </view>
            <view
              :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"
            ></view>
          </view>
        </template>
      </view>
      <view :class="'cu-load' + (loadmore ? 'loading' : 'over')"></view>
    </view>
  </view>
</template>
<script>
/**
 * <AUTHOR>
 * @date 2023年10月10日
 **/
const QR = require("utils/wxqrcode.js");
// const plugin = requirePlugin("logisticsPlugin")
const util = require("utils/util.js");
const app = getApp();
import api from "utils/api";
import offlineOrder from "../components/offline/index";
import headAdmin from "../components/head-admin/index";
import orderList from "@/components/order-list/index.vue";
import { getUserInvoicePage, offlineGoodsListApi } from "@/pages/invoice/api/userinvoice.js";

export default {
  components: {
    offlineOrder,
    headAdmin,
    orderList,
  },

  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      type: "3", //默认选中状态
      // navTabCur: 0,//导航栏选中
      // 导航栏
      collectType: [
        {
          value: "线上订单",
          key: "",
        },
        {
          value: "线下订单",
          key: "1",
        },
        {
          value: "我的发票",
          key: "2",
        },
        {
          value: "抬头管理",
          key: "3",
        },
      ],
      loadmore: true,
      dataList: [], //数据列表
      page: {
        searchCount: false,
        current: 1,
        size: 10,
        ascs: "",
        //升序字段
        descs: "create_time",
      },
      selectValue: [],
      listOrderInfo: [],
    };
  },

  computed: {
    collectTypeWidth() {
      const length = this.collectType.length;
      if (length <= 4) {
        return "750rpx";
      } else {
        return 187.5 * length + "rpx";
      }
    },
  },
  props: {},

  onShow() {
    app.initPage().then((res) => {
      this.loadmore = true;
      this.dataList = [];
      this.page.current = 1;
      this.userInvoicePage();
    });
  },

  onLoad(options) {},

  onReachBottom() {
    if (this.loadmore && this.type !== "3") {
      this.page.current = this.page.current + 1;
      this.userInvoicePage();
    }
  },

  onPullDownRefresh() {
    // 显示顶部刷新图标
    uni.showNavigationBarLoading();
    this.refresh(); // 隐藏导航栏加载框
    uni.hideNavigationBarLoading(); // 停止下拉动作
    uni.stopPullDownRefresh();
  },
  methods: {
    // 导航栏切换
    navTabSelect(e) {
      let dataset = e.currentTarget.dataset;
      if (dataset.key != this.type) {
        // this.navTabCur = dataset.index;
        this.type = dataset.key;
        this.refresh();
      }
    },

    userInvoicePage() {
      const { type } = this;
      console.log("userInvoicePage==》", type);
      if (+type === 1) {
        this.offlineGoodsList();
      } else if (+type === 2) {
      } else if (+type === 3) {
        this.getUserInvoicePage();
      } else {
        this.onLineOrder();
      }
    },

    //抬头列表管理接口
    async getUserInvoicePage() {
      const res = await getUserInvoicePage();
      console.log("data===>", res);
      let dataList = res.data;
      this.dataList = dataList;
    },

    //线下接口
    offlineGoodsList() {
      offlineGoodsListApi(Object.assign({}, this.page)).then((res) => {
        if (res.data != null) {
          // let dataList = res.data.rows;
          let dataList = [
            {
              createBy: null,
              createTime: null,
              isbill: 0,
              itemCount: 1,
              jygs: "002",
              marketName: "松雷-南岗店",
              params: {},
              remark: null,
              searchValue: null,
              selldetailZtnewList: null,
              sellpaymentZtnewList: null,
              shbillno: 5446647,
              shcustno: "0000133746",
              shdate: "2023-04-23",
              shdjlb: "1",
              shinvno: 9172,
              shmarket: "201",
              shoughtpay: 178,
              shsenddate: "2023-04-23",
              shsyjid: "116003",
              updateBy: null,
              updateTime: null,
            },
            {
              createBy: null,
              createTime: null,
              isbill: 0,
              itemCount: 2,
              jygs: "002",
              marketName: "松雷-南岗店",
              params: {},
              remark: null,
              searchValue: null,
              selldetailZtnewList: null,
              sellpaymentZtnewList: null,
              shbillno: 5446627,
              shcustno: "0000133746",
              shdate: "2023-04-23",
              shdjlb: "1",
              shinvno: 25150,
              shmarket: "201",
              shoughtpay: 397,
              shsenddate: "2023-04-23",
              shsyjid: "119110",
              updateBy: null,
              updateTime: null,
            },
          ];
          this.dataList = [...this.dataList, ...dataList];
          if (dataList.length < this.page.size) {
            this.loadmore = false;
          }
        } else {
          this.loadmore = false;
        }
      });
    },

    // 删除订单
    orderDel(item, index) {
      console.log(index);
      this.dataList.splice(index, 1);
    },

    // 查看物流
    orderLogistics(event, index) {
      console.log("event==>", event);
      // plugin.openWaybillTracking({
      // 	waybillToken: event.data.waybill_token
      // });
    },

    // 倒计时结束
    countDownDone(index) {
      this.$set(this.dataList[index], "orderStatusDesc", "交易关闭");
      this.$set(this.dataList[index], "statusDesc", "交易关闭");
      this.$set(this.dataList[index], "status", "5");
    },

    // 取消订单
    orderCancel(item, index) {
      api.orderGet(this.dataList[index].id).then((res) => {
        this.$set(this.dataList, index, res.data);
      });
    },

    //线上接口
    onLineOrder() {
      api.orderPage(Object.assign({}, this.page)).then((res) => {
        let orderList = res.data.records;
        console.log("onLineOrder===res==>", orderList);
        orderList.forEach((item) => {
          if (item.deliveryWay == "2" && item.status == "1") {
            item.qrImg = QR.createQrCodeImg(
              "/pages/mall/order/detail?id=" + item.id,
              {
                size: parseInt(300), //二维码大小
              }
            );
          }
        });
        this.dataList = [...this.dataList, ...orderList];
        if (orderList.length < this.page.size) {
          this.loadmore = false;
        }
      });
    },

    refresh(parameter) {
      this.loadmore = true;
      this.dataList = [];
      // this.page.current = 1;
      if (parameter == "add") {
        uni.showToast({
          title: "添加成功！",
          icon: "success",
        });
      }
      if (parameter == "edit") {
        uni.showToast({
          title: "编辑成功！",
          icon: "success",
        });
      }
      if (parameter == "del") {
        uni.showToast({
          title: "删除成功！",
          icon: "success",
        });
      }
      this.userInvoicePage();
    },
  },
};
</script>

<style scoped>
.collection-types {
  top: unset !important;
}

.bg-black-ash {
  background-color: #ebebeb !important;
  color: #000000;
}

.btn {
  height: 50rpx;
  width: 352rpx;
  line-height: 50rpx;
  margin: 18rpx 7rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  background-color: #fff;
}

.margin-top-bars {
  margin-top: 100rpx;
}

.margin-top-content {
  margin-top: 80rpx;
}

.offline-margin {
  margin: 0upx 20upx;
  /* border-radius: 30upx; */
}

.coupon-list {
  margin-top: 86rpx;
}

.coupon-order {
  position: fixed;
  right: 0;
  bottom: 300upx;
  width: 100upx;
  height: 100upx;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 24upx;
}
</style>
