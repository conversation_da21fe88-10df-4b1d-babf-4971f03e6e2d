<template>
  <view>
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">发票详情</block>
		</cu-custom>

    <view style="position: relative;">
      <image style="width:100%"
				:src="'https://img.songlei.com/invoice/invoice-info-bg.png' || $imgUrl('live/img/no_pic.png')" mode="aspectFill"
				class="invoice-img">
			</image>

      <view style="position: absolute;top: 20rpx;left: 60rpx;">
        <view class="text-xl text-white">
          已驳回
        </view>

        <view class="text-sxm text-white">
          商家已驳回您的申请，请联系商家咨询
        </view>

        <view style="width: 159%;position: absolute;left: -64rpx;top: 85rpx;">
          <invoice-info :setBtn="backBtn" @handle-back="handleBack"></invoice-info>
        </view>
      </view>
    </view>



  </view>
</template>

<script>
const app = getApp();
import invoiceInfo from "../components/invoice-info/index";

export default {
  components: {
      invoiceInfo
		},
  onLoad: function (options){
    if (options) {
        if (options.back) {
          this.backBtn = options.back
        }
    }
  },
  data(){
    return{
      theme: app.globalData.theme, //全局颜色变量
      backBtn:false,
    }
  },
  methods:{
    // 返回
    handleBack(){
      uni.navigateBack({
        delta: 1
      });
    },
  }
}
</script>

<style scoped>
.invoice-img{
  width: 100%;
  height: 195upx;
}
</style>

