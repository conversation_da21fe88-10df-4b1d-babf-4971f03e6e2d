<template>
  <view>
    <!-- 开票确认 -->
    <view class="cu-card no-card margin">
			<view class="cu-item padding-xs radius-lg" style="margin-bottom: 20rpx;border-radius: 30rpx;">
				<view class="margin-left-sm flex align-center">
						<text class="flex-sub text-df text-gray">发票类型 </text>
						<view class="flex-four text-df text-black margin-left-sm">
							纸质发票
						</view>
				</view>

				<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="flex-sub text-df text-gray">发票内容 </text>
						<view class="flex-four text-df text-black margin-left-sm">
              商品明细
						</view>
				</view>

				<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="flex-sub text-df text-gray">抬头类型 </text>
						<view class="flex-four text-df text-black margin-left-sm">
              单位
						</view>
				</view>
          
        <view class="margin-left-sm margin-top-xs flex align-center">
            <text class="flex-sub text-df text-gray">抬头名称 </text>
            <view class="flex-four text-df text-black margin-left-sm">
              李先生
            </view>
        </view>

        <view class="margin-left-sm margin-top-xs flex align-center">
            <text class="flex-sub text-df text-gray">申请时间 </text>
            <view class="flex-four text-df text-black margin-left-sm">
              2023-2-20 09:18:38
            </view>
        </view>

        <view class="margin-left-sm margin-top-xs flex align-center">
            <text class="flex-sub text-df text-gray">电子信箱 </text>
            <view class="flex-four text-df text-black margin-left-sm">
              <EMAIL>
            </view>
        </view>
			</view>

      <view class="cu-item padding-xs radius-lg" style="margin-bottom: 20rpx;border-radius: 30rpx;">
				<view class="margin-left-sm flex align-center">
						<text class="cuIcon-shop text-bold"></text>
						<view class="flex-four text-df text-black margin-left-sm text-bold">
							松雷南岗店超市
						</view>
				</view>
          
        <view class="margin-left-sm margin-top-xs flex align-center">
            <text class="flex-sub text-df text-gray">订单状态 </text>
            <view class="flex-four text-df text-black margin-left-sm">
              已完成
            </view>
        </view>

        <view class="margin-left-sm margin-top-xs flex align-center">
            <text class="flex-sub text-df text-gray">订单编号 </text>
            <view class="flex-four text-df text-black margin-left-sm">
              32083218564825997
            </view>
        </view>

        <view class="margin-left-sm margin-top-xs flex align-center">
            <text class="flex-sub text-df text-gray">申请时间 </text>
            <view class="flex-four text-df text-black margin-left-sm">
              2023-02-20 09:14:38
            </view>
        </view>
			</view>

      <!-- 提交 -->
      <view  class="text-center btn-bottom" style="height:180upx">
        <button v-if="!setBtn" class="cu-btn round btn-size" @tap="confirmSub">
          确认提交
        </button>
        <button @tap="gateBack()" class="cu-btn round" :class="setBtn?'btn-grey-size':'btn-red-size'">
          {{setBtn?'返回':'返回修改'}}
        </button>
      </view>
    </view>
  </view>
</template>

<script>

export default {
  nsme:'invoice-info',
  props:{
    setBtn:{
      type:Boolean,
      default:false
    }
  },
  data(){
    return{
      form: {
					id:'',
					isVat:'0',
					invoiceTitle: '',//发票抬头
					invoiceTaxNo: '',//单位税号
					vatCompanyAddress: '',//公司地址
					vatTelphone: '',//注册电话
					vatBankName: '',//开户银行
					invoiceType: '1',// 发票类型（1：个人 2：公司）
					vatBankAccount: '',//银行账号
					email:'',//电子信箱
					invoiceContent:'发票内容'
				},
    }
  },
  created() {

		},
  methods: {
    //确认提交
    confirmSub(){
      this.$emit('handle-confirm-sub')
    },
    //返回
    gateBack(){
      this.$emit('handle-back')
    }
  }
}
</script>

<style scoped lang="scss">
.btn-size{
  width:542upx;
  background: #F32634;
  color: #FFFFFF;
  margin-top: 14upx;
}
.btn-red-size{
  width:542upx;
  background: #ffffff;
  color: #F32634;
  margin-top: 14upx;
}
.btn-grey-size{
  width:542upx;
  background: #ffffff;
  color: #666666;
  margin-top: 14upx;
}
.btn-bottom{
  width:100%;
  height: 165upx;
  position:fixed;
  bottom:0;
  left: 0;
}

</style>