<template>
  <veiw>
    <view class="cu-item radius-lg bg-white" style="margin-bottom: 20rpx;border-radius: 30rpx;padding: 20rpx 10rpx;">
      <view v-if="source===''" class="margin-left-sm">
        <view class="flex-four text-df text-black margin-left-xs text-bold">
        {{type!='1'?'普通发票抬头-单位':'普通发票抬头-个人'}}
        </view>
      </view>
    
      <view v-if="source===''" class="margin-sm margin-sm flex align-center line margin-sm">
      </view>

      <form @submit="regSub">

        <view style="background: #FFFFFF;" :style="{height:source?'750upx':''}">
          <!-- 添加，编辑 抬头管理 -->
          <template v-if="source===''">
            <view v-if="!isShow" class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;">
              <view class="title" style="width: 24%;">发票类型</view>
              <view class="flex text-center" style="background-color: #FFFFFF;width:500rpx">
                <view class="round margin-xs bg-white-red" v-for="(item, index) in isVat" :key="index">
                  <view style="width: 162rpx;height: 56rpx;line-height: 56rpx;font-size: 24rpx;">
                    {{ item.value }}
                  </view>
                </view>
              </view>
            </view>
  
            <view v-if="!isShow" class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;">
              <view class="title" style="width: 24%;">抬头类型</view>
              <view class="flex text-center solid-top" style="background-color: #FFFFFF;width:500rpx;line-height:85rpx;height: 85rpx;">
                <view class="round margin-xs" :class="'btn ' +(item.key == type ? 'bg-white-red' : 'bg-black-ash')" v-for="(item, index) in invoiceType" :key="index" @tap="navTabSelect" :data-index="index"
                    :data-key="item.key">
                  <view style="width: 162rpx;height: 56rpx;line-height: 60rpx;font-size: 24rpx;">
                    {{ item.value }}
                  </view>
                </view>
              </view>
            </view>
  
            <view class="cu-form-group password_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;">
              <view class="title" style="width: 24%;">发票抬头</view>
              <block v-if="2 == type">
                <view style="width: 500rpx">
                  <input-fuzzy-search
                    ref="fuzzy"
                    @search="searchInvoiceTitleBefore"
                    :dataSource="dataSource"
                    @select="selectInvoiceTitle"
                    placeholder="请输入3个以上汉字"
                  />
                </view>
              </block>
              <block v-else>
                <input 
                  class="input-border solid-top" 
                  style="line-height:85rpx;height: 85rpx;"
                  placeholder="请输入发票抬头" 
                  type="text" name="invoiceTitle" 
                  v-model="form.invoiceTitle">
              </block>
            </view>
  
            <template v-if="type!='1'">
              <view class="cu-form-group password_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;">
                <view class="title" style="width: 24%;">单位税号</view>
                <input
                class="input-border solid-top"
                style="line-height:85rpx;height: 85rpx;"
                placeholder="请输入单位税号"
                name="invoiceTaxNo"
                type="text"
                v-model="form.invoiceTaxNo"
                >
              </view>
            </template>
            
            <template v-if="type!='1'">
              <view class="cu-form-group password_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" >
                <view class="title" style="width: 24%;">注册地址</view>
                <input
                    class="input-border solid-top"
                    style="line-height:85rpx;height: 85rpx;"
                    placeholder="选填"
                    type="text"
                    name="vatCompanyAddress"
                    v-model="form.vatCompanyAddress"
                  >
              </view>
    
              <view class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" >
                <view class="title" style="width: 24%;" >注册电话</view>
                <input
                    class="input-border solid-top"
                    placeholder="选填"
                    style="line-height:85rpx;height: 85rpx;"
                    name="vatTelphone"
                    type="number"
                    v-model="form.vatTelphone"
                >
              </view>
    
              <view class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" >
                <view class="title" style="width: 24%;" >开户银行</view>
                <input
                  class="input-border solid-top"
                  style="line-height:85rpx;height: 85rpx;"
                  placeholder="选填"
                  name="vatBankName"
                  type="text"
                  v-model="form.vatBankName"
                >
              </view>
    
              <view class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" >
                <view class="title" style="width: 24%;">银行账号</view>
                <input
                  class="input-border solid-top"
                  style="line-height:85rpx;height: 85rpx;"
                  placeholder="选填"
                  name="vatBankAccount"
                  type="number"
                  v-model="form.vatBankAccount"
                >
              </view>
            </template>
          </template>

          <!-- 商品详情 申请开票-->
          <scroll-view v-else scroll-y scroll-with-animation :style="{
            height: `${source==='0'?'620rpx':(source==='1'&&type==='1'?'450rpx':'750rpx')}`,
            marginBottom:`${source==='0'?'115rpx':'0'}`
            }">   
            <view v-if="!isShow" class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;">
              <view class="title" style="width: 24%;">发票类型</view>
              <view class="flex text-center" style="background-color: #FFFFFF;width:500rpx">
                <view class="round margin-xs bg-black-red" v-for="(item, index) in isVat" :key="index">
                  <view style="width: 162rpx;height: 56rpx;line-height: 56rpx;font-size: 24rpx;">
                    {{ item.value }}
                  </view>
                </view>
              </view>
            </view>
  
            <view class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;">
              <view class="title" style="width: 24%;">发票内容</view>
              <view class="flex text-center solid-top" style="background-color: #FFFFFF;width:500rpx;line-height:85rpx;height: 85rpx;">
                <view class="round margin-xs bg-black-red" >
                  <view style="width: 162rpx;height: 56rpx;line-height: 60rpx;font-size: 24rpx;">
                    {{ source=='0'?'商品明细':'商品类别' }}
                  </view>
                </view>
              </view>
            </view>
  
            <view v-if="!isShow" class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;">
              <view class="title" style="width: 24%;">抬头类型</view>
              <view class="flex text-center solid-top" style="background-color: #FFFFFF;width:500rpx;line-height:85rpx;height: 85rpx;">
                <view class="round margin-xs" :class="'btn ' +(item.key == type ? 'bg-black-red' : 'bg-black-ash')" v-for="(item, index) in invoiceType" :key="index" @tap="navTabSelect" :data-index="index"
                    :data-key="item.key">
                  <view style="width: 162rpx;height: 56rpx;line-height: 60rpx;font-size: 24rpx;">
                    {{ item.value }}
                  </view>
                </view>
              </view>
            </view>
            
            <view class="cu-form-group password_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;">
              <view class="title" style="width: 24%;">发票抬头</view>
              <input 
                class="input-border solid-top" 
                style="line-height:85rpx;height: 85rpx;"
                cursor-spacing="50"
                placeholder="请输入发票抬头" 
                type="text" name="invoiceTitle" 
                @focus="focusGetInvoiceSelect(form,$event)"
                v-model="form.invoiceTitle">
            </view>

            <!--抬头返显下拉数据-->
            <template v-if="isShowSelect">
              <view class=" password_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" v-for="(ele, index) in invoiceSelect" :key="index">
                <view 
                @tap="setInvoice(ele)" 
                class="input-border padding-left-xs solid"
                style="line-height:85rpx;height: 85rpx;width: 82%;margin: auto;"
                >
                {{ ele.invoiceTitle }}
                </view>
              </view>
            </template>
  
            <template v-if="type!='1'">
              <view class="cu-form-group password_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;">
                <view class="title" style="width: 24%;">单位税号</view>
                <input
                class="input-border"
                :class="isShowSelect&&type!='1'?'border-none':'solid-top'"
                style="line-height:85rpx;height: 85rpx;"
                placeholder="请输入单位税号"
                name="invoiceTaxNo"
                type="text"
                v-model="form.invoiceTaxNo"
                >
              </view>
            </template>
            
            <view class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" >
              <view class="title" style="width: 24%;">电子邮箱</view>
              <input
              class="input-border"
              :class="isShowSelect&&type=='1'?'border-none':'solid-top'"
              style="line-height:85rpx;height: 85rpx;"
              placeholder="请输入收票人电子邮箱"
              name="email"
              type="text"
              v-model="form.email"
              >
            </view>
            
            <template v-if="type!='1'">
              <view class="cu-form-group password_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" >
                <view class="title" style="width: 24%;">注册地址</view>
                <input
                    class="input-border solid-top"
                    style="line-height:85rpx;height: 85rpx;"
                    placeholder="选填"
                    type="text"
                    name="vatCompanyAddress"
                    v-model="form.vatCompanyAddress"
                  >
              </view>
    
              <view class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" >
                <view class="title" style="width: 24%;" >注册电话</view>
                <input
                    class="input-border solid-top"
                    placeholder="选填"
                    style="line-height:85rpx;height: 85rpx;"
                    name="vatTelphone"
                    type="number"
                    v-model="form.vatTelphone"
                >
              </view>
    
              <view class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" >
                <view class="title" style="width: 24%;" >开户银行</view>
                <input
                  class="input-border solid-top"
                  style="line-height:85rpx;height: 85rpx;"
                  placeholder="选填"
                  name="vatBankName"
                  type="text"
                  v-model="form.vatBankName"
                >
              </view>
    
              <view class="cu-form-group phone_margin border-none" style="margin-top: 0rpx;min-height: 85rpx;" >
                <view class="title" style="width: 24%;">银行账号</view>
                <input
                  class="input-border solid-top"
                  style="line-height:85rpx;height: 85rpx;"
                  placeholder="选填"
                  name="vatBankAccount"
                  type="number"
                  v-model="form.vatBankAccount"
                >
              </view>
            </template>

            <view v-if="source==='0'" class="margin-sm margin-sm flex align-center line margin-sm"></view>

            <view v-if="source==='0'" class="margin" style="color: #888888;font-size: 30upx;">
              <view class="text-center text-lg" style="font-weight: 500;color: #000000">开票金额说明</view>
              <view class="text-df"> ·开票金额为消费者实付款金额, 优惠券、礼金、积分、红包 等不在开票范围内。</view>
              <view class="text-df margin-top-sm"> ·如果订单发生退货退款，开票金额将变更为最终实付金额</view>
            </view>
          </scroll-view>
  
            <!-- 提交 -->
          <view  class="text-center" :class="source==='0'?'bg-white new-btn-bottom':(source==='1'?'new-btn-bottom':'bg-white btn-bottom')" :style="{
            height:`${source==='1'?'180rpx':'130rpx'}`}">
              <button v-if="isShow" @tap="delInvoice(form.id)" class="cu-btn round del-btn-size">
                删除
              </button>
  
              <button class="cu-btn round " :class="!isShow?'btn-size':'edit-btn-size'" form-type="submit">
                {{source==='0'?'确认':(source==='1'?'提交申请':'提交')}}
              </button>

              <button v-if="source==='1'" @tap="gateBack()" class="cu-btn round btn-red-size">
                不开发票
              </button>
          </view>
        </view>
      
      </form>
    </view> 

    <view v-if="source===''" class="margin" style="color: #888888;font-size: 26rpx;">
      注：可点击相应项目编辑
    </view>

  </veiw>
</template>

<script>
  import inputFuzzySearch from "@/components/invoice/inputFuzzySearch/index.vue";
  import { debounceGet } from "@/utils/util.js";
  import validate from 'utils/validate';
  import { fuzzyQueryInvoiceTitleApi, getInvoiceTitleInfoApi, addUserInvoice,putUserInvoice,delUserInvoice,getUserInvoicePage } from "@/pages/invoice/api/userinvoice.js"

export default {
  name:'add-edit',
  components: {
    inputFuzzySearch,
  },  
  props:{
    //抬头状态类型（1：个人 2：公司）
    headType:{
      type:String,
      default:''
    },
    // 数据
    setData:{
      type:Object,
      default:{},
    },
    //区分false添加和true编辑
    isShow:{
      type:Boolean,
      default:false
    },
    //使用来源 
    /*
    * 0:从商品详情来;1:从全部商品列表申请发票
    
    */
    source:{
      type:String,
      default:''
    },
    //控制抬头下拉框
    isShowInvoiceSelect:{
      type:Boolean,
      default:false
    },
    //初始化个人下拉显示数据
    newIndividual:{
      type:Array,
      default:[]
    },
    
  },
  watch: {
    headType: {
			handler(newVal, oldVal) {
        this.type = newVal
      },
			immediate: true
		},
    setData: {
			handler(newVal, oldVal) {
        if (newVal) {
          this.form = newVal
          this.$nextTick(()=>{
            if (this.$refs.fuzzy) {
              this.$refs.fuzzy.name = newVal.invoiceTitle;
            }
          })
        }
      },
			immediate: true
		},
  },
  mounted(){
    this.debounce = debounceGet(() => {
      this.searchInvoiceTitle();
    }, 1000);    
  },
  data(){
    return{
				type: '2',//默认选中状态
				// 抬头类型导航栏
				invoiceType: [{
					value: '单位',
					key: '2'          
				}, {
					value: '个人',
					key: '1'
				}
      ],
      debounce: "",
      dataSource: [],
      //发票类型是否增值税发票(0：否 ；1：是)
      isVat:[{
					value: '普通发票',
					key: '0'
				},
        // {
				// 	value: '增值税专用发票',
				// 	key: '1'
				// }
      ],
      //发票内容(0：商品明细 ；1：商品类别)
      invoiceContent:[{
					value: '商品明细',
					key: '0'
				},
        {
					value: '商品类别',
					key: '1'
				}
      ],
      content:'商品明细',
      form:{
					invoiceTitle: '',//发票抬头
					invoiceTaxNo: '',//单位税号
					vatCompanyAddress: '',//公司地址
					vatTelphone: '',//注册电话
					vatBankName: '',//开户银行
					vatBankAccount: '',//银行账号
      },//表单数据
      invoiceSelect:[],//抬头下拉框
      isShowSelect:this.isShowInvoiceSelect,//下拉框显示
      corporationData:[],//单位数据
      individual:this.newIndividual,//个人
    }

  },
  methods:{
    //不开发票
    gateBack(){
      uni.navigateBack({
        delta: 1
      });
    },

    //公司发票抬头模糊搜索
    searchInvoiceTitle() {
      this.dataSource = [];
      const params = {
        invoiceTitle: this.form.invoiceTitle,
      };
      fuzzyQueryInvoiceTitleApi(params).then((res) => {
        if (res.data) {
          res.data.forEach((u) => {
            this.dataSource.push({
              text: u.companyname,
            });
          });
        }
      });
    },
    searchInvoiceTitleBefore(val) {
      if (val.trim().length > 3) {
        this.form.invoiceTitle = val;
        this.debounce();
      }
    },
    //公司发票抬头选中详情
    selectInvoiceTitle(info) {
      this.form.invoiceTaxNo = "";
      this.form.vatCompanyAddress = "";
      this.form.vatTelphone = "";
      this.form.vatBankName = "";
      this.form.vatBankAccount = "";
      const params = {
        invoiceTitle: info.text,
      };
      getInvoiceTitleInfoApi(params).then((res) => {
        if (res.data) {
          const {
            taxpayerName,
            taxpayerNo,
            taxpayerAddress,
            taxpayerTelephone,
            taxpayerBankName,
            taxpayerBankAccount,
          } = res.data;
          this.form.invoiceTitle = taxpayerName;
          this.form.invoiceTaxNo = taxpayerNo;
          this.form.vatCompanyAddress = taxpayerAddress;
          this.form.vatTelphone = taxpayerTelephone;
          this.form.vatBankName = taxpayerBankName;
          this.form.vatBankAccount = taxpayerBankAccount;
        }
      });
    },

    //设置发票为下拉框
    setInvoice(ele){
      if (ele) {
        this.form = ele;
        this.isShowSelect=false;
        this.getUserInvoicePage(true)
      }
    },
    //抬头下拉框
    focusGetInvoiceSelect(data,e){
      if (data&&data.id) {
        this.isShowSelect=true;
        // this.getUserInvoicePage(data.id)
          // 抬头下拉框，排除第一个
        if (this.type=='1') {
          this.invoiceSelect =this.individual.filter((i)=>{
            return i.id !== data.id
          })
        }else{
          this.invoiceSelect =this.corporationData.filter((i)=>{
            return i.id !== data.id
          })
        }
      }
    },

    //获取抬头信息
    async getUserInvoicePage(bool){
        const res =await getUserInvoicePage()
				if (res.data) {
					let data =res.data
					let newIndividual = []
          let newCorporationData = []
          //区分个人，单位
          data.forEach(el => {
            if (el.invoiceType=='1') {
              newIndividual.unshift(el)
            }
            if (el.invoiceType=='2') {
              newCorporationData.unshift(el)
            }
          });
          newCorporationData= Array.from(new Set(newCorporationData))
          newIndividual= Array.from(new Set(newIndividual))
          this.individual=newIndividual;
          this.corporationData = newCorporationData
            if (this.type=='1') {
              //默认返显第一个
              this.invoiceSelect =newIndividual
              if (bool)return
              this.form = newIndividual[0]
            }else{
              this.invoiceSelect =newCorporationData
              if (bool)return
              this.form = newCorporationData[0]
            }
				}
      },

    // 重置
    reset() {
        this.form= {
          invoiceTitle: '',//发票抬头
          invoiceContent:'',//发票内容
          invoiceTaxNo: '',//单位税号
          vatCompanyAddress: '',//公司地址
          vatTelphone: '',//注册电话
          vatBankName: '',//开户银行
          invoiceType: this.type,// 发票类型（1：个人 2：公司）
          vatBankAccount: '',//银行账号
          email:'',//电子信箱
        }
			},

    	// 切换
			navTabSelect(e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.key != this.type) {
					this.type = dataset.key;
          if (this.source) {
            this.isShowSelect =false;
            this.getUserInvoicePage()
            return
          }
          this.reset()
				}
			},

      //发票内容切换
      invoiceContentSelect(e) {
				let dataset = e.currentTarget.dataset;
        this.content = dataset.value
        this.$set(this.form,'invoiceContent',dataset.value )
        console.log("invoiceContentSelect==>",this.form,e.currentTarget);
			},

     //提交
      regSub (e) {
        console.log("下一步", e);
        let that = this
        that.form.invoiceType=this.type
        console.log(" this.form===>", this.form,this.type);
      if (!that.form.invoiceType) {
        uni.showToast({
          title: '请选择发票类型',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (!that.form.invoiceTitle) {
        uni.showToast({
          title: '请输入发票抬头',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (that.type =='2'&&!that.form.invoiceTaxNo) { 
        uni.showToast({
          title: '请输入单位税号',
          icon: 'none',
          duration: 3000
        });
        return;
      }else if(that.type =='2'&&!validate.invoiceTax(that.form.invoiceTaxNo)){
        uni.showToast({
          title: '单位税号为15或17或18或20位数字大写字母组合，请检查！',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (that.source&&!validate.validateEmail(that.form.email)) {
        uni.showToast({
          title: '请输入正确收票人电子邮箱',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      if (that.source==='0') {
        that.$emit("handle-invoice-data",that.form,'')
        uni.setStorageSync('invoice',that.form)
        return;
      }

      if (that.source==='1') {
        that.$emit("handle-invoice-data",that.form,false)
        // uni.setStorageSync('invoice',that.form)
        return;
      }
    

      if (!that.isShow) {
        addUserInvoice(Object.assign({}, that.form)).then(res=>{
          if (res.data) {
            that.$emit('handle-add-edit','add',false)
          }
        })
      }else{
        putUserInvoice(Object.assign({}, that.form)).then(res=>{
          if (res.data) {
            that.$emit('handle-add-edit','edit',false)
          }
        })
      }
    },

    //删除
    delInvoice(id){
      console.log('=====',id);
      delUserInvoice(id).then(res=>{
          if (res.data) {
            this.$emit('handle-add-edit','del',false)
          }
        })
    }
  }

}
</script>

<style scoped>
.border-none{
  border: none;
}
.del-btn-size{
  width:270upx;
  background: #FFFFFF;
  color: #000000;
  margin-top: 14upx;
  border: 1upx solid #ccc;
  margin-right: 30upx;
}
.edit-btn-size{
  width:270upx;
  background: #F32634;
  color: #FFFFFF;
  margin-top: 14upx;
}
.bg-black-red{
  background-color: #FDEDEE !important;
	color: #F12533;
}
.bg-white-red{
  /* 原先采用的颜色是bg-black-red */
  background-color: #f12533 !important;
  color: #fff;
}
.bg-black-ash {
	background-color: #F4F2F7 !important;
	color: #000000;
}
.btn-size{
  width:542upx;
  background: #F32634;
  color: #FFFFFF;
  margin-top: 14upx;
}
.btn-red-size{
  width:542upx;
  background: #ffffff;
  color: #F32634;
  margin-top: 14upx;
}
.btn-bottom{
  width:100%;
  height: 165upx;
  position:fixed;
  bottom:0;
  left: 0;
}
.new-btn-bottom{
  width:100%;
  height: 115upx;
  position:fixed;
  bottom:0;
  left: 0;
}
.line-personal{
  width: 95%;
  height: 2upx;
  /* background: linear-gradient(to right, #cccccc, #cccccc 7.5upx, transparent 7.5upx, transparent); */
  background: radial-gradient(#cccccc, #cccccc 4px, transparent 4px, transparent);
  background-size: 10upx 100%;
}

.line{
  width: 95%;
  height: 4upx;
  background: linear-gradient(to right, #999999, #999999 7.5upx, transparent 7.5upx, transparent);
  /* background: radial-gradient(#000000, #000000 4px, transparent 4px, transparent); */
  background-size: 10upx 100%;
}

</style>