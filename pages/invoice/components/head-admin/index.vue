<template>
  <view>
    <view class="cu-card no-card margin-top" style="margin-bottom:165upx;">
      <view v-if="!isShowEdit&&dataList&&dataList.length>0">
        <view class="flex justify-center" style="margin: 15rpx 50rpx 15rpx 10rpx;">
          <view><text class="cuIcon-info margin-xs" style="color:#FF0000;font-size: 38rpx;"></text></view>
          <view class="text-sm margin-left-xs" style="margin-top: 2rpx;color:#FF0000">
            抬头信息仅用于开具发票，请勿用于转账等其他用途，谨防受骗!
          </view>
        </view>
        <!-- 单位 -->
        <view v-if="corporationData&&corporationData.length>0" class="cu-item radius-lg bg-white" style="margin-bottom: 20rpx;border-radius: 30rpx;padding: 20rpx 10rpx;">
          <view class="margin-left-sm">
            <view class="flex-four text-df text-black margin-left-xs text-bold">
            普通发票抬头-单位
            </view>
          </view>
  
          <view class="margin-sm margin-sm flex align-center line margin-sm">
          </view>

          <view v-for="(item,index) in corporationData" >
            <view v-if="item.invoiceType=='2'" :key="index" class="margin-left-sm margin-top-xs flex justify-between" style="margin-bottom: 35rpx;">
              <view class="text-df text-gray">
                <view class="text-black">
                  {{item.invoiceTitle}}
                </view>
                <view class="text-grey">
                  {{item.invoiceTaxNo}}
                </view>
              </view>
              <view @tap="addInvoice(item.id)" class="text-df text-black margin-top-xs margin-right-sm">
                <view class="cuIcon-write"></view>
                <view style="border-top: 1rpx solid"></view>
              </view>
            </view>
          </view>
        </view> 
        
        <!-- 个人 -->
        <view v-if="individual&&individual.length>0" class="cu-item radius-lg bg-white" style="margin-bottom: 20rpx;border-radius: 30rpx;padding: 20rpx 10rpx;">
          <view class="margin-left-sm">
            <view class="flex-four text-df text-black margin-left-xs text-bold">
              普通发票抬头-个人
            </view>
          </view>

          <view class="margin-sm margin-sm flex align-center line-personal margin-sm">
          </view>
  
          <view v-for="(item,index) in individual" :key="index" class="margin-left-sm margin-top-xs flex justify-between" style="margin-bottom: 35rpx;">
            <view class="text-df text-gray">
              <view class="text-black">
                {{item.invoiceTitle}}
              </view>
            </view>
            <view @tap="addInvoice(item.id)" class="text-df text-black margin-top-xs margin-right-sm">
              <view class="cuIcon-write"></view>
              <view style="border-top: 1rpx solid"></view>
            </view>
          </view>
        </view> 
      </view>

      <view v-else-if="!isShowEdit" class="text-center">
        <image
          src="https://img.songlei.com/userinvoice/none.png"
          mode="aspectFit"
          style="width:429rpx;height: 352rpx;"
        />
        <view class="text-center">
          暂无数据
        </view>
      </view>

      <!--添加和编辑  -->
      <view v-if="isShowEdit&&form">
        <addEdit :headType="type" :setData="form" :isShow="isControl" @handle-add-edit="handleCallback"></addEdit>
      </view>

      <view v-if="!isShowEdit" class="bg-white text-center btn-bottom">
        <!-- 添加台头-->
          <view class="cu-btn round btn-size" @click="addInvoice">
            添加发票抬头
          </view>
      </view>
		</view>
  </view>
</template>

<script>
const app = getApp();
import addEdit from "../add-edit/index"

import { editUserInvoice} from "@/pages/invoice/api/userinvoice.js"

export default {
  name:'head-admin',
  props:{
    //tap栏状态
    navTabType:{
      type:String,
      default:'3'
    },
    // 接口数据
    dataList:{
      type:Array,
      default:[],
    }
  },
  watch: {
    dataList: {
				handler(newVal, oldVal) {
        console.log("this.dataList===watch==",newVal);
          if (newVal) {
            let newIndividual = []
            let newCorporationData = []
            newVal.forEach(el => {
              if (el.invoiceType=='1') {
                newIndividual.unshift(el)
              }
              if (el.invoiceType=='2') {
                newCorporationData.unshift(el)
              }
            });
            this.corporationData= Array.from(new Set(newCorporationData))
            this.individual= Array.from(new Set(newIndividual))
          }
      },
			immediate: true
		},
  },

  components: {
		addEdit,
		},

  data(){
    return{
			type: '2',//默认选中状态
      isShowEdit:false,//是否显示添加编辑
      isControl:false,//控制是编辑还是添加
      form: {
        isVat:'0',
        invoiceTitle: '',//发票抬头
        invoiceTaxNo: '',//单位税号
        vatCompanyAddress: '',//公司地址
        vatTelphone: '',//注册电话
        vatBankName: '',//开户银行
        invoiceType: '',// 发票类型（1：个人 2：公司）
        vatBankAccount: ''//银行账号
      },
      corporationData:[],//公司数据
      individual:[],//个人数据
    }
  },
  methods:{

    // 重置
    reset() {
        this.form= {
          invoiceTitle: '',//发票抬头
          invoiceTaxNo: '',//单位税号
          vatCompanyAddress: '',//公司地址
          vatTelphone: '',//注册电话
          vatBankName: '',//开户银行
          invoiceType:this.isControl? this.type:'2',// 发票类型（1：个人 2：公司）
          vatBankAccount: ''//银行账号
        }
			},

      // 点击添加发票抬头
      addInvoice(id){
        this.isShowEdit=true;
        // this.isControl=false;
        // 有值就编辑返显
        if (id&&id.length>0) {
          this.getInvoice(id);
          this.isControl=true;
        }else{
          this.isControl=false;
          this.type = '2'
          this.reset()
        }
      },

      // 通过id获取抬头详情返显
      async getInvoice(id){
        console.log("id====>",id);
        const res = await editUserInvoice(id)
        let data = res.data;
        this.form=data
        this.type = data.invoiceType
        console.log("data===>",data,this.form);
      },

    	// 切换
			navTabSelect(e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.key != this.type) {
					this.type = dataset.key;
          this.reset()
				}
        console.log("type===>",dataset,this.type);
			},

      //处理回调
      handleCallback(parameter,bool){
        if (parameter=='add') {
          this.$emit('head-refresh','add')
        }
        if (parameter=='edit') {
          this.$emit('head-refresh','edit')
        }
        if (parameter=='del') {
          this.$emit('head-refresh','del')
        }
        this.isShowEdit=bool
      },
  }

}
</script>

<style scoped>
.btn-size{
  width:542upx;
  height: 82upx;
  background: #F32634;
  color: #FFFFFF;
  margin-top: 14upx;
}
.btn-bottom{
  width:100%;
  height: 165upx;
  position:fixed;
  bottom:0;
  left: 0;
}
.line-personal{
  width: 95%;
  height: 2upx;
  /* background: linear-gradient(to right, #cccccc, #cccccc 7.5upx, transparent 7.5upx, transparent); */
  background: radial-gradient(#cccccc, #cccccc 4px, transparent 4px, transparent);
  background-size: 10upx 100%;
}

.line{
  width: 95%;
  height: 4upx;
  background: linear-gradient(to right, #999999, #999999 7.5upx, transparent 7.5upx, transparent);
  /* background: radial-gradient(#000000, #000000 4px, transparent 4px, transparent); */
  background-size: 10upx 100%;
}

</style>