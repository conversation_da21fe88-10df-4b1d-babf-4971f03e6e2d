<template>
  <view>
    <view class="cu-card no-card margin-top">
				<view class="cu-item padding-xs radius-lg" style="margin-bottom: 20rpx;border-radius: 30rpx;"  v-for="(item,index) in newDataList" :key="index">
					<view class="margin-left-sm flex align-center">
						<text class="flex-sub text-df text-gray">购物门店 </text>
						<view class="flex-four text-df text-black margin-left-sm text-bold">
							{{item.marketName}}
						</view>
					</view>

					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="flex-sub text-df text-gray">小票日期 </text>
						<view class="flex-four text-df text-black margin-left-sm">
              {{item.shsenddate}}
						</view>
					</view>

					<view v-if="item.open!==true" class="margin-left-sm margin-top-xs flex align-center">
						<text class="flex-sub text-df text-gray">实付金额 </text>
						<view class="flex-four text-df text-black margin-left-sm">
              <format-price 
                  styleProps="font-weight:bold" 
                  signFontSize="20rpx" 
                  smallFontSize="24rpx" 
                  priceFontSize="28rpx"
                  color="#000000"
                  :price="item.shoughtpay"
                />
						</view>
					</view>
          
					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="flex-sub text-df text-gray">商品件数 </text>
						<view class="flex-four text-df text-black margin-left-sm">
              {{item.itemCount}}
						</view>
					</view>
          
          <view class="box" :class="item.selldetailZtnewList.length===1?'margin-top':''" v-if="item.open==true&&item.selldetailZtnewList&&item.selldetailZtnewList.length">
            <!-- 商品明细 -->
            <view :class="item.selldetailZtnewList.length===1?'':'margin-top'" v-for="(el,key) in item.selldetailZtnewList" :key="key">
              <view class="margin-left-sm margin-top-xs flex align-center">
                <text class="flex-sub text-df text-gray">商品明细 </text>
                <view class="flex-four text-df text-black margin-left-sm overflow-1">
                  {{el.gbcname}}
                </view>
              </view>
    
              <view class="margin-left-sm margin-top-xs flex align-center">
                <text class="flex-sub text-df text-gray">单价 </text>
                <view class="flex-four text-df text-black margin-left-sm">
                  {{el.sdprice}}
                </view>
              </view>
    
              <view class="margin-left-sm margin-top-xs flex align-center">
                <text class="flex-sub text-df text-gray">数量 </text>
                <view class="flex-four text-df text-black margin-left-sm">
                  {{el.sdsl}}
                </view>
              </view>
    
              <view class="margin-left-sm margin-top-xs flex align-center">
                <text class="flex-sub text-df text-gray">折扣 </text>
                <view class="flex-four text-df text-black margin-left-sm">
                  {{el.sdzkje}}
                </view>
              </view>
    
              <view class="margin-left-sm margin-top-xs flex align-center">
                <text class="flex-sub text-df text-gray">小计 </text>
                <view class="flex-four text-df text-black margin-left-sm">
                  <format-price 
                    styleProps="font-weight:bold" 
                    signFontSize="20rpx" 
                    smallFontSize="24rpx" 
                    priceFontSize="28rpx"
                    color="#000000"
                    :price="el.sdcjje"
                  />
                </view>
              </view>
            </view>

            <!-- 交易 -->
            <view class="margin-top">
              <view class="margin-left-sm margin-top-xs flex align-center">
                <text class="flex-sub text-df text-gray">交易类型 </text>
                <view class="flex-four text-df text-black margin-left-sm">
                  {{item.sellpaymentZtnewList[0].name }}
                </view>
              </view>
  
              <view class="margin-left-sm margin-top-xs flex align-center">
                <text class="flex-sub text-df text-gray">收款机号 </text>
                <view class="flex-four text-df text-black margin-left-sm">
                  {{item.shsyjid}}
                </view>
              </view>
              
              <view class="margin-left-sm margin-top-xs flex align-center">
                <text class="flex-sub text-df text-gray">实付金额 </text>
                <view class="flex-four text-df text-black margin-left-sm">
                  <format-price 
                  styleProps="font-weight:bold" 
                  signFontSize="20rpx" 
                  smallFontSize="24rpx" 
                  priceFontSize="28rpx"
                  color="#000000"
                  :price="item.shoughtpay"
                />
                </view>
              </view>
            </view>
          </view>

          <view v-if="navTabType=='1'" class="margin-left-sm margin-top-xs flex align-center line margin-right-sm">
					</view>

          <view v-if="navTabType=='1'" class="margin-top-sm margin-bottom-xs" style="display: flex;justify-content: flex-end;">
						<view style="height: 56upx;border: 1upx solid rgba(153, 153, 153, 1)"  class="cu-btn round  shadow-blur bg-white" @tap="changeContent(item,index)" :style="{width:item.open?'200upx':'500upx'}">
              {{item.open?'收起':`还有${item.itemCount}件商品`}}
              <text class=" margin-left-sm" :class="item.open?'cuIcon-fold':'cuIcon-unfold'"></text>
            </view>
						<view style="width: 200upx;height: 56upx;border: 1upx solid rgba(153, 153, 153, 1)"  class="cu-btn round  shadow-blur margin-left bg-white">
              {{item.isbill==0?'待开发票':'查看发票'}}</view>
					</view>
				</view> 
			</view>
  </view>
</template>

<script>
import { offlineGoodsDetail } from "@/pages/invoice/api/userinvoice.js";
import formatPrice from "@/components/format-price/index.vue";
export default {
  name:'offline-order',
  components:{
    formatPrice
  },
  props:{
    navTabType:{
      type:String,
      default:'1'
    },
    dataList: {
      type: Array,
      default: () => []
    },
  },
  watch: {
      navTabType(newVal, oldVal) {
        console.log("this.navTabType",newVal,oldVal);
        },
      dataList: {
				handler(newVal, oldVal) {
          
          if (newVal) {
            newVal.forEach((el,index) => {
              this.$set(newVal[index],'open',false)
              
            });
            this.newDataList = newVal
          }
        console.log("this.newDataList",this.newDataList);

        },
        immediate: true
      },
    },
  mounted(){
  },

  created() {
  },

  data(){
    return{
      show: false,
      newDataList:null,
      // goodsList:[],
    }

  },
  methods:{
    changeContent(item,index) {
      this.newDataList.forEach(i => {
          if (i.open !== this.newDataList[index].open) {
            i.open = false;
          }
        })
        item.open = !item.open
        this.offlineGoodsDetail(item.shbillno,index)
    },
    //线下详情接口
    async offlineGoodsDetail(id,index){
        const res =await offlineGoodsDetail(id)
          console.log("data===>",res);
					let dataList = res.data.data[0]
					let {shsyjid,shoughtpay,selldetailZtnewList,sellpaymentZtnewList} = dataList;
          console.log("====>",sellpaymentZtnewList);
          this.$set(this.newDataList[index],'shsyjid',shsyjid)
          this.$set(this.newDataList[index],'shoughtpay',shoughtpay)
          this.$set(this.newDataList[index],'sellpaymentZtnewList',sellpaymentZtnewList)
          this.$set(this.newDataList[index],'selldetailZtnewList',selldetailZtnewList)
          console.log('0000=>',this.newDataList);
          
      },
  }

}
</script>

<style scoped>
.line{
  width: 95%;
  height: 2upx;
  background: linear-gradient(to right, #cccccc, #cccccc 7.5upx, transparent 7.5upx, transparent);
  /* background: radial-gradient(#000000, #000000 4px, transparent 4px, transparent); */
  background-size: 10upx 100%;
}

.box {
  overflow: hidden;
  transition: all 0.3;
  /* border-bottom: 1px solid #F9F9F9; */
}

</style>