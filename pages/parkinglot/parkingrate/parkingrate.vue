<template>
	<view class="page">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">停车费用</block>
		</cu-custom>
		<image style="width: 100%; height: 170rpx; z-index: -1"
			src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%9B%BE%E5%B1%82%201%20(2).png" mode=""></image>
		<view v-if="parkfee" style="margin-top: -170rpx">
			<view class="first_content">
				<view class="car_Nubmer">
					{{ parkfee.plateNo | plateNo }}
				</view>

				<view style="border: 0.5px dashed #cacaca; margin-top: 20rpx">
				</view>

				<view class="cell_text" style="
					margin-top: 10rpx;
					padding-left: 95rpx;
					padding-right: 95rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
				  ">
					<view style="
					  display: flex;
					  justify-content: space-between;
					  align-items: center;
					  flex-direction: column;
					">
						<view style="
							font-size: 34rpx;
							font-weight: 600;
							margin-right: 10rpx;
							text-align: center;
						  ">
							{{ parkfee.totalFee }}元
						</view>
						<view style="color: #888888; font-size: 26rpx"> 停车费用 </view>
					</view>
					<view style="
						  display: flex;
						  justify-content: space-between;
						  align-items: center;
						  flex-direction: column;
						">
						<view style="font-size: 34rpx; font-weight: 600; margin-right: 10rpx">
							{{ parkfee.parkMinutes }}分钟
						</view>
						<view style="color: #888888; font-size: 26rpx"> 停车时长 </view>
					</view>
				</view>

				<view class="" style="border: 0.5px dashed #cacaca; margin-top: 20rpx">
				</view>

				<view class="cell_text" style="margin-top: 10rpx; display: flex">
					<view class="" style="
					  display: flex;
					  align-items: center;
					  justify-content: center;
					  flex-direction: column;
					">
						<view class="" style="font-weight: bolder">
							{{ parkfee.enterTime }}
						</view>
						<view class="" style="color: #888888; font-size: 26rpx">
							入场时间
						</view>
					</view>
					<view class="" style="
						  display: flex;
						  justify-self: center;
						  align-items: center;
						  flex-direction: column;
						  margin-right: 104rpx;
						">
						<view class="" style="font-weight: bolder"> 暂无 </view>
						<view class="" style="color: #888888; font-size: 26rpx">
							出场时间
						</view>
					</view>
				</view>
			</view>

			<view class="first_content" style="margin-top: 20rpx; padding-left: 40rpx; padding-right: 40rpx">
				<template v-if="formatDetails && formatDetails.length">
					<view class="cell_text" v-for="(item, index ) in formatDetails" :key="index">
						<view class="cell_text">
							{{ item.deductName }}
						</view>
						<view class="fee_num" style="color: #9f7a5e">
							-{{ item.money }}元
						</view>
					</view>
				</template>
				<view class="cell_text" @click="deductpoints" v-if="parkfee.point" style="margin-top: 10rpx">
					<view class="cell_text">
						积分抵扣(<text v-if="parkfee.point">{{ parkfee.point }}</text>)
					</view>
					<view class="fee_num" v-if="val"> 积分:{{ val.integral }} </view>
					<view class="fee_num" v-else style="display: flex; align-items: center; color: #888888">
						去选择
						<image style="height: 24rpx; width: 16rpx; margin-left: 16rpx"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%9B%BE%E5%B1%82%202%20%E6%8B%B7%E8%B4%9D.png"
							mode=""></image>
					</view>
				</view>

				<view style="border: 0.5px dashed #cacaca; margin-top: 30rpx"> </view>

				<view class="cell_text">
					<view class="fee_text"> </view>
					<view class="fee_num" style="display: flex; align-items: center">
						合计 :
						<text style="
                color: red;
                font-size: 40rpx;
                margin-right: 12rpx;
                font-weight: bolder;
              ">¥{{
                checked
                  ? Number(parkfee.parkFee)
                  : Number(parkfee.parkFee) - Number(val.money)
              }}</text>
					</view>
				</view>
			</view>
			<view class="advertisement" style="" @click="getstandard">
				<image style="width: 50rpx; height: 50rpx; margin-right: 20rpx"
					src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/qingdan%20%E6%B8%85%E5%8D%95%20%E6%8B%B7%E8%B4%9D%202.png"
					mode=""></image>
				<view class=""> 停车场收费标准是什么？快来看看介绍吧！ </view>
			</view>

			<view style="margin-right: 20rpx; margin-left: 20rpx"
				v-if="Number(parkfee.parkFee) - Number(val.money) == '0'">
				<button type="primary"
					style="box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,0.2), 0 6rpx 10rpx 0 rgba(0,0,0,0.19);height: 92rpx; display: flex;justify-content: center;align-items:center;font-size: 34rpx;color: #512F14;font-weight: 500;background: #e1c8b5;border-radius: 70rpx;width: 100%;margin-top: 40rpx;"
					@tap.stop="$noMultipleClicks(paypark)">
					确定离场
				</button>
			</view>
			<view v-else style="
				  display: flex;
				  flex-direction: column;
				  justify-content: center;
				  margin-right: 80rpx;
				  margin-left: 80rpx;
				">
				<view @click="$noMultipleClicks(openPay)" type="primary"
					style="position: relative;box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,0.2), 0 6rpx 10rpx 0 rgba(0,0,0,0.19);height: 92rpx;font-size: 34rpx;color: #512F14;font-weight: 500;background: #e1c8b5;border-radius: 70rpx;width: 100%;margin-top: 40rpx;">
					<text style="position: absolute; left: 50%; transform: translateX(-50%); margin-top: 20rpx; ">去支付</text>
					<image style="position: absolute; right: 76rpx; margin-top: 18rpx; background-color: #fff; border-radius: 50%;width:60rpx; height:60rpx;"
						src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/carpark/ic_weixin.png"
						mode=""></image>
				</view>

				<view v-if="isETC==1&&parkfee.plateNo!='无牌车'" @click="$noMultipleClicks(etcPay)" type="primary"
					style="box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,0.2), 0 6rpx 10rpx 0 rgba(0,0,0,0.19);height: 92rpx; font-size: 34rpx;color: #512F14;font-weight: 500;background: #e1c8b5;border-radius: 70rpx;width: 100%;margin-top: 40rpx;">
					<text  style="position: absolute; left: 50%;  transform: translateX(-50%); margin-top: 20rpx; ">ETC支付</text>
					<image style="position: absolute; right:152rpx; margin-top: 24rpx; width:60rpx; height:45rpx;"
						src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/carpark/ic_etc.png"
						mode=""></image>
				</view>

			</view>
		</view>
		<view v-else>
			<view style="
          display: flex;
          align-items: center;
          justify-self: center;
          flex-direction: column;
          margin-top: 100rpx;
        ">
				<image src="https://img.songlei.com/live/nodata.png" mode="" style="width: 200rpx; height: 134rpx">
				</image>
				<view style="margin-top: 20rpx; color: #8799a3; text-align: center">
					暂无数据
				</view>
			</view>
		</view>

		<n-transition ref="pop" speed="ease-in-out" :height="800" :maskVal="0.5">
			<view class="title"> 积分抵扣金额 </view>
			<view class="cellcontent">
				<view @click="checkeds" :class="[checked ? 'piont_classing' : 'piont_class']" style="
            display: flex;
            justify-content: center;
            width: 550rpx;
            margin: 0 auto;
          ">
					<view style=""> 暂不使用 </view>
				</view>
				<view v-for="(item, index) in arr" :key="index" style="display: flex; justify-content: center">
					<view v-if="
              item.integral <= parkfee.point && item.money <= parkfee.parkFee
            " @click="getmoney(item, index)" style="width: 550rpx">
						<view :class="[item.checked ? 'piont_classing' : 'piont_class']">
							{{ item.integral }}积分 抵扣
							<text style="color: red; margin-right: 10rpx; margin-left: 10rpx">{{ item.money }}元</text>
						</view>
					</view>
				</view>
			</view>
			<view @click="submitpay">
				<button @click="submitpay" type="primary" :disabled="loading||isPaying" style="
            background: #c9a588;
            border-radius: 36rpx;
            border: none;
            width: 61%;
            margin-top: 30rpx;
          ">
					确定
				</button>
			</view>
		</n-transition>
		<qspopup ref="QSpopup" :delbol='delbol'></qspopup>
		<!-- 支付方式弹框 -->
		<view class="cu-modal bottom-modal" style="overflow: hidden;" :class="payAsync ? 'show' : ''"
			@tap.stop="payAsync = false">
			<view class="cu-dialog bg-white" style="padding-bottom: 80rpx; position: absolute; bottom: 0; left: 0;"
				@tap.stop>
				<pay-components ref="pay" orderType="PARK"></pay-components>
				<button style="width:90%" class="cu-btn round bg-red margin-top-lg text-white"
					@tap.stop="$noMultipleClicks(paypark)" :loading="loading" :disabled="loading" type
					id="goPay">立即支付</button>
			</view>
		</view>
		<QSpopup ref="QSpopup" :delbol='delbol'></QSpopup>
	</view>
</template>

<script>
	import nTransition from "../component/n-transition/n-transition.vue";
	import qspopup from "../component/QS-popup/QS-popup.vue";
	import payComponents from '@/components/pay-components/pay-components.vue';
	import QSpopup from "../component/QS-popup/QS-popup.vue";
	import api from "utils/api";
	const app = getApp();
	export default {
		components: {
			nTransition,
			qspopup,
			payComponents,
			QSpopup
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: getApp().globalData.theme, //全局颜色变量
				parkfee: "",
				userinfo: uni.getStorageSync("user_info"),
				carNumber: "",
				store: "",
				arr: [],
				checked: true,
				val: "",
				noClick: true,
				pageTitle: "停车缴费",
				loading: false,
				isPaying: false,
				timer: null,
				delbol: false,
				parkFeePic: '', // 该停车场的收费图片
				payAsync: false,
				loading: false,
				isETC: 0, // 是否支持Etc
				//是否已经开启Etc
				isPlateEtc: 0
			};
		},

		filters: {
			plateNo: function(value) {
				if (value == "无牌车") {
					return value;
				} else {
					return value.substring(0, 2) + "·" + value.substring(2, value.length);
				}
			},
		},

		computed: {
			formatDetails: function() {
				const formatLsit = [],
					hasModel = [];
				//deductMode deductSource 合并因为有许多小额的票，所以做了合并
				if (this.parkfee.details && this.parkfee.details.length > 0) {
					this.parkfee.details.forEach(item => {
						if (hasModel.length == 0) {
							formatLsit.push(item);
							hasModel.push(item.deductMode + item.deductSource);
						} else if (hasModel.indexOf(item.deductMode + item.deductSource) == -1) {
							formatLsit.push(item);
							hasModel.push(item.deductMode + item.deductSource)
						} else {
							const index = hasModel.indexOf(item.deductMode + item.deductSource);
							formatLsit[index].money = (Number(formatLsit[index].money) + Number(item.money))
								.toFixed(2);
						}
					})
				}
				return formatLsit;
			}
		},

		onLoad(options) {
			console.log(options);
			//etf那里重新下单
			if(options.reOrder==1){
				this.store = options.store;
				this.carNumber = options.carNumber;
				this.isETC = 1;
				this.isPlateEtc = 1;
				//请求check接口
				this.custId = uni.getStorageSync('user_info').erpCid;
				this.parkFeePic = options.parkFeePic;
				api.parkfee({
					"channel": "SONGSHU",
					"store": this.store,
					"plateNo": this.carNumber,
					"custId": uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
					"custType": "",
					"openId": uni.getStorageSync('user_info').openId || uni.getSystemInfoSync().deviceId,
					"parkCode": "",
					"mobile": uni.getStorageSync("user_info").phone,
				}).then(res => {
					this.delbol = false;
					if (res.code == '0') {
					  this.dealWithParkData(res);
					} else if (res.code == '90002') {
						this.$refs.QSpopup.shows(this.carNumber, '不需要缴费')
					} else if (res.code == "90001") {
						this.$refs.QSpopup.shows(this.carNumber, res.msg, "/pages/parkinglot/fast-fee/fast-fee",
							'to');
					} else {
						this.$refs.QSpopup.shows(this.carNumber, res.msg);
					}
				})
				this.getParkPic();
			}else if (options.custId) {
				this.custId = options.custId;
				this.carNumber = options.carNumber;
				this.store = options.store;
				this.isETC = options.isETC;
				this.parkFeePic = options.img;
				this.isPlateEtc = options.isPlateEtc;
				//从上个页面中获取
				this.loadparkfeeFromLocal();
			} else {
				if (options.scene) {
					// console.log(JSON.parse(options.scene));
					this.parkfee = ''
					const qrCodeScene = JSON.parse(options.scene);
					if (qrCodeScene) {
						this.loginMaUser(qrCodeScene); //调用入场方法
					}
				}
			}
		},

		methods: {
			
			loginMaUser(sence) {
				const userInfo = uni.getStorageSync("user_info");
				if (userInfo && userInfo.openId) {
					if (!this.loading && !this.isPaying) {
						this.loading = true;
						this.parkfee = '';
						this.unlicensedvehicleS(sence);
					}
				} else {
					app.doLogin().then(res => {
						if (!this.loading && !this.isPaying) {
							this.loading = true;
							this.parkfee = '';
							this.unlicensedvehicleS(sence);
						}
					});
				}
			},
			
			getParkPic(){
				api.getstorelist().then(data => {
					//如果sf有值就是从链接带进了门店和停车场参数
					if (data.data && data.data.length > 0) {
						for (let i = 0; i < data.data.length; i++) {
							if (data.data[i].store == this.store) {
								this.parkFeePic = data.data[i].parkFeePic;
								return;
							}
						}
						// 否则就定位获取最近的停车场
					}
				})
			},

			//出停车场接口
			unlicensedvehicleS(sence) {
				if (sence && sence.length) {
					let data = {
						channel: "SONGSHU",
						store: "",
						custId: uni.getStorageSync("user_info").erpCid || uni.getStorageSync("user_info").openId,
						openId: uni.getStorageSync("user_info").openId,
						passWay: sence[7],
						passType: sence[6],
						parkCode: sence[5].slice(1),
						mobile: uni.getStorageSync("user_info").phone,
					};
					api.unlicensedvehicle(data).then((res) => {
						this.loading = false;
						uni.hideLoading();
						this.parkfee = '';
						// 90000停车异常  90001有未支付订单  90002 当前车不需要缴费   90003未找到进场记录  90004 有其他任在支付该停车车费  90005 停车费用发生变化请重新查询
						if (res.code == 0 && res.data) {
							this.parkfee = res.data;
							this.parkFeePic = res.data.parkFeePic;
							this.isETC = res.data.enableEtc;
							//1 表示已经开启了Etc  0 表示没开启
							this.isPlateEtc = res.data.isPlateEtc;
							// 循环获取积分列表。  是每pointMoney元一档， 每档需要PointRate积分
							// 根据金额，和档判断可抵扣的列表
							let moneyTimes = 0;
							if (res.data.parkFee > 0 && res.data.pointMoney > 0) {
								moneyTimes = Math.floor(res.data.parkFee / res.data.pointMoney);
							}
							console.log("=====moneyTimes======", moneyTimes);
							// 根据当前会员的积分 和 积分档获取可抵扣积分列表
							let pointTimes = 0;
							if (res.data.point > 0 && res.data.pointRate > 0) {
								pointTimes = Math.floor(res.data.point / res.data.pointRate);
							}
							console.log("=====pointTimes======", pointTimes);
							const times = Math.min(moneyTimes, pointTimes);
							console.log("=====times======", times);
							for (var i = 0; i <= times; i++) {
								this.arr.push({
									integral: (i + 1) * res.data.pointRate,
									money: (i + 1) * res.data.pointMoney,
									checked: false,
								});
							}
						} else if (res.code == "90001") {
							this.$refs.QSpopup.shows(this.carNumber, res.msg,
								"/pages/parkinglot/fast-fee/fast-fee", 'reTo');
						} else {
							this.$refs.QSpopup.shows(this.carNumber, res.msg,
								"/pages/parkinglot/parking-home/index", 'reTo');
						}
					}).catch(e => {
						uni.hideLoading();
						this.loading = false;
					});
				} else {
					uni.hideLoading();
					//入参错误
					this.loading = false;
				}
			},
			getstandard() {
				uni.navigateTo({
					url: `/pages/parkinglot/charging-standard/charging-standard?img=${this.parkFeePic}`,
				});
			},

			submitpay() {
				if (this.checked) {
					this.$refs["pop"].hide();
				} else {
					this.$refs["pop"].hide();
				}
			},
			checkeds() {
				this.checked = !this.checked;
				this.val = "";
				this.arr.forEach(function(item, inx) {
					item.checked = false;
				});
			},
			getmoney(val, index) {
				if (Number(this.parkfee.parkFee) - Number(val.money) < 0) {
					uni.showModal({
						title: "提示",
						content: "抵扣积分超额",
					});
					this.arr[index].checked = false;
				} else {
					if (this.arr[index].checked) {
						this.val = "";
						this.checked = false;
						console.log(val, this.checked, "====================");
						this.arr.forEach(function(item, inx) {
							item.checked = false;
						});
					} else {
						this.val = val;
						this.checked = false;
						console.log(val, this.checked, "====================");
						this.arr.forEach(function(item, inx) {
							if (inx == index) {
								item.checked = true;
							} else {
								item.checked = false;
							}
						});
					}
				}
			},
			deductpoints() {
				console.log(this.parkfee);
				this.$refs["pop"].show();
			},

			// 立即支付;
			openPay(val) {
				this.payAsync = true
			},

			etcPay() {
				this.paypark(1);
			},

			// isETC 0 或则 null 表示不支持 ETC， 为1表示支持ETC
			paypark(isETC) {
				let that = this;
				if (this.parkfee && this.parkfee.orderId && !this.isPaying) {
					this.isPaying = true;
					uni.showLoading({
						title: "提交订单中",
					});
					if (
						(this.val.money &&
							Number(this.parkfee.parkFee) - Number(this.val.money) == "0") ||
						this.parkfee.parkFee == "0"
					) {
						api
							.paysubmit({
								openId: uni.getStorageSync("user_info").openId || uni.getSystemInfoSync().deviceId,
								mobile: uni.getStorageSync("user_info").phone,
								channel: "SONGSHU",
								orderId: this.parkfee.orderId,
								costPoints: this.val ? this.val.integral : 0,
								costPointMoney: this.val ? this.val.money : 0,
								costMoney: this.checked ?
									this.parkfee.parkFee : Number(this.parkfee.parkFee) - Number(this.val.money),
								isETC: 0 //不是ETC支付
							})
							.then((res) => {
								this.isPaying = false;
								uni.hideLoading();
								if (res.code == "0") {
									uni.showToast({
										title: "确认成功",
									});
									uni.redirectTo({
										url: "/pages/parkinglot/fee-success/fee-success?id=" + this.parkfee
											.orderId+"&store="+that.store
									});
								}
							}).catch(e => {
								uni.hideLoading();
								this.isPaying = false;
							});
					} else if (isETC > 0) {
						console.log("==========111====",this.isPlateEtc);
						// 这里需要判断是否已经开启了etc支付,如果没开启的话,需要跳转到开启页面
						if(this.isPlateEtc==0){
							console.log("=========2222====");
							uni.setStorageSync("openEtc", JSON.stringify(this.parkfee));
							uni.redirectTo({
								url: "/pages/parkinglot/open-etc/index?orderId="+ this.parkfee
									.orderId+"&costPoints="+(that.val ? that.val.integral : 0)+"&costPointMoney="+(that.val ? that.val.money : 0)+"&costMoney="+(that.checked ?
									that.parkfee.parkFee : Number(that.parkfee.parkFee) - Number(that.val.money))+'&paymentPrice='+(that.checked ?
												that.parkfee.parkFee : Number(that.parkfee.parkFee) - Number(that
													.val.money))+'&store='+that.store+'&parkFeePic='+this.parkFeePic,
							});
							uni.hideLoading();
							return
						}
						console.log("==========333====");
						// 是ETC支付
						api
							.paysubmit({
								openId: uni.getStorageSync("user_info").openId || uni.getSystemInfoSync().deviceId,
								mobile: uni.getStorageSync("user_info").phone,
								channel: "SONGSHU",
								orderId: that.parkfee.orderId,
								costPoints: that.val ? that.val.integral : 0,
								costPointMoney: that.val ? that.val.money : 0,
								costMoney: that.checked ?
									that.parkfee.parkFee : Number(that.parkfee.parkFee) - Number(that.val.money),
								isETC: 1
							})
							.then((res) => {
								if (res.data) {
									uni.hideLoading();
									this.isPaying = false;
									uni.redirectTo({
										url: `/pages/parkinglot/fee-success/fee-success?id=${this.parkfee.orderId}&isETC=1`+"&store="+this.store+'&parkFeePic='+this.parkFeePic
									})
								} else {
									uni.hideLoading();
									this.isPaying = false;
									uni.redirectTo({
										url: "/pages/parkinglot/fee-detail/fee-detail?item=" +
											this.parkfee.orderId,
									});
								}
							}).catch(e => {
								uni.hideLoading();
								this.isPaying = false;
							});
					} else {
						// 点击立即支付
						//提交订单
						api
							.paysubmit({
								openId: uni.getStorageSync("user_info").openId || uni.getSystemInfoSync().deviceId,
								mobile: uni.getStorageSync("user_info").phone,
								channel: "SONGSHU",
								orderId: that.parkfee.orderId,
								costPoints: that.val ? that.val.integral : 0,
								costPointMoney: that.val ? that.val.money : 0,
								costMoney: that.checked ?
									that.parkfee.parkFee : Number(that.parkfee.parkFee) - Number(that.val.money),
							})
							.then((res) => {
								if (res.data) {
									//立即支付
									try {
										const orderInfo = {
											expiredTime:res.data.payRequest.expireSeconds,
											paymentPrice: that.checked ?
												that.parkfee.parkFee : Number(that.parkfee.parkFee) - Number(that
													.val.money),
											listOrderInfo: [{
												id: that.parkfee.orderId,
												name: "停车缴费",
												paymentPrice: that.checked ?
													that.parkfee.parkFee : Number(that.parkfee.parkFee) -
													Number(that.val.money),
												quantity: 1,
											}, ],
											paymentType: "1",
											// "payBankAccountId": "1588445682521681922",   -> 支付信息
											businessType: "PARK",
											businessCode: that.store || res.data.payRequest.businessCode,
											openId: uni.getStorageSync("user_info").openId || uni
												.getSystemInfoSync().deviceId,
											mobile: uni.getStorageSync("user_info").phone,
										}
										that.$refs.pay?.payOrder(orderInfo, false, "PARK")
									} catch (e) {
										console.error(e, '======error==========')
									}

									// api
									// 	.paywct({
									// 		paymentPrice: this.checked ?
									// 			this.parkfee.parkFee : Number(this.parkfee.parkFee) - Number(this
									// 				.val.money),
									// 		listOrderInfo: [{
									// 			id: this.parkfee.orderId,
									// 			name: "停车缴费",
									// 			paymentPrice: this.checked ?
									// 				this.parkfee.parkFee : Number(this.parkfee.parkFee) -
									// 				Number(this.val.money),
									// 			quantity: 1,
									// 		}, ],
									// 		paymentType: "1",
									// 		// "payBankAccountId": "1588445682521681922",   -> 支付信息
									// 		businessType: "PARK",
									// 		businessCode: this.store || res.data.payRequest.businessCode,
									// 		openId: uni.getStorageSync("user_info").openId,
									// 		mobile: uni.getStorageSync("user_info").phone,
									// 	})
									// 	.then((res) => {
									// 		if (res.data) {
									// 			uni.showLoading({
									// 				title: "支付中",
									// 			});
									// 			//微信小程序
									// 			uni.requestPayment({
									// 				provider: "wxpay",
									// 				timeStamp: res.data.barCodePay.timeStamp,
									// 				nonceStr: res.data.barCodePay.nonceStr,
									// 				package: res.data.barCodePay.package1,
									// 				signType: res.data.barCodePay.signType,
									// 				paySign: res.data.barCodePay.paySign,
									// 				success: (res) => {
									// 					this.isPaying = false;
									// 					uni.redirectTo({
									// 						url: `/pages/parkinglot/fee-success/fee-success?id=${this.parkfee.orderId}`
									// 					})
									// 				},
									// 				fail: (error) => {
									// 					this.isPaying = false;
									// 					uni.redirectTo({
									// 						url: "/pages/parkinglot/fee-detail/fee-detail?item=" +
									// 							this.parkfee.orderId,
									// 					});
									// 				},
									// 				complete: (error) => {
									// 					uni.hideLoading();
									// 					this.isPaying = false;

									// 					that.val = "";
									// 				},
									// 			});
									// 		} else {
									// 			uni.hideLoading();
									// 			this.isPaying = false;
									// 		}
									// 	}).catch(e => {
									// 		uni.hideLoading();
									// 		this.isPaying = false;
									// 	});
								} else {
									uni.hideLoading();
									this.isPaying = false;
								}
							}).catch(e => {
								uni.hideLoading();
								this.isPaying = false;
							});
					}
				} else {
					this.getparkfee();
				}
			},

			loadparkfeeFromLocal() {
				const res = uni.getStorageSync(this.carNumber);
				this.isPlateEtc = res.data.isPlateEtc;
				this.dealWithParkData(res);
				uni.removeStorage({
					key: this.carNumber
				});
			},
			
			dealWithParkData(res){
				this.parkfee = '';
				// 90000停车异常  90001有未支付订单  90002 当前车不需要缴费   90003未找到进场记录  90004 有其他任在支付该停车车费  90005 停车费用发生变化请重新查询
				if (res.code == 0 && res.data) {
					this.parkfee = res.data;
					// 循环获取积分列表。  是每pointMoney元一档， 每档需要PointRate积分
					// 根据金额，和档判断可抵扣的列表
					let moneyTimes = 0;
					if (res.data.parkFee > 0 && res.data.pointMoney > 0) {
						moneyTimes = Math.floor(res.data.parkFee / res.data.pointMoney);
					}
					// 根据当前会员的积分 和 积分档获取可抵扣积分列表
					let pointTimes = 0;
					if (res.data.point > 0 && res.data.pointRate > 0) {
						pointTimes = Math.floor(res.data.point / res.data.pointRate);
					}
				
					const times = Math.min(moneyTimes, pointTimes);
					console.log("=====times======", times);
					if (res.data.pointMoney > 0) {
						for (var i = 0; i <= times; i++) {
							this.arr.push({
								integral: (i + 1) * res.data.pointRate,
								money: (i + 1) * res.data.pointMoney,
								checked: false,
							});
						}
					}
				
				} else if (res.code == "90001") {
					this.$refs.QSpopup.shows(this.carNumber, res.msg, "/pages/parkinglot/fast-fee/fast-fee", 'reTo');
				} else {
					this.$refs.QSpopup.shows(this.carNumber, res.msg, "/pages/parkinglot/parking-home/index", 'reTo');
				}
			},
			//  获取详情
			getparkfee() {
				api
					.parkfee({
						channel: "SONGSHU",
						store: this.store,
						plateNo: this.carNumber,
						custId: this.custId || uni.getStorageSync("user_info").erpCid,
						custType: "",
						openId: uni.getStorageSync("user_info").openId,
						parkCode: "",
						mobile: uni.getStorageSync("user_info").phone,
					})
					.then((res) => {
						this.parkfee = '';
						// 90000停车异常  90001有未支付订单  90002 当前车不需要缴费   90003未找到进场记录  90004 有其他任在支付该停车车费  90005 停车费用发生变化请重新查询
						if (res.code == 0 && res.data) {
							this.parkfee = res.data;
							for (var i = 0; i <= res.data.parkFee; i++) {
								this.arr.push({
									integral: (i + 1) * res.data.pointRate,
									money: (i + 1) * res.data.pointMoney,
									checked: false,
								});
							}
						} else if (res.code == "90001") {
							this.$refs.QSpopup.shows(this.carNumber, res.msg, "/pages/parkinglot/fast-fee/fast-fee",
								'reTo');
						} else {
							this.$refs.QSpopup.shows(this.carNumber, res.msg, "/pages/parkinglot/parking-home/index",
								'reTo');
						}
					});
			},
		},

		onUnload() {
			this.timer && clearTimeout(this.timer);
		}
	};
</script>

<style scoped>
	::v-deep .radio::before .checkbox::before {
		right: 0 !important;
	}

	.title {
		text-align: center;
		font-size: 40rpx;
		margin-top: 30rpx;
		font-weight: bold;
	}

	.headimgUrl {
		display: flex;
		width: 200rpx;
		height: 200rpx;
		border-radius: 20rpx;
		justify-content: center;
		text-align: center;
	}

	.headimgUrl_view {
		display: flex;
		justify-content: center;
		/* margin-top: 20rpx; */
		/* margin-bottom: 20rpx; */
	}

	page {
		background: #ffffff;
	}

	.fee_num {
		color: #2c2c2c;
	}

	.parkfee {
		display: flex;
		justify-content: space-between;
		/* border-bottom: 1px solid #919191; */
		height: 79rpx;
		align-items: center;
		margin: 20rpx;
		border-radius: 20rpx;
	}

	.activite {
		color: #04c9c3;
	}

	.content {
		/* background: #008000; */
	}

	.scroll-view_H {
		white-space: nowrap;
		width: 100%;
		color: #cccccc;
	}

	.scroll-view-item_H {
		display: inline-block;
		width: 20%;
		height: 50rpx;
		line-height: 50rpx;
		text-align: center;
		padding: 10px 0;
	}

	.from_fee {
		padding: 20rpx;
		background: white;
		margin: 20rpx;
		border-radius: 30rpx;
	}

	.fee_text {
		color: #949494;
	}

	.cell {
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
	}

	.cellcontent {
		padding: 30rpx;
		height: 530rpx;
		overflow: scroll;
	}

	.car_Number_title {
		font-size: 30rpx;
	}

	.car_Nubmer {
		font-weight: 600;
		font-size: 40rpx;
		display: flex;
		justify-content: center;
	}

	.first_content {
		margin-left: 20rpx;
		margin-right: 20rpx;
		/* border: 1px solid red; */
		background: white;
		border-radius: 22rpx;
		padding: 20rpx;
	}

	.cell_text {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 30rpx;
	}

	.advertisement {
		height: 86rpx;
		background: white;
		border-radius: 20rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		margin-top: 20rpx;
		display: flex;
		align-items: center;
		margin-top: 10rpx;
	}

	.piont_class {
		background: #f7f6f5;
		border-radius: 60rpx;
		height: 75rpx;
		text-align: center;
		margin-top: 20rpx;
		line-height: 75rpx;
	}

	.piont_classing {
		background: #c59e7f;
		border-radius: 60rpx;
		height: 75rpx;
		width: 550rpx;
		text-align: center;
		margin-top: 20rpx;
		line-height: 75rpx;
	}
</style>