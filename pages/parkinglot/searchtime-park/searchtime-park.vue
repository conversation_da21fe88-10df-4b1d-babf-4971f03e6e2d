<template>
	<view class="page-carnumber-test">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{storeName}}</block>
		</cu-custom>
		<image src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%AF%BB%E8%BD%A6.png"
			style="width: 100%;height:180rpx;z-index: -1;"></image>
		<view class="contents">
			<view v-for="(item,index) in listpark" :key='index'
				style="padding-left: 20rpx;padding-right: 20rpx;background: white;margin-top: 30rpx;border-radius: 30rpx;">
				<view class=""
					style="border-bottom: 1px dashed #e7e7e7;text-align: center;padding-top: 16rpx;font-family: PingFang SC;padding-bottom: 16rpx;font-weight: 600;font-size: 32rpx;">
					{{item.plateNo|plateNo}}
				</view>
				<view class="" style="display: flex;align-items: center;padding-top: 20rpx;">
					<view class="">
						<image :src="item.carPics[0]" mode="aspectFill"
							style="height: 200rpx;width: 200rpx;border-radius: 30rpx;"></image>
					</view>
					<view class="" style="display: flex;flex: 1;flex-direction: column;margin-left: 20rpx;height: 215rpx;padding-top: 15rpx;">
						<view class="">
							<text style="color: #888888;margin-right: 20rpx;">该车位于 : </text>
							<text style="color: #000000;font-family: PingFang SC;">{{item.floorCode +'层'+ item.spaceNo}}</text>
						</view>
						<view class="" style="padding-top: 16rpx;">
							<text style="color: #888888;margin-right: 20rpx;">进场时间 : </text>
							<text style="color: #000000;font-family: PingFang SC;">{{item.enterTime}}</text>
						</view>
						<view class="" style="padding-top: 16rpx;">
							<text style="color: #888888;margin-right: 20rpx;">已停时间 : </text>
							<text v-if="item.parkMinutes<60" style="color: #000000;font-family: PingFang SC;">{{item.parkMinutes}} 分钟</text>
							<text v-else style="color: #000000;font-family: PingFang SC;">
								{{toHourMinute(item.parkMinutes)}} 
							</text>
						</view>

					</view>
				</view>
			</view>
		</view>
          	<view  v-if="loadmore" :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>

	</view>
</template>

<script>
	import api from 'utils/api'
	import __config from 'config/env';
	import CarNumber from "../component/codecook-carnumber/codecook-carnumber.vue";
	import QQMapWX from 'public/jweixin-module/qqmap-wx-jssdk.min.js';
	const app = getApp();
	export default {
		data() {
			return {
				carNumber: '黑A07A51',
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				carlist: '',
				listpark:[],
				pageNum: 1,
				pageSize: 5,
				loadmore:true,
				store:'',
				storeName:''
			};
		},
		watch: {
			carNumber(num) {
				console.log(num);
			},
		},
		filters: {
			plateNo: function(value) {
				return value.substring(0, 2) + '·'+ value.substring(2, value.length)
			},
		
		},
		onLoad(options) {
			console.log(options)
			if(options.time=='0'||options.time){
				this.time = options.time;
				this.store = options.store;
				this.storeName = decodeURIComponent(options.storeName);
				this.list()
			}

		},
		methods: {
		 toHourMinute(minutes){
		  return (Math.floor(minutes/60) + "小时" + (minutes%60) + "分" )
		},
			onReachBottom() {
				// this.pageNum++
				if (this.listpark.length != this.rowsCount) {
					this.pageNum = this.pageNum + 1
					this.list()
				}else{
					this.loadmore = false;
				}
			},

			onPullDownRefresh() {
		 	// 显示顶部刷新图标
				console.log(111)
		 	uni.showNavigationBarLoading();

		 	uni.hideNavigationBarLoading(); // 停止下拉动作

				uni.stopPullDownRefresh();
			},
			list() {
				api.entrytime({
					"channel": "SONGSHU",
					"store": this.store,
					"pageNum": this.pageNum,
					"pageSize": 10	,
					"entryTime": this.time
				}).then(res => {
					this.rowsCount =res.data.rowsCount
					if(res.data.dataList){
						this.listpark = [...this.listpark, ...res.data.dataList]
					}
					console.log(this.listpark.length , res.data.rowsCount)
				if (this.listpark.length >= res.data.rowsCount) {
					this.loadmore = false;
				}
				})
			}
		},
		onShow() {},
		mounted() {
			// this.getlist()
		}
	};
</script>

<style scoped lang="less">
	.contents {
		border-radius: 50rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		margin-top: -125rpx;	
		padding-bottom: 100rpx;

	}
</style>
