<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">缴费记录</block>
		</cu-custom>
		<view class="">
			<image src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%9B%BE%E5%B1%82%201%20(2).png"
				style="height: 120rpx;width: 100%;z-index: -1;" mode=""></image>
		</view>
		<view class="total">
			缴费记录共 <text style="color:#d2c1b4;padding-left: 10rpx;padding-right: 10rpx;">{{rowsCount}}</text> 条
		</view>
		<view class="content" v-for="(item,index) in list" :key="index">
			<view class="" @click="parkfee(item)">
				<view class="parkname-orderstatus">
					<view class="parkname">
						<image style="width: 30rpx;height: 30rpx;margin-right: 10rpx;"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E8%BD%A6%E7%89%8C%E6%A3%80%E6%B5%8B.png"
							mode=""></image> {{item.plateNo}}
						<image v-if="item.isEtc==1 || item.orderStatus=='E'"
							style="margin-left: 20rpx;width:60rpx; height:45rpx;"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/carpark/ic_etc.png" mode="">
						</image>
					</view>
					<view class="">
						<text style="color: #888888;margin-right: 10rpx;">支付金额 : </text> <text
							style="color: red;">¥{{item.costMoney}}</text> <text style="color: #9F7A5E;"><text
								style="color: #000000;">+</text>积分:{{item.costPoints}}</text>
					</view>

				</view>

				<view style="border: 1px dashed #e7e7e7;height: 1rpx;margin-top: 10rpx;">

				</view>
				<view class="parkname-orderstatus three">
					<view class="three_text" style="padding-left: 44rpx;">
						订单状态:<text class="" v-if="item.orderStatus=='N'">
							新订单
						</text>
						<text class="" v-else-if="item.orderStatus=='Y'">
							已完成
						</text>
						<text class="" v-else-if="item.orderStatus=='F'">
							已失败
						</text>
						<text class="" v-else-if="item.orderStatus=='E'">
							ETC待支付
						</text>
						<text class="" v-else>
							已取消
						</text>
					</view>
					<view class="two_text" v-if="item.orderStatus=='E'">
						<view class="">
							剩余应付：￥{{Number(item.parkFee) - Number(item.costPointmoney)}}
						</view>
					</view>
				</view>

				<view class="parkname-orderstatus two">
					<view class="three_text" style="padding-left: 44rpx;">
						{{item.parkName}}
					</view>
					<view class="two_text">
						<view class="">
						</view>
					</view>
				</view>
				<view class="parkname-orderstatus three">
					<view class="three_text" style="padding-left: 44rpx;" v-if="item.parkMinutes<60">
						停放 {{item.parkMinutes}} 分钟

					</view>
					<view class="three_text" style="padding-left: 44rpx;" v-else>
						停放 {{toHourMinute(item.parkMinutes)}}
					</view>
					<view class="three_text">
						<!--  -->
					</view>
				</view>
				<view class="parkname-orderstatus three">
					<view class="three_text" style="padding-left: 44rpx;">
						进场时间: {{item.enterTime}}
					</view>
					<view class="three_text">
						<!-- 离场：{{item.leaveTime?item.leaveTime :'暂无'}} -->
					</view>
				</view>
				<view class="parkname-orderstatus three">
          <view class="three_text" style="padding-left: 44rpx;" v-if="item.orderStatus=='N'">
            查费时间: {{item.checkTime?item.checkTime :'暂无'}}
          </view>
          <view class="three_text" style="padding-left: 44rpx;" v-if="item.orderStatus!='N'">
            离场时间: {{item.leaveTime?item.leaveTime :'暂无'}}
          </view>
					<view class="three_text">
						<!-- 离场：{{item.leaveTime?item.leaveTime :'暂无'}} -->
					</view>
				</view>
				<!-- E  ETC未支付订单 -->
				<view class="parkname-orderstatus three" v-if="item.orderStatus=='N'||item.orderStatus=='E'"
					style="display: flex;justify-content: space-between;">
					<view class="" @click.stop="orderrest(item)"
						style="background: #c9a588;border-radius: 36rpx;border: none;width: 38%;height: 75rpx;text-align: center;line-height: 75rpx;color: white;">
						取消订单
					</view>
					<view class="" @click.stop="openPay(item)"
						style="background: #c9a588;border-radius: 36rpx;border: none;width: 38%;height: 75rpx;text-align: center;line-height: 75rpx;color: white;">
						{{item.orderStatus=='E'?'继续支付':'立即支付'}}
					</view>
				</view>
        <view class="parkname-orderstatus three">
          <view class="four_text" style="padding-left: 44rpx;" v-if="item.orderStatus=='N'">
            请在 {{item.delayTime}} 之前完成支付，过期将自动取消！
          </view>
        </view>
			</view>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
		<!-- 支付方式弹框 -->
		<view class="cu-modal bottom-modal" style="overflow: hidden;" :class="payAsync ? 'show' : ''"
			@tap.stop="payAsync = false">
			<view class="cu-dialog bg-white" style="padding-bottom: 80rpx; position: absolute; bottom: 0; left: 0;"
				@tap.stop>
				<pay-components ref="paycom" orderType="PARK"></pay-components>
				<button style="width:90%" class="cu-btn round bg-red margin-top-lg text-white"
					@tap.stop="$noMultipleClicks(setpay)" :loading="loading" :disabled="loading" type
					id="goPay">立即支付</button>
			</view>
		</view>
	</view>
</template>

<script>
	import api from 'utils/api';
	import payComponents from '@/components/pay-components/pay-components.vue';
	export default {
		components: {
			payComponents
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: getApp().globalData.theme, //全局颜色变量
				rowsCount: '',
				pageNo: 1,
				noClick: true,
				list: [],
				loadmore: true,
				payAsync: false,
				payItem: {},
				loading: false
			}
		},
		onLoad(options) {
			if (options.custId) {
				this.custId = options.custId
			}
		},
		onShow() {
			this.pageNo = 1;
			this.geetlists()
			this.list = []
		},
		onReachBottom() {
			this.pageNo = this.pageNo + 1
			this.geetlists()

		},
		onPullDownRefresh() {
			this.pageNo = 1;
			this.geetlists()
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		methods: {
			toHourMinute(minutes) {
				return (Math.floor(minutes / 60) + "小时" + (minutes % 60) + "分")
			},
			// 立即支付;
			openPay(val) {
				this.payItem = val;
				let that = this;
				if (val.orderStatus == 'E') {
					uni.showModal({
						title: '提示',
						showCancel: true,
						content: '是否不使用ETC，直接支付剩余金额？',
						success(res) {
							if(res.confirm){
								api.restorder({
									"channel": "SONGSHU",
									"orderId": val.orderId,
									"cancelSource": "MANUAL",
									"cancelBy": uni.getStorageSync('user_info').id,
								}).then(res => {
									uni.redirectTo({
										url: '/pages/parkinglot/parkingrate/parkingrate?reOrder=1&store='+val.store+'&carNumber='+val.plateNo
									})
								})
							}
						},
						complete() {}
					});
				} else {
					this.payAsync = true;
				}
			},
			setpay() {
				let that = this;
				api.parkPayOrder({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
					"orderId": this.payItem.orderId,
				}).then(async (res) => {
					if (res && res.data) {
						const orderInfo = {
							"orderId": this.payItem.orderId,
							...res.data.payRequest,
							expiredTime: res.data.payRequest.expireSeconds
						}
						await that.$refs.paycom?.payOrder(orderInfo, false, "PARK")
					}
				}).catch(e => {
					console.log("====订单失效=====")
                    this.payAsync = false;
					this.pageNo = 1;
					this.geetlists();
				});
			},

			orderrest(val) {
				api.restorder({
					"channel": "SONGSHU",
					"orderId": val.orderId,
					"cancelSource": "MANUAL",
					"cancelBy": uni.getStorageSync('user_info').id,
					// "cancelDate": "",
					// "cancelRemark": ""
				}).then(res => {
					this.list = []
					// if(res.data=='OK'){
					this.pageNo = 1
					this.geetlists()
					// }

				})
			},
			parkfee(val) {
				uni.navigateTo({
					url: `/pages/parkinglot/fee-detail/fee-detail?item=${val.orderId}`
				})
			},
			geetlists() {
				api.parklists({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
					"openId": uni.getStorageSync('user_info').openId || uni.getSystemInfoSync().deviceId,
					"pageNo": this.pageNo,
					"pageSize": 10
				}).then(res => {
					console.log(res, '---')
					this.rowsCount = res.data.rowsCount;
					if (this.pageNo == 1) {
						this.list = [...res.data.dataList]
					} else {
						this.list = [...this.list, ...res.data.dataList]
					}
					if (this.list.length >= res.data.rowsCount) {
						this.loadmore = false;
					}
				})
			},
			unique(arr) {
				const res = new Map();
				return arr.filter((arr) => !res.has(arr.orderId) && res.set(arr.orderId, 1));
			},
		},

	}
</script>

<style scoped>
	.total {
		background: white;
		padding: 20rpx;
		margin-top: -120rpx;
		border-radius: 30rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
	}

	.activite {
		border-bottom: 1px solid #CDA185;
		color: #CDA185 !important;
	}



	.parkname-orderstatus {
		display: flex;
		justify-content: space-between;

	}

	.content {
		padding: 20rpx;
		background: white;
		margin-top: 20rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		border-radius: 30rpx;
	}

	.two {
		margin-top: 12rpx;

	}

	.three {
		margin-top: 12rpx;
	}

	.three_text {
		color: #b6b6b6;
	}

	.two_text {
		color: #e1af90;
	}

  .four_text {
    color: #fF0000;
    font-size: 22rpx;
  }

	.parkname {
		font-weight: 700;
		font-size: 30rpx;
		display: flex;
		align-items: center;
	}
</style>