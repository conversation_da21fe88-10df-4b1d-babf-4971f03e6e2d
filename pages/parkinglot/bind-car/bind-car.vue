<template>
	<view class="page-carnumber-test">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">绑定车牌号</block>
		</cu-custom>
		<view class="content">
			<view class="location" @click="getlocation" v-if="selectstore && selectstore.length">
				<view class="">
					{{selectstore[0].storeName}}
				</view>
			</view>
			<view class="please-Car-number">
				请填写您的车牌号
			</view>
			<car-number v-model="carNumber"></car-number>
			<view class="please-store" @click="bindcar">
				<button type="primary" style="background: #c9a588;border-radius: 36rpx;border: none;">绑定车牌</button>
			</view>
			<view class="please-store" style="margin-top: 40rpx;">
				<t-slide ref="slide" @del="del" @itemClick="itemClick" class='slide' style="width: 100%;">
					<template v-slot:default="{item}" style='width: 100%;'>
						<view class="" style="display: flex;justify-content: space-between;width: 100%height:100rpx;">
							<view class="">
								{{item.plateNo}}
							</view>
							<view class=""style='margin-left: 20rpx;'>
								固定
							</view>
						</view>
					</template>
				</t-slide>
			</view>
			<!-- 	<view  class="please-store" style="margin-top: 40rpx;" v-for="(item,index) in carlist" :key="index">
					      <view class="" style="display: flex;justify-content: space-between;">
					       <view class="">
					       	  	 {{item.plateNo}}
					       </view>
						   <view class="">
						   	  固定
						   </view>
					      </view>
				</view>
	 -->
		</view>
	</view>
</template>

<script>
	import api from 'utils/api'
	import __config from 'config/env';
	var qqmapsdk
	import CarNumber from "../component/codecook-carnumber/codecook-carnumber.vue";
	import QQMapWX from 'public/jweixin-module/qqmap-wx-jssdk.min.js';
	import tSlide from "../component/t-slide/t-slide.vue"
	// import slide from "../component/t-slide/"
	const app = getApp();
	export default {
		components: {
			CarNumber,
			tSlide
		},
		data() {
			return {
				carNumber: "",
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				carlist: '',
        selectstore: []
			};
		},
		watch: {
			carNumber(num) {
				console.log(num);
			},
		},
		onLoad(options) {

		},
		methods: {
			//点击单行
			itemClick(data) {
				console.log('点击', data)
			},
			//删除
			del(data) {
				console.log(data,'data')
				api.delcar({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid||uni.getStorageSync('user_info').openId,
					"openId": uni.getStorageSync('user_info').openId||uni.getSystemInfoSync().deviceId,
					"plateNo": data.plateNo,
					"plateType": data.plateType
				}).then(res => {
					console.log(res.data=='OK')
					if(res.data=='OK'){
						uni.showToast({
						  title: '解绑成功',
						  icon: 'success',
						  duration: 2000
						});
						this.getcarlist()
					}
                      
				})
				 
			},
			//编辑
			edit(data) {
				console.log('编辑', data)
				uni.showToast({
					title: '编辑ID--' + data.id,
					icon: 'none'
				})
			},
			bindcar() {
				if (this.carNumber == '' || this.carNumber.length < 7) {
					uni.showToast({
						title: '请输入车牌',
						icon: 'error'
					})
				} else if (this.carNumber.length >= 7) {
					api.getbindcar({
						"channel": "SONGSHU",
						"custId": uni.getStorageSync('user_info').erpCid||uni.getStorageSync('user_info').openId,
						"openId": uni.getStorageSync('user_info').openId||uni.getSystemInfoSync().deviceId,
						"plateNo": this.carNumber,
						"plateType": "FIX"
					}).then(res => {
						uni.showToast({
							title: '绑定成功',
							icon: 'success',
						});
						this.getcarlist()
					})
				}
			},
			//获取绑定的车牌
			getcarlist() {
				api.getcarlist({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid||uni.getStorageSync('user_info').openId,
					"openId": uni.getStorageSync('user_info').openId||uni.getSystemInfoSync().deviceId,
				}).then(res => {
					console.log(res.code == '0')
					if (res.code == '0') {
						this.carlist = res.data.dataList
						this.$nextTick(() => {
							this.$refs.slide.assignment(this.carlist)
						})
					}


				})
			},
		},
		onShow() {
			// 实例化API核心类
			// qqmapsdk = new QQMapWX({
			// 	key: __config.mapKey
			// });
			let that = this
			this.getcarlist()

		},
	};
</script>

<style scoped lang="less">
	v::deep .slide{
		width: 100% !important;
	}
	.please-Car-number {
		padding-top: 20rpx;
		font-size: 38rpx;
	}

	.content {
		background: white;
		padding-left: 20rpx;
		padding-right: 20rpx;
		padding-bottom: 20rpx;
	}

	.please-store {
		margin-top: 30rpx;
	}

	.please-binding-car {
		margin-top: 20rpx;
	}

	.Memberexclusive {
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		padding-left: 20rpx;
		padding-right: 20rpx;
		background: white;
		height: 100rpx;
		align-items: center;
		border: 1px solid red;
	}

	.every-vip {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: white;
	}

	.tc-vip {
		display: flex;
		justify-content: space-between;
		background: white;
		padding-left: 20rpx;
		padding-right: 20rpx;
		height: 138rpx;
		border: 1px solid yellow;
		align-items: center;
	}

	.number-vip {
		font-size: 46rpx;
	}

	.text-vip {
		font-size: 28rpx;
	}

	.tc-vip-tab {
		display: flex;
		justify-content: space-between;
		background: white;
		padding-left: 20rpx;
		padding-right: 20rpx;
		height: 168rpx;
		border: 1px solid yellow;
		align-items: center;
		margin-top: 20rpx;

	}

	.location {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80rpx;
	}
</style>
