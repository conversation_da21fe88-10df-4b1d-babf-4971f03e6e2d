<template>
	<view class="page">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">ETC停车</block>
		</cu-custom>
		<image style="width: 100%; height: 170rpx; z-index: -1"
			src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%9B%BE%E5%B1%82%201%20(2).png" mode=""></image>
		<view style="margin-top: -170rpx" class="etc-content">
			<view class="etc-top">
				<image style="width:80rpx; height:60rpx;"
					src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/carpark/ic_etc.png" mode=""></image>
				<text style="font-size: 34rpx; padding-left: 30rpx; font-weight: 800;">开启ETC停车支付</text>
			</view>
			<view style="margin: 30rpx 20rpx; font-size:30rpx;">
				开启后，出库优先使用ETC自动扣费，出库时若要使用其他支付方式请先拔卡，或提前使用其他支付方式支付停车费。
			</view>
			<view class="text-xsm flex align-center justify-center"
				style="flex-direction: column; margin-top: 40rpx;margin-bottom: 40rpx;line-height: 40rpx;">
				<checkbox-group @change="checkChange">
					<label class="flex align-center">
						<checkbox value="1" :checked="check" class="round scarlet red" style="transform:scale(0.6);" />
						<text>我已阅读并同意ETC停车支付协议
						</text>
					</label>
				</checkbox-group>
				<view  style="color: #D0A088;"  class="cur-hover text-blue text-sm" @click.stop="navTo('/pages/public/webview/webview?url=' + carAgreement)">《ETC停车支付协议》</view>
			</view>
		</view>

		<view @click="$noMultipleClicks(etcPay)" type="primary"
			style="box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,0.2), 0 6rpx 10rpx 0 rgba(0,0,0,0.19);height: 92rpx; font-size: 34rpx;color: #512F14;font-weight: 500;background: #e1c8b5;border-radius: 70rpx;margin: 40rpx 60rpx;">
			<text
				style="position: absolute; left: 50%;  transform: translateX(-50%); margin-top: 20rpx; ">确认开启并使用ETC支付</text>
			<!-- <image style="position: absolute; right:152rpx; margin-top: 24rpx; width:60rpx; height:45rpx;"
	   		src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/carpark/ic_etc.png"
	   		mode=""></image> -->
		</view>

		<view @click="$noMultipleClicks(openPay)" type="primary"
			style="position: relative;box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,0.2), 0 6rpx 10rpx 0 rgba(0,0,0,0.19);height: 92rpx;font-size: 34rpx;color: #512F14;font-weight: 500;background: #e1c8b5;border-radius: 70rpx;margin: 40rpx 60rpx;">
			<text
				style="position: absolute; left: 50%; transform: translateX(-50%); margin-top: 20rpx; ">使用其他方式支付</text>
			<image
				style="position: absolute; right: 76rpx; margin-top: 18rpx; background-color: #fff; border-radius: 50%;width:60rpx; height:60rpx;"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/carpark/ic_weixin.png" mode=""></image>
		</view>


		<!-- 支付方式弹框 -->
		<view class="cu-modal bottom-modal" style="overflow: hidden;" :class="payAsync ? 'show' : ''"
			@tap.stop="payAsync = false">
			<view class="cu-dialog bg-white" style="padding-bottom: 80rpx; position: absolute; bottom: 0; left: 0;"
				@tap.stop>
				<pay-components ref="pay" orderType="PARK"></pay-components>
				<button style="width:90%" class="cu-btn round bg-red margin-top-lg text-white"
					@tap.stop="$noMultipleClicks(paypark)" :loading="loading" :disabled="loading" type
					id="goPay">立即支付</button>
			</view>
		</view>
	</view>
</template>

<script>
	import api from "utils/api";
	import payComponents from '@/components/pay-components/pay-components.vue';
	import __config from '@/config/env'; // 配置文件
	export default {
		components: {
			payComponents
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: getApp().globalData.theme, //全局颜色变量
				check: false,
				payAsync: false,
				isPaying: false,
				parkfee: null,
				noClick: true,
				costPoints:'',
				costPointMoney:'',
				costMoney: '',
				store:'',
				paymentPrice: '',
				carAgreement: __config.carAgreement,
			}
		},

		onLoad(options) {
			this.parkfee = JSON.parse(uni.getStorageSync("openEtc"));
			uni.removeStorage({
				key:"openEtc"
			})
			this.costPoints = options.costPoints;
			this.costPointMoney = options.costPointMoney;
			this.costMoney = options.costMoney;
			this.store = options.store;
			this.paymentPrice = options.paymentPrice;
			this.parkFeePic =  options.parkFeePic;
		
		},
		methods: {
			checkChange(e) {
				const {
					value
				} = e.detail;
				this.check = !!value[0]
			},

			// 立即支付;
			openPay(val) {
				this.payAsync = true
			},

			etcPay() {
				if (!this.check) {
					uni.showToast({
						icon: "none",
						title: "请先阅读并同意ETC停车支付协议"
					})
					return
				}
				this.paypark(1);
			},

			// isETC 0 或则 null 表示不支持 ETC， 为1表示支持ETC
			paypark(isETC) {
				let that = this;
				if (this.parkfee && this.parkfee.orderId && !this.isPaying) {
					this.isPaying = true;
					uni.showLoading({
						title: "提交订单中",
					});
					if (isETC > 0) {
						// 是ETC支付
						api
							.paysubmit({
								openId: uni.getStorageSync("user_info").openId || uni.getSystemInfoSync().deviceId,
								mobile: uni.getStorageSync("user_info").phone,
								channel: "SONGSHU",
								orderId: that.parkfee.orderId,
								costPoints:this.costPoints,
								costPointMoney:this.costPointMoney,
								costMoney: this.costMoney,
								isETC: 1
							})
							.then((res) => {
								if (res.data) {
									uni.hideLoading();
									this.isPaying = false;
									uni.redirectTo({
										url: `/pages/parkinglot/fee-success/fee-success?id=${this.parkfee.orderId}&isETC=1&store=${this.store}&parkFeePic=${this.parkFeePic}`
									})
								} else {
									uni.hideLoading();
									this.isPaying = false;
									uni.redirectTo({
										url: "/pages/parkinglot/fee-detail/fee-detail?item=" +
											this.parkfee.orderId,
									});
								}
							}).catch(e => {
								uni.hideLoading();
								this.isPaying = false;
							});
					} else {
						// 点击立即支付
						//提交订单
						api
							.paysubmit({
								openId: uni.getStorageSync("user_info").openId || uni.getSystemInfoSync().deviceId,
								mobile: uni.getStorageSync("user_info").phone,
								channel: "SONGSHU",
								orderId: that.parkfee.orderId,
								costPoints:this.costPoints,
								costPointMoney:this.costPointMoney,
								costMoney: this.costMoney,
							})
							.then((res) => {
								if (res.data) {
									//立即支付
									try {
										const orderInfo = {
											paymentPrice: this.paymentPrice,
											listOrderInfo: [{
												id: that.parkfee.orderId,
												name: "停车缴费",
												paymentPrice: this.paymentPrice,
												quantity: 1,
											}, ],
											paymentType: "1",
											// "payBankAccountId": "1588445682521681922",   -> 支付信息
											businessType: "PARK",
											businessCode: this.store || res.data.payRequest.businessCode,
											openId: uni.getStorageSync("user_info").openId || uni
												.getSystemInfoSync().deviceId,
											mobile: uni.getStorageSync("user_info").phone,
										}
										that.$refs.pay?.payOrder(orderInfo, false, "PARK")
									} catch (e) {
										console.error(e, '======error==========')
									}
								} else {
									uni.hideLoading();
									this.isPaying = false;
								}
							}).catch(e => {
								uni.hideLoading();
								this.isPaying = false;
							});
					}
				} 
			},
                navTo(url) {
                	uni.navigateTo({
                		url
                	})
                }
		}
	}
</script>

<style scoped>
	.etc-content {
		background: #ffffff;
		margin: 24rpx;
		padding: 30rpx;
		border-radius: 26rpx;
	}

	.etc-top {
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>