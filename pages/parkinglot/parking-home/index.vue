<template>
	<view class="page-carnumber-test">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">
				<view class="location" @click="getlocation">
					<view class="" style="display: flex;justify-content: center;align-items: center;font-size: 32rpx;
										
										font-weight: 400;
										color: #000000;">
						<image v-if="selectstore.storeName" style="width: 30rpx;height: 37rpx;margin-right: 8rpx;"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%AE%9A%E4%BD%8D%402x.png">
						</image> {{selectstore&&selectstore.storeName?selectstore.storeName:'请选择门店'}}
					</view>
				</view>
			</block>
		</cu-custom>
		<view class="">
			<image style="width:100%;height: 198rpx;z-index:-1;"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/newcar/%E5%81%9C%E8%BD%A6%E7%BC%B4%E8%B4%B9.png">
			</image>
		</view>

		<view class="tc-vip">
			<view class="every-vip">
				<view class="number-vip">
					{{vipmember.points?vipmember.points:0}}
				</view>
				<view class="text-vip" style="margin-top:-16rpx">
					当前积分
				</view>
				<view style="background: #F9F3EE;color: #B29580;border-radius: 50rpx;
				      padding-left: 15rpx;
				      padding-right: 15rpx;
				      font-size: 24rpx;
				  ">
					500积分=1小时
				</view>
			</view>
			<view class="every-vip" @click="Parkingcoupon">
				<view class="qita-box">
					<view class="number-vip">
						{{vipmember.moneyCouponCount?vipmember.moneyCouponCount:0}}
					</view>
					<image class="qita"
					src="http://img.songlei.com/car/tcq.gif"
					mode="aspectFit"/>
				</view>
				<view class="text-vip">
					停车劵
				</view>
			</view>
			<view class="every-vip">
				<view class="number-vip">
					{{vipmember.freeHours?vipmember.freeHours:0}}
				</view>
				<view class="text-vip">
					免费时长
				</view>
			</view>
		</view>

		<view class="content">
			<view class="please-Car-number">
				-请填写您的车牌号-
			</view>
			<car-number v-model="carNumber" ref="carNumber"></car-number>
			<view class="please-store" v-if="!selectstore|| !selectstore.storeName">
				<image style="height: 90rpx;" @click="getlocation"
					src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E8%AF%B7%E9%80%89%E6%8B%A9%E9%97%A8%E5%BA%97.png"
					mode="heightFix"></image>
			</view>
			<view class="please-store" @click="parkingrate" v-else-if="carNumber.length>=7">
				<image style="height: 90rpx;"
					src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E6%9F%A5%E8%AF%A2%E5%81%9C%E8%BD%A6%E8%B4%B9.png"
					mode="heightFix"></image>
			</view>
			<view class="please-store" v-else>
				<image style="height: 90rpx;"
					src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E8%AF%B7%E8%BE%93%E5%85%A5%E8%BD%A6%E7%89%8C%E5%8F%B7.png"
					mode="heightFix"></image>
			</view>
			<view style="color:#888888;margin-top:2rpx;text-align:center;padding-bottom:18rpx;">
				- 缴费后请于15分钟内离场 超时将重新计费 -
			</view>
		</view>

		<view class="box-cont">
			<view style="font-size: 36rpx;font-family: PingFang SC;text-align: center;
					font-weight: 500;
					color: #000000;">
				-选择/删除车牌-
			</view>
			<view v-if="getcarlists.length" style="display: flex;flex-wrap: wrap;justify-content: space-between;;">
				<view class="box-car" v-for="(itemMsg, indexMsg) in getcarlists" :key="indexMsg">
					<view style="display: flex;">
						<view @click="putcarno(itemMsg)">
							{{ itemMsg.plateNo|plateNo }}
						</view>
						<image v-if="itemMsg.isEtc==1" style="width:50rpx; height:40rpx;margin-left: 10rpx;"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/live/carpark/ic_etc.png"
							mode=""></image>
						<!-- 占位置	 -->
						<view v-else style="width:50rpx; height:40rpx;"></view>
						<view style="border:0.5px  dashed;
							height: 36rpx;
							margin-top:4rpx;
							margin-left: 10rpx;
							margin-right:10rpx;">
						</view>
						<view style="width: 40rpx;height: 40rpx;" @click="affifmdel(itemMsg)">
							<image style="width: 40rpx;height: 40rpx;"
								src="https://slshop-file.oss-cn-beijing.aliyuncs.com/newcar/%E5%9B%BE%E5%B1%82%2010.png"
								mode=""></image>
						</view>
					</view>
				</view>

			</view>
			<view class="" v-else style="text-align: center;">
				暂无数据
			</view>
			<!-- 			<button type="primary" @click="bindcar"
				style="background: #E8D2C1;border-radius: 100rpx;border: none;width: 77%;margin-top: 20rpx;color: #512F14;">绑定车牌</button> -->

			<view class="" style="text-align: center;margin-top: 20rpx;">
				<image @click="bindcar" style="height: 90rpx;"
					src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E7%BB%91%E5%AE%9A%E8%BD%A6%E7%89%8C.png"
					mode="heightFix"></image>

			</view>

		</view>
		<!-- 专享 -->
		<view class="tc-vip-tab">
			<view class="every-vip" @click="emptypark">
				<view class="number-vip">
					<image class="image-footer"
						src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%81%9C%E8%BD%A6%E4%BD%8D%20(1).png"
						mode="">
					</image>
				</view>
				<view class="text-vip">
					空位查询
				</view>
			</view>
			<view class="every-vip" @click="searchpark">
				<view class="number-vip">
					<image class="image-footer"
						src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%9F%BA%E6%9C%ACicon_%E5%AF%BB%E8%BD%A6.png"
						mode="">
					</image>
				</view>
				<view class="text-vip">
					寻车
				</view>
			</view>
			<view class="every-vip" @click="drawabill">
				<view class="number-vip">
					<!-- {{rowsCount}} -->
					<image class="image-footer"
						src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%8F%91%E7%A5%A8%20%E6%8B%B7%E8%B4%9D%203.png"
						mode="">
					</image>

				</view>
				<view class="">
					开发票
				</view>
			</view>
			<view class="every-vip" @click="fastfee">
				<view class="number-vip">
					<image class='image-footer'
						src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E8%AE%B0%E5%BD%95%20(2)%20%E6%8B%B7%E8%B4%9D%203.png"
						alt=""></image>
				</view>
				<view class="">
					缴费记录
				</view>
			</view>
		</view>

		<view class="advertisement" style="" @click="reading()">
			<image style="width: 50rpx;height: 50rpx;margin-right: 20rpx"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/qingdan%20%E6%B8%85%E5%8D%95%20%E6%8B%B7%E8%B4%9D%202.png"
				mode=""></image>
			<view style="color: #ED585A;">
				别急着走，看看我的“优惠券”吧!
			</view>
			<text class="cuIcon-right" style="padding-left: 165rpx;" ></text>
		</view>

		<view class="advertisement" style="" @click="getstandard">
			<image style="width: 50rpx;height: 50rpx;margin-right: 20rpx"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/qingdan%20%E6%B8%85%E5%8D%95%20%E6%8B%B7%E8%B4%9D%202.png"
				mode=""></image>
			<view class="">
				停车场收费标准是什么？快来看看介绍吧！
			</view>
		</view>
		<!-- 店铺广告  -->
		<view class="" style="margin-top: 20rpx;">
			<swiper autoplay :interval="3000" circular>
				<template v-for="(item, index) in parklist">
					<navigator hover-class="none" v-if="item.linkUrl" :url="item.linkUrl">
						<swiper-item :key="index" style="display: flex;justify-content: center;">
							<image :src="item.imgUrl"
								style="width: 100%;height: 210rpx;margin-left: 20rpx;margin-right: 20rpx;"></image>
						</swiper-item>
					</navigator>
				</template>
			</swiper>
		</view>
		<QSpopup ref="QSpopup" :delbol='delbol'></QSpopup>
		<uni-popup ref="perpopup" type="center" :mask-click='false'>
			<view class="permissions_box">
				当您使用APP时，获取最近的停车场需要位置权限，不授权上述权限，不影响APP其他功能使用。
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import api from 'utils/api';
	import __config from 'config/env';
	var qqmapsdk
	import CarNumber from "../component/codecook-carnumber/codecook-carnumber.vue";
	import QQMapWX from 'public/jweixin-module/qqmap-wx-jssdk.min.js';
	const app = getApp();
	import QSpopup from "../component/QS-popup/QS-popup.vue";
	import distanceUtil from 'utils/distanceUtil';
	import utils from "utils/util.js"

	export default {
		components: {
			CarNumber,
			QSpopup
		},
		data() {
			return {
				carNumber: "黑A",
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				storelist: [], //门店列表
				arr2: [],
				selectstore: '',
				getcarlists: '',
				isOpen: false,
				disabled: false,
				show: false,
				open: false,
				vipmember: '', // 会员专享信息
				rowsCount: '',
				title: '',
				info: '',
				yangrMsgShow: true,
				parklist: '',
				sotreparkdata: '',
				delbol: false,
				//从后台生成的二维码或者链接里面获取sf里面传递过来的门店号和停车场参数
				sf: ''
			};
		},
		watch: {
			carNumber(num) {
				console.log(num);
			},
			selectstore: {
				handler: function(newVal) {
					if (newVal && newVal.mustLogin == 'Y') {
						//必须登录，这时候跳转到登录
						app.isLogin('/pages/login/index?reUrl=' + encodeURIComponent(
							'/pages/parkinglot/parking-home/index?item=' + JSON.stringify(newVal)));
					}
				},
				deep: true
			}
		},
		filters: {
			plateNo: function(value) {
				return value.substring(0, 2) + '·' + value.substring(2, value.length)
			},
		},
		onLoad(options) {
			if (options.item) {
				this.selectstore = JSON.parse(decodeURIComponent(options.item));
				if (this.selectstore) {
					this.carNumber = this.selectstore.platePrefix || '';
					if (this.carNumber) {
						this.$refs.carNumber.fill = new Array(8).fill('');
						this.carNumber.split("").forEach((item, index) => {
							this.$refs.carNumber.fill[index] = item;
						})
						this.$refs.carNumber.updateCurrent();
					}
				}
			} else if (options.sf) {
				this.sf = options.sf;
			} else if (options.scene) {
				const qrCodeScene = decodeURIComponent(options.scene);
				if (qrCodeScene) {
					//接受二维码中参数  参数sf=XXX
					// 缴费二维码能带上门店和停车场参数，都可选
					// pages/parkinglot/parking-home/index?sf=&nGo=
					// sf=HSY,290,100029797
					this.sf = utils.UrlParamHash(qrCodeScene, 'sf');
				}
			}
			const userInfo = uni.getStorageSync("user_info");
			if (app.isLogin(false)) {
				if (!this.selectstore || !this.selectstore.store) {
					this.getstorelist()
				}
			} else {
				app.doLogin().then(res => {
					if (!this.selectstore || !this.selectstore.store) {
						this.getstorelist()
					}
				});
			}
		},

		onShow() {
			if (app.isLogin(true)) {
				this.getMemberexclusive()
				this.getcarlist()
				this.geetlist()
			}
		},

		mounted() {
			// 获取停车广告
			api.parklist().then(res => {
				this.parklist = res.data
			})
		},

		methods: {
			drawabill() {
				uni.navigateTo({
					url: "/pages/invoice/parkApply/index",
				});
			},
			//跳转停车劵
			Parkingcoupon() {
				uni.navigateTo({
					url: '/pages/coupon/coupon-user-list/index?navTabCur=2'
				})
			},
			searchpark() {
				if (this.selectstore && this.selectstore.store) {
					uni.navigateTo({
						url: `/pages/parkinglot/patrol-car/index?store=${this.selectstore.store}&storeName=${encodeURIComponent(this.selectstore.storeName)}`
					})
				} else {
					uni.navigateTo({
						url: '/pages/parkinglot/select-store/index'
					})
				}
			},
			//空车查询
			emptypark() {
				if (this.selectstore && this.selectstore.store) {
					uni.navigateTo({
						url: `/pages/parkinglot/empty-carquery/empty-carquery?store=${this.selectstore.store}&storeName=${encodeURIComponent(this.selectstore.storeName)}`
					})
				} else {
					uni.navigateTo({
						url: '/pages/parkinglot/select-store/index'
					})
				}
			},
			affifmdel(data) {
				let that = this
				this.sotreparkdata = data
				this.delbol = true
				this.$nextTick(function(item) {
					that.$refs.QSpopup.shows()
				})
			},
			//删除
			delpark(data) {
				api.delcar({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
					"openId": uni.getStorageSync('user_info').openId || uni.getSystemInfoSync().deviceId,
					"plateNo": this.sotreparkdata.plateNo,
					"plateType": this.sotreparkdata.plateType
				}).then(res => {
					console.log(res.data == 'OK')
					if (res.data == 'OK') {
						this.$refs.QSpopup.hide()
						this.getcarlist()
					}
				})
			},
			closeYangrMsg() {
				this.yangrMsgShow = true;
			},
			getstandard() {
				if (this.selectstore && this.selectstore.store) {
					uni.navigateTo({
						url: `/pages/parkinglot/charging-standard/charging-standard?img=` + this.selectstore
							.parkFeePic
					})
				} else {
					uni.navigateTo({
						url: '/pages/parkinglot/select-store/index'
					})
				}
			},
			//查询缴费记录
			geetlist() {
				api.parklist({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
					"openId": uni.getStorageSync('user_info').openId || uni.getSystemInfoSync().deviceId,
					"pageNo": 1,
					"pageSize": 10
				}).then(res => {
					this.rowsCount = res.data.rowsCount
				})
			},
			fastfee() {
				uni.navigateTo({
					url: '/pages/parkinglot/fast-fee/fast-fee'
				})
			},
			changeContent(item, index) {
				this.open = !this.open;
			},
			//查询会员专享
			getMemberexclusive() {
				api.Memberexclusive({
					"channel": "SONGSHU",
					"store": this.selectstore.store,
					"custId": uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
					"openId": uni.getStorageSync('user_info').openId || uni.getSystemInfoSync().deviceId,
				}).then(res => {
					this.vipmember = res.data
					console.log(res.data, '====')
				})
			},

			//查询停车费用
			parkingrate() {
				api.parkfee({
					"channel": "SONGSHU",
					"store": this.selectstore.store,
					"plateNo": this.carNumber,
					"custId": uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
					"custType": "",
					"openId": uni.getStorageSync('user_info').openId || uni.getSystemInfoSync().deviceId,
					"parkCode": "",
					"mobile": uni.getStorageSync("user_info").phone,
				}).then(res => {
					this.delbol = false;
					if (res.code == '0') {
						uni.setStorageSync(this.carNumber, res);
						uni.navigateTo({
							url: `/pages/parkinglot/parkingrate/parkingrate?custId=${uni.getStorageSync('user_info').erpCid}&carNumber=${this.carNumber}&store=${this.selectstore.store}&isETC=${this.selectstore.enableEtc||0}&img=${this.selectstore.parkFeePic}&isPlateEtc=${res.data.isPlateEtc}`
						})
					} else if (res.code == '90002') {
						this.$refs.QSpopup.shows(this.carNumber, '不需要缴费')
					} else if (res.code == "90001") {
						this.$refs.QSpopup.shows(this.carNumber, res.msg, "/pages/parkinglot/fast-fee/fast-fee",
							'to');
					} else {
						this.$refs.QSpopup.shows(this.carNumber, res.msg);
					}
				})
			},
			//赋值车牌号
			putcarno(val) {
				this.$refs.carNumber.fill = val.plateNo.split('').length == '7' ? val.plateNo.split('').concat('') : val
					.plateNo.split('')
			},
			//获取绑定的车牌
			getcarlist() {
				api.getcarlist({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
					"openId": uni.getStorageSync('user_info').openId || uni.getSystemInfoSync().deviceId,
					"plateType": ""
				}).then(res => {
					if (res.code == '0') {
						// this.itemList.body = res.data.dataList
						this.getcarlists = res.data.dataList
						// this.$set(itemList,body,res.data.dataList)
					}
				})
			},
			// 查看停车劵
			parkingcoupon() {
				uni.navigateTo({
					url: `/pages/parkinglot/park-coupon/park-coupon?custId=${uni.getStorageSync('user_info').erpCid}`
				})
			},
			// 绑定车牌
			bindcar() {
				uni.navigateTo({
					url: '/pages/parkinglot/bind-car/bind-car'
				})
			},
			getlocation(e) {
				uni.navigateTo({
					url: '/pages/parkinglot/select-store/index'
				})
			},
			getstorelist() {
				let that = this;
				api.getstorelist().then(data => {
					that.storelist = data.data;
					//如果sf有值就是从链接带进了门店和停车场参数
					if (that.sf && that.storelist && this.storelist.length > 0) {
						const storeCode = that.sf.split(",")[1];
						for (let i = 0; i < that.storelist.length; i++) {
							if (that.storelist[i].store == storeCode) {
								that.selectstore = that.storelist[i];
								that.dealSelectstore();
								return;
							}
						}
						// 否则就定位获取最近的停车场
					} else {
						that.checkPosPermission();
					}
				})
			},

			checkPosPermission() {
				// #ifdef MP-WEIXIN
				this.getCurrentPos();
				// #endif 
				// #ifdef APP-PLUS
				let that = this;
				var platform = uni.getSystemInfoSync().platform;
				if (platform == 'android') {
					plus.android.checkPermission(
						'android.permission.ACCESS_FINE_LOCATION',
						granted => {
							if (granted.checkResult == -1) {
								//弹出
								that.$refs.perpopup.open('top')
							} else {
								//执行你有权限后的方法
								that.getCurrentPos();
							}
						},
						error => {
							console.error('Error checking permission:', error.message);
						}
					);
					plus.android.requestPermissions(['android.permission.ACCESS_FINE_LOCATION'],
						(e) => {
							//关闭
							that.$refs.perpopup.close()
							if (e.granted.length > 0) {
								//执行你有权限后的方法
								that.getCurrentPos();
							}
						})

				} else {
					//执行你有权限后的方法 ios
					that.getCurrentPos();
				}
				// #endif 
			},

			getCurrentPos() {
				let that = this;
				uni.getLocation({
					type: 'gcj02',
					// 开启高精度定位
					// isHighAccuracy: true,
					// 是否解析地址信息
					geocode: true,
					success: (res) => {
						const nearDistance = distanceUtil.getNearestDistance(res.latitude, res
							.longitude, that.storelist);
						if (nearDistance) that.selectstore = nearDistance;
						that.dealSelectstore();
					},
					fail() {
						//定位失败
						that.selectstore = null;
					}
				});
			},

			reading(){
				uni.navigateTo({
					url: '/pages/coupon/coupon-user-list/index'
				})
			},

			dealSelectstore() {
				let that = this;
				if (that.selectstore) {
					that.carNumber = that.selectstore.platePrefix || '';
					if (that.carNumber) {
						that.$refs.carNumber.fill = new Array(8).fill('');
						that.carNumber.split("").forEach((item, index) => {
							that.$refs.carNumber.fill[index] = item;
						})
						that.$refs.carNumber.updateCurrent();
					}
					that.getMemberexclusive()
					that.getcarlist()
					that.geetlist()
				}
			}
		}
	};
</script>

<style scoped lang="less">
	.advertisement {
		height: 86rpx;
		background: white;
		border-radius: 20rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		margin-top: 20rpx;
		display: flex;
		align-items: center;
		// margin-top: 10rpx;

	}

	::v-deep button:after {
		boder: none !important;
	}

	::v-deep .uni-collapse-item__title-text {
		font-size: 30rpx;
		font-weight: 600;
	}

	.please-Car-number {
		padding-top: 20rpx;
		text-align: center;
		margin-top: 20rpx;
		font-size: 38rpx;
		font-family: FZZhunYuan-M02;
		font-weight: 500;
		color: #000000;
	}

	.content {
		background: white;
		border-radius: 50rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
	}

	.please-store {
		margin-top: 40rpx;
		text-align: center;
	}

	.please-binding-car {
		margin-top: 20rpx;
	}

	.Memberexclusive {
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		background: white;
		// height: 100rpx;
		align-items: center;
		border-bottom: 1px solid #ececec;
		padding-left: 20rpx;
		padding-right: 20rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		border-top-left-radius: 21rpx;
		border-top-right-radius: 21rpx;
		padding-top: 20rpx;
		padding-bottom: 20rpx;
	}

	.every-vip {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: white;
		justify-content: center;
	}

	.tc-vip {
		display: flex;
		justify-content: space-between;
		background: white;
		padding-left: 50rpx;
		padding-right: 50rpx;
		height: 150rpx;
		align-items: center;
		margin-left: 20rpx;
		margin-right: 20rpx;
		margin-top: -116rpx;
		border-radius: 50rpx;
	}

	.number-vip {
		font-size: 46rpx;
		color: #9F7A5E;
		font-family: FZCuYuan-M03;
	}

	.qita-box{
		position: relative;
		width: 96rpx; 
		height: 52rpx;
		text-align:center;
		line-height:52rpx;
		margin-top: 6rpx;
		margin-bottom: 12rpx;
		.number-vip {
			position: relative;
			z-index: 10;
			color: #fff;
		}
		.qita{
			position: absolute;
			top: 0;
			left: 0;
			width: 96rpx; 
			height: 52rpx;
		}
	}	

	.text-vip {
		font-size: 28rpx;
		color: #000000;
	}

	.tc-vip-tab {
		display: flex;
		justify-content: space-between;
		background: white;
		padding-left: 20rpx;
		padding-right: 20rpx;
		// height: 20/0rpx;
		// border: 1px solid yellow;
		align-items: center;
		margin-top: 20rpx;
		border-radius: 37rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		// padding: 52rpx;
		padding-top: 40rpx;
		padding-bottom: 30rpx;
		padding-left: 30rpx;
		padding-right: 30rpx;

	}

	.location {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80rpx;
	}

	.collapse {
		padding: 0 36rpx;
	}

	.coll-header {
		display: flex;
		// height: 100rpx;
		align-items: center;
		justify-content: space-between;
		// border: 1px solid red;
		// border-bottom: 1px solid #ECECEC;
		background: white;
		padding-left: 20rpx;
		padding-right: 20rpx;
		height: 118rpx;
		background: #FFFFFF;
		border-radius: 37rpx;
		margin-top: 20rpx;
		margin-right: 20rpx;
		margin-left: 20rpx;
	}

	.box {
		overflow: hidden;
		transition: all 0.3;
		border-bottom: 1px solid #f9f9f9;
	}

	.head-txt {
		font-weight: 700;
		font-size: 30rpx;
		font-family: FZZhunYuan-M02;
		font-weight: 400;
		color: #636363;

	}

	.sub-num {
		font-size: 28rpx;
		color: #999999;
	}

	.box-circle {
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		background-color: #18bbb4;
		margin-left: 4rpx;
		margin-right: 19rpx;
	}

	.box-cont {
		display: flex;
		flex-wrap: wrap;
		background: white;
		// justify-content: center;
		flex-direction: column;
		margin-bottom: 20rpx;
		padding: 21rpx;
		// margin-right: 21rpx;
		// margin-right: 30rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		margin-top: 14px;
		border-radius: 44rpx;
		// margin-top: -16rpx;
	}

	.box-cont:last-child {
		margin-bottom: 20rpx;
	}

	.box-car {
		// margin-right: 15rpx;
		// padding-bottom: 10rpx;
		background: #F4F4F4;
		width: 318rpx;
		height: 70rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		// justify-content: space-between;
		margin-top: 15rpx;
	}

	.img-title {
		display: flex;
		align-items: center;
		font-size: 33rpx;
		font-family: FZCuYuan-M03;
		font-weight: 400;
		color: #9F7A5E;

	}

	.image-footer {
		width: 110rpx;
		height: 110rpx;
	}

	.permissions_box {
		padding: 200rpx 30rpx 50rpx;
		background-color: #fff;
		color: #000000;
	}
</style>