<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">无车牌入场</block>
		</cu-custom>
	</view>
</template>

<script>
const util = require('utils/util.js');
const app = getApp();
import api from 'utils/api';
export default {
	data() {
		return {
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			bgColor: 'rgba(255, 255, 255, 0)',
			startTime: 0,
			logs: []
		};
	},
	onLoad(options) {
		console.log(options);
		this.startTime = new Date().getTime();
		this.logs.push({
			wxdata_perf_monitor_id: '步骤1:页面onLoad',
			wxdata_perf_error_msg: 'success',
			wxdata_perf_cost_time: new Date().getTime() - this.startTime,
			wxdata_perf_extra_info1: new Date().toLocaleString()
		});
		if (options.q) {
			const qrCodeScene = decodeURIComponent(options.q);
			this.logs.push({
				wxdata_perf_step_id: '步骤2:解析qrCodeScene',
				wxdata_perf_error_msg: qrCodeScene,
				wxdata_perf_cost_time: new Date().getTime() - this.startTime,
				wxdata_perf_extra_info1: new Date().toLocaleString()
			});
			if (qrCodeScene) {
				let sence = qrCodeScene.split('/');
				console.log(sence);
				this.loginMaUser(sence);
			}
		}else if (options.scene) {
			// sf=&nGo=&c=r2KSQ7GBY/2/2 路径样例
			let scenes = decodeURIComponent(options.scene);
			console.log('===scenes=====');
			const cParams = util.UrlParamHash(scenes, 'c');
			if (cParams) {
				//扫小程序码入场新添加的逻辑
				// 拼接成扫普通链接二维码的参数样式 ["https:", "", "shopapi.songlei.com", "slshop-h5", "ma", "r100000862", "1", "192.168.1.229"]
				// 因为后面逻辑是根据这个格式解析的
				const jointScene = ['', '', '', '', ''].concat(cParams.split('/'));
				this.loginMaUser(jointScene);
			} else {
				//感觉这里没啥用，之前伟东的逻辑先不敢直接删除
				this.loginMaUser(JSON.parse(options.scene));
			}
		} else if (options.c) {
			// 直接链接过来
			const jointScene = ['', '', '', '', ''].concat(options.c.split('/'));
			this.loginMaUser(jointScene);
		}
	},

	//页面卸载时候上报日志到微信公众平台
	onUnload() {
		wx.reportEvent('wxdata_perf_monitor', {
			wxdata_perf_monitor_id: 'parkingpot-unlicense',
			wxdata_perf_monitor_level: 1,
			wxdata_perf_error_code: 0,
			wxdata_perf_error_msg: 'success',
			wxdata_perf_cost_time: new Date().getTime() - this.startTime,
			wxdata_perf_extra_info1: JSON.stringify(this.logs)
		});
	},

	methods: {
		loginMaUser(sence) {
			this.logs.push({
				wxdata_perf_step_id: '步骤3:调loginMaUser',
				wxdata_perf_error_msg: '步骤3:调loginMaUser',
				wxdata_perf_cost_time: new Date().getTime() - this.startTime,
				wxdata_perf_extra_info1: new Date().toLocaleString()
			});
			const userInfo = uni.getStorageSync('user_info');
			if (userInfo && userInfo.openId) {
				this.unlicensedvehicleS(sence);
			} else {
				app.doLogin().then((res) => {
					const user_info = uni.getStorageSync('user_info');
					this.unlicensedvehicleS(sence);
				});
			}
		},

		unlicensedvehicleS(sence) {
			this.logs.push({
				wxdata_perf_step_id: '步骤5:startUnlicensedvehicleS解析sence',
				wxdata_perf_error_msg: sence.toString(),
				wxdata_perf_cost_time: new Date().getTime() - this.startTime,
				wxdata_perf_extra_info1: new Date().toLocaleString()
			});

			if (sence) {
				console.log('==unlicensedvehicleS=====', sence);
				if (sence[6] == '2') {
					this.logs.push({
						wxdata_perf_step_id: '步骤6:goToparkingrate去缴费',
						wxdata_perf_error_msg: sence[6],
						wxdata_perf_cost_time: new Date().getTime() - this.startTime,
						wxdata_perf_extra_info1: new Date().toLocaleString()
					});
					uni.redirectTo({
						url: `/pages/parkinglot/parkingrate/parkingrate?scene=${JSON.stringify(sence)}`
					});
				} else {
					let data = {
						openId: uni.getStorageSync('user_info').openId,
						channel: 'SONGSHU',
						store: '',
						custId: uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
						mobile: uni.getStorageSync('user_info').phone,
						passWay: sence[7],
						passType: sence[6],
						parkCode: sence[5].slice(1)
					};
					this.logs.push({
						wxdata_perf_step_id: '步骤7:startUnlicensedvehicleAPI调无牌照入场接口',
						wxdata_perf_error_msg: JSON.stringify(data),
						wxdata_perf_cost_time: new Date().getTime() - this.startTime,
						wxdata_perf_extra_info1: new Date().toLocaleString()
					});

					this.startTime = new Date().getTime();
					api.unlicensedvehicle(data).then((res) => {
						console.log(res, '======-----');
						this.logs.push({
							wxdata_perf_step_id: '步骤8:endUnlicensedvehicleAPI调无牌照入场接口结束',
							wxdata_perf_error_msg: JSON.stringify(res),
							wxdata_perf_cost_time: new Date().getTime() - this.startTime,
							wxdata_perf_extra_info1: new Date().toLocaleString()
						});

						if (res.code == '0') {
							uni.showToast({
								title: '入场成功'
							});
							setTimeout(function () {
								uni.redirectTo({
									url: '/pages/parkinglot/parking-home/index'
								});
							}, 1000);
						} else if (res.code == '90000') {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false,
								success(res) {
									uni.redirectTo({
										url: '/pages/parkinglot/parking-home/index'
									});
								}
							});
						}
					});
				}
			}
		}
	}
};
</script>

<style></style>
