<template>
	<view class="page">
		<cu-custom bgImage="https://img.songlei.com/live/point-sign/top.png" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">{{parkfee.orderStatus=='Y'?'支付成功':'缴费详情'}}</block>
		</cu-custom>
		<view style="position: relative; height: 380rpx;">
			<image mode="widthFix" style="width: 750rpx" src="https://img.songlei.com/live/point-sign/bg.png">
			</image>
			<view class="detail-container" v-if="parkfee">
				<view class="detail-content">
					<view class="flex justify-center align-center" v-if="parkfee.orderStatus=='Y'||parkfee.orderStatus=='E'">
						<image mode="widthFix" style="width: 56rpx; height: 56rpx;"
							src="https://img.songlei.com/live/pay_success.png">
						</image>
						<view  v-if="parkfee.isEtc==1&&parkfee.orderStatus=='E'">
							<text class="pay-status" style="font-size: 27rpx;">剩余</text>
							<text class="pay-status" style="color: #ff0000;font-size: 27rpx;">{{etcNeedPay}}元</text>
							<text  class="pay-status"  style="font-size: 27rpx;">将于出库时通过ETC扣除</text>
						</view>
						<text v-else class="pay-status">支付成功</text>
					</view>

					<view class="flex justify-center align-center" v-if="parkfee.orderStatus=='Y'||parkfee.orderStatus=='E'"
						style="padding-bottom: 4rpx;border-bottom: dashed 1rpx #CCCCCC;">
						<view class="parkinglot_time-count text-center flex flex-direction justify-center">
							<view class="time-tip">{{outTime<0||isNegative?'已超时':''}}</view>
							<count-down :fontWeight="800" negativeIsShow :outTime="outTime" :textColor="'#B4835B'"
								:backgroundColor="'white'" :connectorColor="'#B4835B'"
								@updateNegativeShow="updateNegativeShow" fontSize="40rpx">
							</count-down>
							<view class="time-tip">离场倒计时</view>
						</view>
					</view>

					<view class="from_fee" v-if="parkfee">
						<view class="parkfee">
							<view class="fee_text">
								绑定车牌：
							</view>
							<view class="fee_num">
								{{parkfee.plateNo}}
							</view>
						</view>
						<view class="parkfee">
							<view class="fee_text">
								订单编号:
							</view>
							<view class="fee_num">
								{{parkfee.orderId}}
							</view>
						</view>
						<view class="parkfee">
							<view class="fee_text">
								车场名称:
							</view>
							<view class="fee_num">
								{{parkfee.parkName}}
							</view>
						</view>

						<view class="parkfee">
							<view class="fee_text">
								入场时间:
							</view>
							<view class="fee_num">
								{{parkfee.enterTime}}
							</view>
						</view>

						<view class="parkfee">
							<view class="fee_text">
								缴费时间:
							</view>
							<view class="fee_num">
								{{parkfee.payTime}}
							</view>
						</view>

						<view class="parkfee" style="padding-bottom: 36rpx;border-bottom: dashed 1rpx #CCCCCC;">
							<view class="fee_text">
								停车时长:
							</view>
							<view class="fee_num">
								{{delayTimeFormat}}
							</view>
						</view>


						<view class="parkfee">
							<view class="fee_text">
								缴费金额
							</view>
							<view class="fee_num  flex align-center">
								<format-price color="#2c2c2c" signFontSize="18rpx" smallFontSize="22rpx"
									priceFontSize="28rpx" :price="parkfee.totalFee"></format-price>
							</view>
						</view>

						<view class="parkfee" v-for="(item,index) in parkfee.details" :key="index">
							<view class="fee_text">
								{{item.deductName}} {{item.deductSource=='P'?`(${item.amount})`:''}}
							</view>
							<view class="fee_num flex align-center" v-if="item.money">
								{{item.deductSource!='C'?'-':''}}
								<format-price color="#2c2c2c" signFontSize="18rpx" smallFontSize="22rpx"
									priceFontSize="28rpx" :price="item.money"></format-price>
							</view>
						</view>

						<view class="parkfee" style="border-top:dashed 1rpx #CCCCCC;padding-top: 30rpx;">
							<view class="fee_text">
								合计抵扣:
							</view>
							<view class="fee_num flex align-center">
								-
								<format-price color="#2c2c2c" signFontSize="18rpx" smallFontSize="22rpx"
									priceFontSize="28rpx" :price="totalDiscount"></format-price>
							</view>
						</view>

						<view v-if="isETC>0" class="parkfee" style="justify-content: flex-end;">
							<view class="fee_num" style="padding-right: 10rpx;">
								剩余应付:
							</view>
							<view class="fee_num">
								<format-price styleProps="font-weight:800" color="#C7A081;" signFontSize="18rpx"
									smallFontSize="30rpx" priceFontSize="40rpx" :price="etcNeedPay"></format-price>
							</view>
						</view>

						<view v-else class="parkfee" style="justify-content: flex-end;">
							<view class="fee_num" style="padding-right: 10rpx;">
								实付金额:
							</view>
							<view class="fee_num">
								<format-price styleProps="font-weight:800" color="#C7A081;" signFontSize="18rpx"
									smallFontSize="30rpx" priceFontSize="40rpx"
									:price="parkfee.costMoney"></format-price>
							</view>
						</view>
					</view>
				</view>
				<button v-if="parkfee" type="primary" @click="$noMultipleClicks(close)"
					style="color: #512F14;font-weight: 800;margin-top: 40rpx;margin-bottom: 40rpx;background: #c9a588;border-radius: 36rpx;border: none;width: 90%; text-align: center;">关闭</button>
				<button v-if="parkfee&&parkfee.orderStatus=='E'" type="primary" @click="$noMultipleClicks(reOrder)"
						style="font-size: 32rpx;color: #512F14;font-weight: 800;margin-top: 40rpx;margin-bottom: 40rpx;background: #c9a588;border-radius: 36rpx;border: none;width: 90%; text-align: center;">取消ETC支付并使用其他方式支付</button>
				<view v-else>
					<view
						style="display: flex;align-items: center;justify-self: center;flex-direction: column;margin-top: 100rpx;">
						<image src="https://img.songlei.com/live/nodata.png" style="width: 200rpx;height: 134rpx;">
						</image>
						<view style="margin-top: 20rpx;color: #8799A3;text-align: center;">
							暂无数据
						</view>
					</view>
				</view>

			</view>
		</view>
	</view>
</template>

<script>
	import api from 'utils/api';
	import formatPrice from "@/components/format-price/index.vue";
	import countDown from "@/components/count-down/index";
	export default {
		components: {
			formatPrice,
			countDown
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: getApp().globalData.theme, //全局颜色变量
				parkfee: '',
				userinfo: uni.getStorageSync('user_info'),
				carNumber: '',
				store: "",
				noClick: true,
				isNegative: false,
				isETC: '',
				totalDiscount: 0,
				etcNeedPay: 0,
				parkFeePic:''
			}
		},
		computed: {
			delayTimeFormat() {
				let result = "";
				var days = parseInt(this.parkfee.parkMinutes / 60 / 24, 10);
				if (days > 0) {
					result = days + '天'
				}
				var hours = parseInt(this.parkfee.parkMinutes / 60, 10);
				if (hours > 0) {
					result += hours + '小时'
				}
				var minutes = parseInt(this.parkfee.parkMinutes % 60, 10);
				if (minutes > 0) {
					result += minutes + '分钟'
				}
				return result;
			},

			outTime() {
				if (this.parkfee.delayTime) {
					return new Date(this.parkfee.delayTime).getTime() - new Date().getTime()
				}
				return ""
			}

		},

		onLoad(options) {
			if (options.isETC > 0) {
				this.isETC = options.isETC;
			}
			if (options.id) {
				this.orderId = options.id
				this.getdetail()
			}
			this.store = options.store;
			console.log("====options.store========="+options.store);
			this.parkFeePic = options.parkFeePic;
		},

		methods: {
			updateNegativeShow() {
				this.isNegative = true;
			},
			// 获取详情
			getdetail() {
				api.feedetail({
					"channel": "SONGSHU",
					"orderId": this.orderId
				}).then(res => {
					if (res.data) {
						this.parkfee = res.data;
						this.isETC = this.isETC || res.data.isETC;
					} else {
						this.parkfee = ''
					}

					if (this.parkfee== '') {
						this.totalDiscount = 0;
					}
					if (this.parkfee.details && this.parkfee.details.length > 0) {
						this.totalDiscount = 0;
						this.parkfee.details.forEach(item => {
							this.totalDiscount =(Number( this.totalDiscount) + Number(item.money)).toFixed(2)
						})
						console.log("this.totalDiscount"+this.totalDiscount)
					}
					if (this.isETC>0 ) {
						this.etcNeedPay = (Number(this.parkfee.totalFee) - Number(this.totalDiscount)).toFixed(2);
					}
				})
			},

			close() {
				let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
				if (routes.length > 1) {
					uni.navigateBack({
						delta: 1
					});
				} else {
					uni.navigateTo({
						url: '/pages/parkinglot/fast-fee/fast-fee'
					})
				}
			},
			
			//重新下订单
			reOrder(){
				api.restorder({
					"channel": "SONGSHU",
					"orderId": this.orderId,
					"cancelSource": "MANUAL",
					"cancelBy": uni.getStorageSync('user_info').id,
				}).then(res => {
					uni.redirectTo({
						url: '/pages/parkinglot/parkingrate/parkingrate?reOrder=1&store='+this.store+'&carNumber='+this.parkfee.plateNo+'&parkFeePic='+this.parkFeePic
					})
				})
				
			}
		}

	}
</script>

<style scoped lang="less">
	.detail-container {
		position: absolute;
		top: 0;
		width: 690rpx;
		margin: 30rpx;
	}

	.detail-content {
		background: #FFFFFF;
		border-radius: 30rpx;
		padding: 37rpx 34rpx;
	}

	.headimgUrl {
		display: flex;
		width: 200rpx;
		height: 200rpx;
		border-radius: 20rpx;
		justify-content: center;
		text-align: center;
	}

	.headimgUrl_view {
		display: flex;
		justify-content: center;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	page {
		background: #FFFFFF;
	}

	.fee_num {
		font-size: 28rpx;
		font-weight: 500;
		color: #000000;
	}

	.parkfee {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 20rpx;
	}

	.activite {
		color: #04c9c3;
	}

	.scroll-view_H {
		white-space: nowrap;
		width: 100%;
		color: #CCCCCC;
	}

	.scroll-view-item_H {
		display: inline-block;
		width: 20%;
		height: 50rpx;
		line-height: 50rpx;
		text-align: center;
		padding: 10px 0;
	}

	.from_fee {
		padding: 10rpx 10rpx 20rpx;
		font-size: 26rpx;
	}

	.fee_text {
		font-size: 26rpx;
		color: #888888;
	}

	.pay-status {
		font-size: 40rpx;
		font-weight: 800;
		color: #B4835B;
		line-height: 40rpx;
		padding-left: 14rpx;
	}

	.parkinglot_time-count {
		background-image: url("https://img.songlei.com/live/pay_bg.png");
		background-size: 100% auto;
		background-repeat: no-repeat;
		width: 284rpx;
		height: 284rpx;
		margin-top: 40rpx;
		margin-bottom: 34rpx;

		.time {
			font-size: 40rpx;
			font-weight: 800;
			color: #B4835B;
			line-height: 40rpx;
		}

		.time-tip {
			font-size: 26rpx;
			font-weight: 500;
			color: #888888;
			line-height: 40px;
		}
	}
</style>