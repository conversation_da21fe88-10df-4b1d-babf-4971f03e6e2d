<template>
	<view class="page">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">缴费详情</block>
		</cu-custom>
		<view class="from_fee" v-if="parkfee">
			<view class="parkfee">
				<view class="fee_text">
					停车场地:
				</view>
				<view class="fee_num">
					{{parkfee.parkName}}
				</view>
			</view>
			<view class="parkfee">
				<view class="fee_text">
					车辆号码：
				</view>
				<view class="fee_num">
					{{parkfee.plateNo}}
				</view>
			</view>
			<view class="parkfee">
				<view class="fee_text">
					入场时间:
				</view>
				<view class="fee_num">
					{{parkfee.enterTime}}
				</view>
			</view>
			<view class="parkfee" style="padding-bottom: 20rpx;border-bottom: dashed 1rpx #CCCCCC;">
				<view class="fee_text">
					停车时长:
				</view>
				<view class="fee_num">
					{{parkfee.parkMinutes}}分钟
				</view>
			</view>
			<view class="parkfee">
				<view class="fee_text">
					总费用:
				</view>
				<view class="fee_num">
					<format-price color="#2c2c2c" signFontSize="18rpx" smallFontSize="22rpx"
						priceFontSize="32rpx" :price="parkfee.totalFee"></format-price>
				</view>
			</view>
			<view class="parkfee" v-for="(item,index) in formatDetails" :key="index">
				<view class="fee_text">
					{{item.deductName}} {{item.deductSource=='P'?`(${item.amount})`:''}}
				</view>
				<view class="fee_num flex align-center" v-if="item.money">
					{{item.deductSource!='C'?'-':''}}
					<format-price color="#2c2c2c" signFontSize="18rpx" smallFontSize="22rpx"
						priceFontSize="32rpx" :price="item.money"></format-price>
				</view>
			</view>
			
			<view class="parkfee">
				<view class="fee_text">
					支付方式:
				</view>
				<view class="fee_num">
					在线支付
				</view>
			</view>
			<view class="parkfee" style="padding-bottom:20rpx;border-bottom: dashed 1rpx #CCCCCC;">
				<view class="fee_text">
					订单编号:
				</view>
				<view class="fee_num">
					{{parkfee.orderId}}
				</view>
			</view>
			<view class="parkfee" >
				<view class="fee_text">
					提交时间:
				</view>
				<view class="fee_num">
					{{parkfee.costTime}}
				</view>
			</view>
			<view class="parkfee">
				<view class="fee_text">
					支付时间:
				</view>
        <view class="fee_num" v-if="parkfee.orderStatus=='N'">
          请在 {{parkfee.delayTime}} 之前完成支付
        </view>
				<view class="fee_num" v-if="parkfee.orderStatus!='N'">
          {{parkfee.payTime?parkfee.payTime :'暂无'}}
				</view>
			</view>

			<view  v-if="parkfee.orderStatus=='N'||parkfee.orderStatus=='E'">
				<button type="primary" @click="$noMultipleClicks(openPay)"
					style="background: #c9a588;border-radius: 36rpx;border: none;width: 100%;margin-top: 40rpx;">{{parkfee.orderStatus == 'E'?'取消ETC支付并使用其他方式支付':'立即支付'}}</button>
			</view>
		</view>
		<view v-else>
			<view 
				style="display: flex;align-items: center;justify-self: center;flex-direction: column;margin-top: 100rpx;">
				<image src="https://img.songlei.com/live/nodata.png" mode="" style="width: 200rpx;height: 134rpx;">
				</image>
				<view  style="margin-top: 20rpx;color: #8799A3;text-align: center;">
					暂无数据
				</view>
			</view>
		</view>
		<!-- 支付方式弹框 -->
		<view class="cu-modal bottom-modal" style="overflow: hidden;" :class="payAsync ? 'show' : ''"
			@tap.stop="payAsync = false">
			<view class="cu-dialog bg-white"
				style="padding-bottom: 80rpx; position: absolute; bottom: 0; left: 0;" @tap.stop>
				<pay-components ref="pay"  orderType="PARK"></pay-components>
				<button style="width:90%" class="cu-btn round bg-red margin-top-lg text-white"
					@tap.stop="$noMultipleClicks(paysubmit)" :loading="loading" :disabled="loading" type
					id="goPay">立即支付</button>
			</view>
		</view>
	</view>
</template>

<script>
	import api from 'utils/api';
	import formatPrice from "@/components/format-price/index.vue";
	import payComponents from '@/components/pay-components/pay-components.vue';
	export default {
		components: {
			formatPrice,
			payComponents
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: getApp().globalData.theme, //全局颜色变量
				parkfee: '',
				userinfo: uni.getStorageSync('user_info'),
				carNumber: '',
				store: "",
				noClick: true,
				payAsync: false,
				loading: false
			}
		},
		
		computed: {
			formatDetails: function() {
				const formatLsit = [],
					hasModel = [];
				if (this.parkfee.details && this.parkfee.details.length > 0) {
					this.parkfee.details.forEach(item => {
						if (hasModel.length == 0) {
							formatLsit.push(item);
							hasModel.push(item.deductMode+item.deductSource);
						} else if (hasModel.indexOf(item.deductMode+item.deductSource) == -1) {
							formatLsit.push(item);
							hasModel.push(item.deductMode+item.deductSource)
						} else {
							const index = hasModel.indexOf(item.deductMode+item.deductSource);
							formatLsit[index].money = (Number(formatLsit[index].money) + Number(item.money))
								.toFixed(2);
						}
					})
				}
				return formatLsit;
			}
		},
		onLoad(options) {
			if (options.item) {
				this.orderId = options.item
			}
		},

		onShow() {
			this.getdetail()
		},
		methods: {
			// 立即支付;
			openPay() {
				if (this.parkfee.orderStatus == 'E') {
					api.restorder({
						"channel": "SONGSHU",
						"orderId": this.parkfee.orderId,
						"cancelSource": "MANUAL",
						"cancelBy": uni.getStorageSync('user_info').id,
					}).then(res => {
						uni.redirectTo({
							url: '/pages/parkinglot/parkingrate/parkingrate?reOrder=1&store='+this.parkfee.store+'&carNumber='+this.parkfee.plateNo
						})
					})
				} else {
					this.payAsync = true;
				}
			},
			
			async paysubmit() {
				api.parkPayOrder({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid || uni.getStorageSync('user_info').openId,
					"orderId": this.parkfee.orderId,
				}).then(async (res) => {
					if (res && res.data) {
						const orderInfo = {
							"orderId": this.parkfee.orderId,
							...res.data.payRequest,
							expiredTime: res.data.payRequest.expireSeconds
						}
						await this.$refs.pay?.payOrder(orderInfo, false, "PARK")
					}
				}).catch(e => {
					console.log("====e=====",e)
				    this.payAsync = false;
				});
			},
			// 点击立即支付
			paypark() {
				console.log(1)
			},
			//  获取详情
			getdetail() {
				api.feedetail({
					"channel": "SONGSHU",
					"orderId": this.orderId
				}).then(res => {
					if (res.data) {
						this.parkfee = res.data
					} else {
						this.parkfee = ''
					}
				})
			}
		},

	}
</script>

<style scoped>
	.headimgUrl {
		display: flex;
		width: 200rpx;
		height: 200rpx;
		border-radius: 20rpx;
		justify-content: center;
		text-align: center;
	}

	.headimgUrl_view {
		display: flex;
		justify-content: center;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	page {
		background: #FFFFFF;
	}

	.fee_num {
		font-size: 28rpx;
		font-weight: 500;
		color: #000000;
	}

	.parkfee {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 20rpx;
	}

	.activite {
		color: #04c9c3;
	}


	.scroll-view_H {
		white-space: nowrap;
		width: 100%;
		color: #CCCCCC;
	}

	.scroll-view-item_H {
		display: inline-block;
		width: 20%;
		height: 50rpx;
		line-height: 50rpx;
		text-align: center;
		padding: 10px 0;
	}

	.from_fee {
		padding: 10rpx 10rpx 20rpx;
		font-size: 26rpx;
		background: white;
		border-radius: 30rpx;
		margin: 30rpx 20rpx;
	}

	.fee_text {
		font-size: 26rpx;
		color: #888888;
	}
</style>