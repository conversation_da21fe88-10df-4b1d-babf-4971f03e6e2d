<template>
	<view class="page-carnumber-test">
	<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">
				<view class="location">
					<view class="" style="display: flex;justify-content: center;align-items: center;font-size: 32rpx;
										
										font-weight: 400;
										color: #000000;">
						<image style="width: 30rpx;height: 37rpx;margin-right: 8rpx;"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%AE%9A%E4%BD%8D%402x.png">
						</image>  {{storeName}}
					</view>
				</view>
			</block>
		</cu-custom>
		<image src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%AF%BB%E8%BD%A6.png"
			style="width: 100%;height:180rpx;z-index: -1;"></image>
		<view class="contents">
			<view class="tabs">
				<view :class="[index==1?'tabtexts':'tabtext']" @click="parkserch(1)">
					车牌查询
				</view>
				<view @click="parkserch(2)" :class="[index==2?'tabtexts':'tabtext']">
					时间查询
				</view>
			</view>

			<view v-if="index==1">
				<view class="text_park">
					-请填写您的车牌号-
				</view>
				<car-number v-model="carNumber" ref="carNumber"></car-number>
				<view class="">
					<view v-if="getcarlists.length" style="display: flex;flex-wrap: wrap;justify-content: space-between;margin-left: 20rpx;margin-right: 20rpx;margin-top: 20rpx">
						<view class="box-car" v-for="(itemMsg, indexMsg) in getcarlists" :key="indexMsg">
							<view style="display: flex;">
								<view @click="putcarno(itemMsg)">
									{{ itemMsg.plateNo|plateNo }}
								</view>
								<view style="border:0.5px  dashed;
									height: 36rpx;
									margin-top:4rpx;
									margin-left: 44rpx;
									margin-right:30rpx;">
					
								</view>
								<view style="width: 40rpx;height: 40rpx;" @click="affifmdel(itemMsg)">
									<image style="width: 40rpx;height: 40rpx;"
										src="https://slshop-file.oss-cn-beijing.aliyuncs.com/newcar/%E5%9B%BE%E5%B1%82%2010.png"
										mode=""></image>
								</view>
							</view>
					
						</view>
					
					</view>
					
				</view>
				<view class="serch-park" @click="search_park">
					<image style="height:93rpx;"
						src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E6%9F%A5%E8%AF%A2.png" mode="heightFix">
					</image>
				</view>
			</view>
			<view class="timeselect" v-else>
				<view  style="margin-top:30rpx;margin-left: 60rpx;">
					注：若您在<text style="color: #9C7454;">10点至11点之间</text>停的车，请选择 <text style="color:#9C7454">10</text>。
				</view>
				<view class="mumber">
					<view :class="[index==ind?'every_mumbers':'every_mumber']" v-for="(item,index) in 24" @click="comfimtime(item,index)">
						{{item}}
					</view>
				</view>
				<view class="serch-park" @click="inquerabout">
					<image style="height:93rpx;"
						src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E6%9F%A5%E8%AF%A2.png" mode="heightFix">
					</image>
				</view>
			</view>
		</view>
		<view class="serch-park" @click="backhome">
			<image style="height:93rpx;" mode="heightFix"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%8E%BB%E6%94%AF%E4%BB%98%20%E6%8B%B7%E8%B4%9D.png">
			</image>
		</view>
	    <QSpopup ref="QSpopup" :delbol='delbol'></QSpopup>
		<view class="">
			<n-transition ref="pop" speed="ease-in-out" :height="900" :maskVal="0.5">
				<view class="showpop">
					<view class="plateNo" style="font-size: 40rpx;
					                            padding-bottom: 20rpx;
												font-family: PingFang SC;
												font-weight: 800;
												color: #000000;
												display: flex;
												justify-content: center;border-bottom: 1px dashed;
												">
						{{datas.plateNo|plateNo}}
					</view>
					<view style="color: #888888;display: flex;justify-content: center;margin-top: 20rpx;">
						  该车位于
					</view>
					<view  style="display: flex;justify-content: center;">
						  <text style="font-size:40rpx;color: #CE9E75;margin-right: 30rpx;">{{datas.floorCode}}</text> <text style="font-size: 40rpx;">{{datas.spaceNo}}号车位</text>
					</view>
					<view style="margin-top: 20rpx;">
						<swiper  autoplay :interval="3000" circular>
							<template v-for="(item, index) in 	datas.carPics">
								<swiper-item :key="index" 
									style="display: flex;justify-content: center;">
									<image  :src="item" style="width: 100%;height: 300rpx;margin-left: 20rpx;margin-right: 20rpx;"></image>																					
								</swiper-item>
							</template>
						</swiper>
					</view>
					 <view style="margin-top: 20rpx;">
					 	<text style="color:#888888;font-size: 32rpx;margin-right: 20rpx;margin-left: 15rpx;">进场时间:</text> <text style="color: #000000;font-size: 32rpx;">{{datas.enterTime}}</text>
					 </view>
					 <view>
					 	<text style="color:#888888;font-size: 32rpx;margin-right: 20rpx;margin-left: 15rpx;">已停时间:</text> <text style="color: #000000;font-size: 32rpx;" v-if="datas.parkMinutes>60">{{toHourMinute(datas.parkMinutes)}}</text> <text style="color: #000000;font-size: 32rpx;" v-else>{{datas.parkMinutes}}分钟</text>
					 </view>
					 <view style="display: flex;justify-content: center;margin-top: 60rpx;border-top: 1px dashed;" @click="comfim">
					 	  <image mode="heightFix" style="text-align: center;margin-top: 40rpx;height: 90rpx;" src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E7%A1%AE%E5%AE%9A.png"></image>
					 </view>
				</view>
			</n-transition>
		</view>
	</view>
</template>

<script>
	import QSpopup from "../component/QS-popup/QS-popup.vue";
	import nTransition from '../component/n-transition/n-transition.vue'
	import api from 'utils/api'
	import __config from 'config/env';
	var qqmapsdk
	import CarNumber from "../component/codecook-carnumber/codecook-carnumber.vue";
	import QQMapWX from 'public/jweixin-module/qqmap-wx-jssdk.min.js';
	import tSlide from "../component/t-slide/t-slide.vue"
	// import slide from "../component/t-slide/"
	const app = getApp();
	export default {
		components: {
			CarNumber,
			nTransition,
			tSlide,
			QSpopup
		},
		data() {
			return {
				carNumber: '',
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				carlist: '',
				index: 1,
				datas: '',
				ind:null,
				store:'',
				storeName:'',
				getcarlists:'',
				sotreparkdata:'',
				delbol:false
			};
		},
		watch: {
			carNumber(num) {
				console.log(num);
			},
		},
		filters: {
			plateNo: function(value) {
				if(value){
				return value.substring(0, 2) + '·'+ value.substring(2, value.length)
			}
			},
		
		},
	onLoad(options) {
		if (options.store) {
			this.store = options.store;
			this.storeName = decodeURIComponent(options.storeName);
			this.getcarlist()
		}
	},
		methods: {
			delpark(data) {
				api.delcar({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid||uni.getStorageSync('user_info').openId,
					"plateNo": this.sotreparkdata.plateNo,
					"plateType": this.sotreparkdata.plateType
				}).then(res => {
					console.log(res.data == 'OK')
					if (res.data == 'OK') {
						// uni.showToast({
						// 	title: '解绑成功',
						// 	icon: 'success',
						// 	duration: 2000
						// });
						this.$refs.QSpopup.hide()
						this.getcarlist()
					}
				})
			},
			affifmdel(data) {
				let that = this
				this.sotreparkdata = data
				this.delbol =true
				this.$nextTick(function(item){
				that.$refs.QSpopup.shows()
				})
			},
			putcarno(val) {
				this.$refs.carNumber.fill = val.plateNo.split('').length == '7' ? val.plateNo.split('').concat('') : val
					.plateNo.split('')
			},
			//获取绑定的车牌
			getcarlist() {
				api.getcarlist({
					"channel": "SONGSHU",
					"custId": uni.getStorageSync('user_info').erpCid||uni.getStorageSync('user_info').openId,
					"openId": uni.getStorageSync('user_info').openId||uni.getSystemInfoSync().deviceId,
					"plateType": ""
				}).then(res => {
					if (res.code == '0') {
						// this.itemList.body = res.data.dataList
						this.getcarlists = res.data.dataList
						// this.$set(itemList,body,res.data.dataList)
					}
				})
			},
			toHourMinute(minutes){
			  return (Math.floor(minutes/60) + "小时" + (minutes%60) + "分" )
			},
			backhome(){
			   uni.navigateTo({
			   	url:'/pages/parkinglot/parking-home/index'
			   })	
			},
			comfim() {
			 		this.$refs['pop'].hide()
			},
			search_park() {
				let that = this
				if(!this.carNumber){
					uni.showToast({
						title:'请输入车牌号',
						icon:'none'
					})
					}else{
				api.carinfo({
					"channel": "SONGSHU",
					"store": this.store,
					"plateNo": this.carNumber
				}).then(res => {
					if(res.data.dataList&&res.data.dataList.length){
						this.$refs.carNumber.keyHideHandler()
						that.datas = res.data.dataList[0]
						// this.$nextTick(fucntion(){
							that.$refs['pop'].show()
						// })
					}else{
						this.$refs.carNumber.keyHideHandler()
						uni.showToast({
							title:'暂无数据',
							icon:'none'
						})
					}
				
				})
				}
			},
			parkserch(index) {
				this.index = index

			},
			comfimtime(item,index) {
				this.ind = index
				console.log(item)
				this.itemNum = item


			},
			inquerabout(){
				if(!this.itemNum){
					uni.showToast({
						title:'请示选择时间',
						icon:'none'
					})
				}else{
				uni.navigateTo({
					url:`/pages/parkinglot/searchtime-park/searchtime-park?time=${this.itemNum}&store=${this.store}&storeName=${encodeURIComponent(this.storeName)}`
				})
				}
			}
		},
		onShow() {
			// 实例化API核心类
			// qqmapsdk = new QQMapWX({
			// 	key: __config.mapKey
			// });
			let that = this
			// this.$refs['pop'].hide()
		},
	};
</script>

<style scoped lang="less">
	v::deep .slide {
		width: 100% !important;
	}

	.please-Car-number {
		padding-top: 20rpx;
		font-size: 38rpx;
	}

	.content {
		background: white;
		padding-left: 20rpx;
		padding-right: 20rpx;
		padding-bottom: 20rpx;
	}

	.please-store {
		margin-top: 30rpx;
	}

	.please-binding-car {
		margin-top: 20rpx;
	}

	.Memberexclusive {
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		padding-left: 20rpx;
		padding-right: 20rpx;
		background: white;
		height: 100rpx;
		align-items: center;
		border: 1px solid red;
	}

	.every-vip {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: white;
	}

	.timeselect {
		display: flex;
		flex-direction: column;
	}
	.box-car {
		// margin-right: 15rpx;
		// padding-bottom: 10rpx;
		background: #F4F4F4;
		width: 318rpx;
		height: 70rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		// justify-content: space-between;
		margin-top: 15rpx;
	}

	.tc-vip {
		display: flex;
		justify-content: space-between;
		background: white;
		padding-left: 20rpx;
		padding-right: 20rpx;
		height: 138rpx;
		border: 1px solid yellow;
		align-items: center;
	}

	.number-vip {
		font-size: 46rpx;
	}

	.text-vip {
		font-size: 28rpx;
	}

	.tc-vip-tab {
		display: flex;
		justify-content: space-between;
		background: white;
		padding-left: 20rpx;
		timeselect padding-right: 20rpx;
		height: 168rpx;
		border: 1px solid yellow;
		align-items: center;
		margin-top: 20rpx;

	}

	.location {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80rpx;
	}

	.tabs {
		display: flex;
		justify-content: space-around;
	}

	.tabtexts {
		font-size: 30rpx;
		color: #CE9E75;
		margin-top: 30rpx;
	}

	.tabtext {
		font-size: 30rpx;
		margin-top: 30rpx;
	}

	.contents {
		border-radius: 50rpx;
		background: white;
		margin-left: 20rpx;
		margin-right: 20rpx;
		margin-top: -97rpx;

	}

	.serch-park {
		display: flex;
		justify-content: center;
		margin-top: 40rpx;
		padding-bottom: 30rpx;
	}

	.text_park {
		text-align: center;
		margin-top: 50rpx;
		color: #000000;
	}

	.mumber {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		margin-top: 30rpx;
		padding-left: 10rpx;
		padding-right: 10rpx;
		padding-bottom: 30rpx;
		margin-left: 20rpx;
	}

	.mumberlist {
		display: flex;
	}

	.every_mumber {
		width: 81rpx;
		height: 81rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #FCF6F1;
		border-radius: 12rpx;
		margin-right: 20rpx;
		margin-top: 10rpx;
		color: #9C7454;

	}
	.every_mumbers {
		width: 81rpx;
		height: 81rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #FCF6F1;
		border-radius: 12rpx;
		margin-right: 20rpx;
		margin-top: 10rpx;
		background: #C6A080;;
	
	}
	
	.showpop{
		padding-left: 40rpx;
		padding-right: 40rpx;
		margin-top: 20rpx;
		
	}
	
	.location {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80rpx;
	}
</style>
