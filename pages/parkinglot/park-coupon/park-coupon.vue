<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">停车优惠劵</block>
		</cu-custom>
		<view>
				<view>
					<view class="fixed pos">
						<view class="scroll-view-item_H" v-for="(tab,index) in tabBars" :key="tab.id" :id="tab.id"
							:class="navIndex==index ? 'activite' : ''" @click="checkIndex(index)">{{tab.name}}</view>
					</view>
					<!-- 内容切换 -->
					<view class="content" v-if="couponlist.length">
						<view v-for="(item,index) in couponlist" :key="index">
							<view @click="getdetailcoupon(item)" style="display: flex;height: 190rpx;margin-bottom: 20rpx;">
								<view style="width: 228rpx;position: relative;z-index: 999;">
									<view style="position: absolute;
												margin-left: -41rpx;
												left: 50%;
												top: 50%;
												margin-top: -52rpx;
		                                       ">
										<view style="position: absolute;color: white;z-index: 99;height: 100%;top: 26rpx;right: 10rpx;">
											¥
										</view>
										<view
											style="position: absolute;color: white;font-weight: 700;font-size: 51rpx;z-index: 99;">
											{{item.balance}}
										</view>
									</view>
									<image style="height: 190rpx;"
										src="https://slshop-file.oss-cn-beijing.aliyuncs.com/coupon/discount.png">
									</image>
								</view>
								<view
									style="border: 1px solid #d7b9a1;padding: 20rpx;font-weight: 500;font-size: 34rpx;border-radius: 20rpx;margin-left: 2rpx;">
									<view>
										{{item.typeName}}
									</view>
									<view style="font-size: 30rpx;">
										{{item.expDate}} - {{item.effDate}}
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="content" v-else>
						<view
							style="display: flex;align-items: center;justify-self: center;flex-direction: column;margin-top: 100rpx;">
							<image src="https://img.songlei.com/live/nodata.png" mode="" style="width: 200rpx;height: 134rpx;">
							</image>
							<view style="margin-top: 20rpx;color: #8799A3;">
								没有相关优惠劵
							</view>
						</view>
					</view>
				</view>
				<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
		
			</view>
		
	</view>
</template>

<script>
	import api from 'utils/api'
	export default {
		props:{
			custId:{
				type:String,
			},
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: getApp().globalData.theme, //全局颜色变量
				navIndex: 0,
				tabBars: [{
					name: '未使用',
					id: '0'
				}, {
					name: '已使用',
					id: '02'
				}, {
					name: '已失效',
					id: '03'
				}],
				couponlist: [],
				pageNo: 1,
				loadmore: false,
				rowsCount: '',
				custIds:''

			}
		},
		onReachBottom() {
			// if (this.PageCur == '2') {
			// 	this.$refs.goodlist && this.$refs.goodlist.reachBottom();
			// } else if (this.PageCur == '4') {
			// 	this.$refs.shopActive && this.$refs.shopActive.reachBottom();
			// }
			if (this.rowsCount > this.couponlist.length) {
				this.pageNo = this.pageNo + 1
				this.getcoupon()
				this.loadmore = true
			}
		},
		onPullDownRefresh() {
			this.couponlist = []
			this.pageNo = 1
			this.getcoupon()
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		methods: {
			checkIndex(index) {
				console.log(index)
				this.navIndex = index;
				this.getcoupon()
				this.couponlist = []
			},
			scroll: function(e) {
				console.log(e)
				this.old.scrollTop = e.detail.scrollTop
			},
			//  获取优惠劵THI
			getcoupon() {
				api.getlist({
					"channel": "SONGSHU",
					"custId": this.custId,
					"openId": uni.getStorageSync('user_info').openId||uni.getSystemInfoSync().deviceId,
					"pageNo": this.pageNo,
					"pageSize": "10",
					"status": this.tabBars[this.navIndex].id
				}).then(res => {
					if (res.data.custAccnts) {
						// this.couponlist = res.data.custAccnts
						this.couponlist = Array.from(new Set([...res.data.custAccnts, ...this.couponlist]))
						// this.loadmore = true/
					}
					this.rowsCount = res.data.rowsCount
					if (this.couponlist.length >= res.data.rowsCount) {
						this.loadmore = false;
					}
				})
			},
			// 优惠劵详情
			getdetailcoupon(item) {
				uni.navigateTo({
					url: ""
				})

			}
		},
		//获取custid
		mounted() {
		   // if(this.custId){
     //       this.custIds = this.custId
		   // this.getcoupon()
		   // console.log(this.custId,'custId---')
		   // }
		},
		onLoad(options) {
			if (options.custId) {
				this.custId = options.custId
				this.getcoupon()
			}
		}
	}
</script>

<style scoped>
	.activite {
		border-bottom: 1px solid #CDA185;
		color: #CDA185 !important;
	}

	.content {
		/* background: #008000; */
		/* height: 300px; */
		padding: 20rpx;
		/* margin-top: 120rpx; */
		padding-top: 140rpx;
	}

	.scroll-view_H {
		border-bottom: 1px solid #e2e2e2;
		white-space: nowrap;
		width: 100%;
		color: #CCCCCC;
		position: fixed;
		top: 180rpx;
		z-index: 9999;
		background: white;
	}

	.scroll-view-item_H {
		border-bottom: 1px solid #e2e2e2;
		background: white;
		display: inline-block;
		width: 33.3%;
		height: 93rpx;
		line-height: 50rpx;
		text-align: center;
		padding: 10px 0;
		color: black;

		/* 	position: fixed;
		top: 120rpx; */
	}

	.fixed {
		position: fixed;
		/* margin-top: 120rpx; */
		z-index: 99999;
		width: 100%;
	}
</style>
