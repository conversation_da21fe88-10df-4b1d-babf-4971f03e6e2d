<template>
	<view>
		<view @touchmove.stop.prevent="voidFc" @tap.stop="maskHide" class="fadeIn_mask" :class="showBl?'show':'hide'">
		</view>
		<view @touchmove.stop.prevent="voidFc" :class="[('QS_' + type), showBl?'show':'hide']">
			<view class="" v-if="delbol">
				<view class="content">
					<view class="" style="display: flex;justify-content: center;padding-top: 40rpx;">
						<image style="width:50rpx;height: 50rpx;margin-right: 10rpx;"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%9B%BE%E5%B1%82%202%20(1).png"
							mode=""></image>
						<view class="" style="font-size: 35rpx;
													font-family: PingFang SC;
													font-weight: 500;
													color: #6B6B6B;">
							是否删除所选车牌
						</view>
					</view>
					<view class="" style="display: flex;justify-content: center;margin-top: 60rpx;">
						<view class="" @click="delparnum">
							<image style="width: 235rpx;height: 76rpx;margin-right: 40rpx;"
								src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E7%A1%AE%E5%AE%9A%20(1).png"
								mode=""></image>
						</view>
						<view class="" @click="hide">
							<image style="width: 235rpx;height: 76rpx;"
								src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%8F%96%E6%B6%88.png"
								mode=""></image>
						</view>

					</view>
					<view class=""
						style="border: 1px dashed #e7e7e7;margin-left: 30rpx;margin-right: 30rpx;margin-top: 50rpx;">

					</view>
					<view style="display: flex;justify-content: center;
					margin-top: 35rpx;color: #A5A5A5;">
						注：删除后可点击【绑定车牌】重新绑定。
					</view>
				</view>
			</view>
			<view v-else style="position: relative;">
				<image style="width: 600rpx;  height: 636rpx;"
					src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%9B%BE%E5%B1%82%2011%402x.png" mode="">
				</image>
				<view class="" style="position: absolute;top: 226rpx;width: 100%;">
					<view class=""
						style="font-size: 32rpx;text-align: center;font-family: PingFang SC;font-weight: 500;color: #000000;">
						当前车辆
					</view>
					<view class=""
						style="width: 531rpx;margin: 0 auto;height: 75rpx;border-radius: 38px;background: #F0F0F0;">
						<view class=""
							style="font-size: 48rpx;font-family: PingFang SC;font-weight: 800;color: #000000;text-align: center;margin-top: 10rpx;display: flex;justify-content: center;">
							<view v-for="(item,index) in car" :key="index" style="margin-right: 6rpx;">
								{{item}}
							</view>
						</view>
						<view class="" style="text-align: center;margin-top: 45rpx;font-size: 36rpx;">
							{{txt?txt:'不需要缴费哦~'}}
						</view>
						<view class="" @click="hide" style="margin: 0 auto;width: 467rpx;font-size: 32rpx;line-height: 82rpx;text-align: center;
																		font-family: PingFang SC;
																		font-weight: 500;
																		color: #000000;
																		height: 82rpx;
																		background: linear-gradient(0deg, #DBC1AC 0%, #DABFAA 100%);
																		border-radius: 38px;margin-top: 20rpx;">
							确定
						</view>
					</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			type: {
				type: String,
				default: 'fadeInMiddle'
			},
			tapMaskHide: {
				type: Boolean,
				default: true
			},
			delbol: {
				type: Boolean,
				default: false

			}
		},
		data() {
			return {
				showBl: false,
				car: '',
				txt: '',
				redirectUrl: '',
				navigatorType: '', //reTo需要用redirectTo  和 to需要 
			}
		},
		methods: {
			delparnum() {
				this.$parent.delpark()
			},
			show() {
				this.showBl = true;
			},
			maskHide() {
				if (this.tapMaskHide) this.hide();
			},
			hide() {
				this.showBl = false;
				let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
				let preRoute = '';
				if (routes.length > 1) {
					preRoute = routes[routes.length - 2].route;
				}
				if (this.redirectUrl) {
					console.log("====this.redirectUrl=======",this.redirectUrl)
					console.log("===curRoute=======",preRoute)
					console.log("===this.redirectUrl.indexOf(curRoute)=======",this.redirectUrl.indexOf(preRoute))
					if (this.redirectUrl.indexOf(preRoute) > 0) {
						uni.navigateBack({
							delta: 1
						});
						return
					}
					if (this.navigatorType == 'reTo') {
						uni.redirectTo({
							url: this.redirectUrl
						})
					} else {
						uni.navigateTo({
							url: this.redirectUrl
						})
					}
				}
			},
			voidFc() {},
			shows(val, txt, redirectUrl, navigatorType) {
				this.navigatorType = navigatorType;
				console.log(txt)
				this.car = val || '无牌车';
				this.showBl = true;
				this.txt = txt;
				this.redirectUrl = redirectUrl;
			}
		}
	}
</script>

<style scoped>
	@import url("css/QS-popup.css");

	.content {
		margin-left: 50rpx;
		margin-right: 50rpx;
		background: white;
		width: 600rpx;
		height: 400rpx;
		border-radius: 30rpx;
	}
</style>