<template>
	<view>
		<view class="nav">
			<view class="nav-left">
				<scroll-view scroll-y :style="'height:'+height+'px'">
					<view class="nav-left-item" v-for="(item,index) in categoryList" @click="categoryClickMain(item,index)" :key="index"
					 :style="index==categoryActive?'color:'+activeTextColor+';background-color:'+activeBackgroundColor:''">
						{{item[label]}}
					</view>
				</scroll-view>
			</view>
			<view class="nav-right">
				<scroll-view scroll-y :scroll-top="scrollTop" @scroll="scroll" :style="'height:'+height+'px'" scroll-with-animation>
					<view class="nav-right-item" v-for="(item,index2) in subCategoryList" :key="index2" @click="categoryClickSub(item)">
						<view class="" style="font-size: 28rpx;font-weight: 800;">{{item.storeName}}</view>
						<view class="" style="color: #8e8e8e;
    font-size: 25rpx;
    margin-top: 10rpx;">
							{{item.address}}
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "category",
		data() {
			return {
				height: 0,
				scrollTop: 0,
				scrollHeight: 0,
				categoryActive: 0,
				activeStyle: {
					color: this.activeTextColor,
					backgroundColor: this.activeBackgroundColor
				}
			}
		},
		props: {
			//主分类激活索引
			defaultActive: {
				type: Number,
				default: 0
			},
			//主分类List
			categoryList: {
				type: Array,
				default: () => {
					return null;
				}
			},
			//侧边分类List
			subCategoryList: {
				type: Array,
				default: () => {
					return null;
				}
			},
			activeTextColor: {
				type: String,
				default: '#CDA185'
			},
			activeBackgroundColor: {
				type: String,
			},
			label: {
				type: String,
				default: 'name'
			},
			imgSrc: {
				type: String,
				default: 'logo'
			},
			//主分类点击事件
			categoryMainClick: {},
			//子分类点击事件
			categorySubClick: {}
		},
		methods: {
			scroll(e) {
				this.scrollHeight = e.detail.scrollHeight;
			},
			categoryClickMain(category, index) {
				this.$emit('categoryMainClick',category)
				this.categoryActive = index;
				this.scrollTop = -this.scrollHeight * index;
			},
			categoryClickSub(category) {
				this.$emit('categorySubClick',category)
			},
			getGreatCircleDistance(lat1, lng1, lat2, lng2) {
					var EARTH_RADIUS = 6378137.0;
					var PI = Math.PI;
			
					function getRad(d) {
						return d * PI / 180.0;
					}
					var radLat1 = getRad(lat1);
					var radLat2 = getRad(lat2);
			
					var a = radLat1 - radLat2;
					var b = getRad(lng1) - getRad(lng2);
			
					var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) *
						Math.pow(Math.sin(b / 2), 2)));
					s = s * EARTH_RADIUS;
					s = Math.round(s * 10000) / 10000.0;
			
					return s; // 公里数
				}
		},
		mounted() {
			this.categoryActive = this.defaultActive
			uni.getSystemInfo({
				success: res => {
					this.height = res.screenHeight;
				}
			})
		},
		watch: {
			subCategoryList(newValue, oldValue) {
				
			}
		},
	}
</script>

<style scoped>
	.nav {
		display: flex;
		width: 100%;
		background: white;
	}

	.nav-left {
		width: 30%;
	}

	.nav-left-item {
		height: 50px;
		border-right: solid 1px #E0E0E0;
		border-bottom: solid 1px #E0E0E0;
		font-size: 18px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.nav-right {
		width: 70%;
		/* padding-top: 11px */
		border: 1px solid #E0E0E0;
		
	}

	.nav-right-item {
		/* width: 28%; */
		/* height: 100px; */
		/* line-height: 100px; */
		float: left;
		/* text-align: center; */
		padding: 5px;
		font-size: 13px;
		border-bottom: 1px solid #E0E0E0;
        width: 100%;
		padding-left: 20rpx;
	}

	.nav-right-item image {
		width: 50px;
		height: 50px;
	}

	.active {
		color: #CDA185;
	}

	.padding {
		height: var(--status-bar-height);
		width: 100%;
		top: 0;
		position: fixed;
		background-color: #CDA185;
	}
</style>
