<template>
	<view class="page">
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">停车场介绍</block>
		</cu-custom>
		<image src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%9B%BE%E5%B1%82%201%20(2).png"
			style="height: 180rpx;width: 100%;z-index: -1;" mode=""></image>
		<view style="display: flex;justify-content: center;align-items: center;">
			<image mode="widthFix" :src="parkFeePic" style="width: 100%;margin-left: 20rpx;margin-right: 20rpx;margin-top: -160rpx;
     "></image>
		</view>
	</view>
</template>

<script>
	import api from 'utils/api'
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: getApp().globalData.theme, //全局颜色变量
				parkFeePic: ''
			}
		},
		onLoad(options) {
			if (options.img) {
				this.parkFeePic = options.img
			}
		}
	}
</script>

<style scoped>
	page {
		background: #FFFFFF;
	}

</style>