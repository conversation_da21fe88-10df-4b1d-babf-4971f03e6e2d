<template>

   	<view>
   		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
   			<block slot="backText">返回</block>
   			<block slot="content">停车场</block>
   		</cu-custom>
   		<category :categoryList="categoryList" :subCategoryList="subCategoryList"
   			@categoryMainClick="categoryMainClick" @categorySubClick="categorySubClick"></category>
   	</view>
   </template>
   <script>
   	import api from 'utils/api'
   	const app = getApp();
   	import category from "../component/qiyue-category/qiyue-category.vue"
   	export default {
   		components: {
   			category
   		},
   		data() {
   			return {
   				categoryList: [],
   				subCategoryList: [],
   				carNumber: "渝AT9999",
   				CustomBar: this.CustomBar,
   				theme: app.globalData.theme, //全局颜色变量
   			}
   		},

   		methods: {
   			categoryMainClick(category) {
   				this.subCategoryList = category.subCategoryList;
   			},
   			categorySubClick(category) {
					// 获取当前页面栈
					const pages = getCurrentPages();
					// 判断是否有上一页
					if (pages.length > 1) {
							// 获取上一页的实例
							const previousPage = pages[pages.length - 2];
							// 获取上一页的路径
							const previousPagePath = previousPage.route; // 获取路径
							console.log('上一页路径:', previousPagePath);
							uni.reLaunch({
								url:`/${previousPagePath}?item=${JSON.stringify(category)}`
							})
					} else {
							console.log('没有上一页');
					}
   			},
   			getGreatCircleDistance(lat1, lng1, lat2, lng2) {
   				var EARTH_RADIUS = 6378137.0;
   				var PI = Math.PI;

   				function getRad(d) {
   					return d * PI / 180.0;
   				}
   				var radLat1 = getRad(lat1);
   				var radLat2 = getRad(lat2);

   				var a = radLat1 - radLat2;
   				var b = getRad(lng1) - getRad(lng2);

   				var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) *
   					Math.pow(Math.sin(b / 2), 2)));
   				s = s * EARTH_RADIUS;
   				s = Math.round(s * 10000) / 10000.0;

   				return s; // 公里数
   			}
   		},
   		mounted() {
   			let that = this
   			api.getstorelist().then(res => {
   				console.log(res.data, 'res.data');
				const fatherCategory= [];
				if(res.data&&res.data.length>0){
					res.data.forEach(item=>{
						if(fatherCategory.indexOf(item.cityName)==-1){
							fatherCategory.push(item.cityName);
						}
					})
					
					fatherCategory.forEach(fatherCategoryItem=>{
						const subCategoryList = [];
						res.data.forEach(item=>{
							if(fatherCategoryItem==item.cityName){
								subCategoryList.push(item)
							}
						})
						that.categoryList.push({
							"name": fatherCategoryItem,
							"subCategoryList": subCategoryList
						})
					})
				}
   				//默认选中第一个
   				that.subCategoryList = that.categoryList[0].subCategoryList;
   			})
   		}
   	}
   </script>
