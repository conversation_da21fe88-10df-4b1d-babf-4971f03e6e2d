<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">
				<view class="location">
					<view style="display: flex;justify-content: center;align-items: center;font-size: 32rpx;
									
									font-weight: 400;
									color: #000000;">
						<image style="width: 30rpx;height: 37rpx;margin-right: 8rpx;"
							src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%AE%9A%E4%BD%8D%402x.png">
						</image> {{storeName}}
					</view>
				</view>
			</block>
		</cu-custom>

		<image src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E7%BB%84%202.png" style="width: 100%;
        height: 185rpx;z-index: -1;" mode=""></image>
		<view class="emptypark">
			<view class="left_empty">
				当前空位：<text
					style="font-size: 50rpx;font-family: PingFang SC;font-weight: bold;color: #9F7A5E;">{{listpark.free}}</text>
			</view>
			<view class="" style="color: #888888;">
				下拉刷新空位数
			</view>
		</view>

		<view class="emptyparktwo" v-if="listpark.parks&&listpark.parks.length>0">
			<view v-for="(parkItem,parkIndex) in listpark.parks" style="width:100%;">
				<view :class="[index== listpark.parks.length-1?'emptyparktwotree':'emptyparktwtow']">
					<view class="left_empty" style="color: #888888;">
						<text style="color: #000000;font-size: 40rpx;margin-left: 10rpx;">{{parkItem.parkName}}</text>
					</view>
					<view style="color: #888888;" class="flex align-center">
						空车位：<view style="color: #000000;font-size: 40rpx;width:90rpx;">{{parkItem.free}}</view>
					</view>
				</view>
				<!-- 停车点的楼层数大于1才分层显示 -->
				<template v-if="listpark.parks[parkIndex].floors&&listpark.parks[parkIndex].floors.length>1">
					<view :class="[index== listpark.parks[parkIndex].floors.length-1?'emptyparktwotree':'emptyparktwtow']"
						v-for="(item,index) in listpark.parks[parkIndex].floors" :key="index" class="floorItem">
						<view class="left_empty" style="color: #888888;">
							<text style="color: #000000;font-size: 34rpx;margin-left: 10rpx;">{{item.floorName}}</text>
						</view>
						<view style="color: #888888;"  class="flex align-center">
							空车位：<view style="color: #000000;font-size: 34rpx;width:90rpx;">{{item.free}}</view>
						</view>
					</view>
				</template>
				
			</view>

		</view>
		<view class="advertisement" @click="getstandard">
			<image style="width: 50rpx;height: 50rpx;margin-right: 20rpx"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/qingdan%20%E6%B8%85%E5%8D%95%20%E6%8B%B7%E8%B4%9D%202.png"
				mode=""></image>
			<view class="">
				停车场收费标准是什么？快来看看介绍吧！
			</view>
		</view>
		<view style="display: flex;
    justify-content: center;" @click="parkback">
			<image mode="heightFix" style="height: 90rpx;margin-top: 50rpx;"
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/car/%E5%8E%BB%E6%94%AF%E4%BB%98%20%E6%8B%B7%E8%B4%9D.png">
			</image>
		</view>
	</view>
</template>

<script>
	import api from 'utils/api'
	const app = getApp();
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				pageNum: 1,
				pageSize: 10,
				listpark: "",
				store: "",
				storeName: ''
			}
		},

		onLoad(options) {
			if (options.store) {
				this.store = options.store;
				this.storeName = decodeURIComponent(options.storeName);
				this.getnolate()

			}
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			this.getnolate()
			uni.showNavigationBarLoading();

			uni.hideNavigationBarLoading(); // 停止下拉动作

			uni.stopPullDownRefresh();
		},
		methods: {
			getstandard() {
				uni.navigateTo({
					url: `/pages/parkinglot/charging-standard/charging-standard`
				})
			},
			parkback() {

				uni.navigateBack({
					delta: 1
				})
			},
			getnolate() {
				api.noelate({
					"channel": "SONGSHU",
					"store": this.store,
				}).then(res => {
					this.listpark = res.data
				})
			}
		},
	}
</script>

<style scoped>
	.emptypark {
		background: white;
		border-radius: 30rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		display: flex;
		justify-content: space-around;
		height: 130rpx;
		align-items: center;
		margin-top: -107rpx;
	}

	.emptyparktwo {
		background: white;
		border-radius: 30rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		display: flex;
		justify-content: space-around;
		align-items: center;
		margin-top: 30rpx;
		padding-left: 80rpx;
		padding-right: 80rpx;
		flex-direction: column;
	}

	.emptyparktwoone {
		display: flex;
		justify-content: space-between;
		width: 100%;
		border-bottom: 1px dashed;
		height: 100rpx;
		align-items: center;

	}

	.emptyparktwtow {
		display: flex;
		justify-content: space-between;
		width: 100%;
		border-bottom: 1px dashed #e7e7e7;
		height: 140rpx;
		align-items: center;
	}

	.emptyparktwotree {
		display: flex;
		justify-content: space-between;
		width: 100%;
		/* border-bottom: 1px dashed; */
		height: 140rpx;
		align-items: center;
	}

	.advertisement {
		height: 86rpx;
		background: white;
		border-radius: 20rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		margin-top: 26rpx;

	}

	.location {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80rpx;
	}

	.floorItem {
		padding: 0 40rpx;
	}
</style>