<template>
  <view>
    <view>
      <!-- 开屏展示 -->
      <view
        @click="handleOpenScreen(openScreen.linkUrl)"
        v-if="scene != 1154 && openScreen && openScreen.imgUrl && showScreen"
        :style="{
          position: 'absolute',
          left: 0,
          bottom: 0,
          top: 0,
          right: 0,
          backgroundColor: screenBgColor ? screenBgColor : '#FFFFFF',
          height: '100%',
          overflow: 'hidden',
          zIndex: '10001',
        }"
      >
        <image
          :src="openScreen.imgUrl"
          @error="handleImageError"
          mode="widthFix"
          :style="{ overflow: 'hidden', width: '750rpx', height: '100vh' }"
        >
        </image>
        <view
          class="text-sm"
          style="
            position: absolute;
            bottom: 70px;
            right: 28rpx;
            width: 140rpx;
            background-color: rgba(0, 0, 0, 0.5);
            color: #ffffff;
            border-radius: 40rpx;
            padding: 14rpx 0;
            text-align: center;
          "
          @click.stop="handleSkip"
          >跳过{{ times > -1 ? times : "" }}s
        </view>
      </view>
      <view
        v-if="scene != 1154"
        :style="{
          height: showScreen ? windowHeight + 'px' : 'auto',
          overflow: showScreen ? 'hidden' : 'scroll',
          paddingBottom: '110rpx',
        }"
      >
        <divComponents
          :showScreen="showScreen"
          ref="divComponents"
          :microId="pagePathId"
          :divType="pageState"
          :isBack="false"
          :dialogIsShowing="dialogIsShowing"
          :showMinutes="showMinutes"
        />
        <redPackage></redPackage>
      </view>
      <share-single-page v-if="scene == 1154" />
    </view>
    <!-- #ifdef APP -->
    <!-- <custom-tab-bar v-if="scene!=1154"  ref="customTabBar" /> -->
    <!-- #endif -->
  </view>
</template>

<script>
import divComponents from "@/components/div-components/index.vue";
import shareSinglePage from "@/components/share-single-page/index.vue";
import redPackage from "@/components/red-package/index.vue";
import { gotoPage } from "@/components/div-components/div-base/div-page-urls.js";
import { EventBus } from "@/utils/eventBus.js";
const util = require("utils/util.js");
const app = getApp();
import api from "utils/api";
// #ifdef APP-PLUS
import { startPush, setPushAlias } from "@/api/push";
// #endif

export default {
  components: {
    divComponents,
    shareSinglePage,
    redPackage,
  },
  data() {
    return {
      pagePathId: null, //微页面id
      pageState: "",
      num: 0,
      scene: "",
      openScreen: {},
      showScreen: app.globalData.showOpenScreen,
      //开屏广告倒计时
      times: "",
      // 开屏广告展示多长时间
      showMinutes: -1,
      //开屏广告背景色
      screenBgColor: "",
      intervalObj: null,
      timeOutObj: null,
      CustomBar: this.CustomBar,
      isFirst: true,
      windowHeight: 0,
      //有数统计使用
      page_title: "松鼠美妆",

      //弹框广告的展示状态
      dialogIsShowing: true,
      remoteImgUrl: "",
      platform: "",
    };
  },
  onTabItemTap(e) {
    EventBus.$emit("showcompons");
  },
  async onLoad(options) {
    //弹框广告关闭状态通知或者后台配置是未显示状态
    console.log("进入了home", options);
    uni.$on("showBottomBar", (res) => {
      this.dialogIsShowing = false;
    });
    // #ifdef MP-WEIXIN
    let getLaunchOptions = uni.getLaunchOptionsSync();
    this.scene = getLaunchOptions.scene;
    //场景值等于 1154 分享单页模式
    if (this.scene && this.scene == 1154) return;
    // #endif
    // 保存别人分享来的 userCode
    util.saveSharerUserCode(options);
    let that = this;
    uni.getSystemInfo({
      success(res) {
        that.platform = res.platform;
        that.windowHeight = res.screenHeight;
      },
    });
    // 全屏广告
    if (app.globalData.showOpenScreen) {
      this.showScreen = true;
      this.advertisement("OPEN_ADV");
    } else {
      this.showScreen = false;
    }
    //在onLaunch中异步调用接口获取到返回值后执行
    await this.$onLaunched;
    this.initData();
    // #ifdef APP-PLUS
    this.setJGPushAlias();
    // 接收跳转到数据
    var args = plus.runtime.arguments;
    if (args) {
      let url;
      if (args.startsWith("songlei://")) {
        url = args.replace("songlei://", "");
      }
      if (args.startsWith("meitao://")) {
        url = args.replace("meitao://", "");
      }
      uni.navigateTo({
        url,
      });
    }
    // #endif
  },

  onShow() {
    app.shoppingCartCount();
    if (this.scene && this.scene == 1154) return;
    app.initPage();
    if (!this.isFirst) {
      this.handleSkip();
    }
    this.isFirst = false;

    // 再次进入页面时，刷新新人专享数据
    EventBus.$emit("refreshNewCustomerDialog");
    // 再次进入页面时，刷新消息数据
    EventBus.$emit("refreshMessage");
  },
  //页面生命周期 点击 tab 时触发，参数为Object
  onTabItemTap(e) {
    EventBus.$emit("showcompons");
  },
  onUnload() {
    // 新人专享弹框
    EventBus.$off("refreshNewCustomerDialog");
  },

  methods: {
    handleImageError() {
      this.openScreen.imgUrl = this.remoteImgUrl;
    },
    handleOpenScreen(pageUrl) {
      if (pageUrl) {
        gotoPage(pageUrl);
      }
    },
    handleSkip() {
      let that = this;
      this.intervalObj && clearTimeout(this.intervalObj);
      this.timeOutObj && clearTimeout(this.timeOutObj);
      this.showScreen = false;
      app.globalData.showOpenScreen = false;
      app.isBottomTabBar();
    },
    advertisement(id) {
      let that = this;
      api
        .advertisementinfo({
          bigClass: id,
        })
        .then((res) => {
          that.openScreen = {
            ...res.data,
          };
          that.screenBgColor = that.openScreen.bgColor;
          //倒计时
          if (that.times >= 0) {
            that.showMinutes = that.times =
              Number(that.openScreen.showMinutes) + 1;
          }

          // 判断是否展示
          let canShow = false;
          if (!that.openScreen || !that.openScreen.imgUrl) {
            canShow = false;
          } else {
            canShow = util.isShowAdvert(
              that.openScreen.intervalTime,
              that.openScreen.intervalNumber,
              "screen",
            );
          }
          if (!canShow) {
            // 不展示做归零处理
            that.times = 0;
            that.showScreen = false;
            app.globalData.showOpenScreen = false;
          } else {
            this.remoteImgUrl = res.data.imgUrl;
            const localOpenScreen = uni.getStorageSync("image_cache");
            console.log(
              "获取缓存的地址---",
              localOpenScreen,
              localOpenScreen.imgUrl,
              localOpenScreen.localImgUrl,
            );
            if (
              localOpenScreen &&
              localOpenScreen.localImgUrl &&
              localOpenScreen.imgUrl == res.data.imgUrl
            ) {
              let isExist = true;
              try {
                const res = uni
                  .getFileSystemManager()
                  .accessSync(localOpenScreen.localImgUrl);
                isExist = true;
              } catch (e) {
                isExist = false;
                uni.removeStorage({
                  key: "image_cache",
                });
              }
              if (isExist) {
                that.openScreen.imgUrl = localOpenScreen.localImgUrl;
              }
            }

            // #ifdef MP
            const tempRes = {
              ...res.data,
            };
            if (
              !localOpenScreen ||
              localOpenScreen.imgUrl != tempRes.imgUrl ||
              !localOpenScreen.localImgUrl
            ) {
              uni.downloadFile({
                url: tempRes.imgUrl,
                success: function (res) {
                  if (res.statusCode === 200) {
                    wx.getFileSystemManager().saveFile({
                      tempFilePath: res.tempFilePath, // 传入一个临时文件路径
                      success(res) {
                        uni.setStorage({
                          key: "image_cache",
                          data: {
                            ...tempRes,
                            localImgUrl: res.savedFilePath,
                          },
                        });
                      },
                      fail(error) {
                        uni.removeStorage({
                          key: "image_cache",
                        });
                      },
                    });
                  } else {
                    uni.removeStorage({
                      key: "image_cache",
                    });
                  }
                },
                fail(error) {
                  uni.removeStorage({
                    key: "image_cache",
                  });
                },
              });
            }
            // #endif
            that.intervalObj && clearTimeout(that.intervalObj);
            that.timeOutObj && clearTimeout(that.timeOutObj);
            uni.hideTabBar();
            app.globalData.tabBarHide = app.globalData.tabBarHide + 1;
            that.intervalObj = setInterval(() => {
              if (+that.times <= 1) {
                app.globalData.showOpenScreen = false;
                that.showScreen = false;
                clearInterval(that.intervalObj);
                that.$nextTick(() => {
                  // 显示数据
                  app.isBottomTabBar();
                });
              } else {
                that.times = that.times - 1;
              }
            }, 1000);
          }
        });
    },
    initData() {
      const tabBar = uni.getStorageSync("tabBar");
      this.pageState = "home";
      if (tabBar && tabBar[0] && tabBar[0].pagePathId > 0) {
        if (tabBar[0].pagePathId) {
          this.pagePathId = tabBar[0].pagePathId;
        }
      }
    },

    /**
     * 给极光设置别名
     */
    setJGPushAlias() {
      setTimeout(() => {
        const user_info = uni.getStorageSync("user_info");
        if (user_info && user_info.id) {
          setPushAlias(user_info.id);
        }
      }, 10000);
    },

    onShareAppMessage: function () {
      let share = this.$refs.divComponents.shares;
      return share;
    },

    onPullDownRefresh() {
      this.initData();
      this.$refs.divComponents.pullDownRefresh();
    },

    onReachBottom() {
      EventBus.$emit("divGoodsGroupsReachBottom", "true");
    },
    onPageScroll(res) {
      uni.$emit("vonPageScroll", res);
      uni.$emit("vonVideoPageScroll", res);
    },
    onUnload() {
      EventBus.$off("divGoodsGroupsReachBottom");
      this.intervalObj && clearInterval(this.intervalObj);
      this.timeOutObj && clearTimeout(this.timeOutObj);
      uni.$off("showBottomBar");
    },
  },
};
</script>
