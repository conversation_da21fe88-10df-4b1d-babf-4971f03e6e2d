<script>
	import Vue from 'vue';
	import api from 'utils/api';
	import sceneApi from '@/api/scene.js';
	import util from 'utils/util';
	import store from '@/store/index.js';
	import __config from 'config/env';
	import {
		senLogin
	} from "@/public/js_sdk/sensors/utils.js";
	// #ifdef APP-PLUS
	import APPUpdate from '@/public/APPUpdate/index.js'; //App版本更新
	import {
		startPush
	} from '@/api/push';
	// #endif
	// #ifdef MP-WEIXIN
	const businessCirclePlugin = requirePlugin('business-circle-plugin');
	// #endif

	// 对接神策
	import initSensors from "@/public/js_sdk/sensors/index.js";
	export default {
		globalData: {
			shoppingCartCount: 0, //购物车数量
			tenantId: null, //租户Id
			appId: null, //公众号appId
			componentAppId: null, //第三方平台appid
			theme: {
				//主题定义
				backgroundColor: null, //背景颜色,支持渐变色
				themeColor: null, //主题颜色
				tabbarBackgroundColor: null, //tabbar背景颜色
				tabbarColor: null, //tabBar上的文字默认颜色
				tabbarSelectedColor: null, //tabBar上的文字选中时的颜色
				tabbarBorderStyle: null, //tabBar上边框的颜色
				tabbarItem: [], //tabBar明细设置
				customFiled: null //顶部tabBar
			},
			tabBar: [],
			isSinglePage: false, //分享单页场景值
			payMode: false, //视频号过来的场景值
			isShowingPage: false,
			showOpenScreen: false,
			//腾讯广告 clickid
			gdtVid: '',
			//来源广告的广告id
			weixinadinfoId: 0,
			//统计进入小程序的来源
			sf: '',
			nGo: '',
			//因为支付和下单都是组件，把防抖变量放在组件内控制是不起作用的，需要全局控制
			//是否正在调起支付
			isDealPaying: false,
			orderSubLoading: false,
			money: '',
			AuthenticationType: '',
			tabBarHide: 0,
			// 拍照图片
			cameraPic: '',
			appState: '', //show 展示在前端   hide 在后台运行		
			scene: '' //场景值
		},
		onLaunch(e) {
			this.loadFontFaceFrom();
			// #ifdef APP
			if (uni.getSystemInfoSync().platform === 'ios') {
				this.isNetwork(e);
			} else {
				this.resloveOnLaunch(e);
			}
			// #endif
			// #ifndef APP
			this.resloveOnLaunch(e);
			// #endif
		},
		onShow: function(e) {
			this.globalData.appState = 'show';
			if (e && e.referrerInfo && e.referrerInfo.extraData) {
				const schoolData = e.referrerInfo.extraData;
				util.setStorage(schoolData, 'school_user_info');
			}

			const user = uni.getStorageSync('user_info');
			if (!user) {
				uni.setStorageSync(
					'user_info',
					JSON.stringify({
						id: null
					})
				);
			} else {
				// 每次打开小程序要调这个接口
				senLogin(user);
			}
			// #ifdef APP
			// 检查是否有user_info,没有的化设置一个
			setTimeout(() => {
				var args = plus.runtime.arguments;
				if (args) {
					let url;
					// 外部链接跳转过来的
					if (args.startsWith('songlei://')) {
						url = args.replace(/songlei:\/\//, '');
					}
					if (args.startsWith('meitao://')) {
						url = args.replace(/meitao:\/\//, '');
					}
					if (url) {
						uni.navigateTo({
							url
						});
					}
					plus.runtime.arguments = '';
				}
			}, 100);
			// #endif
		},

		onHide() {
			this.globalData.appState = 'hide';
		},
		methods: {
			resloveOnLaunch(e) {
				let that = this;
				// #ifdef APP
				startPush();
				uni.hideTabBar();
				setTimeout(() => {
					if (this.tabBarHide === 0) {
						uni.showTabBar();
					}
				}, 10000);
				// #endif
				that.setGlobalSize();
				// #ifdef MP || APP
				uni.onWindowResize(function(res) {
					that.setGlobalSize(res.size.windowWidth);
				});
				// #endif
				const {
					scene,
					path,
					query
				} = e || {};
				console.log("e=====>", e);
				if (query) {
					const {
						gdt_vid,
						weixinadinfo,
						sf,
						nGo
					} = query;
					if (gdt_vid) {
						this.globalData.gdtVid = gdt_vid;
					}
					this.globalData.sf = sf;
					this.globalData.nGo = nGo;
					// console.log("获取weixinadinfo---", weixinadinfo);
					if (weixinadinfo) {
						let weixinadinfoArr = weixinadinfo.split('.');
						this.globalData.weixinadinfoId = weixinadinfoArr[0];
					}

					const qrCodeScene = decodeURIComponent(scene);
					if (qrCodeScene) {
						//接受二维码中参数  参数sf=XXX&id=XXX
						const qrCodeSceneArray = qrCodeScene.split('&');
						const qrCodeSf = util.UrlParamHash(qrCodeScene, 'sf');
						if (qrCodeSf) {
							this.globalData.sf = qrCodeSf;
						}
						const qrCodeNGo = util.UrlParamHash(qrCodeScene, 'nGo');
						if (qrCodeNGo) {
							this.globalData.nGo = qrCodeNGo;
						}
					}
				}
				this.isSinglePage = scene === 1154;
				if (path == 'pages/home/<USER>' && scene != 1154) {
					this.globalData.showOpenScreen = true;
				}
				//设置全局样式
				this.setGlobalStyle();
				// #ifdef MP
				//小程序平台检测新版本
				//场景值等于 1154 分享单页模式
				if (e.scene && e.scene == 1154) return;
				//视频号过来的场景值
				const localLiveScenes = [1175, 1176, 1177, 1184, 1191, 1193, 1195, 1200, 1201, 1216, 1228];
				if (e.scene && localLiveScenes.includes(e.scene)) {
					this.globalData.payMode = true;
				}
				this.updateManager();
				this.doLogin();
				// #endif
				// #ifdef APP
				// 检查是否有user_info,没有的化设置一个
				if (!uni.getStorageSync('user_info')) {
					uni.setStorageSync(
						'user_info',
						JSON.stringify({
							id: null
						})
					);
				}
				// // 原生app版本更新检测
				APPUpdate();
				// #endif
				// #ifdef H5
				// H5平台获取参数中的租户ID、公众号appID,并存入globalData
				//微信公众号
				if (util.isWeiXinBrowser()) {
					let local = location.href;
					let tenantId = util.getUrlParam(local, 'tenant_id');
					let appId = util.getUrlParam(local, 'app_id');
					let componentAppId = util.getUrlParam(local, 'component_appid');
					this.globalData.tenantId = tenantId;
					this.globalData.appId = appId;
					this.globalData.componentAppId = componentAppId;
				} else {
					// 非微信环境H5
					let tenantId = __config.tenantId;
					this.globalData.tenantId = tenantId;
				}
				// #endif
				// #ifdef APP-PLUS
				// APP平台需要从配置文件中获取租户ID
				let tenantId = __config.tenantId;
				this.globalData.tenantId = tenantId;
				// #endif
				//获取购物车数量
				if (uni.getStorageSync('third_session')) {
					this.shoppingCartCount();
				}
				this.globalData.isShowingPage = true;
				if (scene) {
					sceneApi.getScene().then(res => {
						// 处理结果
						this.globalData.scene = res[scene];
					}).catch(err => {
						// 处理错误
					});
				}

				//初始化神策
				initSensors();
			},
			setGlobalSize(windowWidth) {
				// #ifdef APP
				if (windowWidth) {
					if (windowWidth !== Vue.prototype.windowWidth) {
						// 切换屏幕数据
						let dsvgds = getCurrentPages();
						let currPage = dsvgds[dsvgds.length - 1];
						uni.reLaunch({
							url: '/pages/login/blank?paths=' + currPage.$page.fullPath
						});
					}
				}
				// #endif
				if (!windowWidth) {
					windowWidth = wx.getSystemInfoSync().windowWidth;
				}
				Vue.prototype.windowWidth = windowWidth;
				Vue.prototype.pixelRatio = 750 / windowWidth;
				// #ifdef MP-WEIXIN || MP-QQ
				let {
					width,
					height,
					bottom,
					top
				} = wx.getMenuButtonBoundingClientRect();
				Vue.prototype.menuHeight = height;
				Vue.prototype.menuWidth = width;
				// #endif

				uni.getSystemInfo({
					success: function(e) {
						// [2020-08-01]更新ColorUI 修复ios 状态栏错位
						// #ifndef MP
						Vue.prototype.StatusBar = e.statusBarHeight;
						if (e.platform == 'android') {
							Vue.prototype.CustomBar = e.statusBarHeight + 50;
						} else {
							Vue.prototype.CustomBar = e.statusBarHeight + 45;
						}

						// #endif
						// #ifdef MP-WEIXIN || MP-QQ
						Vue.prototype.StatusBar = e.statusBarHeight;
						console.log('胶囊=======', bottom, 'top===', top, 'e.statusBarHeight==', e
							.statusBarHeight);
						if (bottom > 0 && top > 0) {
							Vue.prototype.CustomBar = bottom + top - e.statusBarHeight;
						} else {
							Vue.prototype.CustomBar = e.statusBarHeight + 50;
						}
						// #endif

						// #ifdef MP-ALIPAY
						Vue.prototype.StatusBar = e.statusBarHeight;
						Vue.prototype.CustomBar = e.statusBarHeight + e.titleBarHeight;
						// #endif
						if (Vue.prototype.CustomBar < 10) {
							Vue.prototype.CustomBar = 60;
						}

						// #ifdef APP
						//app给个默认值
						Vue.prototype.HeightBar = 40;
						Vue.prototype.menuHeight = 40;
						Vue.prototype.leftMenuHeight = 40;
						Vue.prototype.leftMenuWidth = 100;
						Vue.prototype.menuWidth = 0;
						// #endif

						//  状态栏内容高度，正常跟CustomBar一样高，但是三星折叠屏幕，胶囊高度宽太不协调，做个缩小处理
						// #ifndef APP
						Vue.prototype.HeightBar = Vue.prototype.menuHeight;
						Vue.prototype.leftMenuHeight = Vue.prototype.menuHeight;
						Vue.prototype.leftMenuWidth = Vue.prototype.menuWidth;
						// #endif
						if (windowWidth < 350) {
							// #ifdef APP
							Vue.prototype.HeightBar = Vue.prototype.HeightBar * 0.8;
							// #endif
							Vue.prototype.CustomBar = Vue.prototype.CustomBar - 4;
							Vue.prototype.menuWidth = Vue.prototype.menuWidth;
							Vue.prototype.menuHeight = Vue.prototype.menuHeight;
							Vue.prototype.leftMenuHeight = Vue.prototype.leftMenuHeight * 0.6;
							Vue.prototype.leftMenuWidth = Vue.prototype.leftMenuWidth * 0.6;
						}
						console.error("uni.getSystemInfoSync().pixelRatio---", uni.getSystemInfoSync()
							.pixelRatio);
						store.commit('updateData', {
							windowWidth: Vue.prototype.windowWidth,
							pixelRatio: Vue.prototype.pixelRatio,
							menuHeight: Vue.prototype.menuHeight,
							menuWidth: Vue.prototype.menuWidth,
							CustomBar: Vue.prototype.CustomBar,
							HeightBar: Vue.prototype.HeightBar,
							StatusBar: Vue.prototype.StatusBar,
							leftMenuWidth: Vue.prototype.leftMenuWidth,
							leftMenuHeight: Vue.prototype.leftMenuHeight,
							// 550 为是否是pad版本的分界线
							isPhone: Vue.prototype.windowWidth < 550,
							padWidth: 550,
							bigPhone: 430,
							// 二折叠小屏幕
							smallWidth: 310,
							// 倍数关系  
							multipleView: Vue.prototype.windowWidth < 550 ? 2 : 1.2,
							isIOS: util.isIOS(),
						});
						store.dispatch("updateRootFontSize")
					}
				});
			},

			//设置全局样式
			setGlobalStyle() {
				//获取主题装修配置
				api.themeMobileGet().then((res) => {
					let themeMobile = res.data;
					//定义默认配置
					let backgroundColor = 'gradual-scarlet';
					let themeColor = 'red';
					let tabbarBackgroundColor = '#ffffff';
					let tabbarColor = '#666666';
					let tabbarSelectedColor = '#e53c43';
					let tabbarBorderStyle = '#black';
					let customFiled = {
						topTabbarIconColor: '#000000',
						topTabbarIconBackgroundColor: '#A07A56',
						topTabbarBorderColor: '#000',
						topTabbarSplitLineColor: '#eee',
						integralTab: '-1'
					};
					let tabbarItem = [{
							index: 0,
							text: '松鼠美妆',
							iconPath: '/static/public/img/icon/1-001.jpg',
							selectedIconPath: '/static/public/img/icon/1-002.jpg'
						},
						{
							index: 1,
							text: '松鼠好物',
							iconPath: '/static/public/img/icon/2-001.jpg',
							selectedIconPath: '/static/public/img/icon/2-002.jpg'
						},
						{
							index: 2,
							text: '逛逛',
							iconPath: '/static/public/img/icon/3-001.jpg',
							selectedIconPath: '/static/public/img/icon/3-002.jpg'
						},
						{
							index: 3,
							text: '购物车',
							iconPath: '/static/public/img/icon/4-001.jpg',
							selectedIconPath: '/static/public/img/icon/4-002.jpg'
						},
						{
							index: 4,
							text: '我的',
							iconPath: '/static/public/img/icon/5-001.jpg',
							selectedIconPath: '/static/public/img/icon/5-002.png'
						}
					];
					//将默认配置换成后台数据
					if (themeMobile) {
						themeColor = themeMobile.themeColor;
						backgroundColor = themeMobile.backgroundColor;
						tabbarBackgroundColor = themeMobile.tabbarBackgroundColor;
						tabbarColor = themeMobile.tabbarColor;
						tabbarSelectedColor = themeMobile.tabbarSelectedColor;
						tabbarBorderStyle = themeMobile.tabbarBorderStyle;
						customFiled = themeMobile.customFiled;
					}
					this.globalData.theme.backgroundColor = backgroundColor;
					this.globalData.theme.themeColor = themeColor;
					this.globalData.theme.tabbarBackgroundColor = tabbarBackgroundColor;
					this.globalData.theme.tabbarColor = tabbarColor;
					this.globalData.theme.tabbarSelectedColor = tabbarSelectedColor;
					this.globalData.theme.tabbarBorderStyle = tabbarBorderStyle;
					this.globalData.theme.tabbarItem = tabbarItem;
					this.globalData.theme.customFiled = customFiled;
					if (themeMobile && themeMobile.tabbarItem && themeMobile.tabbarItem.info.length > 0) {
						let tabbarItemInfo = themeMobile.tabbarItem.info;
						tabbarItemInfo.forEach((item) => {
							if (item.text) tabbarItem[item.index].text = item.text;
							// #ifdef MP
							if (item.iconPath) tabbarItem[item.index].iconPath = item.iconPath;
							if (item.selectedIconPath) tabbarItem[item.index].selectedIconPath = item
								.selectedIconPath;
							// #endif
						});

						this.globalData.theme.tabbarItem = tabbarItem;
						const that = this;
						uni.setStorage({
							key: 'tabBar',
							data: tabbarItemInfo,
							success: function() {
								that.$isResolve();
							}
						});
					}
					this.setTabBar();
				});
			},

			// #ifdef MP
			//小程序平台检测新版本
			updateManager() {
				const updateManager = uni.getUpdateManager();
				updateManager.onUpdateReady(function() {
					// console.log("====最新版本已经准备好,接口判断当前版本是否强制更新再启动=====")
					api
						.isForceUpdate()
						.then((result) => {
							// console.log("=====result====",result)
							if (result.data.weixinForceUpdate) {
								uni.showModal({
									title: '更新提示',
									content: '新版本已经准备好，请重启运行新版本！',
									showCancel: false,
									success(res) {
										if (res.confirm) {
											updateManager.applyUpdate();
										}
									}
								});
							}
						})
						.catch((e) => {
							console.log(e);
						});
				});
			},
			// #endif

			//设置tabbar
			setTabBar() {
				this.globalData.isShowingPage = true;
				let themeMobile = this.globalData.theme;
				// if (themeMobile.tabbarBackgroundColor) {
				//动态设置 tabBar 的整体样式
				// #ifdef MP
				if (uni.getSystemInfoSync().platform === 'windows') {
					return;
				}
				// #endif
				uni.setTabBarStyle({
					backgroundColor: themeMobile.tabbarBackgroundColor,
					color: themeMobile.tabbarColor,
					selectedColor: themeMobile.tabbarSelectedColor,
					borderStyle: themeMobile.tabbarBorderStyle
				});
				let tabbarItem = themeMobile.tabbarItem;
				setTimeout(() => {
					tabbarItem.forEach((item) => {
						//动态设置 tabBar 某一项的内容
						let iconPath = item.iconPath;
						let selectedIconPath = item.selectedIconPath;
						// #ifdef H5
						//uni tabBar H5不支持http的图片,但是修改一下可以支持
						if (selectedIconPath.indexOf('http') != -1) {
							// 此判断包括 http 和 https
							let indexTemp = selectedIconPath.indexOf(':/') + 1;
							selectedIconPath = selectedIconPath.substring(indexTemp, selectedIconPath
								.length);
						}
						if (iconPath.indexOf('http') != -1) {
							let indexTemp = iconPath.indexOf(':/') + 1;
							iconPath = iconPath.substring(indexTemp, iconPath.length);
						}
						// #endif
						uni.setTabBarItem({
							index: item.index,
							text: item.text,
							iconPath: iconPath,
							selectedIconPath: selectedIconPath
						});
					});
				}, 1000);
				// }
			},

			//获取购物车数量
			shoppingCartCount() {
				api.shoppingCartCount().then((res) => {
					let shoppingCartCount = res.data;
					this.globalData.shoppingCartCount = shoppingCartCount + '';
					if (+shoppingCartCount) {
						uni.setTabBarBadge({
							index: 3,
							text: shoppingCartCount + ''
						});
					} else {
						uni.removeTabBarBadge({
							index: 3
						});
					}

					uni.$emit('updateCart');
				});
			},

			//页面初始化方法，供每个页面调用
			//是否必须微信是登录状态，比如获取手机号，头像昵称解析加密的数据必须保证微信的session有效
			initPage(isNeedWxLogin) {
				let that = this;
				return new Promise((resolve, reject) => {
					that.setTabBar();
					//小程序或公众号H5，每个页面都进行登录校验
					if (util.isMiniPg() || (that.globalData.appId && util.isWeiXinBrowser())) {
						if (!uni.getStorageSync('third_session') && !isNeedWxLogin) {
							//无thirdSession，进行登录
							that.doLogin().then((res) => {
								resolve('success');
							});
						} else {
							if (util.isMiniPg()) {
								//小程序需要检查登录态是否过期
								uni.checkSession({
									success() {
										//session_key 未过期，并且在本生命周期一直有效
										// console.log('session_key 未过期')
										resolve('success');
									},
									fail() {
										// session_key 已经失效，需要重新执行登录流程
										// console.log('session_key 已经失效')
										that.doLogin().then((res) => {
											resolve('success');
										});
									}
								});
							} else {}
						}
					} else {
						resolve('success');
					}
				});
			},
			// 判断是否显示还是隐藏底部导航栏
			isBottomTabBar() {
				this.globalData.tabBarHide = this.globalData.tabBarHide - 1;
				if (this.globalData.tabBarHide <= 0) {
					this.globalData.tabBarHide = 0;
					uni.showTabBar();
					this.shoppingCartCount();
				}
			},
			// 登录操作
			doLogin() {
				uni.showLoading({
					title: '登录中'
				});
				return new Promise((resolve, reject) => {
					// #ifdef MP-WEIXIN
					//微信小程序登录
					this.loginWxMa().then((res) => {
						resolve('success');
					});
					// #endif
					// #ifdef APP
					api.userInfoGet().then((res) => {
						const userInfo = res.data;
						senLogin(userInfo);
						uni.setStorageSync('third_session', userInfo.thirdSession);
						uni.setStorageSync('user_info', userInfo);
						// 获取购物车数量
						that.shoppingCartCount();
					});
					// #endif
					// #ifdef H5
					//微信公众号H5
					if (util.isWeiXinBrowser()) {
						let local = location.href;
						let code = util.getUrlParam(local, 'code');
						let state = util.getUrlParam(local, 'state');
						//授权code登录
						if (code) {
							//有code
							if (state == 'snsapi_base' || state == 'snsapi_userinfo') {
								//登录授权
								this.loginWxMp(code, state).then((res) => {
									resolve('success');
								});
							}
						} else {
							//无code则发起网页授权
							//微信公众号H5，页面授权登录
							let appId = this.globalData.appId;
							let pages = getCurrentPages();
							let currentPage = pages[pages.length - 1];
							let route = currentPage.route;
							let redirectUri = location.href;
							let componentAppId_str = this.globalData.componentAppId ? '&component_appid=' + this
								.globalData.componentAppId : '';
							redirectUri = encodeURIComponent(redirectUri);
							let wx_url =
								'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
								appId +
								'&redirect_uri=' +
								redirectUri +
								componentAppId_str +
								'&response_type=code&scope=snsapi_base&state=snsapi_base#wechat_redirect';
							location.href = wx_url;
						}
					}
					// #endif
				});
			},
			//微信小程序登录
			// #ifdef MP-WEIXIN
			loginWxMa() {
				return new Promise((resolve, reject) => {
					let that = this;
					uni.login({
						success: function(res) {
							if (res.code) {
								api
									.loginWxMa({
										jsCode: res.code
									})
									.then((res) => {
										uni.hideLoading();
										let userInfo = res.data;
										if (userInfo && userInfo.erpCid > 0) {
											senLogin(userInfo);
										}
										uni.setStorageSync('third_session', userInfo.thirdSession);
										uni.setStorageSync('user_info', userInfo);
										// 获取购物车数量
										that.shoppingCartCount();
										resolve('success');
									});
							}
						}
					});
				});
			},
			// #endif
			//公众号登录
			// #ifdef H5
			loginWxMp(code, state) {
				let that = this;
				return new Promise((resolve, reject) => {
					let that = this;
					api
						.loginWxMp({
							jsCode: code,
							scope: state
						})
						.then((res) => {
							//公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
							let query = that.$Route.query;
							delete query.code;
							delete query.state;
							util.resetPageUrl(query);
							let userInfo = res.data;
							uni.setStorageSync('third_session', userInfo.thirdSession);
							uni.setStorageSync('user_info', userInfo);
							//获取购物车数量
							that.shoppingCartCount();
							resolve('success');
						})
						.catch((res) => {});
				});
			},
			// #endif
			// 判断是否有数据
			isUser() {
				const userInfo = uni.getStorageSync('user_info');
				if (userInfo) {
					const {
						id,
						erpCid
					} = userInfo;
					if (id && erpCid) {
						// 登录中
						return 1;
					} else if (id && !erpCid) {
						//这个情况才提示登录失效，否则就是还没登录,直接跳转登录页面，
						return 2;
					}
					return 0;
				}
				return -1;
			},
			// 判断是否登录
			isLogin(hidden = false) {
				console.log('this.isUser()===', this.isUser());
				if (this.isUser() === 1) {
					return true;
				} else {
					if (!hidden && this.isUser() === 2) {
						uni.showModal({
							title: '提示',
							content: '登录失效，请重新登录',
							showCancel: false,
							success() {
								const pages = getCurrentPages();
								const url = pages[pages.length - 1]['$page']['fullPath'];
								uni.reLaunch({
									url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
								});
							}
						});
					} else {
						const pages = getCurrentPages();
						const url = pages[pages.length - 1]['$page']['fullPath'];
						uni.reLaunch({
							url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
						});
					}
					return false;
				}
			},
			// 小程序能直接进入的页面登录判断
			loopUser() {
				return new Promise((resolve, reject) => {
					uni.showLoading({
						title: '加载中'
					});
					var interVal = setInterval(() => {
						const code = this.isUser();
						if (code !== -1) {
							clearInterval(interVal);
							uni.hideLoading();
							if (code === 1) {
								resolve();
							} else if (code === 2) {
								uni.showModal({
									title: '提示',
									content: '登录失效，请重新登录',
									showCancel: false,
									success() {
										const pages = getCurrentPages();
										const url = pages[pages.length - 1]['$page']['fullPath'];
										uni.reLaunch({
											url: '/pages/login/index?reUrl=' +
												encodeURIComponent(url)
										});
									}
								});
								reject();
							} else {
								const pages = getCurrentPages();
								const url = pages[pages.length - 1]['$page']['fullPath'];
								uni.reLaunch({
									url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
								});
							}
						}
					}, 100);
				});
			},
			// 判断网络是否连接
			isNetwork(e) {
				const that = this;
				uni.getNetworkType({
					success: function(res) {
						console.log(res.networkType); //网络类型 wifi、2g、3g、4g、ethernet、unknown、none
						if (res.networkType === 'none') {
							console.log('当前无网络');
							const CALLBACK = function(res) {
								if (res.isConnected !== false) {
									console.log('有网络');
									const pages = getCurrentPages();
									const url = pages[pages.length - 1]['$page']['fullPath'];
									uni.offNetworkStatusChange(CALLBACK);
									that.resloveOnLaunch(e);
									//还需要再次执行app onLaunch
									uni.reLaunch({
										url
									});
								}
							};
							// 没有网络就监听，监听到有网络了之后重新来
							uni.onNetworkStatusChange(CALLBACK);
						} else {
							// 有网络的话就不管
							console.log('有网络');
							that.resloveOnLaunch(e);
						}
					},
					fail(err) {
						console.log('判断网络错误提示', err);
						that.resloveOnLaunch(e);
					}
				});
			},

			loadFontFaceFrom() {
				// #ifdef MP-WEIXIN
				if (uni.getSystemInfoSync().platform === 'android' || uni.getSystemInfoSync().platform === 'devtools') {
					console.error('-------------远程加载字体--------------');
					//	let fontUrl = 'https://slshop-file.oss-cn-beijing.aliyuncs.com/apk/songshufont/AlibabaPuHuiTi-2-55-Regular.woff';
					// let fontUrl = 'https://slshop-file.oss-cn-beijing.aliyuncs.com/apk/songshufont/FT53.ttf';
					// uni.loadFontFace({
					// 	global: true,
					// 	family: 'font-ali-regular',
					// 	source: 'url("' + fontUrl + '")',
					// 	success() {
					// 		console.error('-----------AlibabaRegular---success--------------');
					// 	},
					// 	fail(e) {
					// 		console.error('----------AlibabaRegular----fails-------------');
					// 	}
					// });
					// let fontBoldUrl = 'https://slshop-file.oss-cn-beijing.aliyuncs.com/apk/songshufont/AliMedium.ttf';
					// uni.loadFontFace({
					// 	global: true,
					// 	family: 'font-ali-bold',
					// 	source: 'url("' + fontBoldUrl + '")',
					// 	success() {
					// 		console.error('-----------AliBold---success--------------');
					// 	},
					// 	fail(e) {
					// 		console.error('----------AliBold----fails-------------');
					// 	}
					// });
					// let fontThinUrl = 'https://slshop-file.oss-cn-beijing.aliyuncs.com/apk/songshufont/AliLight.otf';
					// uni.loadFontFace({
					// 	global: true,
					// 	family: 'font-ali-thin',
					// 	source: 'url("' + fontThinUrl + '")',
					// 	success() {
					// 		console.error('-----------fontThinUrl---success--------------');
					// 	},
					// 	fail(e) {
					// 		console.error('----------fontThinUrl----fails-------------');
					// 	}
					// });
					let fontEnUrl = 'https://slshop-file.oss-cn-beijing.aliyuncs.com/apk/songshufont/Harmonyos.ttf';
					uni.loadFontFace({
						global: true,
						family: 'font-en-regular',
						source: 'url("' + fontEnUrl + '")',
						success() {
							console.error('-----------Harmonyos---success--------------');
						},
						fail(e) {
							console.error('-----------Harmonyos---fails-------------');
						}
					});
				}
				// #endif
			}
		}
	};
</script>
<style lang="scss">
	/* #ifndef APP-PLUS-NVUE */
	@import './app.scss';
	/* #endif */
</style>