import __config from "@/config/env";
import util from "@/utils/util";
const CryptoJS = require("crypto-js");
import {
  senTrack,
  getCurrentTitle,
  senGoods,
} from "@/public/js_sdk/sensors/utils.js";
/**
 * @params errModalHide 结算领取优惠券时，不提示任何错误信息，不拦截任何错误信息，允许下一步结算操作
 * @params errGoLogin  大转盘没登录的时候，调剩余
 */

const uuid = () => {
  var s = [];
  var hexDigits = "0123456789abcdef";
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = "-";

  var uuid = s.join("");
  return uuid;
};

const request = (
  url,
  method,
  data,
  showLoading,
  errModalHide,
  dialogFinishCallBack,
  contentType,
  isSign,
) => {
  let _url = url;
  const userInfo = uni.getStorageSync("user_info");
  //#ifndef H5
  // 通过扫码微信小程序码到pages/pos/agreeauth界面，授权退货申请，调的接口是pos端的服务，只有微信小程序有这个功能
  _url = __config.basePath + url;
  //#endif
  // channel 渠道 全局判断渠道 SONGSHU-微信小程序 APP-APP
  // 先判断data有没有值，再判断data里面有没有channel，再判断channel的值，最后替换channel的值
  if (data) {
    const systemInfo = wx.getSystemInfoSync();
    if (
      data.channel == "" ||
      data.channel == "SONGSHU" ||
      data.channel == "APP"
    ) {
      // console.error("==systemInfo.environment ==================",systemInfo.environment )
      if (systemInfo.environment && systemInfo.environment === "wxwork") {
        data.channel = "WXWORK";
      } else if (systemInfo.uniPlatform == "app") {
        data.channel = "APP";
      } else if (systemInfo.uniPlatform == "mp-weixin") {
        data.channel = "SONGSHU";
      }
    }
  }

  return new Promise((resolve, reject) => {
    if (showLoading) {
      uni.showLoading({
        title: "加载中",
      });
    }

    let header = {
      //#ifdef H5
      "client-type": util.isWeiXinBrowser() ? "H5-WX" : "H5", //客户端类型普通H5或微信H5
      "tenant-id": getApp().globalData.tenantId || __config.tenantId,
      "app-id": getApp().globalData.appId ? getApp().globalData.appId : "", //微信h5有appId
      //#endif
      //#ifdef MP-WEIXIN
      "client-type": "MA", //客户端类型小程序
      "app-id": uni.getAccountInfoSync().miniProgram.appId, //小程序appId
      //#endif
      //#ifdef APP-PLUS
      "client-type": "APP", //客户端类型APP
      "app-id": uni.getStorageSync("user_info")?.appId || "",
      "tenant-id": __config.tenantId,
      //#endif
      "third-session": uni.getStorageSync("third_session")
        ? uni.getStorageSync("third_session")
        : "",
      user_id: userInfo ? userInfo.id : "",
      "Content-Type": contentType || "application/json",
    };
    console.log(
      "发送接口url:" + _url + ", 接口方式：" + method + ", 数据data: ",
      data,
    );
    // 处理如果需要加密的话 header里增加3个参数  timestamp,requestId, sign
    let timestamp = Math.round(new Date().getTime());
    let requestId = uuid();
    let securityKey = "songleishop"; //签名Key
    // sign的获取方式 对字符串进行md5(str)加密得签名sign	，其中params 取 get/delete方法的url参数，取put/post 的 body的json参数，对参数名排序后拼接成如a=1&b=2字符串
    //取key

    let sign = "",
      tempData = {};
    if (
      method == "get" ||
      method == "GET" ||
      method == "DELETE" ||
      method == "delete"
    ) {
      let param = {};
      if (_url && _url.indexOf("?") != -1) {
        const paramsString = _url.split("?")[1];
        const eachParamArray = paramsString.split("&");
        eachParamArray.forEach((p) => {
          const key = p.split("=")[0];
          var value = p.split("=")[1];
          Object.assign(param, {
            [key]: value,
          });
        });
      }

      data = {
        ...param,
        ...data,
      };
      let keys = [];
      for (let k in data) {
        keys.push(k);
        tempData[k] = data[k] == null ? "" : data[k];
      }
      //排序
      keys.sort();
      //取value
      let str = "";
      for (let k in keys) {
        str =
          str +
          "&" +
          keys[k] +
          "=" +
          (data[keys[k]] == null ? "" : data[keys[k]]);
      }
      str = str.slice(1, str.length);
      let paramStr =
        "timestamp=" +
        timestamp +
        "&requestId=" +
        requestId +
        "&params=" +
        str +
        "&securityKey=" +
        securityKey;
      sign = CryptoJS.MD5(paramStr).toString();
    } else {
      tempData = data;
      let paramStr =
        "timestamp=" +
        timestamp +
        "&requestId=" +
        requestId +
        "&params=" +
        JSON.stringify(data || {}) +
        "&securityKey=" +
        securityKey;
      sign = CryptoJS.MD5(paramStr).toString();
    }
    header = {
      ...header,
      timestamp,
      requestId,
      sign,
    };

    uni.request({
      url: _url,
      method: method,
      data: tempData,
      withCredentials: true,
      header,
      success(res) {
        console.log(_url + "接口返回值===", res);
        if (res.statusCode == 200) {
          if (res.data.code != 0) {
            if (res.data.code == 60001 || res.data.code == 60002) {
              if (!getApp().globalData.logining) {
                getApp().globalData.logining = true; //防止同时多个接口触发登录
                if (
                  util.isMiniPg() ||
                  (getApp().globalData.appId && util.isWeiXinBrowser())
                ) {
                  //小程序或公众号H5，删除third_session重新登录
                  uni.removeStorageSync("third_session");
                  getApp()
                    .doLogin()
                    .then((res) => {
                      var pages = getCurrentPages(); //获取页面栈
                      var currPage = pages[pages.length - 1]; // 当前页面
                      currPage.onLoad(currPage.options);
                      currPage.onShow();
                    });
                } else {
                  util.backLoginPage();
                }
                setTimeout(function () {
                  getApp().globalData.logining = false;
                }, 2000);
              }
              reject("session过期重新登录");
            } else if (res.data.code == 60003) {
              if (!getApp().globalData.logining2) {
                getApp().globalData.logining2 = true; //防止同时多个接口触发登录
                util.backLoginPage();
                setTimeout(function () {
                  getApp().globalData.logining2 = false;
                }, 2000);
              }
              reject("请先登录商城");
            } else if (
              res.data.code == 90002 ||
              res.data.code == 90003 ||
              res.data.code == 90001 ||
              res.data.code == 90000
            ) {
              resolve(res.data);
            } else if (res.data.code == 50009) {
              resolve(res.data);
            } else {
              !errModalHide &&
                uni.showModal({
                  title: "提示",
                  showCancel: false,
                  content: res.data.msg ? res.data.msg + "" : "网络连接错误",
                  success() {},
                  complete() {
                    dialogFinishCallBack && dialogFinishCallBack();
                  },
                });
              !errModalHide
                ? reject(res.data.msg || "网络连接错误")
                : resolve();
            }
          }
          resolve(res.data);
        } else if (res.statusCode == 404) {
          !errModalHide &&
            uni.showModal({
              title: "提示",
              content: "接口请求出错，请检查手机网络",
              showCancel: false,
              success(res) {},
            });
          !errModalHide ? reject() : resolve();
        } else if (res.statusCode == 502) {
          // console.log(502);
          !errModalHide &&
            uni.showModal({
              title: "提示",
              content: "服务器维护中，请稍后再来",
              showCancel: false,
              success(res) {},
            });
          !errModalHide ? reject(res) : resolve();
        } else if (res.statusCode == 503) {
          console.log(503);
          !errModalHide &&
            uni.showModal({
              title: "提示",
              content: "503错误，服务未启动",
              showCancel: false,
              success(res) {},
            });
          !errModalHide ? reject(res) : resolve();
        } else {
          console.log("网络连接错误1",_url, res);
          !errModalHide &&
            uni.showModal({
              title: "提示",
              showCancel: false,
              content: res.data.msg || "网络连接错误",
              success(res) {},
            });
          !errModalHide ? reject(res) : resolve();
        }
      },
      fail(error) {
        console.log("接口报错==>", error);
        // interrupted 是程序在后台请求超过5s，报错的提示
        !errModalHide &&
          error.errMsg &&
          error.errMsg.indexOf("interrupted") == -1 &&
          uni.showModal({
            title: "提示",
            content: "接口请求出错：" + error.errMsg,
            showCancel: false,
            success(res) {},
          });
        !errModalHide ? reject(error) : resolve();
      },
      complete(res) {
        uni.hideLoading();
      },
    });
  });
};

module.exports = {
  request,
  requestApi: (obj) => {
    const {
      url = "/",
      method = "get",
      data,
      showLoading = true,
      errModalHide = false,
      dialogFinishCallBack = null,
      contentType = "application/json",
      isSign = false, //是否加墨, false 是不加密
    } = obj;
    return request(
      url,
      method,
      data,
      showLoading,
      errModalHide,
      dialogFinishCallBack,
      contentType,
      isSign,
    );
  },
  loginWxMa: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //微信小程序登录接口
    return request("/mallapi/wxuser/loginma", "post", data, false);
  },
  loginWxMp: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //微信公众号登录接口
    return request("/mallapi/wxuser/loginmp", "post", data, false);
  },
  loginByPhoneMa: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //商城通过小程序授权手机号一键登录商城
    return request(
      "/mallapi/userinfo/ma/phone/login",
      "post",
      {
        ...data,
        clickId: getApp().globalData.gdtVid || "",
      },
      true,
    );
  },
  loginByPhone: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //商城手机验证码登录商城
    return request("/mallapi/userinfo/phone/login", "post", data, true);
  },
  login: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //商城账号登录
    return request("/mallapi/userinfo/login", "post", data, true);
  },
  autologinApi: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //APP一键登录
    return request("/mallapi/userinfo/phone/autologin", "post", data, true);
  },

  erpOpenIdBind: (data) => {
    //确保商城用户信息同步到erp
    return request("/mallapi/userinfo/ma/erpOpenIdBind", "get", data, true);
  },

  logout: (data) => {
    //商城退出登录
    return request("/mallapi/userinfo/logout", "post", data, true);
  },
  getPhoneCode: (data) => {
    //获取手机验证码
    return request("/mallapi/phone/code", "get", data, true);
  },
  register: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //账号注册
    return request("/mallapi/userinfo/register", "post", data, true);
  },
  updateUserPhone: (data) => {
    //修改商城用户手机号
    return request("/mallapi/userinfo/phone", "post", data, true);
  },
  getJsSdkConfig: (data) => {
    //获取jssdk config参数
    return request("/mallapi/wxjssdk/config", "post", data, false);
  },
  themeMobileGet: () => {
    //获取商城主题装修配置
    return request("/mallapi/thememobile", "get", null, false);
  },
  shopInfoPage: (data) => {
    //店铺列表
    return request("/mallapi/shopinfo/page", "get", data, false);
  },
  shopInfoPageWithSpu: (data) => {
    //店铺列表带商品
    return request("/mallapi/shopinfo/pagewithspu", "get", data, false);
  },
  shopInfoGetShopList: (data) => {
    //获取搜索店铺列表带商品
    return request("/mallapi/shopinfo/getShopList", "get", data, false);
  },
  shopInfoGet: (id) => {
    //店铺查询
    return request("/mallapi/shopinfo/" + id, "get", null, false);
  },
  userInfoUpdateByMa: (data) => {
    //通过微信小程序更新用户信息
    return request("/mallapi/userinfo/ma", "put", data, true, true); //密文解析不对不弹出错误信息
  },
  userInfoUpdateByMp: (data) => {
    //通过微信公众号网页授权更新用户信息
    return request("/mallapi/userinfo/mp", "put", data, true);
  },
  goodsCategoryTree: (data) => {
    //商城商品分类tree查询
    return request("/mallapi/goodscategory/tree", "get", data, true);
  },

  goodsCategoryTreeByLevel: (data) => {
    //商城商品分类tree查询
    return request(
      "/mallapi/goodscategory/treeWithBrandByLevel",
      "get",
      data,
      true,
    );
  },

  goodsCategoryPage: (data) => {
    //商城商品分类list查询
    return request("/mallapi/goodscategory/page", "get", data, true);
  },
  goodsCategoryShopTree: (data) => {
    //店铺商品分类tree查询
    return request("/mallapi/goodscategoryshop/tree", "get", data, true);
  },
  searchFindList: (data) => {
    //获取搜索发现列表
    return request("/mallapi/os/getHotSearchList", "get", data, false);
  },
  searchDownList: (data) => {
    //获取搜索下拉提示列表
    return request("/mallapi/os/getSuggestList", "get", data, false);
  },
  searchList: (data) => {
    //搜索
    return request(
      "/mallapi/os/getSearchList",
      "get",
      {
        ...data,
        clickId: getApp().globalData.gdtVid || "",
      },
      false,
    );
  },
  HitSearchList: (data) => {
    //获取搜索框底纹列表
    return request("/mallapi/os/getHitSearchList", "get", data, false);
  },
  goodsPage: (data) => {
    //商品列表
    return request("/mallapi/goodsspu/page", "get", data, false);
  },
  //搜索全网热榜接口调整
  selectHot: () => {
    //商品列表
    return request("/mallapi/goodsspu/selectHot", "get", null, false);
  },

  goodsGet: (id, skuId, customerGoodsId) => {
    const scene = util.getSharerUserCode();
    //商品查询
    return request(
      "/mallapi/goodsspu/getGoodsById?skuId=" +
        (skuId && skuId != "undefined" ? skuId : "") +
        "&id=" +
        id +
        "&customerGoodsId=" +
        customerGoodsId +
        "&scene=" +
        scene +
        "&clickId=" +
        (getApp().globalData.gdtVid || ""),
      "get",
      null,
      false,
    );
  },
  goodsGetCode: (id) => {
    //商品条码查询
    return request(
      "/mallapi/goodsspu/getSpuByBarCode?barCode=" +
        id +
        "&clickId=" +
        (getApp().globalData.gdtVid || "") +
        "&type=1",
      "get",
      null,
      false,
    );
  },
  goodsListGet: (data) => {
    //查询自定商品
    return request("/mallapi/goodsspu/listByIds", "get", data, false);
  },
  goodsSpecGet: (data) => {
    //商品规格查询
    return request("/mallapi/goodsspuspec/tree", "get", data, true);
  },
  goodsGroupsGet: (data) => {
    //获取分组商品
    return request("/mallapi/goodsspu/pageByGroupId", "get", data);
  },
  goodsGroupListByIds: (data) => {
    //获取分组商品
    return request("/mallapi/goodsgroup/getGoodGroupListByIds", "get", data);
  },
  shoppingCartPage: (data) => {
    data = util.dataAddSharerUserCode(data);
    //购物车列表
    return request("/mallapi/shoppingcart/page", "get", data, false);
  },
  shoppingCartOldPage: (data) => {
    //购物车列表
    return request("/mallapi/shoppingcart/oldPage", "get", data, false);
  },
  shoppingCartAdd: (data, showLoading = true) => {
    //购物车新增
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    return request(
      "/mallapi/shoppingcart",
      "post",
      {
        ...data,
        clickId: getApp().globalData.gdtVid || "",
      },
      showLoading,
    );
  },
  shoppingCartUpdate: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //购物车修改
    return request("/mallapi/shoppingcart", "put", data, true);
  },
  shoppingCartDel: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //购物车删除
    return request("/mallapi/shoppingcart/del", "post", data, false);
  },
  shoppingCartCount: (data) => {
    //#ifdef MP-WEIXIN
    //购物车数量
    return request("/mallapi/shoppingcart/count", "get", data, false, true);
    //#endif
    //#ifndef MP-WEIXIN
    if (util.isUserLogin()) {
      //购物车数量
      return request("/mallapi/shoppingcart/count", "get", data, false, true);
    } else {
      return Promise.reject("");
    }
    //#endif
  },

  orderSub: async (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    //通过视频号进来，根据文档，需要获取traceId传给后台，后台拿这个traceId去同步给微信视频号
    // #ifdef MP-WEIXIN
    // 视频号跳转过来的场景值
    const payMode = getApp().globalData.payMode;
    if (payMode) {
      const awx = util.toAsync(["checkBeforeAddOrder"]);
      const res = await awx.checkBeforeAddOrder();
      if (res && res.data && res.data.traceId) {
        data.traceId = res.data.traceId;
        // 视频号后面拼接了sharer_user_code=localLive没啥用，因为购物车情况下支付拿不到localLive
        data.scene = "localLive";
      }
    }
    // #endif
    //订单提交
    return request(
      "/mallapi/orderinfo/orderSubGeneral",
      "post",
      {
        ...data,
        clickId: getApp().globalData.gdtVid || "",
      },
      true,
    );
  },
  orderPage: (data) => {
    //订单列表
    return request("/mallapi/orderinfo/page", "get", data, false);
  },
  orderGet: (id) => {
    //订单详情查询
    return request("/mallapi/orderinfo/" + id, "get", null, false);
  },
  advertisementinfo: (data) => {
    //支付成功广告查询
    return request(
      "/mallapi/advertisementinfo/detail",
      "get",
      data,
      false,
      true,
    );
  },
  advertisementNewYear: (data) => {
    //首页红包提醒图片
    return request(
      "/mallapi/advertisementinfo/actpop",
      "get",
      data,
      false,
      true,
    );
  },
  orderCancel: (id) => {
    //订单确认取消
    return request("/mallapi/orderinfo/cancel/" + id, "put", null, true);
  },
  orderReceive: (id) => {
    //订单确认收货
    return request("/mallapi/orderinfo/receive/" + id, "put", null, true);
  },
  orderDel: (id) => {
    //订单删除
    return request("/mallapi/orderinfo/" + id, "delete", null, false);
  },
  orderCountAll: (data) => {
    //订单计数
    return request("/mallapi/orderinfo/countAll", "get", data, false);
  },
  orderLogisticsGet: (logisticsId) => {
    //订单物流查询
    return request(
      "/mallapi/orderinfo/orderlogistics/" + logisticsId,
      "get",
      null,
      false,
    );
  },
  getLogisticsRouteBy: (data) => {
    //获取物流信息
    return request("/mallapi/yddsend/logisticsRoute", "get", data, true);
  },
  unifiedOrder: (data) => {
    //下单接口
    return request("/mallapi/orderinfo/unifiedOrder", "post", data, true);
  },
  payOrder: (data) => {
    //下单接口-新
    return request(
      "/mallapi/orderinfo/payOrder-zt",
      "post",
      {
        ...data,
      },
      true,
    );
  },

  paySuccessOrder: (data) => {
    //支付成功，朋友圈统计接口
    if (!getApp().globalData.gdtVid) {
      return new Promise(function (resolve, reject) {
        reject();
      });
    }
    return request(
      "/mallapi/orderinfo/purchase",
      "get",
      {
        ...data,
        clickId: getApp().globalData.gdtVid || "",
      },
      false,
    );
  },

  userAddressPage: (data) => {
    //用户收货地址列表
    return request("/mallapi/useraddress/page", "get", data, false);
  },
  userAddressSave: (data) => {
    //用户收货地址新增
    return request("/mallapi/useraddress", "post", data, true);
  },
  userAddressDel: (id) => {
    //用户收货地址删除
    return request("/mallapi/useraddress/" + id, "delete", null, false);
  },
  userCollectAdd: (data) => {
    //用户收藏新增
    return request(
      "/mallapi/usercollect",
      "post",
      {
        ...data,
        clickId: getApp().globalData.gdtVid || "",
      },
      true,
    );
  },
  userCollectDel: (id) => {
    //用户收藏删除
    return request("/mallapi/usercollect/" + id, "delete", null, false);
  },
  userCollectPage: (data) => {
    //用户收藏列表
    return request("/mallapi/usercollect/page", "get", data, false);
  },
  userFootprintPage: (data) => {
    //用户足迹列表
    return request("/mallapi/userfootprint/page", "get", data, false);
  },
  goodsAppraisesAdd: (data) => {
    //商品评价新增
    return request("/mallapi/goodsappraises", "post", data, true);
  },
  goodsAppraisesPage: (data) => {
    //商品评价列表
    return request("/mallapi/goodsappraises/page", "get", data, false);
  },
  qrCodeUnlimited: (data) => {
    //获取小程序二维码
    //获取小程序当前版本
    let envVersion;
    // #ifdef MP
    const accountInfo = wx.getAccountInfoSync();
    envVersion = accountInfo.miniProgram.envVersion;
    // #endif
    return request(
      "/mallapi/wxqrcode/unlimited",
      "post",
      {
        ...data,
        envVersion: envVersion || "release",
      },
      true,
    );
  },
  orderItemGet: (id) => {
    //查询订单详情
    return request("/mallapi/orderitem/" + id, "get", null, false);
  },
  orderItemListGet: (data) => {
    //查询订单详情
    return request("/mallapi/orderitem/listByIds", "get", data, false);
  },
  getShopDynamic: (data) => {
    //查询订单详情
    return request("/mallapi/shopdynamic/getShopDynamic", "get", data, false);
  },
  orderRefundsSave: (data) => {
    //发起退款
    return request("/mallapi/orderrefunds", "post", data, true);
  },
  userInfoGet: () => {
    //查询商城用户信息
    return request("/mallapi/userinfo", "get", null, false);
  },
  userInfoUpdate: (data) => {
    //修改商城用户
    return request("/mallapi/userinfo", "put", data, true);
  },
  pointsRecordPage: (data) => {
    //查询积分记录
    return request("/mallapi/pointsrecord/page", "get", data, false);
  },

  erpPointsRecordPage: (data) => {
    //ERP查询积分记录
    return request(
      "/mallapi/userinfo/getUserSalePayMessage",
      "get",
      data,
      false,
    );
  },
  pointsConfigGet: () => {
    //查询积分配置
    return request("/mallapi/pointsconfig", "get", null, false);
  },
  pointsShoppingMallPage: (data) => {
    //查询积分商城商品列表
    return request(
      "/mallapi/goodsspu/pointsShoppingMallPage",
      "get",
      data,
      false,
    );
  },

  couponUserSave: (data) => {
    //电子券用户领取
    const errModalHide = data.errModalHide ? true : false;
    return request(
      "/mallapi/couponuser",
      "post",
      {
        ...data,
        clickId: getApp().globalData.gdtVid || "",
      },
      true,
      errModalHide,
    )
      .then((res) => {
        const couponInfo = res.data;
        // 埋点成功
        senTrack("CouponReceive", {
          page_name: getCurrentTitle(0),
          coupon_id: data.couponId,
          coupon_name: couponInfo.name,
          coupon_type: "电子券",
          coupon_use_method:
            couponInfo.suitType == "1"
              ? "全部商品可用"
              : couponInfo.suitType == "2"
              ? "指定商品可用"
              : couponInfo.suitType == "3"
              ? "指定商品不可用"
              : couponInfo.suitType == "4"
              ? "指定商品可用"
              : "",
          effect_time: couponInfo.validBeginTime,
          expire_time: couponInfo.validEndTime,
          // 优惠券购买金额  电子券的领取没有购买金额
          coupon_buy_amount: 0,
          coupon_threshold_amount: couponInfo.premiseAmount,
          coupon_amount: couponInfo.reduceAmount || couponInfo.discount,
          is_success: res && res.code === 0,
          fail_reason: "",
        });
        return res;
      })
      .catch((err) => {
        // 埋点失败
        // 获取优惠券信息
        module.exports
          .getCouponInfo(data.couponId, false)
          .then((res) => {
            const couponInfo = res.data;
            senTrack("CouponReceive", {
              page_name: getCurrentTitle(0),
              coupon_id: data.couponId,
              coupon_name: couponInfo.name,
              coupon_type: "电子券",
              coupon_use_method:
                couponInfo.suitType == "1"
                  ? "全部商品可用"
                  : couponInfo.suitType == "2"
                  ? "指定商品可用"
                  : couponInfo.suitType == "3"
                  ? "指定商品不可用"
                  : couponInfo.suitType == "4"
                  ? "指定商品可用"
                  : "",
              effect_time: couponInfo.validBeginTime,
              expire_time: couponInfo.validEndTime,
              // 优惠券购买金额  电子券的领取没有购买金额
              coupon_buy_amount: 0,
              coupon_threshold_amount: couponInfo.premiseAmount,
              coupon_amount: couponInfo.reduceAmount || couponInfo.discount,
              is_success: false,
              fail_reason:
                (err && err.msg) || (err && err.message) || JSON.stringify(err),
            });
          })
          .catch((e) => {
            console.error(e);
          });

        throw err;
      });
  },

  couponUserPage: (data) => {
    //电子券用户列表
    return request("/mallapi/couponuser/page", "get", data, false);
  },
  offlinecouponUserPage: (data) => {
    //线下电子券用户列表
    return request("/mallapi/couponuser/entPage", "get", data, false);
  },
  orderCouponUserPage: (data) => {
    //用户符合条件电子券列表 order
    return request("/mallapi/couponuser/spuCoupon", "post", data, false);
  },
  couponInfoPage: (data) => {
    //电子券列表
    return request("/mallapi/couponinfo/page", "get", data, false);
  },
  getCouponInfo: (id, isSenTrack = true) => {
    //获取电子券详情
    return request("/mallapi/couponinfo/" + id, "get", null, false)
      .then((res) => {
        if (isSenTrack) {
          const couponInfo = res.data;
          senTrack("CouponDetailPageView", {
            page_name: getCurrentTitle(0),
            coupon_id: couponInfo.id,
            coupon_name: couponInfo.name,
            coupon_type: "电子券",
            coupon_use_method:
              couponInfo.suitType == "1"
                ? "全部商品可用"
                : couponInfo.suitType == "2"
                ? "指定商品可用"
                : couponInfo.suitType == "3"
                ? "指定商品不可用"
                : couponInfo.suitType == "4"
                ? "指定商品可用"
                : "",
            effect_time: couponInfo.validBeginTime,
            expire_time: couponInfo.validEndTime,
            // 优惠券购买金额  电子券的领取没有购买金额
            coupon_buy_amount: 0,
            coupon_threshold_amount: couponInfo.premiseAmount,
            coupon_amount: couponInfo.reduceAmount || couponInfo.discount,
          });
        }
        return res;
      })
      .catch((err) => {
        throw err;
      });
  },
  getOfflineCouponInfo: (id) => {
    //获取线下电子券详情
    return request("/mallapi/couponinfo/getEnt/" + id, "get", null, false);
  },
  bargainInfoPage: (data) => {
    //砍价列表
    return request("/mallapi/bargaininfo/page", "get", data, false);
  },
  bargainInfoGet: (data) => {
    //砍价详情
    return request("/mallapi/bargaininfo", "get", data, true);
  },
  bargainUserSave: (data) => {
    //发起砍价
    return request("/mallapi/bargainuser", "post", data, true);
  },
  bargainCutPage: (data) => {
    //帮砍记录
    return request("/mallapi/bargaincut/page", "get", data, true);
  },
  bargainUserGet: (id) => {
    //砍价记录详情
    return request("/mallapi/bargainuser/" + id, "get", null, false);
  },
  bargainUserPage: (data) => {
    //砍价记录列表
    return request("/mallapi/bargainuser/page", "get", data, true);
  },
  bargainCutSave: (data) => {
    //砍一刀
    return request("/mallapi/bargaincut", "post", data, true);
  },
  listEnsureBySpuId: (data) => {
    //通过spuID，查询商品保障
    return request("/mallapi/ensuregoods/listEnsureBySpuId", "get", data, true);
  },
  grouponInfoPage: (data) => {
    //拼团列表
    return request("/mallapi/grouponinfo/page", "get", data, false);
  },
  grouponInfoGet: (id) => {
    //拼团详情
    return request("/mallapi/grouponinfo/" + id, "get", null, true);
  },
  grouponUserPageGrouponing: (data) => {
    //拼团中分页列表
    return request("/mallapi/grouponuser/page/grouponing", "get", data, true);
  },
  grouponUserPage: (data) => {
    //拼团记录列表
    return request("/mallapi/grouponuser/page", "get", data, true);
  },
  grouponUserGet: (id) => {
    //拼团记录详情
    return request("/mallapi/grouponuser/" + id, "get", null, false);
  },

  seckillhallList: (date) => {
    //秒杀会场时间列表
    return request("/mallapi/seckillhall/list", "get", date, false);
  },

  // 当前秒杀的会场的商品
  nowSeckillSpus: (date) => {
    return request("/mallapi/seckillhall/nowSpus", "get", date, false);
  },

  seckillinfoPage: (data) => {
    //秒杀列表
    return request("/mallapi/seckillinfo/page", "get", data, false);
  },
  seckillInfoGet: (seckillHallInfoId) => {
    //秒杀详情
    return request(
      "/mallapi/seckillinfo/" + seckillHallInfoId,
      "get",
      null,
      true,
    );
  },
  wxTemplateMsgList: (data) => {
    //订阅消息列表
    return request("/mallapi/wxtemplatemsg/list", "post", data, false);
  },

  liveRoomInfoListByShop: (shopId) => {
    //获取店铺直播房间列表
    return request("/mallapi/wxmalive/roominfo/" + shopId, "get", null, true);
  },

  liveRoomInfoList: (data) => {
    //获取直播房间列表
    return request("/mallapi/liveInfo/page", "get", data, true);
  },
  customerServiceList: (shopId) => {
    //客服列表
    return request(
      "/mallapi/customerservice/list/" + shopId,
      "get",
      null,
      false,
    );
  },
  platformCustomerServiceList: () => {
    //平台客服列表
    return request("/mallapi/customerservice/platformList", "get", null, false);
  },
  pagedevise: (pageType) => {
    //首页组件配置
    return request(
      "/mallapi/pagedevise?pageType=" + pageType,
      "get",
      null,
      false,
    );
  },
  pagedeviseShop: (shopId) => {
    // 店铺组件配置
    return request(
      "/mallapi/pagedevise?pageType=2&shopId=" + shopId,
      "get",
      null,
      false,
    );
  },
  // pagedevisePage: microPageId => {
  // return request('/mallapi/pagedevise?pageType=1', 'get', null, false);
  // 微页面组件配置
  // return request('/mallapi/pagedevise?pageType=3&id=' + microPageId, 'get', null, false);
  // },

  //无需session
  pagedevisePage: (microPageId) => {
    // 微页面组件配置
    return request(
      "/mallapi/pagedevise/detail?pageType=3&id=" + microPageId,
      "get",
      null,
      false,
    );
  },

  newPersonApi: (data) => {
    //顾客活动标签查询
    return request(
      "/sco-customer-api/rest?method=sco.cust.tags.getByCust",
      "post",
      data,
      true,
    );
  },

  getJiguangConfig: (data) => {
    //获取极光参数
    return request("/mallapi/jiguang/config", "get", data, false);
  },
  getJiguangMessages: (data) => {
    //获取极光消息
    return request(
      "/mallapi/jiguang/messages?userName=" +
        data.userName +
        "&beginTime=" +
        data.beginTime +
        "&endTime=" +
        data.endTime,
      "get",
      null,
      false,
    );
  },
  articlePage: (data) => {
    //文章列表
    return request("/mallapi/articleinfo/page", "get", data, false);
  },
  articleGet: (id) => {
    //文章详情
    return request("/mallapi/articleinfo/" + id, "get", null, false);
  },
  articleGetGoods: (id) => {
    //文章商品
    return request("/mallapi/goodsspu/listByIds?ids=" + id, "get", null, false);
  },
  articleCategoryPage: (data) => {
    //文章分类列表
    return request("/mallapi/articlecategory/page", "get", data, false);
  },
  userSign: (data) => {
    //积分签到
    return request("/mallapi/signrecord/user", "post", data, false);
  },
  getUserCustAccntTot: (data) => {
    //获取可用、当前积分
    return request("/mallapi/userinfo/getUserCustAccntTot", "get", null, false);
  },
  getSignRecord: (data) => {
    //签到记录查询
    return request("/mallapi/signrecord/user", "get", data, false);
  },
  signConfigPage: (data) => {
    //获取签到积分记录
    return request("/mallapi/signconfig/page", "get", data, false);
  },
  distributionConfig: () => {
    //分销设置查询
    return request("/mallapi/distributionconfig", "get");
  },
  distributionuser: () => {
    //分销员查询
    return request("/mallapi/distributionuser", "get");
  },
  distributionuserPage: (data) => {
    //分销员查询
    return request("/mallapi/distributionuser/page", "get", data);
  },
  distributionorderPage: (data) => {
    //分销订单分页
    return request("/mallapi/distributionorder/page", "get", data);
  },
  distributionorderFrozenAmount: (data) => {
    //获取当前分销员的冻结金额
    return request("/mallapi/distributionorder/frozenAmount", "get", data);
  },
  distributionPromotionPage: (data) => {
    //分销 推广人统计
    return request("/mallapi/userinfo/page", "get", data);
  },
  userwithdrawRecordSave: (data) => {
    //分销 申请提现
    return request("/mallapi/userwithdrawrecord", "post", data, true);
  },
  userwithdrawRecordUpdate: (data) => {
    //分销 提现申请修改
    return request("/mallapi/userwithdrawrecord", "put", data, true);
  },
  userwithdrawRecordPage: (data) => {
    //分销 提现记录
    return request("/mallapi/userwithdrawrecord/page", "get", data);
  },
  userwithdrawRecord: (id) => {
    //分销 提现记录详情
    return request("/mallapi/userwithdrawrecord/" + id, "get", null);
  },
  userRecord: () => {
    //分销 用户消费总额记录
    return request("/mallapi/userrecord", "get", null);
  },
  wxAppConfig: (id) => {
    //获取wx一键授权的component_appid
    return request("/mallapi/wxapp/" + id, "get", null);
  },
  userAppraisesPage: (data) => {
    //我的评价
    return request("/mallapi/goodsappraises/page/user", "get", data);
  },
  getRecommendList: (data) => {
    //智能推荐 获取数据
    return request("/mallapi/ar/getRecommendList", "get", data);
  },

  getRecSpuCodeByUserId: (data) => {
    //松雷自己的智能推荐接口
    return request("/mallapi/recommendspu/getRecSpuCodeByUserId", "get", data);
  },
  dataController: (data) => {
    //智能推荐  数据采集
    return request(
      "/mallapi/ar/dataController",
      "post",
      {
        ...data,
        sceneId: "sy101,shop002",
      },
      false,
    );
  },
  getShopIdByUserId: (data) => {
    //通过客服id，获取店铺id
    return request("/mallapi/userinfo/getShopIdByUserId", "get", data);
  },
  getLastMessages: (data) => {
    //获取极光消息最后一条数据
    return request("/mallapi/jiguang/lastMessages", "get", data);
  },
  getActivityList: (data) => {
    // 消息_活动优惠表分页列表
    return request("/mallapi/msgactivitydiscount/page", "get", data);
  },
  getSelectNewMsgByType: (data) => {
    // 自定义消息_消息类型查询最新的一条消息
    return request(
      "/mallapi/msgactivitydiscount/selectNewMsgByType",
      "get",
      data,
    );
  },
  // shoppingPointSettlement: data => {
  // 	// 购物车点击结算时将选中的商品信息加入缓存
  // 	return request('/mallapi/shoppingcart/shoppingPointSettlement', 'post', data);
  // },
  getSpuListByDistributionMode: (data) => {
    // 根据配送方式获取对应商品列表
    return request(
      "/mallapi/goodsspu/getSpuListByDistributionMode",
      "post",
      data,
    );
  },
  getOrderGenerate: (data) => {
    //如果有分享人则带上分享人的user_code
    data = util.dataAddSharerUserCode(data);
    // 根据配送方式获取对应商品列表
    return request("/mallapi/orderinfo/orderGenerate", "post", data);
  },

  //根据店铺Id获取店铺自提列表
  getTaskAddr: (data) => {
    return request("/mallapi/shoptakeaddress/getTaskAddr", "get", data);
  },

  //获取用户二维码
  getCardList: (data) => {
    return request("/mallapi/userinfo/getCardList", "get", data);
  },

  // 秒杀装修组件 获取秒杀商品
  getSeckillhallProductList: (data) => {
    return request("/mallapi/seckillhall/nowList", "get", data);
  },

  // 根据物流单号获取物流信息
  getLogisticsRouteByNo: (data) => {
    return request("/mallapi/yddsend/logisticsRoute", "get", data);
  },

  // 根据商品id获取折扣信息 折扣活动
  getPromotionsInfoByGoodsSpuId: (data) => {
    return request("/mallapi/promotionsinfo/getListByGoodsSpuId", "get", data);
  },

  // 获取直接间分割序列，从这里开始以上是预告，以下是直播中或者历史
  // 这个接口里面有字段，获取平台客服的链接
  getLivingTag: () => {
    return request("/mallapi/wxuser/config", "get", {}, false);
  },

  postTimelineAdvertAction: (data) => {
    return request("/mallapi/goodsspu/commonUploadAction", "get", data);
  },
  // 根据活动id实现分享功能
  getActShare: (id) => {
    return request("/mallmarket/actmarket/share", "get", {
      actId: id,
    });
  },

  // 根据活动id获取大转盘详细信息
  getActInfo: (id) => {
    return request("/mallmarket/actmarket/actandprize", "get", {
      id,
    });
  },

  getSeckillCategory: () => {
    return request("/mallapi/seckillcategory/list", "get", null);
  },

  //钱包是否认证
  isAuthentication: () => {
    return request("/mallapi/purseUser/page", "get", null);
  },

  //钱包是否开户
  isAccount: () => {
    return request("/mallapi/purse/page", "get", null);
  },

  //上传认证信息
  UploadAuthentication: (data) => {
    return request("/mallapi/purseUser/authentication", "post", data, true);
  },

  //银行卡查询
  getCardBin: (id) => {
    return request("/mallapi/purse/purse-cardBin", "get", {
      tAcNo: id,
    });
  },

  //查询客户信息检查
  getCustInfoCheck: () => {
    return request("/mallapi/purse/purse-custInfoCheck", "get", null);
  },

  //钱包查詢職業列表
  getOccupationList: () => {
    return request("/mallapi/purse/purse-occupationList", "get", null);
  },

  //钱包查詢地址列表
  getAddrList: () => {
    return request("/mallapi/purse/purse-addrList", "get", null);
  },

  //钱包开户获取手机验证码
  getMobilePhoneCode: (data) => {
    return request("/mallapi/purse/purse-phoneCode", "get", data, true);
  },

  //获取钱包支付密码需要的随机数
  getRandomNumber: () => {
    return request("/mallapi/purse/purse-generateRand", "get", null, true);
  },

  //获取电子账户信息
  getAccountQuery: () => {
    return request("/mallapi/purse/purse-accountQuery", "get", null, true);
  },

  //获取电子账单明细
  getAcnoTrsJnlQuery: (data) => {
    return request("/mallapi/purse/purse-acnoTrsJnlQuery", "post", data, true);
  },

  //查询是否首次入金 first_inner_flag 0：否，1：是
  getPurse: () => {
    return request("/mallapi/purse/page", "get", true);
  },

  //首次入金 bank_inner_path 20用户输入验证码，00和70不用验证码
  getBankInnerPathQuery: () => {
    return request(
      "/mallapi/purse/purse-bankInnerPathQuery",
      "get",
      null,
      true,
    );
  },

  //根据入金状态传递需要数据 bankInnerPath，messageCode
  getBankInnerpathSign: (data) => {
    return request("/mallapi/purse/purse-bankInnerpathSign", "get", data, true);
  },

  //入金
  getBankInnerTransfer: (data) => {
    return request(
      "/mallapi/purse/purse-bankInnerTransfer",
      "post",
      data,
      true,
    );
  },

  //补充个人信息
  custInfoMainten: (data) => {
    return request("/mallapi/purse/purse-custInfoMainten", "post", data, true);
  },

  //获取银行卡名称
  getBankInnerpathSign: (data) => {
    return request("/mallapi/purse/purse-bankInnerpathSign", "get", data, true);
  },
  //砍价列表
  bargainInfoPage1: (id) => {
    return request("/mallapi/bargainhall/nowlist", "get", null);
  },

  //首页自动展示正在砍价中的两件商品
  bargainhallNowSpuList: () => {
    return request("/mallapi/bargainhall/nowlist", "get", null);
  },

  //可乐招新的接口
  getGiftList: (data) => {
    return request("/mallapi/userinfo/getGiftList", "get", data);
  },

  getcorqc: (data) => {
    return request("/mallapi/userinfo/receiveDisplay", "get", data, true);
  },

  //支付有礼
  giveUserRecord: (id) => {
    return request("/mallapi/giveUserRecord/" + id, "get", null, false);
  },

  //申请取消退款
  tkorder: (data) => {
    return request("/mallapi/orderrefunds/cancelrefund", "post", data);
  },
  // 获取配送时间
  deliverytimeperiod: () => {
    return request("/mallapi/deliverytimeperiod", "get", null, true);
  },
  //排行榜
  getRanking: (id) => {
    return request("/mallapi/rankInfo/" + id, "get", null, false);
    //联名员注册
  },
  brandvipmember: (data) => {
    return request("/mallapi/brandvipmember", "post", data);
  },
  // 查询关联的品牌信息
  getBrandVipMember: (brandId) => {
    return request("/mallapi/brandvipmember/getBrandVipMember", "get", {
      brandId,
    });
  },
  // 联名卡和会信息修改
  brandvipmembers: (data) => {
    return request("/mallapi/brandvipmember", "put", data, true);
  },
  goodsspu: (data) => {
    return request("/mallapi/goodsspu/page", "get", data, true);
  },
  // 获取我的卡包
  mumberilist: (userId) => {
    return request("/mallapi/brandvipmember/page", "get", {
      userId,
    });
  },
  deliverytimeperiod: (data) => {
    return request("/mallapi/deliverytimeperiod", "get", data);
  },

  //获取当前最新版本小程序是否需要强制更新再运行
  isForceUpdate: () => {
    return request("/slshop-h5/app/mpVersion.json", "get", null, false);
  },

  // 购车车获取优惠明细
  cartEstimatePrice: (data) => {
    data = util.dataAddSharerUserCode(data);
    return request(
      "/mallapi/shoppingcart/cartEstimatePrice",
      "get",
      data,
      true,
    );
  },

  //通过优惠券Id获取能使用该优惠券的商品
  getGoodsInfoByCouponId: (params) => {
    return request("/mallapi/couponinfo/getGoodsInfoByCouponId", "get", params);
  },

  // 我的订单---物流信息
  // mallapi/orderinfo/orderlogistics/deliverList
  logisticsDeliverList: (params) => {
    return request(
      "/mallapi/orderinfo/orderlogistics/deliverList",
      "get",
      null,
      true,
    );
  },
  // 物流单号
  LogisticsorderNo: (data) => {
    return request("/mallapi/orderlogistics", "put", data, true);
  },
  // 添加银行卡支付获取银行卡类型
  getCardDetail: (id) => {
    return request(
      "/mallapi/payBankCard/getCardDetail/" + id,
      "get",
      null,
      false,
    );
  },
  // 添加银行卡支付绑定接口
  bindBankCard: (data) => {
    return request("/mallapi/payBankCard/bindBankCard", "post", data);
  },
  // 添加银行卡支付获取验证码接口
  authorizeBankCard: (data) => {
    return request("/mallapi/payBankCard/authorizeBankCard", "post", data);
  },
  // 添加银行卡支付 获取可添加银行卡列表
  getPayBankList: (params) => {
    return request("/mallapi/paybank/getPayBank", "get", null, true);
  },
  // 获取已已签约的银行卡列表
  getPaybankaccount: (params) => {
    return request(
      "/mallapi/paybankaccount/getPaybankaccount",
      "get",
      null,
      true,
    );
  },
  // 解绑已签约的银行卡
  unbindBankCard: (params) => {
    return request("/mallapi/payBankCard/unbindBankCard", "get", params, true);
  },

  // 首页生日弹框信息展示
  getBirthDayDialogInfo(data) {
    return request(
      "/sco-customer-api/rest?method=sco.cust.activity.bdayactivity",
      "post",
      data,
      false,
      true,
    );
  },

  //快速添加购物车
  // defaultAdd: (data, goodsSpu) => {
  // 	return request("/mallapi/shoppingcart/defaultAdd", "post", data).then(res => {
  // 		return {
  // 			code: 0,
  // 			data:{
  // 				count: res.data
  // 			}
  // 		};
  // 	}).catch(err => {
  // 		console.error("==err===", err);
  // 		throw err;
  // 	});
  // },

  // 快速添加购物车
  defaultAdd: (data, goodsSpu) => {
    return request("/mallapi/shoppingcart/defaultAddNew", "post", data)
      .then((res) => {
        const promotion_type_list = [];
        if (goodsSpu.estimatedPriceVo?.promotionsDiscountPrice > 0) {
          promotion_type_list.push("折扣");
        }
        if (
          goodsSpu.estimatedPriceVo?.coupon > 0 ||
          goodsSpu.estimatedPriceVo?.discountPrice > 0
        ) {
          promotion_type_list.push("优惠券");
        }
        senGoods(
          "GoodsAddToCart",
          {
            page_name: getCurrentTitle(0),
            activity_id: "",
            //商品原价划线价
            goods_crossed_price: Number(
              goodsSpu.estimatedPriceVo?.originalPrice,
            ),
            // 到手价
            goods_strike_price: Number(
              goodsSpu.estimatedPriceVo?.estimatedPrice,
            ),
            //优惠类型
            promotion_type_list,
            // 是否是推荐商品
            is_recommend_goods: false,
            // 是否描述 秒杀入口进来判断
            is_flash_sale: false,
            sku_id: res.data.skuId,
            sku_name: res.data.specInfo || goodsSpu.name + "-单规格",
          },
          goodsSpu,
        );
        return res;
      })
      .catch((err) => {
        console.error("==err===", err);
        throw err;
      });
  },

  // 获取门店列表
  getstorelist: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.info.store",
      "post",
      data,
      true,
    );
  },
  //绑定车牌
  getbindcar: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.plate.bind",
      "post",
      data,
      true,
    );
  },
  // 获取车牌列表
  getcarlist: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.plate.search",
      "post",
      data,
      true,
    );
  },
  //查询会员专享
  Memberexclusive: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.info.customer",
      "post",
      data,
      true,
    );
  },
  //停车劵查询
  getlist: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.coupon.getlist",
      "post",
      data,
      true,
    );
  },
  //停车费用查询
  parkfee: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.charge.check",
      "post",
      data,
      true,
    );
  },
  //停车缴费
  parklists: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.charge.getlist",
      "post",
      data,
      true,
    );
  },
  // 缴费详情
  feedetail: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.charge.getinfo",
      "post",
      data,
      true,
    );
  },
  // 提交订单
  feesubmit: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.charge.submit",
      "post",
      data,
      true,
    );
  },
  // 提交订单
  parkPayOrder: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.charge.payorder",
      "post",
      data,
      true,
    );
  },
  // 解绑手机号
  delcar: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.plate.disbind",
      "post",
      data,
      true,
    );
  },
  //支付
  paywct: (data) => {
    return request("/mallapi/orderinfo/payOrder-zt", "post", data, true);
  },
  // 提交
  paysubmit: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.charge.submit",
      "post",
      data,
      true,
    );
  },
  // 取消订单
  restorder: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.charge.cancel",
      "post",
      data,
      true,
    );
  },
  // 获取群二维码
  wkCode: (params) => {
    return request(
      "/mallapi/advertisementinfo/detail/?bigClass=WK_QRCODE",
      "get",
      params,
      true,
    );
  },
  // 获取多张轮播(我的个人中心)
  listMineimg: (params) => {
    return request(
      "/mallapi/advertisementinfo/list/?bigClass=USER_CENTER",
      "get",
      params,
      true,
    );
  },
  // 获取配置的广告图片
  getAdvers: (params) => {
    return request("/mallapi/advertisementinfo/list/", "get", params, true);
  },
  // 获取多张轮播(签到页面)
  listScoreimg: (params) => {
    return request(
      "/mallapi/advertisementinfo/list/?bigClass=USER_SCORE",
      "get",
      params,
      true,
    );
  },
  // 获取自提的时间段
  gettime: (params) => {
    return request("/mallapi/deliverytimeperiod/oneself", "get", params, true);
  },
  // 店铺页面展示的分类，现在展示品牌的分类
  brandCategoryShopTree: (data) => {
    //店铺商品分类tree查询
    return request("/mallapi/brandcategoryshop/tree", "get", data, true);
  },
  // 推荐混排-数据列表
  getRecommendMixedList: (data) => {
    return request("/mallapi/product/page", "get", data, false);
  },
  // 店铺页面展示的分类，现在展示品牌的分类
  getWayBillTokenByOrderId: (id) => {
    return request(
      "/mallapi/orderlogistics/getWayBillTokenByOrderId?orderInfoId=" + id,
      "get",
      null,
      false,
    );
  },
  //二回页面
  backflow: (data) => {
    return request("/mallapi/backflow", "get", data, false);
  },
  //调查问卷 保存
  saveAnswer: (data) => {
    return request(
      "/sco-customer-admin/wxQuesquestionnaire/saveAnswer",
      "post",
      data,
      true,
    );
  },
  //停车场广告
  parklist: (params) => {
    return request(
      "/mallapi/advertisementinfo/list/?bigClass=park",
      "get",
      params,
      true,
    );
  },
  // 增加巡车接口   sco-park-api/rest?method=sco.park.info.noplate
  noelate: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.info.search",
      "post",
      data,
      true,
    );
  },
  //按车牌寻车
  carinfo: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.info.carinfo",
      "post",
      data,
      true,
    );
  },
  //根据时间查找车牌
  entrytime: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.info.entrytime",
      "post",
      data,
      true,
    );
  },
  //判断是否开通钱包零钱
  pursepage: (data) => {
    return request("/mallapi/purse/page", "get", data, false);
  },
  //获取验证码
  getcode: (data) => {
    return request("/mallapi/purse/signAgreementPayment", "get", data, false);
  },
  testverify: (data) => {
    return request(
      "/mallapi/purse/authorizeAgreementPayment",
      "post",
      data,
      false,
    );
  },

  // 提醒发货
  remindShipment: (data) => {
    return request("/mallapi/orderlogistics/remindShipping", "put", data, true);
  },
  // 退换/售后
  orderRefundsList: (data) => {
    return request("/mallapi/orderrefunds/page", "get", data, true);
  },
  // 退换/售后--订单删除
  orderRefundsDel: (id) => {
    return request("/mallapi/orderrefunds/" + id, "delete", null, false);
  },
  // 退换/售后--订单详情查询
  orderRefundsGet: (id) => {
    return request("/mallapi/orderrefunds/" + id, "get", null, false);
  },
  // 拼团购会场--列表
  groupPurchaseHallList: (data) => {
    return request("/mallapi/grouppurchasehall/nowlist", "get", data, true);
  },
  // 退换/售后--无牌停车场
  unlicensedvehicle: (data) => {
    return request(
      "/sco-park-api/rest?method=sco.park.charge.channel",
      "post",
      data,
      true,
    );
  },
  // 查询订单
  searchpayorder: (data) => {
    return request(
      "/sco-activity-api/rest?method=sco.activity.api.exec.payorder",
      "post",
      data,
      true,
    );
  },
  // 查询跟团纪律
  groupRecords: (data) => {
    return request(
      "/mallapi/grouppurchasehall/groupRecords",
      "get",
      data,
      true,
    );
  },

  // 个人中心随机展示一张优惠券
  getRecommendCoupon: (data) => {
    return request(
      "/mallapi/couponuser/recommended-coupons",
      "get",
      data,
      true,
    );
  },

  // 支付成功的回传给接口，由于后端拿微信的回调比较慢，
  frontPayStatusCallback: (data) => {
    return request(
      "/mallapi/paypaymentrecords/frontPayStatusCallback",
      "post",
      data,
      true,
    );
  },

  // 短链接查询出明文
  // parseUrlLink: data => {
  // 	return request('/mallapi/wxqrcode/queryUrlLink', 'post', data, true);
  // },

  // 根据扫到的码解析出来要跳转的路径
  qrcodequery: (data) => {
    return request("/mallapi/qrcodequery/query", "get", data, true);
  },

  // 批量添加购物车
  batchAdd: (data) => {
    return request("/mallapi/shoppingcart/batchAdd", "post", data)
      .then((res) => {
        const list = res.data;
        if (list && list.length > 0) {
          list.forEach((item) => {
            const { goodsSku, goodsSpu } = item;
            const promotion_type_list = [];
            if (goodsSpu.estimatedPriceVo?.promotionsDiscountPrice > 0) {
              promotion_type_list.push("折扣");
            }
            if (
              goodsSpu.estimatedPriceVo?.coupon > 0 ||
              goodsSpu.estimatedPriceVo?.discountPrice > 0
            ) {
              promotion_type_list.push("优惠券");
            }
            senGoods(
              "GoodsAddToCart",
              {
                forward_source: getCurrentTitle(1),
                source_module: "订单列表再次购买",
                activity_id: "",
                sku_id: goodsSku.id,
                sku_name: goodsSku.specInfo || goodsSpu.name + "-单规格",
                //商品原价划线价
                goods_crossed_price: Number(
                  goodsSpu.estimatedPriceVo?.originalPrice,
                ),
                // 到手价
                goods_strike_price: Number(
                  goodsSpu.estimatedPriceVo?.estimatedPrice,
                ),
                //优惠类型
                promotion_type_list,
                // 是否是推荐商品
                is_recommend_goods: false,
                // 是否描述 秒杀入口进来判断
                is_flash_sale: false,
              },
              goodsSpu,
            );
          });
        }
        return res;
      })
      .catch((err) => {
        throw err;
      });
  },
};
