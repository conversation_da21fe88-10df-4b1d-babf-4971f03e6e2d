
<template>
  <transition name="message-animation">
    <view :style="messageStyle">
      <view 
        v-if="showMessage" 
        class="top-message" 
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
        :style="[{
          top:CustomBar+'px',
          display: 'block' 
          }]">
        <view 
          v-if="couponUserInfo"
          @click.stop="handleToPage" 
          style="
            background-color: #FFFFFF;
            border-radius: 20rpx;
          "
          >
          <view class="flex radius text-white main electronic-coupons align-center">
            <view 
              class="discount-padding-sm shadow-blur radius" 
              style="width: 175rpx;height: 175rpx; z-index: 1; padding-right: 0;">
              <image 
                :style="{
                  height: '150rpx', 
                  width:'150rpx',
                }"
                :src="couponUserInfo.couponPicUrl || defaultImg" />
            </view>

            <view class="flex-sub discount-padding-sm radius flex flex-direction justify-between">
              <view class="validity flex justify-between" style="align-items: center;color: #9b9c9d">
                <view class="text-xs">
                  <!-- 有效期至{{ couponUserInfo.expDate }} -->
                  <view class="text-left text-bold text-df text-black">{{ couponUserInfo.couponName }}</view>
                  <view class="text-left text-black">{{couponUserInfo.effDate.split(' ')[0]}}~{{couponUserInfo.expDate.split(' ')[0]}}</view>
                  <view class="text-left text-black">{{couponUserInfo.effDate.split(' ')[1]}}~{{couponUserInfo.expDate.split(' ')[1]}}</view>
                </view>
                <view class="to-use-btn">
                  去使用
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </transition>
</template>

<script>
import { accMul } from "@/utils/numberUtil";
import { mapState } from 'vuex' 
import { topPop } from 'api/activity.js';
export default {
  data() {
    return {
      message: '顶部消息', // 信息内容
      showMessage: false, // 是否显示信息
      messageStyle: `transform: translate(0rpx, 0rpx)`, // 信息样式
      touchStartX: 0, // 触摸开始的X坐标
      touchStartY: 0, // 触摸开始的Y坐标
      touchMoveX: 0, // 触摸移动的X坐标
      touchMoveY: 0, // 触摸移动的Y坐标
      originalX: 0, // 初始X坐标
      originalY: 0, // 初始Y坐标
      couponUserInfo:{
        // amount: 1000,
        // batchId: "1808448553964990464",
        // canPresent: "Y",
        // canRefund: "N",
        // costAmount: 0,
        // costPoint: 0,
        // couponName: "金鹏满减12月",
        // couponNo: "202407000001289811",
        // couponPicUrl: this.$imgUrl('1/material/cbcb7f07-f210-48ca-aaa4-0d90a725a12a.jpg'),
        // couponTypeCode: "MZ2024010004",
        // couponTypeName: "金鹏满减12月",
        // custId: "**********",
        // effDate: "2024/01/01 01:01:01",
        // evtscd: "20230008",
        // expDate: "2024/12/31 23:59:59",
        // facemode: "2",
        // facevalue: 1000,
        // occurDate: "2024/07/03 00:00:00",
        // popFlag: "0",
        // presentTimes: 0,
        // promotionTypeCode: "0",
        // rowNo: 1,
        // showValue: "Y",
        // simpleUsageRule: "测试---使用说明",
        // srcDate: "2024/07/03 18:31:49",
        // status: "01",
        // useType: "05",
      }
    };
  },
  computed: {
			...mapState([
				'windowWidth',
				'pixelRatio',
				'CustomBar',
				'StatusBar',
				'menuHeight',
				'menuWidth',
				'leftMenuWidth',
				'leftMenuHeight'
			]),

      redPacketCoupon() {
				//业务要求按真实金额展示，不要截取
				// let realVal = Number(this.couponUserInfo.amount).toFixed(2)
        if (this.couponUserInfo) return this.couponUserInfo.amount || this.couponUserInfo.facevalue
			},

			defaultImg() {
        if (this.couponUserInfo) {
          const {
            status
          } = this.couponUserInfo
          if (status == '01' || status == '05') {
            return this.$imgUrl('coupon/discount-offline.png')
          }
          if (status == '02' || status == '03' || status == '04' || status == '06' || status == '07') {
            return this.$imgUrl('coupon/discount-offline2.png')
          }
        }
			},

			useTypeText() {
        if (this.couponUserInfo) {
          const {
            useType
          } = this.couponUserInfo
          if (useType == '01') {
            return '红包券'
          } else if (useType == '02') {
            return '面值券'
          } else if (useType == '03') {
            return '礼品券'
          } else if (useType == '04') {
            return '折扣券'
          } else if (useType == '05') {
            return '满减券'
          } else if (useType == '09') {
            return '停车券'
          } else if (useType == '10') {
            return '运费券'
          } else {
            return ''
          }
        }
			},

			isUseCoupon() {
        if (this.couponUserInfo){
            const {
              status,
              srcDate
            } = this.couponUserInfo
            if (status != '01') return false
            if (status == '01' && !srcDate) return false
            const startTime = new Date(srcDate).getTime()
            const currentTime = new Date().getTime()
            const timeElapsed = (currentTime - startTime) / (1000 * 60 * 60); // 计算时间差（小时）
            return timeElapsed <= 72; // 判断时间是否小于等于72小时
          }
        }
		},

  filters: {
		accMulPrice: function(value) {
			return accMul(value, 10)
		},
	},

  methods: {
    // 触摸开始
    onTouchStart(event) {
      this.touchStartX = event.touches[0].clientX;
      this.touchStartY = event.touches[0].clientY;
      this.originalX = parseInt(this.messageStyle.transform?.replace('translateX(', '')?.replace('rpx)', '')) || 0;
      this.originalY = parseInt(this.messageStyle.transform?.replace('translateY(', '')?.replace('rpx)', '')) || 0;
    },

    // 触摸移动
    onTouchMove(event) {
      this.touchMoveX = event.touches[0].clientX;
      this.touchMoveY = event.touches[0].clientY;
      // let distanceX = this.touchMoveX - this.touchStartX;
      // let distanceY = this.touchMoveY - this.touchStartY;
      // console.log("distanceY",distanceY,this.originalY + distanceY);
      // console.log("distanceX",distanceX,this.originalX + distanceX);

      // this.messageStyle = `transform: translate(${this.originalX + distanceX}rpx, ${this.originalY + distanceY}rpx)`
    },

    // 触摸结束
    onTouchEnd() {
      let distanceX = this.touchMoveX - this.touchStartX;
      let distanceY = this.touchMoveY - this.touchStartY;
      if (distanceY < -50 && Math.abs(distanceY) > Math.abs(distanceX)) {
        this.hideMessage(); // 上滑一定距离后关闭
      }
    },

    // 获取劵信息
    async infoCoupon(parameter) {
			try {
					const {data} = await topPop({triggerType:parameter});
					this.couponUserInfo = data;
          if(this.couponUserInfo){
            this.showMessage = true;
          }
          console.log("this.couponUserInfo",this.couponUserInfo);
          
			} catch (e) {} finally {
			}
		},

    handleToPage() {
			if (this.couponUserInfo.status === '08' && this.couponUserInfo.activeDesc) {
				uni.showModal({
					title: '提示',
					content: this.couponUserInfo.activeDesc || '请仔细查看激活说明',
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/coupon/coupon-offline-detail-plus/index?id=' + this
									.couponUserInfo.couponNo
							})
						}
					}
				});
			} else {
				uni.navigateTo({
					url: '/pages/coupon/coupon-offline-detail-plus/index?id=' + this.couponUserInfo.couponNo
				})
			}
		},

    // 设置消息内容和持续时间
    setMessage(data, duration) {
      this.infoCoupon(data);
      this.messageStyle = {
        animation: `slide-in ${duration / 1000}s`
      };
      // 消息持续时间,单位:ms,设置为0时,不自动收起消息
      if (duration !== 0) {
        setTimeout(() => {
          this.hideMessage();
        }, duration);
      }
    },

    // 隐藏消息
    hideMessage() {
      this.couponUserInfo = {};
      this.showMessage = false;
      this.messageStyle = {};
    }
  }
};
</script>

<style scoped>
.message-animation-enter-active, .message-animation-leave-active {
  transition: all 0.5s;
}
.message-animation-enter, .message-animation-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

  .top-message {
    position: fixed;
    top: 90rpx;
    left: 0;
    width: 100%;
    /* background-color: #FFF1DC; */
    padding: 10rpx;
    text-align: center;
    animation-fill-mode: forwards; /* 动画结束后保持最后的样式 */
    transition: transform 0.3s; /* 添加过渡效果 */
    /* border-radius: 20rpx; */
  }

  .to-use-btn {
		width: 152rpx;
		height: 50rpx;
		/* background: #FFFFFF; */
		border-radius: 24rpx;
		border: 1rpx solid #FFFFFF;
		font-weight: 500;
		font-size: 22rpx;
		color: #FFFFFF;
		line-height: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
    background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
	}

	.label-give {
		position: absolute;
		width: 62rpx;
		height: 51rpx;
		background-color: #D2A688;
		text-align: center;
		border-radius: 6rpx;
		line-height: 44rpx;
		clip-path: polygon(0 0, 100% 0, 100% 100%, 50% 42rpx, 0 100%);
		top: 0;
		right: 0;
	}

	.label-give-load {
		position: absolute;
		width: 106rpx;
		height: 40rpx;
		top: 0;
		right: 0;
		/* background-color: #D0D0D0; */
		border-radius: 0 6rpx 0 20rpx;
		text-align: center;
	}
</style>
