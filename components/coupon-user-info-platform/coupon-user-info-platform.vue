<template>
	<view>
		<!-- 代金券 -->
		<view class="flex radius text-white main electronic-coupons" v-if="couponUserInfo.type == '1'">
			<view class="flex-twice padding-sm shadow-blur radius t1-r">

				<view class="flex">
					<view class="flex-sub flex">
						<text class="text-price text-xl margin-top" v-if="couponUserInfo.giveType =='1'"></text>
						<text v-if="couponUserInfo.giveType =='1'"
							class="text-sl overflow-1 number text-bold margin-left-xs">{{couponUserInfo.reduceAmount}}</text>
						<text v-if="couponUserInfo.giveType =='2'" class="text-xl overflow-1  margin-left-xs">满赠</text>
					</view>
					<view class="discount">
						<view class="text-xs overflow-1">满{{couponUserInfo.premiseAmount}}元可用</view>
						<view class="text-xs">
							{{couponUserInfo.suitType == '1' ? '全部商品可用' : couponUserInfo.suitType == '2' ? '指定商品可用' : couponUserInfo.suitType == '3' ? '指定类目可用' : ''}}
						</view>
					</view>
				</view>
				<view class="validity margin-top-sm text-center" style="margin-top: -20rpx;">
					<view class="text-xs">{{couponUserInfo.superposition=='2'?"与其他券不可叠加使用":"与其他可叠加使用"}}</view>

					<view class="text-xs">有效期至{{couponUserInfo.validEndTime}}</view>
				</view>
			</view>
			<view class="flex-sub padding-sm shadow-blur radius text-center t1-l ">
				<view class="ticket ">代金券</view>
				<navigator hover-class="none" :url="'/pages/goods/goods-list/index?couponUserId=' + couponUserInfo.id"
					class="cu-btn round text-bold bg-white sm margin-top-sm"
					v-if="toUse && couponUserInfo.status == '0'">前去使用</navigator>
				<view class="padding-xs round" v-if="couponUserInfo.status == '1'">已使用</view>
				<view class="padding-xs round" v-if="couponUserInfo.status == '2'">已过期</view>
			</view>
		</view>
		<!-- 折扣券 -->
		<view class="flex radius text-white main electronic-coupons" v-if="couponUserInfo.type == '2'">
			<view class="flex-twice padding-sm shadow-blur radius t2-r">

				<view class="flex">
					<view class="flex-sub flex">
						<text class="text-sl text-bold overflow-1 number">{{couponUserInfo.discount}}</text>
						<text class="text-sm margin-top-lg margin-left-xs">折</text>
					</view>
					<view class="discount">
						<view class="text-xs overflow-1">订单满{{couponUserInfo.premiseAmount}}元可使用</view>
						<view class="text-xs">
							{{couponUserInfo.suitType == '1' ? '全部商品可用' : couponUserInfo.suitType == '2' ? '指定商品可用' : couponUserInfo.suitType == '3' ? '指定类目可用' : ''}}
						</view>
					</view>
				</view>
				<view class="validity margin-top-sm text-center">
					<view class="text-xs">{{couponUserInfo.superposition=='2'?"与其他券不可叠加使用":"与其他可叠加使用"}}</view>
					<view class="text-xs">有效期至{{couponUserInfo.validEndTime}}</view>
				</view>
			</view>
			<view class="flex-sub padding-sm shadow-blur radius text-center t2-l">
				<view class="ticket ">折扣券</view>
				<navigator hover-class="none" :url="'/pages/goods/goods-list/index?couponUserId=' + couponUserInfo.id"
					class="cu-btn round text-bold bg-white sm margin-top-sm"
					v-if="toUse && couponUserInfo.status == '0'">前去使用</navigator>
				<view class="padding-xs text-bold round" v-if="couponUserInfo.status == '1'">已使用</view>
				<view class="padding-xs text-bold round" v-if="couponUserInfo.status == '2'">已过期</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {};
		},

		components: {},
		props: {
			couponUserInfo: {
				type: Object,
				default: () => ({})
			},
			toUse: {
				type: Boolean,
				default: true
			}
		},
		methods: {}
	};
</script>
<style scoped>
	.electronic-coupons {
		height: 200rpx
	}

	.t1-r {
		background: radial-gradient(circle at top right, transparent 5px, #f37b1d 0) top right, radial-gradient(circle at bottom right, transparent 5px, #f37b1d 0) bottom right;
		background-size: 100% 60%;
		background-repeat: no-repeat;
	}

	.t1-l {
		background: radial-gradient(circle at top left, transparent 5px, #f37b1d 0) top left, radial-gradient(circle at bottom left, transparent 5px, #f37b1d 0) bottom left;
		background-size: 100% 60%;
		background-repeat: no-repeat;
		border-left: 2rpx dashed rgba(255, 255, 255, .3);
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.store {
		margin-top: -20rpx;
	}

	.number {
		font-size: 68rpx;
	}

	.discount {
		margin: auto;
		margin-left: -50rpx;
		padding: -50rpx;
	}

	.t2-r {
		background: radial-gradient(circle at top right, transparent 5px, #39b54a 0) top right, radial-gradient(circle at bottom right, transparent 5px, #39b54a 0) bottom right;
		background-size: 100% 60%;
		background-repeat: no-repeat;
	}

	.t2-l {
		background: radial-gradient(circle at top left, transparent 5px, #39b54a 0) top left, radial-gradient(circle at bottom left, transparent 5px, #39b54a 0) bottom left;
		background-size: 100% 60%;
		background-repeat: no-repeat;
		border-left: 2rpx dashed rgba(255, 255, 255, .3);
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
