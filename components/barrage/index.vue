<template>
	<view class="vastwu-barrage" :style="{width:width,height:height}">
		<block v-for="(item,index) in items" :key="index">
			<!-- 每个盒子不一定只放一个 -->
			<!--  #ifdef MP-WEIXIN -->
			<view class="barrages" :style="{top: `${trackList[index]}rpx`,color: item.color,
				  		  animation: `mymove ${Number(item.time)*item.textList.length}s linear infinite ${item.paused}`
				  		  }">
				<view class="barrage" v-for="(tx,i) in item.textList" :key="i">
					{{tx}}
				</view>
			</view>
			<!--  #endif-->

			<!--  #ifndef MP-WEIXIN -->
			<view class="barrages" :style="{top: `${trackList[index]}rpx`,color: item.color,
				  		  }">
				<view class="barrage" v-for="(tx,i) in item.textList" :key="i">
					{{tx}}
				</view>
			</view>
			<!--  #endif-->
		</block>
	</view>
</template>

<script>
	export default {
		name: 'vastwu-barrage',
		props: {
			trackList: {
				type: Array,
				default: function() {
					return [0, 70]
				}
			},
			minTime: {
				type: Number,
				default: 3
			},
			maxTime: {
				type: Number,
				default: 8
			},
			width: {
				type: String,
				default: '100%'
			},
			height: {
				type: String,
				default: '100%'
			}
		},
		data() {
			return {
				items: [],
				values: '',
				textIndex: 0
			}
		},
		created() {
			this.values = this.items.length
		},
		methods: {
			add(text = '', time = this.randomNum(this.minTime, this.maxTime)) {
				if (this.items.length >= this.trackList.length) {
					if (this.textIndex < this.trackList.length) {
						this.items[this.textIndex].textList.push(text);
						this.textIndex++;
					} else {
						this.textIndex = 0
						this.items[this.textIndex].textList.push(text);
						this.textIndex++;
					}
				} else {
					this.items.push({
						textList: [text],
						time,
						color: '#ffffff',
						paused: '',
					});
				}
			},
			init(items = []) {
				this.items = [];
				let i = 0;
				this.values = items.length;

				for (let i = 0; i < items.length; i++) {
					let time = 10;
					time = this.randomNum(this.minTime, this.maxTime);
					this.add(items[i], time);
				}
			},
			stop(e, item) {
				item.paused = 'pause'
			},
			go(e, item) {
				item.paused = ''
			},
			//生成从minNum到maxNum的随机数
			randomNum(minNum, maxNum) {
				switch (arguments.length) {
					case 1:
						return parseInt(Math.random() * minNum + 1, 10);
						break;
					case 2:
						return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
						break;
					default:
						return 0;
						break;
				}
			}
		}
	}
</script>

<style>
	.barrages {
		position: absolute;
		white-space: nowrap;
		animation: mymove 10s linear infinite;
		animation-timing-function: linear;
		-webkit-animation-timing-function: linear;
		animation-fill-mode: infinite;

		-webkit-backface-visibility: hidden;
		-moz-backface-visibility: hidden;
		-ms-backface-visibility: hidden;
		backface-visibility: hidden;

		-webkit-perspective: 1000;
		-moz-perspective: 1000;
		-ms-perspective: 1000;
		perspective: 1000;
		/* Other transform properties here */
	}

	.barrage {
		position: relative;
		padding: 10rpx 50rpx 10rpx 65rpx;
		border-radius: 40rpx;
		/* background-color: #2b71d6;
		opacity: .9; */
		margin-right: 50rpx;
		display: inline-block;
	}

	.vastwu-barrage {
		z-index: 3;
		position: relative;
		overflow: hidden;
	}

	@keyframes mymove {
		from {
			/* left: 100%; */
			transform: translateX(750rpx);
		}

		to {
			/* left: -120%; */
			transform: translateX(-100%);
		}
	}

	@-moz-keyframes mymove

	/* Firefox */
		{
		from {
			/* left: 100%; */
			transform: translateX(750rpx);
		}

		to {
			/* left: -120%; */
			transform: translateX(-100%);
		}
	}

	@-webkit-keyframes mymove

	/* Safari and Chrome */
		{
		from {
			/* left: 100%; */
			transform: translateX(750rpx);
		}

		to {
			/* left: -120%; */
			transform: translateX(-100%);
		}
	}

	@-o-keyframes mymove

	/* Opera */
		{
		from {
			/* left: 100%; */
			transform: translateX(750rpx);
		}

		to {
			/* left: -120%; */
			transform: translateX(-100%);
		}
	}
</style>