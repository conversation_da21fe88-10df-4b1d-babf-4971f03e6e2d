<template>
	<view class="img-box" :class="{ animate: showAnimate }">
		<image class="main-img" src="http://img.songlei.com/lottery/order_btn.png" />
		<view class="strong-light"></view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			showAnimate: false
		};
	},
	mounted() {
		setTimeout(() => {
			this.showAnimate = true;
		}, 100);
	}
};
</script>

<style scoped>
/* 基础样式 */
.container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
}

.img-box {
	position: relative;
	overflow: hidden;
	perspective: 1000px; /* 增强立体感 */
}

.main-img {
	width: 462rpx;
	height: 174rpx;
	transform: translateY(100%) scale(0.3);
	opacity: 0;
	filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5)); /* 添加光影效果 */
}

.strong-light {
	position: absolute;
	top: 10%;
	left: -100%;
	width: 20%;
	height: 80%;
	background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%,rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 1) 100%);
	transform: skewX(-20deg);
	opacity: 0;
}

/* 动画组合 */
.animate .main-img {
	animation: enter 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards, bounce 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0.6s forwards;
}

.animate .strong-light {
	animation: lightSweep 3s linear 1.6s infinite;
}

/* 关键帧动画 */
@keyframes enter {
	0% {
		transform: translateY(100%) scale(0.3);
		opacity: 0;
	}
	100% {
		transform: translateY(0) scale(1);
		opacity: 1;
	}
}

@keyframes bounce {
	0% {
		transform: scale(1);
	}
	30% {
		transform: scale(1.15);
	}
	60% {
		transform: scale(0.95);
	}
	80% {
		transform: scale(1.05);
	}
	100% {
		transform: scale(1);
	}
}

@keyframes lightSweep {
	0% {
		left: -150%;
		opacity: 0;
	}
	20% {
		opacity: 1;
	}
	80% {
		opacity: 1;
	}
	100% {
		left: 1050%;
		opacity: 0;
	}
}
</style>
