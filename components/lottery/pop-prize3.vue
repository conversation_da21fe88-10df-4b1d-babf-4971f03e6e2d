<template>
  <view class="container">
    <!-- 文案奖励 -->
    <view>
      <image
        :src="'http://img.songlei.com/lottery/fireworksTitle2.gif' | formatImg750"
        mode="widthFix"
        :style="{
          width: '100%',
          minHeight: '100%',
          display: 'block',
        }"
      ></image>
    </view>

    <!-- 额外奖励 -->
    <view style="padding-right: 95rpx; height: 96rpx" class="flex justify-end">
      <image
        v-if="actInfo.actInfoSubsidyVO"
        class="icon"
        :class="{ 'icon-animation': iconVisible, 'shake': iconShaking }"
        :src="'http://img.songlei.com/lottery/icon.png'"
        mode="widthFix"
        :style="{
          width: '180rpx',
          minHeight: '96rpx',
          display: 'block',
          transform: iconTransform,
        }"
      ></image>
    </view>

    <!-- 积分和优惠 -->
    <view class="flex margin-lr margin-top-xs" style="height: 298rpx;">
      <!-- 积分劵 -->
      <view style="width: 307rpx;height: 298rpx;position: relative;" :class="{'margin-auto': !actInfo.actInfoSubsidyVO}">
        <view 
          v-if="pricesVisible"
          class="envelopeprice text-bold"
          :class="{ 'fade-in': envelopeVisible && pricesVisible, 'slide-in-left': envelopeVisible }"
          style="position: absolute; z-index: 1; display: flex; flex-direction: column; align-items: center; justify-content: center; height: 70%; width: 100%;">
          <view style="color: red;" :style="{
			  fontSize: actInfo.objType == 7? '55rpx': '28rpx'
		  }">{{actInfo.objType == 7?actInfo.realValue:actInfo.prizeName }}</view>
          <view v-if="actInfo.objType == 7">相当于￥{{actInfo.redPacketAmount}}</view>
        </view>
        <image
          class="envelope"
          :class="{ 'slide-in-left': envelopeVisible }"
          :src="objTypeMap[actInfo.objType]"
          mode="widthFix"
          :style="{
            width: '307rpx',
            minHeight: '298rpx;',
            display: 'block',
          }"
        ></image>
      </view>

      <!-- 加号 -->
      <view v-if="actInfo.actInfoSubsidyVO" style="height: 298rpx;padding-top: 110rpx;margin: auto;">
        <image
          class="plus"
          :class="{ 'slide-in-right': pointsVisible }"
          :src="'http://img.songlei.com/lottery/plus.png'"
          mode="widthFix"
          :style="{
            width: '77rpx',
            minHeight: '77rpx',
            display: 'block',
          }"
        ></image>
      </view>

      <!-- 优惠卷 -->
      <view v-if="actInfo.actInfoSubsidyVO" style="width: 307rpx;height: 298rpx;position: relative;">
        <view 
          v-if="pricesVisible"
          class="pointsprice"
          :class="{ 'fade-in': pointsVisible && pricesVisible }"
          style="position: absolute; z-index: 1; display: flex; flex-direction: column; align-items: center; justify-content: center; height: 70%; width: 100%;">
          <view style="color: #003499;">
            <text style="font-size: 55rpx;" class="text-bold">{{actInfo.actInfoSubsidyVO.subsidyPrice}}</text>
            <text style="font-size: 42rpx;">元</text>
          </view>
          <view style="color: #003499;">
            {{actInfo.actInfoSubsidyVO.subsidyName}}
          </view>
        </view>
        <image
          class="points"
          :class="{ 'slide-in-right': pointsVisible }"
          :src="'http://img.songlei.com/lottery/points.png'"
          mode="widthFix"
          :style="{
            width: '307rpx',
            minHeight: '298rpx;',
            display: 'block',
          }"
        ></image>
      </view>
    </view>

    <!-- 下单按钮 -->
    <view @click="handleOrder" style="width: 462rpx;margin: 120rpx auto;">
      <image
        :src="'http://img.songlei.com/lottery/btn2.gif'"
        mode="widthFix"
        :style="{
          width: '100%',
          minHeight: '174rpx',
          display: 'block',
        }"
      ></image>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    actInfo: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },

  data() {
    return {
      envelopeVisible: false,
      pointsVisible: false,
      iconVisible: false,
      iconShaking: false,
      iconTransform: 'translateY(50px)', // 初始位置在下方
      pricesVisible: false, // 控制价格信息的显示
      objTypeMap:{
        '1': 'http://img.songlei.com/lottery/1gift.png',//赠品
        // '2': '虚拟物品',
        '3': 'http://img.songlei.com/lottery/3coupons.png',//优惠劵
        '4': 'http://img.songlei.com/lottery/4points.png',//积分
        // '5': '谢谢参与',
        '6': 'http://img.songlei.com/lottery/6offline-coupons.png',//线下优惠券
        // '7': 'http://img.songlei.com/lottery/7gift-card.png',//礼品卡
        '7': 'http://img.songlei.com/lottery/7points-red.png',//积分红包
      }
    };
  },

  mounted() {
    this.startAnimations();
  },

  methods: {
    // 点击下单
    handleOrder() {
      // 处理下单逻辑
      this.$emit('toOrder');
    },

    // 动画开始
    startAnimations() {
      // 开始 envelope 和 points 动画
      this.envelopeVisible = true;
      this.pointsVisible = true; // 同时开始 points 动画
      setTimeout(() => {
        this.iconVisible = true; // 开始 icon 动画
        this.iconTransform = 'translateY(0)'; // 恢复到原位
        setTimeout(() => {
          this.startShake(); // 开始颤抖动画
          this.pricesVisible = true; // 显示价格信息
        }, 500); // 根据需要调整时间
      }, 1000); // 根据需要调整时间
    },

    startShake() {
      this.iconShaking = true; // 开始颤抖
      setTimeout(() => {
        this.iconShaking = false; // 停止颤抖
      }, 1000); // 颤抖持续时间
    }
  }

}
</script>

<style scoped>
.margin-auto {
  margin: auto;
}
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-items: center;
  justify-content: center;
}

.slide-in-left {
  animation: slideInLeft 1s forwards;
}

.slide-in-right {
  animation: slideInRight 1s forwards;
}

.fade-in {
  animation: fadeIn 1.5s forwards;
}

.icon-animation {
  transition: transform 1s ease; /* 添加平滑过渡 */
}

.shake {
  animation: shake 0.5s forwards; /* 颤抖动画 */
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes shake {
  0% { transform: translateY(0); }
  25% { transform: translateY(-5px); }
  50% { transform: translateY(5px); }
  75% { transform: translateY(-5px); }
  100% { transform: translateY(0); }
}
</style>
