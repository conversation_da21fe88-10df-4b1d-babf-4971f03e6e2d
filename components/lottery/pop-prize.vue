<template>
	<!-- 抽奖 popup -->
	<view class="cu-modal" catchtouchmove="true" v-if="showModal" :class="showModal ? 'show' : ''">
		<view class="cu-dialog" style="width: 750rpx; background-color: transparent">
			<image style="width: 30px; height: 30px" :src="$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')" fit="cover" @tap.stop="handleClose" />
			<view style="width: 100%; justify-content: center; display: flex; flex-direction: column; align-items: center">
				<view v-if="showType == 1" class="prize-content">
					<view v-if="prize.objType != 5" style="margin-top: 60rpx; height: 150rpx">
						<view style="font-weight: bold; font-size: 46rpx; color: #f00">恭喜您中奖啦！</view>
						<view style="font-size: 30rpx; color: #5f5f5f; margin: 10rpx auto 20rpx">获得以下奖励</view>
					</view>
					<view v-else style="height: 150rpx; margin-top: 60rpx; font-weight: bold; font-size: 46rpx; color: dimgrey">很遗憾！</view>
					
					<view v-if="prize.objType == 7">
						<view class="flex" style="align-items: center; overflow: hidden;">
							<view style="font-weight: bold; font-size: 118rpx; min-height: 118rpx; color: #ff0000" class="shiny heartBeat">
								{{ prize.realValue }}
							</view>
							<view style="width: 54rpx; font-size: 26rpx; color: #999999; height: 60rpx; display: flex; flex-direction: column">
								<view>积分</view>
								<view>红包</view>
							</view>
						</view>
						<view style="font-size: 30rpx; color: #5f5f5f">相当于 ￥{{ prize.redPacketAmount }}</view>
					</view>
					<view v-else>
						<image style="max-height: 125rpx; margin-top: 44rpx" mode="aspectFit" :src="prize.url"></image>
						<view class="prize-name">
							{{ prize.prizeName }}
						</view>
					</view>
					<image
						v-if="prize.objType != 5"
						class="prize-btn"
						:class="prize.status == 1 ? 'use-layout' : ''"
						@click="handleReceive"
						:src="
							prize.status == 1
								? $imgUrl('live/activity/lottery/button_use2.webp')
								: $imgUrl('live/activity/lottery/button_receive.png')
						"
					></image>
				</view>

				<image v-if="showType == 1 && prize.advertImg" :src="prize.advertImg" style="margin-top: 10px" mode="widthFix"></image>

				<view class="assistance-layout" v-else-if="prize.actInfoHelpVO && prize.actInfoHelpVO.isCanHelp == 1">
					<view class="title">邀请好友助力得红包</view>
					<view class="desc">
						已有{{ prize.actInfoHelpVO.helpUsers && prize.actInfoHelpVO.helpUsers.length ? prize.actInfoHelpVO.helpUsers.length : 0 }}位好友为您助力，已得{{
							prize.actInfoHelpVO.redPackPrice
						}}元红包
					</view>
					<view class="show" @click="handleShowAssistanceList">查看好友助力></view>
					<view class="action-layout">
						<view class="assistance-post" @click="handlePost">扫码助力</view>
						<button open-type="share" style="background-color: transparent; border: 0" class="cu-btn">
							<view class="assistance-wx">邀请好友</view>
						</button>
					</view>
				</view>
				<view class="goon-layout"></view>
			</view>
		</view>
	</view>
</template>

<script>
import { receivePrize } from '@/api/activity.js';
import __config from '@/config/env';
export default {
	name: 'pop-prize',
	props: {
		prize: {
			type: Object,
			default: () => {}
		},
		showType: {
			type: String,
			default: '1' //  1显示奖品   2  只显示下面的分享
		}
	},
	data() {
		return {
			showModal: true
		};
	},
	methods: {
		handleShowAssistanceList() {
			console.log('==handleShowAssistanceList====');
			this.$emit('handleShowAssistanceList');
		},
		handleClose() {
			this.showModal = false;
			this.$emit('close');
		},

		handleReceive() {
			console.log('==去领取奖品=====', this.prize);
			if ((this.prize.status === 0 || this.prize.status === '0') && this.prize.id) {
				// 调用领取的接口
				this.receiveGoods(this.prize.id);
			} else if (this.prize.objType === '4' || this.prize.objType === 4) {
				uni.navigateTo({
					url: '/pages/signrecord/signrecord-info/index'
				});
			} else if (this.prize.objType == '3' || this.prize.objType === 3) {
				uni.navigateTo({
					url: '/pages/goods/goods-list/index?couponId=' + this.prize.objValue
				});
			} else if (this.prize.objType === '6') {
				//如果有
				if (this.prize.couponNo) {
					uni.navigateTo({
						url: '/pages/coupon/coupon-offline-detail-plus/index?id=' + this.prize.couponNo
					});
				} else {
					uni.navigateTo({
						url: '/pages/coupon/coupon-user-list/index?navTabCur=1'
					});
				}
			} else if (this.prize.objType === '7') {
				// 如果是页面大转盘，就跳转到指定的微页面，如果是在微页面的弹框上弹出的点击去使用，就关闭弹框
				var pages = getCurrentPages(); // 获取页面栈实例
				var currentPage = pages[pages.length - 1]; // 获取当前页面实例
				var currentRoute = currentPage.route; // 获取当前页面路径
				console.log('当前页面路径：' + currentRoute);
				if (currentRoute == 'pages/activity/lottery/index') {
					if (__config.basePath == 'https://site.songlei.com') {
						uni.navigateTo({
							url: `/pages/micro-page/index?id=1603198286064328705`
						});
					} else {
						uni.navigateTo({
							url: `/pages/micro-page/index?id=1879780440649437185`
						});
					}
				} else {
					// 关闭大转盘
					this.showModal = false;
					this.$emit('closeLottery');
				}
			} else {
				uni.showToast({
					title: '请去奖品明细查看',
					mask: true,
					icon: 'none'
				});
				this.handleClose();
			}
		},

		async receiveGoods(id) {
			let res = await receivePrize({
				id
			});
			if (res.ok) {
				let { data } = res;
				uni.showToast({
					title: '领券成功,请去奖品明细查看',
					mask: true,
					icon: 'none'
				});
			} else {
				uni.hideLoading();
				uni.showToast({
					title: '领取奖品失败',
					mask: true,
					icon: 'none'
				});
			}
		},

		handlePost() {
			this.$emit('handleShowPost');
		}
	}
};
</script>

<style lang="scss" scoped>
.prize-content {
	width: 675rpx;
	height: 768rpx;
	background-size: 100% 100%;
	background-image: url(https://img.songlei.com/live/activity/nineGrid/prize-bg2.webp);
	display: flex;
	flex-direction: column;
	align-items: center;

	.prize-name {
		margin-top: 20rpx;
		font-size: 30rpx;
		font-weight: bold;
		color: #000000;
	}

	.prize-btn {
		width: 444rpx;
		height: 104rpx;
		margin-top: 190rpx;
	}

	.use-layout {
		width: 466rpx;
		height: 126rpx;
	}
}

.assistance-layout {
	width: 640rpx;
	height: 360rpx;
	background: linear-gradient(0deg, #fff9e3 0%, #fffefb 100%);
	box-shadow: 2rpx 3rpx 0rpx 0rpx #ffffff;
	border-radius: 40rpx;
	margin-top: 18rpx;
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	.title {
		font-weight: 800;
		font-size: 36rpx;
		color: #000000;
	}

	.desc {
		font-weight: 500;
		font-size: 30rpx;
		color: #5f5f5f;
		margin-top: 24rpx;
	}

	.show {
		font-weight: 500;
		font-size: 30rpx;
		color: #5f5f5f;
		line-height: 47rpx;
		text-decoration-line: underline;
		margin-top: 24rpx;
	}

	.action-layout {
		margin-top: 18rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;

		.assistance-post {
			font-weight: 800;
			font-size: 30rpx;
			color: #ffffff;
			width: 210rpx;
			height: 106rpx;
			line-height: 85rpx;
			background-size: 100% 100%;
			background-image: url(https://img.songlei.com/live/activity/lottery/assistance-post.webp);
		}

		.assistance-wx {
			font-weight: 800;
			font-size: 30rpx;
			color: #ffffff;
			width: 300rpx;
			height: 104rpx;
			line-height: 85rpx;
			background-size: 100% 100%;
			background-image: url(https://img.songlei.com/live/activity/lottery/assistance-wx.webp);
		}
	}
}
.goon-layout {
	margin-top: 18rpx;
	width: 542rpx;
	height: 146rpx;
	// background-size: 100% 100%;
	// background-image: url(https://img.songlei.com/live/activity/lottery/goon.webp);
}

.shiny {
	color: #f25430;
	background: -webkit-gradient(linear, left top, left bottom, from(#f25430), to(#ff0000));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	display: block;
	// width: 160rpx;
	font-weight: 900;
	position: relative;
}

.shiny::before {
	background-position: -90rpx;
	-webkit-animation: flare 3s infinite;
	-webkit-animation-timing-function: linear;
	background-image: linear-gradient(65deg, transparent 20%, rgba(255, 255, 255, 0.5) 20%, rgba(255, 255, 255, 0.7) 27%, transparent 27%, transparent 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	content: 'Shiny';
	color: #fff;
	display: block;
	padding-right: 40rpx;
	position: absolute;
}

@-webkit-keyframes flare {
	0% {
		background-position: -10rpx;
	}

	30% {
		background-position: 200rpx;
	}

	100% {
		background-position: 200rpx;
	}
}


@keyframes heartBeat {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}

	14% {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}

	28% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}

	42% {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}

	70% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

.heartBeat {
	-webkit-animation-timing-function: linear;
	-webkit-animation-name: heartBeat;
	animation-name: heartBeat;
	-webkit-animation-duration: 6s;
	animation-duration: 6s;
	-webkit-animation-timing-function: ease-in-out;
	animation-timing-function: ease-in-out;
}

</style>
