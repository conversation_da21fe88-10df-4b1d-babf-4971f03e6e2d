<template>
	<!-- 中的谢谢参与奖 -->
	<view>
		<pop-empty
			v-if="showPrize && ((prizeInfo && prizeInfo.objType == 5) || (sharePopInfo && sharePopInfo.objType == 5))"
			:showType="showType" :prize="showType == 1 ? prizeInfo : sharePopInfo" @close="handleClosePrize"
			@handleShowAssistanceList="showAssistanceList = true" @handleShowPost="handleShowPost"
			@closeLottery="handleCloseLottery"></pop-empty>
		<!-- 中了奖品 -->
		<pop-prize v-else-if="showPrize" :showType="showType" :prize="showType == 1 ? prizeInfo : sharePopInfo"
			:lotteryInfo="lotteryInfo" @close="handleClosePrize" @handleShowAssistanceList="showAssistanceList = true"
			@handleShowPost="handleShowPost" @closeLottery="handleCloseLottery" show></pop-prize>
		<view class="almost-lottery" v-show="!showPrize">
			<!-- lottery -->
			<view class="almost-lottery__light" v-if="isDrawFinish"></view>
			<view class="almost-lottery__head" v-if="isDrawFinish">
				<image :src="lotteryInfo.topImg || $imgUrl('live/activity/lottery/top.png')" style="width: 544rpx"
					mode="widthFix"></image>
				<view class="start-time" v-if="lotteryInfo.startTime">
					活动时间：{{ lotteryInfo.startTime.substr(0, 10) }} ~
					{{ lotteryInfo.endTime.substr(0, 10) }}
				</view>
				<view class="prize-btn" @click="handleShowPrizeList">中奖明细</view>
				<view class="rules-btn" @click="handleShowRule">规则</view>
				<view v-if="sharePopInfo.isCanInvite == 1" class="share-btn" @click="handleShowSharePop">邀请好友</view>
				<!-- <view class="lottery-base" :style="{ backgroundImage: 'url(' + lotteryInfo.baseImg  + ')'}">
				<image class="lottery-btn" :src="lotteryInfo.drawImg" @click="handleActionStart"></image>
				<view class="almost-lottery__count">
					抽奖 {{ freeNum }} 次
				</view>
			</view> -->
			</view>
			<view class="almost-lottery__wheel">
				<lottery-com ref="lotteryCom" :lottery-size="lotteryConfig.lotterySize" :action-size="lotteryConfig.actionSize"
					:ring-count="2" :duration="1" :self-rotaty="false" :img-circled="false" :canvasCached="false"
					:lottery-bg="lotteryInfo.dialImg" :action-bg="lotteryInfo.drawImg" :colors="lotteryInfo.bgColor"
					:prize-list="prizeList" :prize-index="prizeIndex" @reset-index="prizeIndex = -1"
					@draw-before="handleDrawBefore" @draw-start="handleDrawStart" @draw-end="handleDrawEnd"
					@finish="handleDrawFinish" v-if="prizeList.length" />
			</view>
			<view v-if="isDrawFinish" class="lottery-base" :style="{ backgroundImage: 'url(' + lotteryInfo.baseImg + ')' }">
				<image class="lottery-btn" :src="$imgUrl('live/activity/lottery/lottery_btn.png')" @click="handleActionStart">
				</image>
				<view v-if="freeNum > -1" class="almost-lottery__count">抽奖 {{ freeNum }} 次</view>
				<view v-else class="almost-lottery__count">请先登录</view>
			</view>
			<view class="barrage">
				<barrage ref="barrage" width="750rpx" height="140rpx"></barrage>
			</view>
			<pop-rules v-if="showRules" :rule="lotteryInfo.detail" @close="handleCloseRule" />
			<pop-prize-list v-if="showPrizeList" :lotteryId="lotteryId" @close="handleClosePrizeList" />

			<pop-status v-if="showStatus" :startTime="lotteryInfo.startTime" :endTime="lotteryInfo.endTime"
				:status="lotteryInfo.status" />
			<PopAssistanceList v-if="showAssistanceList"
				:list="showType == 1 ? prizeInfo.actInfoHelpVO.helpUsers : sharePopInfo.actInfoHelpVO.helpUsers"
				@close="showAssistanceList = false" />
			<!-- 送券通知 -->
			<coupon-notification ref="topMessage"></coupon-notification>
			<wxSharePic ref="wxSharePic" :bgImg="lotteryInfo.shareImg" :prizeList="prizeList" @success="shareSuccess" />
			<postSharePic v-if="showPostShare" v-model="showPostShare" :shareParams="sharePostParams" />
		</view>
	</view>
</template>

<script>
const app = getApp()
import LotteryCom from './lottery-com.vue'
import PopRules from './pop-rules.vue'
import PopPrizeList from './pop-prizelist.vue'
import PopAssistanceList from './pop-assistancelist.vue'
// 中奖之后的动画
import PopPrize from './pop-prize2.vue'
// 没中奖之后的弹框 和没中奖的逻辑
import PopEmpty from './pop-prize.vue'
import PopStatus from './pop-status.vue'
import Barrage from '@/components/barrage/index.vue'
import couponNotification from '@/components/coupon-notification/index'
import wxSharePic from './wx-share-pic.vue'
import postSharePic from './post-share-pic.vue'
import { getActivityInfo, getActivityUser, getPrizeInfo, getHistoryUsers, getInviteInfo } from '@/api/activity.js'
import { clearCacheFile, clearStore } from '@/utils/lottery-utils.js'
const util = require('utils/util.js')
import { getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'
import { senTrack } from '@/public/js_sdk/sensors/utils.js'

export default {
	name: 'lotteryIndex',
	components: {
		LotteryCom,
		Barrage,
		PopRules,
		PopPrizeList,
		PopEmpty,
		PopPrize,
		PopStatus,
		couponNotification,
		PopAssistanceList,
		wxSharePic,
		postSharePic
	},
	props: {
		lotteryId: {
			type: String,
			default: ''
		},
		//是否弹框的方式展示
		isPop: {
			type: Boolean,
			default: false
		},
		showPrizeListProp: {
			type: Boolean,
			default: false
		}
	},
	data () {
		return {
			// 以下是转盘配置相关数据
			lotteryConfig: {
				// 抽奖转盘的整体尺寸，单位rpx
				lotterySize: 630,
				// 抽奖按钮的尺寸，单位rpx
				actionSize: 196
			},

			// 以下是转盘 UI 配置
			// 转盘外环图，如有需要，请参考替换为自己的设计稿
			// lotteryBg: require('@/static/lottery-bg.png'),
			// 抽奖按钮图
			// actionBg: require('@/static/action-bg.png'),

			// 以下是奖品配置数据
			// 奖品数据
			lotteryInfo: {},
			prizeList: [],
			// 奖品是否设有库存
			onStock: true,
			// 中奖下标
			prizeIndex: -1,
			// 中奖奖品
			prizeInfo: null,
			// 是否正在抽奖中，避免重复触发
			prizeing: false,
			// 权重随机数的最大值
			prizeWeightMax: 0,
			// 权重数组
			prizeWeightArr: [],
			// 以下为业务需求有关示例数据
			// 金币余额
			goldCoin: 20,
			// 当日免费抽奖次数余额
			freeNum: 0,
			// 每次消耗的金币数
			goldNum: 20,
			// 每天免费抽奖次数
			freeNumDay: 1,
			tabIndex: 0,
			historyList: [
				'187****6531中10元优惠券',
				'135****1235中1元优惠券',
				'176****9863中66元优惠券',
				'132****2136中33元优惠券',
				'185****9036中88元优惠券',
				'189****4765中88元优惠券'
			],
			isDrawFinish: false, //是否在请求数据绘制中
			showRules: false, // 规则
			showPrizeList: false, // 中奖明细弹框
			showPrize: false, // 恭喜中奖了弹框
			showStatus: false, // 活动已开始或者已结束弹框
			showAssistanceList: false,
			shares: {},
			shareImgSrc: '',
			showPostShare: false,
			sharePostParams: {},
			assistancelist: [],
			sharePopInfo: {
				isCanInvite: false,
				userPrizeId: ''
			},
			showType: '1', //1显示奖品   2  只显示下面的分享
			enterTime: ''
		}
	},

	watch: {
		lotteryId: {
			handler (newVal, oldVal) {
				if (newVal != oldVal && newVal) {
					app.initPage().then((res) => {
						this.handleInitCanvas()
					})
				}
			},
			immediate: true
		}
	},

	created () {
		this.prizeList = []
		this.enterTime = new Date().getTime()
	},

	destroyed () {
		const leaveTime = new Date().getTime()
		const stayDuration = Math.floor((leaveTime - this.enterTime) / 1000)

		senTrack('ActivityPageLeave', {
			'page_name': getCurrentTitle(0),
			'forward_source': getCurrentTitle(0),
			'page_level': '一级',
			'activity_id': this.lotteryId,
			'activity_name': this.lotteryInfo.name,
			'activity_type_first': '玩法活动',
			'activity_type_second': '幸运大转盘',
			'stay_duration': stayDuration
		})
	},

	methods: {
		handleCloseLottery () {
			this.$emit('close')
		},
		shareSuccess (e) {
			this.shareImgSrc = e
		},
		// 重新生成
		handleInitCanvas () {
			// clearCacheFile()
			// clearStore()
			this.prizeList = []
			this.getPrizeList()
			this.$nextTick(() => {
				if (this.showPrizeListProp) {
					this.handleShowPrizeList()
				}
			})
		},

		// 获取奖品列表
		async getPrizeList () {
			uni.showLoading({
				title: '奖品准备中...'
			})
			let res = await getActivityInfo({
				id: this.lotteryId
			})
			console.log('获取奖品列表', res)
			if (res.ok) {
				let data = res.data
				if (data) {
					// 神策埋点
					senTrack('ActivityPageView', {
						'page_name': getCurrentTitle(0),
						'forward_source': getCurrentTitle(0),
						'page_level': '一级',
						'activity_id': this.lotteryId,
						activity_name: data.actInfo.name,
						activity_type_first: '玩法活动',
						activity_type_second: '幸运大转盘',
					})

					this.lotteryInfo = data.actInfo || {}
					//整理分享用的数据

					// 1 未开始  3 已结束
					if (this.isPop && (this.lotteryInfo.status == 1 || this.lotteryInfo.status == 3)) {
						this.$emit('close')
						return
					}
					//如果是页面展示大转盘，需要展示背景图片
					if (!this.isPop) {
						this.$emit('setbg', this.lotteryInfo.bgImg)
						console.error('==setbg==')
					}
					//1待开始  3已结束 弹框展示
					if (this.lotteryInfo.status == 1 || this.lotteryInfo.status == 3) {
						this.showStatus = true
					}
					if (this.lotteryInfo.bgColor) {
						this.lotteryInfo.bgColor = this.lotteryInfo.bgColor.split(';')
					}
					this.lotteryInfo.drawImg = this.lotteryInfo.drawImg || this.$imgUrl('live/activity/lottery/lottery_action.png')
					this.lotteryInfo.dialImg = this.lotteryInfo.dialImg || this.$imgUrl('live/activity/lottery/lottery_bg.png')
					this.lotteryInfo.baseImg = this.lotteryInfo.baseImg || this.$imgUrl('live/activity/lottery/lottery_base.png')
					this.lotteryInfo.detail = this.lotteryInfo.detail || '暂无规则信息'
					this.prizeList = data.prizeList
					console.log('已获取到奖品列表数据，开始绘制抽奖转盘')
					// 计算开始绘制的时间
					if (console.time) {
						console.time('绘制转盘用时')
					}
					this.$nextTick(() => {
						this.$refs.wxSharePic.onCanvas()
						this.handlePostShare()
					})
				}

				//获取历史中奖用户
				this.getHistoryUsers()
				if (util.isUserLogin()) {
					//获取用户可转次数
					this.getActivityUserInfo()
					this.getShareInfo()
				} else {
					//负数表示 需要登录查看
					this.freeNum = -1
				}
			} else {
				uni.hideLoading()
				uni.showToast({
					title: '获取奖品失败',
					mask: true,
					icon: 'none'
				})
			}
		},

		async getHistoryUsers () {
			let res = await getHistoryUsers({
				actId: this.lotteryId
			})
			if (res && res.data && res.data.length > 0) {
				let pat = /(\d{3})\d*(\d{4})/
				res.data.forEach((item) => {
					this.historyList.push(`${item.phone.replace(pat, '$1****$2')}中了${item.prizeName}`)
				})
			}
			this.$refs.barrage.init(this.historyList)
		},

		// 获取该用户剩余抽奖次数
		async getActivityUserInfo () {
			let res = await getActivityUser({
				actId: this.lotteryId
			})
			if (res.ok) {
				let data = res.data
				if (data) {
					this.freeNum = data.num
				}
			} else {
				uni.hideLoading()
				uni.showToast({
					title: '获取剩余抽奖次数失败',
					mask: true,
					icon: 'none'
				})
			}
		},

		// 抽奖开始之前
		handleDrawBefore (callback) {
			console.log('抽奖开始之前')
			let flag = false
			// 还有免费数次或者剩余金币足够抽一次
			if (this.freeNum > 0) {
				flag = true
			} else {
				flag = false
				uni.showToast({
					title: '抽奖次数已用完',
					icon: 'none',
					duration: 2000
				})
			}
			callback(flag)
		},

		// 本次抽奖开始
		handleDrawStart () {
			console.error('触发抽奖按钮')
			let that = this
			if (this.prizeing) return
			if (this.lotteryInfo && this.lotteryInfo.attendLimit) {
				const attendLimit = JSON.parse(this.lotteryInfo.attendLimit)
				if (attendLimit && attendLimit.sale > 0) {
					uni.showModal({
						title: '温馨提示',
						content: '每次抽奖需要消耗' + attendLimit.sale + '积分，是否继续?',
						showCancel: true,
						success (res) {
							if (res.confirm) {
								that.dealDrawStart()
							}
						}
					})
				} else {
					this.dealDrawStart()
				}
			} else {
				this.dealDrawStart()
			}
		},

		dealDrawStart () {
			this.prizeing = true
			// 更新免费次数
			this.freeNum--
			this.tryLotteryDraw()
		},

		// 尝试发起抽奖
		tryLotteryDraw () {
			console.log('旋转开始，获取中奖下标......')
			//从后端获取中奖下标
			this.remoteGetPrizeIndex()
		},

		// 远程请求接口获取中奖下标
		remoteGetPrizeIndex () {
			getPrizeInfo(this.lotteryId)
				.then((res) => {
					if (res && res.data) {
						this.prizeInfo = res.data
						this.prizeIndex = this.prizeInfo.sort
						console.error('本次抽中奖品 =>', this.prizeInfo.prizeName)
						// 场景类型(1:大转盘 2:订单支付 3: 商品预览)
						this.$refs.topMessage.setMessage(1, 5000) // 将数据传递给顶部信息提示组件，并设置持续时间为0ms
						if (res.data && res.data.actInfoHelpVO && res.data.actInfoHelpVO.helpUsers) {
							this.assistancelist = res.data.actInfoHelpVO.helpUsers || []
						}
						this.getShareInfo()

						senTrack('ActivityResult', {
							'page_name': getCurrentTitle(0),
							'page_level': '一级',
							'activity_id': this.lotteryId,
							'activity_name': this.lotteryInfo.name,
							'activity_type_first': '玩法活动',
							'activity_type_second': '幸运大转盘',
							'is_obtain_prize': this.prizeInfo.id ? true : false,
							'prize_name': this.prizeInfo.prizeName,
							'prize_id': this.prizeInfo.id,
						})
					}
				})
				.catch((error) => {
					//接口报错，旋转状态重置
					this.prizeing = false
				})
		},

		// 本次抽奖结束
		handleDrawEnd () {
			console.log('旋转结束，执行拿到结果后到逻辑')
			this.prizeing = false
			this.showPrize = true
			this.showType = '1'
		},
		// 抽奖转盘绘制完成
		handleDrawFinish (res) {
			this.isDrawFinish = true
			console.log('抽奖转盘绘制完成', res)
			if (res.ok) {
				// 计算结束绘制的时间
				if (console.timeEnd) {
					console.timeEnd('绘制转盘用时')
				}
			}
			let stoTimer = setTimeout(() => {
				stoTimer = null
				uni.hideLoading()
			}, 50)
		},

		handleShowSharePop () {
			this.showPrize = true
			this.showType = '2'
		},

		handleShowRule () {
			this.showRules = true
		},
		handleCloseRule () {
			this.showRules = false
		},
		handleShowPrizeList () {
			this.showPrizeList = true
		},

		handleClosePrizeList () {
			this.showPrizeList = false
		},

		handleClosePrize () {
			this.showPrize = false
		},

		handleActionStart () {
			senTrack('ActivityClick', {
				'page_name': getCurrentTitle(0),
				'page_level': '一级',
				'page_name': getCurrentTitle(0),
				'activity_id': this.lotteryId,
				'activity_name': this.lotteryInfo.name,
				'activity_type_first': '玩法活动',
				'activity_type_second': '幸运大转盘'
			})
			this.$refs.lotteryCom.handleActionStart()
		},

		shareMessage () {
			this.shares.title = this.lotteryInfo.name || '抽一次，有惊喜'
			this.shares.imageUrl = this.lotteryInfo.shareImg || this.shareImgSrc
			if (this.sharePopInfo && this.sharePopInfo.userPrizeId > 0 && this.sharePopInfo.actInfoHelpVO.isCanHelp == 1) {
				this.shares.path = 'pages/activity/assiatance/index?id=' + this.lotteryId
				this.shares.path += '&pid=' + this.sharePopInfo.userPrizeId
			} else {
				//用小程序自己的路径
			}
			console.error('====this.shares======', this.shares)
			return this.shares
		},

		handleShowPost () {
			this.showPostShare = true
		},
		handlePostShare () {
			const prizeList = this.getValidPrize()
			let posterConfig = {
				width: 750,
				height: 1030,
				backgroundColor: '#fff',
				debug: false,
				texts: [
					{
						x: 10,
						y: 780,
						text: '扫码参与瓜分亿元红包',
						fontSize: 40,
						color: '#f00'
					},
					{
						x: 10,
						y: 840,
						text: '邀请您来帮忙助力!',
						fontSize: 40,
						color: '#000'
					},
					{
						x: 500,
						y: 850,
						text: '扫描/长按识别!',
						fontSize: 30,
						color: '#B3B3B3'
					}
				],
				blocks: [],
				images: [
					{
						width: 730,
						height: 584,
						x: 10,
						y: 10,
						url: this.$imgUrl('live/activity/nineGrid/default-share.jpg')
					},
					{
						width: 200,
						height: 200,
						x: 490,
						y: 614,
						url: null,
						qrCodeName: 'qrCodeName' // 二维码唯一区分标识
					}
				]
			}
			if (this.prizeList && this.prizeList[0]) {
				posterConfig.blocks.push({
					width: 232,
					height: 274,
					x: 20,
					y: 300,
					borderRadius: 30,
					backgroundColor: '#ffffff'
				})
				posterConfig.images.push({
					width: 232,
					height: 260,
					x: 20,
					y: 310,
					borderRadius: 30,
					url: this.prizeList[0].url
				})
			}
			if (this.prizeList && this.prizeList[1]) {
				posterConfig.blocks.push({
					width: 232,
					height: 274,
					x: 262,
					y: 300,
					borderRadius: 30,
					backgroundColor: '#ffffff'
				})
				posterConfig.images.push({
					width: 232,
					height: 260,
					x: 272,
					y: 310,
					borderRadius: 30,
					url: this.prizeList[1].url
				})
			}
			if (this.prizeList && this.prizeList[2]) {
				posterConfig.blocks.push({
					width: 232,
					height: 274,
					x: 502,
					y: 300,
					borderRadius: 30,
					backgroundColor: '#ffffff'
				})
				posterConfig.images.push({
					width: 232,
					height: 260,
					x: 512,
					y: 310,
					borderRadius: 30,
					url: this.prizeList[2].url
				})
			}
			let userInfo = uni.getStorageSync('user_info')
			console.log('userInfo==>', userInfo)
			if (userInfo && userInfo.headimgUrl) {
				//如果有头像则显示
				posterConfig.images.push({
					width: 121,
					height: 121,
					x: 10,
					y: 614,
					borderRadius: 120,
					url: userInfo.headimgUrl
				})
				posterConfig.texts.push({
					x: 150,
					y: 664,
					text: userInfo.nickName,
					fontSize: 32,
					color: '#000'
				})
				posterConfig.texts.push({
					x: 150,
					y: 710,
					text: userInfo.phone,
					fontSize: 32,
					color: '#000'
				})
			}

			if (this.sharePopInfo && this.sharePopInfo.userPrizeId > 0 && this.sharePopInfo.actInfoHelpVO.isCanHelp == 1) {
				this.sharePostParams = {
					scene: 'pid=' + this.sharePopInfo.userPrizeId,
					page: 'pages/activity/assiatance/index',
					posterConfig: posterConfig
				}
			} else {
				this.sharePostParams = {
					scene: 'id=' + this.lotteryId,
					page: 'pages/activity/lottery/index',
					posterConfig: posterConfig
				}
			}
		},

		async getShareInfo () {
			let res = await getInviteInfo({
				actId: this.lotteryId
			})
			if (res.ok) {
				if (res.data) {
					this.sharePopInfo = {
						...res.data,
						actInfoHelpVO: res.data.helpVo
					}
					this.shareMessage()
					this.handlePostShare()
				}
			}
		},

		// 获取有效的奖品
		getValidPrize () {
			let result = []
			if (this.prizeList && this.prizeList.length > 0) {
				result = this.prizeList.filter(function (item) {
					return item.objType != 5
				})
			}
			return result
		}
	}
}
</script>

<style lang="scss" scoped>
.almost-lottery {
	flex: 1;
	position: relative;
	// height: 1108rpx;
}

.almost-lottery__light {
	position: absolute;
	top: 30rpx;
	width: 750rpx;
	height: 830rpx;
	background-size: 750rpx 830rpx;
	background-image: url(https://img.songlei.com/live/activity/lottery/light_bg.png);
}

.barrage {
	position: absolute;
	width: 750rpx;
	top: 790rpx;
}

.almost-lottery__head {
	position: relative;
	width: 100%;

	.start-time {
		font-size: 28rpx;
		font-weight: 500;
		color: #ffffff;
		position: absolute;
		top: 150rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 600rpx;
	}

	.prize-btn,
	.rules-btn,
	.share-btn {
		background: rgba(255, 255, 255, 0.4);
		border-top-left-radius: 20rpx;
		border-bottom-left-radius: 20rpx;
		position: absolute;
		right: 0;
		top: 156rpx;
		color: #ffffff;
		font-size: 26rpx;
		padding: 4rpx 20rpx;
		z-index: 1;
	}

	.rules-btn {
		top: 222rpx;
	}

	.share-btn {
		top: 286rpx;
	}
}

.lottery-base {
	// position: absolute;
	// top: 780rpx;
	width: 580rpx;
	height: 320rpx;
	max-width: 580rpx;
	max-height: 320rpx;
	// left: 50%;
	// left: 50%;
	// transform: translateX(-50%);
	margin: -60rpx auto 0;
	background-size: 100%;
}

.lottery-btn {
	width: 352rpx;
	height: 94rpx;
	top: 168rpx;
	position: relative;
}

.almost-lottery__count {
	top: 162rpx;
	color: #ffffff;
	font-size: 24rpx;
	position: relative;
}

.almost-lottery__wheel {
	text-align: center;
	margin-top: 30rpx;
}
</style>
