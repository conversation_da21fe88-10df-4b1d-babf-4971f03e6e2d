<template>
	<!-- 抽奖 popup -->
	<view
		:style="{ width: '100vw', height: '100vh', backgroundSize: '100% auto', backgroundImage: isPop ? '' : `url(${bgImg})` }">
		<view class="cu-modal" :style="{ background: 'rgba(0, 0, 0, 0.75)', backgroundSize: '100% 100%' }"
			catchtouchmove="true" v-if="showModal" :class="showModal ? 'show' : ''">
			<view class="cu-dialog" style="width: 750rpx; background-color: transparent">
				<cu-custom :hideMarchContent="true" :isBack="true" v-if="!isPop"></cu-custom>
				<lottery ref="lottery" :lotteryId="lotteryId" @close="handleClose" :isPop="isPop"
					:showPrizeListProp="showPrizeListProp" @setbg="handleSetBg" />
				<image v-if="isPop" style="width: 30px; height: 30px; position: fixed; right: 40rpx; top: 140rpx"
					:src="$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')" fit="cover"
					@tap.stop="handleClose" />
			</view>
		</view>
	</view>
</template>

<script>
import Lottery from './lottery.vue'

export default {
	name: 'pop-lottery',
	components: {
		Lottery
	},
	props: {
		lotteryId: {
			type: String,
			default: ''
		},
		isPop: {
			type: Boolean,
			default: true
		},
		showPrizeListProp: {
			type: Boolean,
			default: false
		}
	},
	data () {
		return {
			showModal: true,
			bgImg: ''
		}
	},

	methods: {
		handleClose () {
			console.error('this.$refs.lottery.showPrize===', this.$refs.lottery.showPrize)
			if (this.$refs.lottery.showPrize) {
				this.$refs.lottery.showPrize = false
			} else {
				this.showModal = false
				this.$emit('close', '')
			}
		},
		handleSetBg (bgImg) {
			this.bgImg = bgImg
		},

		shareMessage () {
			return this.$refs.lottery.shareMessage()
		}
	}
}
</script>

<style lang="scss" scoped>
.almost-lottery__popup-wrap {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;

	.almost-lottery {
		background: transparent;
	}
}
</style>
