<template>
	<!-- 抽奖 popup -->
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''">
		<view class="cu-dialog" style="width: 750rpx;background-color: transparent;">
			<view
				style="width: 100%; justify-content: center; display: flex;flex-direction: column; align-items: center;">
				<view class="prize-content">
					<view class="title">- 中奖明细 -</view>
					<scroll-view scroll-y="true" style="height: 560rpx;">
						<view class="prize-list">
							<template v-if="prizeList&&prizeList.length>0">
								<view v-for="(item, index) in prizeList" class="prize-info">
									<view>
										<view class="time">{{item.createTime}}</view>
										<view class="prize-name">{{item.prizeName}} * 1</view>
									</view>
									<view class="receive-btn" @tap.stop="handleReceive(item)">
										{{item.status == 0 ? '去领取' : ((item.drawObjType == '3' || item.drawObjType == '4' || item.drawObjType == '6') ? '去使用' : '已领取')}}
									</view>
								</view>
							</template>
							<view v-else class="no-data">
								暂无中奖信息
							</view>
						</view>
					</scroll-view>
				</view>
				<image style="width: 30px; height: 30px; margin-top: 30rpx; "
					src="https://img.songlei.com/1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png"
					fit="cover" @tap.stop="handleClose" />
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getUserPrizes,
		receivePrize
	} from '@/api/activity.js'
	export default {
		name: 'pop-prizelist',
		props: {
			lotteryId: {
				type: String,
				default: ''
			},
		},
		data() {
			return {
				showModal: true,
				prizeList: []
			}
		},

		watch: {
			lotteryId: {
				handler(newVal, oldVal) {
					if (newVal != oldVal && newVal) {
						this.getPrizes();
					}
				},
				immediate: true
			}
		},

		methods: {
			async getPrizes() {
				let res = await getUserPrizes({
					actId: this.lotteryId
				});
				if (res.ok) {
					let {
						data
					} = res;
					this.prizeList = data || [];
				} else {
					uni.hideLoading()
					uni.showToast({
						title: '获取中奖清单失败',
						mask: true,
						icon: 'none'
					})
				}
			},

			handleClose() {
				this.showModal = false;
				this.$emit("close");
			},

			handleReceive(item) {
				if ((item.status == 0 || item.status == '0') && item.id) {
					// 调用领取的接口 
					this.receiveGoods(item.id);
				} else if (item.drawObjType == '4' || item.drawObjType == 4) {
					uni.navigateTo({
						url: '/pages/user/user-integral-shop/index'
					})
				} else if (item.drawObjType == '3' || item.drawObjType == 3) {
					uni.navigateTo({
						url: '/pages/goods/goods-list/index?couponId=' + item.drawObjValue
					})
				} else if (item.drawObjType == '6') {
					//如果有
					if (item.couponNo) {
						uni.navigateTo({
							url: '/pages/coupon/coupon-offline-detail-plus/index?id=' + item.couponNo
						})
					} else {
						uni.navigateTo({
							url: '/pages/coupon/coupon-user-list/index?navTabCur=1'
						})
					}
				}
			},

			async receiveGoods(id) {
				let res = await receivePrize({
					id
				});
				if (res.ok) {
					let {
						data
					} = res;
					this.getPrizes();
				} else {
					uni.hideLoading()
					uni.showToast({
						title: '领取奖品失败',
						mask: true,
						icon: 'none'
					})
				}
			}
		},

	}
</script>

<style lang="scss" scoped>
	.prize-content {
		width: 660rpx;
		height: 670rpx;
		background: #FFFFFF;
		border-radius: 38rpx;
		padding: 34rpx;

		.prize-list {
			background: #FFFFFF;
		}

		.title {
			color: #000000;
			font-size: 40rpx;
		}

		.no-data {
			margin-top: 30rpx;
			text-align: center;
			font-size: 26rpx;
		}

		.prize-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: solid 1rpx #eeeeee;
			padding:  14rpx 0;
		}

		.time {
			color: #999999;
			font-size: 24rpx;
		}

		.prize-name {
			font-weight: bold;
			color: #000000;
			font-size: 30rpx;
		}

		.receive-btn {
			width: 183rpx;
			height: 70rpx;
			line-height: 70rpx;
			background: linear-gradient(270deg, #FF4E00 0%, #FF8463 100%);
			border-radius: 35rpx;
			color: #FFFFFF;
			font-weight: 500;
			font-size: 28rpx;
			text-align: center;
		}
	}
</style>