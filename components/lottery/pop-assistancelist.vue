<template>
	<!-- 助力列表弹框 -->
	<view class="cu-modal" catchtouchmove="true" v-if="showModal" :class="showModal ? 'show' : ''">
		<view class="cu-dialog" style="width: 750rpx; background-color: transparent">
			<image style="width: 30px; height: 30px" :src="$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')" fit="cover" @tap.stop="handleClose" />
			<view style="width: 100%; justify-content: center; display: flex; flex-direction: column; align-items: center">
				<view class="assistance-content">
					<view style="font-weight: bold; font-size: 35rpx; color: #793636">好友助力</view>
					<template v-if="list && list.length > 0">
						<view v-for="item in list" class="assistance-item">
							<image :src="$imgUrl('live/activity/nineGrid/default-avatar.png')"></image>
							<view class="txt">
								<view>好友：{{item.helpUserName}}</view>
								<view>助力得{{item.amount}}元红包</view>
								<view>{{item.createTime}}</view>
							</view>
						</view>
					</template>
					<view v-else style="font-weight: bold; font-size: rpx; color: #793636; margin-top: 80rpx">暂无好友助力，加油！</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { receivePrize } from '@/api/activity.js';
export default {
	name: 'pop-assistancelist',
	props: {
		list: {
			type: Array,
			default: () => []
		}
	},
	
	data() {
		return {
			showModal: true
		};
	},

	methods: {
		handleClose() {
			this.showModal = false;
			this.$emit('close');
		}
	}
};
</script>

<style lang="scss" scoped>
.assistance-content {
	width: 675rpx;
	height: 960rpx;
	background-size: 100% 100%;
	background-image: url(https://img.songlei.com/live/activity/nineGrid/pop-assistancelistbg.webp);
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx;

	.assistance-item {
		margin-top: 20rpx;
		display: flex;
		image {
			width: 98rpx;
			height: 98rpx;
			border-radius: 50%;
			margin-right: 30rpx;
		}

		.txt {
			font-weight: 500;
			font-size: 28rpx;
			line-height: 40rpx;
			color: #444444;
			text-align: left;
		}
	}
}
</style>
