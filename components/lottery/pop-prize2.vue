<template>
	<view class="container" v-if="show">
		<!-- 背景容器 -->
		<image class="rotating-img" :src="$imgUrl('lottery/light_bg.png')" mode="aspectFit" />
		<image v-if="step == 2 || step == 3" class="redpackage-img" :src="$imgUrl('lottery/redpackage.gif')" mode="aspectFit"></image>
		<image v-if="step == 3 || step == 4 || step == 5 || step == 6" class="colored-img" :src="$imgUrl('lottery/colored-ribbon.gif')" mode="scaleToFill"></image>
		<image v-if="step == 4 || step == 5" class="slide-image" :src="$imgUrl('lottery/yellow-light_bg.png')" mode="widthFix"></image>
		<image v-if="step == 5" class="congratulations-image" :src="$imgUrl('lottery/congratulations.png')" mode="widthFix"></image>
		<popPrize3 v-if="step == 6" :actInfo="prize" @toOrder="handleToOrder" />
	</view>
</template>

<script>
import popPrize3 from './pop-prize3.vue';
import __config from '@/config/env';
export default {
	components: {
		popPrize3
	},
	props: {
		show: { type: Boolean, default: false }, // 控制显示
		prize: {
			type: Object,
			default: () => {}
		},
		lotteryInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			step: 1 // 背景光圈
		};
	},
	computed: {},
	watch: {
		show: {
			handler(val) {
				if (val) this.startAnimation();
			},
			immediate: true
		}
	},
	methods: {
		startAnimation() {
			setTimeout(() => (this.step = 2), 200); // 红包出现
			setTimeout(() => (this.step = 3), 400); // 彩带升起
			setTimeout(() => (this.step = 4), 1200); // 黄色背景图出现
			setTimeout(() => (this.step = 5), 2000); // 恭喜你文字出现
			setTimeout(() => (this.step = 6), 2600); // 奖品内容出现
		},

		handleToOrder() {
			console.log('==去领取奖品=====', this.prize);
			// console.log('==lotteryInfo=====', this.lotteryInfo);

			var pages = getCurrentPages(); // 获取页面栈实例
			var currentPage = pages[pages.length - 1]; // 获取当前页面实例
			var currentRoute = currentPage.route; // 获取当前页面路径
			// console.log('当前页面路径：', currentPage);
			// console.log('当前页面路由:', this.lotteryInfo.goToUrl);
			// currentPage.options && currentPage.options.id
			
			if (this.lotteryInfo.goToUrl) {
				const currentPageId = currentPage.options && (currentPage.options.id || currentPage.options.scene);
				let gotoId = "";
				 if(this.lotteryInfo.goToUrl.split('?')&&this.lotteryInfo.goToUrl.split('?')[1] &&  this.lotteryInfo.goToUrl.split('?')[1].split('=') && this.lotteryInfo.goToUrl.split('?')[1].split('=')[1]){
					 gotoId =  this.lotteryInfo.goToUrl.split('?')[1].split('=')[1];
				 } 
				if (this.lotteryInfo.goToUrl.includes(currentRoute) && currentPageId == gotoId) {
					// 配置跳转大转盘链接和当前页面一样则关闭大转盘
					// console.log('==currentPageId=====', currentPageId);
					this.showModal = false;
					this.$emit("close");
					this.$emit('closeLottery');
				} else {
					if (this.lotteryInfo.goToUrl == '/pages/home/<USER>' || this.lotteryInfo.goToUrl == '/pages/second-tab/index"' || this.lotteryInfo.goToUrl == 'pages/shopping-cart/index' || this.lotteryInfo.goToUrl == '/pages/tab-personal/index' || this.lotteryInfo.goToUrl == '/pages/third-tab/index') {
						uni.switchTab({
							url: this.lotteryInfo.goToUrl
						});
					} else {
						uni.navigateTo({
							url: this.lotteryInfo.goToUrl
						});
					}
				}
			} else{
				if (this.prize.objType === '7' || (this.prize.actInfoSubsidyVO && this.prize.actInfoSubsidyVO.subsidyName)) {
					// 如果是页面大转盘，就跳转到指定的微页面，如果是在微页面的弹框上弹出的点击去使用，就关闭弹框
					if (
						currentRoute == 'pages/micro-page/index' &&
						__config.basePath == 'https://site.songlei.com' &&
						currentPage.options &&
						currentPage.options.id == '1603198286064328705'
					) {
						// 关闭大转盘
						this.showModal = false;
						this.$emit('closeLottery');
					} else if (currentRoute == 'pages/micro-page/index' &&
						__config.basePath == 'https://shopapi.songlei.com' && currentPage.options && currentPage.options.id == '1879780440649437185') {
						// 关闭大转盘
						this.showModal = false;
						this.$emit('closeLottery');
					} else {
						if (__config.basePath == 'https://site.songlei.com') {
							uni.navigateTo({
								url: `/pages/micro-page/index?id=1603198286064328705`
							});
						} else {
							uni.navigateTo({
								url: `/pages/micro-page/index?id=1879780440649437185`
							});
						}
					}
				} else if ((this.prize.status === 0 || this.prize.status === '0') && this.prize.id) {
					// 调用领取的接口
					this.receiveGoods(this.prize.id);
				} else if (this.prize.objType === '4' || this.prize.objType === 4) {
					uni.navigateTo({
						url: '/pages/signrecord/signrecord-info/index'
					});
				} else if (this.prize.objType == '3' || this.prize.objType === 3) {
					uni.navigateTo({
						url: '/pages/goods/goods-list/index?couponId=' + this.prize.objValue
					});
				} else if (this.prize.objType === '6') {
					//如果有
					if (this.prize.couponNo) {
						uni.navigateTo({
							url: '/pages/coupon/coupon-offline-detail-plus/index?id=' + this.prize.couponNo
						});
					} else {
						uni.navigateTo({
							url: '/pages/coupon/coupon-user-list/index?navTabCur=1'
						});
					}
				} else {
					uni.showToast({
						title: '请去奖品明细查看',
						mask: true,
						icon: 'none'
					});
					this.handleClose();
				}
			}
		
		}
	}
};
</script>

<style scoped lang="scss">
.container {
	position: relative;
	width: 750rpx;
	height: 100vh;
	/* 旋转动画定义 */
	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 图片旋转样式 */
	.rotating-img {
		position: absolute;
		top: calc(50% - 325rpx);
		left: 50rpx;
		width: 650rpx; /* 建议使用rpx适配小程序 */
		height: 650rpx;

		/* 动画配置 */
		animation: spin 8s linear infinite;
		-webkit-animation: spin 8s linear infinite; /* iOS兼容 */

		/* 优化显示 */
		image-rendering: -webkit-optimize-contrast;
		will-change: transform; /* 提升动画性能 */
	}

	/* 解决部分安卓机型锯齿问题 */
	.rotating-img {
		transform: translateZ(0);
		backface-visibility: hidden;
	}

	.redpackage-img {
		position: absolute;
		top: calc(50% - 250rpx);
		left: 125rpx;
		width: 500rpx; /* 建议使用rpx适配小程序 */
		height: 500rpx;
	}

	.colored-img {
		position: absolute;
		top: 0;
		left: 25rpx;
		width: 700rpx; /* 建议使用rpx适配小程序 */
		height: 1000rpx;
	}

	/* 图片动画样式 */
	.slide-image {
		width: 750rpx; /* 图片宽度 */
		height: 300rpx;
		right: -100%; /* 初始位置在屏幕右侧外 */
		top: calc(50% - 200rpx); /* 垂直居中 */
		/* 动画配置 */
		animation: slideIn 0.3s ease-out forwards;
	}

	/* 关键帧动画 */
	@keyframes slideIn {
		0% {
			right: -100%; /* 起始位置：完全在右侧外 */
		}
		100% {
			right: 20rpx; /* 结束位置：右侧留出20rpx间距 */
		}
	}

	.congratulations-image {
		position: absolute;
		width: 53%;
		height: 72rpx;
		right: -100%;
		top: calc(50% - 36rpx);
		opacity: 0;
		animation: contentBounce 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
	}

	/* 回弹动画关键帧 */
	@keyframes contentBounce {
		0% {
			right: -100%;
			opacity: 0;
		}
		70% {
			right: 18%;
			opacity: 1;
		}
		85% {
			right: 10%;
		}
		95% {
			right: 25%;
		}
		100% {
			right: 23.5%;
			opacity: 1;
		}
	}
}
</style>
