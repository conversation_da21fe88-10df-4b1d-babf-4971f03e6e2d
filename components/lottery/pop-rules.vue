<template>
	<!-- 抽奖 popup -->
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''">
		<view class="cu-dialog" style="width: 750rpx;background-color: transparent;">

			<view
				style="width: 100%; justify-content: center; display: flex;flex-direction: column; align-items: center;">
				<view class="content">
					<view class="title">- 规则 -</view>
					<scroll-view scroll-y style="height: 300px;">
						<rich-text style="white-space: pre-wrap;">
							<p>{{richTextContent}}</p>
						</rich-text>
						<!-- <jyf-parser :html="rule"></jyf-parser>-->
					</scroll-view>
				</view>
			</view>

			<image style="width: 30px; height: 30px; margin-top: 30rpx; "
				src="https://slshop-file.oss-cn-beijing.aliyuncs.com/1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png"
				fit="cover" @tap.stop="handleClose" />
		</view>

	</view>
	</view>
</template>

<script>
	export default {
		name: 'pop-rules',
		props: {
			rule: {
				type: String,
				default: ''
			},
		},
		data() {
			return {
				showModal: true,
			}
		},

		computed: {
			richTextContent() {
				// 将 \n 替换为 <br/>
				return this.rule.replace(/\\n|\\r\\n|\\r|\\n|\\↵/g, '<br/>');
			}
		},

		methods: {
			handleClose() {
				this.showModal = false;
				this.$emit("close");
			},
		},

	}
</script>

<style lang="scss" scoped>
	.content {
		width: 660rpx;
		height: 661rpx;
		background: #FFFFFF;
		border-radius: 38rpx;
		padding: 34rpx;

		.title {
			color: #000000;
			font-size: 40rpx;
		}
	}
</style>