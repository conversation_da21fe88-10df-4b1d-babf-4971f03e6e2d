<!-- 分享海报 -->
<template>
	<view >
		<view :class="'cu-modal ' + (value ? 'show' : '')">
			<view class="cu-dialog show-bg">
				<view class="bg-white" style="height: calc(100vh - 300rpx); overflow-y: scroll;">
					<image :src="posterUrl" class="image-box" mode="widthFix"></image>
				</view>
				<view class="cu-bar bg-white solid-top show-btn">
					<view class="action margin-0 flex-sub" @tap="hidePosterShow">取消</view>
					<!-- #ifdef MP || APP-PLUS -->
					<view class="action margin-0 flex-sub solid-left text-red text-bold" @tap="savePoster">保存到相册</view>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<view class="action margin-0 flex-sub solid-left text-red text-bold"  @tap="hidePosterShow">长按图片可保存或分享</view>
					<!-- #endif -->
				</view>
			</view>
		</view>
		<poster id="poster" ref='posterRef' :hide-loading="false" :preload="false" :config="posterConfig"
			@success="onPosterSuccess"
			@fail="onPosterFail"></poster>
	</view>
</template>

<script>
	const app = getApp();
	const { base64src } = require("utils/base64src.js");
	import api from 'utils/api'
	import util from '@/utils/util'
	import __config from '@/config/env';
	import poster from "@/components/wxa-plugin-canvas/poster/index";

	export default {
		components:{
			poster
		},
		props: {
			value: Boolean, // 是否显示弹框
			shareParams: { // 分享参数
				type: Object,
				default: () => {
					return {
						title: '', 	// 分享给微信好友链接时的标题
						desc: '',	// 分享给微信好友链接时的描述
						imgUrl: '',	// 分享给微信好友链接时的图片
						url: '', 	// 分享给微信好友链接时的url,  如果有传值则使用该值，否则用该页的当前路径
                        activityId:'',
						scene: '', 	// 分享海报时的scene,一般为 id
						page: '', 	// 分享海报的page, 如果有传值则使用该值，否则用该页的当前路径
						posterConfig: null // 分享海报的配置
					}
				}
			}
		},
		
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				shareShow: ' show ',
				posterUrl: "",
				posterShow: false,
				posterConfig: null,
				curLocalUrl: null,
				showModal: false,
				isWeiXinBrowser: util.isWeiXinBrowser()
			};
		},
		created() {
			this.initData()
		},
		onShow() {},
		methods: {
			initData(){
				// #ifdef APP-PLUS
				api.wxAppConfig(__config.wxAppId).then(res => {
					if(res.data.data&&res.data.data.isComponent=='1') {
						this.curLocalUrl= util.setAppPlusShareUrl(res.data.data);
					}else{
						this.curLocalUrl = util.setAppPlusShareUrl();
					}
				});
				// #endif
				this.onCreatePoster();
			},
			shareHide() {
				this.$emit('input', false);
			},
			onPosterSuccess(e) {
				this.posterUrl = e;
				this.posterShow = true;
			},
			onPosterFail(err) {
				console.error(err);
			},
			hidePosterShow() {
				this.posterShow = false;
				this.$emit('input', false);
			},
			/**
			 * 异步生成海报
			 */
			onCreatePoster() {
				// #ifdef MP
					const userInfo = uni.getStorageSync('user_info')
					const userCode = userInfo ? '&'+'' : ''//userInfo.userCode
					api.qrCodeUnlimited({
						theme: app.globalData.theme,  // 全局颜色变量
						page: this.shareParams.page ? this.shareParams.page : util.getCurPage(getCurrentPages()), // 当前页面路径
						scene: this.shareParams.scene + userCode
					}).then(res => {
						base64src(res.data, res2 => {
							this.startCreatePoster(res2);
						});
					});
				// #endif
				// #ifdef APP-PLUS
					let url = ''
					if(this.shareParams.url){
						url = this.shareParams.url;
						uni.showLoading({
							title: '海报生成中',
							mask: false
						});
						this.$refs.qrCodeRef._makeCode(url); // 需要先生成二维码后 才能生成海报
					}else{
						api.wxAppConfig(__config.wxAppId).then(res => {
							if(res.data.data&&res.data.data.isComponent=='1') {
								url = util.setAppPlusShareUrl(res.data.data);
							}else{
								url = util.setAppPlusShareUrl();
							}
							uni.showLoading({
								title: '海报生成中',
								mask: false
							});
							this.$refs.qrCodeRef._makeCode(url); // 需要先生成二维码后 才能生成海报
						});
					}
				// #endif
				// #ifdef H5
					let url = '';
					if(this.shareParams.url){
						url = this.shareParams.url
					}else{
						url =  util.setH5ShareUrl();
					}
					uni.showLoading({
						title: '海报生成中',
						mask: false
					});
					this.$refs.qrCodeRef._makeCode(url); // H5需要先生成二维码后 才能生成海报
				// #endif
			},
			startCreatePoster(res){ // 开始 生成海报
				uni.hideLoading();
				// 需要注意：分享海报的图片方法会传入res对象,用 qrCodeName: 'qrCodeName'属性进行唯一标识
				if(!this.shareParams.posterConfig){
					return
				}
				let images = this.shareParams.posterConfig.images;
				// 将二维码图片的base64放入到海报绘制配置中
				images.forEach(val=>{
					if(val.qrCodeName){
						val.url = res;
						return
					}
				})
				this.posterConfig = this.shareParams.posterConfig;
				this.posterShow = false;
				this.$refs.posterRef.onCreate(false, this.posterConfig); // 入参：true为抹掉重新生成
			},
			//点击保存到相册
			savePoster: function() {
				var that = this;
				console.log("----this.posterUrl----",this.posterUrl)
				uni.saveImageToPhotosAlbum({
					filePath: this.posterUrl,
					success(res) {
						that.posterShow = false;
						that.$emit('input', false);
						uni.showModal({
							content: '图片已保存到相册，赶紧晒一下吧~',
							showCancel: false,
							confirmText: '好的',
							confirmColor: '#333',
							success: function(res) {
								if (res.confirm) {
									/* 该隐藏的隐藏 */
									that.$emit('input', false);
								}
							},
							fail: function(res) {
								console.log(res);
							}
						});
					}
				});
			}
		}
	};
</script>
<style>
	.share-txt {
		font-size: 30rpx;
		padding: 30rpx;
		background-color:#d9d9d9;
	}
	
	.show-bg{
		/* height: 84%; */
		margin-top: 120rpx;
	}

	.image-box{
		width: 100%;
	}

	.show-btn{
		margin-top: -130rpx;
	}
</style>
