<template>
  <view class="selTitle-page">
    <view class="row-box">
      <view class="qian"><text style="color: red">*</text>抬头名称</view>
      <view class="hou">
        <block v-if="2 == invoiceType">
          <view style="width: 354rpx">
            <input-fuzzy-search
              ref="fuzzy"
              @search="searchInvoiceTitleBefore"
              :dataSource="dataSource"
              @select="selectInvoiceTitle"
              placeholder="请输入3个以上汉字"
            />
          </view>
        </block>
        <block v-else>
          <input
            class="wenzi"
            style="width: 354rpx"
            placeholder="请输入抬头名称"
            v-model="invoiceTitle"
            @input="inputInvoiceTitle"
          />
        </block>
        <view class="xuan" @click="titleOpen">选择抬头&gt;</view>
      </view>
    </view>
    <uni-popup
      ref="titlePopup"
      @maskClick="titleClose"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="cell-outbox">
        <view class="cell-box">
          <view
            class="cell"
            v-for="(u, i) in titleList"
            :key="i"
            @click="selTitle(u)"
          >
            <view class="shang">{{ u.invoiceTitle }}</view>
            <view class="xia">{{ u.invoiceTaxNo }}</view>
          </view>
        </view>
        <!-- #ifdef MP -->
        <view class="shadow-blur bg-green btn" @click="handleInvoice">
          <text class="cuIcon-weixin" style="margin-right: 8rpx"></text>
          <view>微信导入</view>
        </view>
        <!-- #endif -->
        <view class="btn2" @click="goUserInvoice">添加发票抬头</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/

import inputFuzzySearch from "@/components/invoice/inputFuzzySearch/index.vue";
import { debounceGet } from "@/utils/util.js";
import {
  fuzzyQueryInvoiceTitleApi,
  addUserInvoice,
  getInvoiceTitleInfoApi,
  getUserInvoicePage,
} from "@/pages/invoice/api/userinvoice.js";
export default {
  name: "selTitle",
  components: {
    inputFuzzySearch,
  },
  props: {
    invoiceType: {
      type: Number,
      require: true,
      default: 2,
    },
  },
  data() {
    return {
      dataSource: [],
      debounce: "",
      invoiceTitle: "",
      tempInvoiceTitle: "",
      titleList: [],
    };
  },
  watch: {
    invoiceType(newVal) {
      if (1 == newVal) {
        this.invoiceTitle = "";
      }
    },
  },
  mounted() {
    this.debounce = debounceGet(() => {
      this.searchInvoiceTitle();
    }, 1000);
  },
  methods: {
    //公司发票抬头模糊搜索
    searchInvoiceTitle() {
      this.dataSource = [];
      const params = {
        invoiceTitle: this.tempInvoiceTitle,
      };
      fuzzyQueryInvoiceTitleApi(params).then((res) => {
        if (res.data) {
          res.data.forEach((u) => {
            this.dataSource.push({
              text: u.companyname,
            });
          });

          //有数据的情况隐藏软键盘  
          if (this.dataSource&&this.dataSource.length>0) {
            uni.hideKeyboard()
          }
        }
      });
    },

    searchInvoiceTitleBefore(val) {
      console.log("searchInvoiceTitleBefore",val);
      if (val&&val.trim().length > 3) {
        this.tempInvoiceTitle = val;
        this.debounce();
      }else if(!val&&val==""){
        this.tempInvoiceTitle = "";
        this.invoiceTitle = ""
        this.$emit("input", this.invoiceTitle);
      }
    },

    selectInvoiceTitle(info) {
      console.log("info",info);
      const params = {
        invoiceTitle: info.text,
      };
      getInvoiceTitleInfoApi(params).then((res) => {
        let invoiceInfo = "";
        if (res.data) {
          const {
            taxpayerName,
            taxpayerNo,
            taxpayerAddress,
            taxpayerTelephone,
            taxpayerBankName,
            taxpayerBankAccount,
          } = res.data;
          invoiceInfo = {
            invoiceTitle: taxpayerName,
            invoiceTaxNo: taxpayerNo,
            vatCompanyAddress: taxpayerAddress,
            vatTelphone: taxpayerTelephone,
            vatBankName: taxpayerBankName,
            vatBankAccount: taxpayerBankAccount,
          };
        }
        this.$emit("select", invoiceInfo);
      });
    },

    //个人发票抬头
    inputInvoiceTitle() {
      this.$emit("input", this.invoiceTitle);
    },

    //发票抬头列表
    titleOpen() {
      this.$refs.titlePopup.open("bottom");
      this.getUserInvoicePage();
    },

    async getUserInvoicePage() {
      this.titleList = [];
      const res = await getUserInvoicePage();
      this.titleList = res.data.filter((v) => {
        return v.invoiceType == this.invoiceType;
      });
    },

    selTitle(info) {
      this.$emit("select", info);
      if (2 == this.invoiceType) {
        this.$refs.fuzzy.name = info.invoiceTitle;
      } else {
        this.invoiceTitle = info.invoiceTitle;
      }
      this.titleClose();
    },

    addUserInvoiceInfo(info) {
      const params = {
        invoiceType: info.type,
        invoiceTitle: info.title,
        invoiceTaxNo: info.taxNumber,
        vatCompanyAddress: info.companyAddress,
        vatTelphone: info.telephone,
        vatBankName: info.bankName,
        vatBankAccount: info.bankAccount,
      };
      addUserInvoice(params).then((res) => {
        if (res.data) {
          this.getUserInvoicePage();
        }
      });
    },

    handleInvoice() {
      let that = this;
      uni.authorize({
        scope: "scope.invoiceTitle",
        success() {
          uni.chooseInvoiceTitle({
            success(res) {
              //res.type 0：单位，1：个人
              if (0 == res.type) {
                res.type = 2;
              }
              if (res.type == that.invoiceType) {
                that.addUserInvoiceInfo(res);
              } else {
                let str = "请选择个人抬头信息哦~";
                if (2 == that.invoiceType) {
                  str = "请选择公司抬头信息哦~";
                }
                uni.showToast({
                  title: str,
                  icon: "none",
                });
              }
            },
            fail(err) {
              console.log("fail:" + JSON.stringify(err));
            },
          });
        },
        fail(err) {
          console.log("fail:" + JSON.stringify(err));
        },
      });
    },

    goUserInvoice() {
      this.titleClose();
      uni.navigateTo({
        url: "/pages/invoice/userInvoice/index",
      });
    },

    titleClose() {
      this.$refs.titlePopup.close();
    },
  },
};
</script>

<style scoped lang="scss">
.selTitle-page {
  .row-box {
    display: flex;
    margin-top: 20rpx;
    .qian {
      text-align: right;
      width: 130rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #888888;
      margin-right: 22rpx;
    }
    .hou {
      width: 500rpx;
      height: 72rpx;
      border-bottom: 1rpx dashed #333;
      display: flex;
      .wenzi {
        width: 490rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #000000;
      }
      .xuan {
        margin-left: 10rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgb(0, 122, 255);
      }
    }
  }
  .cell-outbox {
    background-color: #ffffff;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    width: 750rpx;
    height: 800rpx;
    padding-top: 36rpx;
    .cell-box {
      /* #ifdef MP */
      height: 578rpx;
      /* #endif */
      /* #ifndef MP */
      height: 670rpx;
      /* #endif */
      overflow: auto;
      .cell {
        height: 100rpx;
        border-top: 1rpx solid #dcdcdc;
        font-size: 32rpx;
        &:last-child {
          border-bottom: 1rpx solid #dcdcdc;
        }
        .shang {
          padding-left: 30rpx;
          font-size: 32rpx;
          font-weight: bold;
        }
        .xia {
          padding-left: 30rpx;
        }
      }
    }
    .btn {
      position: absolute;
      left: 25rpx;
      bottom: 100rpx;
      width: 700rpx;
      height: 80rpx;
      line-height: 30rpx;
      text-align: center;
      border-radius: 40rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .btn2 {
      position: absolute;
      left: 25rpx;
      bottom: 10rpx;
      width: 700rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 40rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      background: linear-gradient(90deg, #f32633 0%, #ce1a26 100%);
    }
  }
}
</style>
