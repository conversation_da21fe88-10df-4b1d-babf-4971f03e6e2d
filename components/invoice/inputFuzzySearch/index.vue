<template>
  <view class="inputFuzzySearch-page">
    <input
      :placeholder="placeholder"
      @input="search"
      v-model="name"
    />
    <view class="cell-box" :style="{ width: `${listWidth}rpx` }">
      <view class="cell" v-for="(u, i) in list" :key="i" @click="select(u)">
        {{ u.text }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    placeholder: {
      type: String,
      require: false,
      default: "请输入",
    },
    listWidth: {
      type: String,
      require: false,
      default: "500",
    },
    dataSource: {
      type: Array,
      require: true,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      list: [],
      name: "",
    };
  },
  watch: {
    dataSource: {
      handler(newVal) {
        if (!this.name) {
          this.list = [];
        } else {
          this.list = newVal;
        }
      },
      deep: true,
    },
  },
  methods: {
    search() {
      this.$emit("search", this.name);
    },
    select(info) {
      this.name = info.text;
      this.$emit("select", info);
      this.list = [];
    },
    hide() {
      setTimeout(() => {
        this.list = [];
      }, 200);
    },
  },
};
</script>

<style scoped lang="scss">
.inputFuzzySearch-page {
  position: relative;
  input {
    margin-top: -12rpx;
    height: 70rpx;
    font-size: 28rpx;
    box-sizing: border-box;
  }

  .cell-box {
    position: absolute;
    z-index: 50;
    left: 0;
    top: 100%;
    background-color: #fff;
    box-shadow: 0 4rpx 4rpx rgba(0, 0, 0, 0.1), -4rpx 0 4rpx rgba(0, 0, 0, 0.1),
      4rpx 0 4rpx rgba(0, 0, 0, 0.1);
    border-radius: 8rpx;
    .cell {
      padding-left: 10rpx;
      padding-right: 10rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      border-left: 1rpx solid #e4e7ed;
      border-right: 1rpx solid #e4e7ed;
      line-height: 80rpx;
      &:last-child {
        border-bottom: 1rpx solid #e4e7ed;
      }
    }
  }
}
</style>
