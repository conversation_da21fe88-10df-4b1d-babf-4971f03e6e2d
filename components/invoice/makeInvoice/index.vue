<template>
  <view class="makeInvoice-page">
    <uni-popup
      ref="invoicePopup"
      :mask-click="false"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="whole-box">
        <view class="tou-box">
          <view style="width: 140rpx"></view>
          <view class="tou">开票</view>
          <view class="you">
            <text @click="tipOpen">发票须知</text>
            <text class="guan" @click="invoiceClose(1)">x</text>
          </view>
        </view>
        <view class="core-box">
          <view class="hang">
            <view class="qian">发票类型</view>
            <view class="hou">
              <view class="btn sel" style="width: 214rpx">电子普通发票</view>
            </view>
          </view>
          <view class="hang">
            <view class="qian">发票内容</view>
            <view class="hou">
              <block v-if="contentFlag">
                <view
                  v-if="spuType== 6"
                  class="btn sel"
                  style="width: 162rpx"
                  @click="toggleContent(3)"
                >
                  礼品卡
                </view>
                <view
                  v-if="spuType!== 6"
                  class="btn"
                  style="width: 162rpx"
                  :class="[1 == contentVal ? 'sel' : 'unsel']"
                  @click="toggleContent(1)"
                >
                  商品明细
                </view>
                <view
                  v-if="spuType!== 6"
                  class="btn"
                  style="width: 162rpx"
                  :class="[2 == contentVal ? 'sel' : 'unsel']"
                  @click="toggleContent(2)"
                >
                  商品类别
                </view>
              </block>
              <block v-else>
                <view class="btn sel" style="width: 140rpx">礼品卡</view>
              </block>
            </view>
          </view>
          <view class="hang">
            <view class="qian">抬头类型</view>
            <view class="hou">
              <view
                class="btn"
                style="width: 116rpx"
                :class="[2 == invoiceType ? 'sel' : 'unsel']"
                @click="toggleType(2)"
              >
                单位
              </view>
              <view
                class="btn"
                style="width: 116rpx"
                :class="[1 == invoiceType ? 'sel' : 'unsel']"
                @click="toggleType(1)"
              >
                个人
              </view>
            </view>
          </view>
          <sel-title
            :invoiceType="invoiceType"
            @select="selectInvoiceTitle"
            @input="inputInvoiceTitle"
          />
          <view class="hang" v-if="2 == invoiceType">
            <view class="qian"><text style="color: red">*</text>单位税号</view>
            <view class="hou">
              <input
                class="wen"
                placeholder="请输入发票单位税号"
                v-model="invoiceTaxNo"
              />
            </view>
          </view>

          <block v-if="2 == invoiceType">
            <view class="kuai-box">
              <view class="kuai">更多信息</view>
              <view class="gengduo" @click="convert">
                <text v-if="moreFlag">收起</text>
                <text v-else>展开</text>
                <image
                  class="jt"
                  :src="moreFlag ? iconPic.jiantou2 : iconPic.jiantou"
                />
              </view>
            </view>
            <block v-if="moreFlag">
              <view class="hang">
                <view class="qian">注册地址</view>
                <view class="hou">
                  <input
                    class="wen"
                    placeholder="选填"
                    v-model="vatCompanyAddress"
                  />
                </view>
              </view>
              <view class="hang">
                <view class="qian">注册电话</view>
                <view class="hou">
                  <input class="wen" placeholder="选填" v-model="vatTelphone" />
                </view>
              </view>
              <view class="hang">
                <view class="qian">开户银行</view>
                <view class="hou">
                  <input class="wen" placeholder="选填" v-model="vatBankName" />
                </view>
              </view>
              <view class="hang">
                <view class="qian">银行账号</view>
                <view class="hou">
                  <input
                    class="wen"
                    placeholder="选填"
                    v-model="vatBankAccount"
                  />
                </view>
              </view>
            </block>
          </block>
          <view class="kuai-box">
            <view class="kuai">开票通知</view>
            <view></view>
          </view>
          <view class="hang">
            <view class="qian"><text style="color: red">*</text>手机号码</view>
            <view class="hou">
              <input
                class="wen"
                placeholder="请输入手机号码"
                v-model="notifyPhone"
              />
            </view>
          </view>
          <view class="hang">
            <view class="qian">
              <!-- <text style="color: red">*</text> -->
              电子邮箱
            </view>
            <view class="hou">
              <input
                class="wen"
                placeholder="请输入电子邮箱账号"
                v-model="email"
              />
            </view>
          </view>
        </view>
        <view class="annniu" @click="saveOpen">提交申请</view>
        <view class="annniu2" @click="invoiceClose(2)">不开发票</view>
      </view>
    </uni-popup>
    <uni-popup
      ref="tipPopup"
      @maskClick="tipClose"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="neirong-box">
        <view class="toubu">发票须知</view>
        <view class="neirong">
          <view>
            1.开票金额为消费者实付款金额，优惠券礼金、积分、红包、礼品卡等不在开票范围内。
          </view>
          <view>2.如果订单发生退货退款，开票金额将变更为最终实付款金额。</view>
        </view>
        <view class="annniu" @click="tipClose">我知道了</view>
      </view>
    </uni-popup>
    <submit-confirm
      :invoiceData="invoiceData"
      @close="saveClose"
      v-if="saveFlag"
    />
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/
import selTitle from "@/components/invoice/selTitle/index.vue";
import submitConfirm from "@/components/invoice/submitConfirm/index.vue";
import { onlineOrderPreInvoice } from "@/pages/invoice/api/userinvoice.js";
import validate from 'utils/validate'

export default {
  name: "makeInvoice",
  components: {
    selTitle,
    submitConfirm,
  },

  props:{
    spuType:{
      type: Number,
      default: 1,
    }
  },

  data() {
    return {
      xid: "",
      contentFlag: true,
      contentVal: this.spuType==6? 3 : 1,//订单状态6是礼品卡，传3代表是礼品卡 1是明细 2是类别
      invoiceType: 2,
      invoiceTitle: "",
      invoiceTaxNo: "",
      notifyPhone: "",
      email: "",
      vatCompanyAddress: "",
      vatTelphone: "",
      vatBankName: "",
      vatBankAccount: "",
      // invoiceAmount:0,
      moreFlag: false,

      invoiceData: "",
      saveFlag: false,

      iconPic: {
        jiantou: "http://img.songlei.com/invoice/jiantou.png",
        jiantou2: "http://img.songlei.com/invoice/jiantou2.png",
      },
      params:{}
    };
  },
  mounted() {
    this.$refs.invoicePopup.open("bottom");
  },
  methods: {
    invoiceClose(val) {
      // this.$refs.invoicePopup.close();
      this.$emit("close", val);
    },

    toggleContent(val) {
      this.contentVal = val;
    },
    toggleType(val) {
      this.invoiceType = val;
      this.invoiceTitle = "";
      this.invoiceTaxNo = "";
      this.vatCompanyAddress = "";
      this.vatTelphone = "";
      this.vatBankName = "";
      this.vatBankAccount = "";
      this.moreFlag = false;
    },

    //抬头名称
    selectInvoiceTitle(info) {
      if (2 == this.invoiceType) {
        this.invoiceTaxNo = "";
        this.vatCompanyAddress = "";
        this.vatTelphone = "";
        this.vatBankName = "";
        this.vatBankAccount = "";
        if (info) {
          const {
            invoiceTitle,
            invoiceTaxNo,
            vatCompanyAddress,
            vatTelphone,
            vatBankName,
            vatBankAccount,
          } = info;
          this.invoiceTitle = invoiceTitle;
          this.invoiceTaxNo = invoiceTaxNo;
          this.vatCompanyAddress = vatCompanyAddress;
          this.vatTelphone = vatTelphone;
          this.vatBankName = vatBankName;
          this.vatBankAccount = vatBankAccount;
        }
      } else {
        this.invoiceTitle = info.invoiceTitle;
      }
    },
    inputInvoiceTitle(val) {
      this.invoiceTitle = val;
    },

    // 展开收起
    convert() {
      this.moreFlag = !this.moreFlag;
    },
    // 发票须知
    tipOpen() {
      this.$refs.tipPopup.open();
    },
    tipClose() {
      this.$refs.tipPopup.close();
    },

    //提交
    saveOpen() {
      let str = "";
      if (!this.invoiceTitle.trim()) {
        str = "抬头名称不能为空哦~"; // 有抬头肯定就有单位税号
      } else if (!validate.validateMobile(this.notifyPhone)) {
        str = "请先输入正确的手机号码哦~";
      }else if (this.email) {
        // str = "请先输入电子邮箱哦~";
        const regex = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;
        if (!regex.test(this.email)) {
          str = "电子邮箱格式不正确哦~";
        }
      } 
      if (str) {
        uni.showToast({
          title: str,
          icon: "none",
        });
        return;
      }
      this.invoiceData = {
        invoiceTitle: this.invoiceTitle,
        invoiceType: this.invoiceType,
        invoiceTaxNo: this.invoiceTaxNo,
        notifyPhone: this.notifyPhone,
        email: this.email,
      };
      this.saveFlag = true;
    },
    saveClose(flag) {
      this.saveFlag = flag;
      let that = this;
      // this.invoiceClose(3);
      // return;
      if (flag) {
        const params = {
          scene: 1,
          invoiceType: this.invoiceType,
          // invoiceAmount: this.invoiceAmount,
          callbackParameter: this.callbackParameter,
          invoiceTitle: this.invoiceTitle,
          notifyPhone: this.notifyPhone,
          email: this.email,
          invoiceClassification:this.contentVal,
        };
        if (2 == this.invoiceType) {
          params.invoiceTaxNo = this.invoiceTaxNo;
          params.vatCompanyAddress = this.vatCompanyAddress;
          params.vatTelphone = this.vatTelphone;
          params.vatBankName = this.vatBankName;
          params.vatBankAccount = this.vatBankAccount;
        }
        this.params = params
        
        // onlineOrderPreInvoice(this.params).then(() => {
          // this.invoiceClose(3);
        // });

        that.$noMultipleClicks(that.invoiceClose(3))
      }
    },

    // 线上商品详情开票
    onlineOrderPreInvoiceOpen(params){
      // 礼品卡的发票分类
      if (params&&params.spuType == 6 ) {
        this.params.invoiceClassification = 3//3代表是礼品卡 1是明细 2是类别
      }
      onlineOrderPreInvoice(Object.assign(this.params,...params)).then(() => {
          // this.invoiceClose(3);
        });
    }
  },
};
</script>

<style scoped lang="scss">
.makeInvoice-page {
  .whole-box {
    background: #fff;
    border-top-left-radius: 22rpx;
    border-top-right-radius: 22rpx;
    padding-bottom: 6rpx;

    .tou-box {
      width: 750rpx;
      padding: 12rpx 24rpx;
      padding-bottom: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .tou {
        font-size: 30rpx;
        font-weight: bold;
        color: #000;
      }
      .you {
        font-size: 28rpx;
        .guan {
          margin-left: 18rpx;
          font-size: 40rpx;
        }
      }
    }
    .core-box {
      margin: auto;
      width: 720rpx;
      height: 788rpx;
      padding: 0rpx 24rpx;
      padding-bottom: 10rpx;
      overflow: auto;
      .hang {
        display: flex;
        margin-top: 20rpx;
        .qian {
          text-align: right;
          width: 130rpx;
          font-size: 28rpx;
          font-weight: 500;
          color: #888888;
          margin-right: 30rpx;
        }
        .hou {
          width: 500rpx;
          height: 72rpx;
          border-bottom: 1rpx dashed #333;
          display: flex;
          .btn {
            height: 56rpx;
            line-height: 56rpx;
            text-align: center;
            border-radius: 28rpx;
            font-size: 24rpx;
            font-weight: 500;
            margin-right: 22rpx;
          }
          .sel {
            background: #f12533;
            color: #fff;
          }
          .unsel {
            background: #f4f2f7;
            color: #000000;
          }
          .wen {
            width: 490rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #000000;
          }
        }
      }
      .kuai-box {
        margin-top: 18rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .kuai {
          font-size: 30rpx;
          font-family: PingFang SC;
          font-weight: bold;
        }
        .gengduo {
          display: flex;
          align-items: center;
          justify-content: center;
          .jt {
            margin-left: 6rpx;
            margin-right: 10rpx;
            width: 24rpx;
            height: 9rpx;
          }
        }
      }
    }
    .annniu {
      margin: auto;
      width: 700rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: linear-gradient(90deg, #f32633 0%, #ce1a26 100%);
      color: #ffffff;
      border-radius: 40rpx;
    }
    .annniu2 {
      margin: auto;
      margin-top: 14rpx;
      width: 700rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: #e4e4e4;
      color: #000;
      border-radius: 40rpx;
    }
  }
  .neirong-box {
    width: 630rpx;
    height: 411rpx;
    background: #ffffff;
    border-radius: 23rpx;
    padding-top: 30rpx;
    .toubu {
      text-align: center;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #000000;
    }
    .neirong {
      padding: 0 42rpx;
      margin-top: 28rpx;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #000000;
    }
    .annniu {
      margin: auto;
      margin-top: 48rpx;
      width: 335rpx;
      height: 60rpx;
      text-align: center;
      line-height: 60rpx;
      background: #ff0000;
      border-radius: 30rpx;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
    }
  }
}
</style>

