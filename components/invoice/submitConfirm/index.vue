<template>
  <view class="submitConfirm-page">
    <uni-popup
      ref="popup"
      :mask-click="false"
      maskBackgroundColor="rgba(0, 0, 0, 0.8)"
    >
      <view class="xinxi-outbox">
        <view class="guanbi" @click="close(false)">x</view>
        <view class="toubu">开具电子发票</view>
        <view class="tishi">
          <view>*请确认邮箱无误</view>
          <view>电子发票将在系统开具后发送至您的邮箱，请注意查收。</view>
        </view>
        <view class="xinxi-box">
          <view class="xinxi">
            <view class="zou">发票类型</view>
            <view class="you">电子发票</view>
          </view>
          <view class="xinxi">
            <view class="zou">发票抬头</view>
            <view class="you">{{ invoiceData.invoiceTitle }}</view>
          </view>
          <view class="xinxi" v-if="2 == invoiceData.invoiceType">
            <view class="zou">纳税人识别号</view>
            <view class="you">{{ invoiceData.invoiceTaxNo }}</view>
          </view>
          <view class="xinxi">
            <view class="zou">手机号码</view>
            <view class="you">{{ invoiceData.notifyPhone }}</view>
          </view>
          <view class="xinxi">
            <view class="zou">电子邮箱</view>
            <view class="you">{{ invoiceData.email }}</view>
          </view>
        </view>
        <view
          class="annniu"
          :style="{
            'margin-top': 2 == invoiceData.invoiceType ? '40rpx' : '100rpx',
          }"
          @click="close(true)"
        >
          确认提交
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2023年10月31日
 **/

function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            func.apply(context, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
export default {
  name: "submitConfirm",
  props: {
    invoiceData: {
      type: Object,
      require: true,
      default: null,
    },
  },
  mounted() {
    this.$refs.popup.open("bottom");
  },
  methods: {
    close: debounce(function(flag) {
        this.$emit("close", flag);
        this.$refs.popup.close();
    }, 2000)
  },
};
</script>

<style scoped lang="scss">
.submitConfirm-page {
  .xinxi-outbox {
    background-color: #ffffff;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    width: 750rpx;
    height: 710rpx;
    .guanbi {
      height: 40rpx;
      padding-top: 10rpx;
      margin-right: 40rpx;
      text-align: right;
      font-size: 40rpx;
      font-weight: bold;
      color: #9d9d9d;
    }

    .toubu {
      text-align: center;
      font-size: 32rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #282828;
    }

    .tishi {
      margin-top: 50rpx;
      text-align: center;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #f32634;
    }

    .xinxi-box {
      margin: 40rpx;
      .xinxi {
        margin-top: 25rpx;
        display: flex;
        .zou {
          width: 180rpx;
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #888888;
        }

        .you {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #000000;
        }
      }
    }

    .annniu {
      margin: auto;
      width: 700rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 40rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      background: linear-gradient(90deg, #f32633 0%, #ce1a26 100%);
    }
  }
}
</style>
