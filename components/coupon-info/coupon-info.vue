<template>
  <navigator
    :url="
      '/pages/coupon/coupon-detail/index?id=' +
      couponInfo.id +
      '&toUse=' +
      toUse +
      '&couponUser=' +
      JSON.stringify(couponInfo.couponUser)
    "
    hover-class="none"
  >
    <!-- 代金券 -->
    <view
      class="flex radius text-white main electronic-coupons"
      v-if="couponInfo.type == '1'&&couponInfo.isShow == '1'"
    >
      <!-- 左大右小 -->
      <!-- <view class="flex-twice padding-sm shadow-blur radius t1-r"> -->
      <view class="discount-padding-sm shadow-blur radius t2-r">
        <view class="flex">
          <view style="text-align: center">
            <view class="flex-sub flex">
              <view style="text-align: center; margin: 0 auto">
                <!-- <text
                  class="text-price text-xl margin-top"
                  v-if="couponInfo.giveType == '1'"
                ></text> -->
                <text
                  v-if="couponInfo.giveType == '1'"
                  class="text-sl text-bold overflow-1 number"
                >{{ couponInfo.reduceAmount }}
                  <text style="font-size: 23rpx; font-weight: 400">元 </text>
                </text>

                <text
                  v-if="couponInfo.giveType == '2'"
                  class="text-xl overflow-1 margin-left-xs"
                >满赠
                </text>
              </view>
            </view>

            <view class="coupon-line1"></view>

            <view>
              <view class="text-xs overflow-1 padding-left-xs">订单满{{ couponInfo.premiseAmount }}元可使用</view>
              <view class="text-xs">
                {{
                  couponInfo.suitType == '1'
                    ? '全部商品可用'
                    : couponInfo.suitType == '2'
                    ? '指定商品可用'
                    : couponInfo.suitType == '3'
                    ? '指定商品不可用'
                    : couponInfo.suitType == '4'
                    ? '指定商品可用'
                    : ''
                }}
              </view>
            </view>

            <image
              class="discount-img"
              :src="couponInfo.couponType == 1 ? $imgUrl('coupon/discount.png') : $imgUrl('coupon/discount-offline.png')"
            ></image>
          </view>
        </view>
      </view>

      <view class="flex-sub padding-sm shadow-blur radius t2-l">
        <view class="flex justify-between">
          <view>
            <view
              class="ticket"
              style="color: #000000"
            >代金券</view>
            <view
              class="text-sm"
              style="overflow: hidden;
                    color: #000000;
                    width: 290rpx;
                    white-space: nowrap;
                    text-overflow: ellipsis;"
            >
              <!-- <text class="cuIcon-shop"></text>{{couponInfo.shopInfo.name}} -->
              <text
                v-if="couponInfo.rangeType != '1'"
                class="cuIcon-shop"
              ></text>
              {{
                couponInfo.rangeType != '1'
                  ? couponInfo.shopInfo.name
                  : couponInfo.name
              }}
            </view>

            <view class="validity margin-top-sm">
              <view
                class="text-xs"
                style="color: #cccccc"
                v-if="couponInfo.expireType == '1'"
              >领取后{{ couponInfo.validDays }}天有效</view>
              <view
                class="text-xs"
                style="color: #cccccc"
                v-if="couponInfo.expireType == '2'"
              >
                {{ couponInfo.validBeginTime }}至{{ couponInfo.validEndTime }}
              </view>
            </view>
          </view>

          <view style="min-width: 130rpx;">
            <button
              class="cu-btn bg-white round text-bold sm margin-top-sm"
              style="
                color: #ffffff;
                background: linear-gradient(-90deg, #ff5147 0%, #ff936c 100%);
              "
              v-if="!couponInfo.couponUser"
              @tap.stop="couponUserSave"
            >
              立即领取
            </button>

            <view
              v-if="couponInfo.couponUser"
              style="position: relative"
            >
              <view
                class="padding-xs text-bold text-sm"
                style="color: #cccccc;"
                v-if="!toUse"
              >已领取</view>
              <view
                class="cu-btn bg-white text-xs round sm toUse"
                @tap.stop="toGoodsList(couponInfo.id, couponInfo.couponUser.id)"
                v-if="toUse"
              >已领取，去使用 <text class="cuIcon-right"></text></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 折扣券 -->
    <view
      class="flex radius text-white main electronic-coupons"
      v-if="couponInfo.type == '2'&&couponInfo.isShow == '1'"
    >
      <!-- 左大右小 -->
      <!-- <view class="flex-twice padding-sm shadow-blur radius t1-r"> -->
      <view class="discount-padding-sm shadow-blur radius t2-r">
        <view class="flex">
          <view style="text-align: center">
            <view class="flex-sub flex">
              <view style="text-align: center; margin: 0 auto">
                <!-- <text
                  class="text-price text-xl margin-top"
                  v-if="couponInfo.giveType == '1'"
                ></text> -->
                <text class="text-sl text-bold overflow-1 number">{{ couponInfo.discount }}
                  <text style="font-size: 23rpx; font-weight: 400">折</text>
                </text>

                <text
                  v-if="couponInfo.giveType == '2'"
                  class="text-xl overflow-1 margin-left-xs"
                >满赠
                </text>
              </view>
            </view>

            <view class="coupon-line1"></view>

            <view>
              <view class="text-xs overflow-1 padding-left-xs">订单满{{ couponInfo.premiseAmount }}元可使用</view>
              <view class="text-xs">
                {{
                  couponInfo.suitType == '1'
                    ? '全部商品可用'
                    : couponInfo.suitType == '2'
                    ? '指定商品可用'
                    : couponInfo.suitType == '3'
                    ? '指定商品不可用'
                    : couponInfo.suitType == '4'
                    ? '指定商品可用'
                    : ''
                }}
              </view>
            </view>

            <image
              class="discount-img"
              :src="couponInfo.couponType != 1 ? $imgUrl('coupon/discount.png') : $imgUrl('coupon/discount-offline.png')"
            ></image>
          </view>
        </view>
      </view>

      <view class="flex-sub padding-sm shadow-blur radius t2-l">
        <view class="flex justify-between">
          <view>
            <view
              class="ticket"
              style="color: #000000"
            >折扣券</view>
            <view
              class="text-sm"
              style="overflow: hidden;
                    color: #000000;
                    width: 290rpx;
                    white-space: nowrap;
                    text-overflow: ellipsis;"
            >
              <text
                class="cuIcon-shop"
                v-if="couponInfo.rangeType != '1'"
              ></text>{{
                couponInfo.rangeType != '1'
                  ? couponInfo.shopInfo.name
                  : couponInfo.name
              }}
            </view>

            <view class="validity margin-top-sm">
              <view
                class="text-xs"
                style="color: #cccccc"
                v-if="couponInfo.expireType == '1'"
              >领取后{{ couponInfo.validDays }}天有效</view>
              <view
                class="text-xs"
                style="color: #cccccc"
                v-if="couponInfo.expireType == '2'"
              >
                {{ couponInfo.validBeginTime }}至{{
                  couponInfo.validEndTime
                }}</view>
            </view>
          </view>

          <view>
            <button
              class="cu-btn bg-white text-bold round sm margin-top-sm"
              style="
                color: #ffffff;
                background: linear-gradient(-90deg, #ff5147 0%, #ff936c 100%);
                width: 120rpx;
              "
              v-if="!couponInfo.couponUser"
              @tap.stop="couponUserSave"
            >
              立即领取
            </button>

            <view
              v-if="couponInfo.couponUser"
              style="position: relative"
            >
              <view
                class="padding-xs text-bold"
                style="color: #cccccc;width: 120rpx;"
                v-if="!toUse"
              >已领取</view>
              <view
                class="cu-btn bg-white text-xs round sm toUse"
                @tap.stop="toGoodsList(couponInfo.id, couponInfo.couponUser.id)"
                v-if="toUse"
              >已领取，去使用 <text class="cuIcon-right"></text></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </navigator>
</template>

<script>
import api from 'utils/api'

export default {
  data () {
    return {

    };
  },
  props: {
    couponInfo: {
      type: Object,
      default: () => ({})
    },
    toUse: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    couponUserSave () {
      console.log("点击之后有效果")
      api.couponUserSave({
        couponId: this.couponInfo.id
      }).then(res => {
        uni.showToast({
          title: '领取成功',
          icon: 'success',
          duration: 2000
        });
        this.couponInfo.couponUser = res.data;
        console.log("this.couponInfo.couponUser==>", this.couponInfo.couponUser);
        this.$emit('receiveCoupon', this.couponInfo);
      });
    },

    //去使用的跳转 通过优惠劵详情 获取 对应商品列表
    toGoodsList (id, couponUserId) {
	  // 以下是优化之后的方式
	  uni.navigateTo({
	    url: "/pages/goods/goods-list/index?couponId="+id
	  });
    }

  }
};
</script>
<style>

.toUse {
  color: #cccccc;
  position: absolute;
  right: 0rpx;
  display: block;
  width: 200rpx;
  top: -5rpx;
  padding: 0rpx;
}
</style>
