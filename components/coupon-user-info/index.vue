<template>
	<navigator :url="
      '/pages/coupon/coupon-detail/index?id=' +
      couponUserInfo.couponId +
      '&toUse=' +
      toUse +
      '&status=' +
      status +
      '&couponUser=' +
      JSON.stringify(couponUserInfo)
    " hover-class="none">
		<view>
			<!-- 代金券 -->
			<view class="flex radius text-white main electronic-coupons" v-if="couponUserInfo.type == '1'">
				<view class="discount-padding-sm shadow-blur radius t2-r" :style="{
				  backgroundSize:'100%',
				  backgroundImage:  `url(${bgImg})`,
				}">
					<view class="flex">
						<view style="text-align: center">
							<view class="flex-sub flex">
								<view style="text-align: center; margin: 0 auto">
									<text v-if="couponUserInfo.giveType == '1'"
										class="text-sl overflow-1 number text-bold margin-left-xs">
										{{ couponUserInfo.reduceAmount }}
										<text style="font-size: 23rpx; font-weight: 400"> 元 </text>
									</text>

									<text v-if="couponUserInfo.giveType == '2'"
										class="text-xl overflow-1 margin-left-xs">
										满赠
									</text>
								</view>
							</view>

							<view class="coupon-line1"></view>

							<view>
								<view class="text-xs overflow-1 padding-left-xs">
									订单满{{ couponUserInfo.premiseAmount }}元可使用
								</view>
								<view class="text-xs" style="padding-bottom: 10rpx;">
									{{
										couponUserInfo.suitType == '1'
										  ? '全部商品可用'
										  : couponUserInfo.suitType == '2'
										  ? '指定商品可用'
										  : couponUserInfo.suitType == '3'
										  ? '指定商品不可用'
										  : couponUserInfo.suitType == '4'
										  ? '指定商品可用'
										  : ''
									  }}
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 代金券 -->
				<view class="flex-sub discount-padding-sm shadow-blur radius t2-l" style="background: #fff">
					<view class="ticket margin-top-sm" style="color: #000000">代金券
					</view>

					<view class=" flex justify-between align-center"  :style="{
							color: '#000000',
							width: couponUserInfo.status == '2'?'330rpx':'auto'
						}">
						<view class="text-sm overflow-2" >
							{{ couponUserInfo.couponName}}
						</view>
						<view class="coupon-num" v-if="showNum&&couponUserInfo.couponNum>0"
							style="font-size: 30rpx;color:#8E8E8F">
							<text style="font-size: 22rpx;">X</text>
							<text> {{couponUserInfo.couponNum}} </text>
						</view>
					</view>

					<view class="round" style="color: #cccccc" v-if="couponUserInfo.status == '1'">已使用
					</view>

					<view class="round" style="color: #cccccc" v-if="couponUserInfo.status == '2'">已过期
					</view>

					<view class="validity  flex justify-between align-center" style="color: #9b9c9d"
						:style="{paddingTop: couponUserInfo.status == '2'||couponUserInfo.status == '1'?'0':'20rpx'}">
						<view class="text-xs" style="color: #9b9c9d;">
							有效期至{{ couponUserInfo.validEndTime }}
						</view>
						<view class="use-btn" @tap.stop="toGoodsList(couponUserInfo.couponId,couponUserInfo.id)"
							v-if="toUse && couponUserInfo.status == '0'">
							去使用
						</view>
					</view>
					
					<image v-if="couponUserInfo.status == '2'" class="Invalid-img"
						:src="$imgUrl('coupon/Invalid.png')" />
				</view>
			</view>

			<!-- 折扣券 -->
			<view class="flex radius text-white main electronic-coupons" v-if="couponUserInfo.type == '2'">
				<view class="discount-padding-sm shadow-blur radius t2-r" :style="{
				  backgroundSize:'100%',
				   backgroundImage:  `url(${bgImg})`
				}">
					<view class="flex-sub flex">
						<view style="text-align: center; margin: 0 auto">
							<text class="text-sl text-bold overflow-1 number">{{ couponUserInfo.discount }}
								<text style="font-size: 23rpx; font-weight: 400">折</text>
							</text>
						</view>
					</view>

					<view class="coupon-line1"></view>
					<!-- <view class="discount"> -->
					<view>
						<view class="text-xs overflow-1 padding-left-xs">
							订单满{{ couponUserInfo.premiseAmount }}元可使用</view>
						<view class="text-xs" style="text-align:center;">
							{{
                    couponUserInfo.suitType == '1'
                      ? '全部商品可用'
                      : couponUserInfo.suitType == '2'
                      ? '指定商品可用'
                      : couponUserInfo.suitType == '3'
                      ? '指定商品不可用'
                      : couponUserInfo.suitType == '4'
                      ? '指定商品可用'
                      : ''
                  }}
						</view>
					</view>
				</view>
				<!-- 折扣劵 -->
				<view class="flex-sub discount-padding-sm shadow-blur radius t2-l" style="background: #fff">
					<view class="ticket margin-top-sm" style="color: #000000">折扣券</view>

					<view class=" flex justify-between align-center">
						<view class="text-sm overflow-2" style="color: #000000;width: 290rpx;">
							{{ couponUserInfo.couponName}}
						</view>
						<view class="coupon-num" v-if="showNum&&couponUserInfo.couponNum>0"
							style="font-size: 30rpx;color:#8E8E8F">
							<text style="font-size: 22rpx;">X</text>
							<text> {{couponUserInfo.couponNum}} </text>
						</view>
					</view>

					<view class="round" style="color: #cccccc" v-if="couponUserInfo.status == '1'">
						已使用
					</view>
					<view class="round" style="color: #cccccc" v-if="couponUserInfo.status == '2'">
						已过期
					</view>

					<view class="coupon-label1"
						:style="couponUserInfo.couponType==1?'background: #cfab89':'background: #CF2D45'">
						{{couponUserInfo.couponType==1?'线上劵':'通用劵'}}
					</view>
					<view class="validity margin-top-sm flex justify-between" style="color: #9b9c9d">
						<view class="text-xs">
							有效期至{{ couponUserInfo.validEndTime }}
						</view>
						<view class="use-btn" @tap.stop="toGoodsList(couponUserInfo.couponId,couponUserInfo.id)"
							v-if="toUse && couponUserInfo.status == '0'">
							去使用
						</view>
					</view>
					
					<image v-if="couponUserInfo.status == '2'" class="Invalid-img"
						:src="$imgUrl('coupon/Invalid.png')" />
				</view>

			</view>

		</view>
	</navigator>
</template>

<script>
	import api from 'utils/api'

	export default {
		data() {
			return {};
		},

		components: {},
		props: {
			couponUserInfo: {
				type: Object,
				default: () => ({})
			},
			status: {
				type: String | Number,

			},
			toUse: {
				type: Boolean,
				default: true
			},
			showNum: {
				type: Boolean,
				default: false //是否显示优惠券数量，因为这个字段是后面添加的，
			}
		},
		computed: {
			bgImg(){
				if(this.toUse &&  this.couponUserInfo.status == '0') {
					return this.couponUserInfo.couponType == 1 ? this.$imgUrl('coupon/discount.png') : this.$imgUrl('coupon/discount-offline.png');
				} else {
					return this.couponUserInfo.couponType == 1 ? this.$imgUrl('coupon/discount2.png') : this.$imgUrl('coupon/discount-offline2.png')
				}
			},
		},
		methods: {

			//去使用的跳转 通过优惠劵详情 获取 对应商品列表
			toGoodsList(id, couponUserId) {
				uni.navigateTo({
					url: "/pages/goods/goods-list/index?couponId=" + id
				});

			}
		}
	};
</script>
<style scoped>
	.coupon-num {
		padding: 0 20rpx;
		font-size: 20rpx;
	}

	.use-btn {
		width: 152rpx;
		height: 50rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		border: 1rpx solid #939395;
		font-weight: 500;
		font-size: 22rpx;
		color: #4D4D4D;
		line-height: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.discount-padding-sm {
		padding: 0 18rpx 20rpx;
	}
	
</style>