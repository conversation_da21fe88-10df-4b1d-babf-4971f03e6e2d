<!-- 分享组件 (重构版)  -->
<!-- 分享给微信好友 和 分享海报生成 -->
<!-- 可以单独设置分享微信好友和海报生成 showShareFriends 和 showSharePoster -->
<template>
  <view>
    <!-- 显示第一个 -->
    <view
      v-if="value"
      :class="'cu-modal bottom-modal ' + (value == true ? 'show' : '')"
      @tap="shareHide"
    >
      <view class="cu-dialog" @tap.stop>
        <!-- <view class="cu-bar bg-white">
					<view class="action text-green"></view>
					<view class="action text-red" @tap="shareHide">取消</view>
				</view> -->
        <view class="flex flex-direction">
          <!-- #ifdef APP-PLUS || MP-WEIXIN -->
          <share-friends
            v-show="showShareFriends"
            :shareObj="{
              title: shareParams.title,
              desc: shareParams.desc,
              imgUrl: shareParams.imgUrl,
              url: shareParams.url,
              page: shareParams.page,
              scene: shareParams.scene,
              trackParams: shareParams.trackParams,
            }"
          ></share-friends>
          <!-- #endif -->
          <!-- #ifdef H5 -->
          <share-friends
            v-show="showShareFriends && isWeiXinBrowser"
            :shareObj="{
              title: shareParams.title,
              desc: shareParams.desc,
              imgUrl: shareParams.imgUrl,
              url: shareParams.url,
              page: shareParams.page,
              scene: shareParams.scene,
              trackParams: shareParams.trackParams,
            }"
          ></share-friends>
          <!-- #endif -->
          <view
            v-show="showSharePoster"
            style="
              width: 100%;
              font-size: 24rpx;
              background-color: #fff;
              border-bottom: solid 1px #f4f4f4;
            "
            class="padding"
            @tap.stop="onCreatePoster(1)"
            >海报样式1</view
          >

          <view
            v-if="shareParams.posterConfig2"
            v-show="showSharePoster"
            style="
              width: 100%;
              font-size: 24rpx;
              background-color: #fff;
              border-bottom: solid 1px #f4f4f4;
            "
            class="padding"
            @tap.stop="onCreatePoster(2)"
            >海报样式2</view
          >

          <view
            style="
              background-color: #fff;
              border-bottom: solid 1px #f4f4f4;
              padding-top: 20rpx;
            "
            class="padding"
            @tap.stop="shareHide"
            >取消</view
          >
        </view>
      </view>
    </view>

    <view :class="'cu-modal ' + (posterShow ? 'show' : '')">
      <view class="cu-dialog show-bg">
        <view
          class="bg-white"
          :style="{ height: `calc(${showShareHeight}vh - 400rpx)` }"
        >
          <image :src="posterUrl" mode="aspectFit" class="image-box"></image>
        </view>
        <view class="cu-bar bg-white solid-top show-btn">
          <view class="action margin-0 flex-sub" @tap="hidePosterShow"
            >取消</view
          >
          <!-- #ifdef MP || APP-PLUS -->
          <view
            class="action margin-0 flex-sub solid-left text-red text-bold"
            @tap="savePoster"
            >保存到相册</view
          >
          <!-- #endif -->
          <!-- #ifdef H5 -->
          <view
            class="action margin-0 flex-sub solid-left text-red text-bold"
            @tap="hidePosterShow"
          >
            长按图片可保存或分享</view
          >
          <!-- #endif -->
        </view>
      </view>
    </view>
    <poster
      id="poster"
      ref="posterRef"
      :hide-loading="false"
      :preload="false"
      :config="posterConfig"
      @success="onPosterSuccess"
      @fail="onPosterFail"
    ></poster>
    <!-- #ifdef H5 || APP-PLUS -->
    <!-- 二维码组件，不显示，只用来生成二维码调用 说明文档 https://github.com/q310550690/uni-app-qrcode -->
    <!-- 该组件生成二维码时需要canvas元素装载,固直接引用组件没有使用js，效果一样 -->
    <view>
      <tki-qrcode
        ref="qrCodeRef"
        :size="260"
        :show="false"
        :val="curLocalUrl"
        @result="startCreatePoster"
        icon="/static/public/logo.png"
      ></tki-qrcode>
    </view>
    <!-- #endif -->
    <uni-popup ref="perpopup" type="center" :mask-click="false">
      <view class="permissions_box">
        当您使用APP时，保存海报到相册时候需要外部存储(含相册)写入权限，不授权上述权限，不影响APP其他功能使用。
      </view>
    </uni-popup>
  </view>
</template>

<script>
const app = getApp();
const { base64src } = require("utils/base64src.js");
import api from "utils/api";
import util from "@/utils/util";
import __config from "@/config/env";

import shareFriends from "@/components/share-friends/index.vue";
import tkiQrcode from "@/components/tki-qrcode/tki-qrcode.vue";
import poster from "@/components/wxa-plugin-canvas/poster/index";

export default {
  components: {
    shareFriends,
    tkiQrcode,
    poster,
  },
  props: {
    value: Boolean, // 是否显示弹框
    shareParams: {
      // 分享参数
      type: Object,
      default: () => {
        return {
          title: "", // 分享给微信好友链接时的标题
          desc: "", // 分享给微信好友链接时的描述
          imgUrl: "", // 分享给微信好友链接时的图片
          url: "", // 分享给微信好友链接时的url,  如果有传值则使用该值，否则用该页的当前路径

          scene: "", // 分享海报时的scene,一般为 id
          page: "", // 分享海报的page, 如果有传值则使用该值，否则用该页的当前路径
          posterConfig: null, // 分享海报的配置
        };
      },
    },
    showShareFriends: {
      //显示分享好友，默认true显示
      type: Boolean,
      default: true,
    },
    showSharePoster: {
      //显示分享海报，默认true显示
      type: Boolean,
      default: true,
    },
    showShareHeight: {
      //显示分享海报，默认true显示
      type: Number,
      default: 100,
    },
  },
  mounted() {
    console.log("----", this.shareParams);
  },
  watch: {
    value(val) {},
    shareParams(val) {
      console.log("----", val);
    },
    posterUrl(val) {},
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      shareShow: " show ",
      posterUrl: "",
      posterShow: false,
      posterConfig: null,
      curLocalUrl: null,
      showModal: false,
      isInitShare: false,
      shareObjTemp: {},
      isWeiXinBrowser: util.isWeiXinBrowser(),
    };
  },
  created() {
    this.initData();
  },
  onShow() {},
  methods: {
    initData() {
      // #ifdef APP-PLUS
      api.wxAppConfig(__config.wxAppId).then((res) => {
        if (res.data.data && res.data.data.isComponent == "1") {
          this.curLocalUrl = util.setAppPlusShareUrl(res.data.data);
        } else {
          this.curLocalUrl = util.setAppPlusShareUrl();
        }
        console.log("生成的二维码链接", this.curLocalUrl, res.data.data);
      });
      // #endif
    },
    shareHide() {
      this.$emit("input", false);
    },
    onPosterSuccess(e) {
      this.posterUrl = e;
      this.posterShow = true;
    },
    onPosterFail(err) {
      console.error(err);
    },
    hidePosterShow() {
      this.posterShow = false;
      this.$emit("input", false);
    },
    /**
     * 异步生成海报
     */
    onCreatePoster(type) {
      // #ifdef  APP-PLUS
      let that = this;
      var platform = uni.getSystemInfoSync().platform;
      if (platform == "android") {
        plus.android.checkPermission(
          "android.permission.WRITE_EXTERNAL_STORAGE",
          (granted) => {
            if (granted.checkResult == -1) {
              //弹出
              that.$refs.perpopup.open("top");
            } else {
              //执行你有权限后的方法
              that.dealCreatePoster(type);
            }
          },
          (error) => {
            console.error("Error checking permission:", error.message);
          },
        );
        plus.android.requestPermissions(
          ["android.permission.WRITE_EXTERNAL_STORAGE"],
          (e) => {
            //关闭
            that.$refs.perpopup.close();
            if (e.granted.length > 0) {
              //执行你有权限后的方法
              that.dealCreatePoster(type);
            }
          },
        );
      } else {
        //执行你有权限后的方法 ios
        that.dealCreatePoster(type);
      }
      // #endif

      // #ifdef MP
      this.dealCreatePoster(type);
      // #endif

      // #ifdef H5
      let url = "";
      if (this.shareParams.url) {
        url = this.shareParams.url;
      } else {
        url = util.setH5ShareUrl();
      }
      uni.showLoading({
        title: "海报生成中",
        mask: false,
      });
      this.$refs.qrCodeRef._makeCode(url); // H5需要先生成二维码后 才能生成海报
      // #endif
    },

    dealCreatePoster(type) {
      const userInfo = uni.getStorageSync("user_info");
      const userCode = userInfo ? "&" + userInfo.userCode : "";
      api
        .qrCodeUnlimited({
          theme: app.globalData.theme, // 全局颜色变量
          page: this.shareParams.page
            ? this.shareParams.page
            : util.getCurPage(getCurrentPages()), // 当前页面路径
          scene: this.shareParams.scene + userCode,
        })
        .then((res) => {
          console.log("res===>", res);
          base64src(res.data, (res2) => {
            console.log("二维码转化", res2);
            if (type == 1) this.startCreatePoster(res2);
            else this.startCreatePoster2(res2);
          });
        });
    },

    startCreatePoster(res) {
      // 开始 生成海报
      uni.hideLoading();
      // 需要注意：分享海报的图片方法会传入res对象,用 qrCodeName: 'qrCodeName'属性进行唯一标识
      if (!this.shareParams.posterConfig) {
        return;
      }
      let images = this.shareParams.posterConfig.images;
      // 将二维码图片的base64放入到海报绘制配置中
      images.forEach((val) => {
        if (val.qrCodeName) {
          val.url = res;
          return;
        }
      });
      this.posterShow = false;
      this.$refs.posterRef.onCreate(true, this.shareParams.posterConfig); // 入参：true为抹掉重新生成
    },
    startCreatePoster2(res) {
      // 开始 生成海报样式2
      uni.hideLoading();
      // 需要注意：分享海报的图片方法会传入res对象,用 qrCodeName: 'qrCodeName'属性进行唯一标识
      if (!this.shareParams.posterConfig2) {
        return;
      }
      let images = this.shareParams.posterConfig2.images;
      // 将二维码图片的base64放入到海报绘制配置中
      images.forEach((val) => {
        if (val.qrCodeName) {
          val.url = res;
          return;
        }
      });
      this.posterShow = false;
      this.$refs.posterRef.onCreate(true, this.shareParams.posterConfig2); // 入参：true为抹掉重新生成
    },
    //点击保存到相册
    savePoster: function () {
      var that = this;
      console.log("----this.posterUrl----", this.posterUrl);
      uni.saveImageToPhotosAlbum({
        filePath: this.posterUrl,
        success(res) {
          that.posterShow = false;
          that.$emit("input", false);
          uni.showModal({
            content: "图片已保存到相册，赶紧晒一下吧~",
            showCancel: false,
            confirmText: "好的",
            confirmColor: "#333",
            success: function (res) {
              if (res.confirm) {
                /* 该隐藏的隐藏 */
                that.$emit("input", false);
              }
            },
            fail: function (res) {
              console.log(res);
            },
          });
        },
      });
    },
  },
};
</script>
<style>
.show-bg {
  /* height: 84%; */
  margin-top: 120rpx;
}

.image-box {
  height: 90%;
}

.show-btn {
  margin-top: -130rpx;
}

.permissions_box {
  padding: 200rpx 30rpx 50rpx;
  background-color: #fff;
  color: #000000;
}
</style>
