
<template>
  <view :class="'text-' + size + ' text-red'">
    <text
      :class="(value > index ? 'cuIcon-likefill' : 'cuIcon-like') + ' margin-right'"
      v-for="(item, index) in 5"
      :key="index"
      @tap="redeHander(index)"
    >
    </text>
  </view>
</template>

<script>
export default {
  data () {
    return {};
  },
  watch: {
    value (val) { }
  },
  components: {},
  props: {
    value: {
      type: Number,
      default: 0
    },
    size: {
      type: String,
      default: 'xxl'
    }
  },
  methods: {
    redeHander (index) {
      let value = index + 1;
      this.$emit('onChange', value);
    }

  }
};
</script>
<style>
</style>
