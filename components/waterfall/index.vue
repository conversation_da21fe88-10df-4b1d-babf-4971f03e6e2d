<template>
	<view class="container" :style="{
			width: `${fullWidth}rpx`,
			boxSizing: 'border-box',
			backgroundColor: `${backgroundColor}`
		}">
		<view class="waterfall_left" :style="{
				paddingLeft: `${goodsSpace * multipleView + 4}rpx`,
				paddingRight: `${goodsSpace * multipleView}rpx`,
				boxSizing: 'border-box'
			}">
			<template v-for="(item, index) in listLeft">
				<view class="waterfall_item" :style="{
						borderTopLeftRadius: `${borderRadiusLeftTop * multipleView}rpx`,
						borderTopRightRadius: `${borderRadiusRightTop * multipleView}rpx`
					}" v-if="item.id > 0 && item.type !== 'swiper'" :key="item.id" @click="handleClick(item)">
					<view class="item_img" :class="
							((item &&
								item.estimatedPriceVo &&
								item.estimatedPriceVo.discountPrice &&
								item.estimatedPriceVo.discountPrice != '0.0' &&
								item.estimatedPriceVo.discountPrice != '0') ||
								(item &&
									item.estimatedPriceVo &&
									item.estimatedPriceVo.promotionsDiscountPrice &&
									item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
									item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
								(item && item.estimatedPriceVo && item.estimatedPriceVo.coupon && item.estimatedPriceVo.coupon != '0.0' && item.estimatedPriceVo.coupon != '0')) &&
							spuPriceStyle == 2
								? 'img-border'
								: ''
						" :style="{
							borderColor,
							position: 'relative'
						}">
						<!-- @load="considerPush" @error='considerPush' -->

						<image :src="item.picUrls[0] | formatImg360" mode="widthFix"
							:style="{ height: `${fullWidth / (isPhone ? 2 : 3) - 4}rpx` }"></image>

						<member-icon v-if="item.memberLevelLimit && item.memberLevelLimit != '101'"
							:memberLevelLimit="item.memberLevelLimit" />
						<view class="card-icon" v-if="setData" :style="{
								top: `${setData.imgTop * multipleView}rpx`,
								left: `${setData.imgLeft * multipleView}rpx`,
								zIndex: '0'
							}">
							<view class="card-icon">
								<image v-if="setData.leftShowMark == 1" style="width: 67rpx; height: 70rpx"
									mode="aspectFill" :src="setData.imageUrls" />
								<image v-if="setData.leftShowMark == 2 && item.Top" style="width: 67rpx; height: 70rpx"
									mode="aspectFill"
									:src="item.Top ? $imgUrl('waterfall/TOP/TOP${item.Top}.png') : ''" />
							</view>
						</view>

						<view class="right-card-icon" v-if="setData" :style="{
								top: `${setData.rightImgTop * multipleView}rpx`,
								right: `${setData.rightImgTop * multipleView}rpx`,
								zIndex: '0'
							}">
							<view class="right-card-icon">
								<image v-if="
										item.estimatedPriceVo &&
										item.estimatedPriceVo.totalDiscountPrice &&
										item.estimatedPriceVo.totalDiscountPrice != '0' &&
										item.estimatedPriceVo.totalDiscountPrice != '0.0' &&
										setData.rightImageUrls
									" style="width: 67rpx; height: 70rpx" mode="aspectFill" :src="setData.rightImageUrls" />

								<view v-if="
										item.estimatedPriceVo &&
										item.estimatedPriceVo.totalDiscountPrice &&
										item.estimatedPriceVo.totalDiscountPrice != '0' &&
										item.estimatedPriceVo.totalDiscountPrice != '0.0' &&
										setData.ShowPrice == 1
									" class="text-bold text-white text-sm" :style="{
										position: 'absolute',
										bottom: '-2rpx',
										right: '0rpx',
										width: '65rpx',
										height: '35rpx',
										lineHeight: '35rpx',
										textAlign: 'center',
										fontSize: `${setData.topPriceSize * multipleView}rpx`,
										color: `${setData.PriceColor}`
									}">
									{{ item.estimatedPriceVo.totalDiscountPrice }}
								</view>
							</view>
						</view>

						<view
							v-if="!setData || (setData && (!setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle == 1)))">
							<view style="position: absolute" :style="{
									top: spuPriceStyle == 1 ? 0 : 'auto',
									bottom: spuPriceStyle == 1 ? 0 : '-10rpx',
									left: spuPriceStyle == 1 ? 0 : '-6rpx',
									right: spuPriceStyle == 1 ? 0 : '-6rpx'
								}" v-if="
									(item &&
										item.estimatedPriceVo &&
										item.estimatedPriceVo.discountPrice &&
										item.estimatedPriceVo.discountPrice != '0.0' &&
										item.estimatedPriceVo.discountPrice != '0') ||
									(item.estimatedPriceVo &&item.estimatedPriceVo.promotionsDiscountPrice &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
									(item.estimatedPriceVo&&item.estimatedPriceVo.coupon && item.estimatedPriceVo.coupon != '0.0' && item.estimatedPriceVo.coupon != '0')
								">
								<good-price :goodsSpu="item" :small="!isPhone"
									:customCoverImage="customCoverImage"></good-price>
							</view>
						</view>
						<view class="sell-out" v-if="item.inventoryState == 1">
							<view class="sell-out-text">售罄</view>
						</view>
					</view>

					<view class="item_info">
						<!-- #ifdef APP-PLUS -->
						<view class="text-black padding-lr-sm overflow-2 text-xsm text-middle">{{ item.name }}</view>
						<!-- #endif -->
						<!-- #ifndef APP-PLUS -->
						<view class="text-black padding-lr-sm overflow-2 text-xsm">{{ item.name }}</view>
						<!-- #endif -->
						<view>
							<view v-if="setData && setData.bottomPriceStyle == 2 && item.surplus"
								style="position: relative; text-align: center">
								<image style="margin: auto; width: 304rpx; height: 69rpx" mode="aspectFill"
									:src="setData ? setData.bottomPriceStyleUrls : ''" />
								<view :style="{
										position: 'absolute',
										top: '24rpx',
										left: '40rpx',
										width: '100rpx',
										color: `${setData.retailPriceColor}`
									}">
									{{ item.estimatedPriceVo.originalPrice }}
								</view>
								<view :style="{
										position: 'absolute',
										top: '24rpx',
										left: '140rpx',
										width: '100rpx',
										color: `${setData.activityPriceColor}`
									}">
									{{ item.estimatedPriceVo.estimatedPrice }}
								</view>
							</view>

							<view v-if="
									!setData || !setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle !== 2) || item.spuType == 3 || item.spuType == 5
								">
								<view>
									<view class="text-sm text-gray padding-left-sm margin-tb-xs overflow-2 text-thin"
										style="color: #eb8c5c">
										{{ item.sellPoint || '' }}
									</view>

									<view v-if="item.articleExtension"
										class="text-sm text-gray padding-left-sm overflow-1" :style="{
											color: `${setData.entryColor}`,
											backgroundColor: `${setData.entryBgColor}`,
											margin: '0 10rpx'
										}">
										{{ item.articleExtension }}
									</view>

									<view class="flex align-center couponBox padding-lr-sm">
										<!-- 优惠券标签 -->
										<class-label
											v-if="item.estimatedPriceVo && item.estimatedPriceVo.apiCouponNameIdVO && item.estimatedPriceVo.apiCouponNameIdVO.name"
											:text="item.estimatedPriceVo.apiCouponNameIdVO.name" value="pinkBPink"
											:marginRight="10" :borderRadius="6"></class-label>
										<!-- 功效标签 -->
										<template
											v-if="item.goodsMarkInfoVo && item.goodsMarkInfoVo.spuParametersList && item.goodsMarkInfoVo.spuParametersList.length > 0">
											<class-label
												v-for="(spuParameters, ind) in item.goodsMarkInfoVo.spuParametersList"
												:key="ind" v-if="ind < 3" :text="spuParameters.optionValue"
												value="greenBGreen" :marginRight="10" :borderRadius="6"></class-label>
										</template>
									</view>
									<!-- 积分商品 -->
									<view v-if="item.spuType == 3 || item.spuType == 5"
										class="margin-top-xs margin-left-sm margin-bottom-sm">
										<text class="text-gray text-xs">积分：</text>
										<text
											class="text-red text-lg text-bold">{{ item.goodsPoints ? item.goodsPoints : 0 }}</text>
									</view>
									<!-- 付费商品 -->
									<view v-else>
										<view class="flex justify-between align-center padding-lr-sm"
											:class="isDistrubution ? '' : 'margin-bottom-sm'">
											<view class="flex align-baseline price-height" v-if="
													(item &&
														item.estimatedPriceVo &&
														item.estimatedPriceVo.discountPrice &&
														item.estimatedPriceVo.discountPrice != '0.0' &&
														item.estimatedPriceVo.discountPrice != '0') ||
													(item.estimatedPriceVo &&item.estimatedPriceVo.promotionsDiscountPrice &&
														item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
														item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
													(item.estimatedPriceVo&&item.estimatedPriceVo.coupon && item.estimatedPriceVo.coupon != '0.0' && item.estimatedPriceVo.coupon != '0')
												">
												<price-handle color="#ff2a71"
													:value="item.estimatedPriceVo.estimatedPrice" signFont="20rpx"
													bigFont="36rpx" smallFont="20rpx"></price-handle>
												<image class="juhui-tag" mode="aspectFill"
													src="https://img.songlei.com/waterfall/label/temai_logo.png">
												</image>
												<price-handle propClass="decoration-through" style="padding-left: 10rpx"
													fontWeight="nomal" color="#c1c0c6"
													:value="item.estimatedPriceVo.originalPrice" signFont="26rpx"
													bigFont="26rpx" smallFont="26rpx"></price-handle>
											</view>
											<view v-else class="text-bold text-xs"
												style="color: #ff2a71; font-size: 40rpx">
												<price-handle color="#ff2a71"
													:value="item.estimatedPriceVo.originalPrice" signFont="20rpx"
													bigFont="36rpx" smallFont="20rpx"></price-handle>
											</view>
										</view>
										<view class="padding-lr-sm" style="font-size: 24rpx; color: #999"
											v-if="isDistrubution && item.skus && item.skus.length > 0 && (item.skus[0].rebateType == '0' || item.skus[0].rebateType == '1')">
											<text style="background: #efeeee; padding: 2rpx 0"
												v-for="(specsItem, index) in item.skus[0].specs" :key="index" :style="
													item.skus[0].specs.length > 1
														? index == 0
															? 'border-radius: 20rpx 0 0 20rpx; padding-left: 20rpx;'
															: index == item.skus[0].specs.length - 1
															? 'border-radius: 0 20rpx 20rpx 0; padding-right: 20rpx;'
															: 'padding: 2rpx 0;'
														: 'border-radius: 20rpx; padding: 2rpx 20rpx;'
												">
												<text v-if="index == 0 && specsItem.specValueName">规格：</text>
												{{ specsItem.specValueName }}
												<text>{{ index < item.skus[0].specs.length - 1 ? '、' : '' }}</text>
											</text>
										</view>
										<view v-if="isDistrubution" class="flex align-center padding-lr-sm"
											:class="isDistrubution ? 'margin-bottom-sm' : ''">
											<view style="
													line-height: 40rpx;
													height: 4;
													background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%);
													border-radius: 30rpx;
													color: white;
													padding: 0 12rpx;
												">
												<text style="font-size: 22rpx">分享最高可赚</text>
												<text style="font-size: 22rpx; padding-left: 10rpx">￥</text>
												<text
													style="font-size: 28rpx">{{ item.rebateAmount ? item.rebateAmount : '0.00' }}</text>
											</view>
										</view>
									</view>
									<!-- <view v-if="item.inventoryState==1" class="padding-lr-sm text-balck text-xs margin-bottom-sm"
											style="font-weight: 500;font-size: 28rpx;">已售罄
										</view> -->
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="waterfall_item" :style="{
						borderTopLeftRadius: `${borderRadiusLeftTop * 2}rpx`,
						borderTopRightRadius: `${borderRadiusRightTop * 2}rpx`
					}" style="border-radius: 0; background-color: transparent"
					v-if="item.type === 'swiper' && item.goodsList && item.goodsList.length">
					<swiper :indicator-dots="true" :autoplay="true" :circular="true" :interval="item.interval || 1500"
						:style="{ height: isPhone ? (item.height || 150) * 2 + 'rpx' : (item.height || 150) * 1.35 + 'rpx' }">
						<swiper-item v-for="(items, indexs) in item.goodsList" @click="handleClicks(items.pageUrl)"
							:key="items.id">
							<image :src="items.imageUrl" style="width: 100%; height: 100%"
								:style="{ borderRadius: item.borderRadius === '1' ? '15px' : '0' }" mode="aspectFill">
							</image>
						</swiper-item>
					</swiper>
				</view>
			</template>
		</view>

		<view class="waterfall_right" :style="{
				paddingRight: isPhone ? `${goodsSpace * multipleView + 4}rpx` : 0,
				paddingLeft: `${goodsSpace * multipleView}rpx`,
				boxSizing: 'border-box'
			}">
			<template v-for="(item, index) in listRight">
				<view class="waterfall_item" :style="{
						borderTopLeftRadius: `${borderRadiusLeftTop * multipleView}rpx`,
						borderTopRightRadius: `${borderRadiusRightTop * multipleView}rpx`
					}" v-if="item.id > 0" :key="item.id" @click="handleClick(item)">
					<view class="item_img" :class="
							((item &&
								item.estimatedPriceVo &&
								item.estimatedPriceVo.discountPrice &&
								item.estimatedPriceVo.discountPrice != '0.0' &&
								item.estimatedPriceVo.discountPrice != '0') ||
								(item &&
									item.estimatedPriceVo &&
									item.estimatedPriceVo.promotionsDiscountPrice &&
									item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
									item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
								(item && item.estimatedPriceVo && item.estimatedPriceVo.coupon && item.estimatedPriceVo.coupon != '0.0' && item.estimatedPriceVo.coupon != '0')) &&
							spuPriceStyle == 2
								? 'img-border'
								: ''
						" :style="{
							borderColor
						}">
						<!-- @load="considerPush" @error='considerPush' -->

						<image :src="item.picUrls[0] | formatImg360"
							:style="{ height: `${fullWidth / (isPhone ? 2 : 3) - 4}rpx` }" mode="widthFix"
							@load="handleLoaded(index)" @error="handleLoaded(index)"></image>

						<member-icon v-if="item.memberLevelLimit && item.memberLevelLimit != '101'"
							:memberLevelLimit="item.memberLevelLimit" />

						<view class="card-icon" v-if="setData" :style="{
								top: `${setData.imgTop * multipleView}rpx`,
								left: `${setData.imgLeft * multipleView}rpx`,
								zIndex: '0'
							}">
							<view class="card-icon">
								<image v-if="setData.leftShowMark == 1" style="width: 67rpx; height: 70rpx"
									mode="aspectFill" :src="setData.imageUrls" />

								<image v-if="setData.leftShowMark == 2 && item.Top" style="width: 67rpx; height: 70rpx"
									mode="aspectFill"
									:src="item.Top ? $imgUrl('waterfall/TOP/TOP${item.Top}.png') : ''" />
							</view>
						</view>

						<view class="right-card-icon" v-if="setData" :style="{
								top: `${setData.rightImgTop * multipleView}rpx`,
								right: `${setData.rightImgTop * multipleView}rpx`,
								zIndex: '0'
							}">
							<view class="right-card-icon">
								<image v-if="
										item.estimatedPriceVo &&
										item.estimatedPriceVo.totalDiscountPrice &&
										item.estimatedPriceVo.totalDiscountPrice != '0' &&
										item.estimatedPriceVo.totalDiscountPrice != '0.0' &&
										setData.rightImageUrls
									" style="width: 67rpx; height: 70rpx" mode="aspectFill" :src="setData.rightImageUrls" />

								<view v-if="
										item.estimatedPriceVo &&
										item.estimatedPriceVo.totalDiscountPrice &&
										item.estimatedPriceVo.totalDiscountPrice != '0' &&
										item.estimatedPriceVo.totalDiscountPrice != '0.0' &&
										setData.ShowPrice == 1
									" class="text-bold text-white text-sm" :style="{
										position: 'absolute',
										bottom: '-2rpx',
										right: '0rpx',
										width: '65rpx',
										height: '35rpx',
										lineHeight: '35rpx',
										textAlign: 'center',
										fontSize: `${setData.topPriceSize * 2}rpx`,
										color: `${setData.PriceColor}`
									}">
									{{ item.estimatedPriceVo.totalDiscountPrice }}
								</view>
							</view>
						</view>

						<view
							v-if="!setData || (setData && (!setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle == 1)))">
							<view style="position: absolute" :style="{
									top: spuPriceStyle == 1 ? 0 : 'auto',
									bottom: spuPriceStyle == 1 ? 0 : '-10rpx',
									left: spuPriceStyle == 1 ? 0 : '-6rpx',
									right: spuPriceStyle == 1 ? 0 : '-6rpx'
								}" v-if="
									(item &&
										item.estimatedPriceVo &&
										item.estimatedPriceVo.discountPrice &&
										item.estimatedPriceVo.discountPrice != '0.0' &&
										item.estimatedPriceVo.discountPrice != '0') ||
									(item.estimatedPriceVo &&
										item.estimatedPriceVo.promotionsDiscountPrice &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
									(item.estimatedPriceVo && item.estimatedPriceVo.coupon && item.estimatedPriceVo.coupon != '0.0' && item.estimatedPriceVo.coupon != '0')
								">
								<good-price :small="!isPhone" :goodsSpu="item"
									:customCoverImage="customCoverImage"></good-price>
							</view>
						</view>
						<view class="sell-out" v-if="item.inventoryState == 1">
							<view class="sell-out-text">售罄</view>
						</view>
					</view>

					<view class="item_info">
						<!-- #ifdef APP-PLUS -->
						<view class="text-black padding-lr-sm overflow-2 text-xsm text-middle">{{ item.name || '' }}
						</view>
						<!-- #endif -->
						<!-- #ifndef APP-PLUS -->
						<view class="text-black padding-lr-sm overflow-2 text-xsm">{{ item.name || ''}}</view>
						<!-- #endif -->
						<view>
							<view
								v-if="setData && setData.bottomPriceStyle && setData.bottomPriceStyle !== 1 && item.surplus"
								style="position: relative; text-align: center">
								<image style="margin: auto; width: 304rpx; height: 69rpx" mode="aspectFill"
									:src="setData ? setData.bottomPriceStyleUrls : ''" />
								<view :style="{
										position: 'absolute',
										top: '24rpx',
										left: '40rpx',
										width: '100rpx',
										color: `${setData && setData.retailPriceColor}`
									}">
									{{ item.estimatedPriceVo.originalPrice }}
								</view>
								<view :style="{
										position: 'absolute',
										top: '24rpx',
										left: '140rpx',
										width: '100rpx',
										color: `${setData.activityPriceColor}`
									}">
									{{ item.estimatedPriceVo.estimatedPrice }}
								</view>
							</view>

							<view v-if="
									!setData || !setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle !== 2) || item.spuType == 3 || item.spuType == 5
								">
								<view class="text-sm text-gray padding-left-sm margin-tb-xs overflow-2 text-thin"
									style="color: #eb8c5c">
									{{ item.sellPoint || ''}}
								</view>

								<view v-if="item.articleExtension" class="text-sm text-gray padding-left-sm overflow-1"
									:style="{
										color: `${setData.entryColor}`,
										backgroundColor: `${setData.entryBgColor}`,
										margin: '0 10rpx'
									}">
									{{ item.articleExtension }}
								</view>

								<view class="flex align-center couponBox padding-lr-sm">
									<!-- 优惠券标签 -->
									<class-label
										v-if="item.estimatedPriceVo && item.estimatedPriceVo.apiCouponNameIdVO && item.estimatedPriceVo.apiCouponNameIdVO.name"
										:text="item.estimatedPriceVo.apiCouponNameIdVO.name" value="pinkBPink"
										:marginRight="10" :borderRadius="6"></class-label>
									<!-- 功效标签 -->
									<template
										v-if="item.goodsMarkInfoVo && item.goodsMarkInfoVo.spuParametersList && item.goodsMarkInfoVo.spuParametersList.length > 0">
										<class-label
											v-for="(spuParameters, ind) in item.goodsMarkInfoVo.spuParametersList"
											:key="ind" v-if="ind < 3" :text="spuParameters.optionValue"
											value="greenBGreen" :marginRight="10" :borderRadius="6"></class-label>
									</template>
								</view>
								<!-- 积分商品 -->
								<view v-if="item.spuType == 3 || item.spuType == 5"
									class="margin-top-xs margin-left-sm margin-bottom-sm">
									<text class="text-gray text-xs">积分：</text>
									<text
										class="text-red text-lg text-bold">{{ item.goodsPoints ? item.goodsPoints : 0 }}</text>
								</view>
								<!-- 付费商品 -->
								<view v-else>
									<view class="flex justify-between align-center padding-lr-sm"
										:class="isDistrubution ? '' : 'margin-bottom-sm'">
										<view class="flex align-baseline price-height" v-if="
												(item &&
													item.estimatedPriceVo &&
													item.estimatedPriceVo.discountPrice &&
													item.estimatedPriceVo.discountPrice != '0.0' &&
													item.estimatedPriceVo.discountPrice != '0') ||
												(item.estimatedPriceVo &&
													item.estimatedPriceVo.promotionsDiscountPrice &&
													item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
													item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
												(item.estimatedPriceVo &&
													item.estimatedPriceVo.coupon &&
													item.estimatedPriceVo.coupon != '0.0' &&
													item.estimatedPriceVo.coupon != '0')
											">
											<price-handle color="#ff2a71" :value="item.estimatedPriceVo.estimatedPrice"
												signFont="20rpx" bigFont="36rpx" smallFont="20rpx"></price-handle>
											<image class="juhui-tag" mode="aspectFill"
												src="https://img.songlei.com/waterfall/label/temai_logo.png"></image>
											<price-handle propClass="decoration-through" style="padding-left: 10rpx"
												fontWeight="normal" color="#c1c0c6"
												:value="item.estimatedPriceVo.originalPrice" signFont="26rpx"
												bigFont="26rpx" smallFont="26rpx"></price-handle>
										</view>
										<view v-else-if="item.estimatedPriceVo" class="text-bold text-xs"
											style="color: #ff2a71; font-size: 40rpx">
											<price-handle color="#ff2a71" :value="item.estimatedPriceVo.originalPrice"
												signFont="20rpx" bigFont="36rpx" smallFont="20rpx"></price-handle>
										</view>
									</view>
									<view class="padding-lr-sm" style="font-size: 24rpx; color: #999"
										v-if="isDistrubution && item.skus && item.skus.length > 0 && (item.skus[0].rebateType == '0' || item.skus[0].rebateType == '1')">
										<text style="background: #efeeee; padding: 2rpx 0"
											v-for="(specsItem, index) in item.skus[0].specs" :key="index" :style="
												item.skus[0].specs.length > 1
													? index == 0
														? 'border-radius: 20rpx 0 0 20rpx; padding-left: 20rpx;'
														: index == item.skus[0].specs.length - 1
														? 'border-radius: 0 20rpx 20rpx 0; padding-right: 20rpx;'
														: 'padding: 2rpx 0;'
													: 'border-radius: 20rpx; padding: 2rpx 20rpx;'
											">
											<text v-if="index == 0 && specsItem.specValueName">规格：</text>
											{{ specsItem.specValueName }}
											<text>{{ index < item.skus[0].specs.length - 1 ? '、' : '' }}</text>
										</text>
									</view>
									<view v-if="isDistrubution" class="flex align-center padding-lr-sm"
										:class="isDistrubution ? 'margin-bottom-sm' : ''">
										<view style="
												line-height: 40rpx;
												height: 4;
												background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%);
												border-radius: 30rpx;
												color: white;
												padding: 0 12rpx;
											">
											<text style="font-size: 22rpx">分享最高可赚</text>
											<text style="font-size: 22rpx; padding-left: 10rpx">￥</text>
											<text
												style="font-size: 28rpx">{{ item.rebateAmount ? item.rebateAmount : '0.00' }}</text>
										</view>
									</view>
								</view>
								<!-- <view v-if="item.inventoryState==1" class="padding-lr-sm text-balck text-xs margin-bottom-sm"
                      style="font-weight: 500;font-size: 28rpx;">已售罄
                    </view> -->
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>

		<view v-if="!isPhone" class="waterfall_right-pad" :style="{
				paddingRight: `${goodsSpace * 2 + 4}rpx`,
				paddingLeft: `${goodsSpace * 2}rpx`,
				boxSizing: 'border-box'
			}">
			<template v-for="(item, index) in listRightPad">
				<view class="waterfall_item" :style="{
						borderTopLeftRadius: `${borderRadiusLeftTop * 2}rpx`,
						borderTopRightRadius: `${borderRadiusRightTop * 2}rpx`
					}" v-if="item.id > 0" :key="item.id" @click="handleClick(item)">
					<view class="item_img" :class="
							((item &&
								item.estimatedPriceVo &&
								item.estimatedPriceVo.discountPrice &&
								item.estimatedPriceVo.discountPrice != '0.0' &&
								item.estimatedPriceVo.discountPrice != '0') ||
								(item &&
									item.estimatedPriceVo &&
									item.estimatedPriceVo.promotionsDiscountPrice &&
									item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
									item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
								(item && item.estimatedPriceVo && item.estimatedPriceVo.coupon && item.estimatedPriceVo.coupon != '0.0' && item.estimatedPriceVo.coupon != '0')) &&
							spuPriceStyle == 2
								? 'img-border'
								: ''
						" :style="{
							borderColor
						}">
						<!-- @load="considerPush" @error='considerPush' -->

						<image :src="item.picUrls[0] | formatImg360"
							:style="{ height: `${fullWidth / (isPhone ? 2 : 3) - 4}rpx` }" mode="widthFix"
							@load="handleLoaded(index)" @error="handleLoaded(index)"></image>

						<member-icon v-if="item.memberLevelLimit && item.memberLevelLimit != '101'"
							:memberLevelLimit="item.memberLevelLimit" />

						<view class="card-icon" v-if="setData" :style="{
								top: `${setData.imgTop * 2}rpx`,
								left: `${setData.imgLeft * 2}rpx`,
								zIndex: '0'
							}">
							<view class="card-icon">
								<image v-if="setData.leftShowMark == 1" style="width: 67rpx; height: 70rpx"
									mode="aspectFill" :src="setData.imageUrls" />

								<image v-if="setData.leftShowMark == 2 && item.Top" style="width: 67rpx; height: 70rpx"
									mode="aspectFill"
									:src="item.Top ? $imgUrl('waterfall/TOP/TOP${item.Top}.png') : ''" />
							</view>
						</view>

						<view class="right-card-icon" v-if="setData" :style="{
								top: `${setData.rightImgTop * 2}rpx`,
								right: `${setData.rightImgTop * 2}rpx`,
								zIndex: '0'
							}">
							<view class="right-card-icon">
								<image v-if="
										item.estimatedPriceVo &&
										item.estimatedPriceVo.totalDiscountPrice &&
										item.estimatedPriceVo.totalDiscountPrice != '0' &&
										item.estimatedPriceVo.totalDiscountPrice != '0.0' &&
										setData.rightImageUrls
									" style="width: 67rpx; height: 70rpx" mode="aspectFill" :src="setData.rightImageUrls" />

								<view v-if="
										item.estimatedPriceVo &&
										item.estimatedPriceVo.totalDiscountPrice &&
										item.estimatedPriceVo.totalDiscountPrice != '0' &&
										item.estimatedPriceVo.totalDiscountPrice != '0.0' &&
										setData.ShowPrice == 1
									" class="text-bold text-white text-sm" :style="{
										position: 'absolute',
										bottom: '-2rpx',
										right: '0rpx',
										width: '65rpx',
										height: '35rpx',
										lineHeight: '35rpx',
										textAlign: 'center',
										fontSize: `${setData.topPriceSize * 2}rpx`,
										color: `${setData.PriceColor}`
									}">
									{{ item.estimatedPriceVo.totalDiscountPrice }}
								</view>
							</view>
						</view>

						<view
							v-if="!setData || (setData && (!setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle == 1)))">
							<view style="position: absolute" :style="{
									top: spuPriceStyle == 1 ? 0 : 'auto',
									bottom: spuPriceStyle == 1 ? 0 : '-10rpx',
									left: spuPriceStyle == 1 ? 0 : '-6rpx',
									right: spuPriceStyle == 1 ? 0 : '-6rpx'
								}" v-if="
									(item &&
										item.estimatedPriceVo &&
										item.estimatedPriceVo.discountPrice &&
										item.estimatedPriceVo.discountPrice != '0.0' &&
										item.estimatedPriceVo.discountPrice != '0') ||
									(item.estimatedPriceVo &&
										item.estimatedPriceVo.promotionsDiscountPrice &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
									(item.estimatedPriceVo && item.estimatedPriceVo.coupon && item.estimatedPriceVo.coupon != '0.0' && item.estimatedPriceVo.coupon != '0')
								">
								<good-price :small="!isPhone" :goodsSpu="item"
									:customCoverImage="customCoverImage"></good-price>
							</view>
						</view>
						<view class="sell-out" v-if="item.inventoryState == 1">
							<view class="sell-out-text">售罄</view>
						</view>
					</view>

					<view class="item_info">
						<!-- #ifdef APP-PLUS -->
						<view class="text-black padding-lr-sm overflow-2 text-xsm text-middle">{{ item.name }}</view>
						<!-- #endif -->
						<!-- #ifndef APP-PLUS -->
						<view class="text-black padding-lr-sm overflow-2 text-xsm">{{ item.name }}</view>
						<!-- #endif -->
						<view>
							<view
								v-if="setData && setData.bottomPriceStyle && setData.bottomPriceStyle !== 1 && item.surplus"
								style="position: relative; text-align: center">
								<image style="margin: auto; width: 304rpx; height: 69rpx" mode="aspectFill"
									:src="setData ? setData.bottomPriceStyleUrls : ''" />
								<view :style="{
										position: 'absolute',
										top: '24rpx',
										left: '40rpx',
										width: '100rpx',
										color: `${setData && setData.retailPriceColor}`
									}">
									{{ item.estimatedPriceVo.originalPrice }}
								</view>
								<view :style="{
										position: 'absolute',
										top: '24rpx',
										left: '140rpx',
										width: '100rpx',
										color: `${setData.activityPriceColor}`
									}">
									{{ item.estimatedPriceVo.estimatedPrice }}
								</view>
							</view>

							<view v-if="
									!setData || !setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle !== 2) || item.spuType == 3 || item.spuType == 5
								">
								<view class="text-sm text-gray padding-left-sm margin-tb-xs overflow-2 text-thin"
									style="color: #eb8c5c">
									{{ item.sellPoint || ''}}
								</view>

								<view v-if="item.articleExtension" class="text-sm text-gray padding-left-sm overflow-1"
									:style="{
										color: `${setData.entryColor}`,
										backgroundColor: `${setData.entryBgColor}`,
										margin: '0 10rpx'
									}">
									{{ item.articleExtension }}
								</view>

								<view class="flex align-center couponBox padding-lr-sm">
									<!-- 优惠券标签 -->
									<class-label
										v-if="item.estimatedPriceVo && item.estimatedPriceVo.apiCouponNameIdVO && item.estimatedPriceVo.apiCouponNameIdVO.name"
										:text="item.estimatedPriceVo.apiCouponNameIdVO.name" value="pinkBPink"
										:marginRight="10" :borderRadius="6"></class-label>
									<!-- 功效标签 -->
									<template
										v-if="item.goodsMarkInfoVo && item.goodsMarkInfoVo.spuParametersList && item.goodsMarkInfoVo.spuParametersList.length > 0">
										<class-label
											v-for="(spuParameters, ind) in item.goodsMarkInfoVo.spuParametersList"
											:key="ind" v-if="ind < 3" :text="spuParameters.optionValue"
											value="greenBGreen" :marginRight="10" :borderRadius="6"></class-label>
									</template>
								</view>
								<!-- 积分商品 -->
								<view v-if="item.spuType == 3 || item.spuType == 5"
									class="margin-top-xs margin-left-sm margin-bottom-sm">
									<text class="text-gray text-xs">积分：</text>
									<text
										class="text-red text-lg text-bold">{{ item.goodsPoints ? item.goodsPoints : 0 }}</text>
								</view>
								<!-- 付费商品 -->
								<view v-else>
									<view class="flex justify-between align-center padding-lr-sm"
										:class="isDistrubution ? '' : 'margin-bottom-sm'">
										<view class="flex align-baseline price-height" v-if="
												(item &&
													item.estimatedPriceVo &&
													item.estimatedPriceVo.discountPrice &&
													item.estimatedPriceVo.discountPrice != '0.0' &&
													item.estimatedPriceVo.discountPrice != '0') ||
												(item.estimatedPriceVo &&
													item.estimatedPriceVo.promotionsDiscountPrice &&
													item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
													item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
												(item.estimatedPriceVo &&
													item.estimatedPriceVo.coupon &&
													item.estimatedPriceVo.coupon != '0.0' &&
													item.estimatedPriceVo.coupon != '0')
											">
											<price-handle color="#ff2a71" :value="item.estimatedPriceVo.estimatedPrice"
												signFont="20rpx" bigFont="36rpx" smallFont="20rpx"></price-handle>
											<image class="juhui-tag" mode="aspectFill"
												src="https://img.songlei.com/waterfall/label/temai_logo.png"></image>
											<price-handle propClass="decoration-through" style="padding-left: 10rpx"
												fontWeight="normal" color="#c1c0c6"
												:value="item.estimatedPriceVo.originalPrice" signFont="26rpx"
												bigFont="26rpx" smallFont="26rpx"></price-handle>
										</view>
										<view v-else-if="item.estimatedPriceVo" class="text-bold text-xs"
											style="color: #ff2a71; font-size: 40rpx">
											<price-handle color="#ff2a71" :value="item.estimatedPriceVo.originalPrice"
												signFont="20rpx" bigFont="36rpx" smallFont="20rpx"></price-handle>
										</view>
									</view>
									<view class="padding-lr-sm" style="font-size: 24rpx; color: #999"
										v-if="isDistrubution && item.skus && item.skus.length > 0 && (item.skus[0].rebateType == '0' || item.skus[0].rebateType == '1')">
										<text style="background: #efeeee; padding: 2rpx 0"
											v-for="(specsItem, index) in item.skus[0].specs" :key="index" :style="
												item.skus[0].specs.length > 1
													? index == 0
														? 'border-radius: 20rpx 0 0 20rpx; padding-left: 20rpx;'
														: index == item.skus[0].specs.length - 1
														? 'border-radius: 0 20rpx 20rpx 0; padding-right: 20rpx;'
														: 'padding: 2rpx 0;'
													: 'border-radius: 20rpx; padding: 2rpx 20rpx;'
											">
											<text v-if="index == 0 && specsItem.specValueName">规格：</text>
											{{ specsItem.specValueName }}
											<text>{{ index < item.skus[0].specs.length - 1 ? '、' : '' }}</text>
										</text>
									</view>
									<view v-if="isDistrubution" class="flex align-center padding-lr-sm"
										:class="isDistrubution ? 'margin-bottom-sm' : ''">
										<view style="
												line-height: 40rpx;
												height: 4;
												background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%);
												border-radius: 30rpx;
												color: white;
												padding: 0 12rpx;
											">
											<text style="font-size: 22rpx">分享最高可赚</text>
											<text style="font-size: 22rpx; padding-left: 10rpx">￥</text>
											<text
												style="font-size: 28rpx">{{ item.rebateAmount ? item.rebateAmount : '0.00' }}</text>
										</view>
									</view>
								</view>
								<!-- <view v-if="item.inventoryState==1" class="padding-lr-sm text-balck text-xs margin-bottom-sm"
		              style="font-weight: 500;font-size: 28rpx;">已售罄
		            </view> -->
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>
	</view>
</template>
<script>
	import goodPrice from 'components/good-price/good-price.vue';
	import memberIcon from '@/components/member-icon/index.vue';
	import utils from '@/utils/util.js';
	import {
		mapState
	} from 'vuex';

	const app = getApp();
	export default {
		name: 'waterfall',
		components: {
			goodPrice,
			memberIcon
		},
		props: {
			// 需要在使用页面 onPageScroll 传进来
			list: {
				type: Array,
				required: true,
				default: []
			},
			goodsSpace: {
				type: String | Number,
				default: 3
			},
			fullWidth: {
				type: Number,
				default: 750
			},
			loadMore: {
				type: Boolean,
				default: false
			},
			setData: {
				type: Object,
				default: () => {}
			},
			backgroundColor: {
				type: String,
				default: '#f1f1f1'
			},
			isDistrubution: {
				type: Boolean,
				default: false
			},
			customCoverImage: {
				type: String | Number,
				default: ''
			},

			borderRadiusLeftTop: {
				type: String | Number,
				default: 12
			},

			borderRadiusRightTop: {
				type: String | Number,
				default: 12
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				listLeft: [],
				listRight: [],
				listRightPad: [],
				newList: [],
				isIOS: utils.isIOS()
			};
		},
		computed: {
			...mapState(['isPhone', 'windowWidth', 'multipleView']),

			borderColor: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.spuPriceStyle == 1 ? '' : customFiled.spuPicBorder;
				}
				return '#d9bda5';
			},
			spuPriceColor: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					if (customFiled.spuPriceStyle == 1) {
						return customFiled.spuPriceColor1 || '#fff';
					}
					return customFiled.spuPriceColor2 || '#fff';
				}
				return '#fff';
			},

			spuPriceStyle: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.spuPriceStyle || 2;
				}
				return 2;
			}
		},

		watch: {
			list: {
				handler(newValue, oldValue) {
					this.listLeft = [];
					this.listRight = [];
					this.listRightPad = [];
					this.considerPush();
				},
				deep: true
			}
		},

		mounted() {
			this.init();
		},

		methods: {
			// 触发重新排列
			init() {
				this.listLeft = [];
				this.listRight = [];
				this.listRightPad = [];
				this.considerPush();
			},
			// 清空数据列表
			clear() {
				this.listLeft = [];
				this.listRight = [];
				this.listRightPad = [];
			},

			// 计算排列
			considerPush() {
				if (!this.list || this.list.length == 0) return;
				let isSwiper = false;
				this.list.forEach((item, index) => {
					// 计算立减价格，记录商品顺序
					if (item.type !== 'swiper') {
						let surplus = '';
						if (item.estimatedPriceVo) {
							if (item.estimatedPriceVo.originalPrice != item.estimatedPriceVo.estimatedPrice) {
								let price = item.estimatedPriceVo.originalPrice - item.estimatedPriceVo
									.estimatedPrice;
								surplus = price;
								this.$set(this.list[index], 'surplus', surplus);
							}
						}

						this.$set(this.list[index], 'Top', isSwiper ? (index < 11 ? index : '') : index + 1 < 11 ?
							index + 1 : '');
					} else {
						isSwiper = true;
						item.id = Math.random();
					}

					if (!this.isPhone) {
						if (index % 3 == 0) {
							this.listLeft.push(item);
						} else if ((index + 1) % 2 == 0) {
							this.listRight.push(item);
						} else {
							this.listRightPad.push(item);
						}
					} else {
						if (index % 2 == 0) {
							this.listLeft.push(item);
						} else {
							this.listRight.push(item);
						}
					}
				});
			},
			handleLoaded(index) {
				// 最后一个左侧可能比右侧多出来一个半，要平衡下
				if (this.listRight.length > 1 && !this.loadMore && index == this.listRight.length - 1) {
					let leftH = 0,
						rightH = 0; //左右高度
					var query = uni.createSelectorQuery().in(this);
					if (this.isPhone) {
						query.selectAll('.waterfall_left').boundingClientRect();
						query.selectAll('.waterfall_right').boundingClientRect();
						query.exec((res) => {
							leftH = res[0].length != 0 ? res[0][0].height : 0; //防止查询不到做个处理
							rightH = res[1].length != 0 ? res[1][0].height : 0;
							// console.log('结果',leftH,rightH)
							if (leftH > rightH + 303) {
								// 左侧高度比右侧高出来一个多的话做处理
								this.listRight.push(this.listLeft.pop());
							}
						});
					}
					// else {
					// 	query.selectAll('.waterfall_left').boundingClientRect();
					// 	query.selectAll('.waterfall_right').boundingClientRect();
					// 	query.selectAll('.waterfall_right-pad').boundingClientRect();
					// 	query.exec((res) => {
					// 		leftH = res[0].length != 0 ? res[0][0].height : 0; //防止查询不到做个处理
					// 		rightH = res[1].length != 0 ? res[1][0].height : 0;
					// 		// console.log('结果',leftH,rightH)
					// 		if (leftH > rightH + 303) {
					// 			// 左侧高度比右侧高出来一个多的话做处理
					// 			this.listRight.push(this.listLeft.pop());
					// 		}
					// 	});
					// }
				}
			},
			handleClick(item) {
				this.$emit('click', item);
			},
			handleClicks(url) {
				if (url && url.indexOf('/pages/goods/goods-detail/index') === 0) {
					const sourceModule = encodeURIComponent('热区组件');
					if (url.indexOf('?') > -1) {
						url += `&source_module=${sourceModule}`;
					} else {
						url += `?source_module=${sourceModule}`;
					}
				}
				uni.navigateTo({
					url
				});
				uni.switchTab({
					url
				});
			}
		}
	};
</script>
<style lang="scss" scoped>
	.card-icon {
		width: 67rpx;
		height: 70rpx;
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		z-index: 0;
	}

	.right-card-icon {
		width: 67rpx;
		height: 70rpx;
		position: absolute;
		top: 0rpx;
		right: 0rpx;
		z-index: 0;
	}

	.container {
		display: flex;
		flex-flow: row nowrap;
		justify-content: space-around;
		align-items: flex-start;
	}

	.waterfall_left,
	.waterfall_right {
		width: 50%;
	}

	@media screen and (min-width: 549px) {

		.waterfall_left,
		.waterfall_right,
		.waterfall_right-pad {
			width: 247rpx;
		}
	}

	.waterfall_item {
		width: 100%;
		margin: 10rpx 0 20rpx 0;
		background-color: #ffffff;
		overflow: hidden;

		.item_img {
			width: 100%;
			position: relative;

			image {
				width: 100%;
				overflow: hidden;
			}
		}
	}

	.item_info {
		margin-top: 10rpx;
	}

	/* 优惠券部分 */
	.couponBox {
		line-height: 26rpx;
	}

	.img-border {
		border-left-width: 6rpx;
		border-right-width: 6rpx;
		border-top-width: 6rpx;
		border-style: solid;
		border-color: transparent;
	}

	.juhui-tag {
		width: 64rpx;
		height: 25rpx;
		margin-left: 4rpx;
	}

	.price-height {
		height: 46rpx;
	}

	@media screen and (min-width: 549px) {
		.juhui-tag {
			width: 38rpx;
			height: 15rpx;
			margin-left: 4rpx;
		}

		.waterfall_item {
			margin: 10rpx 0 10rpx 0;
		}

		.price-height {
			height: 27rpx;
		}
	}
</style>