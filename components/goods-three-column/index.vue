<template>
  <view class="goods-container flex">
    <view
      style="width: 33%;"
      v-for="(item, index) in goodsList"
      :key="index"
    >
      <view style="width: 100%;">
        <view :style="{
				  padding: `${goodsSpace*2}rpx`,
				}">
          <view class="goods-box">
            <navigator
              hover-class="none"
              style="background-color: #FFFFFF;"
              :url="'/pages/goods/goods-detail/index?id=' + item.id+ '&source_module=三列组件'"
            >
              <view class="img-box">
                <!-- <image :src="item.picUrls[0] ? item.picUrls[0] : $imgUrl('live/img/no_pic.png')">
								</image> -->
                <lazy-load
                  :image="item.picUrls[0] |  formatImg360"
                  mode="aspectFill"
                  imgHeight="200"
                >
                </lazy-load>
              </view>
              <view class="text-black margin-top-sm padding-lr-sm overflow-1">{{item.name}}</view>
              <view class="margin-top-xs text-sm text-gray padding-left-sm overflow-1">{{item.sellPoint}}
              </view>
			  <!-- 积分商品 -->
			  <view v-if="item.spuType == 3 || item.spuType == 5" class="margin-top-xs  margin-left-sm">
				<text class="text-gray text-xs">积分：</text>
			  	<text
			  		class="text-red text-lg text-bold"
			  	>{{item.goodsPoints ? item.goodsPoints : 0}}</text>
			  	
			  </view>
			  <!-- 付费商品 -->
              <view v-else class="flex justify-between margin-top-sm align-center">
                <view
                  class="text-price text-bold text-red text-lg"
                  style="margin-left: 10px;"
                >
                  {{item.priceDown}}
                  <text
                    v-if="item.priceDown!=item.estimatedPriceVo.price"
                    class="text-red text-xs"
                    style="font-weight: 500;padding-left: 6rpx"
                  >劵后价</text>

                  <text
                    v-if="item.inventoryState=='1'"
                    class="text-gray text-xs"
                    style="font-weight: 500;padding-left: 6rpx"
                  >已售罄
                  </text>

                  <!-- <text
                    v-if="
                    item.estimatedPriceVo.originalPrice!='0.0'&&item.priceDown!= item.estimatedPriceVo.originalPrice"
                    class="price-original text-xs"
                  >￥{{ item.estimatedPriceVo.originalPrice }}
                  </text> -->
                </view>

                <view
                  class="cu-tag bg-red radius sm"
                  v-if="item.freightTemplat&&item.freightTemplat.type == '2'"
                >包邮
                </view>

                <view
                  v-if="item.inventoryState!='1'"
                  class="text-gray text-sm"
                  style="transform: scale(0.8, 0.8); flex: 1; margin-right: 10px;"
                >已售{{item.saleNum}}
                </view>
              </view>
            </navigator>
          </view>
        </view>

      </view>

    </view>
  </view>
</template>

<script>
import lazyLoad from "components/lazy-load/index";
export default {
  data () {
    return {};
  },

  components: {
    lazyLoad
  },
  props: {
    goodsList: {
      type: Array,
      default: () => []
    },
    goodsSpace: {
      type: String | Number,
      default: 8
    }
  },
  methods: {}
};
</script>
<style>
.goods-container {
  justify-content: space-between;
  flex-wrap: wrap;
  box-sizing: content-box;
  /* padding: 20rpx; */
}

.goods-box {
  width: 100%;
  height: 390rpx;
  background-color: #fff;
  overflow: hidden;
  /* margin-bottom: 20rpx; */
  border-radius: 10rpx;
  /* box-shadow: 0px 0px 30px #e5e5e5; */
}

.goods-box .img-box {
  width: 100%;
  height: 200rpx;
  overflow: hidden;
}

.goods-box .img-box image {
  width: 100%;
  height: 200rpx;
}
</style>
