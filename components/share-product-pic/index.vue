<template>
  <view style="position: fixed;left:100%;z-index: -9999;opacity: 0;">
    <canvas
      :canvas-id="canvasID"
      :style="{ width: canvasW + 'px', height: canvasH + 'px' }"
    ></canvas>
  </view>
</template>

<script>
var _this;
export default {
  name: "printable-coupon",
  props: {
    //canvasID 等同于 canvas-id
    canvasID: {
      Type: String,
      default: 'posterCanvas'
    },
    //优惠券类型 0代金券 1折扣券 2兑换券
    type: {
      type: Number,
      default: 0
    },
    //原价减到手价大于0 true反之false
    showDesc: {
      type: Boolean,
      default: true
    },
    // //优惠券左侧标题
    // leTitle: {
    //   //优惠券左侧标题
    //   type: Number,
    //   default: ''
    // },
    // //优惠券左侧内容
    // leDesc: {
    //   type: Number,
    //   default: ''
    // },
    //优惠券右侧标题
    riTitle: {
      type: String,
      default: ''
    },
    //优惠券开始时间到结束时间 格式 yyyy.mm.dd - yyyy.mm.dd
    riTime: {
      type: String,
      default: ''
    },
    //到手价
    estimatedPrice: {
      type: Number | String,
      default: 0
    },
    //划线价
    originalPrice: {
      type: Number | String,
      default: 0
    },
    //商品图片
    spuImg: {
      type: String,
      default: ''
    },
    //商家微信
    wxchat: {
      type: String,
      default: ''
    },
    //门店信息对应的四个图标，强烈建议更改为自己图片服务器内的网络地址图片
    icons: {
      type: Object,
      default () {
        return {
          //背景图
          bgimg:  'https://img.songlei.com/share/share-bg-img.png',
          //立即购买
          order: 'https://img.songlei.com/share/order.png',
          //松鼠优惠
          biscount: 'https://img.songlei.com/share/biscount-2.png',
          //松鼠爆款
          popular: 'https://img.songlei.com/share/popular-money-1.png',
          // 预估到手价背景图
          estimateBgimg: 'https://img.songlei.com/share/estimateBgimg.png',
        }
      }
    }
  },
  data () {
    return {
      canvasW: 0,
      canvasH: 0,
      ctx: null,
      width: 700
    };
  },
  methods: {
    async onCanvas () {
      uni.showLoading({
        title: "努力加载中.."
      })
      let _this = this;
      const ctx = uni.createCanvasContext(_this.canvasID, _this);
      _this.canvasW = uni.upx2px(_this.width)
      _this.canvasH = uni.upx2px(_this.width * 0.8)
      // _this.canvasH = uni.upx2px(_this.width / 2)
      ctx.setFillStyle('#FFFFFF'); //canvas背景颜色
      ctx.fillRect(0, 0, _this.canvasW, _this.canvasH); //canvas画布大小
      // ctx.fillRect(0, 0, _this.canvasW-130, _this.canvasH); //canvas画布大小
      //最终显示正常的需要宽为350px 为了让其显示完全 所以需要进行比例缩放
      ctx.scale(_this.canvasW / 350, _this.canvasW / 350)
      // ctx.scale(_this.canvasW / 500, _this.canvasW / 350)
      //线条颜色
      let lineStroke = '#FFFFFF'
      let beginX = 15 //起始坐标x
      let beginY = 15 //起始坐标y
      let leftW = 100 //左侧宽度
      let rightW = 210 //右侧宽度
      let couponH = 30 //高度
      let radius = 10 //圆的半径
      let money = '￥'
      _this.drawCouponShell(ctx, beginX, beginY, leftW, rightW, couponH, radius, lineStroke)

      //优惠券内部文字(左侧)
      let u = ''//￥'
      let m = ''//_this.leTitle
      let desc1 = ''//_this.leDesc > 0 ? `满${_this.leDesc}元可用` : '无门槛使用'
      let leftFont = {
        x: beginX + leftW / 2 - radius - ctx.measureText(u).width / 2 - ctx.measureText(m)
          .width / 2,
        y: beginY + 40
      }

      //优惠券左侧上
      // ctx.beginPath();
      // _this.textAlign = 'center'
      // let mX = leftFont.x
      // let mY = leftFont.y
      // let uX = 0
      // if(_this.type==0){
      // 	ctx.setFontSize(uni.upx2px(26));
      // 	mX = leftFont.x + ctx.measureText(u).width
      // 	ctx.font = 'normal bold 20px sans-serif';
      // 	uX = leftFont.x
      // }
      // if (_this.type == 1) {
      // 	u = '折'
      // 	ctx.font = 'normal bold 20px sans-serif';
      // 	uX = leftFont.x + ctx.measureText(m).width
      // }
      // if (_this.type == 2) {
      // 	m = '兑换券'
      // 	mX = leftW / 3
      // 	u = ''
      // 	desc1 = ''
      // 	mY = leftFont.y + 15
      // }
      // ctx.setFontSize(uni.upx2px(26));
      // ctx.setFillStyle('#E41F19');
      // ctx.fillText(u, uX, leftFont.y);
      //大字体  代金券 左小右大 折扣券 左大右小
      // ctx.font = 'normal bold 20px sans-serif';
      // ctx.fillText(m, mX, mY)
      // ctx.closePath();

      //优惠券左侧下
      // ctx.beginPath();
      // _this.textAlign = 'center'
      // ctx.setFontSize(uni.upx2px(24));
      // ctx.setFillStyle('#E41F19');
      // ctx.fillText(desc1,leftW / 2 - ctx.measureText(desc1).width / 3, leftFont.y + 20)
      // ctx.closePath();

      //背景图
      let bgimg = await _this.getImageInfo(_this.icons.bgimg)
      ctx.drawImage(bgimg, 0, 0, _this.canvasW + 20, _this.canvasW)
      //顶部图片
      if (_this.showDesc == false) {
        let biscount = await _this.getImageInfo(_this.icons.popular)
        ctx.drawImage(biscount, beginX + leftW + radius - 40, leftFont.y - 50, 260, 70)

      } else {
        let biscount = await _this.getImageInfo(_this.icons.biscount)
        ctx.drawImage(biscount, beginX, leftFont.y - 35, 160, 62)
      }

      //顶部文字
      // let title = _this.riTitle
      // ctx.beginPath();
      // //设置字体（粗体 大小 
      // ctx.font = 'normal bold 22px sans-serif';
      // ctx.setFillStyle('#000');
      // ctx.fillText(title, beginX + leftW + radius - 30, leftFont.y - 20)
      // ctx.closePath();

      //优惠价
      if (_this.showDesc == true) {
        ctx.font = 'normal 26px sans-serif';
        ctx.setFillStyle('#FEE2BB');
        ctx.fillText(money, beginX + leftW + radius + 45, leftFont.y + 12)
        let desc2 = `${_this.riTime}`
        ctx.beginPath();
        // ctx.setFontSize(uni.upx2px(23));
        //设置字体（粗体 大小
        ctx.font = 'normal 50px sans-serif';
        ctx.setFillStyle('#FEE2BB');
        ctx.fillText(desc2, beginX + leftW + radius + 67, leftFont.y + 12)
        ctx.closePath();
      }

      // if (_this.showDesc == true) {
      const ws = _this.judgeWidth(_this.width)
      const bottomSet = {
        rimX: beginX,
        // rimY: beginY + couponH + beginY,
        rimY: beginY + couponH,
        rimW: leftW + radius * 2 + rightW,
        rimH: (_this.canvasH - couponH * (_this.canvasW / 350) - beginY * ws * (_this.canvasW /
          350))
      }

      //下侧内容(边框)
      ctx.beginPath();
      ctx.setShadow(0, 0, 0, '#FFFFFF')
      ctx.setFillStyle(lineStroke)

      ctx.rect(bottomSet.rimX, bottomSet.rimY + 40, bottomSet.rimW - 12, 180)
      ctx.fillRect(bottomSet.rimX, bottomSet.rimY + 40, bottomSet.rimW - 12, 180);
      ctx.stroke();
      ctx.closePath();
      //取消下面的阴影
      ctx.setShadow(0, 0, 0, '#FFFFFF')

      ctx.beginPath();
      ctx.stroke();
      ctx.closePath();
      //商品图片
      ctx.setFillStyle('#FFFFFF');
      let spuImg = await _this.getImageInfo(_this.spuImg)
      ctx.drawImage(spuImg, bottomSet.rimX + 5, bottomSet.rimY + 45, 175, 170)

      //到手价
      if (_this.showDesc == false) {

        //图片
        let estimateBgimg = await _this.getImageInfo(_this.icons.estimateBgimg)
        ctx.drawImage(estimateBgimg, bottomSet.rimX + 185, bottomSet.rimY + 70, 130, 65)

        //到手价文字
        ctx.setFillStyle('#ffffff');
        ctx.font = 'normal 16px sans-serif';
        ctx.fillText('到手价', bottomSet.rimX + 220, bottomSet.rimY + 90)

        ctx.font = 'normal 18px sans-serif';
        ctx.setFillStyle('#000000');
        ctx.fillText(money, bottomSet.rimX + 190, bottomSet.rimY + 130)


        ctx.font = 'normal 38px sans-serif';
        ctx.setFillStyle('#000000');
        ctx.fillText(_this.estimatedPrice, bottomSet.rimX + 205, bottomSet.rimY + 130)

      } else {

        //图片
        let estimateBgimg = await _this.getImageInfo(_this.icons.estimateBgimg)
        ctx.drawImage(estimateBgimg, bottomSet.rimX + 185, bottomSet.rimY + 85, 130, 65)

        //到手价文字
        ctx.setFillStyle('#ffffff');
        ctx.font = 'normal 16px sans-serif';
        ctx.fillText('预计到手价', bottomSet.rimX + 210, bottomSet.rimY + 105)

        ctx.font = 'normal 18px sans-serif';
        ctx.setFillStyle('#000000');
        ctx.fillText(money, bottomSet.rimX + 190, bottomSet.rimY + 145)

        if (_this.estimatedPrice.length > 4) {
          ctx.font = 'normal 34px sans-serif';
          ctx.setFillStyle('#000000');
          ctx.fillText(_this.estimatedPrice, bottomSet.rimX + 205, bottomSet.rimY + 145)
        } else {
          ctx.font = 'normal 36px sans-serif';
          ctx.setFillStyle('#000000');
          ctx.fillText(_this.estimatedPrice, bottomSet.rimX + 205, bottomSet.rimY + 145)
        }
      }
      //立即购买
      let order = await _this.getImageInfo(_this.icons.order)
      ctx.drawImage(order, bottomSet.rimX + 188, bottomSet.rimY + 163, 120, 45)
      // if (_this.address.length > 14) {
      // 	//地址超过14个字符时则会换行 第一行只能展示14个字符
      // 	ctx.fillText(_this.address.slice(0, 14), bottomSet.rimX + 50, bottomSet.rimY + 90)
      // 	let endStr = _this.address.slice(14)
      // 	if (endStr.length > 14) {
      // 		//如果截取14个后的部分还是大于14则第二行只展示13个字符外加三个点
      // 		ctx.fillText(endStr.slice(0, 13) + '...', bottomSet.rimX + 50, bottomSet.rimY + 108)
      // 	} else {
      // 		//其他时候则会展示完全
      // 		ctx.fillText(endStr, bottomSet.rimX + 50, bottomSet.rimY + 108)
      // 	}
      // } else {
      // 	//否则会展示完全
      // 	ctx.fillText(_this.address, bottomSet.rimX + 50, bottomSet.rimY + 90)
      // }

      //划线价
      if (_this.showDesc == true) {
        //原价文字
        ctx.setFillStyle('#828282');
        ctx.font = 'normal 18px sans-serif';
        ctx.fillText('零售价', bottomSet.rimX + 185, bottomSet.rimY + 70)

        ctx.font = 'normal 18px sans-serif';
        ctx.setFillStyle('#828282');
        ctx.fillText(money, bottomSet.rimX + 235, bottomSet.rimY + 70)
        //划线
        ctx.setStrokeStyle('#828282')
        ctx.strokeRect(bottomSet.rimX + 185, bottomSet.rimY + 65, 105, 1)
        //价格
        ctx.font = 'normal 18px sans-serif';
        ctx.setFillStyle('#828282');
        ctx.fillText(_this.originalPrice, bottomSet.rimX + 252, bottomSet.rimY + 70)
      }
      // let phoneIcon = await _this.getImageInfo(_this.icons.phone)
      // ctx.drawImage(phoneIcon, bottomSet.rimX + 20, bottomSet.rimY + 115, 16, 16)
      // ctx.fillText(_this.spuImg, bottomSet.rimX + 50, bottomSet.rimY + 129)
      // let wxchatIcon = await _this.getImageInfo(_this.icons.wxchat)
      // ctx.drawImage(wxchatIcon, bottomSet.rimX + 20, bottomSet.rimY + 135, 16, 16)
      // ctx.fillText(_this.wxchat, bottomSet.rimX + 50, bottomSet.rimY + 149)

      //绘制文字
      // (cxt, x, y, w, h, r, c) 
      // _this.drawRoundRectColor(ctx, bottomSet.rimX + 180, bottomSet.rimY + 100, bottomSet.rimY + 60, 25, 5, '#FF0000'); //绘制矩形圆角背景
      // let str = '立即购买'
      // ctx.font = 'normal 16rpx sans-serif';
      // ctx.setTextAlign('center');
      // ctx.setFillStyle("#ffffff");
      // //context.fillText(text,x,y)开始绘制文本的 x 坐标位置和 y 坐标
      // ctx.fillText(str, bottomSet.rimX + 230, bottomSet.rimY + 120)
      // // }

      //延迟后渲染至canvas上
      let pic = await _this.setTime(ctx)
      _this.$emit('success', pic);
    },
    //彻底改成同步 防止拿到的图片地址为空
    setTime (ctx) {
      _this = this;
      return new Promise((resole, err) => {
        setTimeout(() => {
          ctx.draw(false, async () => {
            let pic = await _this.getNewPic();
            resole(pic)
          });
        }, 600)
      })
    },
    getNewPic () {
      return new Promise((resolve, errs) => {
        setTimeout(() => {
          uni.canvasToTempFilePath({
            canvasId: _this.canvasID,
            quality: 1,
            complete: (res) => {
              // 在H5平台下，tempFilePath 为 base64
              // 关闭showLoading
              uni.hideLoading();
              //  储存海报地址  也是分享的地址
              resolve(res.tempFilePath)
            }
          }, _this);
        }, 200)
      })
    },
    //获取图片的临时地址
    getImageInfo (imgSrc) {
      return new Promise((resolve, errs) => {
        uni.getImageInfo({
          src: imgSrc,
          success: (image) => {
            resolve(image.path);
          },
          fail: (err) => {
            console.error('getImageInfo:', err)
          }
        });
      });
    },
    /**
     * 绘制优惠券外观
     * @param {Object} ctx canvas的上下文环境
     * @param {Object} beginX 起始x轴坐标
     * @param {Object} beginY 起始y轴坐标
     * @param {Object} leftW 优惠券左侧宽度
     * @param {Object} rightW 优惠券右侧宽度
     * @param {Object} couponH 优惠券高度
     * @param {Object} radius 圆的半径
     * @param {Object} lineStroke 线条颜色
     */
    drawCouponShell (ctx, beginX, beginY, leftW, rightW, couponH, radius, lineStroke) {
      //优惠券外壳
      ctx.setShadow(1, 1.5, 3, '#a3a3a3')
      // 左上侧圆角
      ctx.beginPath();
      ctx.arc(beginX + 10, beginY + 10, radius, Math.PI, Math.PI * 1.5);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //左上横线
      ctx.beginPath();
      ctx.moveTo(beginX + 10, beginY);
      ctx.lineTo(leftW + beginX, beginY);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //上半圆
      ctx.beginPath();
      ctx.arc(leftW + beginX + radius, beginY, radius, 0, Math.PI);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //右上横线
      ctx.beginPath();
      ctx.moveTo(beginX + leftW + radius * 2, beginY);
      ctx.lineTo(beginX + leftW + radius * 2 + rightW, beginY);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //右侧竖线
      ctx.beginPath();
      ctx.moveTo(beginX + leftW + radius * 2 + rightW, beginY);
      ctx.lineTo(beginX + leftW + radius * 2 + rightW, couponH + beginY);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //右下横线
      ctx.beginPath();
      ctx.moveTo(beginX + leftW + radius * 2, beginY + couponH);
      ctx.lineTo(beginX + leftW + radius * 2 + rightW, beginY + couponH);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //下半圆
      ctx.beginPath();
      ctx.arc(leftW + beginX + radius, beginY + couponH, radius, 0, Math.PI, true);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //左下横线
      ctx.beginPath();
      ctx.moveTo(beginX + 10, couponH + beginY);
      ctx.lineTo(leftW + beginX, couponH + beginY);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //左下侧圆角
      ctx.beginPath();
      ctx.arc(beginX + 10, couponH + beginY - 10, radius, Math.PI * 0.5, Math.PI);
      ctx.stroke();
      ctx.closePath();

      //左侧竖线
      ctx.beginPath();
      ctx.moveTo(beginX, beginY + 10);
      ctx.lineTo(beginX, couponH + beginY - 10);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //虚线
      ctx.beginPath();
      ctx.moveTo(beginX + leftW + radius, beginY + radius);
      ctx.lineTo(beginX + leftW + radius, beginY + couponH - radius);
      ctx.setLineDash([5, 8]);
      ctx.strokeStyle = lineStroke;
      ctx.stroke();
      ctx.closePath();

      //取消下面的阴影
      ctx.setShadow(0, 0, 0, '#000')
      //取消虚线的使用
      ctx.setLineDash();
    },
    //根据输入的宽_this.width(rpx)判断方框的高度比例
    judgeWidth (width) {
      const dpr = uni.getSystemInfoSync().pixelRatio
      if (width >= 600) {
        if (dpr == 2) {
          return width / 50 - 11
        }
        return width / 50 - 11 + 1
      }
      return (width / 50 - 12) * 2 + 1
    },
    //绘制按钮圆角
    drawRoundRectColor (cxt, x, y, w, h, r, c) {
      if (2 * r > w || 2 * r > h) { //圆的直径必然要小于矩形的宽高     
        return false;
      }
      cxt.save();
      cxt.translate(x, y);
      //绘制圆角矩形的各个边
      cxt.beginPath(0);
      cxt.arc(w - r, h - r, r, 0, Math.PI / 2); //从右下角顺时针绘制，弧度从0到1/2PI  
      cxt.lineTo(r, h); //矩形下边线
      cxt.arc(r, h - r, r, Math.PI / 2, Math.PI); //左下角圆弧，弧度从1/2PI到PI 
      cxt.lineTo(0, r); //矩形左边线 
      cxt.arc(r, r, r, Math.PI, Math.PI * 3 / 2); //左上角圆弧，弧度从PI到3/2PI
      cxt.lineTo(w - r, 0); //上边线  
      cxt.arc(w - r, r, r, Math.PI * 3 / 2, Math.PI * 2); //右上角圆弧 
      cxt.lineTo(w, h - r); //右边线
      cxt.closePath();

      cxt.fillStyle = c || "#fff"; //若是给定了值就用给定的值否则给予默认值  
      cxt.fill();
      cxt.restore();
    },
  },
  mounted () {
    _this = this;
  },
}
</script>

<style lang="scss">
</style>
