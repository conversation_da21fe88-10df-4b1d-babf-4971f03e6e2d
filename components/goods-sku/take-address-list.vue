<template>
	<view>
		<!-- 导航栏 -->
		<view class="cu-bar bg-white solid-bottom plr-lg ptb-xl">
			<view class="text-black text-bold font-lg">自提地址</view>
		</view>

		<!-- 自提地址列表 -->
		<view class="cu-list menu" v-if="addressList && addressList.length > 0">
			<view
				v-for="(item, index) in addressList"
				:key="item.id"
				class="padding flex justify-between border-item"
				:class="[item.selected ? 'bg-light-orange text-dark-yellow ' : '', isManaging ? 'flex-direction' : 'align-center']"
				@tap.stop="selectAddress(index)"
			>
				<view>
					<view v-if="item.provinceName || item.cityName || item.countyName" class="text-xss" :class="[item.selected ? '' : 'text-light-gray']">
						{{ item.provinceName || '' }} {{ item.cityName || '' }} {{ item.countyName || '' }}
					</view>
					<view class="text-xdf text-bold">{{ item.detailInfo }}</view>
					<view class="text-xss" :class="[item.selected ? '' : 'text-gray']">
						{{ item.telNum }}
						<text class="cu-tag margin-left-sm" v-if="item.isDefault === '1'">默认</text>
					</view>
				</view>
				<radio class="brown" style="transform: scale(0.7)" :value="item.id" :checked="item.selected" />
			</view>
		</view>
		<view v-else class="margin-top text-center">该店铺没配自提地址</view>
	</view>
</template>

<script>
import api from 'utils/api';
import distanceUtil from 'utils/distanceUtil';
export default {
	props: {
		shopId: {
			type: String,
			default: ''
		},
		latitude: {
			type: String,
			default: ''
		},
		longitude: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			addressList: []
		};
	},
	created() {
		this.shopAddressPage();
	},
	methods: {
		shopAddressPage() {
			api.getTaskAddr({
				shopId: this.shopId
			}).then((res) => {
				this.addressList = res.data;
				if (this.addressList && this.addressList.length > 0) {
					const nearDistance = distanceUtil.getNearestDistance(this.latitude, this.longitude, this.addressList);
					console.error('==nearDistance===', nearDistance);
					let tag = 0; // 初始化默认选中的
					this.addressList.forEach((item, i) => {
						if (nearDistance) {
							if (nearDistance.id == item.id) {
								tag = i;
							}
							this.addressList[i].selected = this.addressList[i].id === nearDistance.id;
						} else {
							this.addressList[i].selected = i == 0;
						}
					});
					this.$emit('selectAddress', this.addressList[tag], 'shop');
				}
			});
		},

		selectAddress(index) {
			this.addressList.forEach((item, i) => {
				this.$set(this.addressList, i, {
					...this.addressList[i],
					selected: i === index
				});
			});
			this.$emit('selectAddress', this.addressList[index], 'shop');
		}
	}
};
</script>

<style scoped>
.cu-tag:not([class*='bg']):not([class*='line']) {
	background-color: ##b96d47;
}
.border-item {
	border-bottom: solid 1rpx #f6f6f6;
}
</style>
