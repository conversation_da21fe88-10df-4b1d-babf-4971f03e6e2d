<template>
	<view>
		<!-- 快递 同城  自提 -->
		<!-- 	<view class="content text-black text-bold font-lg ptb-xl plr-lg">收货方式</view>
		 -->
		<view style="background: #f6f6f6">
			<view class="flex align-center justify-around padding-top-sm padding-bottom-sm mlr-48">
				<view @click="handlePickChange(item.type)" class="distribution-item"
					:class="[distributionMode === item.type?'checked':'']" v-for="item in distributionData"
					:key="item.type">
					{{item.name}}
				</view>
			</view>
		</view>

		<!-- 分割线 -->
		<!-- <view class="height-xsm" style="background: #f6f6f6"></view> -->

		<!-- 产品需求统一校验礼品卡电子卡类型不展示 -->
		<view class="plr-xxxsm" v-if="goodsSpu.productType != '2' && promptlyBuySt">
			<view>
				<!-- 0 快递 1自提 2 同城  收货地址和自提地址里面的地址字段一样 -->
				<view @click="handleShowModal" class="flex align-center justify-between"
					:class="[distributionMode === '0' ? 'solid-bottom' : '', distributionMode == '2' || distributionMode == '1' ? 'mt-xxsm' : 'mtb-xxsm']">
					<view class="flex" style="flex: 1">
						<image :src="$imgUrl('live/user-center/gray_address.png')" class="icon-address" />
						<view v-if="selectAddress" style="flex: 1" class="mt-sss ml-xs text-black font-xxxmd">
							<view class="text-bold overflow-1">
								{{ selectAddress.detailInfo || '暂无地址' }}
								{{ scrollTop < 6 ? '' : selectAddress.userName || '' }}
								<text v-if="distributionMode === '1'">【下单后{{ orderPrompt || '' }}】</text>
							</view>
							<view v-if="scrollTop < 6" class="font-xmd text-gray mt-s">
								{{ selectAddress.userName || '' }} {{ selectAddress.telNum || '' }}
							</view>
						</view>
						<view v-else>请选择地址</view>
					</view>
					<text v-if="scrollTop < 6" class="cuIcon-right text-col text-light-gray"></text>
				</view>
				<!-- 1自提 2 同城   同城配送-上门自提 选时间 -->
				<view class="solid-bottom pb-sm" v-if="distributionMode == '2' || distributionMode == '1'">
					<view class="plr-xxxsm mtb-xs">
						<delivery-timeperiod @change="handleDeliveryTime"
							:useraddresid="selectAddress && selectAddress.id" ref="timeperiod" class="deliver"
							:type="distributionMode" :title="distributionMode == '2' ? '同城配送' : '上门自提'"
							:shopId="goodsSpu.shopId"></delivery-timeperiod>
					</view>

					<view v-if="scrollTop < 6 && distributionMode == '1' && selfLimitDay > 0" class="Tips text-sm"
						style="margin-top: 0">
						<view class="Tips-title pl-xxxsm">温馨提示:</view>
						<view class="Tips-text plr-xxxsm">
							请您购买后及时提货，商品自您选择的自提时间起，
							<text>{{ selfLimitDay }}</text>
							日为自提期限到期未提货的，系统自动取消订单并给您办理退款。
						</view>
					</view>
				</view>
			</view>
			<!-- 弹框遮罩层 -->
			<view v-if="showPopup" class="popup-mask" @tap.stop="closePopup"></view>
			<!-- 弹框内容 -->
			<view class="popup-container" :class="{ 'popup-show': showPopup }">
				<view class="cu-bar bg-white justify-end solid-bottom">
					<view class="text-black text-bold font-xxl" style="flex:1; text-align: center;">地址和配送服务</view>
					<view class="action" @click="closePopup">
						<text class="cuIcon-close text-black font-xxxl text-bold"></text>
					</view>
				</view>

				<scroll-view scroll-y class="popup-content" catchtouchmove="true">
					<!-- 快递配送 ｜ 同城配送 显示收货地址 -->
					<!-- 地址列表 -->
					<template v-if="distributionMode === '0'">
						<address-list
							@selectAddress="handleSelectAddress" />
					</template>
					
					<template v-if="distributionMode === '2'">
						<address-list
							@selectAddress="handleSelectAddress" />
					</template>
					
					<template v-if="distributionMode === '1'">
						<take-address-list  @selectAddress="handleSelectAddress"
							:shopId="goodsSpu.shopId" :latitude="latitude" :longitude="longitude" />
					</template>
				</scroll-view>

				<view class="padding flex flex-direction sure cu-bar tabbar margin-bottom-xl">
					<view class="btn-group">
						<button class="lg font-lg"
							style="width: 90vw; border-radius: 12rpx;background: #d9b192; color: #ffffff"
							@click="confirmPick">确定</button>
					</view>
				</view>
			</view>
		</view>
		<uni-popup ref="perpopup" type="pop" :mask-click="false">
			<view class="permissions_box">当您使用APP时，获取最近的自提点需要位置权限，不授权上述权限，不影响APP其他功能使用。</view>
		</uni-popup>
	</view>
</template>

<script>
	import AddressList from '@/components/goods-sku/address-list.vue';
	import TakeAddressList from '@/components/goods-sku/take-address-list.vue';
	import DeliveryTimeperiod from '@/components/delivery-timeperiod/delivery-timeperiod.vue';
	import api from 'utils/api';

	export default {
		components: {
			AddressList,
			DeliveryTimeperiod,
			TakeAddressList
		},
		props: {
			goodsSpu: {
				type: Object,
				default: () => {}
			},
			goodsSku: {
				type: Object,
				default: () => {}
			},
			modalSkuType: {
				type: String,
				default: ''
			},
			scrollTop: {
				type: Number,
				default: 0 //滑动距离顶部距离
			}
		},

		data() {
			return {
				showPopup: false,
				distributionMode: '1',
				selfLimitDay: '', //自提天数
				selectAddress: null, // 选择的快递收货地址或者配送地址或者的自提地址
				// 当前经纬度
				latitude: '',
				longitude: '',
				time: '' //选择的自提时间或者配送时间
			};
		},

		created() {
			let that = this;
			// #ifdef MP-WEIXIN
			this.getCurrentPos();
			// #endif
			// #ifdef APP-PLUS
			var platform = uni.getSystemInfoSync().platform;
			if (platform == 'android') {
				plus.android.checkPermission(
					'android.permission.ACCESS_FINE_LOCATION',
					(granted) => {
						console.log("=====granted=========", granted);
						if (granted.checkResult == -1) {
							//弹出
							that.$refs.perpopup.open('top');
						} else {
							//执行你有权限后的方法
							that.getCurrentPos();
						}
					},
					(error) => {
						console.error('Error checking permission:', error.message);
					}
				);
				plus.android.requestPermissions(['android.permission.ACCESS_FINE_LOCATION'], (e) => {
					//关闭
					that.$refs.perpopup.close();
					if (e.granted.length > 0) {
						//执行你有权限后的方法
						that.getCurrentPos();
					}
				});
			} else {
				//执行你有权限后的方法 ios
				that.getCurrentPos();
			}
			// #endif
		},

		computed: {
			// 展示配送方式
			distributionData() {
				const defaultDistributionData = this.defaultDistribution();
				this.distributionMode = defaultDistributionData[0].type;
				return defaultDistributionData;
			},

			// 判断是否可以展示收货方式， 目前跟order组件保持一致
			promptlyBuySt() {
				const {
					skus,
					shelf,
					verifyStatus,
					presaleType,
					spuType,
					stock
				} = this.goodsSpu;
				const stocks = skus?.length ? skus.reduce((pre, cur) => cur.stock + pre, 0) : stock; // 计算商品库存
				/**
				 * shelf verifyStatus 上架、审核通过
				 * stock 商品库存
				 * presaleType 预售商品
				 * spuType 3-积分商品，5-付费优惠券；都可用积分兑换
				 * modalSkuType 2-立即购买按钮 3-立即兑换
				 */
				return (
					(shelf == '1' && verifyStatus == '1' && stocks && (!presaleType || presaleType != '1') && this
						.modalSkuType == '2') ||
					this.modalSkuType == '3' ||
					spuType == 3 ||
					spuType == 5
				);
			},
			// 下单提示
			orderPrompt() {
				return this.goodsSpu.shopId == '1549975071225999362' ? '请等待老师送货' : '30分钟可提货';
			}
		},

		methods: {
			getCurrentPos() {
				let that = this;
				uni.getLocation({
					success(res) {
						const {
							latitude,
							longitude
						} = res;
						that.latitude = latitude;
						that.longitude = longitude;
					},
					fail() {}
				});
			},
			handleShowModal() {
				this.showPopup = true;
			},

			closePopup() {
				this.showPopup = false;
			},

			// 默认配送方式数据
			defaultDistribution() {
				// 展示配送方式
				const arr = [{
						type: '0',
						name: '快递发货',
						parameterName: 'distributionVo'
					},
					{
						type: '2',
						name: '同城配送',
						parameterName: 'sameCityVo'
					},
					{
						type: '1',
						name: '到店自提',
						parameterName: 'selfMentionVo'
					}
				];
				const distribution = arr.filter((item) => this.goodsSpu.distributionMode.indexOf(item.type) !== -1);
				return distribution;
			},

			handlePickChange(type) {
				// 切换了收货方式，先把之前选择的收货方式移除
				console.log("======handlePickChange=========", type);
				this.selectAddress = {};
				this.distributionMode = type;
				if (this.distributionMode == 1) {
					this.getSelfLimitDay();
				}
			},

			handleSelectAddress(address, type) {
				console.log("==handleSelectAddress===", address, type);
				if((type=='user' && (this.distributionMode==0 || this.distributionMode==2)) || (type=='shop' &&  this.distributionMode==1)){
					this.selectAddress = address;
					//组件刚刚加载，默认选中一个的时候触发。 如果有弹框，需要点击确定的时候再触发
					if (this.showPopup == false) {
						this.$emit('confirm', {
							distributionMode: this.distributionMode,
							address: this.selectAddress,
							time: this.time
						});
					}
				}
			},

			confirmPick() {
				this.$emit('confirm', {
					distributionMode: this.distributionMode,
					address: this.selectAddress,
					time: this.time
				});
				this.closePopup();
			},

			handleDeliveryTime(e, type) {
				this.time = e;
			},

			// //获取自提天数
			getSelfLimitDay() {
				let that = this;
				api.getLivingTag().then((res) => {
					if (res && res.data) {
						that.selfLimitDay = res.data.selfLimitDay ? res.data.selfLimitDay : null;
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.distribution-item {
		width: 186rpx;
		height: 60rpx;
		background: #FFFFFF;
		color: #FF4444;
		border-radius: 20rpx;
		line-height: 60rpx;
		text-align: center;
	}
	
	.checked {
		background-color: #FF4444;
		color: #fff;
	}

	.popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		z-index: 999;
	}

	.popup-container {
		position: fixed;
		left: 0;
		right: 0;
		bottom: -100%;
		height: 85vh;
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;
		transition: all 0.3s ease;
		z-index: 1000;
	}

	.sure {
		padding-bottom: calc(env(safe-area-inset-bottom));
	}

	.popup-show {
		transform: translateY(-100%);
	}

	.popup-content {
		height: calc(85vh - 340rpx);
	}

	.icon-address {
		width: 40rpx;
		height: 40rpx;
	}

	.permissions_box {
		background-color: #fff;
		padding: 20rpx;
		z-index: 9999;
	}
</style>