<template>
	<view>
		<view class="cu-bar flex response" @tap.stop="changeModalGift()">
			<!-- <view class="cu-bar flex response" @tap.stop="$EventBus.$emit('changeModalGift', 'show',giftCardPrice,giftDeductPrice,giftPrice)">-->
			<view class="flex-twice">
				<view class="text-black margin-left-sm text-height text-col text-df">红包礼品卡（剩余￥{{giftCardPrice}}）</view>
			</view>
			<view class="flex-sub text-right margin-right flex justify-end align-center">
				<view v-if="giftCardPrice!='0'">
					<view v-if="giftPrice && giftPrice>0" class="flex-twice flex justify-end align-center text-right text-red">
						-
						<format-price signFontSize="26rpx" smallFontSize="26rpx" priceFontSize="30rpx"
			  	:price="giftPrice" />
						<!-- <text class="text-price">{{ giftPrice }}</text> -->
					</view>
					<text v-else-if="giftPrice=='0'" class="flex-twice text-right text-df">
						暂时不使用
					</text>
					<text v-else class="flex-twice text-right text-df">
						去选择
					</text>
					<text v-if="!giftCardPrice&&giftPrice!=null " class="flex-twice text-right text-df">
						无可用
					</text>
				</view>
				<text v-else class="flex-twice text-right text-df">
					去购买
				</text>
				<text class="cuIcon-right text-col"></text>
			</view>
		</view>

		<!-- 礼品卡支付金额 弹框 -->
		<!-- <view :class="'cu-modal bottom-modal ' + modalGift"> -->
		<view class="cu-modal bottom-modal" :class="modalGift ? 'show' : ''" catchtouchmove="touchMove">
			<!-- 关闭弹框icon -->

			<text class="cuIcon-close text-center flex align-center justify-center"
				:style="{color: `#FFF`,position: `absolute`,top: top,right: `3%`,border: `1rpx solid #FFF`,width: `40rpx`,height: `40rpx`,borderRadius: `50%`}"
				@tap="hideModalGift()">
			</text>

			<view class="cu-dialog dialo-sku bg-white-black" @tap.stop :style="{
            paddingTop:'0px',
            height: top=='16%'?'78%':'58%',
            'z-index':'99999'
            }" :class="modalGift ? 'animation-slide-bottom' : ''">
				<view class="flex justify-between margin-bottom margin ">
					<view class="text-xsm text-bold text-df">礼品卡金额（剩余¥{{giftCardPrice}}）</view>
					<view @click="Usagerules" class="text-sm">使用规则<text class="cuIcon-right text-col"></text></view>
				</view>

				<view class="gift-line"></view>

				<view class="bg-white-black">
					<radio-group style="display: block;" @change="radioChange">
						<label v-for="(item, index) in items" :key="item.value">
							<view class="flex justify-between margin-bottom margin">
								<view class="text-xsm">{{item.name}}</view>
								<view>
									<radio style="transform:scale(0.7);width: 49rpx;height: 40rpx;" :value="item.value"
										:checked="index === current" />
								</view>
							</view>
						</label>
					</radio-group>
				</view>

				<!-- 自定义使用 -->
				<view :style="{background:current=='2'?`#F8F8F8`:`#FFFFFF`}" class="gift-price">
					<view v-if="current=='2'">
						<view class="text-xsm margin-top-xs text-left padding-left-lg">使用礼品卡金额</view>
						<view class="search-content round margin-top-xs">
							<text class="search-content-price">￥</text>
							<input type="digit" placeholder="请输入礼品卡金额" v-model="giftPrice"
								placeholder-style="font-size:26rpx;font-weight:500" confirm-type="done">
							<text v-if="giftPrice" class="cuIcon-close close" @click="close"></text>
						</view>

						<view v-if="isPrice" class="text-sm padding-xs text-red text-left padding-left-lg">
							{{priceText}}
						</view>

						<view class="text-xsm text-center text-light-gray margin-top">本单您可使用 ¥ {{giftDeductPrice}}金额哦
						</view>
					</view>
				</view>

				<view class="padding-top-xl foot">
					<button :disabled="isPrice" style="width:92%;" class="cu-btn round bg-red margin text-xsm"
						@tap="submitGift">
						确定
					</button>
				</view>
			</view>
		</view>
	</view>

</template>

<script>
	import price from "utils/price-max-min";
	import {
		getIsPassword
	} from '@/api/gift';
	import codePage from "@/components/verification-code/index.vue";
	import formatPrice from "@/components/format-price/index.vue";
	export default {
		data() {
			return {
				current: null,
				modalGift: '', //礼品卡信息弹框
				giftPrice: null, //礼品卡金额
				isPrice: false, //控制输入的价格
				priceText: '', //礼品卡错误内容
				radioChangeVal: '', //单选内容
			};
		},

		components: {
			codePage,
			formatPrice
		},

		props: {
			//最高抵扣
			giftDeductPrice: {
				type: Number | String,
				default: '',
			},
			// 抵扣金额
			newGiftPrice: {
				type: String | Number,
				default: null
			},
			//可用礼品卡金额
			giftCardPrice: {
				type: Number | String,
				default: '',
			},
			//关闭弹框icon 的高度
			top: {
				type: String,
				default: '16%',
			},

		},

		watch: {
			newGiftPrice: {
				handler: function(newValue, oldValue) {
					this.giftPrice = newValue
					this.$EventBus.$emit('gift-price-value', this.giftPrice)
				},
				immediate: true, //第一次刷新页面时就会执行
			},

			giftPrice: {
				handler: function(newValue, oldValue) {
					if (this.current == '2') {
            
						// 正则限制输入的金额不能为中文、特殊字符、两位小数
						let reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^[0-9]\.[0-9]([0-9])?$)/;
						// 输入金额不能为空
						if (this.giftPrice === '') {
							this.priceText = '请输入金额';
							this.isPrice = true;
							console.log('请输入金额')
							return
						};
						// 如果不符合正则，提示
						if (!reg.test(+this.giftPrice)) {
							this.priceText = '金额错误必须大于0或保留两位小数，请重新输入'
							this.isPrice = true;
							this.giftPrice = this.giftPrice
							console.log('支付金额格式必须大于0，请重新输入')
							return
						};
						// 非数字和小数点去掉     
						this.giftPrice = this.giftPrice.toString().replace(/[^0123456789.]/, "")
						// 防止无输入无限个 “.”
						this.giftPrice = this.giftPrice.toString().replace(/\.+/, ".")
						// 在不是 “0.” 开头的字符进行修改：“01”=>1
						if (this.giftPrice.charAt(0) == "0" && this.giftPrice.charAt(1) != "." && this.giftPrice
							.length >= 2) {
							this.giftPrice = this.giftPrice.toString().replace(/0/, "")
						}
						// 获取第一个小数点的索引值
						let index = this.giftPrice.indexOf('.')
						// 获取最后一个小数点的索引值
						let lastIndex = this.giftPrice.lastIndexOf('.')
						// 判断小数点是不是开头，如果是开头，则去除
						if (index == 0) {
							this.giftPrice = this.giftPrice.toString().replace(/\./, "")
						}
						// 只允许小数点后面有 2 位字符
						if (index >= 1) {
							this.giftPrice = this.giftPrice.substring(0, index + 3)
						}
						// 防止小数点后面又出现小数点
						if (index != lastIndex) {
							this.giftPrice = this.giftPrice.substring(0, index + 2)
						}
						//输入金额不能大于剩余金额,不能大于抵扣金额，若大于全部按抵扣金额        
						let maxPrice = price.numberCompare(this.giftPrice, this.giftCardPrice)
						let deductPrice = price.numberCompare(this.giftPrice, this.giftDeductPrice)
						if (maxPrice === 'greater' || deductPrice === 'greater') {
							this.$nextTick(() => {
								this.giftPrice = this.giftDeductPrice;
							})
						}
						this.isPrice = false;
            console.log("到这里了giftPrice是多少", this.giftPrice)
						this.$emit('giftPriceValue', this.giftPrice)
					}
				},
				immediate: true, //第一次刷新页面时就会执行
			},
		},

		computed: {
			items() {
				if (this.giftCardPrice) {
					return [{
							value: '0',
							name: '暂时不使用',
							checked: 'true'
						},
						{
							value: '1',
							name: `抵扣¥${this.giftDeductPrice} 最高使用`
						},
						{
							value: '2',
							name: '自定义使用'
						}
					]
				}
			},
		},

		created() {
			this.getIsPassword();
		},

		mounted() {

		},

		methods: {
			//规则跳转
			Usagerules() {
				console.log('出发了')
				uni.navigateTo({
					url: "/pages/goods/goodsrules/goodsrules?pageName=gift",
				});
			},
			changeModalGift() {
				//默认暂不使用
				// if (!this.giftPrice) {
				// this.giftPrice = '0';
				// }
				//礼品卡余额等于 0 0.00 让去购买
				if (this.giftCardPrice == '0' || this.giftCardPrice == '0.00') {
					let shopId = uni.getStorageSync('paypaymentuserpassword').shopId
					uni.navigateTo({
						url: `/pages/shop/shop-detail/index?id=${shopId}`
					})
					return
				}
				this.modalGift = 'show';
				//处理默认使用 暂不使用，(返显还是第一次都是使用 暂不使用)
				this.current = 0;
				this.giftPrice = 0;
				this.$emit('giftPriceValue', this.giftPrice);
				this.$emit('showModal', false);
			},

			// 礼品卡确认使用
			submitGift() {
				let that = this
				that.modalGift = '';
				if (that.current == '0') {
					that.giftPrice = 0
				} else if (that.current == '1') {
					that.giftPrice = that.radioChangeVal
				}
				// console.log("submitGift===>",that.giftPrice,that.current);
				that.$emit('giftPriceValue', that.giftPrice)
				
				if (that.current != null || that.current != 'null') {
					that.$emit('giftOrderConfirm')
				}
				that.$emit('showModal', true);
			},

			//查询是否设置密码
			async getIsPassword() {
				getIsPassword().then((res => {
					console.log("getIsPassword", res);
					if (res.data) {
						// this.userpassword=res.data
						// isActive 0：未开通 1：已开通支付密码
						// isPasswordless:0：未开通 1：已开通免密支付
						uni.setStorageSync('paypaymentuserpassword', res.data)
					}
				}))
			},

			//礼品卡清空输入框
			close() {
				this.giftPrice = '';
				this.$emit('giftPriceValue', this.giftPrice)
			},

			//礼品卡单选 
			radioChange: function(evt) {
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current = i;
						console.log("ev====", evt.detail.value, this.current);
						if (evt.detail.value == '0') {
							this.radioChangeVal = '0';
							this.isPrice = false;
						} else if (evt.detail.value == '1') {
							this.radioChangeVal = this.giftDeductPrice
							this.isPrice = false;
						} else {
							this.radioChangeVal = '';
							this.giftPrice = '';
							this.isPrice = true;
						}
						break;
					}
				}
			},

			//关闭礼品卡弹框
			hideModalGift() {
				this.modalGift = '';
				this.current = null;
				if (!this.giftPrice) {
					this.giftPrice = null;
					this.$emit('giftPriceValue', this.giftPrice)
					this.$emit('giftOrderConfirm')
				}
				this.$emit('showModal', true);
			},

		},
	};
</script>

<style lang="scss" scoped>
	.showDialog {
		z-index: 99999;
		position: absolute;
		left: 0px;
		top: 0px;
		bottom: 0px;
		right: 0px;
		background: rgba(0, 0, 0, 0.6);
	}

	.gift-price {
		width: 669rpx;
		height: 220rpx;
		background: #F8F8F8;
		border-radius: 23rpx;
		margin: 0 auto;
		padding: 15rpx;
		margin-bottom: 40rpx
	}

	.search-content-price {
		font-weight: 700;
		font-size: 50upx;
	}

	.search-content {
		line-height: 64upx;
		height: 64upx;
		font-size: 30upx;
		color: #000000;
		flex: 1;
		display: flex;
		align-items: center;
		margin: 0 30upx;

	}

	.search-content input {
		flex: 1;
		padding-right: 30upx;
		height: 64upx;
		line-height: 64upx;
		font-size: 50upx;
		text-align: left;
		font-weight: 700;
	}

	.gift-line {
		width: 95%;
		height: 2upx;
		margin: auto;
		/**background: linear-gradient(to right, #999999, #999999 7.5upx, transparent 7.5upx, transparent);*/
		background: radial-gradient(#999999, #999999 2px, transparent 2px, transparent);
		background-size: 10upx 100%;
	}
	
	.arrow-width {
		padding-right: 27rpx;
	}
</style>