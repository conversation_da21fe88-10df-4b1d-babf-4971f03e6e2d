<template>
	<view class="cu-modal bottom-modal" :class="modalSku ? 'show' : ''" catchMove="true" @tap.stop>
		<!-- 银行卡支付 原style="height:85%;"微信不支持 调整78%-->
		<view class="cu-dialog dialo-sku bg-white" :class="modalSku ? 'animation-slide-bottom' : ''" style="height: 90%"
			@tap.stop>
			<view v-if="isTransferShow" class="cu-card article no-card">
				<!-- 轮播展示加购人数和下单人数 立即支付和加购的弹框都有 -->
				<view class="flex align-center" style="background: #fff7f5; border-radius: 18rpx 18rpx 0rpx 0rpx">
					<swiper class="font-md height-60 text-center" style="line-height: 100%; width: 93%; color: #f85420"
						circular :autoplay="true" :interval="3000" :duration="500" :vertical="true"
						easing-function="linear">
						<swiper-item class="flex justify-center align-center ">
							<template v-if="goodsSpu && goodsSpu.avatarUrl && goodsSpu.avatarUrl.length">
								<image class="avatar-icon" v-for="item in goodsSpu.avatarUrl" :src="item"></image>
							</template>
							<text class="ml-s font-md">{{ addCartNum }}+人加购</text>
						</swiper-item>
						<swiper-item class="flex justify-center align-center">
							<template v-if="goodsSpu && goodsSpu.avatarUrl && goodsSpu.avatarUrl.length">
								<image class="avatar-icon" v-for="item in goodsSpu.avatarUrl" :src="item"></image>
							</template>
							<text class="ml-s">{{ addOrderNum }}+人下单</text>
						</swiper-item>
					</swiper>
					<image class="size-xxxl" @tap="hideModalSku" :src="$imgUrl('live/black-close.png')"></image>
				</view>
				<delivery-way v-if="modalSku && goodsSpu.productType != 2" :shopInfo="shopInfo" :goodsSpu="goodsSpu" :goodsSku="goodsSku"
					:modalSkuType="modalSkuType" @confirm="handleDeliveryType" :scrollTop="scrollTop" />
				<!-- 商品信息 -->
				<view v-if="goodsShow" class="mlr-xxxsm flex align-center pt-xxxxsm">
					<!-- 图片展示 -->
					<image @click="handlePreviewImage(goodsSku.picUrl)" v-show="goodsSku.picUrl"
						:src="goodsSku.picUrl || 'https://img.songlei.com/live/img/no_pic.png'" mode="aspectFill" class="row-img" />
					<image @click="handlePreviewImage(0)"
						v-show="!goodsSku.picUrl && goodsSpu.picUrls && goodsSpu.picUrls.length"
						:src="goodsSpu.picUrls[0] || 'https://img.songlei.com/live/img/no_pic.png'" mode="aspectFill"
						class="row-img" />
					<!-- end图片展示 -->

					<!-- 商品信息 -->
					<view class="flex flex-direction justify-between">
						<!-- 积分商品 -->
						<view v-if="goodsSpu.spuType == 3" class="ml-md">
							<view class="flex justify-between" style="margin-right: 40rpx">
								<view class="margin-top-xl text-bold text-red text-xl"
									v-show="!goodsSku || !goodsSku.goodsPoints">
									{{ goodsSpu.goodsPointsDown }}
									<text v-show="goodsSpu.goodsPointsDown != goodsSpu.goodsPointsUp"
										class="text-red margin-lr-xs">-</text>
									<text v-show="goodsSpu.goodsPointsDown != goodsSpu.goodsPointsUp"
										class="text-red text-bold">{{ goodsSpu.goodsPointsUp }}积分</text>
								</view>
								<view class="margin-top-xl text-bold text-red text-lg"
									v-show="goodsSku && goodsSku.goodsPoints">{{ goodsSku.goodsPoints }}积分</view>

								<view class="text-red margin-top-xl"
									style="height: 40rpx; border-radius: 10rpx; font-size: 24rpx; background-color: rgba(255, 0, 0, 0.1); margin-left: 30rpx; padding-top: 4rpx">
									积分兑换
								</view>
							</view>

							<view class="text-gray text-sm" style="margin-top: 16rpx">
								价格
								<text
									style="margin-left: 10rpx; text-decoration: line-through">￥{{ goodsSpu.priceDown || goodsSpu.priceUp }}</text>
							</view>
						</view>
						<!-- 付费优惠券 积分兑换 -->
						<view v-else-if="goodsSpu.spuType == 5" class="ml-md">
							<view class="flex justify-between" style="margin-right: 40rpx">
								<view class="margin-top-xl text-bold text-red text-xl"
									v-show="!goodsSku || !goodsSku.goodsPoints">
									{{ goodsSpu.goodsPointsDown }} 积分
									<text v-show="goodsSpu.goodsPointsDown != goodsSpu.goodsPointsUp"
										class="text-red margin-lr-xs">-</text>
									<text v-show="goodsSpu.goodsPointsDown != goodsSpu.goodsPointsUp"
										class="text-red text-bold">{{ goodsSpu.goodsPointsUp }}积分</text>
								</view>
								<view class="margin-top-xl text-bold text-red text-lg"
									v-show="goodsSku && goodsSku.goodsPoints">{{ goodsSku.goodsPoints }}积分</view>
							</view>
						</view>
						<!-- 付费商品 -->
						<view v-else>
							<!-- 新客专享 产品没规划这里具体需要怎么展示了，先注释了-->
							<!-- <view v-if="goodsSpu.newCustomerGoods && goodsSpu.newCustomerGoods.newPersonPrice">
									<format-price
										color="#F85420"
										signFontSize="24rpx"
										smallFontSize="30rpx"
										priceFontSize="40rpx"
										:price="goodsSpu.newCustomerGoods.newPersonPrice"f
									/>
									<text class="text-bold" style="margin-right: 10rpx">（ 新客专享 ）</text>
								</view> -->

							<!-- 普通商品立即支付-->
							<view v-if="modalSkuType == '2'" class="ml-md">
								<view class="flex align-baseline text-bold text-orange" style="flex-wrap: wrap">
									实付
									<format-price styleProps="font-weight:900" color="#F85420" signFontSize="26rpx"
										smallFontSize="32rpx" priceFontSize="50rpx" :price="realPayPrice" />
									<view v-if="realPayPrice != subtotalPrice" class="mlr-sm"
										style="width: 1rpx; height: 25rpx; background-color: #f85420"></view>
									<view v-if="realPayPrice != subtotalPrice">优惠前￥{{ subtotalPrice }}</view>
								</view>
								<view class="flex mt-sm">
									<text @click="handlePriceDialog" style="background: #feeeef; border-radius: 6rpx"
										class="ptb-s text-orange font-md plr-xxs line-height-1">
										{{ realPayPrice != subtotalPrice ? '共减￥' + reductionPrice : '价格明细' }} >
									</text>
									<template v-if="couponTags && couponTags.length">
										<text v-for="item in couponTags"
											style="border: 1px solid #F85420; border-radius: 6rpx"
											class="ml-xsm ptb-s text-orange font-md plr-xxs line-height-1">
											{{ item.premiseAmount && item.premiseAmount > 0 ? `满${item.premiseAmount}元` : '无门槛' }}减{{ item.reduceAmount }}
										</text>
									</template>
								</view>
							</view>

							<!-- 普通商品不是立即支付的弹框-->
							<view v-else>
								<view class="flex align-center padding-tb-xs" style="flex-wrap: wrap">
									<view class="flex align-center text-bold ml-xxxsm"
										v-show="!goodsSku || !goodsSku.salesPrice">
										<format-price color="#F85420" signFontSize="26rpx" smallFontSize="32rpx"
											priceFontSize="42rpx" :price="goodsSpu.priceDown" />
										<text v-show="goodsSpu.priceDown != goodsSpu.priceUp" class="text-red">-</text>
										<format-price color="#F85420" v-show="goodsSpu.priceDown != goodsSpu.priceUp"
											signFontSize="26rpx" smallFontSize="32rpx" priceFontSize="42rpx"
											:price="goodsSpu.priceUp" />
									</view>
									<view class="text-bold ml-xxxsm" v-if="goodsSku && goodsSku.estimatedPriceVo">
										<format-price color="#F85420" signFontSize="24rpx" smallFontSize="28rpx"
											priceFontSize="38rpx"
											:price="(Number(goodsSku.estimatedPriceVo.price) - Number(goodsSku.estimatedPriceVo.promotionsDiscountPrice)).toFixed(2)" />
									</view>
									<!-- spuType -->

									<!-- 折后价格 -->
									<view
										v-if="goodsSpu.estimatedPriceVo && goodsSpu.spuType != 6 && goodsSpu.priceDown != goodsSpu.estimatedPriceVo.estimatedPrice"
										class="flex align-center ml-md plr-xxxsm ptb-s align-baseline"
										style="background: #fe5002; border-radius: 10rpx">
										<text class="font-xmd text-white">劵后</text>
										<format-price color="#fff"
											v-show="goodsSku && goodsSku.estimatedPriceVo && goodsSku.estimatedPriceVo.estimatedPrice"
											signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="50rpx"
											:price="goodsSku.estimatedPriceVo && goodsSku.estimatedPriceVo.estimatedPrice" />
										<!-- 选择规格后的券后价 -->
										<format-price color="#fff"
											v-show="!(goodsSku && goodsSku.estimatedPriceVo && goodsSku.estimatedPriceVo.estimatedPrice)"
											signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="50rpx"
											:price="goodsSku.estimatedPriceVo && goodsSpu.estimatedPriceVo.estimatedPrice" />
									</view>
								</view>
								<view
									v-if="goodsSpu.spuType != 6 && goodsSpu.estimatedPriceVo && goodsSpu.priceDown != goodsSpu.estimatedPriceVo.estimatedPrice"
									class="flex align-center ml-md font-xxmd" style="color: #fe5002">
									立减
									<text
										v-if="goodsSku && goodsSku.estimatedPriceVo && goodsSku.estimatedPriceVo.estimatedPrice">
										{{
											(
												Number(goodsSku.estimatedPriceVo.price) -
												Number(goodsSku.estimatedPriceVo.promotionsDiscountPrice) -
												Number(goodsSku.estimatedPriceVo.estimatedPrice)
											).toFixed(2)
										}}
									</text>
									<!-- 选择规格后的券后价 -->
									<text v-else>
										{{
											(
												Number(goodsSpu.estimatedPriceVo.price) -
												Number(goodsSpu.estimatedPriceVo.promotionsDiscountPrice) -
												Number(goodsSpu.estimatedPriceVo.estimatedPrice)
											).toFixed(2)
										}}
									</text>
								</view>
							</view>
						</view>

						<!-- 规格展示 新需求改完，这里没展示的地方了，先注释了-->
						<!-- <view class="text-black text-sm" v-show="goodsSpecData.length">
								选择：
								<view class="display-ib" v-for="(item, index) in goodsSpecData" :key="index">
									<view class="display-ib" v-show="!item.checked">{{ item.value }}</view>
									<view class="display-ib" v-show="item.checked" v-for="(item2, index2) in item.leaf" :key="index2">
										<view class="display-ib" v-show="item.checked == item2.id">
											{{ item2.value }}
										</view>
									</view>
									<view class="display-ib" v-show="goodsSpecData.length != index + 1">,</view>
								</view>
							</view>
							 -->
						<!-- 购买数量 -->
						<view v-show="goodsShow" class="flex align-center margin-left margin-top-sm font-xmd">
							<!-- goodsSpu.newCustomerGoods.buyLimit == 1 新客专享购买数量限制 每人指定商品任选一件购买 -->
							<base-stepper customClass="padding-right-sm"
								:disabled="goodsSpu.newCustomerGoods && goodsSpu.newCustomerGoods.buyLimit == 1 ? true : false"
								:stNum="cartNum" :min="minBuyNum" :max="maxBuyNum" @numChange="numChange" />
							<text class="ml-xmd" :class="goodsSku.stock <= 3 ? 'text-scarlet' : ''"
								v-if="goodsSku.stock != null">
								(库存{{ goodsSku.stock <= 3 ? '紧张，仅剩' : '' }}{{ goodsSku.stock }}件{{ minBuyNum > 1 ? '，最小购买' + minBuyNum + '件' : '' }})
							</text>
							<view class="font-xmd ml-md text-gray"
								v-if="goodsSpu.newCustomerGoods && goodsSpu.newCustomerGoods.buyLimit == 1">
								新客专享仅限购买{{ minBuyNum > 1 ? minBuyNum : 1 }}件
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 规格选择 -->
			<scroll-view scroll-y scroll-with-animation @scroll="handleScroll" :style="{
					height: modalSkuType == '2' ? 'calc(90vh - 534rpx)' : 'calc(90vh - 380rpx)'
				}">
				<view class="mlr-xxxsm">
					<!-- 如果不这样会不刷新数据 -->
					<view v-show="false">{{ goodsSpecData }}</view>
					<!-- 规格选择 -->
					<view v-show="goodsShow" class="mtb-xxsm" v-for="(item, index) in goodsSpecData" :key="index">
						<view class="text-black text-bold font-xxxmd">
							<text class="text-black">{{ item.value }}</text>
						</view>

						<view class="grid bg-white">
							<button style="height: auto" class="cu-btn mt-xxsm mr-xxsm ptb-sm plr-md"
								:class="item.checked == item2.id ? 'line-red' : ''" v-for="(item2, index2) in item.leaf"
								:key="index2" @tap="skuClick(item2, index, $event, index2)" :data-index="index"
								:data-index2="index2" :disabled="!!item2.disabled">
								<!-- #ifdef -->
								<!-- 用于小程序页面刷新！！ -->
								<text v-show="false">{{ JSON.stringify(item2) }}</text>
								<!-- #endif -->
								{{ item2.value }}
							</button>
						</view>
					</view>

					<!-- 大码关联的商品，由于现在系统里面都是单规格商品，把关联的商品弄一个大码关联 -->
					<view v-if="showSimilarSpu && oldGoodsSpu.similarSpuList && oldGoodsSpu.similarSpuList.length > 1"
						class="similar-spu">
						<view v-show="goodsShow" class="bg-white">
							<view class="flex justify-between align-center text-black">
								<text class="text-bold font-xxxmd mt-xxsm mb-md">更多规格</text>
								<view class="flex font-xmd " @tap="viewTypeEdit">
									<text :class="' cuIcon-' + (viewType == 'list' ? 'list' : 'cascades')"></text>
									<text class="pl-xsm">{{ viewType == 'list' ? '列表' : '大图' }}</text>
								</view>
							</view>
							<view class="flex" :class="viewType == 'list' ? 'flex-wrap ma' : 'flex-direction'">
								<view v-for="(itemGood, i) in oldGoodsSpu.similarSpuList" :key="i"
									class="item-similar-spu mb-md flex align-center"
									:class="[itemGood.spuId == goodsSpu.id ? 'select' : '', viewType == 'list' ? 'flex-direction gridItem  mlr-xs' : 'pr-sm']"
									@click="changeOtherGoods(itemGood.spuId)">
									<image :class="viewType == 'list' ? 'listImg' : ''" :src="itemGood.picUrl"
										mode="aspectFill"></image>
									<view class="overflow-1 font-md ml-xxs"
										:class="viewType == 'list' ? 'mt-sm' : ''" style="flex: 1">
										{{ itemGood.name }}
									</view>
									<format-price style="min-width: 100rpx" signFontSize="20rpx" smallFontSize="24rpx"
										priceFontSize="30rpx" :price="itemGood.price"
										:color="itemGood.spuId == goodsSpu.id ? '#F85420' : '#000'" />
								</view>
							</view>
						</view>
					</view>
				</view>
			
				<!-- 立即购买展示信息：发票、备注、支付方式 -->
				<order ref="orderPay" :shopInfo="shopInfo" :goodsSpu="goodsSpu" :goodsSku="goodsSku"
					:modalSkuType="modalSkuType" :cartNum="cartNum" :goodsSpecData="selectshop"
					:invoiceRemark="invoiceRemark" :invoiceState="invoiceState" :selectedCouponIds="selectedCouponIds"
					:goodsShow="goodsShow" :deviverParams="deviverParams" :showDisable.sync="isOrderBtnShow"
					:AuthenticationType="needUserCertified" @getPayPrice="getPayPrice" @getCoupons="getCoupons"
					@getCouponPrice="getCouponPrice" @invoiceOpen="invoiceOpen" @changeModalCoupon="changeModalCoupon"
					@changeGoodsShow="changeGoodsShow" @changeQuoat="changeQuoat" @priceDiscount="priceDiscount"
					@showModal="showModal" @showTransfer="showTransfer" @changeInvoicing="changeInvoicing"
					:customerGoodsId="customerGoodsId" :customerId="customerId" @preOrder="handlePreOrder" />

			</scroll-view>

			<!-- 按钮 立即兑换、立即购买、确定 -->
			<view v-if="isModalShow && isTransferShow" class="bg-white foot border"
				style="position: absolute; bottom: 0; width: 100%; border-top: 1px solid rgba(0, 0, 0, 0.1); z-index: 2">
				<!-- 小计 立即支付 ｜ 立即兑换时展示 -->
				<view
					v-if="goodsSku.stock > 0 && goodsSpu.spuType != 3 && goodsSpu.spuType != 5 && modalSkuType == '2' && JSON.stringify(goodsSku) !== '{}'"
					class="text-bold padding-left-lg flex" style="line-height: 60rpx">
					<view class="text-black flex">
						小计
						<format-price signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="30rpx"
							:price="subtotalPrice" color="#000" />
					</view>
					<view v-if="reductionPrice" class="flex">
						，
						<view class="flex text-orange">
							共减
							<format-price signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="30rpx"
								:price="reductionPrice" color="#f85420" />
						</view>
					</view>
				</view>
				<view class="cu-bar tabbar">
					<view v-if="goodsSpu.spuType == 3" class="btn-group">
						<button class="cu-btn round shadow-blur lg" :class="'bg-' + theme.themeColor" style="width: 90%"
							@tap="toDo" data-type="2"
							:disabled="goodsSku.stock <= 0 || goodsSku.enable == '0' || quoatAsync" type>
							立即兑换
						</button>
					</view>
					<view v-else-if="goodsSpu.spuType == 5" class="btn-group">
						<button class="cu-btn round shadow-blur lg" :class="'bg-' + theme.themeColor" style="width: 90%"
							@tap="toDo" data-type="5"
							:disabled="goodsSku.stock <= 0 || goodsSku.enable == '0' || quoatAsync" type>
							立即兑换
						</button>
					</view>

					<view v-else-if="modalSkuType == ''" class="btn-group">
						<!--视频号不显示加入购物车 isShowShoppingCart 是否加购物车 0:否 1:是-->
						<button v-if="goodsSpu.isShowShoppingCart == 1"
							class="cu-btn bg-orange round shadow-blur m-sku-bn lg" @tap="toDo" data-type="1"
							:disabled="goodsSku.enable == '0'" type>
							加入购物车
						</button>

						<button id="toOrderDetail" class="cu-btn round shadow-blur m-sku-bn lg"
							:style="{ width: goodsSpu.isShowShoppingCart != 1 ? '90%' : '45%' }"
							:class="'bg-' + theme.themeColor" @tap="$noMultipleClicks(toDo, $event)" data-type="2"
							:disabled="goodsSku.stock <= 0 || goodsSku.enable == '0'" type>
							立即购买
						</button>
					</view>
					<view class="btn-group flex" v-else-if="modalSkuType != ''">
						<view class="cu-btn shadow-blur lg flex align-center"
							:class="'bg-' + theme.themeColor + (payOrCart ? ' disable' : '')"
							style="width: 90%; line-height: 80rpx; background: #ff5000; border-radius: 12rpx !important"
							@tap="$noMultipleClicks(wxTemplate, $event)" :disabled="payOrCart" type
							:data-type="modalSkuType">
							<text class="mr-xxxs text-bold">{{ payOrCartText }}</text>
							<format-price v-if="goodsSku.id && payOrCartText && payOrCartText == '立即支付'"
								styleProps="font-weight:900;" class="format-price"
								v-show="goodsSpu.priceDown != goodsSpu.priceUp" signFontSize="26rpx"
								smallFontSize="32rpx" priceFontSize="42rpx" :price="realPayPrice" color="#fff" />
						</view>
					</view>
				</view>
			</view>

			<!-- 发票信息 弹框 -->
			<make-invoice @close="invoiceClose" v-show="invoiceFlag" :spuType="goodsSpu.spuType" ref="invoice" />

			<!-- 选择优惠券 -->
			<view v-if="modalCoupon" :class="'cu-modal bottom-modal ' + modalCoupon" @tap.stop="modalCoupon = ''">
				<view class="cu-dialog bg-white" @tap.stop>
					<view class="text-lg text-center padding">优惠券</view>
					<scroll-view scroll-y scroll-with-animation style="height: 700rpx">
						<checkbox-group class="block" @change="checkboxChangeCoupon">
							<view class="cu-item padding flex flex-wrap justify-between"
								v-for="(item, index) in spuCoupons" :key="index">
								<coupon-user-info-platform class="basis-xl" :couponUserInfo="item"
									:toUse="false"></coupon-user-info-platform>
								<checkbox class="round red text-center vertical-center" :value="item.couponId"
									:disabled="$refs.orderPay.orderConfirmLoading || !!item.disabled"
									:checked="item.checked" />
							</view>
						</checkbox-group>
					</scroll-view>
					<view class="padding">
						<button class="cu-btn response lg" :class="'bg-' + theme.themeColor"
							@tap="modalCoupon = ''">确定</button>
					</view>
				</view>
			</view>

			<!-- 礼品卡密码提示|验密 -->
			<gift-dialog :modalPwdDialog="modalDialog" ref="giftPwdDialog"
				@giftOrderConfirm="giftOrderConfirm"></gift-dialog>

			<!-- 开通零钱使用组件 -->
			<modal :visible.sync="showphone"></modal>

			<modelchangge ref="modelchangge" :showchange="showchange" @changeModal="changeModal" :payPrice="payPrice">
			</modelchangge>
			<priceDialog v-if="showPriceDialog" @close="handlePriceDialog" :preOrder="preOrder" />
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import Modal from '@/components/getphonecode/getphonecode';
	import modelchangge from '../modelchangge/modelchangge';
	import baseStepper from 'components/base-stepper/index';
	import formatPrice from '@/components/format-price/index.vue';
	import deliveryWay from '@/components/goods-sku/delivery-way.vue';
	import api from 'utils/api';

	import giftDialog from '@/components/goods-sku/gift-dialog.vue';
	import price from 'utils/price-max-min';
	import order from 'components/order/index';
	const util = require('utils/util.js');
	import makeInvoice from '@/components/invoice/makeInvoice/index.vue';
	import priceDialog from '@/components/goods-sku/price-dialog.vue';
	import {
		getWxTemplate
	} from '@/api/message.js';
	import { senGoods, getCurrentTitle, senTrack } from '@/public/js_sdk/sensors/utils.js';

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				initFirst: true, //只初始化一次
				noClick: true,
				choiceAll: false, //是否选择全部
				difference: [], //sku列表
				shopItemInfo: {}, //存放要和选中的值进行匹配的数据
				selectArr: [], //存放被选中的值
				subIndex: [], //是否选中 因为不确定是多规格还是单规格，所以这里定义数组来判断
				selectshop: {}, //存放最后选中的商品
				oldGoodSku: {},
				oldGoodsSpu: {}, //更多版本里面使用，渲染第一次加载的商品里面的similarSpu,防止每次切换更多版本，渲染顺序发生变化，
				oldCartNum: 0,
				oldSpecInfo: '',
				payPrice: 0,
				availableExchangePrice: 0, // 可用积分抵扣金额
				couponPrice: 0,
				invoiceFlag: false, // 发票信息弹框
				invoiceState: false, // 开不开发票
				invoiceRemark: '', // 发票内容
				modalCoupon: '', // 选择优惠券弹框
				spuCoupons: [], // 优惠券数据
				selectedCouponIds: [], // 优惠券选中的数据
				goodsShow: true,
				quoatAsync: false, // 限购禁用支付按钮
				isIOS: '',
				isOrderBtnShow: false, // order订单中返回的disable
				showphone: false,
				showchange: false,
				getpice: 0,
				modalDialog: '', //礼品卡密码弹框
				giftPrice: null, //礼品卡金额
				subtotalPrice: 0, //小计，
				reductionPrice: 0, //共减
				isModalShow: true, //处理礼品卡在IOS里显示不全，没有遮罩
				isTransferShow: true, // 处理对公转账在IOS里显示不全，没有遮罩
				deviverParams: null,
				showPriceDialog: false, // 价格明细弹框
				preOrder: null,
				scrollTop: 0,
				viewType: 'cascades'
			};
		},
		components: {
			baseStepper,
			formatPrice,
			order,
			modelchangge,
			Modal,
			giftDialog,
			makeInvoice,
			deliveryWay,
			priceDialog
		},
		props: {
			shopInfo: {
				type: Object,
				default: () => {}
			},
			goodsSpu: {
				type: Object,
				default: () => {}
			},
			goodsSpecData: {
				type: Array,
				default: () => []
			},
			goodsSku: {
				type: Object,
				default: () => {}
			},
			modalSku: {
				type: Boolean,
				default: false
			},
			modalSkuType: {
				type: String,
				default: ''
			},
			cartNum: {
				type: Number,
				default: 1
			},
			shoppingCartId: {
				type: String,
				default: null
			},
			showSimilarSpu: {
				type: Boolean,
				default: false
			},
			needUserCertified: {
				type: String,
				default: ''
			},
			customerGoodsId: {
				type: String,
				default: ''
			},
			customerId: {
				type: String,
				default: ''
			}
		},
		watch: {
			goodsSku: {
				handler: function(newValue, oldValue) {
					if ((!oldValue || !(oldValue.id > 0)) && newValue && newValue.id > 0) {
						this.oldGoodSku = {
							...newValue
						};
					}
				},
				immediate: true //第一次刷新页面时就会执行
			},
			goodsSpu: {
				handler: function(newValue, oldValue) {
					if ((!oldValue || !(oldValue.id > 0)) && newValue && newValue.id > 0) {
						this.oldGoodsSpu = {
							...newValue
						};
					}
				},
				immediate: true //第一次刷新页面时就会执行
			},
			cartNum: {
				handler: function(newValue, oldValue) {
					if (!(oldValue > 0)) {
						this.oldCartNum = newValue;
					}
				},
				immediate: true //第一次刷新页面时就会执行
			},
			// 这个数据是最后请求的，所以这个有数据表示就能初始化规格数据了，initFirst：监控是否重复初始化数据
			goodsSpecData(val, oldVal) {
				if (val != oldVal && this.initFirst) {
					this.initSpecData();
				}
			}
		},
		computed: {
			addCartNum() {
				return Math.floor(Math.random() * (1000 - 20 + 1)) + 20;
			},
			addOrderNum() {
				return Math.floor(Math.random() * (1000 - 20 + 1)) + 90;
			},
			minBuyNum() {
				return this.goodsSku && this.goodsSku.minBuyNum > 0 ? this.goodsSku.minBuyNum : 1;
			},
			maxBuyNum() {
				// goodsSpu.newCustomerGoods.buyLimit == 1 ? minBuyNum > 1 ? minBuyNum : 1 : goodsSku.stock
				if (this.goodsSpu.newCustomerGoods && this.goodsSpu.newCustomerGoods.buyLimit == 1) {
					return this.minBuyNum > 1 ? this.minBuyNum : 1;
				} else {
					return this.goodsSku.stock;
				}
			},
			// 立即支付 | 确定 按钮 文案
			payOrCartText() {
				const {
					spuType
				} = this.goodsSpu;
				if (this.goodsSku.stock == 0) {
					return '暂时缺货';
				}
				if (spuType != 3 && spuType != 5 && this.modalSkuType == '2') {
					// 立即支付
					return `立即支付`;
				} else {
					// 添加购物车
					return '确定';
				}
			},

			realPayPrice() {
				const {
					spuType
				} = this.goodsSpu;
				if (spuType != 3 && spuType != 5 && this.modalSkuType == '2') {
					// 立即支付
					this.getpice = Number(this.payPrice).toFixed(2);
					return Number(this.payPrice).toFixed(2);
				} else if (spuType != 3 && spuType != 5 && this.modalSkuType == '2') {
					// 立即支付
					return this.payPrice || '';
				}
				return '';
			},

			// 立即支付 | 确定 按钮 disabled
			payOrCart() {
				if (this.modalSkuType == '2' && this.isOrderBtnShow) {
					return true;
				}
				if (this.modalSkuType == '2') {
					// 立即购买按钮
					if (this.goodsSku.stock <= 0) {
						// 是否有库存
						return true;
					}
					if (this.quoatAsync) {
						// 是否超过限购数量
						return true;
					}
				}
				if (this.goodsSku.enable == '0') {
					return true;
				}

				return false;
			},

			couponTags() {
				let res = [];
				// 从预下单接口遍历出来使用的券，显示在商品图片右侧
				if (this.preOrder && this.preOrder.spuCoupons && this.preOrder.spuCoupons.length > 0 && this.preOrder
					.availableSelectCoupons) {
					// 创建已选ID的Set以便快速查找
					const selectedIds = new Set(this.preOrder.availableSelectCoupons);
					// 过滤出ID存在于selectedIds中的券对象
					return this.preOrder.spuCoupons.filter((coupon) => selectedIds.has(coupon.couponId));
				}
			}
		},

		beforeDestroy() {
			this.$EventBus.$off('showchange');
		},

		created() {
			this.isIOS = util.isIOS();
			//礼品卡验密弹框关闭
			this.$EventBus.$on('gift-pwd-dialog', (v) => {
				this.modalDialog = '';
			});

			//获取礼品卡金额
			this.$EventBus.$on('gift-price-value', (v) => {
				this.giftPrice = v;
			});
			//接收显示短信零钱组件显示方法
			this.$EventBus.$on('Displayphone', (type) => {
				console.log('showphone', type);
				this.showphone = type;
			});
			// 点击选择金额显示弹框   false  、 true
			this.$EventBus.$on('showchange', (v) => {
				console.log('showchange', v.modalchange);
				this.showchange = v.modalchange;
			});
		},
		mounted() {},

		methods: {
			// 开不开发票
			changeInvoicing(bool, order) {
				console.log('bool,order===>', bool, order, this.$refs);
				if (this.invoiceState) {
					this.$refs?.invoice.onlineOrderPreInvoiceOpen(order);
				}
			},
			// 对公转账 IOS里显示不全，没有遮罩
			showTransfer(parameter) {
				this.isTransferShow = parameter;
			},
			//处理礼品卡在IOS里显示不全，没有遮罩
			showModal(parameter) {
				this.isModalShow = parameter;
				// this.$emit("showModal",this.isModalShow)
			},
			//礼品卡调用预下单
			giftOrderConfirm() {
				this.$refs?.orderPay.orderSub();
			},

			changeModal(value) {
				this.showchange = false;
			},
			changeOtherGoods(id) {
				this.$emit('changeOtherGoods', id);
				//切换更多版本，购买数量重置
				this.cartNum = 1;
				this.numChange(1);
			},
			// 预览图片
			handlePreviewImage(params) {
				if (typeof params === 'number') {
					uni.previewImage({
						urls: this.goodsSpu.picUrls,
						current: params
					});
				} else {
					const imgArr = [params];
					uni.previewImage({
						urls: imgArr,
						current: 0
					});
				}
			},
			initSpecData(goodsSpecData) {
				if (goodsSpecData) {
					if (this.goodsSpecData.length > 0) {
						this.goodsSpecData.splice(0, this.goodsSpecData.length);
					}
					goodsSpecData.map((item) => {
						this.goodsSpecData.push(item);
					});
				}
				if (this.goodsSpu && this.goodsSpu.skus) {
					//默认选择的sku，第一个库存大于0的
					const defaultSelectSku = this.goodsSpu.skus.find((spec) => spec.stock > 0);
					this.initFirst = false;
					// this.difference = [], //sku列表 {"id": "23","price": 500,"stock": 48,"difference": ["100","绿色","X","豪华"]}
					this.shopItemInfo = {}; //存放要和选中的值进行匹配的数据
					this.selectArr = []; //存放被选中的值
					if (defaultSelectSku && defaultSelectSku.specs && defaultSelectSku.specs.length > 0) {
						this.selectArr = defaultSelectSku.specs.map((spec) => spec.specValueId);
					}

					this.subIndex = []; //是否选中 因为不确定是多规格还是单规格，所以这里定义数组来判断
					//初始化数据
					if (this.goodsSpecData.length > 0) {
						this.goodsSpecData.map((item) => {
							item.diabled = false; //是否点击
							item.ishow = true; //是否显示，暂时无用
							let checkedLeaf = item.leaf.find((itemLeaf) => this.selectArr.indexOf(itemLeaf.id) > -
								1);
							item.checked = checkedLeaf ? checkedLeaf.id : '';
							this.subIndex.push(-1);
						});
					}
					let goodsSpecData = this.goodsSpecData;
					let oldSpecInfo = '';
					goodsSpecData.forEach((spec, index) => {
						spec.leaf.forEach((specItem) => {
							if (spec.checked == specItem.id) {
								oldSpecInfo = oldSpecInfo + specItem.value;
								if (goodsSpecData.length != index + 1) {
									oldSpecInfo = oldSpecInfo + ';';
								}
							}
						});
					});
					this.oldSpecInfo = oldSpecInfo;

					this.changeData(this.goodsSpu.skus);
					this.checkItem(); //计算sku里面规格形成路径
					this.checkInpath(-1); //传-1是为了不跳过循环
					//默认规格都选好之后，预下单
					this.handelChooseAllSpec();
				}
			},
			changeData(differenceList) {
				for (let item of differenceList) {
					let nowOneCompound = [];
					for (let oneCompound of item.specs) {
						nowOneCompound.push(oneCompound.specValueId);
					}
					item.difference = nowOneCompound;
				}
			},

			checkItem() {
				// 计算有多小种可选路径
				this.goodsSpu.skus.reduce(
					(arrs, items) => {
						return arrs.concat(
							items.difference.reduce(
								(arr, item) => {
									return arr.concat(
										arr.map((item2) => {
											//利用对象属性的唯一性实现二维数组去重
											if (!this.shopItemInfo.hasOwnProperty([...item2, item])) {
												this.shopItemInfo[[...item2, item]] = items;
											}
											return [...item2, item];
										})
									);
								},
								[
									[]
								]
							)
						);
					},
					[
						[]
					]
				);
			},

			checkInpath(clickIndex) {
				//循环所有属性判断哪些属性可选
				//当前选中的兄弟节点和已选中属性不需要循环
				for (let i = 0, len = this.goodsSpecData.length; i < len; i++) {
					if (i == clickIndex) {
						continue;
					}
					let len2 = this.goodsSpecData[i].leaf.length;
					for (let j = 0; j < len2; j++) {
						if (this.subIndex[i] != -1 && j == this.subIndex[i]) {
							continue;
						}
						let choosed_copy = [...this.selectArr];
						this.$set(choosed_copy, i, this.goodsSpecData[i].leaf[j].id);
						let choosed_copy2 = choosed_copy.filter((item) => item !== '' && typeof item !== 'undefined');
						if (this.shopItemInfo.hasOwnProperty(choosed_copy2)) {
							this.$set(this.goodsSpecData[i].leaf[j], 'ishow', true);
							this.$set(this.goodsSpecData[i].leaf[j], 'disabled', false);
						} else {
							this.$set(this.goodsSpecData[i].leaf[j], 'ishow', false);
							this.$set(this.goodsSpecData[i].leaf[j], 'disabled', true);
						}
					}
				}
			},

			// 选择规格
			skuClick(value, index1, event, index2) {
				// 已选择过的可以取消
				this.goodsSpecData[index1].checked = value.id;
				if (value.ishow) {
					//添加、替换、删除
					if (this.goodsSpecData.length > 1) {
						// 判断是否有选中的规格
						const falgSpecData0 = this.goodsSpecData[0].checked === '' && this.goodsSpecData[1].checked !== '';
						const falgSpecData1 = this.goodsSpecData[0].checked !== '' && this.goodsSpecData[1].checked === '';
						// 添加规格
						if (falgSpecData0 || falgSpecData1) {
							if (index1 === 1) this.$set(this.selectArr, 0, this.goodsSpecData[0].leaf[0].id);
							if (index1 === 0) this.$set(this.selectArr, 1, this.goodsSpecData[1].leaf[0].id);
							this.$set(this.selectArr, index1, value.id);
							this.$set(this.subIndex, 0, 0);
							this.$set(this.subIndex, 1, 0);
						}

						if (falgSpecData0) {
							// 添加第一个规则
							this.$set(this.goodsSpecData[0], 'checked', this.goodsSpecData[0].leaf[0].id);
						} else if (falgSpecData1) {
							// 添加第二个规则
							this.$set(this.goodsSpecData[1], 'checked', this.goodsSpecData[1].leaf[0].id);
						} else if (this.selectArr[index1] !== value.id) {
							// 替换
							this.$set(this.selectArr, index1, value.id);
							this.$set(this.subIndex, index1, index2);
						} else if (this.selectArr[index1] === value.id) {
							// 删除
							this.goodsSpecData[index1].checked = '';
							this.$set(this.selectArr, index1, '');
							this.$set(this.subIndex, index1, -1);
						}
					} else {
						if (this.selectArr[index1] !== value.id) {
							this.$set(this.selectArr, index1, value.id);
							this.$set(this.subIndex, index1, index2);
						} else if (this.selectArr[index1] === value.id) {
							this.goodsSpecData[index1].checked = '';
							this.$set(this.selectArr, index1, '');
							this.$set(this.subIndex, index1, -1);
						}
					}
					this.checkInpath(index1);
					this.handelChooseAllSpec();
				} else {
					this.$emit('changeGoodsSku', {});
					this.choiceAll = false;
				}
				this.$emit('changeSpec', this.goodsSpecData);
			},

			handelChooseAllSpec() {
				// 如果全部选完
				if (this.selectArr.every((item) => item != '')) {
					this.selectshop = this.shopItemInfo[this.selectArr];
					if (this.selectshop) {
						//是否有商品 sku
						if (this.selectshop.stock == 0) {
							this.$emit('numChange', 0);
						} else {
							// 如果已选择的数量大于库存数量，那么选择的数量直接使用最大库存数量
							if (this.cartNum > this.selectshop.stock) {
								uni.showToast({
									title: '您好，购买不数量不能大于库存',
									icon: 'none',
									duration: 2000
								});
								this.$emit('numChange', this.selectshop.stock);
							} else if (this.cartNum == 0) {
								uni.showToast({
									title: '您好，购买数量至少为1',
									icon: 'none',
									duration: 2000
								});
								this.$emit('numChange', 1);
							}
						}
						if (this.selectshop.minBuyNum > 0 && this.cartNum < this.selectshop.minBuyNum) {
							uni.showToast({
								title: '您好，购买数量至少为' + this.selectshop.minBuyNum,
								icon: 'none',
								duration: 2000
							});
							this.$emit('numChange', this.selectshop.minBuyNum);
						}
						this.$emit('changeGoodsSku', this.selectshop);
					} else {
						this.$emit('numChange', 1);
					}
					this.choiceAll = true;
				} else {
					this.$emit('changeGoodsSku', {});
					this.choiceAll = false;
				}
			},

			getinter(type) {
				if (type == '5') {
					return type;
				} else if (type == '3') {
					return 4;
				} else {
					return 2;
				}
			},

			touchMove() {
				return;
			},
			hideModalSku() {
				this.$emit('changeModalSku', false);
			},
			// 购买数量控件
			numChange(val) {
				this.$emit('numChange', val);
			},

			// 提交 积分兑换、添加购物车、立即购买
			async toDo(e) {
				let canDo = true;
				let that = this;
				try {
					this.goodsSpecData.forEach((spec) => {
						if (spec.leaf && spec.leaf.length > 0 && !spec.checked) {
							canDo = false;
							uni.showToast({
								title: '请选择' + spec.value,
								icon: 'none',
								duration: 2000
							});
							throw new Error();
						}
					});
				} catch (e) {}

				//如果选择的数量小于1,那么就不能进行购买操作，可以加入购物车
				if (this.cartNum < 1 && e.currentTarget.dataset.type != '1') {
					uni.showToast({
						title: '数量必须大于0',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				if (canDo) {
					let goodsSpu = this.goodsSpu;
					let goodsSku = this.goodsSku;
					let specInfo = '';
					let goodsSpecData = this.goodsSpecData;
					goodsSpecData.forEach((spec, index) => {
						spec.leaf.forEach((specItem) => {
							if (spec.checked == specItem.id) {
								specInfo = specInfo + specItem.value;
								if (goodsSpecData.length != index + 1) {
									specInfo = specInfo + ';';
								}
							}
						});
					});

					if (e.currentTarget.dataset.type == '1') {
						//加购物车
						if (this.shoppingCartId) {
							// this.goodsSpu.spuType
							const params = {
								id: this.shoppingCartId,
								spuId: goodsSpu.id,
								skuId: goodsSku.id,
								quantity: this.cartNum,
								addPrice: goodsSku.salesPrice,
								spuName: goodsSpu.name,
								specInfo: specInfo,
								picUrl: goodsSku.picUrl ? goodsSku.picUrl : goodsSpu.picUrls[0]
							};
							if (this.goodsSpu.spuType == 6) {
								params.type = 3;
							}
							api.shoppingCartUpdate(params).then((res) => {
								uni.showToast({
									title: '修改成功',
									duration: 5000
								});
								this.hideModalSku();
								this.$emit('operateCartEvent');
								const goodsSku = this.goodsSku;
							});
						} else {
							const params = {
								spuId: goodsSpu.id,
								skuId: goodsSku.id,
								quantity: this.cartNum,
								addPrice: goodsSku.salesPrice,
								shopId: goodsSpu.shopId,
								spuName: goodsSpu.name,
								specInfo: specInfo,
								picUrl: goodsSku.picUrl ? goodsSku.picUrl : goodsSpu.picUrls[0]
							};
							if (this.goodsSpu.spuType == 6) {
								params.type = 3;
							}
							api.shoppingCartAdd(params).then((res) => {
								this.hideModalSku();
								setTimeout(()=>{
									uni.showToast({
										title: '加购成功',
										duration: 2000,
										icon: 'none'
									});
								}, 500)
								this.$emit('operateCartEvent');
								this.handleSenGoods('GoodsAddToCart', {
									page_name: getCurrentTitle(0),
									sku_id: goodsSku.id,
									sku_name: specInfo
								});
							});
						}
					} else {
						
						//立即购买，前去确认订单
						/* 把参数信息异步存储到缓存当中 */
						const shoppingCarItemIds = {
							spuId: goodsSpu.id,
							skuId: goodsSku.id,
							quantity: this.cartNum,
							specInfo: specInfo
						};
						if (this.goodsSpu.spuType == 6) {
							shoppingCarItemIds.orderType = 7;
						}
						uni.setStorage({
							key: 'param-orderConfirm',
							data: {
								source: this.getinter(goodsSpu.spuType),
								shoppingCarItemIds: shoppingCarItemIds
							}
						});

						// 立即购买：点击选择显示弹框--立即购买
						if (e.target.id === 'toOrderDetail') {
							// 直接购买时，未领取优惠券则领取购买，领取优惠券则直接购买
							const apiCouponNameId = this.goodsSku.estimatedPriceVo?.apiCouponNameIdVO;
							let url = '/pages/order/order-confirm/index';
							let res = null;
							if (apiCouponNameId && !apiCouponNameId?.couponUserId) {
								const params = {
									couponId: apiCouponNameId.couponId,
									errModalHide: true // 不显示错误弹框
								};
								res = await api.couponUserSave(params);
							}
							if (res?.code === 0 && res?.data) {
								url = res.data.couponId ?
									`/pages/order/order-confirm/index?couponId=${res.data.couponId}` :
									'/pages/order/order-confirm/index';
							}
							setTimeout(() => {
								uni.navigateTo({
									url: url
								});
							}, 500);
						} else {
							// 商品详情页直接点击立即购买-弹框-确定-唤起支付
							// 直接购买唤起立即支付
							/**
							 * 礼品卡
							 * */
							// 1校验有没有设置密码 isActive 0：未开通 1：已开通支付密码
							// a 没有设置去设置
							// b 设置密码判断有没有设置免密 isPasswordless 0：未开通 1：已开通免密支付
							// b1 设置免密直接支付  passwordlessAmount  免密金额
							// b2 没有设置免密弹框输入密码
							let giftPassword = uni.getStorageSync('paypaymentuserpassword');
							if (this.giftPrice) {
								that.modalDialog = '';
								if (giftPassword) {
									if (giftPassword.isActive == '0') {
										uni.navigateTo({
											url: `/pages/gift/set-password/code/index`
										});
										return;
									}
									// 输入金额大于免密金额 去输入密码
									let deductPrice = price.numberCompare(this.giftPrice, giftPassword
										.passwordlessAmount);
									if (giftPassword.isPasswordless == '1' && deductPrice !== 'greater') {
										if (this.giftPrice != '0') {
											await that.$refs?.orderPay.orderConfirmDo();
										}
									} else {
										that.modalDialog = 'showDialog';
										that.$refs.giftPwdDialog.countdown();
										return;
									}
								}
							}
							this.$refs?.orderPay.orderSub();
						}
					}
					this.initFirst = true;
				}
			},
			
			// 数据
			handleSenGoods(type, params = {}) {
				const promotion_type_list = [];
				if (this.goodsSpu.estimatedPriceVo?.promotionsDiscountPrice > 0) {
					promotion_type_list.push('折扣');
				}
				if (this.goodsSpu.estimatedPriceVo?.coupon > 0 || this.goodsSpu.estimatedPriceVo?.discountPrice > 0) {
					promotion_type_list.push('优惠券');
				}
				senGoods(
					type,
					{
						forward_source: getCurrentTitle(1),
						source_module: decodeURIComponent(this.source_module) ,
						activity_id: this.activity_id,
						//商品原价划线价
						goods_crossed_price: Number(this.goodsSpu.estimatedPriceVo?.originalPrice),
						// 到手价
						goods_strike_price: Number(this.goodsSpu.estimatedPriceVo?.estimatedPrice),
						//优惠类型
						promotion_type_list,
						// 是否是推荐商品
						is_recommend_goods: false,
						// 是否描述 秒杀入口进来判断
						is_flash_sale: false,
					
						...params
					},
					this.goodsSpu
				);
			},

			// 获取支付价格
			getPayPrice(payPrice, availableExchangePrice) {
				this.payPrice = payPrice;
				this.availableExchangePrice = availableExchangePrice;
			},
			// 获取商品信息、获取优惠券
			getCoupons(spuCoupons) {
				this.spuCoupons = spuCoupons;
				const selectedCoupons = spuCoupons?.length ? spuCoupons.filter((item) => item.checked) : [];
				this.selectedCouponIds = selectedCoupons.length ? [selectedCoupons[0].couponId] : [];
			},
			// 获取优惠价格
			getCouponPrice(price) {
				this.couponPrice = price;
			},
			// 发票信息弹框
			invoiceOpen() {
				console.log('invoiceOpen', this.invoiceFlag);
				this.invoiceFlag = true;
				// 获取make-invoice组件的引用
				const makeInvoice = this.$refs.invoice;
				// 获取uni-popup组件的引用
				const uniPopup = makeInvoice.$refs.invoicePopup;
				// 调用uni-popup组件中的open方法
				uniPopup.open('bottom');
			},
			invoiceClose(val) {
				this.invoiceFlag = false;
				if (2 == val) {
					this.invoiceState = false;
				}
				if (3 == val) {
					this.invoiceState = true;
				}
			},

			// 选择优惠券弹框
			changeModalCoupon(param) {
				this.modalCoupon = param;
			},
			// 选中优惠券
			checkboxChangeCoupon(e) {
				this.selectedCouponIds = e.detail.value;
			},
			//
			changeGoodsShow(bool) {
				this.goodsShow = bool;
			},
			// 更新可用银行卡
			updataBankList() {
				this.$refs?.orderPay.updataBankList();
			},
			// 限购禁用支付按钮
			changeQuoat(params) {
				this.quoatAsync = params;
			},

			//小计0，共减 1
			priceDiscount(price) {
				this.subtotalPrice = price[0];
				this.reductionPrice = price[1];
			},
			wxTemplate(e) {
				// #ifdef MP
				getWxTemplate({
					type: e.currentTarget.dataset.type == '1' ? 9 : 1
				}).then((res) => {
					uni.requestSubscribeMessage({
						tmplIds: res.data,
						complete: () => {
							this.toDo(e);
						}
					});
				});
				// #endif
				// #ifndef MP
				this.toDo(e);
				// #endif
			},

			handleDeliveryType(data) {
				this.deviverParams = data;
			},

			handlePriceDialog() {
				this.showPriceDialog = !this.showPriceDialog;
			},

			handlePreOrder(preOrder) {
				this.preOrder = preOrder;
				// console.error("===this.preOrder===", this.preOrder);
			},

			handleScroll(e) {
				this.scrollTop = e.detail.scrollTop;
			},

			viewTypeEdit() {
				
				if (this.viewType == 'cascades') {
					this.viewType = 'list';
				} else {
					this.viewType = 'cascades';
				}
			}
		}
	};
</script>
<style lang="scss" scoped>
	.disable {
		opacity: 0.8;
		pointer-events: none;
	}

	.m-sku-bn {
		width: 45%;
	}

	.row-img {
		width: 170rpx !important;
		height: 170rpx !important;
		border-radius: 10rpx;
	}

	.dialo-sku {
		height: 65%;
	}

	.cu-modal {
		text-align: unset;
	}

	.close-icon {
		position: absolute;
		right: 20rpx;
		top: 20rpx;
	}

	.format-price {
		display: block !important;
	}
</style>
<style lang="scss" scoped>
	.showDialog {
		z-index: 99999;
		position: absolute;
		left: 0px;
		top: 0px;
		bottom: 0px;
		right: 0px;
		background: rgba(0, 0, 0, 0.6);
	}

	.gift-price {
		width: 669rpx;
		height: 220rpx;
		background: #f8f8f8;
		border-radius: 23rpx;
		margin: 0 auto;
		padding: 15rpx;
		margin-bottom: 40rpx;
	}

	.search-content {
		line-height: 64upx;
		height: 64upx;
		font-size: 24upx;
		color: #000000;
		flex: 1;
		display: flex;
		align-items: center;
		margin: 0 30upx;
	}

	.search-content input {
		flex: 1;
		padding-right: 30upx;
		height: 64upx;
		line-height: 64upx;
		font-size: 30upx;
	}

	.gift-line {
		width: 95%;
		height: 2upx;
		margin: auto;
		/**background: linear-gradient(to right, #999999, #999999 7.5upx, transparent 7.5upx, transparent);*/
		background: radial-gradient(#999999, #999999 2px, transparent 2px, transparent);
		background-size: 10upx 100%;
	}

	// 发票信息
	.invoice-remark {
		width: 100%;
		border-radius: 20rpx;
		border: 2rpx solid #e6dede;
		margin: 0 auto;
		display: block;
	}

	.similar-spu {
		image {
			width: 60rpx;
			height: 60rpx;
		}

		.item-similar-spu {
			background-color: #f4f4f4;
			border-radius: 10rpx;
			color: black;
		}

		.select {
			background-color: #fef0f0;
			color: #fe5002;
			border: solid 1px #fe5002;
		}

		.listImg {
			width: 100%;
			height: 214rpx;
		}

		.gridItem {
			width: 31%;
		}
	}

	.avatar-icon {
		width: 34rpx;
		height: 34rpx;
		border-radius: 50%;
		margin-left: -4rpx;
	}
</style>