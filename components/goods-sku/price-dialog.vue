<template>
	<!-- 价格明细弹框 -->
	<view class="p-lg font-lg">
		<!-- 弹框遮罩层 -->
		<view v-if="showPopup" class="popup-mask" @tap.stop="closePopup"></view>
		<!-- 弹框内容 -->
		<view class="popup-container" :class="{ 'popup-show': showPopup }">
			<view class="cu-bar bg-white justify-end solid-bottom">
				<view style="flex: 1; text-align: center;" class="text-black text-bold font-xxl">价格明细</view>
				<view class="action" @click="closePopup">
					<text class="cuIcon-close text-black font-xxxl"></text>
				</view>
			</view>

			<scroll-view scroll-y class="popup-content">
				<view v-if="preOrder" class="mlr-lg">
					<view class="flex justify-between align-center mt-xxxxl">
						<view class="flex align-baseline">
							<text class="text-black text-bold">商品总价</text>
							<text class="text-gray ml-xsm font-md">共{{ goodsNum }}件宝贝</text>
						</view>
						<format-price styleProps="font-weight:900;" color="#000" signFontSize="24rpx" smallFontSize="32rpx" priceFontSize="40rpx" :price="preOrder.originalPrice" />
					</view>

					<view v-if="preOrder.shopList[0] && preOrder.shopList[0].freight > 0" class="flex justify-between align-center mt-xxxxl">
						<view class="flex align-center">
							<text class="text-black text-bold">运费</text>
						</view>
						<view class="flex align-center">
							<format-price styleProps="font-weight:900;" color="#000" signFontSize="24rpx" smallFontSize="32rpx" priceFontSize="40rpx" :price="preOrder.shopList[0].freight" />
						</view>
					</view>

					<view v-if="preOrder.reductionPrice > 0" class="flex justify-between align-center mt-xxxxl">
						<view class="flex align-center">
							<text class="text-black text-bold">共减</text>
						</view>
						<view class="flex align-center">
							<text class="text-orange text-bold">-</text>
							<format-price styleProps="font-weight:900;" color="#F85420" signFontSize="24rpx" smallFontSize="32rpx" priceFontSize="40rpx" :price="preOrder.reductionPrice" />
						</view>
					</view>

					<view v-if="preOrder.promotionsPrice > 0 " class="flex justify-between align-center mt-xxxxl">
						<view class="flex align-center">
							<text class="text-black">折扣金额</text>
						</view>
						<view class="flex align-center">
							<text class="text-orange text-bold">-</text>
							<format-price styleProps="font-weight:900;"  color="#F85420" signFontSize="24rpx" smallFontSize="32rpx" priceFontSize="40rpx" :price="preOrder.promotionsPrice" />
						</view>
					</view>

					<view v-if="preOrder.discountPrice > 0" class="flex justify-between align-center mt-xxxxl">
						<view class="flex align-center">
							<text class="text-black">平台券</text>
						</view>
						<view class="flex align-center">
							<text class="text-orange text-bold">-</text>
							<format-price styleProps="font-weight:900;" color="#F85420" signFontSize="24rpx" smallFontSize="32rpx" priceFontSize="40rpx" :price="preOrder.discountPrice" />
						</view>
					</view>
					<view v-if="preOrder.availableExchangePrice > 0 && preOrder.isOpenPoint==1" class="flex justify-between align-center mt-xxxxl">
						<view class="flex align-center">
							<text class="text-black">积分抵扣</text>
						</view>
						<view class="flex flex-direction align-end">
							<view class="flex align-center">
								<text class="text-orange text-bold">-</text>
								<format-price styleProps="font-weight:900;" color="#F85420" signFontSize="24rpx" smallFontSize="32rpx" priceFontSize="40rpx" :price="preOrder.availableExchangePrice" />
							</view>
							<view class="text-gray text-sm-px">({{ preOrder.availableDeductionPoint }}积分)</view>
						</view>
					</view>

					<view v-if="preOrder.giftDeductPrice > 0" class="flex justify-between align-center mt-xxxxl">
						<view class="flex align-center">
							<text class="text-black">礼品卡抵扣</text>
						</view>
						<view class="flex align-center">
							<text class="text-orange text-bold">-</text>
							<format-price styleProps="font-weight:900;" color="#F85420" signFontSize="24rpx" smallFontSize="32rpx" priceFontSize="40rpx" :price="preOrder.giftDeductPrice" />
						</view>
					</view>

					<view class="flex justify-between align-center mt-xxxxl">
						<view class="flex align-center">
							<text class="text-black text-bold">合计</text>
						</view>
						<format-price styleProps="font-weight:900;" color="#000" signFontSize="24rpx" smallFontSize="32rpx" priceFontSize="40rpx" :price="preOrder.totalPrice" />
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import api from 'utils/api';
import formatPrice from '@/components/format-price/index.vue';
export default {
	components: {
		formatPrice
	},
	props: {
		preOrder: {
			type: Object,
			default: () => {}
		}
	},

	data() {
		return {
			showPopup: true
		};
	},

	computed: {
		goodsNum() {
			let num = 0;
			if (this.preOrder && this.preOrder.spuVosCache && this.preOrder.spuVosCache.length) {
				this.preOrder.spuVosCache.forEach((item) => {
					num = num + item.quantity;
				});
			}
			return num;
		}
	},

	methods: {
		closePopup() {
			this.$emit('close');
		}
	}
};
</script>

<style lang="scss" scoped>
.popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 200rpx;
	background: rgba(0, 0, 0, 0.6);
	z-index: 999;
}

.popup-container {
	position: fixed;
	left: 0;
	right: 0;
	bottom: calc(166rpx + env(safe-area-inset-bottom) / 2);
	height: 65vh;
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	transition: all 0.3s ease;
	z-index: 1000;
}

.popup-show {
	// transform: translateY(-100%);
}

.popup-content {
	height: calc(65vh- 100rpx);
}
</style>
