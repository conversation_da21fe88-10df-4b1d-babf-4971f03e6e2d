<template>
	<view>
		<!-- 礼品卡验密提示弹框 -->
		<view :class="modalDialog" v-if="modalDialog">
			<view :class="modalDialog === 'showDialog' ? '' : 'cu-dialog bg-white'" @tap.stop>
				<view style="margin: 0rpx 87rpx 0 87rpx; background-color: #ffffff; border-radius: 20rpx">
					<view style="display: flex; flex-direction: column; align-items: center">
						<image class="margin-top-xl margin-bottom" style="width: 261upx; height: 297upx" mode="aspectFill" :src="$imgUrl('gift/secure.png')"></image>

						<view class="text-center">
							<view class="text-xsm text-black text-center">为保障您的资产安全</view>
							<view class="text-xsm text-black margin-bottom">请您进行虚拟资产验密</view>

							<view class="margin">
								<button
									@click="confirmModalDialog()"
									style="width: 317rpx; background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%); color: #fff"
									class="cu-btn round"
								>
									确定({{ msgTime }})
								</button>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 礼品卡验密弹框 -->
		<view v-if="modalGiftPwd" :class="'cu-modal bottom-modal ' + modalGiftPwd" @tap.stop="hideModalGiftPwd()">
			<view class="cu-dialog bg-white-black" @tap.stop :style="{ height: giftPwdHeight }">
				<view @tap="navTo(1)" class="flex padding-top-xs margin margin-bottom-xl margin-top-xl">
					<view class="cuIcon-back line-light-grey"></view>
					<view class="text-xsm text-center" style="width: 100%">输入礼品卡支付密码</view>
				</view>

				<view class="padding-top">
					<code-page @change="code($event)" :passwordShow="true" />
				</view>

				<view @tap="navTo(2)" class="flex justify-end margin margin-top-lg">
					<view class="text-xsm line-light-grey">忘记密码</view>
					<view class="cuIcon-right line-light-grey"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { passwordCheck } from '@/api/gift';
import codePage from '@/components/verification-code/index.vue';

const MSGTIME = 3;

export default {
	data() {
		return {
			giftPwd: '', //礼品卡支付密码
			modalDialog: this.modalPwdDialog, //知道了弹框
			msgTime: MSGTIME, //知道了弹框倒计时
			modalGiftPwd: '', //礼品卡验密弹框
			time: null // 定时器名称
		};
	},

	components: {
		codePage
	},

	props: {
		// 验密提示框
		modalPwdDialog: {
			type: String,
			default: ''
		},
		//密码弹框高度
		giftPwdHeight: {
			type: String,
			default: '75%'
		}
	},

	watch: {
		modalPwdDialog(newValue, oldValue) {
			console.log('modalPwdDialog===>', newValue, oldValue);
			if (newValue == 'showDialog') {
				this.modalDialog = newValue;
			}
		}
	},

	computed: {},

	created() {},

	mounted() {},

	methods: {
		//倒计时
		countdown() {
			let that = this;
			this.msgTime = 3;
			clearInterval(this.time);
			this.time = setInterval(() => {
				that.msgTime--;
				if (that.msgTime == 0) {
					that.msgTime = '';
					that.modalDialog = '';
					that.modalGiftPwd = 'show';
					// this.$EventBus.$emit('gift-pwd-dialog');
					console.log('打开输入密码');
					clearInterval(this.time);
					return;
				}
			}, 1000);
		},

		//关闭验密提示框
		confirmModalDialog() {
			this.msgTime = '';
			this.modalDialog = '';
			this.modalGiftPwd = 'show';
			this.$EventBus.$emit('gift-pwd-dialog', true);
		},

		// 关闭密码支付弹框
		hideModalGiftPwd() {
			this.modalGiftPwd = '';
			this.giftPwd = '';
			this.$EventBus.$emit('gift-pwd-value', this.giftPwd);
			this.$EventBus.$emit('gift-pwd-dialog', false);
		},

		//礼品卡忘记密码
		navTo(num) {
			// 2是忘记 1是关闭清空
			if (num != 1) {
				uni.setStorageSync('isPassword', false);
				uni.navigateTo({
					url: `/pages/gift/set-password/code/index?password=${num}`
				});
				return;
			}
			this.hideModalGiftPwd();
		},

		//礼品卡密码验密
		async code(val) {
			console.log('val', val);
			let that = this;
			if (val) {
				passwordCheck(Object.assign({ password: val }))
					.then((res) => {
						console.log('setPassword=>', res);
						if (res.code == 0) {
							that.giftPwd = val;
							that.$EventBus.$emit('gift-pwd-value', that.giftPwd);
							that.$EventBus.$emit('gift-pwd-dialog', false);
							that.modalGiftPwd = '';
							// that.$nextTick(()=>{
							// that.$refs.orderPay.orderConfirmDo()
							that.$emit('giftOrderConfirm');
							// })
						}
					})
					.catch((e) => {
						console.log('ee', e);
					});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.showDialog {
	z-index: 99999;
	position: absolute;
	left: 0px;
	top: 0px;
	bottom: 0px;
	right: 0px;
	background: rgba(0, 0, 0, 0.6);
}
.gift-price {
	width: 669rpx;
	height: 220rpx;
	background: #f8f8f8;
	border-radius: 23rpx;
	margin: 0 auto;
	padding: 15rpx;
	margin-bottom: 40rpx;
}
.search-content-price {
	font-weight: 700;
	font-size: 50upx;
}
.search-content {
	line-height: 64upx;
	height: 64upx;
	font-size: 30upx;
	color: #000000;
	flex: 1;
	display: flex;
	align-items: center;
	margin: 0 30upx;
}

.search-content input {
	flex: 1;
	padding-right: 30upx;
	height: 64upx;
	line-height: 64upx;
	font-size: 50upx;
	text-align: left;
	font-weight: 700;
}
.gift-line {
	width: 95%;
	height: 2upx;
	margin: auto;
	/**background: linear-gradient(to right, #999999, #999999 7.5upx, transparent 7.5upx, transparent);*/
	background: radial-gradient(#999999, #999999 2px, transparent 2px, transparent);
	background-size: 10upx 100%;
}
</style>
