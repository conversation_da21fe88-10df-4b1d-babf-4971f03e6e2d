<template>
  <view>
    <!-- 导航栏 -->
    <view class="cu-bar bg-white solid-bottom plr-lg ptb-xl">
      <view class="text-black text-bold font-lg">常用地址</view>
      <view class="flex align-center">
        <view class="text-dark-yellow font-lg" @tap="toggleManage">
          {{ isManaging ? "完成" : "管理" }}
        </view>
        <view class="text-dark-yellow ml-xxxxl font-lg" @tap="addAddress"
          >新增</view
        >
      </view>
    </view>

    <!-- 地址列表 -->
    <view class="cu-list menu" v-if="addressList && addressList.length > 0">
      <view
        v-for="(item, index) in addressList"
        :key="item.id"
        class="padding flex justify-between border-item"
        :class="[
          item.selected ? 'bg-light-orange text-dark-yellow ' : '',
          isManaging ? 'flex-direction' : 'align-center',
        ]"
        @tap.stop="selectAddress(index)"
      >
        <view>
          <view
            class="font-xxxxsm"
            :class="[item.selected ? '' : 'text-light-gray']"
            >{{ item.provinceName }} {{ item.cityName }}
            {{ item.countyName }}</view
          >
          <view
            class="font-xxxmd text-bold mtb-ss"
            :class="[item.selected ? '' : 'text-black']"
            >{{ item.detailInfo }}</view
          >
          <view
            class="font-xxxxsm"
            :class="[item.selected ? '' : 'text-black']"
          >
            {{ item.userName }} {{ item.telNum }}
            <text
              class="ml-xxxsm plr-sm ptb-s"
              style="background: #ffe9dc; border-radius: 8rpx; color: #b96d47"
              v-if="item.isDefault === '1'"
              >默认</text
            >
            <text
              class="plr-sm ml-xxxsm ptb-s"
              style="background: #ffe9dc; border-radius: 8rpx; color: #b96d47"
              v-if="index == 0"
              >最近使用</text
            >
          </view>
        </view>

        <!-- 普通模式下的编辑按钮 -->
        <view
          v-if="!isManaging"
          class="action margin-left"
          @tap.stop="editAddress(index)"
        >
          <!-- <text class="cuIcon-edit font-xxxl text-darkgrey"></text> -->
          <image
            class="size-xxl"
            :src="$imgUrl('live/goods/ic-edit.png')"
          ></image>
        </view>

        <!-- 管理模式下的操作 -->
        <view v-if="isManaging" class="action flex align-center">
          <radio-group @change="setDefault(index)">
            <label class="flex align-center">
              <radio
                class="brown"
                style="transform: scale(0.7)"
                value="1"
                :checked="item.isDefault === '1'"
              />
              <view
                class="margin-left-sm text-xsm-px text-black"
                style="flex: 1"
                >设为默认</view
              >
            </label>
          </radio-group>
          <view
            class="text-dark-yellow text-df-px text-dark-yellow margin-left-lg"
            @tap.stop="deleteAddress(index)"
            >删除</view
          >
        </view>
      </view>
    </view>
    <view v-else class="margin-top text-center text-df-px"
      >暂无可选收货地址，请先新增</view
    >
  </view>
</template>

<script>
import api from "utils/api";
export default {
  data() {
    return {
      isManaging: false,
      addressList: [],
    };
  },
  created() {
    this.userAddressPage();
  },
  methods: {
    userAddressPage() {
      api
        .userAddressPage({
          current: 1,
          size: 40,
          ascs: "",
          //升序字段
          descs: "",
        })
        .then((res) => {
          this.addressList = res.data.records;
          if (this.addressList && this.addressList.length > 0) {
            this.addressList.forEach((item, i) => {
              this.addressList[i].selected = i === 0;
            });
            this.$emit("selectAddress", this.addressList[0], "user");
          }
        });
    },

    toggleManage() {
      this.isManaging = !this.isManaging;
    },

    selectAddress(index) {
      this.addressList.forEach((item, i) => {
        this.$set(this.addressList, i, {
          ...this.addressList[i],
          selected: i === index,
        });
      });
      this.$emit("selectAddress", this.addressList[index], "user");
    },

    //重新设置默认
    async setDefault(index) {
      try {
        await api.userAddressSave({
          ...this.addressList[index],
          isDefault: "1",
        });
        this.addressList.forEach((item) => (item.isDefault = "0"));
        this.addressList[index].isDefault = "1";
      } catch (e) {}
    },

    deleteAddress(index) {
      uni.showModal({
        title: "提示",
        content: "确定要删除该地址吗？",
        success: async (res) => {
          if (res.confirm) {
            api.userAddressDel(this.addressList[index].id).then((res) => {
              this.addressList.splice(index, 1);
            });
          }
        },
      });
    },

    addAddress() {
      // 跳转到新增地址页面
      uni.setStorage({
        key: "param-userAddressForm",
        data: [],
      });
      // isNotify 编辑新增地址页面是否需要发出更新通知
      uni.navigateTo({
        url: "/pages/user/user-address/form/index?isNotify=1",
      });
      this.handleUpdate();
    },

    editAddress(index) {
      // 跳转到编辑地址页面
      uni.setStorage({
        key: "param-userAddressForm",
        data: this.addressList[index],
      });
      uni.navigateTo({
        url: "/pages/user/user-address/form/index?isNotify=1",
      });
      this.handleUpdate();
    },

    handleUpdate() {
      this.$EventBus.$on("updateAddress", () => {
        //需要执行的代码
        this.userAddressPage();
      });
    },

    destroyed() {
      console.log("destroyed: 组件销毁后");
      this.$EventBus.$off("updateAddress");
      s;
    },
  },
};
</script>

<style scoped>
.cu-tag:not([class*="bg"]):not([class*="line"]) {
  background-color: #b96d47;
}
.border-item {
  border-bottom: solid 1rpx #f6f6f6;
}
</style>
