<template>
	<view class="goods-container flex">
		<scroll-view scroll-y="true" :style="{ height: vipUser ? heightvip + 'px' : '' }" class="goods-container flex" enable-flex="true">
			<view class="goods-container flex flex-sub">
				<view class="activerow" v-if="ShopInfos.length > 0">
					<view style="width: 100%">
						<div-goods-row v-for="(items, inde) of ShopInfos" :value="items" :key="inde"></div-goods-row>
					</view>
				</view>
				<view style="width: 50%" v-for="(item, index) in goodsList" :key="index">
					<view style="width: 100%">
						<view
							:style="{
								padding: `${goodsSpace * multipleView}rpx`
							}"
						>
							<view
								class="goods-box"
								:style="{
									borderTopLeftRadius: `${borderRadiusLeftTop * multipleView}rpx`,
									borderTopRightRadius: `${borderRadiusRightTop * multipleView}rpx`
								}"
							>
								<view
									hover-class="none"
									style="background-color: #ffffff"
									:style="{ 'margin-bottom': item.shopName ? '' : '37rpx' }"
									@click="toPage('/pages/goods/goods-detail/index?id=' + item.id, 'goodDetail', item, index+1, searchKey, searchType, encodeURIComponent('商品卡片'))"
								>
									<view
										class="img-box"
										:class="
											((item &&
												item.estimatedPriceVo &&
												item.estimatedPriceVo.discountPrice &&
												item.estimatedPriceVo.discountPrice != '0.0' &&
												item.estimatedPriceVo.discountPrice != '0') ||
												(item &&
													item.estimatedPriceVo &&
													item.estimatedPriceVo.promotionsDiscountPrice &&
													item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
													item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
												(item &&
													item.estimatedPriceVo &&
													item.estimatedPriceVo.coupon &&
													item.estimatedPriceVo.coupon != '0.0' &&
													item.estimatedPriceVo.coupon != '0')) &&
											spuPriceStyle == 2
												? 'img-border'
												: ''
										"
										:style="{
											borderColor
										}"
									>
										<image lazy-load :lazy-load-margin="0" :src="item.picUrls[0] | formatImg360" mode="aspectFill" height="362"></image>
										<member-icon v-if="item.memberLevelLimit && item.memberLevelLimit != '101'" :memberLevelLimit="item.memberLevelLimit"></member-icon>
										<view class="markUrlsList" v-if="item.goodsMarkInfoVo && item.goodsMarkInfoVo.angleMarkUrl && item.goodsMarkInfoVo.angleMarkUrl.length > 0">
											<image
												mode="widthFix"
												class="markImgbox"
												v-for="(marItem, index) in item.goodsMarkInfoVo.angleMarkUrl"
												:key="index"
												:src="marItem"
											></image>
										</view>
										<view
											style="position: absolute; bottom: 0"
											:style="{
												top: spuPriceStyle == 1 ? 0 : 'auto',
												height: spuPriceStyle == 1 ? '1' : '77rpx',
												left: spuPriceStyle == 1 ? 0 : '-6rpx',
												right: spuPriceStyle == 1 ? 0 : '-6rpx'
											}"
											v-if="
												(item &&
													item.estimatedPriceVo &&
													item.estimatedPriceVo.discountPrice &&
													item.estimatedPriceVo.discountPrice != '0.0' &&
													item.estimatedPriceVo.discountPrice != '0') ||
												(item &&
													item.estimatedPriceVo &&
													item.estimatedPriceVo.promotionsDiscountPrice &&
													item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
													item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
												(item &&
													item.estimatedPriceVo &&
													item.estimatedPriceVo.coupon &&
													item.estimatedPriceVo.coupon != '0.0' &&
													item.estimatedPriceVo.coupon != '0')
											"
										>
											<good-price :goodsSpu="item" :small="false" :customCoverImage="customCoverImage"></good-price>
										</view>
										<view class="sell-out" v-if="item.inventoryState == 1">
											<view class="sell-out-text">售罄</view>
										</view>
									</view>
									<view class="text-black text-df margin-top-xs padding-lr-sm flow-1">
										{{ item.name }}
									</view>
									<view class="applyScope">
										<class-label
											v-for="(item, index1) in useScope"
											:key="index1"
											:text="item.text"
											value="lightDarkGrey"
											:marginRight="8"
											:marginBottom="0"
											:padding="[4, 12]"
											:borderRadius="24"
										></class-label>
									</view>

									<!-- 积分商品 -->
									<view v-if="item.spuType == 3 || item.spuType == 5" class="margin-top-xs margin-left-sm">
										<text class="text-gray text-xs">积分：</text>
										<text class="text-red text-lg text-bold">{{ item.goodsPoints ? item.goodsPoints : 0 }}</text>
									</view>
									<!-- 付费商品 -->
									<view v-else class="flex margin-top-xs align-baseline">
										<view class="text-bold text-red text-lg padding-lr-sm" style="padding-right: 0">
											<price-handle :value="item.priceDown" signFont="24rpx" bigFont="32rpx" smallFont="24rpx">
												<text
													slot="append"
													v-if="item.estimatedPriceVo && item.priceDown != item.estimatedPriceVo.price"
													class="text-red text-xs"
													style="font-weight: 500; padding-left: 6rpx"
												>
													劵后价
												</text>
											</price-handle>
										</view>

										<text
											v-if="item.estimatedPriceVo && item.estimatedPriceVo.originalPrice != '0.0' && item.priceDown != item.estimatedPriceVo.originalPrice"
											class="price-original text-xss"
										>
											￥{{ item.estimatedPriceVo.originalPrice }}
										</text>
										<view class="cu-tag bg-red radius sm" v-if="item.freightTemplat && item.freightTemplat.type == '2'">包邮</view>
										<view class="text-gray text-thin text-xss padding-lr-sm">已售{{ item.saleNum | saleNumFilter }}</view>

										<!--悬浮购物车 -->
										<view v-if="showCart" @tap.stop="addShopCar($event, item, 9)" class="round buy text-xl" :class="'bg-' + theme.themeColor + '-border'">
											<text class="cuIcon-cart text-white"></text>
										</view>
									</view>
									<block v-if="item.goodsMarkInfoVo && item.goodsMarkInfoVo.length">
										<view class="btmLabelCon">
											<template v-if="item.goodsMarkInfoVo">
												<class-label v-if="item.goodsMarkInfoVo.selfSupport == 1" text="松鼠自营" value="zy" :marginRight="10"></class-label>
											</template>
											<template v-if="item.goodsMarkInfoVo">
												<block v-for="(tagsList, indexTagsList) in item.goodsMarkInfoVo.tags" :key="indexTagsList">
													<class-label
														v-for="(tags, indexTags) in tagsList"
														:key="indexTags"
														:text="tags.name"
														:value="tags.type"
														:price="tags.price"
														:marginRight="10"
													></class-label>
												</block>
											</template>
											<!-- <class-label v-for="(item,index_) in btmLabel" :key='index_' :text='item.text' :value='item.value' :marginRight='8' :padding="[4,6]"></class-label> -->
										</view>
									</block>
								</view>

								<view class="titleBox" @click.stop="getshop(item)" v-if="item.shopName" style="margin-bottom: 12rpx; display: flex">
									<view style="display: flex; color: #757575; align-items: center">
										<view class="text-black text-df margin-top-xs padding-lr-sm flow-1">
											{{ item.shopName }}
										</view>
										<view class="" style="font-size: 25rpx; padding-top: 12rpx; padding-right: 14rpx">
											<text class="cuIcon-right text-sm"></text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 加入购物车动画 cartx 和 carty 是购物车位置在屏幕位置的比例 例如左上角x0.1 y0.1 右下角 x0.9 y0.9-->
		<shopCarAnimation @caetAnimation="caetAnimation" ref="carAnmation" cartx="0.9" carty="0.9"></shopCarAnimation>
	</view>
</template>

<script>
const app = getApp();
import divGoodsRow from '@/components/div-components/div-goods-row/div-goods-row.vue';
import memberIcon from '@/components/member-icon/index.vue';
import lazyLoad from 'components/lazy-load/index';
import goodPrice from 'components/good-price/good-price.vue';
import shopCarAnimation from 'components/cart/fly-in-cart.vue';
import api from 'utils/api';
import { mapState } from 'vuex';

import { navigateUtil } from '../../static/mixins/navigateUtil.js';
export default {
	mixins: [navigateUtil],
	data() {
		return {
			ShopInfos: this.otherShopInfos,
			useScope: [
				// {
				// 	text:'任何肤质',
				// },
				// {
				// 	text:'爽肤水',
				// },
				// {
				// 	text:'补水',
				// }
			],
			marks: ['http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/list_price_bg2.png', 'http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/list_price_bg2.png'],
			theme: app.globalData.theme, //全局颜色变量
			heightvip: '',
			isShake: true,
			cartCount: 0
		};
	},

	components: {
		lazyLoad,
		goodPrice,
		shopCarAnimation,
		divGoodsRow,
		memberIcon
	},

	props: {
		goodsList: {
			type: Array,
			default: () => []
		},
		goodsSpace: {
			type: String | Number,
			default: 4
		},

		vipUser: {
			typeof: Boolean,
			default: false
		},
		otherShopInfos: {
			type: Array,
			default: () => []
		},
		//是否显示购物车
		showCart: {
			type: Boolean,
			default: false
		},
		//商品添加购物车类型 ""||1正常商品 2 团购 3 礼品卡
		cartType: {
			type: String | Number,
			default: ''
		},
		customCoverImage: {
			type: String | Number,
			default: ''
		},

		borderRadiusLeftTop: {
			type: String | Number,
			default: 12
		},

		borderRadiusRightTop: {
			type: String | Number,
			default: 12
		},
		searchType: {
			type: String,
			default: ''
		},
		searchKey: {
			type: String,
			default: ''
		}
	},

	computed: {
		...mapState(['isPhone', 'multipleView']),
		borderColor: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.spuPriceStyle == 1 ? '' : customFiled.spuPicBorder;
			}
			return '';
		},
		spuPriceColor: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				if (customFiled.spuPriceStyle == 1) {
					return customFiled.spuPriceColor1 || '#fff';
				}
				return customFiled.spuPriceColor2 || '#fff';
			}
			return '#fff';
		},

		spuPriceStyle: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.spuPriceStyle || 2;
			}
			return 2;
		}
	},
	methods: {
		getshop(item) {
			uni.navigateTo({
				url: '/pages/shop/shop-detail/index?id=' + item.shopId
			});
		},
		// 加入购物车
		addShopCar(e, i, n) {
			console.log('加入购物车', e, i, n);
			// 成功的话，调用加入购物车动画 e 事件  i 商品  n 点击购物车的X坐标
			this.$refs.carAnmation.touchOnGoods(e, i.picUrls[0], n);
			this.defaultAdd(i.id, i);
		},
		//购物车摇
		caetAnimation(isShake) {
			console.log('=====isShake====', isShake);
			this.isShake = isShake;
		},

		onCaetAnimation() {
			this.$emit('onCaetAnimation', 'shake', this.cartCount);
		},

		//获取商品详情
		defaultAdd(id,i) {
			// type 3 是礼品卡
			let data = {
				spuId: id
			};
			if (this.cartType) {
				data.type = this.cartType;
			}
			api.defaultAdd(Object.assign({}, data), i)
				.then((res) => {
					console.log('添加购物车', res);
					//购物车数量
					if (res.code == '0') {
						this.cartCount = res.data.count;
						if (this.isShake) {
							this.onCaetAnimation();
						}
					}
				})
				.catch((res) => {});
		},
	},
	mounted() {
		let that = this;
		that.$nextTick(function () {
			if (this.vipUser) {
				uni.createSelectorQuery()
					.select('.shop-information')
					.boundingClientRect((header) => {
						uni.createSelectorQuery()
							.select('.cu-bar')
							.boundingClientRect((bottom) => {
								uni.createSelectorQuery()
									.select('.custom')
									.boundingClientRect((custom) => {
										uni.getSystemInfo({
											success(res) {
												that.heightvip = res.windowHeight - 112 - header.height - 20 - custom.height - bottom.height - 50;
											}
										});
									})
									.exec();
							})
							.exec();
					})
					.exec();
				console.log(this.vipUser, 'vipUser');
			}
		});
	}
};
</script>
<style lang="scss" scoped>
.buy {
	/* padding: 10rpx 20rpx 10rpx 20rpx; */
	display: inline-flex;
	justify-content: center;
	align-items: center;
	width: 48rpx;
	height: 48rpx;
	// font-weight: 700;
	flex-shrink: 0;
	background-color: #e53c43;
	margin: 0 auto;
	margin-right: 20rpx;
}
.activerow {
	margin-top: 20rpx;
	height: auto;
	width: 100%;
}
.goods-container {
	justify-content: space-between;
	flex-wrap: wrap;
	box-sizing: border-box;
	// box-sizing: content-box;
	/* padding: 20rpx; */
}

.goods-box {
	width: 100%;
	/* height: 566rpx; */
	background-color: #fff;
	overflow: hidden;
	/* margin-bottom: 20rpx; */
	/* border-radius: 20rpx; */
	/* box-shadow: 0px 0px 30px #e5e5e5; */
}

.goods-box .img-box {
	width: 100%;
	height: 370rpx;
	max-height: 370rpx;
	/* overflow: hidden; */
	position: relative;
}

.img-border {
	border-left-width: 6rpx;
	border-right-width: 6rpx;
	border-top-width: 6rpx;
	border-style: solid;
	border-color: transparent;
}

.goods-box .img-box image {
	width: 100%;
	height: 349rpx;
	max-height: 349rpx !important;
	overflow: hidden !important;
}

/* 适用范围部分 */
.applyScope {
	display: flex;
	justify-content: flex-start;
	font-size: 20rpx;
	margin: 10rpx 20rpx 0;
	min-height: 6rpx;
}

/* 底部标签 */
.btmLabelCon {
	padding: 0 20rpx;
	display: flex;
	flex-wrap: wrap;
	overflow: hidden;
	height: 48rpx;
}

/* 店铺名称 */
.shopNameCon {
	padding: 0 20rpx 20rpx;
	display: flex;
	align-items: center;
	font-size: 20rpx;
}

.shopName {
	margin-right: 40rpx;
	color: #777777;
}

.shopIn text {
	margin-right: 10rpx;
}

.markUrlsList {
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	height: 40rpx;
	display: flex;
	flex-direction: row-reverse;

	.markImgbox {
		display: block;
		width: 40rpx;
		height: 40rpx;
	}
}
</style>
