<template>
  <view>
    <view>
      <view :style="{background: backgroundColor,}">
        <view
          class="cu-form-group phone_margin border-none"
          style="margin-top: 0rpx;min-height: 85rpx;"
        >
          <view
            class="title"
            style="width: 24%;"
          >姓 名</view>
          <input
            class="input-border"
            placeholder="请输入中文姓名"
            type="text"
            name="cifName"
            v-model="form.cifName"
          >
        </view>

        <view
          class="cu-form-group password_margin border-none"
          style="margin-top: 0rpx;min-height: 85rpx;"
        >
          <view
            class="title"
            style="width: 24%;"
          >身份证号</view>
          <input
            class="input-border"
            placeholder="请输入身份证号"
            name="idNo"
            type="idcard"
            v-model="form.idNo"
          >
        </view>

        <view
          class="cu-form-group password_margin border-none"
          style="margin-top: 0rpx;min-height: 85rpx;"
        >
          <view
            class="title"
            style="width: 24%;"
          >银行卡号</view>
          <input
            class="input-border"
            placeholder="请输入银行卡号"
            @blur="isTAcNo"
            type="idcard"
            name="tAcNo"
            v-model="form.tAcNo"
          >
        </view>

        <view
          class="cu-form-group phone_margin border-none"
          style="margin-top: 0rpx;min-height: 85rpx;"
        >
          <view
            class="title"
            style="width: 24%;"
          >手机号</view>
          <input
            class="input-border"
            placeholder="请输入手机号"
            style="padding-left: 30rpx;border-bottom-right-radius: 0rpx;border-top-right-radius: 0rpx;"
            name="mobilePhone"
            type="number"
            v-model="form.mobilePhone"
          >
          <span
            @click="getPhoneCode"
            class="cu-btn bg-gray "
            style="height: 70rpx;"
            :class="'display:' + msgKey"
          >{{msgText}}</span>
        </view>

        <view
          class="cu-form-group phone_margin border-none"
          style="margin-top: 0rpx;min-height: 85rpx;"
        >
          <view
            class="title"
            style="width: 24%;"
          >验证码</view>
          <input
            class="input-border"
            placeholder="请输入验证码"
            style="padding-left: 30rpx;"
            name="messageCode"
            type="number"
            maxlength=6
            v-model="form.messageCode"
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import api from 'utils/api'
import validate from 'utils/validate'

const MSGINIT = "获取验证码",
  MSGSCUCCESS = "${time}秒后可重发",
  MSGTIME = 60;

export default {
  props:{
    source:{
      type: String,
			default: "",
    },
    backgroundColor:{
      type: String,
			default: "#FFFFFF !important",
    }
  },
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      form: {
        cifName: '',//姓名
        idNo: '',//身份证号码
        mobilePhone: '',//手机号
        tAcNo: '',//银行卡
        messageCode: '',//验证码
        messageTaskId: '',// 短信验证码Id
        msg: ''//银行卡返回信息
      },

      msgKey: false,
      msgText: MSGINIT,
      msgTime: MSGTIME,
    };
  },

  mounted() {
    //获取缓存中的用户信息
    const userinfo = uni.getStorageSync('USER_WALLET_INFO');
    if (userinfo) {
      console.log("userinfo", userinfo);
      this.form.idNo = userinfo.idNo
      this.form.cifName = userinfo.cifName
      this.form.mobilePhone = userinfo.mobilePhone
      this.form.tAcNo = userinfo.tAcNo
    }
  },

  onShow () {
    this.form.messageCode = ''
  },

  methods: {
    //查询银行卡是否可用
    isTAcNo () {
      this.getCardBin()
    },

    //银行卡名称查询
    getCardBin () {
      api.getCardBin(this.form.tAcNo).then((res) => {
        console.log('getCardBin', res);
        if (res.code == '0') {
        } else {
        }
      }).catch((err) => {
        console.log("err", err);
      })
    },

    //获取手机号验证码
    getPhoneCode () {
      let that = this
      if (that.msgKey) return
      if (!validate.validateMobile(that.form.mobilePhone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      that.msgKey = true;
      if (that.source&&that.source=="distribution") {
        that.phoneCode(that.form.mobilePhone)
        return
      }else{
        that.getMobilePhoneCode(that.form.mobilePhone)
      }
    },

    //获取手机号验证码
    getMobilePhoneCode(phone){
      let that = this
      api.getMobilePhoneCode({
          mobilePhone: phone,
          templateId: 'C01'
        }).then(res => {
          that.msgKey = false
          console.log("res", res);
          if (res.code == '0') {
            uni.showToast({
              title: '验证码发送成功',
              icon: 'none',
              duration: 3000
            });
            that.form.messageTaskId = res.data.message_task_id
            that.msgText = MSGSCUCCESS.replace('${time}', that.msgTime)
            that.msgKey = true
            const time = setInterval(() => {
              that.msgTime--
              that.msgText = MSGSCUCCESS.replace('${time}', that.msgTime)
              if (that.msgTime == 0) {
                that.msgTime = MSGTIME
                that.msgText = MSGINIT
                that.msgKey = false
                clearInterval(time)
              }
            }, 1000)
          }
        }).catch(() => {
          that.msgKey = false
        });
    },

    //获取手机号验证码
    phoneCode(phone){
      let that = this
      api.getPhoneCode({
          phone: phone,
          type: '1',
          codeSize:'6'
        }).then(res => {
          that.msgKey = false
          console.log("res", res);
          if (res.code == '0') {
            uni.showToast({
              title: '验证码发送成功',
              icon: 'none',
              duration: 3000
            });
            that.form.messageTaskId = res.data.message_task_id
            that.msgText = MSGSCUCCESS.replace('${time}', that.msgTime)
            that.msgKey = true
            const time = setInterval(() => {
              that.msgTime--
              that.msgText = MSGSCUCCESS.replace('${time}', that.msgTime)
              if (that.msgTime == 0) {
                that.msgTime = MSGTIME
                that.msgText = MSGINIT
                that.msgKey = false
                clearInterval(time)
              }
            }, 1000)
          } 
        }).catch(() => {
          that.msgKey = false
        });
    }
  }

}
</script>

<style>
page {
  background: #ffffff;
}
</style>

<style scoped>

.input-border {
  padding-left: 15px;
  background: #f9f9f9;
  border: 1rpx solid #eaeaea;
  border-radius: 9rpx;
  width: 545rpx;
  height: 70rpx;
}

.border-none {
  border: none;
}

.phone_margin {
  margin: 15rpx 0rpx 0rpx;
  border-radius: 10rpx;
}

.password_margin {
  /* margin: 0rpx 30rpx; */
  border-radius: 10rpx;
}
</style>

