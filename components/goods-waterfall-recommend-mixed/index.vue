<template>
	<view>
		<waterfall-recommend-mixed :loadMore="loadMore" :setData="newData" :list="newGoodsList" :goodsSpace="goodsSpace"
			@click="handleClick" :fullWidth="fullWidth" :showimage="showimage"
			:borderRadiusLeftTop="newData.borderRadiusLeftTop" :borderRadiusRightTop="newData.borderRadiusRightTop"
			:customCoverImage="newData.coverImage" />
	</view>
</template>

<script>
	import waterfallRecommendMixed from "components/waterfallRecommendMixed/index.vue"
	export default {
		data() {
			return {
				newGoodsList: []
			};
		},

		components: {
			waterfallRecommendMixed
		},
		props: {
			newData: {
				type: Object,
				default: () => {}
			},
			
			goodsList: {
				type: Array,
				default: () => []
			},
			goodsSpace: {
				type: String | Number,
				default: 3
			},
			fullWidth: {
				type: Number,
				default: 750
			},

			loadMore: {
				type: Boolean,
				default: false
			},
			newDataSize: {
				type: Number,
				default: 3
			},
			showimage: {
				type: Boolean,
				default: true
			},
			customCoverImage: {
				type: String | Number,
				default: "",
			},

			borderRadiusLeftTop: {
				type: String | Number,
				default: 12,
			},

			borderRadiusRightTop: {
				type: String | Number,
				default: 12,
			},
		},
		watch: {
			newData: {
				handler(newValue, oldValue) {
					console.log(newValue)
					if (!newValue) {
						return;
					}
					/**
					 * @swiperobj 轮播对象
					 * @isSwiper 是否显示
					 * @isSpecial 天天特卖是否显示
					 * @specialImgUrl 天天特卖图片地址
					 * @specialImgRadius 天天特卖圆角
					 * @specialUrl 天天特卖跳转链接地址
					 */
					let {
						goodsList = [], swiperobj = {}, isSwiper = false, specialImgUrl = '', specialImgRadius = '',
							specialUrl = '', isSpecial = false, isTheme = false, themeImgUrl = '', themeImgRadius = '',
							themeUrl = ''
					} = newValue;
					if (+isSwiper) {
						goodsList.unshift(swiperobj);
					}
					//天天特卖
					if (+isSpecial) {
						let speciallist = {
							specialImgUrl,
							specialImgRadius,
							specialUrl,
							isSpecial
						}
						if (goodsList[0].type === 'swiper' && goodsList[0].goodsList && goodsList[0].goodsList.length) {
							goodsList.splice(1, 0, speciallist)
						} else {
							goodsList.unshift(speciallist);
						}
					}
					//主题图片
					if (+isTheme) {
						let theme = {
							themeImgUrl,
							themeImgRadius,
							themeUrl,
							isTheme
						}
						goodsList.splice(this.newDataSize, 0, theme)
					}
					this.newGoodsList = goodsList;
					console.log(this.newGoodsList, this.newData, '-----')
				},
				immediate: true
			},
			goodsList: {
				handler(newValue) {
					this.newGoodsList = newValue;
				}
			}
		},
		methods: {
			handleClick(data) {
				const sourceModule = encodeURIComponent('瀑布流组件');
				uni.navigateTo({
					url: "/pages/goods/goods-detail/index?id=" + data.id + "&source_module=" +sourceModule
				})
			}
		}
	};
</script>
<style>
	/* 优惠券部分 */
	.couponBox {
		line-height: 26rpx;
	}
</style>