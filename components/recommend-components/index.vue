<template>
	<view v-if="canLoad">
		<!-- 智能推荐 -->
		<!-- <view class="cu-bar justify-center margin-top-sm" style="min-height: 70rpx" v-if="showTitle">
			<view class="recommend-action text-xdf" style="color: #000000;">
				<text class="cuIcon-move"></text>
				<text style="margin: 0 10rpx;">优选</text>
				<text class="cuIcon-move"></text>
			</view>
		</view> -->
		<goods-waterfall :goodsList="goodsListRecom"></goods-waterfall>
		<view v-if="needLoadMore" :class="'cu-load ' + (loadmoreRecommendList ?'loading':'over')"></view>
	</view>
</template>

<script>
	import goodsWaterfall from "@/components/goods-waterfall/index.vue"
	const app = getApp();
	import api from '@/utils/api'
	export default {
		components: {
			goodsWaterfall
		},
		props: {
			//接口参数
			itemId: {
				type: String,
				default: null
			},
			sceneId:{
				type: String,
				default: 'sy101'
			},
			needLoadMore: {
				type: Boolean,
				default: false
			},
			//由父组件控制什么时候可以加载推荐数据,如果需要传入itemId 就等获取到itemId再设置为true，如果不需要传入itemId直接设置为true
			canLoad: {
				type: Boolean,
				default: false
			},
			showTitle: {
				type: Boolean,
				default: true
			},
			count:{
				type: Number,
				default: 40
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				loadmoreRecommendList: true,
				goodsListRecom: [],
				loadDataTimes:0
			}
		},
		watch: {
			canLoad: {
				handler(newValue, oldValue) {
                    console.log("是不是要显示标题", this.showTitle)
					if(newValue){
					  this.getGoodsRecom(true);
					}
				},
				immediate:true
			}
		},
		
		methods: {
			//推荐商品
			/**
			 * @param Boolean fresh  是否刷新
			 */
			getGoodsRecom(fresh) {
				if (this.loadmoreRecommendList) {
					api.getRecommendList({
						sceneId: this.sceneId,
						itemId: this.itemId,
						returnCount: this.count
					}).then(res => {
						this.loadDataTimes++; 	
						if (!res.data || res.data.length == 0 ) {
						    if(this.loadDataTimes<1){
								this.getGoodsRecom();
							}
						} else if (res.data || res.data.length>0  ){
							if(fresh) this.goodsListRecom = [ ...res.data];
							else this.goodsListRecom = [...this.goodsListRecom, ...res.data];
						}
						this.$emit('getGoodsRecom', res.data);  
					}).catch(e => {
						this.loadmoreRecommendList = false;
					});
				}

			},

			loadMoreGoodsRecom() {
				if (this.needLoadMore) {
					this.getGoodsRecom(false)
				}
			}
		}
	}
</script>

<style>
	.recommend-action {
		    display: flex;
		    align-items: center;
		    height: 100%;
		    justify-content: center;
		    max-width: 100%;
	}
</style>








