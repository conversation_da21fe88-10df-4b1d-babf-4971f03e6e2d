<template>
	<view v-if="purseShow" :class="classstyle&&classstyle.length ? classstyle[2] : 'cell'" >
		<view :class="
        classstyle.length
          ? classstyle[0]
          : 'text-black margin-left-sm text-height text-col text-df'
      ">
			零钱 (剩余 ¥{{ pursePrice||'0' }})</view>
		<block v-if="purseStatus == '1'">
			<view style="text-align: right" @click="Gouse" class="gouser text-df">
				去使用 <text class="cuIcon-right"></text>
			</view>
		</block>
		<block v-else-if="purseStatus == '2'">
			<view v-if="changprop == '0'" @click="Tochoose" :class="
          classstyle.length
            ? classstyle[1]
            : 'flex-twice text-df text-right margin-right-sm text-gray'
        ">
				<text class="text-red text-df">暂不使用</text>
				<text class="cuIcon-right"></text>
			</view>
			<view v-else-if="changprop" @click="Tochoose" :class="
          classstyle.length
            ? classstyle[1]
            : 'text_usemoney flex-twice flex justify-end  align-center text-df text-right margin-right-sm text-red'
        ">
				-
				<format-price
					signFontSize="26rpx"
					smallFontSize="26rpx"
					priceFontSize="30rpx" 
					:price="changprop" 
				/>
				<!-- <text class="text-price">{{ changprop }} </text> -->
				<text class="cuIcon-right"></text>
			</view>
			<view v-else style="text-align: right" :class="
          classstyle.length
            ? classstyle[1]
            : 'flex-twice text-df text-right margin-right-sm text-gray'
        ">
				<text class="text-red text-df">暂不可用</text>
				<text class="cuIcon-right"></text>
			</view>
		</block>
	</view>
</template>
<script>
	export default {
		components: {},
		props: {
			classstyle: {
				type: Array,
				default:[]
			},
			// 剩余零钱
			pursePrice: {
				type: Number,
				default: 0
			},

			//使用金额
			newChangprop: {
				type: Number,
				default: 0,
			},

			//零钱状态 没开通钱包：0；开通没签约：1；开通并签约：2
			purseStatus: {
				type: String,
				default: ''
			},

			//零钱最高抵扣
			purseDeductPrice: {
				type: String,
				default: '0'
			},

			//零钱 自定义返显默认值
			paymentPursePrice: {
				type: Number,
				default: 0,
			}
		},
		data() {
			return {
				changprop: this.newChangprop, //使用金额
				purseShow: false, //控制零钱是否显示
				radioChange: null, //用户选择项
			};
		},
		methods: {
			Gouse() {
				// 显示手机短信弹框
				this.$EventBus.$emit("Displayphone", true);
			},

			Tochoose() {
				console.log("Tochoose===>", this.changprop);
				this.$EventBus.$emit("showchange", {
					modalchange: true
				});
			},

			// 查询零钱
			getPursepage() {
				// purseStatus 0 未开通零钱 则 不显示
				if (this.purseStatus !== "0" && this.purseStatus) {
					// purseStatus 判断开通未签约 1 开通并签约2选择使用
					// 默认使用最高抵扣

					console.log('---getPursepage---', this.purseStatus, this.purseDeductPrice, this.newChangprop)
					if (this.newChangprop == "-1") {
						this.changprop = this.purseDeductPrice
					}
					this.purseShow = true;
				} else {
					this.purseShow = false;
				}
			}
		},

		created() {
			let that = this;
			//解决一直累加进行请求
			//最高抵扣
			this.$EventBus.$on("purse-deduct-price", (v) => {
				console.log("purseDeductPrice===>", v, this.newChangprop, this.changprop, this.radioChange);
				if (this.newChangprop == "-1" || (this.radioChange && this.radioChange == '1')) {
					that.changprop = v
					this.$emit('radioPurseDeductPrice', that.changprop, this.radioChange)
				}
			});

			//输入的价格
			this.$EventBus.$on("showgetchange", (v) => {
				console.log(v, 'jieh接收到了', this.purseStatus)
				this.radioChange = v[1];
				// if(this.purseStatus=='1'){
				// 		that.changprop = v[0];
				// }else{
				console.log(v, 'small开通并签约', v, this.paymentPursePrice)
				// 处理用户反向操作，返显问题
				if (this.paymentPursePrice !== 0 && v[1] == '1') {
					that.changprop = this.paymentPursePrice
				} else {
					that.changprop = v[0];
				}
			});

			this.getPursepage()
			console.log("pursePrice==created==》", this.pursePrice, 'this.purseStatus', this.purseStatus, "this.changprop",
				this.changprop);
		},

		mounted() {
			console.log("pursePrice==mounted==》", this.pursePrice, "this.purseStatus", this.purseStatus);
		},

		beforeDestroy() {
			// this.$EventBus.$off('Displayphone')
			this.$EventBus.$off('purse-deduct-price')
			this.$EventBus.$off('showgetchange')
		}

	};
</script>

<style scoped lang="scss">
	.cell {
		display: flex;
		justify-content: space-between;
		width: 100%;
	}

	.text_usemoney {
		color: red;
	}

	.gouser {
		padding-left: 8rpx;
		width: 150rpx;
		height: 55rpx;
		background: #dcc2ad;
		border-radius: 28rpx;
		color: white;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: 20rpx;
	}

	.Tips {
		display: flex;
		background-color: #fdf7f7;
		margin: 30rpx 30rpx 0rpx;
		padding: 30rpx 25rpx;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 15rpx;

		.Tips-title {
			font-size: 22rpx;
			width: 30%;
			color: #e54d42;
			font-weight: 700;
		}

		.Tips-text {
			font-size: 20rpx;
			color: #555555;
		}
	}

	.location-bg {
		height: 180rpx !important;
		background-repeat: repeat-x;
		background-position: left bottom;
		background-size: auto 4rpx;
	}

	.addressBackg {
		background-image: url("https://img.songlei.com/1/material/14999eea-9c3b-4617-914e-b37c1cdae3ab.png");
	}

	.row-img {
		width: 180rpx !important;
		height: 180rpx !important;
		border-radius: 10rpx;
	}

	.text-price-two {
		float: left;
		line-height: 84rpx;
	}

	.specification {
		white-space: normal;
		height: auto;
	}

	.loc-content {
		width: calc(100% - 96rpx - 60rpx - 80rpx) !important;
		left: 30rpx !important;
	}

	.loc-info {
		line-height: 1.4em;
	}

	.cu-list.menu>.cu-item:after {
		border-bottom: unset !important;
	}

	.cu-list.menu-avatar>.cu-item {
		height: auto;
		padding: 20rpx;
		min-height: unset !important;
	}

	.cu-list.menu>.cu-item {
		min-height: unset !important;
	}

	.delivery-way {
		justify-content: unset !important;
	}

	.cu-item-box {
		padding: 16rpx 0;

		.cu-bar {
			min-height: auto;
			padding-bottom: 30rpx;
		}

		.cu-bar:last-child {
			padding-bottom: 0;
		}
	}

	.gifts-box {
		display: flex;

		.gifts-title {
			font-size: 24rpx;
			padding-left: 20rpx;
			padding-right: 20rpx;
		}

		.gifts-list {
			flex: 1;
			width: calc(100% - 40rpx - 64rpx);

			// text-align: right;
			.gift-name {
				font-size: 24rpx;
				line-height: 42rpx;
				display: flex;

				.name {
					display: block;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					width: calc(100% - 44rpx);
				}
			}
		}
	}

	// 配送方式
	.distributionBox {
		// width: 100%;
		justify-content: space-between;
		position: relative;
		display: flex;
		padding-right: 10rpx;
		align-items: center;
		padding: 20rpx 20rpx 0;

		.flex-1 {
			flex: 1;
			height: 80rpx;
			border-radius: 80rpx;
			background: #fff;
			color: #ff4444;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 10rpx;
		}

		.flex-1.hover {
			background-color: #ff4444;
			color: #fff;
		}
	}

	.regionBox {
		font-size: 26rpx;
		color: #000000;
		line-height: 26rpx;
	}

	.detailedAddressBox {
		font-size: 28rpx;
		font-weight: bold;
		color: #242223;
		line-height: 30rpx;
		padding: 20rpx 0;
	}

	.personalInfoBox {
		font-size: 22rpx;
		color: #000000;
		line-height: 22rpx;
	}

	.cu-card>.cu-item {
		border-radius: 20rpx;
	}

	.text-height {
		line-height: 60rpx;
	}

	.text-col {
		color: #000000;
	}

	.ModalService {
		width: 100%;
		height: 300rpx;
		text-align: left;
	}

	.prompt {
		// height: 25px;
		font-size: 24rpx;
		color: #000000;
		padding-left: 26rpx;
		padding-bottom: 16rpx;
	}

	.unqualified-goods {
		padding: 0 20rpx;
	}

	.prompt-goods-list {
		display: flex;
		min-width: 0;
		overflow: hidden;

		.prompt-goods-img-box {
			margin-right: 20rpx;

			.prompt-goods-img {
				width: 160rpx;
				height: 160rpx;
			}

			&:last-child {
				margin-right: 0;
			}
		}
	}

	.prompt-goods-length {
		padding-left: 20rpx;
		white-space: nowrap;
	}

	// 自提地址
	.selfAddressBox {
		width: 100%;
		box-sizing: border-box;
		display: flex;

		>view:last-child {
			flex: 1;
		}
	}
</style>