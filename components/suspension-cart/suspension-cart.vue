<template>
	<view>
		<view style="position: relative !important">
			<view @tap.stop="toCart" class="round buy text-xl cart" :class="['bg-' + theme.themeColor + '-border', 'animation-' + animation]">
				<text class="cuIcon-cart text-lg"></text>
			</view>
			<view :class="'cartCount animation-' + animation" class="cu-tag badge" v-show="cartCount != '0'">
				{{ cartCount }}
			</view>
		</view>
	</view>
</template>

<script>
import api from 'utils/api';
const app = getApp();

export default {
	data() {
		return {
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			cartCount: 0 //购物车数量
		};
	},
	props: {
		//购物车数量
		shoppingCartCount: {
			type: String | Number,
			default: 0
		},

		//购物车摇
		animation: {
			type: String,
			default: ''
		},

		//商品添加购物车类型 ""||1正常商品 2 团购 3 礼品卡
		cartType: {
			type: String | Number,
			default: ''
		},

		//店铺Id
		shopId: {
			type: String | Number,
			default: ''
		}
	},

	mounted() {
		this.shoppingCartCountFun();
	},

	watch: {
		shoppingCartCount: {
			handler: function (newValue, oldValue) {
				console.log('=====newValue=购物车数量===', newValue);
				if (newValue && newValue > 0 && newValue != oldValue) {
					this.cartCount = newValue;
				}
			},
			immediate: true //第一次刷新页面时就会执行
		}
	},

	methods: {
		// 购物车数量
		shoppingCartCountFun() {
			let that = this;
			let data = {};
			if (this.cartType == '3') {
				data.type = this.cartType;
			}
			api.shoppingCartCount(Object.assign({}, data)).then((res) => {
				that.cartCount = res.data; //设置TabBar购物车数量
				app.globalData.shoppingCartCount = that.cartCount + '';
				uni.$emit('updateCart');
			});
		},

		//跳转到购物车
		toCart() {
			if (this.cartType == '3') {
				uni.navigateTo({
					url: '/pages/shop/shop-cart/index?cartType=3&shopId=' + this.shopId
				});
				return;
			}
			uni.navigateTo({
				url: '/pages/shop/shop-cart/index'
			});
		}
	}
};
</script>
<style scoped>
.animation-shake {
	animation: shake 0.3s !important;
}

@keyframes shake {
	0%,
	100% {
		transform: translateX(0);
	}

	10% {
		transform: translateX(-9px);
	}

	20% {
		transform: translateX(8px);
	}

	30% {
		transform: translateX(-7px);
	}

	40% {
		transform: translateX(6px);
	}

	50% {
		transform: translateX(-5px);
	}

	60% {
		transform: translateX(4px);
	}

	70% {
		transform: translateX(-3px);
	}

	80% {
		transform: translateX(2px);
	}

	90% {
		transform: translateX(-1px);
	}
}

.cart {
	position: fixed;
	width: 70rpx;
	height: 70rpx;
	right: 32rpx;
	bottom: 200rpx;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
}

.cartCount {
	position: fixed;
	right: 40rpx;
	bottom: 238rpx;
}

@media (min-width: 549px) {
	.cart {
		width: 52rpx;
		height: 52rpx;
	}

	.cartCount {
		position: fixed;
		right: 36rpx;
		bottom: 230rpx;
	}
}
</style>
