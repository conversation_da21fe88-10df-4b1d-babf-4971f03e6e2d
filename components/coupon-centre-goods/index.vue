<template>
  <view>
    <view class="coupon radius-lg margin-lr-sm margin-bottom-sm flex" v-if="picUrl.length >= 2">
      <image v-if="status !== 'Y'" :src="statusObj[status].img" mode="aspectFill" class="statusImg"></image>
      <view class="coupon-img">
        <image src="//img.songlei.com/-1/material/e5c43d18-47fd-4f32-9534-bf9deaff0d82.png" style="width: 100%;" mode="widthFix"></image>
        <image src="//img.songlei.com/-1/material/8d83c2fe-b483-4119-a269-5e8cb49af8b7.png" class="img-right" style="width: 200upx;" mode="widthFix"></image>
      </view>
      <view class="coupon-goods-left">
        <view
          class="coupon-left-icons" 
          :class="goodsInfo.eventTypeCode === '5' ? 'warps' : ''" 
          style="top: 0;left: 10upx;z-index: 1;" 
          :style="{ backgroundImage: 'url(' + iconDict[goodsInfo.eventTypeCode].imgUrl + ');', color: goodsInfo.eventTypeCode === '4' ? '#000' : '' }">
          {{iconDict[goodsInfo.eventTypeCode].label}}
        </view>
        <view class="coupon-title text-cut">
          {{ goodsInfo.eventTypeName || '' }}{{ goodsInfo.simpleUsageRule && `(${goodsInfo.simpleUsageRule})` }}
        </view>
        <view class="goods flex justify-between">
          <block v-if="goodsInfo.couponPicUrl">
            <view class="goods-imges flex" v-for="(item, index) in goodsInfo.couponPicUrl.split(',').slice(0, 3)" :key="index">
              <image :src="item" mode="aspectFill"></image>
            </view>
          </block>
        </view>
      </view>
      <view class="coupon-line"></view>
      <view class="coupon-goods-right">
        <template v-if="goodsInfo.eventTypeCode === '1'">
          <view class="goods-price">
            <block v-if="goodsInfo.useType === '04'">
              <text>{{ goodsInfo.faceValue * 10 }}</text>
              <text class="goods-price-icon">折</text>
            </block>
            <block v-else>
              <text class="goods-price-icon">￥</text>
              <text>{{goodsInfo.faceValue}}</text>
            </block>
          </view>
          <view class="goods-type">
            <block v-if="goodsInfo.condAmount">满{{goodsInfo.condAmount || 0}}可用</block>
            <block v-else>无门槛</block>
          </view>
          <view class="goods-discount">
            <view class="goods-old-price">
              <text class="goods-price-icon">价值:</text>
              <text style="text-decoration: line-through;">￥{{goodsInfo.faceValue}}</text>
            </view>
            <view class="goods-price">
              <text class="goods-price-icon">到手价￥</text>
              <text>{{goodsInfo.cash}}</text>
            </view>
            <view style="margin: 0upx auto 0;">
              <btn
                :status="status"
                :eventTypeCode="goodsInfo.eventTypeCode"
                :eventPicUrl="goodsInfo.eventPicUrl || ''"
                @btnConvert="btnConvert"
              />
            </view>
          </view>
        </template>
        <template v-if="goodsInfo.eventTypeCode === '2'">
          <view class="goods-price">
            <block v-if="goodsInfo.useType === '04'">
              <text>{{ goodsInfo.faceValue * 10 }}</text>
              <text class="goods-price-icon">折</text>
            </block>
            <block v-else>
              <text class="goods-price-icon">￥</text>
              <text>{{goodsInfo.faceValue}}</text>
            </block>
          </view>
          <view class="goods-type">
            <block v-if="goodsInfo.condAmount">满{{goodsInfo.condAmount || 0}}可用</block>
            <block v-else>无门槛</block>
          </view>
          <view style="width: 168upx;border-top: 1upx dotted #ddd;margin-top: 10upx;padding-bottom: 10upx;"></view>
          <view class="goods-setTimeout">
            {{setTime}}
          </view>
          <view style="margin: 20upx auto 0;">
            <btn
              :status="status"
              :eventTypeCode="goodsInfo.eventTypeCode"
              :eventPicUrl="goodsInfo.eventPicUrl || ''"
              @btnConvert="btnConvert"
            />
          </view>
        </template>
        <template v-if="goodsInfo.eventTypeCode === '3'">
          <view class="goods-price">
            <block v-if="goodsInfo.useType === '04'">
              <text>{{ goodsInfo.faceValue * 10 }}</text>
              <text class="goods-price-icon">折</text>
            </block>
            <block v-else>
              <text class="goods-price-icon">￥</text>
              <text>{{goodsInfo.faceValue}}</text>
            </block>
          </view>
          <view class="goods-type">
            <block v-if="goodsInfo.condAmount">满{{goodsInfo.condAmount || 0}}可用</block>
            <block v-else>无门槛</block>
          </view>
          <view style="margin: 20upx auto 0;">
            <btn
              :status="status"
              :eventTypeCode="goodsInfo.eventTypeCode"
              :eventPicUrl="goodsInfo.eventPicUrl || ''"
              @btnConvert="btnConvert"
            />
          </view>
        </template>
        <template v-if="goodsInfo.eventTypeCode === '4'">
          <view class="goods-price">
            <block v-if="goodsInfo.useType === '04'">
              <text>{{ goodsInfo.faceValue * 10 }}</text>
              <text class="goods-price-icon">折</text>
            </block>
            <block v-else>
              <text class="goods-price-icon">￥</text>
              <text>{{goodsInfo.faceValue}}</text>
            </block>
          </view>
          <view class="goods-type">
            <block v-if="goodsInfo.condAmount">
              满{{goodsInfo.condAmount || 0}}可用
            </block>
            <block v-else>
              无门槛
            </block>
          </view>
          <view style="width: 168upx;border-top: 1upx dotted #ddd;margin-top: 10upx;padding-bottom: 10upx;"></view>
          <view class="goods-setTimeout">
            {{goodsInfo.points || 0}}积分兑换
          </view>
          <view style="margin: 20upx auto 0;">
            <btn
              :status="status"
              :eventTypeCode="goodsInfo.eventTypeCode"
              :eventPicUrl="goodsInfo.eventPicUrl || ''"
              @btnConvert="btnConvert"
            />
          </view>
        </template>
        <template v-if="goodsInfo.eventTypeCode === '5'">
          <view class="goods-price">
            <block v-if="goodsInfo.useType === '04'">
              <text>{{ goodsInfo.faceValue * 10 }}</text>
              <text class="goods-price-icon">折</text>
            </block>
            <block v-else>
              <text class="goods-price-icon">￥</text>
              <text>{{goodsInfo.faceValue}}</text>
            </block>
          </view>
          <view class="goods-type">
            <block v-if="goodsInfo.condAmount">满{{goodsInfo.condAmount || 0}}可用</block>
            <block v-else>无门槛</block>
          </view>
          <view style="width: 168upx;border-top: 1upx dotted #ddd;margin-top: 10upx;padding-bottom: 10upx;"></view>
          <view class="goods-setTimeout">
            {{goodsInfo.points}}积分+￥{{goodsInfo.cash || 0}}
          </view>
          <view style="margin: 20upx auto 0;">
            <btn
              :status="status"
              :eventTypeCode="goodsInfo.eventTypeCode"
              :eventPicUrl="goodsInfo.eventPicUrl || ''"
              @btnConvert="btnConvert"
            />
          </view>
        </template>
      </view>
    </view>
    <view class="coupon radius-lg margin-lr-sm margin-bottom-sm flex" v-if="picUrl.length === 1">
      <image v-if="status !== 'Y'" :src="statusObj[status].img" mode="aspectFill" class="statusImg"></image>
      <view class="coupon-img">
        <image src="https://img.songlei.com/-1/material/5c1e883f-6b9a-433f-aa07-9efece3bd73e.png" style="width: 100%;" mode="widthFix"></image>
        <image src="https://img.songlei.com/-1/material/2ef09705-c481-4901-8298-012bb9bd42b5.png" class="img-left" style="width: 246upx;" mode="widthFix"></image>
      </view>
      <view class="coupon-goods-right align-center">
        <view 
          class="coupon-left-icons" 
          :class="goodsInfo.eventTypeCode === '5' ? 'warps' : ''" 
          style="top: 0;left: 10upx;z-index: 1;" 
          :style="{ backgroundImage: 'url(' + iconDict[goodsInfo.eventTypeCode].imgUrl + ');', color: goodsInfo.eventTypeCode === '4' ? '#000' : '' }">
          {{iconDict[goodsInfo.eventTypeCode].label}}
        </view>
        <view class="goods flex align-center justify-center" style="width: 200upx;height: 200upx;">
          <image class="radius-sm" :src="picUrl[0]"></image>
        </view>
      </view>
      <view class="coupon-line"></view>
      <view class="coupon-goods-left" style="width: 465upx;">
        <view class="text-sm padding-left-xs flex align-center padding-top-sm" style="height: 100upx;border-bottom: 1upx dotted #ddd;">
          {{ goodsInfo.eventTypeName || '' }}
        </view>
        <view class="flex flex-direction justify-center" style="height: 135upx;">
          <view style="color: #FF4015;">
            <block v-if="goodsInfo.useType === '04'">
              <text class="text-xl text-bold" style="padding-left: 10upx;">{{ goodsInfo.faceValue * 10 }}</text>
              <text class="text-xs" style="padding-right: 10upx;">折</text>
            </block>
            <block v-else>
              <text class="text-xs">￥</text>
              <text class="text-xl text-bold" style="padding-right: 10upx;">{{goodsInfo.faceValue}}</text>
            </block>
            <text class="text-sm">
              <block v-if="goodsInfo.condAmount">满{{goodsInfo.condAmount || 0}}可用</block>
              <block v-else>无门槛</block>
            </text>
          </view>
          <view class="" style="font-size: 22upx;">
            ({{ goodsInfo.simpleUsageRule || '暂无说明' }})
          </view>
          <template v-if="goodsInfo.eventTypeCode === '1'">
            <view>
              <text class="text-xs" style="color: #999;">
                价值:<text class="decoration-through">￥{{goodsInfo.faceValue}}</text>
              </text>
              <text class="text-xs padding-left-xs" style="color: #FF4015;">
                到手价:￥<text class="text-lg text-bold">{{goodsInfo.cash}}</text>
              </text>
            </view>
            <view class="absolute-15-20">
              <btn
                :status="status"
                :eventTypeCode="goodsInfo.eventTypeCode"
                :eventPicUrl="goodsInfo.eventPicUrl || ''"
                @btnConvert="btnConvert"
              />
            </view>
          </template>
          <template v-if="goodsInfo.eventTypeCode === '2'">
            <view>
              <text class="text-xs" style="color: #FF4015;">
                {{setTime}}
              </text>
            </view>
            <view class="absolute-15-20">
              <btn
                :status="status"
                :eventTypeCode="goodsInfo.eventTypeCode"
                :eventPicUrl="goodsInfo.eventPicUrl || ''"
                @btnConvert="btnConvert"
              />
            </view>
          </template>
          <template v-if="goodsInfo.eventTypeCode === '3'">
            <view class="absolute-15-20">
              <btn
                :status="status"
                :eventTypeCode="goodsInfo.eventTypeCode"
                :eventPicUrl="goodsInfo.eventPicUrl || ''"
                @btnConvert="btnConvert"
              />
            </view>
          </template>
          <template v-if="goodsInfo.eventTypeCode === '4'">
            <view>
              <text class="text-xs" style="color: #FF4015;">
                {{goodsInfo.points}}积分兑换
              </text>
            </view>
            <view class="absolute-15-20">
              <btn
                :status="status"
                :eventTypeCode="goodsInfo.eventTypeCode"
                :eventPicUrl="goodsInfo.eventPicUrl || ''"
                @btnConvert="btnConvert"
              />
            </view>
          </template>
          <template v-if="goodsInfo.eventTypeCode === '5'">
            <view>
              <text class="text-xs" style="color: #FF4015;">
                <text class="text-lg text-bold">{{goodsInfo.points}}</text>积分+<text class="text-lg text-bold">{{goodsInfo.cash}}</text>元
              </text>
            </view>
            <view class="absolute-15-20">
              <btn
                :status="status"
                :eventTypeCode="goodsInfo.eventTypeCode"
                :eventPicUrl="goodsInfo.eventPicUrl || ''"
                @btnConvert="btnConvert"
              />
            </view>
          </template>
        </view>
      </view>
    </view>
    <view class="coupon radius-lg margin-lr-sm margin-bottom-sm flex" v-if="picUrl.length === 0" style="height: 115upx;">
      <image v-if="status !== 'Y'" :src="statusObj[status].img" mode="aspectFill" class="statusImg"></image>
      <view class="coupon-img">
        <image src="https://img.songlei.com/-1/material/d37e92a8-2b08-4227-8683-cded7e455136.png" style="width: 100%;" mode="widthFix"></image>
        <image src="https://img.songlei.com/-1/material/dd5fdc69-ea5e-4305-aacd-7ad869d9f5e8.png" class="img-right" style="width: 200upx;" mode="widthFix"></image>
      </view>
      <view class="coupon-goods-left flex align-center justify-around">
        <view
          class="coupon-left-icons" 
          :class="goodsInfo.eventTypeCode === '5' ? 'warps' : ''" 
          style="top: 0;left: 10upx;z-index: 1;" 
          :style="{ backgroundImage: 'url(' + iconDict[goodsInfo.eventTypeCode].imgUrl + ');', color: goodsInfo.eventTypeCode === '4' ? '#000' : '' }">
          {{iconDict[goodsInfo.eventTypeCode].label}}
        </view>
        <view class="text-center text-sm" style="color: #FF4015;">
          <view>
            <block v-if="goodsInfo.useType === '04'">
              <text class="text-xl text-bold">{{ goodsInfo.faceValue * 10 }}</text>折
            </block>
            <block v-else>￥<text class="text-xl text-bold">{{goodsInfo.faceValue}}</text></block>
          </view>
          <view>
            <block v-if="goodsInfo.condAmount">满{{goodsInfo.condAmount || 0}}可用</block>
            <block v-else>无门槛</block>
          </view>
        </view>
        <view class="text-center text-sm">
          <view>{{ goodsInfo.eventTypeName || '' }}</view>
          <view class="text-xs">({{ goodsInfo.simpleUsageRule || '暂无说明' }})</view>
        </view>
      </view>
      <view class="coupon-line" style="height: 80upx;"></view>
      <view class="coupon-goods-right flex align-center justify-center flex-direction">
        <btn
          :status="status"
          :eventTypeCode="goodsInfo.eventTypeCode"
          :eventPicUrl="goodsInfo.eventPicUrl || ''"
          @btnConvert="btnConvert"
        />
        <template v-if="goodsInfo.eventTypeCode === '1'">
          <view class="text-sm" style="color: #FF4015;margin-top: 5upx;">
            到手价{{goodsInfo.cash}}元
          </view>
        </template>
        <template v-if="goodsInfo.eventTypeCode === '2'">
          <view class="text-sm" style="color: #FF4015;margin-top: 5upx;">
            {{setTime}}
          </view>
        </template>
        <template v-if="goodsInfo.eventTypeCode === '3'">
          
        </template>
        <template v-if="goodsInfo.eventTypeCode === '4' || goodsInfo.eventTypeCode === '5'">
          <view v-if="goodsInfo.eventTypeCode === '4'" class="text-sm" style="color: #FF4015;margin-top: 5upx;">
            {{goodsInfo.points}}积分
          </view>
          <view v-if="goodsInfo.eventTypeCode === '5'" class="text-sm" style="color: #FF4015;margin-top: 5upx;">
            ￥{{goodsInfo.cash}}+{{goodsInfo.points}} 积分
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
import { dateFormat } from '@/utils/util.js'
import btn from './btn.vue'
export default {
  components: {
    btn
  },
  data() {
    return {
      picUrl: [],
      iconDict: [{}, {
        label: '低价购',
        imgUrl: this.$imgUrl('-1/material/c4b4bca9-927d-4e3b-9882-842993922315.png')
      }, {
        label: '限时抢',
        imgUrl: this.$imgUrl('-1/material/611f53ca-cbc4-4f7b-b638-006b84268306.png')
      }, {
        label: '免费领',
        imgUrl: this.$imgUrl('-1/material/5f157493-45c7-4616-82fb-c021379b4305.png')
      }, {
        label: '积分兑',
        imgUrl: this.$imgUrl('-1/material/5df11d2d-8182-43fd-aaf7-40c4a6928243.png')
      }, {
        label: '积分抵值',
        imgUrl: this.$imgUrl('-1/material/5df11d2d-8182-43fd-aaf7-40c4a6928243.png')
      }],
      //N-未开始 Y-正常 O-已抢完 Q-已过期 C-活动已取消 01-已达领取上限 02-不符合活动条件 A 已领取
      statusObj: {
        'N': {
          label: '未开始',
          img: ''
        },
        'Y': {
          label: '正常',
          img: ''
        },
        'O': {
          label: '已抢光',
          img: this.$imgUrl('-1/material/fe4015bf-7e6e-4981-8789-a2d92b7e0672.png')
        },
        'C': {
          label: '活动已取消',
          img: ''
        },
        '01': {
          label: '已达领取上限',
          img: this.$imgUrl('-1/material/71db1c49-ade6-4509-acd5-46ebd01a42a9.png')
        },
        '02': {
          label: '不符合活动条件',
          img: ''
        },
        'A': {
          label: '已领取',
          img: this.$imgUrl('-1/material/71db1c49-ade6-4509-acd5-46ebd01a42a9.png')
        },
      },
      status: 'Y',
      end: false,
      setTime: ''
    }
  },
  props: {
    goodsInfo: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    goodsInfo: {
      handler(newData, oldData) {
        if(newData) {
          const { couponPicUrl, status, eventTypeCode, eventEffDatetime, eventExpDatetime, eventExpTime } = newData;
          if(couponPicUrl) {
            this.picUrl = couponPicUrl.split(',');
          }
          this.status = status;
          if(+eventTypeCode === 2 && !this.setTime) {
            const newTime = +(new Date())
            if(+(new Date(eventEffDatetime)) > newTime) {
              this.setTime = '活动未开始';
            } else if(+(new Date(eventExpDatetime)) < newTime) {
              this.setTime = '活动已过期';
            } else {
              this.end = true;
              this.createTime(+(new Date(`${dateFormat(new Date(), 'yyyy-MM-dd')} ${eventExpTime}`)));
            }
            // 限时抢，定时器
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  destroyed() {
    this.end = false;
  },
  methods: {
    btnConvert() {
      const { status } = this.goodsInfo;
      if(status === 'N') {
        this.uniToast('活动还未开始');
      } else if(status === 'O') {
        this.uniToast('活动已抢完');
      } else if(status === 'Q') {
        this.uniToast('活动已过期');
      } else if(status === 'C') {
        this.uniToast('活动已取消');
      } else if(status === '01') {
        this.uniToast('您已达到了领取的上限');
      } else if(status === '02') {
        this.uniToast('您不符合条件');
      } else if(status === 'Y') {
        this.$emit('success', this.goodsInfo)
      }
    },
    createTime(endTime) {
      const starTime = +(new Date());
      let diff = endTime - starTime;
      if (diff <= 0 || !this.end) {
        this.setTime = '活动已过期';
        return;
      }
      let h = ~~(diff / (1000*60*60));
      h = h > 9 ? h : '0' + h;
      let m = ~~((diff % (1000*60*60)) / (1000 * 60))
      m = m > 9 ? m : '0' + m;
      let s = ~~(((diff % (1000*60*60)) % (1000 * 60)) / 1000);
      s = s > 9 ? s : '0' + s;
      this.setTime = `剩余 ${h}:${m}:${s}`
      setTimeout(() => {
        this.createTime(endTime)
      }, 500)
    },
    uniToast(title) {
      uni.showToast({
        title,
        icon: 'none'
      })
    }
  }
}
</script>

<style scoped>
.coupon-goods-left {
  width: 510upx;
  padding:0 10upx 0;
}
.coupon-goods-left .coupon-title {
  padding-left: 60upx;
  line-height: 44upx;
  font-size: 24upx;
  padding-bottom: 10upx;
  white-space: nowrap;
}
.goods {
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
.coupon-goods-left .goods::after {
  content: '';
  width: 160upx;
}
.goods .goods-imges {
  width: 160upx;
}
.goods .goods-imges image {
  width: 160upx;
  height: 160upx;
  border-radius: 10upx;
}
.goods .goods-imges .goods-info-price {
  width: 100%;
  text-align: center;
  color: #666;
  font-size: 20upx;
}
.coupon-goods-right {
  flex: 1;
  /* background: url('//img.songlei.com/-1/material/8d83c2fe-b483-4119-a269-5e8cb49af8b7.png') no-repeat; */
  background-size: 100% 100%;
  padding: 10upx;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.coupon-goods-right .goods-price {
  font-size: 36upx;
  color: #FF4015;
  font-weight: bold;
  vertical-align: top;
}
.coupon-goods-right .goods-discount .goods-price {
  font-size: 32upx;
  text-align: left;
  margin-top: -12upx;
}
.coupon-goods-right .goods-setTimeout {
  color: #FF4015;
  font-size: 24upx;
  white-space:nowrap;
}
.coupon-goods-right .goods-type {
  color: #FF4015;
  font-size: 24upx;
}
.coupon-goods-right .goods-discount {
  width: 177upx;
  height: 139upx;
  background-color: #fff;
  padding: 0 8upx;
  display: flex;
  flex-direction: column;
  border-radius: 15upx;
}
.coupon-goods-right .goods-old-price {
  font-size: 20upx;
  color: #999999;
  text-align: left;
  margin-top: 10upx;
}
.coupon-goods-right .goods-btn {
  margin: 20upx auto 0;
}
.goods-btn {
  width: 150upx;
  height: 50upx;
  background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
  border-radius: 25upx;
  color: #fff;
  line-height: 50upx;
  font-size: 24upx;
}
.coupon-goods-right .goods-price .goods-price-icon {
  font-size: 20upx;
}
.coupon {
  height: 245upx;
  overflow: hidden;
  position: relative;
}
.coupon-line {
  width: 1upx;
  height: 205upx;
  margin: 20upx 0;
  border-left: 1upx dotted #e0e0e0;  
}
.bg-fff {
  background-color: #ffffff;
}
.coupon-left-icons {
  width: 54upx;
  height: 44upx;
  position: absolute;
  background: url('https://img.songlei.com/-1/material/c4b4bca9-927d-4e3b-9882-842993922315.png') no-repeat;
  background-size: 100%;
  font-size: 16upx;
  color: #fff;
  text-align: center;
  line-height: 36upx;
}
.coupon-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 710upx;
  z-index: -1;
}
.img-right {
  position: absolute;
  right: 0;
  top: 0;
}
.img-left {
  position: absolute;
  left: 0;
  top: 0;
}
.trans-rotate180 {
  transform: rotate(180deg);
}
.warps {
  line-height: 18upx;
  padding: 0 10upx;
  color: #000;
}
.statusImg {
  width: 85upx;
  height: 85upx;
  position: absolute;
  top: -15upx;
  right: 10upx;
  z-index: 1;
}
.absolute-15-20 {
  position: absolute;
  right: 26upx;
  bottom: 20upx;
}
</style>