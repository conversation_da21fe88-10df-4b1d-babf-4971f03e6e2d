<template>
	<view class="container" :style="{width: `${fullWidth}rpx`, boxSizing: 'border-box'}">
		
		<view 
			class="waterfall_left" 
			:style="{
				paddingLeft: `${goodsSpace * 2 + 4}rpx`,
				paddingRight: `${goodsSpace * 2}rpx`,
				boxSizing: 'border-box'
			}"
		>
			<template v-for="(item,index) in listLeft">
				<!-- 轮博 -->
				<view 
					class="waterfall_item" 
					style="border-radius: 0; background-color: transparent;" 
					:key="index" 
					v-if="item.type === 'swiper' && item.goodsList && item.goodsList.length"
				>
					<swiper 
						:indicator-dots="true" 
						:autoplay="true" 
						:circular="true" 
						:interval="item.interval||1500" 
						:style="{ height: (item.height || 150) * 2 + 'rpx' }"
					>
						<swiper-item 
							v-for="(items, indexs) in item.goodsList" 
							@click="handleClicks(items.pageUrl)"
							:key="indexs"
						>
							<image 
								:src="items.imageUrl| formatImg360" 
								style="width: 100%; height: 100%;" 
								:style="{ borderRadius: (item.borderRadius === '1') ? '15px' : '0' }" mode="aspectFill"
							/>
						</swiper-item>
					</swiper>
				</view>

				<!-- 天天特卖 -->
				<view 
					class="waterfall_item" 
					style="border-radius: 0; background-color: transparent;"
					:key="index"
					v-if="item.isSpecial=='1'"
					@click="toPage(item.specialUrl)"
				>	
					<image 
						:src="item.specialImgUrl| formatImg360" 
						:style="{ 
							width: '100%',
							borderRadius: (item.specialImgRadius === '1') ? '15px' : '0' 
						}"
						mode="widthFix"
					/>
				</view>

				<!-- 主题图片 -->
				<view 
					class="waterfall_item" 
					style="border-radius: 0; background-color: transparent;"
					:key="index"
					v-if="item.isTheme=='1'"
					@click="toPage(item.themeUrl)" 
				>	
					<image 
						:src="item.themeImgUrl| formatImg360" 
						:style="{ 
							width: '100%',
							borderRadius: (item.themeImgRadius === '1') ? '15px' : '0' 
						}"
						mode="widthFix"
					/>
				</view>

				<!-- 商品 1-->
				<view 
					class="waterfall_item" 
					:key="index"
					v-if="item.type == 1 && item.goodsInfo.id > 0"
					@click="handleClick(item.goodsInfo)"
				>
					<view 
						class="item_img" 
						:class="((item && item.goodsInfo && item.goodsInfo.estimatedPriceVo) && ((item.goodsInfo.estimatedPriceVo.discountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.discountPrice != '0') || (item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0') || (item.goodsInfo.estimatedPriceVo.coupon != '0.0' && item.goodsInfo.estimatedPriceVo.coupon != '0'))) && spuPriceStyle == 2 ? 'img-border' : ''"
						:style="{
							borderColor,
							position: 'relative'
						}"
					>
						
						<!-- <image 
							:src="item.goodsInfo.picUrls[0] | formatImg360" 
							mode= "widthFix" 
							:style="{height:`${fullWidth/2-4}rpx`}" 
						/> -->
						<lazy-load 
								:borderRadius="12"
								:image="item.goodsInfo.picUrls[0] | formatImg360"
								>
						</lazy-load>
						<member-icon  v-if="item.goodsInfo.memberLevelLimit&&item.goodsInfo.memberLevelLimit!='101'" :memberLevelLimit = "item.goodsInfo.memberLevelLimit">
						</member-icon>
						<view
							class="card-icon"
							v-if="setData"
							:style="{
								top: `${setData.imgTop*2}rpx`,
								left: `${setData.imgLeft*2}rpx`,
								zIndex: '0'
							}"
						>
							<view class="card-icon">
								<image
									v-if="setData.leftShowMark == 1"
									style="width: 67rpx; height: 70rpx;"
									mode="aspectFill"
									:src="setData.imageUrls"
								/>
								<image
									v-if="setData.leftShowMark == 2 && item.goodsInfo.Top"
									style="width: 67rpx; height: 70rpx;"
									mode="aspectFill"
									:src="item.goodsInfo.Top ? $imgUrl('waterfall/TOP/TOP${item.goodsInfo.Top}.png') : ''"
								/>
							</view>
						</view>

						<view
							class="right-card-icon"
							v-if="setData"
							:style="{
								top: `${setData.rightImgTop*2}rpx`,
								right: `${setData.rightImgTop*2}rpx`,
								zIndex: '0'
							}"
						>
							<view class="right-card-icon">
								<image
									v-if="item.goodsInfo.estimatedPriceVo&&(item.goodsInfo.estimatedPriceVo.totalDiscountPrice&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0'&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0.0')&& setData.rightImageUrls"
									style="width: 67rpx; height: 70rpx;"
									mode="aspectFill"
									:src="setData.rightImageUrls"
								/>
								<view
									v-if="item.goodsInfo.estimatedPriceVo&&(item.goodsInfo.estimatedPriceVo.totalDiscountPrice&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0'&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0.0') && setData.rightImageUrls && setData.ShowPrice == 1"
									class="text-bold text-white text-sm"
									:style="{
										position: 'absolute',
										bottom: '-2rpx',
										right: '0rpx',
										width: '65rpx',
										height: '35rpx',
										lineHeight: '35rpx',
										textAlign: 'center',
										fontSize: `${setData.topPriceSize*2}rpx`,
										color: `${setData.PriceColor}`
									}"
								> {{item.goodsInfo.estimatedPriceVo.totalDiscountPrice}}</view>
							</view>
						</view>

						<view v-if="!setData || (setData && (!setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle ==1 )))">
							<view 
								style="position: absolute;" 
								:style="{
									top: spuPriceStyle == 1 ? 0 : 'auto',
									bottom: spuPriceStyle == 1 ? 0 : '-10rpx',
									left: spuPriceStyle == 1 ? 0 : '-6rpx',
									right: spuPriceStyle == 1 ? 0 : '-6rpx'
								}" 
								v-if="(item && item.goodsInfo && item.goodsInfo.estimatedPriceVo) && ((item.goodsInfo.estimatedPriceVo.discountPrice && item.goodsInfo.estimatedPriceVo.discountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.discountPrice != '0') || (item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0') || (item.goodsInfo.estimatedPriceVo.coupon && item.goodsInfo.estimatedPriceVo.coupon != '0.0' && item.goodsInfo.estimatedPriceVo.coupon != '0'))"
							>
								<good-price :goodsSpu="item.goodsInfo" :customCoverImage="customCoverImage"/>
							</view>
						</view>
						<view class="sell-out" v-if="item.goodsInfo.inventoryState == 1">
							<view class="sell-out-text">售罄</view>
						</view>
					</view>

					<view class="item_info">
						<view class="text-black  padding-lr-sm overflow-2">
							<block v-if="showimage">
							<image
									v-if="tradeStateGoodsIcon&&(item && item.goodsInfo && item.goodsInfo.estimatedPriceVo) && ((item.goodsInfo.estimatedPriceVo.discountPrice && item.goodsInfo.estimatedPriceVo.discountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.discountPrice != '0') || (item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0') || (item.goodsInfo.estimatedPriceVo.coupon && item.goodsInfo.estimatedPriceVo.coupon != '0.0' && item.goodsInfo.estimatedPriceVo.coupon != '0'))"
									style="width: 65rpx; height: 28rpx;vertical-align: text-bottom;margin-right: 10rpx;"
									mode="aspectFill"
									:src="tradeStateGoodsIcon"
								/>
							</block>
							{{item.goodsInfo.name}}

						</view>
						<view>	
							<view 
								v-if="setData && setData.bottomPriceStyle == 2 && item.goodsInfo.surplus"
								style="position: relative; text-align: center;"
							>
								<image
									style="margin: auto; width: 304rpx; height: 69rpx;"
									mode="aspectFill"
									:src="setData.bottomPriceStyleUrls"
								/>
								<view
									:style="{
										position: 'absolute',
										top: '24rpx',
										left: '40rpx',
										width: '100rpx',
										color: `${setData.retailPriceColor}`
									}"
								>{{item.goodsInfo.estimatedPriceVo.originalPrice}}</view>
								<view
									:style="{
										position: 'absolute',
										top: '24rpx',
										left: '140rpx',
										width: '100rpx',
										color: `${setData.activityPriceColor}`
									}"
								>{{item.goodsInfogoodsInfo.estimatedPriceVo.estimatedPrice}}</view>
							</view>

							<view v-if="!setData||!setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle !== 2 ) || item.goodsInfo.spuType == 3 || item.goodsInfo.spuType == 5">
								<view>

									<!-- 买点 -->
									<!-- <view v-if="item.goodsInfo&&item.goodsInfo.sellPoint"  class="text-sm text-gray padding-left-sm overflow-2" style="color: #e5bd80;">
										{{item.goodsInfo.sellPoint}}
									</view> -->

									<!-- 标签 优惠券、功效 -->
									<view class="flex align-center couponBox padding-lr-sm ">
										<!-- 优惠券标签 -->
									<!-- 	<class-label
											v-if="item.goodsInfo && item.goodsInfo.estimatedPriceVo && item.goodsInfo.estimatedPriceVo.apiCouponNameIdVO && item.goodsInfo.estimatedPriceVo.apiCouponNameIdVO.name"
											:text='item.goodsInfo.estimatedPriceVo.apiCouponNameIdVO.name' 
											value='pinkBPink' 
											:marginRight='10'
											:borderRadius='6' 
										/> -->
										<!-- 功效标签 -->
										<template v-if="item.goodsInfo.goodsMarkInfoVo && item.goodsInfo.goodsMarkInfoVo.spuParametersList && item.goodsInfo.goodsMarkInfoVo.spuParametersList.length > 0">
											<class-label 
												v-for="(spuParameters,ind) in item.goodsInfo.goodsMarkInfoVo.spuParametersList"
												:key='ind' 
												v-if="ind < 3" 
												:text='spuParameters.optionValue' 
												value='greenBGreen'
												:marginRight='10' 
												:borderRadius='6' 
											/>
										</template>
									</view>

									<!-- 积分商品 -->
									<view v-if="item.goodsInfo.spuType == 3 || item.goodsInfo.spuType == 5" class="margin-top-xs margin-left-sm margin-bottom-sm">
										<text class="text-gray text-xs">积分：</text>
										<text class="text-red text-lg text-bold">{{item.goodsInfo.goodsPoints ? item.goodsInfo.goodsPoints : 0}}</text>
									</view>
									
									<!-- 付费商品 -->
									<view v-else class="padding-lr-sm margin-bottom-sm">
										<view
											v-if="(item && item.goodsInfo && item.goodsInfo.estimatedPriceVo) && ((item.goodsInfo.estimatedPriceVo.discountPrice && item.goodsInfo.estimatedPriceVo.discountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.discountPrice != '0') || (item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0.0' && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0') || (item.goodsInfo.estimatedPriceVo.coupon && item.goodsInfo.estimatedPriceVo.coupon != '0.0' && item.goodsInfo.estimatedPriceVo.coupon != '0'))"
										>
											<view class="flex" style="align-items: baseline;margin-top: 10rpx;">
                        <price-handle color="#FF4E00" :value="item.goodsInfo.estimatedPriceVo.estimatedPrice" signFont="24rpx" bigFont="40rpx" smallFont="28rpx"></price-handle>
												<view style="color: #F08800; font-size: 24rpx; margin-left: 8rpx;">
													券后价
												</view>
												<view class="text-price" style="color:#c1c0c6; font-size: 24rpx; text-decoration: line-through; margin-left: 8rpx;">
													{{ item.goodsInfo.estimatedPriceVo.originalPrice }}
												</view>
											</view>
											<!-- 立减 1-最近浏览商品 -->
											<text v-if="item.goodsInfo.estimatedPriceVo&&(item.goodsInfo.estimatedPriceVo.totalDiscountPrice&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0'&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0.0')" class="surplus">
												立减{{ item.goodsInfo.estimatedPriceVo.totalDiscountPrice }}元
											</text>
										</view>
										<view v-else class="text-bold" style="color: #FF4E00; font-size: 40rpx;">
											<text style="font-size: 24rpx;">￥</text>
											{{ item.goodsInfo.estimatedPriceVo.originalPrice ? item.goodsInfo.estimatedPriceVo.originalPrice : '0.00' }}
										</view>
										<!-- 买点 -->
										<view 
											style="color: #F08800; font-size: 24rpx;" 
											v-if="!item.goodsInfo.surplus && (item.goodsInfo && item.goodsInfo.sellPoint)"
										>
											{{ item.goodsInfo.sellPoint }}
										</view>
										
									</view>
									<!-- <view v-if="item.goodsInfo.inventoryState==1" class="padding-lr-sm text-balck text-xs margin-bottom-sm"
										style="font-weight: 500;font-size: 28rpx;">已售罄
									</view> -->
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 直播 2 -->
				<view 
					class="waterfall_item" 
					style="position: relative;" 
					:key="index"
					v-if="item.type == 2"  
					@click="goToPage(`plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${item.liveInfo.roomid || item.liveInfo.roomId}`)"
				>
					<image mode="aspectFill" :src="item.liveInfo.coverImg" />
					<view class="live-broadcast" style="padding: 0rpx;">
						<view class="playing-status">
							<img 
								style="width: 18rpx; height: 20rpx; margin-right: 10rpx;"
								src="https://img.songlei.com/live/waterfall/zhibozhong-shutiao.gif"
								alt=""
							/>
							<text>直播中</text>
						</view>
						<!-- 观看数量 -->
						<!-- <view class="margin-left-xs">3.35万观看</view> -->
					</view>
					<view class="flex align-center justify-between store-info">
						<view class="flex align-center">
							<image 
								class="shop-logo"
								:src="item.shopLogo || item.liveInfo.shareImg"
								mode="aspectFill"
								alt=""
							/>
							<text class="ellipsis" style="width: 200rpx;">{{item.liveInfo.name ? item.liveInfo.name : '店铺名称'}}</text>
						</view>
						<image 
							style="width: 38rpx; height: 38rpx;"
							src="https://img.songlei.com/live/waterfall/play.png"
							mode="aspectFill"
							alt=""
						/>
					</view>
				</view>

				<!-- 店铺 3 -->
				<view 
					class="waterfall_item" 
					style="position: relative;" 
					:key="index"
					v-if="item.type == 3" 
					@click="goToPage(`/pages/shop/shop-detail/index?id=${item.id}`)"
				>
					<image mode="widthFix" :src="item.url" />
					<view class="flex align-center justify-between store-info">
						<view class="flex align-center">
							<image 
								class="shop-logo"
								style=""
								:src="item.shopLogo"
								mode="aspectFill"
								alt=""
							/>
							<text class="ellipsis">{{item.name ? item.name : '店铺名称'}}</text>
						</view>
					</view>
				</view>

				<!-- 猜你想看更多 4-商品分类 -->
				<view 
					class="waterfall_item" 
					style="background: #FFF8EB; padding-bottom: 20rpx;" 
					:key="index"
					v-if="item.type == 4"
				>
					<view class="classification-title">
						<image 
							style="width: 50rpx; height: 50rpx; margin-right: 10rpx;"
							mode="aspectFill"
							src="https://img.songlei.com/live/waterfall/rat.png"
							alt=""
						/>
						<text>猜你想看更多</text>
					</view>
					<view class="classification-subheading">选一下，获得更精准推荐</view>
					<view class="classification">
						<view 
							class="classification-item" 
							v-for="classification in item.goodsCategories" 
							:key="classification.id" 
							@click="goToPage(`/pages/goods/goods-list/index?title=${classification.name}&`, item.type, classification)"
						>
							<image 
								style="width: 75rpx; height: 75rpx; border-radius: 10rpx;"
								:src="classification.picUrl ? classification.picUrl : 'https://img.songlei.com/live/waterfall/class-default.jpg'"
								mode="aspectFill"
								alt=""
							/>
							<text class="ellipsis" style="width: 210rpx;">{{classification.levelName}}</text>
						</view>
					</view>
					<view class="more-btn" @click="goToPage(`/pages/goods/goods-category/index?id=1599241559719714817`)">更多 ></view>
				</view>

				<!-- 视频 5 -->
				<view 
					class="waterfall_item" 
					:key="index" 
					v-if="item.type == 5" 
					@click="goToPage(`/pages/shop/shop-detail/index?id=${item.id}`)"
					style="position: relative;"
					id="shopVideoBox"
				>
					<video 
						:style="{'width': '100%', 'height': item.videoHeight ?  (303 / item.videoWidth) * item.videoHeight + 'rpx' : '500rpx'}"
						class="shop-video"
						id="shopVideo"
						:src="item.url"
						:controls="false" 
						:muted="true" 
						:object-fit="'cover'"
						:show-center-play-btn="false"
					/>
					<view v-if="item.videoTime" class="live-broadcast" style="padding: 0 13rpx; line-height: 34rpx;">
						<image 
							style="width: 13rpx; height: 17rpx; margin-right: 8rpx;"
							src="https://img.songlei.com/live/waterfall/play-white.png"
							mode="aspectFill"
							alt=""
						/>
						{{item.videoTime | formatTime}}
					</view>
					<view class="flex align-center justify-between store-info">
						<view class="flex align-center">
							<image 
								class="shop-logo"
								:src="item.shopLogo"
								mode="aspectFill"
								alt=""
							/>
							<text class="ellipsis">{{item.name ? item.name : '店铺名称'}}</text>
						</view>
						<view class="flex align-center"> 
							<image 
								style="width: 21rpx; height: 21rpx; margin-right: 10rpx;"
								src="https://img.songlei.com/live/waterfall/zan.png"
								mode="aspectFill"
								alt=""
							/>
							<!-- 点赞数 -->
							<view v-if="item.likeNum" style="font-size: 21rpx; color: #959595;">{{ item.likeNum }}</view>
						</view>
					</view>
				</view>
					<!-- 广告  6-->
	              <view
					class="waterfall_item"
					style="border-radius: 0; background-color: transparent;"
					:key="index"
					v-if="item.type=='6'"
					@click="toPage(item.linkUrl)"
				>	
					<image 
						:src="item.url" 
						:style="{ 
							width: '100%',
							borderRadius: (item.themeImgRadius === '1') ? '15px' : '0' 
						}"
						mode="widthFix"
						@load="handleLoaded(index)" 
						@error='handleLoaded(index)' 
						
					/>
				</view>
				
			</template>
		</view>

		<view 
			class="waterfall_right" 
			:style="{
				paddingRight: `${goodsSpace * 2 + 4}rpx`,
				paddingLeft: `${goodsSpace * 2}rpx`,
				boxSizing: 'border-box',
			}"
		>
			<template v-for="(item,index) in listRight">
				<!-- 天天特卖 -->
				<view
					class="waterfall_item" 
					style="border-radius: 0; background-color: transparent;"
					:key="index"
					v-if="item.isSpecial=='1'"
					@click="toPage(item.specialUrl)"
				>	
					<image 
						:src="item.specialImgUrl| formatImg360" 
						:style="{ 
							width: '100%',
							borderRadius: (item.specialImgRadius === '1') ? '15px' : '0' 
						}"
						mode="widthFix"
						@load="handleLoaded(index)" 
						@error='handleLoaded(index)' 
					/>
				</view>

				<!-- 主题图片 -->
				<view
					class="waterfall_item"
					style="border-radius: 0; background-color: transparent;"
					:key="index"
					v-if="item.isTheme=='1'"
					@click="toPage(item.themeUrl)"
				>	
					<image 
						:src="item.themeImgUrl| formatImg360" 
						:style="{ 
							width: '100%',
							borderRadius: (item.themeImgRadius === '1') ? '15px' : '0' 
						}"
						mode="widthFix"
						@load="handleLoaded(index)" 
						@error='handleLoaded(index)' 
						
					/>
				</view>

				<!-- 商品 1-->
				<view 
					class="waterfall_item" 
					:key="index"
					v-if="item.type == 1 && item.goodsInfo.id > 0" 
					@click="handleClick(item.goodsInfo)"
				>
					<view 
						class="item_img" 
						:class="((item && item.goodsInfo && item.goodsInfo.estimatedPriceVo) && ((item.goodsInfo.estimatedPriceVo.discountPrice && item.goodsInfo.estimatedPriceVo.discountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.discountPrice != '0') || (item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0') || (item.goodsInfo.estimatedPriceVo.coupon && item.goodsInfo.estimatedPriceVo.coupon != '0.0' && item.goodsInfo.estimatedPriceVo.coupon != '0')))  && spuPriceStyle==2 ? 'img-border' : ''"
						:style="{
							borderColor,
						}"
					>
					
						<!-- <image 
							v-show="item.goodsInfo.picUrls.length"
							:src="item.goodsInfo.picUrls[0] | formatImg360" 
							:style="{height: `${fullWidth/2-4}rpx`}" 
							mode="widthFix"  
							@load="handleLoaded(index)" 
							@error='handleLoaded(index)' 
						/> -->
						<lazy-load 
								:borderRadius="12"
								:image="item.goodsInfo.picUrls[0] | formatImg360"
								@load="handleLoaded(index)" 
								@error='handleLoaded(index)' 
								>
						</lazy-load>
						
						<member-icon v-if="item.goodsInfo.memberLevelLimit&&item.goodsInfo.memberLevelLimit!='101'" :memberLevelLimit = "item.goodsInfo.memberLevelLimit">
						</member-icon>

						<view
							class="card-icon"
							v-if="setData"
							:style="{
								top: `${setData.imgTop * 2}rpx`,
								left: `${setData.imgLeft * 2}rpx`,
								zIndex: '0'
							}"
						>
							<view class="card-icon">
								<image
									v-if="setData.leftShowMark == 1"
									style=" width: 67rpx; height: 70rpx;"
									mode="aspectFill"
									:src="setData.imageUrls"
								/>

								<image
									v-if="setData.leftShowMark == 2 && item.goodsInfo.Top"
									style=" width: 67rpx; height: 70rpx;"
									mode="aspectFill"
									:src="item.goodsInfo.Top ? $imgUrl('waterfall/TOP/TOP${item.goodsInfo.Top}.png') : ''"
								/>
							</view>
						</view>

						<view
							class="right-card-icon"
							v-if="setData"
							:style="{
								top: `${setData.rightImgTop * 2}rpx`,
								right: `${setData.rightImgTop * 2}rpx`,
								zIndex: '0'
							}"
						>
							<view class="right-card-icon">
								<image
									v-if="item.goodsInfo.estimatedPriceVo&&(item.goodsInfo.estimatedPriceVo.totalDiscountPrice&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0'&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0.0') && setData.rightImageUrls"
									style=" width: 67rpx; height: 70rpx;"
									mode="aspectFill"
									:src="setData.rightImageUrls"
								/>

								<view
									v-if="item.goodsInfo.estimatedPriceVo&&(item.goodsInfo.estimatedPriceVo.totalDiscountPrice&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0'&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0.0')&& setData.rightImageUrls&& setData.ShowPrice == 1"
									class="text-bold text-white text-sm"
									:style="{
										position: 'absolute',
										bottom: '-2rpx',
										right: '0rpx',
										width: '65rpx',
										height: '35rpx',
										lineHeight: '35rpx',
										textAlign: 'center',
										fontSize: `${setData.topPriceSize*2}rpx`,
										color:`${setData.PriceColor}`
									}"
								>{{item.goodsInfo.estimatedPriceVo.totalDiscountPrice}}</view>
							</view>
						</view>

						<view v-if="!setData || (setData && (!setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle == 1)))">	
							<view 
								style="position: absolute;" 
								:style="{
									top: spuPriceStyle == 1 ? 0 : 'auto',
									bottom: spuPriceStyle == 1 ? 0 : '-10rpx',
									left: spuPriceStyle == 1 ? 0 : '-6rpx',
									right: spuPriceStyle == 1 ? 0 : '-6rpx'
								}" 
								v-if="(item && item.goodsInfo && item.goodsInfo.estimatedPriceVo) && ((item.goodsInfo.estimatedPriceVo.discountPrice && item.goodsInfo.estimatedPriceVo.discountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.discountPrice != '0') || (item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0') || (item.goodsInfo.estimatedPriceVo.coupon && item.goodsInfo.estimatedPriceVo.coupon != '0.0' && item.goodsInfo.estimatedPriceVo.coupon != '0'))"
							>
								<good-price :goodsSpu="item.goodsInfo" :customCoverImage="customCoverImage"/>
							</view>
						</view>
						<view class="sell-out" v-if="item.goodsInfo.inventoryState == 1">
							<view class="sell-out-text">售罄</view>
						</view>
					</view>

					<view class="item_info">
						<view class="text-black  padding-lr-sm overflow-2">
						<block v-if="showimage">
							<image
									v-if="tradeStateGoodsIcon&&(item && item.goodsInfo && item.goodsInfo.estimatedPriceVo) && ((item.goodsInfo.estimatedPriceVo.discountPrice && item.goodsInfo.estimatedPriceVo.discountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.discountPrice != '0') || (item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0') || (item.goodsInfo.estimatedPriceVo.coupon && item.goodsInfo.estimatedPriceVo.coupon != '0.0' && item.goodsInfo.estimatedPriceVo.coupon != '0'))"
									style="width: 65rpx; height: 28rpx;vertical-align: text-bottom;margin-right: 10rpx;"
									mode="aspectFill"
									:src="tradeStateGoodsIcon"
								/>
							</block>
							{{item.goodsInfo.name}}
						</view>

						<view>	
							<view 
								v-if="setData && setData.bottomPriceStyle && setData.bottomPriceStyle !== 1 && item.goodsInfo.surplus"
								style="position: relative; text-align: center;"
							>
								<image
									style="margin: auto; width: 304rpx; height: 69rpx;"
									mode="aspectFill"
									:src="setData?setData.bottomPriceStyleUrls:''"
								/>
								<view
									:style="{
										position: 'absolute',
										top: '24rpx',
										left: '40rpx',
										width: '100rpx',
										color: `${setData.retailPriceColor}`
									}"
								>{{item.goodsInfo.estimatedPriceVo.originalPrice}}</view>
								<view
									:style="{
										position: 'absolute',
										top: '24rpx',
										left: '140rpx',
										width: '100rpx',
										color:`${setData.activityPriceColor}`
									}"
								>{{item.goodsInfo.estimatedPriceVo.estimatedPrice}}</view>
							</view>
						
							<view v-if="!setData||!setData.bottomPriceStyle || (setData.bottomPriceStyle && setData.bottomPriceStyle !== 2) || item.goodsInfo.spuType == 3 || item.goodsInfo.spuType == 5">
								
								<!-- 买点 -->
								<!-- <view v-if="item.goodsInfo&&item.goodsInfo.sellPoint"  class="text-sm text-gray padding-left-sm overflow-2" style="color: #e5bd80;">
									{{item.goodsInfo.sellPoint}}
								</view> -->
								
								<!-- 标签 -->
								<view class="flex align-center couponBox padding-lr-sm ">
									<!-- 优惠券标签 -->
									<!-- <class-label
										v-if="item.goodsInfo && item.goodsInfo.estimatedPriceVo && item.goodsInfo.estimatedPriceVo.apiCouponNameIdVO && item.goodsInfo.estimatedPriceVo.apiCouponNameIdVO.name"
										:text='item.estimatedPriceVo.apiCouponNameIdVO.name' 
										value='pinkBPink' 
										:marginRight='10'
										:borderRadius='6' 
									/> -->
									<!-- 功效标签 -->
									<template v-if="item.goodsInfo.goodsMarkInfoVo && item.goodsInfo.goodsMarkInfoVo.spuParametersList && item.goodsInfo.goodsMarkInfoVo.spuParametersList.length > 0">
										<class-label 
											v-for="(spuParameters,ind) in item.goodsInfo.goodsMarkInfoVo.spuParametersList"
											:key='ind' 
											v-if="ind < 3" 
											:text='spuParameters.optionValue' 
											value='greenBGreen'
											:marginRight='10' 
											:borderRadius='6' 
										/>
									</template>
								</view>
								<!-- 积分商品 -->
								<view v-if="item.goodsInfo.spuType == 3 || item.goodsInfo.spuType == 5" class="margin-top-xs margin-left-sm margin-bottom-sm">
									<text class="text-gray text-xs">积分：</text>
									<text class="text-red text-lg text-bold">{{item.goodsInfo.goodsPoints ? item.goodsInfo.goodsPoints : 0}}</text>
								</view>
								<!-- 付费商品 -->
								<view v-else class="padding-lr-sm margin-bottom-sm">
									<view
										v-if="(item && item.goodsInfo && item.goodsInfo.estimatedPriceVo) && ((item.goodsInfo.estimatedPriceVo.discountPrice && item.goodsInfo.estimatedPriceVo.discountPrice != '0.0' && item.goodsInfo.estimatedPriceVo.discountPrice != '0') || (item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0.0' && item.goodsInfo.estimatedPriceVo.promotionsDiscountPrice !='0') || (item.goodsInfo.estimatedPriceVo.coupon && item.goodsInfo.estimatedPriceVo.coupon != '0.0' && item.goodsInfo.estimatedPriceVo.coupon != '0'))"
									>
										<view class="flex" style="align-items: baseline;margin-top: 10rpx;">
                      <price-handle color="#FF4E00" :value="item.goodsInfo.estimatedPriceVo.estimatedPrice" signFont="24rpx" bigFont="40rpx" smallFont="28rpx"></price-handle>
											<view style="color: #F08800; font-size: 24rpx; margin-left: 8rpx;">
												券后价
											</view>
											<view class="text-price" style="color:#c1c0c6; font-size: 24rpx; text-decoration: line-through; margin-left: 8rpx;">
												{{ item.goodsInfo.estimatedPriceVo.originalPrice }}
											</view>
										</view>
										<!-- 立减 -->
										<text v-if="item.goodsInfo.estimatedPriceVo&&(item.goodsInfo.estimatedPriceVo.totalDiscountPrice&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0'&&item.goodsInfo.estimatedPriceVo.totalDiscountPrice!='0.0')" class="surplus">
											立减{{ item.goodsInfo.estimatedPriceVo.totalDiscountPrice }}元
										</text>
									</view>
									<view v-else class="text-bold" style="color: #FF4E00; font-size: 40rpx;">
										<text style="font-size: 24rpx;">￥</text>
										{{ item&&item.goodsInfo&&item.goodsInfo.estimatedPriceVo&&item.goodsInfo.estimatedPriceVo.originalPrice ? item.goodsInfo.estimatedPriceVo.originalPrice : '0.00' }}
									</view>
									<!-- 买点 -->
									<view 
										style="color: #F08800; font-size: 24rpx;" 
										v-if="!item.goodsInfo.surplus && (item.goodsInfo && item.goodsInfo.sellPoint)"
									>
										{{ item.goodsInfo.sellPoint }}
									</view>
								</view>
								<!-- <view v-if="item.goodsInfo.inventoryState==1" class="padding-lr-sm text-balck text-xs margin-bottom-sm" style="font-weight: 500;font-size: 28rpx;">
									已售罄
								</view> -->
							</view>
						</view>
					</view>
				</view>

				<!-- 直播 2 -->
				<view 
					class="waterfall_item" 
					style="position: relative;" 
					:key="index" 
					v-if="item.type == 2" 
					@click="goToPage(`plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${item.liveInfo.roomid || item.liveInfo.roomId}`)"
				>
					<image 
						mode="aspectFill" 
						:src="item.liveInfo.coverImg"
						@load="handleLoaded(index)" 
						@error='handleLoaded(index)' 
					/>
					<view class="live-broadcast" style="padding: 0rpx;">
						<view class="playing-status">
							<img 
								style="width: 18rpx; height: 20rpx; margin-right: 10rpx;"
								src="https://img.songlei.com/live/waterfall/zhibozhong-shutiao.gif"
								alt=""
							/>
							<text>直播中</text>
						</view>
						<!-- 观看数量 -->
						<!-- <view class="margin-left-xs">3.35万观看</view> -->
					</view>
					<view class="flex align-center justify-between store-info">
						<view class="flex align-center">
							<image 
								class="shop-logo"
								:src="item.shopLogo || item.liveInfo.shareImg"
								mode="aspectFill"
								alt=""
							/>
							<text class="ellipsis"  style="width: 200rpx;">{{item.liveInfo.name ? item.liveInfo.name : '店铺名称'}}</text>
						</view>
						<image 
							style="width: 38rpx; height: 38rpx;"
							src="https://img.songlei.com/live/waterfall/play.png"
							mode="aspectFill"
							alt=""
						/>
					</view>
				</view>

				<!-- 店铺 3 -->
				<view 
					class="waterfall_item" 
					style="position: relative;" 
					:key="index" 
					v-if="item.type == 3" 
					@click="goToPage(`/pages/shop/shop-detail/index?id=${item.id}`)"
				>
					<image mode="widthFix" :src="item.url" />
					<view class="flex align-center justify-between store-info">
						<view class="flex align-center">
							<image 
								class="shop-logo"
								style=""
								:src="item.shopLogo"
								mode="aspectFill"
								alt=""
							/>
							<text class="ellipsis">{{item.name ? item.name : '店铺名称'}}</text>
						</view>
					</view>
				</view>

				<!-- 猜你想看更多 4-商品分类 -->
				<view 
					class="waterfall_item" 
					style="background: #FFF8EB; padding-bottom: 20rpx;" 
					:key="index"
					v-if="item.type == 4" 
				>
					<view class="classification-title">
						<image 
							style="width: 50rpx; height: 50rpx; margin-right: 10rpx;"
							mode="aspectFill"
							src="https://img.songlei.com/live/waterfall/rat.png"
							alt=""
							@load="handleLoaded(index)" 
							@error='handleLoaded(index)'
						/>
						<text>猜你想看更多</text>
					</view>
					<view class="classification-subheading">选一下，获得更精准推荐</view>
					<view class="classification">
						<view 
							class="classification-item" 
							v-for="classification in item.goodsCategories" 
							:key="classification.id" 
							@click="goToPage(`/pages/goods/goods-list/index?title=${classification.name}&`, item.type, classification)"
						>
							<image 
								style="width: 75rpx; height: 75rpx; border-radius: 10rpx;"
								:src="classification.picUrl ? classification.picUrl : 'https://img.songlei.com/live/waterfall/class-default.jpg'"
								mode="aspectFill"
								alt=""
							/>
							<text class="ellipsis" style="width: 200rpx;">{{classification.levelName}}</text>
						</view>
					</view>
					<view class="more-btn" @click="goToPage(`/pages/goods/goods-category/index?id=1599241559719714817`)">更多 ></view>
				</view>

				<!-- 视频 5 -->
				<view 
					class="waterfall_item" 
					:key="index" 
					v-if="item.type == 5" 
					@click="goToPage(`/pages/shop/shop-detail/index?id=${item.id}`)"
					style="position: relative;"

					id="shopVideoBox"
				>
					<video 
						:style="{'width': '100%', 'height': item.videoHeight ?  (303 / item.videoWidth) * item.videoHeight + 'rpx' : '500rpx'}"
						class="shop-video"
						id="shopVideo"
						:src="item.url"
						:controls="false" 
						:muted="true" 
						:object-fit="'cover'"
						:show-center-play-btn="false"
						:loop="true"
					/>
					<view v-if="item.videoTime" class="live-broadcast" style="padding: 0 13rpx; line-height: 34rpx;">
						<image 
							style="width: 13rpx; height: 17rpx; margin-right: 8rpx;"
							src="https://img.songlei.com/live/waterfall/play-white.png"
							mode="aspectFill"
							alt=""
							@load="handleLoaded(index)" 
							@error='handleLoaded(index)'
						/>
						{{item.videoTime | formatTime}}
					</view>
					<view class="flex align-center justify-between store-info">
						<view class="flex align-center">
							<image 
								class="shop-logo"
								:src="item.shopLogo"
								mode="aspectFill"
								alt=""
							/>
							<text class="ellipsis">{{item.name ? item.name : '店铺名称'}}</text>
						</view>
						<view class="flex align-center"> 
							<image 
								style="width: 21rpx; height: 21rpx; margin-right: 10rpx;"
								src="https://img.songlei.com/live/waterfall/zan.png"
								mode="aspectFill"
								alt=""
							/>
							<!-- 点赞数 -->
							<view v-if="item.likeNum" style="font-size: 21rpx; color: #959595;">{{ item.likeNum }}</view>
						</view>
					</view>
				</view>


				<!-- 广告 -->
				<view
					class="waterfall_item"
					style="border-radius: 0; background-color: transparent;"
					:key="index"
					v-if="item.type=='6'"
					@click="toPage(item.linkUrl)"
				>	
					<image 
						:src="item.url" 
						:style="{ 
							width: '100%',
							borderRadius: (item.themeImgRadius === '1') ? '15px' : '0' 
						}"
						mode="widthFix"
						@load="handleLoaded(index)" 
						@error='handleLoaded(index)' 
						
					/>
				</view>
			</template>
		</view>
	 </view>
</template>
<script>
	import goodPrice from "components/good-price/good-price.vue";
	import lazyLoad from "components/lazy-load/index";
	import memberIcon from '@/components/member-icon/index.vue';

	import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"
	const app = getApp();
	export default {
		name: "waterfall",
		mixins: [navigateUtil],
		components: {
			goodPrice,
			lazyLoad,
			memberIcon
		},
		props: {
			list: {
				type: Array,
				required: true,
				default: []
			},
			goodsSpace: {
				type: String | Number,
				default: 3
			},
			fullWidth: {
				type: Number,
				default: 750
			},
			loadMore: {
				type: Boolean,
				default: false
			},
			setData: {
				type: Object,
				default: () => { }
			},
			showimage:{
				type: Boolean,
				default: true
			},
			customCoverImage: {
				type: String | Number,
				default: "",
			},
			
			borderRadiusLeftTop: {
				type: String | Number,
				default: 12,
			},
			
			borderRadiusRightTop: {
				type: String | Number,
				default: 12,
			},
		},
		filters: {
			formatTime(time) {
				let secondTime = parseInt(time);// 秒
        let minuteTime = 0;// 分
				let hourTime = 0;// 小时
				let result = ''

				//如果秒数大于60，将秒数转换成整数
				if(secondTime > 60) {
					//获取分钟，除以60取整数，得到整数分钟
					minuteTime = parseInt(secondTime / 60);
					//获取秒数，秒数取佘，得到整数秒数
					secondTime = parseInt(secondTime % 60);

					//如果分钟大于60，将分钟转换成小时
					if(minuteTime > 60) {
						//获取小时，获取分钟除以60，得到整数小时
						hourTime = parseInt(minuteTime / 60);
						//获取小时后取佘的分，获取分钟除以60取佘的分
						minuteTime = parseInt(minuteTime % 60);
					}
        }

				// 补零
				const zeroFill = (num) =>{
					if(num < 10) return '0' + num
					return num
				}

				if (hourTime) {
					result = zeroFill(hourTime) + ':' + zeroFill(minuteTime) + ':' + zeroFill(secondTime)
				} else {
					result = zeroFill(minuteTime) + ':' + zeroFill(secondTime)
				}

				

				return result
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				listLeft: [],
				listRight: [],
				newList: [],
			}
		},
		computed: {
			borderColor: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.spuPriceStyle == 1 ? '' : customFiled.spuPicBorder
				}
				return "#d9bda5"
			},
			spuPriceColor: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					if (customFiled.spuPriceStyle == 1) {
						return customFiled.spuPriceColor1 || '#fff'
					}
					return customFiled.spuPriceColor2 || '#fff'
				}
				return "#fff"
			},

			spuPriceStyle: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.spuPriceStyle || 2
				}
				return 2
			},

			tradeStateGoodsIcon: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.tradeStateGoodsIcon? customFiled.tradeStateGoodsIcon:''
				}
				return ""
			},
		},

		watch: {
			list(newValue, oldValue) {
				if(newValue.length) {
					this.considerPush();
				}
			},
		},

		async mounted() {
			this.init()

			// 视频进入可视区域自动播放
			const screenHeight = uni.getSystemInfoSync().windowHeight; // 获取可是区域的高度
			const videoDoms = uni.createSelectorQuery().in(this).select('#shopVideoBox') // 获取视频dom节点
			const video = uni.createVideoContext('shopVideo', this) // 创建视频播放
			let domData = null // dom节点位置信息数据
			let size = 0 // 是否进入可视区域
			uni.$on('vonVideoPageScroll', async (e) => {
				// 没有dom节点位置信息则获取，有则不获取
				if (!domData) {
					await videoDoms.boundingClientRect(data => {
						domData = data
					}).exec();
				}
				// 计算视频位置
				await (domData?.top && (size = domData.bottom - e.scrollTop))
				// 判断视频是否进入可视区域
				if (screenHeight > size && size > 0) {
					video.play() // 视频播放
				} else {
					video.pause() // 视频暂停
				}
			});
		},
		

		methods: {
			// 触发重新排列
			init() {
				this.listLeft = [];
				this.listRight = [];
				this.considerPush();
			},
			// 清空数据列表
			clear() {
				this.listLeft = [];
				this.listRight = [];
			},

			// 计算排列
			considerPush() {
        let isSwiper = false
				const total = this.listLeft.length + this.listRight.length;
				const list = this.list.slice(total)
				list.forEach((item, index) => {
					// 计算立减价格，记录商品顺序
					if (item.type !== 'swiper' && item.type == 1) {
						let surplus = ''
						if (item.goodsInfo.estimatedPriceVo) {
							if (item.goodsInfo.estimatedPriceVo.originalPrice != item.goodsInfo.estimatedPriceVo.estimatedPrice) {
								let price = Number(item.goodsInfo.estimatedPriceVo.originalPrice - item.goodsInfo.estimatedPriceVo.estimatedPrice).toFixed(2)
								surplus = price
								this.$set(list[index].goodsInfo, 'surplus', surplus)
							}
						}
						
						this.$set(list[index], 'Top', isSwiper ? (index < 11 ? index:'') : (index + 1 < 11 ? index + 1 : ''))
					} else {
            isSwiper = true;
          }
					if (index % 2 == 0) {
						this.listLeft.push(item)
					} else {
						this.listRight.push(item)

					}
				})
			},
      handleLoaded(index){
			  // 最后一个左侧可能比右侧多出来一个半，要平衡下
			  if(this.listRight.length > 1 && !this.loadMore && index == this.listRight.length - 1) {
				  let leftH = 0, rightH = 0; //左右高度
				  const query = uni.createSelectorQuery().in(this);
				  query.selectAll('.waterfall_left').boundingClientRect()
				  query.selectAll('.waterfall_right').boundingClientRect()
				  query.exec(res => {
				  	leftH = res[0].length != 0 ? res[0][0].height : 0; //防止查询不到做个处理
				  	rightH = res[1].length != 0 ? res[1][0].height : 0;
				  	if (leftH > rightH + 303) {
							// 左侧高度比右侧高出来一个多的话做处理
				  		this.listRight.push(this.listLeft.pop()); 
				  	} 
						// else if (leftH < rightH - 303) {
						// 	// 右侧高度比左侧高出来一个多的话做处理
				  	// 	this.listLeft.push(this.listRight.pop());
						// }
				  });
			  }
			},
			handleClick(item) {
				this.$emit('click', item);
			},
				handleClicks(url) {
					uni.navigateTo({url})
					uni.switchTab({url})
				},
			// 页面跳转
			goToPage(url, type, data) {
				if(type == 4) {
					let category = ''
					switch(data.hierarchy) {
						case '1':
							category = `categoryFirst=${data.id}`
							break;
						case '2':
							category = `categorySecond=${data.id}`
							break;
						case '3':
							category = `categoryThird=${data.id}`
							break;
						case '4':
							category = `categoryFourth=${data.id}`
							break;
						case '5':
							category = `categoryFifth=${data.id}`
							break;
					}
					uni.navigateTo({url: url + category})
					return
				}
				uni.navigateTo({url: url})
			}
			
		}

	}
</script>
<style lang="scss" scoped>
.card-icon {
  width: 67rpx;
  height: 70rpx;
  position: absolute;
  top: 0rpx;
  left: 0rpx;
	z-index: 0;
}

.right-card-icon {
  width: 67rpx;
  height: 70rpx;
  position: absolute;
  top: 0rpx;
  right: 0rpx;
	z-index: 0;
}
	.container {
		display: flex;
		flex-flow: row nowrap;
		justify-content: space-around;
		align-items: flex-start;
	}

	.waterfall_left,
	.waterfall_right {
		width: 50%;
	}

	.waterfall_item {
		width: 100%;
		margin: 10rpx 0 20rpx 0;
		background-color: #FFFFFF;
		border-radius: 15rpx;
		overflow: hidden;

		.item_img {
			width: 100%;
			position: relative;

			image {
				width: 100%;
				overflow: hidden;
			}
		}
	}
	
	.item_info {
		margin-top: 10rpx;
	}

	/* 优惠券部分 */
	.couponBox {
		line-height: 26rpx;
	}
	
	.img-border {
		border-left-width: 6rpx ;
		border-right-width: 6rpx ;
		border-top-width: 6rpx ;
		border-style: solid;
		border-color: transparent;
	}

	// 商品-立减
	.surplus{
		font-size: 20rpx; 
		color: #F08800; 
		border: 1rpx solid #F08800; 
		padding: 5rpx 8rpx; 
		border-radius: 5px;
		// margin-top: 12rpx;
	}

	// 直播
	.live-broadcast{
		position: absolute; 
		top: 0;
		z-index: 999; 
		height: 34rpx; 
		background: rgba(0,0,0,0.4); 
		border-radius: 17rpx; 
		padding: 0 15rpx 0 5rpx; 
		display: flex; 
		align-items: center;
		font-size: 20rpx;
		color: #fff;
		margin-top: 13rpx;
		margin-left: 15rpx;
		.playing-status{
			height: 32rpx; 
			background: #FF2B61; 
			border-radius: 17rpx; 
			display: flex; 
			align-items: center; 
			padding: 0 10rpx;
		}
	}
	.store-info {
		height: 50rpx; 
		margin: 12rpx 0 20rpx;
		padding: 0 15rpx 0 17rpx;
		font-size: 24rpx;
		
		.shop-logo{
			border: 1rpx solid gray; 
			width: 50rpx; 
			height: 50rpx; 
			margin-right: 10rpx; 
			border-radius: 50%;
		}
	}
	.ellipsis{
		width: 180rpx; 
		white-space: nowrap; 
		overflow: hidden; 
		text-overflow: ellipsis;
	}

	// 猜你想看更多
	.classification-title{
		font-size: 30rpx;
		font-weight: bold;
		color: #000000;
		display: flex;
		align-items: center;
		margin: 14rpx 0 0 18rpx;
	}
	.classification-subheading{
		font-size: 24rpx;
		color: #808080;
		margin: 8rpx 0 0 24rpx;
	}
	.classification{
		width: 100%;
		.classification-item{
			width: 314rpx;
			height: 93rpx;
			background: #FFFFFF;
			border-radius: 10px;
			margin: 20rpx auto 0;
			padding: 0 12rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 24rpx;
			color: #808080;
		}
	}
	.more-btn{
		width: 220rpx;
		height: 50rpx;
		background: #EE858A;
		border-radius: 25rpx;
		margin: 20rpx auto 0;
		font-size: 24rpx;
		font-weight: 400;
		color: #FEFEFE;
		line-height: 50rpx;
		text-align: center;
	}
	.waterfall_item{
		border-radius: 15rp;
	}
</style>
