<template>
  <view class="container">
    <view
      class="statusbar"
      style="background-color: red;"
      :style="{height: `${statusBarHeight}px`}"
    >
      <!-- 顶部 -->
    </view>
    <view
      class="navbar"
      style="background-color: red;"
      :style="{height: `${titleBarHeight}px`}"
    >首页
      <!-- 导航栏 -->
    </view>

    <!-- <view>
        <view class="status_bar">
          <view style="color:#000000">商品详情页</view>
        </view>
      </view> -->

    <view class="share">
      <view class="shareImgBox">
        <image
          v-if="shareImgUrl"
          :src="shareImgUrl.imgUrl"
          mode="widthFix"
        ></image>
        <view class="padding-top padding-bottom text-df">松鼠美淘</view>
        <view class=" text-sm text-gray">- 来自松鼠美淘 -</view>
      </view>
    </view>
  </view>
</template>

<script>
import api from 'utils/api'
export default {
  name: 'shareSinglePage',
  data () {
    return {
      titleBarHeight: '',
      statusBarHeight: '',
      shareImgUrl:''
    }

  },
  mounted () {
    let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
    uni.getSystemInfo({//获取系统信息
      success: res => {
        let navHeight = menuButtonObject.height + (menuButtonObject.top - res.statusBarHeight) * 2;//导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
        this.titleBarHeight = navHeight;
        this.statusBarHeight = res.statusBarHeight
      },
      fail (err) {
        console.log(err);
      }
    });
    this.advertisement('WECHAT_MOMENTS')
  },
  methods: {
    //朋友圈图片链接
			advertisement(id) {
				//朋友圈banner
				api.advertisementinfo({
						bigClass: id
					}).then(res => {
            this.shareImgUrl = res.data;
            console.log("shareImgUrl",this.shareImgUrl);
					});
			},
  },
}
</script>

<style lang="scss" scoped>
.status_bar {
  height: 118rpx;
  width: 100%;
  background-color: red;
}
.share {
  text-align: center;
  margin: 0rpx auto;
  // height: 1040rpx;
  width: 700rpx;
  padding: 60rpx 0rpx 0;
  box-shadow: 0 10rpx 10rpx #ccc;
} 
</style>