
<template>
	<view class="cu-modal bottom-modal" :class="modalCustomerService ? 'show' : ''" @tap="hideModal" catchtouchmove="touchMove">
		<view class="cu-dialog bg-white" :class="modalCustomerService ? 'animation-slide-bottom' : ''" @tap.stop>
			<view class="article">
				<view class="cu-item">
					<view style="padding-bottom: 30rpx; border-bottom: solid 1px #f1f1f1" class="content text-lg margin-top-sm">咨询商品及活动</view>
				</view>
				<view class="text-xl close-icon">
					<text class="cuIcon-close" @tap="hideModal"></text>
				</view>
			</view>
			<view class="flex" style="margin: 30rpx 0 30rpx 0">
				<view class="action bg-white" style="flex: 1; align-items: center" @click="handleCusServiceClick">
					<image style="width: 100rpx; height: 100rpx" :src="$imgUrl('live/customer-service.png')"></image>
					<view style="font-size: 24rpx; color: #777777; margin-top: 20rpx">专柜客服</view>
				</view>

				<view class="action bg-white" style="flex: 1; align-items: center" @click="handleGroupModal" v-if="wxworkQrcode">
					<image style="width: 100rpx; height: 100rpx" :src='$imgUrl('live/icon_group.png')'></image>
					<view style="font-size: 24rpx; color: #777777; margin-top: 20rpx">粉丝群</view>
				</view>

				<view class="action bg-white" @click="handleCall" style="flex: 1; align-items: center">
					<image style="width: 100rpx; height: 100rpx" :src="$imgUrl('live/customer-call.png')"></image>
					<view style="font-size: 24rpx; color: #777777; margin-top: 20rpx">专柜电话</view>
				</view>
			</view>
			<view class="cu-bar bg-white tabbar">
				<view class="btn-group">
					<button class="cu-btn round shadow-blur lg btn-confirm" :class="'bg-' + theme.themeColor" @tap="hideModal">取消</button>
				</view>
			</view>
		</view>
		<uni-popup ref="perpopup" type="center" :mask-click="false">
			<view class="permissions_box">当您使用APP时，拨打电话咨询商家时候需要拨打电话权限，不授权上述权限，不影响APP其他功能使用。</view>
		</uni-popup>
	</view>
</template>

<script>
const app = getApp();
import __config from '@/config/env';
import api from 'utils/api'
export default {
	data() {
		return {
			CustomBar: this.CustomBar,
			theme: app.globalData.theme //全局颜色变量
		};
	},
	onLoad() {},

	props: {
		modalCustomerService: {
			type: Boolean,
			default: false
		},
		shopId: {
			type: String | Number,
			default: ''
		},
		goodsSpuId: {
			type: String | Number,
			default: ''
		},
		goodsName: {
			type: String,
			default: '商品名称'
		},
		goodsPic: {
			type: String,
			default: '商品图片'
		},
		sharePic: {
			type: String,
			default: '分享图片'
		},
		phone: {
			type: String | Number,
			default: ''
		},
		wxCustomerUrl: {
			type: String,
			default: ''
		},
		wxworkQrcode: {
			type: String,
			default: ''
		}
	},

	methods: {
		touchMove() {
			return;
		},
		hideModal() {
			this.$emit('changeModal', false);
		},
		handleCall() {
			// #ifdef MP-WEIXIN
			this.callFun();
			// #endif
			// #ifdef APP-PLUS
			let that = this;
			var platform = uni.getSystemInfoSync().platform;
			if (platform == 'android') {
				plus.android.checkPermission(
					'android.permission.CALL_PHONE',
					(granted) => {
						if (granted.checkResult == -1) {
							//弹出
							that.$refs.perpopup.open('top');
						} else {
							//执行你有权限后的方法
							that.callFun();
						}
					},
					(error) => {
						console.error('Error checking permission:', error.message);
					}
				);
				plus.android.requestPermissions(['android.permission.CALL_PHONE'], (e) => {
					//关闭
					that.$refs.perpopup.close();
					if (e.granted.length > 0) {
						//执行你有权限后的方法
						that.callFun();
					}
				});
			} else {
				//执行你有权限后的方法 ios
				that.callFun();
			}
			// #endif
		},
		callFun() {
			if (this.phone > 0) {
				uni.makePhoneCall({
					phoneNumber: this.phone
				});
			} else {
				uni.makePhoneCall({
					phoneNumber: '4006171819'
				});
			}
		},
		async handleCusServiceClick() {
			let that = this;
			console.log('分享id---------', 'pages/goods/goods-detail/index?id=' + this.goodsSpuId);
			let wxCustomerUrl = this.wxCustomerUrl || app.globalData.wxCustomerUrl;
			if (!wxCustomerUrl) {
				const res = await api.getLivingTag();
				if (res && res.data) {
					wxCustomerUrl = res.data.platWxCustomerUrl;
					app.globalData.wxCustomerUrl = res.data.platWxCustomerUrl;
				}
			}
			if (wxCustomerUrl) {
				// #ifdef MP
				const sourceModule = encodeURIComponent('分享');
				wx.openCustomerServiceChat({
					extInfo: {
						url: wxCustomerUrl
					},
					corpId: __config.chatId,
					showMessageCard: true,
					sendMessageTitle: this.goodsName || '商品详情',
					sendMessagePath: 'pages/goods/goods-detail/index?id=' + this.goodsSpuId + '&source_module='+ sourceModule,
					sendMessageImg: this.goodsPic || this.sharePic,
					success(res) {}
				});
				// #endif
				// #ifdef APP
				uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					openCustomerServiceChat: true,
					corpid: __config.chatId,
					customerUrl: wxCustomerUrl,
					fail(err) {
						console.log('打开客服错误', err);
						that.handleCall();
					}
				});
				// #endif
			} else {
				uni.showToast({
					title: '请店铺客服先配置下客服链接',
					icon: 'none',
					duration: 2000
				});
			}
		},
		handleGroupModal() {
			console.log('==handleGroupModal==', this.wxworkQrcode);
			uni.previewImage({
				urls: [this.wxworkQrcode]
			});
		}
	}
};
</script>
<style>
.close-icon {
	position: absolute;
	right: 20rpx;
	top: 20rpx;
}

.btn-confirm {
	width: 96%;
}

.permissions_box {
	padding: 200rpx 30rpx 50rpx;
	background-color: #fff;
	color: #000000;
}
</style>
