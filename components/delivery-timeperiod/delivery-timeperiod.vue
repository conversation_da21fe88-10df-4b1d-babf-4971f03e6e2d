<template>
	<view class="time-period-container flex align-center" v-if="showAsync">
		<view class="text-df text-black">{{ title }}：</view>
		<picker mode="multiSelector" @change="bindMultiPickerChange" @columnchange="bindMultiPickerColumnChange" :value="multiIndex" :range="multiArray">
			<view class="picker">{{ multiArray[0][multiIndex[0]] }} {{ multiArray[1][multiIndex[1]] }}</view>
		</picker>
	</view>
</template>

<script>
import api from 'utils/api';
export default {
	props: {
		useraddresid: '',
		shopId: '',
		title: '',
		type: '' // 1自提   2 同城配送
	},
	data() {
		return {
			totalData: {
				toDay: [],
				nextDay: []
			},
			multiArray: [['今天', '明天'], ['立即送出']],
			dateArray: [],
			multiIndex: [0, 0],
			Selfliftingtime: ''
			// titles:""
		};
	},
	watch: {
		// 1自提   2 同城配送
		type: {
			handler(newVal, oldVal) {
				if (newVal == '1') {
					this.gettime();
				} else if (newVal == '2') {
					this.computedTime();
				}
			},
			immediate: true
		}
	},

	computed: {
		showAsync() {
			return (this.totalData.nextDay.length && this.totalData.toDay.length) || JSON.stringify(this.totalData) !== '{}';
		}
	},
	methods: {
		gettime() {
			api.gettime().then((res) => {
				this.Selfliftingtime = res.data;
				this.$set(this.multiArray, 0, Object.keys(res.data));
				this.$set(this.multiArray, 1, Object.values(res.data)[0]);
				this.$emit('change', Object.keys(res.data)[0] + ' ' + Object.values(res.data)[0][0], '1');
			});
		},
		computedTime() {
			api.deliverytimeperiod({
				userAddressId: this.useraddresid,
				shopId: this.shopId
			}).then((res) => {
				if (res && res.data) {
					this.dateArray = [];
					const dateToday = this.getFomatDate2(0);
					const weekToday = this.getDayOfWeek(this.getFomatDate(0));
					this.dateArray.push(`${dateToday}(${weekToday})`);
					const dateTomorrow = this.getFomatDate2(1);
					const weekTomorrow = this.getDayOfWeek(this.getFomatDate(1));
					this.dateArray.push(`${dateTomorrow}(${weekTomorrow})`);
					const afterTomorrow = this.getFomatDate(2);
					const weekAfterTomorrow = this.getDayOfWeek(afterTomorrow);
					this.dateArray.push(`${this.getFomatDate2(2)}(${weekAfterTomorrow})`);
					this.$set(this.multiArray, 0, [`今天(${weekToday})`, `明天(${weekTomorrow})`, `${this.getFomatDate2(2)}(${weekAfterTomorrow})`]);

					this.totalData.toDay = [];
					this.totalData.nextDay = [];
					res.data.toDay &&
						res.data.toDay.forEach((item) => {
							this.totalData.toDay.push(`${item.startTime}~${item.endTime}`);
						});
					res.data.nextDay &&
						res.data.nextDay.forEach((item) => {
							this.totalData.nextDay.push(`${item.startTime}~${item.endTime}`);
						});
					if (this.totalData.toDay.length) {
						this.$set(this.multiArray, 1, this.totalData.toDay);
					} else {
						this.$set(this.multiArray, 1, this.totalData.nextDay);
						this.multiArray[0].shift();
						this.dateArray.shift();
					}
					this.$emit('change', this.dateArray[this.multiIndex[0]] + ' ' + this.multiArray[1][this.multiIndex[1]], '2');
				}
			});
		},
		bindMultiPickerChange(e) {
			this.multiIndex = e.detail.value;
			if (this.title == '上门自提') {
				this.$emit(
					'change',
					Object.keys(this.Selfliftingtime)[this.multiIndex[0]] + ' ' + this.Selfliftingtime[Object.keys(this.Selfliftingtime)[this.multiIndex[0]]][this.multiIndex[1]],
					'1'
				);
			} else {
				this.$emit('change', this.dateArray[this.multiIndex[0]] + ' ' + this.multiArray[1][this.multiIndex[1]], '2');
			}
		},

		bindMultiPickerColumnChange(e) {
			const { column, value } = e.detail;
			if (this.title == '上门自提') {
				if (column == 0) {
					this.$set(this.multiArray, 1, Object.values(this.Selfliftingtime)[value]);
				}
			} else {
				if (column == 0) {
					if (value == 0) {
						this.totalData.toDay.length ? this.$set(this.multiArray, 1, this.totalData.toDay) : this.$set(this.multiArray, 1, this.totalData.nextDay);
					} else {
						this.$set(this.multiArray, 1, this.totalData.nextDay);
					}
				}
			}
		},

		getDayOfWeek(date) {
			var arr = new Array('日', '一', '二', '三', '四', '五', '六');
			var week = new Date(date).getDay();
			var str = '周' + arr[week];
			return str;
		},

		/**
		 * 获取时间，格式YYYY-MM-DD
		 *
		 * @num    {number}  今天之后的第几天
		 * @returns  日期
		 */
		getFomatDate(num) {
			var date = new Date(new Date().getTime() + 1000 * 60 * 60 * 24 * num);
			var seperator1 = '-';
			var year = date.getFullYear();
			var month = date.getMonth() + 1;
			var strDate = date.getDate();
			if (month >= 1 && month <= 9) {
				month = '0' + month;
			}
			if (strDate >= 0 && strDate <= 9) {
				strDate = '0' + strDate;
			}
			var currentdate = year + seperator1 + month + seperator1 + strDate;
			return currentdate;
		},

		/**
		 * 获取时间，格式X月X日
		 *
		 * @num    {number}  今天之后的第几天
		 * @returns  日期
		 */
		getFomatDate2(num) {
			var date = new Date(new Date().getTime() + 1000 * 60 * 60 * 24 * num);
			var seperator1 = '-';
			var year = date.getFullYear();
			var month = date.getMonth() + 1;
			var strDate = date.getDate();
			var currentdate = year + '年' + month + '月' + strDate + '日';
			return currentdate;
		}
	}
};
</script>

<style>
.time-period-container {
	display: flex;
	justify-content: space-between;
	border-radius: 20rpx;
	background-color: #fff;
}

.picker {
	color: orange;
}
</style>
