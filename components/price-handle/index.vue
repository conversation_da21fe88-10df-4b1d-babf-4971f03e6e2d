<template>
  <view :class="propClass" style="line-height: 1;" :style="{ color, fontWeight }">
    <slot name="prefix"></slot>
    <text
      class="price-handle-big-font price-text"
      v-if="signShow"
      :style="{
        color: `${signColor || color || ''}`,
        '--font': signFonts,
        '--base': basesignFont
      }"
    >￥</text>
    <text 
      class="price-handle-big-font price-text"
      :style="{
        color: `${bigColor || color || ''}`,
        '--font': bigFonts,
        '--base': baseBigFont
      }"
    >{{ integer }}</text>
    <text 
      class="price-handle-big-font price-text"
      v-if="decimal" 
      :style="{ 
        color: smallColor || color || '',
        '--font': smallFonts,
        '--base': baseSmallFont
      }"
    >{{ decimal }}</text>
    <slot name="append"></slot>
  </view>
</template>

<script>
export default {
  data() {
    return {
      integer: 0, // 整数
      decimal: '' // 小数
    }
  },
  /**
  *   功能: 整数和小数大小不同进行处理
  *   value: 传入的值(只能传入数值和数字字符, 必填)
  *   color: 整体传入的颜色(选填，会继承父元素的颜色)
  *   bigColor: 整数传入的颜色(选填，不填会继承color颜色，如color也没有，会继承父元素的颜色)
  *   signColor: ￥字符的颜色(选填，不填会继承color颜色，如color也没有，会继承父元素的颜色)
  *   smallColor: 小数传入的颜色(选填，不填会继承color颜色，如color也没有，会继承父元素的颜色)
  *   bigFont: 整数传入的字体(选填，默认值34rpx)
  *   smallFont: 小数传入的字体(选填，默认值26rpx)
  *   signFont: ￥字符的字体(选填，默认值26rpx)
  *   signShow: ￥字符是否显示(选填，默认值true)
  *   fontWeight: 传入的字体粗细大小(选填，默认值：600)
  *   pointTwo: 小数点位数(选填，默认是根据传入的值来的位数)
  * **/
  props: {
    value: {
      type: Number | String,
      default: 0
    },
    color: {
    	type: String,
    	default: ''
    },
    bigColor: {
      type: String,
      default: ''
    },
    smallColor: {
      type: String,
      default: ''
    },
    signColor: {
      type: String,
      default: ''
    },
    bigFont: {
    	type: String,
    	default: '34rpx'
    },
    smallFont: {
    	type: String,
    	default: '26rpx'
    },
    signFont: {
      type: String,
      default: '26rpx'
    },
    fontWeight: {
      type: String,
      default: 'bold'
    },
    signShow: {
      type: Boolean,
      default: true
    },
    pointTwo: {
      type: Number,
      default: -1
    },
    propClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    baseBigFont() {
      let bigFont = this.bigFont;
      bigFont = parseInt(bigFont);
      let font = 4;
      if (bigFont >= 38 && bigFont < 48) {
        font = 6;
        // return '6rpx';
      } else if(bigFont >= 48 && bigFont < 80) {
        font = 8;
        // return '8rpx';
      } else if(bigFont >= 80) {
        font = 0;
        // return '0rpx'
      }
      // #ifdef APP
      font = font / 2 + 'px'
      // #endif
      // #ifdef MP
      font += 'rpx';
      // #endif
      return font
    },
    baseSmallFont() {
      let bigFont = this.smallFont;
      bigFont = parseInt(bigFont);
      let font = 4;
      if (bigFont >= 38 && bigFont < 48) {
        font = 6;
        // return '6rpx';
      } else if(bigFont >= 48 && bigFont < 80) {
        font = 8;
        // return '8rpx';
      } else if(bigFont >= 80) {
        font = 0;
        // return '0rpx'
      }
      // #ifdef APP
      font = font / 2 + 'px'
      // #endif
      // #ifdef MP
      font += 'rpx';
      // #endif
      return font
    }
    ,
    basesignFont() {
      let bigFont = this.signFont;
      bigFont = parseInt(bigFont);
      let font = 4;
      if (bigFont >= 38 && bigFont < 48) {
        font = 6;
        // return '6rpx';
      } else if(bigFont >= 48 && bigFont < 80) {
        font = 8;
        // return '8rpx';
      } else if(bigFont >= 80) {
        font = 0;
        // return '0rpx'
      }
      // #ifdef APP
      font = font / 2 + 'px'
      // #endif
      // #ifdef MP
      font += 'rpx';
      // #endif
      return font
    },
    bigFonts() {
      let bigFont = this.bigFont;
      // #ifdef APP
      bigFont = parseInt(bigFont);
      bigFont = bigFont / 2 + 'px';
      // #endif
      return bigFont
    },
    smallFonts() {
      let smallFont = this.smallFont;
      // #ifdef APP
      smallFont = parseInt(smallFont);
      smallFont = smallFont / 2 + 'px';
      // #endif
      return smallFont
    },
    signFonts() {
      let signFont = this.signFont;
      // #ifdef APP
      signFont = parseInt(signFont);
      signFont = signFont / 2 + 'px';
      // #endif
      return signFont
    }
  },
  watch: {
    value: {
      handler(newData) {
        const pointTwo = this.pointTwo;
        if (pointTwo === -1) {
          if(newData) {
            let integer = 0;
            let decimal = '';
            if(newData === ~~newData) {
              integer = newData;
            } else {
              [integer, decimal] = String(newData).split('.');
			  if(decimal){
				   decimal = '.' + decimal.slice(0, 2);
			  }
            }
            this.integer = integer;
            this.decimal = decimal;
          } else {
            this.integer = 0;
            this.decimal = ''
          }
        } else {
          if(newData) {
            let integer = 0;
            let decimal = '';
            if(pointTwo) {
              [integer, decimal] = String(newData.toFixed(pointTwo)).split('.')
              decimal = '.' + decimal
            } else {
              integer = ~~pointTwo;
            }
            this.integer = integer;
            this.decimal = decimal;
          } else {
            this.integer = 0;
            if(pointTwo) {
              this.decimal = String(0.0.toFixed(pointTwo)).slice((pointTwo + 1)*-1)
            } else {
              this.decimal = '';
            }
          }
        }
        
      },
      immediate: true
    }
  }
}
</script>

<style scoped>
/* 根据视图窗口进行字体大小的判断 */
@media  (max-width: 310px) {
  .price-handle-big-font {
    font-size: calc( var(--font) + var(--base) );
  }
}
@media  (min-width: 310px) { 
  .price-handle-big-font {
    font-size: calc( var(--font) );
  }
}
.price-handle-big-font {
  /* font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif; */
}
.price-handle-component {
  align-items: baseline;
}
.price-handle-component .price-text {
}
</style>