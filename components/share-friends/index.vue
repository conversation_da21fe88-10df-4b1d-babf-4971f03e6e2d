<!-- 分享给好友组件  -->
<template>
  <view>
    <view>
      <!-- #ifdef MP -->
      <button
        style="
          border-radius: 0;
          background-color: #fff;
          font-size: 24rpx;
          padding: 20rpx;
        "
        class="padding"
        open-type="share"
      >
        发送给朋友
      </button>
      <!-- <button class="cu-btn bg-green lg round shadow-blur" style="width: 100%;" open-type="share">发送给朋友</button> -->
      <!-- #endif -->
      <!-- #ifndef MP -->
      <button
        class="cu-btn bg-green lg round shadow-blur"
        style="width: 100%; border-radius: 0"
        @tap="onShareWX"
      >
        分享给微信好友
      </button>
      <!-- #endif -->
    </view>
    <view
      v-if="showModal"
      class="cu-modal"
      :class="showModal ? 'show' : ''"
      @click="showModal = false"
    >
      <image
        style="right: 0; top: 0; width: 50%; margin-left: 20%"
        :src="$imgUrl('live/message/share_friends.jpg')"
      ></image>
    </view>
  </view>
</template>

<script>
import jweixin from "@/utils/jweixin.js";
import api from "utils/api";
import util from "@/utils/util";
import __config from "@/config/env";
import { senTrack } from "@/public/js_sdk/sensors/utils.js";
import { senGoods } from "@/public/js_sdk/sensors/utils.js";
export default {
  props: {
    shareObj: {
      type: Object,
      default: () => {},
    },
    curLocalUrl: {
      type: String,
    },
    shareShow: {
      type: String,
    },
  },
  watch: {
    shareObj(val, oldVal) {
      this.shareObjTemp = val;
    },
    curLocalUrl(val, oldVal) {
      this.initShareWx();
    },
  },
  data() {
    return {
      showModal: false,
      isInitShare: false,
      shareObjTemp: {},
    };
  },
  created() {
    this.shareObjTemp = this.shareObj;
    this.initShareWx();
  },
  mounted() {
    this.initShareWx();
  },
  methods: {
    initShareWx() {
      // h5页面加载时 会默认初始化分享
      let that = this;
      // #ifdef H5
      if (
        util.isWeiXinBrowser() &&
        this.shareObjTemp.imgUrl &&
        !this.isInitShare
      ) {
        this.isInitShare = true;
        var url = this.shareObjTemp.url
          ? this.shareObjTemp.url
          : util.setH5ShareUrl();
        let signLink = /(Android)/i.test(navigator.userAgent)
          ? url
          : window.entryUrl;
        api
          .getJsSdkConfig({
            url: signLink,
          })
          .then((res) => {
            history.replaceState(history.state, null, url);
            let wxConfig = res.data;
            let shareObjTemp = {
              title: this.shareObjTemp.title,
              desc: this.shareObjTemp.desc,
              link: wxConfig.url,
              imgUrl: this.shareObjTemp.imgUrl,
            };
            jweixin.shareWxFriend(
              wxConfig,
              shareObjTemp,
              function () {
                console.log(e);
                // that.showModal = true;
                // uni.hideLoading();
              },
              function (e) {
                console.log(e);
              },
            );
          })
          .catch((res) => {
            console.log("调用getJsSdkConfig失败：" + res);
          });
      }
      // #endif
    },
    onShareWX() {
      //H5 分享微信好友
      let that = this;
      // #ifdef H5
      if (this.shareObjTemp.imgUrl) {
        uni.showLoading();
        var url = this.shareObjTemp.url
          ? this.shareObjTemp.url
          : util.setH5ShareUrl();

        api
          .getJsSdkConfig({
            url: url,
          })
          .then((res) => {
            history.replaceState(history.state, null, url);
            let wxConfig = res.data;
            let shareObjTemp = {
              title: this.shareObjTemp.title,
              desc: this.shareObjTemp.desc,
              link: wxConfig.url,
              imgUrl: this.shareObjTemp.imgUrl,
            };
            jweixin.shareWxFriend(
              wxConfig,
              shareObjTemp,
              function () {
                that.showModal = true;
                uni.hideLoading();
              },
              function (e) {
                uni.hideLoading();
              },
            );
          })
          .catch((res) => {
            that.showModal = false;
            uni.showToast({
              icon: "none",
              title: "分享失败 " + res,
            });
            uni.hideLoading();
          });
      }
      // #endif
      // #ifdef APP-PLUS
      api.wxAppConfig(__config.wxAppId).then((res) => {
        console.log("获取到数据", res.data);
        if (res.data.data && res.data.data.isComponent == "1") {
          let url = this.shareObjTemp.url
            ? this.shareObjTemp.url
            : util.setAppPlusShareUrl(res.data.data);
          this.shareWxByApp(url);
        } else {
          let url = this.shareObjTemp.url
            ? this.shareObjTemp.url
            : util.setAppPlusShareUrl();
          this.shareWxByApp(url);
        }
      });
      // #endif
    },
    shareWxByApp(url) {
      // that.showModal = true;
      console.log("分享的数据", JSON.stringify(this.shareObjTemp));
      let path = `${this.shareObjTemp.page}`;
      if (this.shareObjTemp.scene) {
        path = `${path}?id=${this.shareObjTemp.scene}`;
      }
      console.log("分享的路径===》", path);

      uni.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 5, //分享形式 5:小程序
        href: url,
        title: this.shareObjTemp.title,
        summary: this.shareObjTemp.desc,
        imageUrl: this.shareObjTemp.imgUrl,
        miniProgram: {
          id: __config.originAppid,
          path,
          type: 0,
          webUrl: "https://shopapi.songlei.com/slshop-h5/ma/",
        },
        success: function (res) {
          // that.showModal = false;
        },
        fail: function (err) {
          // that.showModal = false;
          uni.showModal({
            title: "提示",
            content: "正在开发中",
            showCancel: false,
          });
        },
      });
      if (this.shareObj.trackParams) {
        const { trackType, goodsSpu } = this.shareObj.trackParams;
        if (trackType === "GoodsShareOrCollect" && goodsSpu) {
          senGoods("GoodsShareOrCollect", this.shareObj.trackParams, goodsSpu);
        } else {
          senTrack("ActivityPageShareOrCollect", this.shareObj.trackParams);
        }
      }
    },
  },
};
</script>
<style></style>
