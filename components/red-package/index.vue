<template>
	<view
		:class="showType == 'top' ? 'red-package-top' : 'red-package-bottom'"
		:style="{
			marginTop: 0
		}"
		v-if="redPackageImg"
		@tap="(e) => handleClick(e, linkUrl)"
	>
		<image :src="redPackageImg" mode="widthFix"></image>
		<image
			style="width: 50rpx; height: 50rpx; position: absolute"
			:style="{
				top: showType == 'top' ? '' : 0,
				bottom: showType == 'top' ? 0: '',
				left: showType == 'top' ? 0 : '',
				right: showType == 'top' ? '' : 0
			}"
			:src=$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')
			fit="cover"
			@tap.stop="handleClose"
		/>
	</view>
</template>

<script>
const app = getApp();
import api from 'utils/api';
import util from '@/utils/util';
import { gotoPage } from '@/components/div-components/div-base/div-page-urls.js';
export default {
	props: {
		showType: {
			type: String,
			default: 'bottom' // 顶部 和底部
		}
	},
	data() {
		return {
			CustomBar: this.CustomBar,
			redPackageImg: '',
			linkUrl: ''
		};
	},
	created() {
		this.advertisement('NEW_YEAR');
	},

	methods: {
		//广告
		advertisement(id) {
			//绑卡banner
			api.advertisementNewYear({
				bigClass: id
			}).then((res) => {
				if (res.data && res.data.imgUrl) {
					this.redPackageImg = res.data.imgUrl;
					this.linkUrl = res.data.linkUrl;
				}
				//测试代码
				// this.redPackageImg = $imgUrl('1/material/2ec56bc1-e4f9-4270-b860-58c27c6719cf.jpg-jpg_w750_q70');
				// this.linkUrl = "/pages/shop/shop-detail/index?id=44296994"
			});
		},

		handleClose() {
			this.redPackageImg = '';
		},

		handleClick(e, page) {
			let pages = getCurrentPages();
			console.log('handleZoneClick-->', e, page);
			if (page) {
				gotoPage(page, pages.length);
			} else {
				// uni.showToast({
				// 	title: '没有配置链接地址',
				// 	icon: 'none',
				// 	duration: 2000
				// });
			}
		}
	}
};
</script>

<style lang="scss">
.red-package-bottom {
	position: fixed;
	bottom: -100%;
	animation-delay: 2s;
	animation: popUp 1s ease forwards;
	width: 100%;
	z-index: 9999;

	image {
		width: 750rpx;
		height: 280rpx;
	}
}

@keyframes popUp {
	0% {
		bottom: -100%;
	}
	100% {
		bottom: 0;
	}
}

.red-package-top {
	position: fixed;
	top: -100%;
	animation-delay: 2s;
	animation: popBottom 1s ease forwards;
	width: 100%;
	z-index: 9999;

	image {
		width: 750rpx;
		height: 280rpx;
	}
}

@keyframes popBottom {
	0% {
		top: -100%;
	}
	100% {
		top: 0;
	}
}
</style>
