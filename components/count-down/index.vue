<template>
	<view v-if="outTime>0||negativeIsShow" style="display: initial;" :style="{
		color:textColor,
		fontSize: fontSize,
		fontWeight:fontWeight
		
	  }" class=" margin-left-xs margin-right-xs">
		<text v-if="day>0"
			:style="{'background-color': backgroundColor, 'padding':' 0 3rpx', 'border-radius': '6rpx'}">{{day}}天</text>
		<text
			:style="{'background-color': backgroundColor, 'padding':' 0 3rpx', 'border-radius': '6rpx'}">{{hour||''}}</text>
		<text :style="{'color': connectorColor}">{{showType==1?':':'时'}}</text>
		<text
			:style="{'background-color': backgroundColor, 'padding':' 0 3rpx', 'border-radius': '6rpx'}">{{minute||''}}</text>
		<text :style="{'color': connectorColor}">{{showType==1?':':'分'}}</text>
		<text
			:style="{'background-color': backgroundColor, 'padding':' 0 3rpx', 'border-radius': '6rpx'}">{{second||''}}</text>
		<text :style="{'color': connectorColor}">{{showType==1?'':'秒'}}</text>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				day: null,
				hour: null,
				minute: null,
				second: null,
				timer: null,
				totalTime: '',
				
			};
		},
		components: {},
		watch: {
			outTime(val, oldVal) {
				if (val != oldVal) {
					
				}
			}
		},
		props: {
			// 这里定义了innerText属性，属性值可以在组件使用时指定，毫秒
			outTime: {
				type: Number,
				default: 0
			},
			textColor: {
				type: String,
				default: '#fff'
			},
			connectorColor: {
				type: String,
				default: '#fff'
			},
			index: {
				type: Number,
				default: 0
			},
			backgroundColor: {
				type: String,
				default: 'red'
			},
			fontSize: {
				type: String,
				default: '26rpx'
			},
			fontWeight: {
				type: Number,
				default: 400
			},
			//1 分号形势展示 比如取消订单倒计时  2 XX时XX分XX秒
			showType: {
				type: Number,
				default: 1
			},
			//负数的时候是否展示
			negativeIsShow: {
				type: Boolean,
				default: false
			}
			 
		},
		mounted: function() {
			this.CaculateDate();
		},
		destroyed: function() {
			if (this.timer) {
				clearInterval(this.timer);
			}
		},
		methods: {
			CaculateDate: function() {
				var that = this;
				if (this.totalTime=='') {
					this.totalTime = this.outTime;
				}
				var leftTime = that.totalTime - 1000;
				var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10);
				var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10);
				var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);
				var seconds = parseInt(leftTime / 1000 % 60, 10);
               
				if (leftTime > 0 || this.negativeIsShow) {
					that.totalTime = leftTime;
					that.day = days != 0 ? that.timeFormat(days) : that.timeFormat(0);
					that.hour = hours != 0 ? that.timeFormat(hours) : that.timeFormat(0);
					that.minute = minutes != 0 ? that.timeFormat(minutes) : that.timeFormat(0);
					that.second =  seconds != 0 ? that.timeFormat(seconds) : that.timeFormat(0);
				}
				this.timer = setInterval(function() {
					var leftTime = that.totalTime - 1000;
					var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10);
					var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10);
					var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);
					var seconds = parseInt(leftTime / 1000 % 60, 10);
					if (leftTime > 0  || that.negativeIsShow) {
						that.totalTime = leftTime;
						that.day = days != 0  ? that.timeFormat(days) : that.timeFormat(0);
						that.hour = hours != 0  ? that.timeFormat(hours) : that.timeFormat(0);
						that.minute = minutes != 0  ? that.timeFormat(minutes) : that.timeFormat(0);
						that.second = seconds != 0 ?  that.timeFormat(seconds) : that.timeFormat(0);
						if(that.outTime>=0&&leftTime <0){
							//说明已超时，通知父组件更新UI
							that.$emit('updateNegativeShow',leftTime);
						}
					} else {
						//结束
						clearInterval(that.timer);
						setTimeout(function() {
							that.$emit('countDownDone', that.index);
						}, 2000);
					}
				}, 1000);
			},

			timeFormat(param) {
				//小于10的格式化函数
				if(param<0) param = Math.abs(param);
				return param < 10 ? '0' + param : param;
			}

		}
	};
</script>
<style>
</style>
