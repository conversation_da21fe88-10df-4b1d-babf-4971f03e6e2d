<template>
	<view class="songlei-popup" @click="cancelBtn" v-if="showPopup">
		<view class="popup-box text-xdf">
			<view class="popup-item" @click.stop="cameraBtn(1)">拍摄</view>
			<view class="popup-item" @click.stop="cameraBtn(2)">从相册选择</view>
			<!-- #ifdef MP -->
			<view class="popup-item" @click.stop="cameraBtn(3)">从微信聊天选择</view>
			<!-- #endif -->
			<view class="popup-item" @click.stop="cameraBtn(4)">从历史记录中选取</view>
			<view @click="cancelBtn" class="popup-item"
				style="border-top: 10rpx solid #f1f1f1;border-bottom: 0;padding-bottom: 16rpx;">取消</view>
		</view>
		<uni-popup ref="perpopup" type="center" :mask-click='false'>
			<view class="permissions_box">
				{{
					permissionsType==1?"当您使用APP时，拍照需要授权相机权限，不授权上述权限，不影响APP其他功能使用。":
					permissionsType==2?"当您使用APP时，从相机选图需要授权文件存储权限，不授权上述权限，不影响APP其他功能使用。":""
				}}

			</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				permissionsType: 1
			}
		},
		props: {
			showPopup: {
				type: Boolean,
				default: false
			}
		},
		methods: {
			cameraBtn(type) {
				const that = this;
				this.permissionsType = type;
				if (+type === 1 || +type === 2) {
					// #ifdef MP-WEIXIN
					// 拍摄相机
					uni.chooseMedia({
						count: 1,
						mediaType: ['image'],
						sourceType: [+type === 1 ? 'camera' : 'album'],
						success(res) {
							app.globalData.cameraPic = res.tempFiles[0].tempFilePath;
							uni.navigateTo({
								url: '/pages/camera/index'
							})
						},
						complete() {
							that.cancelBtn();
						}
					})
					// #endif
					// #ifdef APP
					// 拍摄相机
					var platform = uni.getSystemInfoSync().platform;
					if (platform == 'android') {
						plus.android.checkPermission(
							+type === 1 ? 'android.permission.CAMERA' :
							"android.permission.WRITE_EXTERNAL_STORAGE",
							granted => {
								if (granted.checkResult == -1) {
									//弹出
									that.$refs.perpopup.open('top');
									plus.android.requestPermissions([+type === 1 ? 'android.permission.CAMERA' :
											"android.permission.WRITE_EXTERNAL_STORAGE"
										],
										(e) => {
											//关闭
											if (e.granted.length > 0) {
												//执行你有权限后的方法
												that.$refs.perpopup.close()
												that.appScan(type);
											}
											if (e.deniedAlways.length > 0) { //权限被永久拒绝
												uni.showModal({
													title: '提示',
													content: '保存图片权限被拒绝，是否前往开启权限',
													success: (res) => {
														if (res.confirm) {
															// 弹出提示框解释为何需要读写手机储存权限，引导用户打开设置页面开启
															var main = plus.android
																.runtimeMainActivity();
															var Intent = plus.android.importClass(
																"android.content.Intent");
															//直接进入应用列表的权限设置
															var mIntent = new Intent(
																'android.settings.APPLICATION_SETTINGS'
															);
															main.startActivity(mIntent);
															that.$refs.perpopup.close()
														} else if (res.cancel) {
															console.log('用户点击取消');
															that.$refs.perpopup.close()
														}
													}
												});
											}
											if (e.deniedPresent.length > 0) { //权限被临时拒绝
												// 弹出提示框解释为何需要读写手机储存权限，可再次调用plus.android.requestPermissions申请权限
												plus.android.requestPermissions([
													'android.permission.CAMERA',
													'android.permission.WRITE_EXTERNAL_STORAGE'
												])
												that.$refs.perpopup.close()
											}
										})
								} else {
									//执行你有权限后的方法
									that.appScan(type);
								}
							},
							error => {
								console.error('Error checking permission:', error.message);
							}
						);


					} else {
						//执行你有权限后的方法 ios
						that.appScan(type);
					}
					// #endif
				} else if (+type === 3) {
					// 微信聊天选择
					wx.chooseMessageFile({
						count: 1,
						type: 'image',
						success(res) {
							app.globalData.cameraPic = res.tempFiles[0].path;
							uni.navigateTo({
								url: '/pages/camera/index'
							})
						},
						complete() {
							that.cancelBtn();
						}
					})
				} else if (+type === 4) {
					uni.navigateTo({
						url: '/pages/camera/select-list'
					})
				}
			},
			cancelBtn() {
				this.$emit('update:showPopup', false);
			},

			appScan(type) {
				uni.chooseImage({
					count: 1,
					sourceType: [+type === 1 ? 'camera' : 'album'],
					success(res) {
						app.globalData.cameraPic = res.tempFiles[0].path;
						uni.navigateTo({
							url: '/pages/camera/index'
						})
					},
					complete() {
						that.cancelBtn();
					}
				})
			}

		}
	}
</script>

<style scoped>
	/* 弹出框 */
	.songlei-popup {
		position: fixed;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, .5);
		top: 0;
		left: 0;
		z-index: 9999;
	}

	.popup-box {
		width: 100vw;
		background-color: #fff;
		border-radius: 30rpx 30rpx 0 0;
		position: absolute;
		bottom: 0;
		left: 0;
	}

	.popup-item {
		text-align: center;
		color: #000;
		border-bottom: 1rpx solid #f1f1f1;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.permissions_box {
		padding: 200rpx 30rpx 50rpx;
		background-color: #fff;
		color: #000000;
	}
</style>