<template>
	<view class="ske-skeleton" :style="groupStyle">
		<template v-if="loading">
			<block v-for="i in row" :key="i">
				<view class="flex flex-justify-bet margin-bottom" v-if="type==='block' || type ==='circle'">
					<block v-for="j in number" :key="j">
						<view class="ske-cell" :class="animateClass" :style="customStyle" v-if="type==='block'">
						</view>
						<view class="ske-circle" :class="animateClass" :style="{height:size+'px',width:size+'px'}"
							v-if="type==='circle'">
						</view>
					</block>
					<!-- 间距(这个放在最底部) -->
					<view :style="{height:rowSpace}" v-if="i<row-1"></view>
				</view>
				<!-- 自定义模板 -->
				<!-- 菜单页部分 -->
				<view class="flex" v-if="type==='goods'">
					<view class="flex-sub ">
						<view class="flex  margin-bottom-sm" v-for="r in 5" :key="r">
							<view class="ske-block ske-goods-img" :class="animateClass">
							</view>
							<view class="flex-sub margin-left-sm">
								<view class="ske-block ske-goods-cell margin-bottom-sm" :class="animateClass">
								</view>
								<view class="ske-block ske-goods-cell margin-bottom-sm" :class="animateClass">
								</view>
								<view class="ske-block ske-goods-cell" :class="animateClass">
								</view>
							</view>
						</view>
					</view>
				</view>

			</block>
		</template>
		<template v-else>
			<slot />
		</template>
	</view>
</template>
<style lang="scss" src="./index.scss" scoped></style>
<script>
	export default {
		props: {
			loading: {
				type: Boolean,
				default: true
			},
			animate: {//动画
				type: Boolean,
				default: true
			},
			type: {//类型 block circle goods
				type: String,
				default: "block" // block 一个块级组件，可设置长方形，正方形 ；circle圆形，可设置大小； goods 自定义的菜单骨架
			},
			number: {// 一行需要显示几个小组件
				type: Number,
				default: 1
			},
			row: {	// 需要显示几行
				type: Number,
				default: 1
			},
			rowSpace: { // 行间距
				type: String | Number,
				default: "10px"
			},
			groupStyle:{//该骨架屏的样式(主要是设置外内边距)
				type: String,
				default: ""
			},
			customStyle: {//block 该block组件的自定义样式(大小,radius等)	
				type: String,
				default: ""
			},
			size: {//circle 圆的大小
				type: Number | String,
				default: 60
			}
		},
		computed: {
			animateClass() {
				return this.animate ? 'ske-animation' : ''
			}
		}
	}
</script>

<style>
</style>
