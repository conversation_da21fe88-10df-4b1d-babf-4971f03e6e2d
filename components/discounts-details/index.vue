<template>
	<view v-if="true" class="cu-modal bottom-modal" :class="modalDiscounts && 'show'" @tap="hideModal" catchtouchmove="touchMove">
		<view class="cu-dialog bg-white" style="margin-bottom: 100rpx;" :class="modalDiscounts && 'animation-slide-bottom'" @tap.stop>
			<view class="cu-card article no-card" style="height: 34%; padding-top: 20rpx;">
				<view class="cu-item">
					<view class="text-bold text-black discounts-title" >优惠明细</view>
					<view class="text-xl close-icon close-btn">
						<text class="cuIcon-close" @tap="hideModal"></text>
					</view>
					<!-- </view> -->
					<view class="discounts-content">
						<!-- <scroll-view scroll-y scroll-with-animation style="height:60%"> -->
							<view class="goods" v-show="shoppingData.length">
								<view class="goods-img" :style="{'height': height}">
									<view class="goods-item" v-for="(item, index) in shoppingData" :key="index">
										<image :src="item.picUrl || $imgUrl('live/img/no_pic.png')" mode="aspectFill" class="row-img margin-top-xs" />
										<checkbox class="round checked red"
											:class="item.checked ? theme.themeColor+' checked ':''" 
											:value="item.id"
											:disabled="(item.quantity==0 || item.quantity > item.goodsSku.stock) && operation"
											:checked="item.checked"
											@tap="checkChange(item, item.storeIndex, item.shopIndex)">
										</checkbox>
										<format-price 
											v-if="(Number(item.goodsSku.estimatedPriceVo.promotionsDiscountPrice) + Number(item.goodsSku.estimatedPriceVo.estimatedPrice)) !== Number(item.goodsSku.estimatedPriceVo.price)" 
											color="#333"  
											signFontSize="18rpx" 
											smallFontSize="22rpx" 
											priceFontSize="34rpx" 
											:price="item.goodsSku.estimatedPriceVo.estimatedPrice" 
										></format-price>
										<format-price
											v-else
											color='black'  
											signFontSize="20rpx" 
											smallFontSize="24rpx" 
											priceFontSize="34rpx" 
											:price="(Number(item.goodsSku.estimatedPriceVo.price) - Number(item.goodsSku.estimatedPriceVo.promotionsDiscountPrice)).toFixed(2)" 
										></format-price>
									</view>
								</view>
								<view v-if="shoppingData.length > 4" @click="showMore = !showMore" class="goods-show-more text-gray text-sm">已选购 {{shoppingData.length}} 件商品</view>
							</view>
							<view class="discounts-details" v-show="shoppingData.length && discountsPrice">
								<view class="flex justify-between align-center details-item">
									<view class="">商品总额</view>
									<format-price v-if="discountsPrice && discountsPrice.price" color="#333"  signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx" :price="discountsPrice.price" ></format-price>
									<format-price v-else color="#333"  signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx" :price="settlePrice" ></format-price>
								</view>
								<view class="flex justify-between align-center details-item">
									<view class="">折扣</view>
									<view class="flex align-center">
										<view>-</view>
										<format-price color="#333"  signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx" :price="discountsPrice.promotionsDiscountPrice" ></format-price>
									</view>
								</view>
								<view class="flex justify-between align-center details-item">
									<view class="">优惠券</view>
									<view class="flex align-center">
										<view>-</view>
										<format-price color="#333"  signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx" :price="discountsPrice.coupon" ></format-price>
									</view>
								</view>
								<view class="flex justify-between align-center details-item">
									<view class="text-bold text-black">共优惠</view>
									<view class="flex align-center text-red">
										<view>-</view>
										<format-price signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx" :price="totalPrice" ></format-price>
									</view>
								</view>
								<view class="flex justify-between align-center details-item text-sm">
									以上优惠不包含积分抵扣和礼品卡，请在结算页面查看
								</view>
							</view>
							
						<!-- </scroll-view> -->
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import formatPrice from "@/components/format-price/index.vue"
	export default {
		name:"discounts-details",
		components: {
			formatPrice
		},
		props: {
			modalDiscounts: {
				type: Boolean,
				default: false
			},
			discountsPrice: {
				type: Object,
				default: {}
			},
			shoppingCartData: {
				type: Array,
				default: []
			},
			operation: {
				type: Boolean,
				default: false
			},
			settlePrice: {
				// type: Number,
				default: 0
			}
		},
		computed: {
			height() {
				return this.shoppingData.length > 4 ? this.showMore ? 'auto' : '150rpx' : '220rpx'
			},
			totalPrice() {
				return (Number(this.discountsPrice.promotionsDiscountPrice) + Number(this.discountsPrice.coupon)).toFixed(2)
			},		
			shoppingData() {
				let shhoppingList = []
				this.shoppingCartData.length && this.shoppingCartData.forEach((store, storeIndex) => {
					store.shoppingCartData.length && store.shoppingCartData.forEach((shop, shopIndex) => {
						shop.storeIndex = storeIndex
						shop.shopIndex = shopIndex
						shop.checked && shhoppingList.push(shop)
					})
				})
				return JSON.parse(JSON.stringify(shhoppingList))
			},
			shoppingList() {
				// return JSON.parse(JSON.stringify(this.shoppingData))
			}
		},
		watch: {
			shoppingData(newVal) {
				!newVal.length && this.$emit('updataModalDiscounts', 'close')
			}
		},
		data() {
			return {
				theme: app.globalData.theme,
				showMore: false
			};
		},
		mounted() {
		},
		methods: {
			hideModal() {
				this.$emit('updataModalDiscounts', 'close')
			},
			checkChange(item, storeIndex, shopIndex) {
				this.$emit('checkChange', item, storeIndex, shopIndex)
			}
		}
	}
</script>

<style lang="scss" scoped>
v-deep .bottom-modal{
	margin-bottom:140rpx;
}
.discounts-title{
	width: 100%;
	text-align: center;
	font-size: 38rpx;
}
.close-btn{
	position: absolute;
	top: 30rpx;
	right: 30rpx;
}
.discounts-content{
	padding: 0 20rpx;
	height: 1000rpx;
	overflow: hidden;
	overflow-y: scroll;
	.goods{
		// height: 280rpx;
		width: 100%;
		overflow: hidden;
		background: #ececec;
		border-radius: 20rpx;
		margin-top: 20rpx;
		.goods-show-more{
			width: 100%;
			height: 40rpx;
			line-height: 40rpx;
			background: #ececec;
			position: relative;
			margin-top: 70rpx;
			padding: 10rpx 0 50rpx;
			z-index: 999;
		}
		.goods-img{
			display: flex;
			justify-content: flex-start;
			flex-wrap: wrap;
			width: 100%;
			.goods-item{
				width: calc(100% / 4);
				border-radius: 10rpx;
				position: relative;
				margin-bottom: 10rpx;
				image{
					width: 145rpx;
					height: 145rpx;
					border: 1px solid #ccc;
					border-radius: 10rpx;
				}
				.checked{
					position: absolute;
					top: 12rpx;
					right: 14rpx;
					
				}
				
			}
			
		}
	}
	
	.discounts-details{
		flex: 1;
		margin-top: 40rpx;
		.details-item{
			margin-top: 20rpx;
			&:first-child{
				margin-top: 0;
			}
		}
	}
}

</style>