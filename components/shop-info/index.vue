<template>
	<view class="cu-card article" :class="card?'':'no-card'" v-if="shopInfo">
		<!-- <view class="cu-item">
			<view class="shop-name cuIcon-shop margin-left margin-top-sm" @click="toShopHome(shopInfo.id)">
				<text class="text-sm text-bold margin-left-xs">{{shopInfo.name}}</text></view>
			<view class="content shop-detail margin-top-sm align-center">
				<image :src="shopInfo.imgUrl" mode="aspectFit"></image>
				<view class="text-gray">
					<view class="cuIcon-locationfill location margin-left-xs overflow-2">
						<text class="address text-sm margin-left-xs">{{shopInfo.address}}</text></view>
					<view class="cuIcon-mobilefill mobile margin-left-xs margin-top-xs" :hover-stop-propagation="true" @click="callPhone(shopInfo.phone)">
						<text class="phone text-sm margin-left-xs">{{shopInfo.phone}}</text></view>
				</view>
			</view>
		</view> -->
		<view class="cu-box cu-item shop-box">
			<image :style="{borderRadius: borderRadius+'rpx'}" :src="shopInfo.backgroundImg" mode="aspectFill"
				class="cu-box-img"></image>
			<navigator :url="'/pages/shop/shop-detail/index?id=' + shopInfo.id"
				class="content shop-detail margin-top-sm align-center cu-box-content">
				<image :src="shopInfo.imgUrl" mode="aspectFit"></image>
				<view class="text-gray" style="width:58%;">
					<view class="location margin-left-xs overflow-2">
						<text class="address text-df" style="color:#000">{{shopInfo.name | finishingStr }}</text>
					</view>
					<view class="excessive-banner-text">
						<view>
							<text v-if="shopInfo.selfSupport == 1" class="cu-btn round excessive-sm"
								style="background-color: #ff2727">自营</text>
							<text class="cu-btn round excessive-sm" style="background-color: #ff9d31">上新</text>
							<text v-if=" shopInfo.collectCount >0" class="excessive-follow">{{ shopInfo.collectCount }}人关注</text>
						</view>
					</view>
				</view>
				<view class="cu-box-nav">进店</view>
			</navigator>
		</view>
	</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {};
		},
		filters: {
			finishingStr: function(value) {
				if (!value) return '';
				console.log(value, )
				return value.replace(/\n+/g, "");
			}
		},
		components: {},
		props: {
			shopInfo: {
				type: Object,
				default: () => ({})
			},
			card: {
				type: Boolean,
				default: true
			},
			borderRadius: {
				type: String,
				default: '0'
			}
		},
		methods: {
			//跳转到商铺首页
			toShopHome(id) {
				uni.navigateTo({
					url: '/pages/shop/shop-detail/index?id=' + id
				});
			},

			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				});
			}
		}
	};
</script>
<style scoped="">
	.shop-box {
		background: none;
		border-radius: 20rpx;
	}

	.shop-detail image {
		width: 177rpx !important;
		height: 75rpx !important;
		margin: 6rpx 0;
	}

	/* 	.location {
		width: 520rpx;
	} */

	.cu-box {
		width: 100%;
		height: 289rpx;
		position: relative;
	}

	.cu-box-img {
		width: 100%;
		height: 289rpx;
		position: absolute;
		z-index: 1;
	}

	.cu-box-content {
		width: 92%;
		position: absolute;
		z-index: 2;
		background: #fff;
		border-radius: 5rpx;
		bottom: 16rpx;
		left: 4%;
		border-radius: 20rpx;
	}

	.cu-box-nav {
		width: 130rpx;
		height: 43rpx;
		color: linear-gradient(-90deg, #8D020B 0%, #CE1A26 100%);
		border: 1px solid #93040D;
		border-radius: 21rpx;
		text-align: center;
		line-height: 43rpx;
		font-size: 20rpx;
		color: #93040D;
	}

	.excessive-sm {
		padding: 0 8rpx;
		margin-right: 6rpx;
		font-size: 17rpx;
		height: 25rpx;
		color: #ffffff;
	}

	.excessive-follow {
		font-size: 17rpx;
		height: 25rpx;
	}

	.text-gray {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>
