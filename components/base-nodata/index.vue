<template>
	<view class="no-data">
		<image  :src="$imgUrl('live/no-data.png')"  mode="aspectFit"></image>
		<view style="font-size: 24rpx; margin-top: 20rpx; color: #8a8a8a;">{{tip}}</view>
	</view>
</template>

<script>
	export default {
		props: {
		  tip: {
		    type: String,
		    default: '暂无数据'
		  }
		},
		data() {
			return {
				
			}
		},
		methods: {
			
		}
	}
</script>

<style lang="less" scoped>
  .no-data {
	  margin-top: 20%;
	  margin-bottom: 20%;
	  display: flex;
	  flex-direction: column;
	  align-items: center;
	  image {
		  width: 100rpx;
		  height: 100rpx;
	  }
  }
</style>
