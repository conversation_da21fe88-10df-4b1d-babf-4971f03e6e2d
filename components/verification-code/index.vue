<template>
  <!--输入短信验证码密码通用组件--> 
  <view>
    <view class="margin-top-sm">
      <view style="color: #000000;margin:20rpx 0">
        <input
          class="cinput"
          :adjust-position="false"
          auto-blur="true"
          @focus="inputBindFocus"
          @blur="blur"
          @input="codenum"
          password="true"
          :focus="focus"
          value=""
          v-model="code"
          type="number"
          maxlength="6"
        />
        <view class="code-input">
          <view
            v-for="(item,index) in 6"
            :key="index"
            @click="codefocus(index)"
            :style='(index == code.length? "border: 5rpx solid #1195db;width: 100rpx;height: 100rpx;line-height: 100rpx;":"color: " + codeclolor + ";" +"border: 2rpx solid" + codeclolor)'
          >
            {{passwordShow?'':code[index]||''}}<text v-if="code[index]&&passwordShow" class="cuIcon-title text-black text-xdf"></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
data() {
    return {
      code: '',//手机验证码 .toString().replace(/,/gi,'-')
      // 验证码输入聚焦
      focus: false,//input焦点，用于键盘隐藏后重新唤起
      // 验证码框颜色
      codeclolor: "#313131",//自定义光标的颜色,
    };
  },
props: {
  // 密码是否显示 ●
  passwordShow:{
    type: Boolean,
    default: false
  },
  //使用来源
  source:{
    type: String,
    default: ''
  },
  //控制清空
  clear:{
    type: Boolean,
    default: true
  }
},

watch:{
  clear:{
    handler(val){
      console.log("clear",this.clear,val);
      if (!val) {
        this.code =''
      }
    },
    immediate:true
  }
},
mounted(){
  this.focus = true
},

onShow() {  
},

methods:{
    inputBindFocus(e) {
        // 获取手机键盘的高度，赋值给input 所在盒子的 bottom 值
        // 注意!!! 这里的 px 至关重要!!!
        this.$emit('changeBottomVal' ,  e.detail.height +  'px')
    },
    // 输入验证码
    codenum: function (event) {
      let that = this
      let code = event.target.value
      that.code = code
      // 扩展分销使用
      if (this.source) {
        this.$emit('change', this.code);
        return
      }
      if (code.length == 6) {
        // if (code == '123456') {
        //输入六位验证码后自动进行验证并执行验证成功的函数
        // console.log("code", this.code);
        // this.form.phoneverCode =code
        this.$emit('change', this.code);
        //2.如果需要验证码要传给后台
        //第三个接口
        // this.fatherMethod()
        }
    },

    // 键盘隐藏后设置失去焦点
    blur: function () {
      let that = this
      console.log("blur",that.focus);
      that.focus = false
      if (this.clear) {
        this.code =''
      }
      this.$emit('changeBottomVal', 0)
    },

    // 点击自定义光标显示键盘
    codefocus: function (e) {
      let that = this
      console.log("eeee", e);
      if(that.focus) {
        that.focus = false;
        this.$nextTick(() => {
          that.focus = true
        })
      } else {
        that.focus = true
      }
      // if (e == that.code.length) {
        // that.focus = true
      // }
    },
}

}
</script>

<style lang="less" scoped>
.code-input {
margin: auto;
width: 665rpx;
height: 100rpx;
display: flex;
}

.code-input > view {
margin-top: 5rpx;
margin-left: 15rpx;
width: 96rpx;
height: 96rpx;
line-height: 96rpx;
font-size: 60rpx;
font-weight: bold;
color: #313131;
text-align: center;
border-radius: 10rpx;
}

.code-input > view:nth-child(1) {
margin-left: 0rpx;
}

.cinput {
position: fixed;
left: -100rpx;
width: 50rpx;
height: 50rpx;
}

</style>>