<template>
  <view>
    <view class="margin-top-lg" :class="orderInfo.actionButtonList.length > 3 ? 'flex justify-between align-center' : ''" style="position: relative;">
      <view
        v-if="orderInfo.actionButtonList.length > 3"
        style="color: #999999;"
        @tap="moreAsync = !moreAsync"
      >更多</view>
      <view class="btn-more" v-show="moreAsync">
        <template v-for="(item, index) in orderInfo.actionButtonList">
          <view
            class="more-item"
            :class="index >= 4 ? 'more-border' : ''"
            v-if="index >= 3 && btnType[item]"
            :key="index"
          >
            <text @tap="btnType[item].fn()">
              {{ btnType[item].name }}
            </text>
          </view>
        </template>
       
      </view>
      <view class="flex justify-end align-center">
        <template >
          <view class="flex align-center" v-for="(item, index) in orderInfo.actionButtonList" :key="index">
            <view v-if="item == 'needPay'" class="count-down-box">
              <text class="status">{{ orderInfo.orderStatusDesc }}</text>
              <count-down
                :outTime="orderInfo.outTime * 1000"
                :index="orderInfo.index"
                textColor="#EE112B"
                connectorColor="#EE112B"
                backgroundColor="#ffe3e6"
                @countDownDone="$emit('countDownDone', orderInfo.index)"
              />
            </view>
            <button
              v-if="index < 3 && btnType[item]"
              class="button"
              :class="item == 'needPay' ? 'gopay' : ((item == 'takeGoods' || item == 'buyAgain') ? 'red' : 'normal')"
              :style="item == 'needPay' ? 'margin-left: -50rpx;' : ''"
              @tap="btnType[item].fn()"
            >
              {{ btnType[item].name }}
            </button>
          </view>
        </template>
      </view>
    </view>
    <!-- 支付方式弹框 -->
		<view class="cu-modal bottom-modal" style="overflow: hidden;" :class="payAsync ? 'show' : ''" @tap.stop="payAsync = false">
			<view class="cu-dialog bg-white" style="padding-bottom: 80rpx; position: absolute; bottom: 0; left: 0;" @tap.stop>
				<pay-components ref="pay" :payType="orderInfo.paymentType" :orderType="orderInfo.orderType" :bargainHallId="orderInfo.bargainHallId"></pay-components>
				<button style="width:90%" class="cu-btn round bg-red margin-top-lg text-white"
					@tap.stop="$noMultipleClicks(goPay)" :loading="loading" :disabled="loading" type id="goPay">立即支付</button>
			</view>
		</view>
    <!-- 我要催单/提醒发货--弹框 -->
    <view class="cu-modal" :class="remindClass" catchtouchmove="touchMove" @tap="remindClass = ''">
		  <view class="cu-dialog bg-white remind-box" @tap.stop>
        <view class="remind-title">已发出提醒</view>
        <view class="remind-text">
          <view>我们应在 <text>{{ orderInfo.estimateExpressTime }}</text> 前发货，</view>
          <view>请耐心等待。</view>
          <view>您也可以点击客服帮我跟进。</view>
        </view>
        <view class="flex justify-between remind-btns">
          <button class="btn customer-service" @tap="handleCusServiceClick">联系客服</button>
          <button class="btn igotit" @tap="remindClass = ''">我知道了</button>
        </view>
      </view>
    </view>
    <!-- 自提二维码--弹框 -->
    <view class="cu-modal" :class="selfDelivery" catchtouchmove="touchMove" @tap="selfDelivery = ''">
		  <view class="cu-dialog bg-white" style="padding: 40rpx" @tap.stop>
        <image
          @click="previewimg(orderInfo.qrImg)"
          style="width: 300rpx; height: 300rpx;"
          :src="orderInfo.qrImg"
          alt=""
        />
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();	
import api from 'utils/api';
const util = require("utils/util.js");
import __config from '@/config/env';
import payComponents from '@/components/pay-components/pay-components.vue';
import countDown from "@/components/count-down/index";

export default {
  components: { payComponents, countDown },
  props: {
    orderInfo: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      noClick: true,
      payAsync: false,
      moreAsync: false,
	    loading: false,
      btnType: {
        'refundOrder': { name: '申请退款', fn: () => { this.drawback() } },
        'remindShipping': { name: '我要催单', fn: () => { this.remindShipment() } },
        'contactCustomerService': { name: '联系客服', fn: () => { this.handleCusServiceClick() } },
        'viewLogistics': { name: '查看物流', fn: () => { this.orderLogistics() } },
        'takeGoods': { name: '确认收货', fn: () => { this.orderReceive() } },
        'toEvaluate': { name: '去评价', fn: () => { this.orderAppraise() } },
        'buyAgain': { name: '再次购买', fn: () => {this.$noMultipleClicks(this.buyAgain)} },
        'cancelOrder': { name: '取消订单', fn: () => { this.orderCancel() } },
        'needPay': { name: '立即支付', fn: () => { this.openPay() } },
        'deleteOrder': { name: '删除订单', fn: () => { this.orderDel() } },
        'takeQrCode': { name: '自提码', fn: () => { this.selfDeliveryFn() }},
        'viewOrder': { name: '查看订单', fn: () => { this.viewOrder() }},
        'cancelRefund': { name: '撤销申请', fn: () => { this.cancelRefund() }},
        'viewRefund': { name: '查看详情', fn: () => { this.viewRefundOrder() }},
        'deleteRefund': { name: '删除订单', fn: () => { this.refundOrderDel() }},
        'isInvoice': { name: '查看发票', fn: () => { this.viewInvoice() }},
      },
      remindClass: '',
      selfDelivery: ''
    };
  },
  methods: {
    // 删除订单
    orderDel() {
      this.moreAsync = false
      uni.showModal({
        content: '确认删除该订单吗？',
        cancelText: '我再想想',
        confirmColor: '#EE112B',
        success: (res) => {
          if (res.confirm) {
            this.$noMultipleClicks((orderId) => {
              api.orderDel(orderId).then(res => {
                this.$emit('orderDel', res);
              });
            }, this.orderInfo.orderId)
          }
        }
      });
    },
    // orderDelApi(orderId) {
    //   api.orderDel(orderId).then(res => {
    //     // console.log(id, 'ceshi ')
    //     this.$emit('orderDel', res);
    //   }).catch((error) => {
    //     // console.log('失败')
    //   });
    // },
    // 去评价
    orderAppraise() {
      this.moreAsync = false
      uni.navigateTo({
        url: '/pages/appraises/form/index?orderId=' + this.orderInfo.orderId
      });
    },
    // 查看物流
    orderLogistics() {
      this.moreAsync = false
      // #ifdef MP-WEIXIN
      api.getWayBillTokenByOrderId(this.orderInfo.orderId).then(res => {
        this.$emit('orderLogistics', res);
      })
      // #endif
      // #ifndef MP-WEIXIN
      this.$emit('orderLogistics', {});
      // #endif
    },
    // 确认收货
    orderReceive() {
      this.moreAsync = false
      uni.showModal({
        content: '是否确认收货吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',
        success: (res) => {
          if (res.confirm) {
            this.$noMultipleClicks((orderId) => {
              api.orderReceive(orderId).then(res => {
                this.$emit('orderCancel', res);
              });
            }, this.orderInfo.orderId)
          }
        }
      });
    },
    // orderReceiveApi(orderId) {
    //   api.orderReceive(orderId).then(res => {
    //     this.$emit('orderCancel', res);
    //   });
    // },
    // 再次购买
    buyAgain() {
      this.moreAsync = false
      api.batchAdd(this.orderInfo.listOrderItem).then(res => {
        uni.switchTab({
          url: '/pages/shopping-cart/index'
        });
      });
    },
    // 取消订单
    orderCancel() {
      this.moreAsync = false
      uni.showModal({
        content: '确认取消该订单吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',
        success: (res) => {
          if (res.confirm) {
            const { orderId, orderNo, createTime } = this.orderInfo;
            api.orderCancel(orderId).then(res => {
              this.$emit('orderCancel', res);
            });
          }
        }
      });
    },
    compareParams() {
      const { orderNo, salesPrice, paymentPrice } = this.orderInfo;
      const sub_orders = [];
      sub_orders.push({
        "sub_order_id": orderNo,
        "order_amt": Number(salesPrice),
        "pay_amt": Number(paymentPrice),
      })
      return sub_orders
    },
    // 立即支付
    openPay() {
      this.moreAsync = false
      this.payAsync = true
    },
    async goPay() {
      try {
        const orderInfo = {
          listOrderInfo: this.orderInfo.listOrderItem,
          paymentPrice: this.orderInfo.paymentPrice
        }
        await this.$refs.pay?.payOrder(orderInfo, true, this.orderInfo.orderType)
      } catch (e) {
        console.log(e, '======error==========')
      }
    },
    // 查看订单
    viewOrder() {
      this.moreAsync = false
      uni.navigateTo({
        url: '/pages/order/order-detail/index?id=' + this.orderInfo.orderId
      });
    },
    // 我要催单/提醒发货
    remindShipment() {
      this.moreAsync = false
      api.remindShipment({id: this.orderInfo.orderLogistics.id}).then(res => {
        this.remindClass = 'show'
      });
    },
    // 联系客服
   async handleCusServiceClick() {
      this.moreAsync = false;
	  let wxCustomerUrl = this.orderInfo.shopInfo.wxCustomerUrl || app.globalData.wxCustomerUrl;
	  if (!wxCustomerUrl) {
	  	const res = await api.getLivingTag();
	  	if (res && res.data) {
	  		wxCustomerUrl = res.data.platWxCustomerUrl;
	  		app.globalData.wxCustomerUrl = res.data.platWxCustomerUrl;
	  	}
	  }
      if (wxCustomerUrl) {
        // #ifdef MP
        wx.openCustomerServiceChat({
          extInfo: {
            url: wxCustomerUrl
          },
          corpId: __config.chatId,
          showMessageCard: true,
          sendMessageTitle: '咨询订单id:' + this.orderInfo.orderId,
          sendMessagePath: 'pages/order/order-detail/index?id=' + this.orderInfo.orderId,
          sendMessageImg: '',
          success(res) {},
        })
        // #endif
        // #ifdef APP
        uni.share({
        	provider: 'weixin',
        	scene: 'WXSceneSession',
        	openCustomerServiceChat: true,
        	corpid: __config.chatId,
        	customerUrl: wxCustomerUrl,
        	fail(err) {
        		console.log("打开客服错误", err);
            // uni.makePhoneCall({
            //   phoneNumber: '**********'
            // })
        	}
        })
        // #endif
      } else {
        uni.showToast({
          title: '请店铺客服先配置下客服链接',
          icon: 'none',
          duration: 2000
        });
      }
	 
    },
    // 申请售后
    drawback() {
      this.moreAsync = false
      const { listOrderItem } = this.orderInfo;
      const ids = []
      if (listOrderItem && listOrderItem.length) {
        listOrderItem.forEach((item) => {
          ids.push(item.id)
        })
        const idsStr = ids.length > 1 ? ids.join(',') : ids[0]
        uni.navigateTo({
          url: '/pages/order/order-refunds/submit/index?orderItemId=' + idsStr
        });
      }
      
    },
    // 自提二维码
    selfDeliveryFn() {
      this.moreAsync = false
      this.selfDelivery = 'show'
    },
    // 自提二维码预览
    previewimg(img) {
      uni.previewImage({
        urls: [img], //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
        current:img, // 当前显示图片的http链接，默认是第一个
        success: (res) => {},
        fail: (res) => {},
        complete: (res) => {},
      })
	  },
    // 查看售后订单详情
    viewRefundOrder() {
      this.moreAsync = false
      uni.navigateTo({
        url: '/pages/order/order-refunds/form/index?orderItemId=' + this.orderInfo.
        orderItemId
      });
    },
    // 撤销申请--取消退款
    cancelRefund() {
      this.moreAsync = false
      uni.showModal({
        content: '确认撤销该订单吗？',
        cancelText: '我再想想',
        confirmColor: '#EE112B',
        success: (res) => {
          if (res.confirm) {
            api.tkorder({
              orderItemId: this.orderInfo.orderItemId,
              status: this.orderInfo.status,
              id: this.orderInfo.refundId,
            }).then((res) => {
              this.$emit('replaceOrder', res);
            });
          }
        }
      });
      
    },
    // 删除售后订单
    refundOrderDel() {
      this.moreAsync = false
      uni.showModal({
        content: '确认删除该订单吗？',
        cancelText: '我再想想',
        confirmColor: '#EE112B',
        success: (res) => {
          if (res.confirm) {
            this.$noMultipleClicks((refundId) => {
              api.orderRefundsDel(refundId).then(res => {
                this.$emit('orderDel', res);
              });
            }, this.orderInfo.refundId)
          }
        }
      });
    },
    // 查看发票详情
    viewInvoice() {
      this.moreAsync = false
      uni.navigateTo({
        url: `/pages/invoice/invoiceCenter/invoiceDesc?id=${this.orderInfo.orderInvoice.id}`
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.btn-more{
  width: 200rpx;
  height: 210rxp;
  background: #FFFFFF;
  border-radius: 15rpx;
  position: absolute;
  bottom: -80rpx;
  left: 0;
  padding: 20rpx 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  .more-item{
    font-size: 28rpx;
    color: #333333;
    // line-height: 35rpx;
    text-align: center;
    padding: 0 20rpx;
  }
  .more-border{
    padding-bottom: 20rpx;
    border-bottom: 2rpx dotted #000;
  }
  
}
button::after{ border: none;}
.button{
  display: block;
  height: 66rpx;
  line-height: 62rpx;
  border-radius: 33rpx;
  font-size: 28rpx;
  margin: 0 0 0 18rpx;
  background: #FFFFFF;
  border: none;
  outline: none;
}
.normal{
  border: 1rpx solid #CECECE;
  color: #000000;
}
.red{
  border: 1rpx solid #EE112B;
  color: #EE112B;
}
.gopay{
  border: 1rpx solid #EE112B;
  color: #FFF;
  background: #EE112B;
}

.count-down-box {
  // width: 300rpx;
  height: 66rpx;
  border-radius: 24rpx;
  background: #ffe3e6;
  display: flex;
  align-items: center;
  margin-left: 18rpx;
  padding: 0 50rpx 0 20rpx;
  line-height: 66rpx;
  .status {
    display: block;
    color: #ee132b;
  }
}
.remind-box{
  padding: 30rpx 60rpx 40rpx;
  width: 576rpx;
  background: #FFFFFF;
  border-radius: 45rpx;
  .remind-title{
    font-size: 32rpx;
    font-weight: 800;
    color: #000000;
    padding-bottom: 30rpx;
    border-bottom: 2rpx dotted #000;
  }
  .remind-text{
    margin: 30rpx 0 37rpx;
    font-size: 28rpx;
    color: #000000;
    line-height: 45rpx;
    text{
      color: #EC6353;
    }
  }
  .remind-btns{
    .btn{
      width: 220rpx;
      height: 66rpx;
      border-radius: 33rpx;
      color: #fff;
      line-height: 66rpx;
      font-size: 30rpx;
    }
    .customer-service{
      background: linear-gradient(-90deg, #CCAC92 0%, #C09979 0%, #D8BBA3 100%);
    }
    .igotit{
      background: linear-gradient(-90deg, #FF4E00 0%, #FF8463 100%);
    }
  }
}
</style>