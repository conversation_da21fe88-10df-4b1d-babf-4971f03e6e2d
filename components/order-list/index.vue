<template>
  <view>
    <!-- 店铺信息、订单状态 -->
    <view class="bg-white flex justify-between align-center margin-bottom-xs">
      <!-- 店铺信息：logo、名称 -->
      <navigator
        class="flex align-center shop-info "
        hover-class="none"
        :style="{'margin-left': parameter.status=='0' ? '60rpx' : '0'}"
        :url="'/pages/shop/shop-detail/index?id=' + orderInfo.shopInfo.id"
      >
        <image class="logo" mode="widthFix" :src="$imgUrl('live/shop/storeIcon.png')" />
        <text class="shop-name">
          {{ orderInfo.shopInfo.name.length > 16 ? orderInfo.shopInfo.name.slice(0, 16) + '...' : orderInfo.shopInfo.name }}
        </text>
        <text class="cuIcon-right text-sm" />
      </navigator>
      <!-- 订单状态 -->
      <view class="order-status">
        <!-- isPay: 0未支付；1已支付；status：状态0、待付款 1、待发货 2、待收货 3、已完成 4、已关闭 -->
        <text>
          {{ orderInfo.orderStatusDesc }}
        </text>
      </view>
    </view>
    <!-- 物流信息 -->
    <view
      v-if="orderInfo.logisticsTrackDesc"
      class="logistics-info flex align-center justify-between"
      @tap.stop="viewLogistics"
    >
      <text>{{ orderInfo.logisticsTrackDesc }}</text>
      <text class="cuIcon-right text-sm" />
    </view>
    <!-- 商品信息 -->
    <navigator
      hover-class="none"
      :url="'/pages/order/order-detail/index?id=' + orderInfo.id"
      class="cu-item commodity"
      v-for="(item2, index2) in orderInfo.listOrderItem"
      :key="index2"
    >
      <view class="content" style="padding: 0;">
        <!-- 商品图片 -->
        <image
          :src="item2.picUrl ? item2.picUrl : $imgUrl('live/img/no_pic.png')"
          mode="aspectFill"
          class="product-img margin-top-xs"
        />
        <view style="flex: 1;">
          <!-- 商品信息：类型、名称、规格、价格、数量 -->
          <view class="flex justify-between">
            <!-- 商品类型、商品名称 -->
            <view style="width: 320rpx;">
              <view class="text-black margin-top-xs overflow-2">
                <!-- 0--普通订单，1--砍价，2--拼团，3--秒杀，4--积分 -->
                <text
                  class="cu-tag bg-red sm margin-right-xs"
                  v-if="orderTypeAscs(orderInfo.orderType)"
                >{{ orderInfo.orderType | orderTypeText }}</text>
                <text class="text-df" style="font-weight: 600">{{ item2.spuName }}</text>
              </view>
              <!-- 规格信息 -->
              <view
                class="text-gray text-sm overflow-2 margin-top-xs"
                v-if="item2.specInfo"
              >{{ item2.specInfo }}</view>
            </view>
            <!-- 积分、价格、数量 -->
            <view style="flex: 1; text-align: right;">
              <view class="margin-top-xs">
                <view
                  v-if="orderInfo.orderType == 4 || orderInfo.orderType == 5"
                  style="font-size: 32rpx;"
                  class="text-df text-red margin-top-sm"
                >{{ item2.paymentPoints || '暂无' }}积分</view>
      
                <format-price 
                  styleProps="font-weight:bold"  
                  v-else
                  signFontSize="20rpx" 
                  smallFontSize="24rpx" 
                  priceFontSize="30rpx"
                  color="#000000"
                  :price="item2.salesPrice"
                />
                <view class="quantity" >x{{ item2.quantity }}</view>
              </view>
            </view>
          </view>
          <!-- 发货时间 -->
          <view v-if="item2.estimateExpressTime" class="delivery-time">
            {{ item2.estimateExpressTime }}
          </view>
          <!-- 实付款 -->
          <view class="flex text-black justify-end margin-top-lg" style="font-size: 28rpx;">
            <text style="color: #999; margin-right: 7rpx;">共{{ item2.quantity }}件</text>
            <view class="flex text-weight" style="font-weight:bold;">
              <view>实付款</view>
              <format-price 
                styleProps="font-weight:bold"
                signFontSize="20rpx" 
                smallFontSize="24rpx" 
                priceFontSize="30rpx"
                color="#000000"
                :price="item2.paymentPrice"
              />
            </view>
          </view>
        </view>
      </view>
    </navigator> 
    <!-- 退换货信息 -->
    <view class="return-info" v-if="orderInfo.refundDesc">
      {{ orderInfo.refundDesc }}
    </view>
    <!-- 操作按钮 -->
    <buttonList
      v-if="orderInfo.actionButtonList && orderInfo.actionButtonList.length" 
      :orderInfo="{
        orderStatusDesc: orderInfo.orderStatusDesc,
        actionButtonList: orderInfo.actionButtonList,
        qrImg: orderInfo.qrImg,
        orderId: orderInfo.id,
        listOrderItem: orderInfo.listOrderItem,
        orderNo: orderInfo.orderNo,
        createTime: orderInfo.createTime,
        salesPrice: orderInfo.salesPrice,
        paymentPrice: orderInfo.paymentPrice,
        orderLogistics: orderInfo.orderLogistics,
        shopInfo: orderInfo.shopInfo,
        outTime: orderInfo.outTime,
        index: index,
        orderType: orderInfo.orderType,
        bargainHallId: orderInfo.bargainHallId,
        paymentType: orderInfo.paymentType,
        orderInvoice:orderInfo.orderInvoice
      }"
      @orderDel="orderDel($event, index)"
      @orderCancel="orderCancel($event, index)"
      @orderReceive="orderReceive($event, index)"
      @orderLogistics="orderLogistics($event, index)"
      @countDownDone="countDownDone($event)"
    />
    <!-- 自提地址、时间 -->
    <view 
      class="self-delivery"
      v-if="orderInfo.actionButtonList && orderInfo.actionButtonList.indexOf('takeQrCode') != -1"
    >
      <view class="flex justify-between align-center" @tap="addressNavigation">
        <view>
          <view
            class="text-black"
            v-if="orderInfo.orderLogistics && orderInfo.orderLogistics.address"
          >自提地址：{{ orderInfo.orderLogistics.address }}</view>
          <view class="text-black" v-else>自提地址：{{ orderInfo.shopInfo.address }}</view>
          <view
            class="time" 
            v-if="orderInfo.orderLogistics && orderInfo.orderLogistics.takeTime"
          >自提时间：{{ orderInfo.orderLogistics.takeTime }}</view>
          <view class="time" v-else-if="orderInfo.oneselfTime">自提时间：{{ orderInfo.oneselfTime }}</view>
        </view>
        <text class="cuIcon-right text-sm" />
      </view>
      
    </view>
  </view>
</template>

<script>
import buttonList from './btns.vue';
import formatPrice from "@/components/format-price/index.vue";
import api from 'utils/api';

import QQMapWX from 'public/jweixin-module/qqmap-wx-jssdk.min.js';
import __config from 'config/env';
let qqmapsdk;

export default {
  props: {
    orderInfo: {
      type: Object,
      default: () => {}
    },
    index: {
      type: Number,
      default: 0
    },
    parameter: {
      type: Object,
      default: () => {}
    }
  },
  components: { formatPrice, buttonList },
  filters: {
    orderTypeText: function(val) {
      switch(val) {
        case '0':
          return '普通';
        case '1':
          return '砍价';
        case '2':
          return '拼团';
        case '3':
          return '秒杀';
        case '4':
          return '积分';
        case '5':
          return '付费优惠券';
        case '6':
          return '团购';
        default:
          return '';
      }
    }
  },
  data() {
    return {

    }
  },
  created() {
    // 实例化API核心类
    qqmapsdk = new QQMapWX({
      key: __config.mapKey
    });
  },
  methods: {
    orderTypeAscs(val) {
      switch(val) {
        case '0': // '普通'
          return false;
        case '1': // '砍价'
          return true;
        case '2': // '拼团'
          return true;
        case '3': // '秒杀'
          return true;
        case '4': // '积分'
          return false;
        case '5': // '付费优惠券'
          return false;
        case '6': // '团购'
          return false;
        default:
          return false;
      }
    },
    orderDel(event, index) {
      this.$emit('orderDel', event, index)
    },
    orderCancel(event, index) {
      this.$emit('orderCancel', event, index)
    },
    orderReceive(event, index) {
      this.$emit('orderReceive', event, index)
    },
    orderLogistics(event, index) {
      this.$emit('orderLogistics', event, index)
    },
    countDownDone(event) {
      this.$emit('countDownDone', event)
    },
    // 地图导航
    addressNavigation() {
      const name = this.orderInfo.shopInfo.name;
      if(this.orderInfo.orderLogistics) {
        const {latitude, longitude, address} = this.orderInfo.orderLogistics;
        if (latitude && longitude) {
          this.openLocation(latitude, longitude, name, address);
        } else {
          this.qqmapsdk(name, address);
        }
      } else {
        const address = this.orderInfo.shopInfo.address;
        this.qqmapsdk(name, address);
      }
    },
    // 通过地址解析经纬度--打开地图
    qqmapsdk(name, address) {
      qqmapsdk.search({
        keyword: address,
        success: (res) => {
          console.log("根据地址解析经纬度==",res);
          if (res && res.data.length > 0) {
            const latitude = res.data[0].location.lat;
            const longitude = res.data[0].location.lng;
            this.openLocation(latitude, longitude, name, address);
          }
        },
        fail: function(res) { },
        complete: function(res) { }
      });
    },
    // 打开地图
    openLocation(latitude, longitude, name, address) {
      uni.openLocation({
          type: "gcj02",
          latitude: Number(latitude),
          longitude: Number(longitude),
          // scale: 15,
          name: name,
          address: address,
          success: (result)=>{},
          fail: ()=>{},
          complete: ()=>{}
      });
    },
    // 查看物流
    viewLogistics() {
      // this.moreAsync = false
      // #ifdef MP-WEIXIN
      api.getWayBillTokenByOrderId(this.orderInfo.id).then(res => {
        this.orderLogistics(res, this.index);
      })
      // #endif
      // #ifndef MP-WEIXIN
      this.orderLogistics({}, this.index);
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
.shop-info{
  height: 44rpx;
  line-height: 44rpx;
  .logo {
    width: 34rpx;
    margin-right: 10rpx;
  }
  .shop-name {
    font-size: 33rpx;
    font-weight: 800;
    color: #000;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.order-status{
  font-size: 28rpx;
  height: 44rpx;
  line-height: 44rpx;
  text-align: right;
  color: #EE112B;
}
.logistics-info {
  border-radius: 21rpx; 
  background: #F9F9F9; 
  font-size: 26rpx; 
  color: #999999; 
  padding: 20rpx; 
  margin: 20rpx 0 10rpx;
}
.commodity{
  .product-img {
    width: 200rpx !important;
    height: 200rpx !important;
    border-radius: 10rpx;
  } 
  .quantity {
    color: #999;
    font-size: 28rpx;
  }
  .delivery-time{
    font-size: 28rpx;
    color: #EF9416;
  }
  
}

.return-info{
  border-radius: 21rpx;
  background: #F9F9F9;
  font-size: 26rpx; 
  padding: 20rpx;
  margin-top: 30rpx;
  color: #000000;
}
.self-delivery{
  border-radius: 21rpx;
  background: #F9F9F9;
  font-size: 26rpx;
  padding: 20rpx;
  margin-top: 25rpx;
  .time{
    color: #999;
  }
}


</style>