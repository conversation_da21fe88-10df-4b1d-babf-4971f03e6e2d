<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true" :hideMarchContent="true">
			<block slot="backText">返回</block>
			<block slot="content">直播大厅</block>
		</cu-custom>
		<view v-if="roomList&&roomList.length>0" class="live-container flex">
			<view class="live-box" v-for="(item, index) in roomList" :key="index">
				<navigator class="cu-card case"
					:url="'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=' + (item.roomid||item.roomId)">
					<view class="cu-item shadow" style="margin: 0rpx;">
						<view class="margin-top-sm-img">
							<image mode="scaleToFill" :src="item.coverImg" class="live-image radius live-image-2">
							</image>
							<view
								style="position: absolute;top: 10rpx;z-index: 999;color: white;overflow-x: hidden;text-overflow: ellipsis;white-space: nowrap;width: 330rpx;left: 10rpx;right:20rpx">
								{{item.name}}
							</view>
							<view v-if="item.liveStatus=='101'"
								style="position: absolute;top: 46rpx;z-index: 999;color: white;overflow-x: hidden;text-overflow: ellipsis;white-space: nowrap;width: 349rpx;left: 10rpx;">
								<view
									style="display: flex;align-items: center;justify-content: flex-start;font-size: 24px;font-size:24rpx; font-weight: 500;color: #000000;position: relative;">
									<image style="width: 160rpx;height:40rpx"
										:src="$imgUrl('1/material/e6ce5cc2-5230-4989-bf20-ab306925b5f0.png')"
										mode=""></image>
									<view style="position: absolute;display: flex;align-items: center; left: 4rpx;">
										<img style="width: 32rpx; height: 32rpx;"
											:src="$imgUrl('1/material/99307817-c125-43a2-af9f-85613227dd8e.gif')"
											alt="">
										<text style="padding-left: 10rpx;">直播中</text>
									</view>
								</view>
							</view>
							<view v-else-if="item.liveStatus=='102'"
								style="position: absolute;top: 46rpx;z-index: 999;color: white;overflow-x: hidden;text-overflow: ellipsis;white-space: nowrap;width: 349rpx;left: 10rpx;display: flex;align-items: center;margin-top: 6rpx;">
								<view
									style="display: flex;align-items: center;justify-content: flex-start;font-size: 24px;font-size:24rpx; font-weight: 500;color: #000000;position: relative;">
									<image style="width: 160rpx;height:40rpx"
										:src="$imgUrl('1/material/e6ce5cc2-5230-4989-bf20-ab306925b5f0.png')"
										mode=""></image>
									<view style="position: absolute;display: flex;align-items: center; left: 4rpx;">
										<img style="width: 32rpx; height: 32rpx;"
											:src="$imgUrl('1/material/1ba0fa9e-2b1c-4a72-9023-86ef9c90898e.gif')"
											alt="">
										<text style="padding-left: 10rpx;">直播预告</text>
									</view>
								</view>
							</view>
							<view v-else-if="item.liveStatus=='103'&&item.closeReplay=='0'"
								style="position: absolute;top: 46rpx;z-index: 999;color: white;overflow-x: hidden;text-overflow: ellipsis;white-space: nowrap;width: 349rpx;left: 10rpx;">
								<view
									style="display: flex;align-items: center;justify-content: flex-start;font-size: 24px;font-size:24rpx; font-weight: 500;color: #000000;position: relative;">
									<image style="width: 160rpx;height:40rpx"
										:src="$imgUrl('1/material/e6ce5cc2-5230-4989-bf20-ab306925b5f0.png')"
										mode=""></image>
									<view style="position: absolute;display: flex;align-items: center; left: 4rpx;">
										<img style="width: 32rpx; height: 30rpx;"
											:src="$imgUrl('1/material/f7f866b8-4393-48e3-8649-601518c7371c.gif')"
											alt="">
										<text style="padding-left: 10rpx;">直播回放</text>
									</view>
								</view>
							</view>
						</view>
						<view
							v-if="item.goods" style="position: absolute;bottom: 20rpx;height: 100rpx;width: 100%;display: flex;justify-content: space-around;z-index: 222;">
							<view class="" v-for="(items,index) in item.goods.slice(0,3)" :key="index"
								style="background: white;width: 110rpx;border-radius: 11rpx;;height: 110rpx;padding: 10rpx;display: flex;justify-content: center;align-items: center;">
								<image :src="items.coverImgUrl? items.coverImgUrl:item.coverImg +'.jpg'"
									style="width: 100rpx;justify-content: space-around;display: flex;height: 88rpx;border-radius: 11rpx;">
								</image>
							</view>
						</view>
						<!-- <t
						   ext class="text-cut overflow-1"
							style="margin: 48rpx 4rpx 0 4rpx; display: flex; justify-content: center;font-size: 30rpx; color: #000000;">{{item.shopInfo&&item.shopInfo.name}}</text>
						<text class="text-cut overflow-2"
							style="margin: 4rpx 4rpx 28rpx 4rpx; display: flex; justify-content: center;font-size: 24rpx; color: #444444;">{{item.name}}</text>
					      -->
					</view>
				</navigator>
			</view>
		</view>
		<baseNodata v-else-if="!loading" />
		<!--<recommendComponents v-if="!loading&&roomList.length>11" :canLoad="canLoad" /> -->
		<!-- <view :class="'cu-load bg-gray ' + (roomList.length>0?'':'over')"></view> -->
      	<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>
<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import recommendComponents from "components/recommend-components/index";
	import baseNodata from "components/base-nodata/index.vue";

	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				roomList: [],
				loading: true,
				canLoad: false,
				//有数统计使用
				page_title: '直播大厅',
				current: 1,
				total: '',
				loadmore:true,
				roomLists:[]
			};
		},

		components: {
			recommendComponents,
			baseNodata
		},
		props: {},

		onLoad(options) {
			uni.showLoading({
				title: '加载中'
			});
			app.initPage().then(res => {
				this.liveRoomInfoList();
			});
		},
		//上拉加载
		onReachBottom() {
			console.log(this.roomList.length, Number(this.total))
			// if (this.roomList.length >= Number(this.total)) {
			// 	uni.showToast({
			// 		title: '已加载全部数据',
			// 		icon: 'none',
			// 		duration: 1000
			// 	});
			// } else {
				this.current = this.current + 1
				this.liveRoomInfoList()
			// }
		},
		onPullDownRefresh() {
			// 显示顶部刷新图标
			this.current = 1
			this.roomList = []
			this.roomLists = []
			this.loadmore  =  true
			uni.showNavigationBarLoading();
			this.liveRoomInfoList();
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();

		},
		methods: {
			liveRoomInfoList() {
				let that = this 
				api.liveRoomInfoList({
					'liveStatus': '103,102,101',
					current: this.current,
					size: 10
				}).then(res => {
					//  直播间状态。101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期
					if (res.data.records && res.data.records.length > 0) {
						this.total = res.data.total
					res.data.records.forEach(item => {
					    that.roomLists.push(item)
				    if (item.liveStatus == 101 ||item.liveStatus == 102 || (item.liveStatus == 103 && item.closeReplay=='0'))  {
					      that.roomList.push(item)
					  }
					})
					 //如果没有数据的数据数据的时候
					if(that.roomLists.length>=res.data.total){   
						 this.loadmore = false
					}
					}
					this.loading = false;
					this.canLoad = true;
					uni.hideLoading();
				}).catch(e => {
					this.canLoad = true;
					uni.hideLoading();
				});
			}

		}
	};
</script>
<style>
	.live-container {
		justify-content: space-between;
		flex-wrap: wrap;
		box-sizing: content-box;
		padding: 20rpx;
	}

	.live-box {
		width: 349rpx;
		background-color: #fff;
		overflow: hidden;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
		position: relative;
	}

	.live-image {
		width: 100%;
		height: 456rpx;
	}

	.margin-top-sm-img {
		width: 100%;
		height: 560rpx;
		overflow: hidden;
		position: relative;
	}

	.live-image-1 {
		width: 158rpx;
		height: 49rpx;
		position: absolute;
		top: 3rpx;
		left: 0rpx;
		z-index: 2;
	}

	.live-image-2 {
		width: 100%;
		height: 560rpx;
		z-index: 1;
	}

	.live-image-3 {
		width: 125rpx;
		height: 250rpx;
		position: absolute;
		bottom: 0rpx;
		right: -10rpx;
		z-index: 2;
	}

	.live-image-4 {
		width: 82rpx;
		height: 82rpx;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: -21px;
		z-index: 1;
		border-radius: 50%;
	}

	.live-content {
		background-image: url(https://img.songlei.com/1/material/d2eb063b-ca23-47f1-b89d-7c40ef952eb2.png);
		background-size: 100% auto;
		background-repeat: no-repeat;
		padding: 10px 8px 0px 8px;
	}

	.broadcast {
		background: #fff;
		width: 100%;
		border-radius: 10px;
		display: flex;
		flex-wrap: wrap;
		padding: 10rpx 2% 20rpx 2%;
		box-sizing: border-box;
	}

	.broadcast-box {
		width: 25%;
		overflow: hidden;
		text-align: center;
		padding-top: 10px;
	}

	.broadcast-box image {
		width: 126rpx;
		height: 126rpx;
		display: block;
		margin: 0 auto;
	}

	.broadcast-box text {
		margin-top: 3px;
		font-size: 24rpx;
		color: #333333;
		line-height: 50rpx;
	}
</style>
