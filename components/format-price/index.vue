<template>
	<!-- 有划线的 -->
	<view v-if="styleTextDecoration !== 'none'" :style="styleProps" style="position: relative">
		<text :style="{ fontSize: signFontSizeFormat, lineHeight: 1, color }">￥</text>
		<text :style="{ fontSize: priceFontSizeFormat, lineHeight: 1, color: color }">{{ price | filterInt }}</text>
		<text :style="{ fontSize: smallFontSizeFormat, lineHeight: 1, color }">{{ price | filterSmall }}</text>
		<view class="line-center"></view>
	</view>

	<!-- 没有划线的 -->
	<view v-else :style="styleProps">
		<text :style="{ fontSize: signFontSizeFormat, lineHeight: 1, color }">￥</text>
		<text :style="{ fontSize: priceFontSizeFormat, lineHeight: 1,textStroke: 0, textShadow: 'none',  color: color }">{{ price | filterInt }}</text>
		<text :style="{ fontSize: smallFontSizeFormat, lineHeight: 1, color }">{{ price | filterSmall }}</text>
	</view>
</template>

<script>
import { mapState } from 'vuex';
export default {
	name: 'format-price',
	props: {
		// 约定不在style 里使用下划线属性
		styleProps: {
			type: String,
			default: 'font-weight: 400;'
		},
		// 划线单独提出来 划线默认没有
		styleTextDecoration: {
			type: String,
			default: 'none'
		},
		color: {
			type: String,
			default: '#e54d42'
		},
		signFontSize: {
			type: String,
			default: '26rpx'
		},
		priceFontSize: {
			type: String,
			default: '34rpx'
		},
		smallFontSize: {
			type: String,
			default: '26rpx'
		},
		price: {
			type: [String, Number],
			default: '0'
		}
	},
	computed: {
		...mapState(['isPhone']),
		signFontSizeFormat() {
			return this.fontFilter(this.signFontSize);
		},

		priceFontSizeFormat() {
			return this.fontFilter(this.priceFontSize);
		},

		smallFontSizeFormat() {
			return this.fontFilter(this.smallFontSize);
		}
	},
	filters: {
		filterInt(value) {
			value = value + '';
			if (value.startsWith('-')) {
				const priceInt = value.split('.')[0];
				// const prc = value.slice(1, -2);
				return priceInt;
			} else if (+value > 0) {
				const priceInt = value.split('.')[0];
				if (priceInt.startsWith('￥')) {
					return priceInt.substring(1);
				}
				return priceInt;
			}
			return '0';
		},

		filterSmall(value) {
			value = value + '';
			if (String(value).startsWith('-') && String(value).indexOf('.') != -1) {
				const priceSmall = value.split('.')[1];
				if ((priceSmall + '').length == 1) {
					return '.' + priceSmall + '0';
				}
				return '.' + priceSmall;
			} else if (+value > 0) {
				const priceSmall = value.split('.')[1];
				if (priceSmall > 0) {
					if ((priceSmall + '').length == 1) {
						return '.' + priceSmall + '0';
					}
					return '.' + priceSmall;
				}
				return '.00';
			}
			return '.00';
		}
	},

	methods: {
		fontFilter(value) {
			if (value && value.endsWith('rpx') && !this.isPhone) {
				//大屏幕上 转化px
				return Math.ceil(Number(value.substring(0, value.indexOf('rpx'))) / 2) + 'px';
			} else {
				return value;
			}
		}
	}
};
</script>

<style scoped>
.line-center {
	position: absolute;
	width: 100%;
	height: 2rpx;
	background: #000000;
	top: 50%;
}
</style>
