<template>
	<!-- 后台广告图片管理配置的广告， 根据广告类型做不同的展示 -->
	<view>
		<view v-if="type == 1 && adverts && adverts.length > 0">
			<swiper class="swiper" :style="styleProps" circular :indicator-dots="false" autoplay :interval="3000">
				<swiper-item v-for="(item, index) in adverts" :key="index">
					<view v-if="item && item.linkUrl" @click="checkLogin(item.linkUrl)">
						<image :src="item.imgUrl" mode="widthFix"
							style="width: 100%; height: 196rpx; border-radius: 20rpx"></image>
					</view>
				</swiper-item>
			</swiper>
		</view>
		<view v-else-if="(type == 2 || type == 3) && adverts && adverts.length > 0" :style="styleProps"
			style="height: auto !important">
			<view :style="{
					backgroundImage: `url(${adverts[0].imgUrl})`,
					backgroundSize: `cover`,
					overflow: `hidden`
				}">
				<scroll-view class="seckill-layout" scroll-x="true" style="overflow: hidden">
					<view v-if="type == 2" class="flex">
						<view v-for="(item, index) in adverts[0].seckillInfos" :key="index" @click="handleGo(item)"
							class="flex flex-direction align-center seckill-item margin-tb-xs margin-right-xs">
							<image class="goods-img" :src="item.picUrl | formatImg360"></image>
							<view class="text-xs text-white padding-tb-xs">{{ discounts(item) }}折秒</view>
						</view>
					</view>
					<view v-else-if="type == 3" class="flex">
						<view v-for="(item, index) in adverts[0].bargainInfos" :key="index" @click="handleGo(item)"
							class="flex flex-direction align-center seckill-item margin-tb-xs margin-right-xs">
							<image class="goods-img" :src="item.picUrl | formatImg360"></image>
							<view class="text-xs text-white padding-tb-xs">最多邀请{{ item.needPeopleNum }}人</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	import api from 'utils/api';
	import util from 'utils/util';
	import {
		gotoPage
	} from '@/components/div-components/div-base/div-page-urls.js';
	export default {
		props: {
			searchKey: {
				type: String,
				default: () => ''
			},
			styleProps: {
				type: String,
				default: 'font-weight: 400;'
			}
		},
		data() {
			return {
				adverts: '',
				type: 1, // 仅仅图片广告   2 秒杀广告  3 砍价广告
			};
		},
		computed: {
			discounts() {
				return function(item) {
					return parseFloat(((item.seckillPrice / item.originalPrice) * 10).toFixed(1));
				};
			}
		},
		created() {
			this.getAdvert();
		},

		methods: {
			// 获取多张图片
			getAdvert() {
				let selectAdverts = [];
				api.getAdvers({
					bigClass: this.searchKey
				}).then((res) => {
					this.adverts = res.data;
					if (this.adverts && this.adverts.length > 0) {
						for (let i = 0; i < this.adverts.length; i++) {
							if (this.adverts[i].type != 1) {
								this.type = this.adverts[i].type;
								selectAdverts.push(this.adverts[i]);
								break;
							}
						}
						if (this.type !== 1) {
							this.adverts = selectAdverts;
						}
					}
				});
			},

			handleGo(item) {
				if (this.type == 2) {
					if (item && item.id > 0) {
						gotoPage(`/pages/seckill/seckill-list/index?id=${item.id}`);
					} else {
						gotoPage(`/pages/seckill/seckill-list/index`);
					}
				} else {
					gotoPage(`/pages/bargain/bargain-list/index`);
				}
			},


			checkLogin(url) {
				if (!url) {
					uni.showToast({
						title: '开发中，敬请期待',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (!util.isUserLogin()) {
					uni.navigateTo({
						url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
					});
					return;
				}
				uni.navigateTo({ url });
			}

		}
	};
</script>

<style scoped lang="scss">
	.goods-img {
		width: 144rpx;
		height: 136rpx;
		border-radius: 16rpx;
	}

	.seckill-layout {
		margin-left: 128rpx;
		width: 580rpx;
	}

	.seckill-item {
		border-radius: 16rpx;
		background: #f42c23;
	}
</style>