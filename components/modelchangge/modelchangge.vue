<template>
  <view>
    <view
      class="cu-modal bottom-modal"
      :class="showchange ? 'show' : ''"
      catchtouchmove="touchMove"
    >
      <!-- 银行卡支付 原style="height:85%;"微信不支持 调整78%-->
      <view
        class="cu-dialog dialo-sku bg-white"
        :class="showchange ? 'animation-slide-bottom' : ''"
        style="
          height: 78%;
          padding-left: 30rpx;
          padding-right: 30rpx;
          padding-top: 30rpx;
        "
      >
        <view
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #afafaf;
          "
        >
          <view class="flextext">
            <text class="text-df blod" style="font-weight: bold">零钱</text
            ><text class="text-df blod leftmargin">
              (剩余 ¥{{ changemoney||0 }})</text
            >
          </view>
          <view class="flextext">
            <view
              @click="Usagerules"
              style="
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100rpx;
              "
            >
              <view style="font-weight: 500; color: #000000"> 使用规则 </view>
              <image
                style="width: 38rpx; height: 38rpx"
                :src= "$imgUrl('car/%E5%8F%B3%E7%AE%AD%E5%A4%B4%20(2).png')"
                mode="aspectFit|aspectFill|widthFix"
              />
            </view>
            <view @click="closeuse">
              <image
                style="
                  height: 50rpx;
                  width: 50rpx;
                  margin-left: 20rpx;
                  margin-bottom: 30rpx;
                  position: absolute;
                  right: 12rpx;
                  top: 12rpx;
                "
                :src= "$imgUrl('car/%E5%9B%BE%E5%B1%82%203%20(2).png')"
                mode="aspectFit|aspectFill|widthFix"
              />
            </view>
          </view>
        </view>
        <radio-group @change="radioChange " style="width: 100%">
          <label
            v-for="(item, index) in radioslist"
            :key="index"
            style="
              display: flex;
              width: 100%;
              justify-content: space-between;
              margin-top: 20rpx;
            "
          >
            <view style="color: #000000">{{ item.name }}</view>
            <view>
              <radio
                :value="item.value"
                :checked="item.checked"
                class="round margin-right-sm"
                :class="item.checked?'checked red':''"
              />
            </view>
          </label>
        </radio-group>

        <view v-if="showcustom">
          <view
            style="
              background: #f8f8f8;
              display: flex;
              align-items: flex-start;
              border-radius: 30rpx;
              height: 210rpx;
              margin-top: 20rpx;
              flex-direction: column;
              padding-left: 40rpx;
            "
          >
            <view class="text-df blod margigtop"> 零钱抵扣金额 </view>
            <view
              style="
                display: flex;
                font-size: 30rpx;
                margin-top: 20rpx;
                justify-content: center;
                align-items: center;
              "
            >
              <text style="font-size: 60rpx; color: #000000">¥</text>
              <input
                type="mumber"
                v-model="value"
                class="typeof_input"
              />
            </view>
          </view>
          <view
            style="color: #999999; font-size: 24rpx; margin-top: 10rpx"
            class="text-df"
          >
            本单您可使用{{ usershow }}零钱哦
          </view>
        </view>
        <view style="justify-content: center; display: flex">
          <button class="my-btn" @click="define">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import api from "utils/api";
export default {
  name: "modelchangge",
  props: {
    showchange: {
      type: Boolean,
      default: false,
    },
    showvalue: {
      type: String,
    },
    payPrice: {
      default: 0,
    },
  },
  data() {
    return {
      // radioslist: [],
      showcustom: false,
      value: "",
      changemoney: "",//剩余零钱
      changprop: "",
      usershow: false,
      checkoutvalue: "1",
      usetext: "",
    };
  },
  watch: {
    payPrice: {
      handler(newVal, oldVal) {
        if (newVal) {
          console.log("拿到了", newVal);
          
        }
      },
      deep: true,
      immediate: true,
    },
  },

  computed:{
     //渲染列表 并且渲染选中的状态
    radioslist(){
      console.log("this.checkoutvalue====>",this.checkoutvalue);
      return[
        {
          name: "暂时不使用",
          checked: this.checkoutvalue == "0" ? true : false,
          value: 0,
        },
        {
          name: `抵扣￥${this.usershow},最高使用`,
          checked: this.checkoutvalue == "1" ? true : false,
          value: 1,
        },
        {
          name: "自定义使用",
          checked: this.checkoutvalue == "2" ? true : false,
          value: 2,
        },
      ]
    } 
  },

  mounted() {
    // 获取剩余金额
    this.$EventBus.$on("purse-price", (val) => {
      console.log(this.payPrice, val, "purse-price");
      this.changemoney = val
    });

    //获取最高抵扣
    this.$EventBus.$on("purse-deduct-price", (val) => {
      console.log(this.payPrice, val, "purse-deduct-price");
      this.usershow = val
    });
  },
  beforeDestroy() {
    clearInterval(this.verifyCodeTimeout);
    this.$EventBus.$off('purse-deduct-price')
		this.$EventBus.$off('purse-price')
  },
  methods: {
    Usagerules() {
      // uni.$emit("UsagerulesModel",true);
      console.log('出发了')
      uni.navigateTo({
        url: "/pages/goods/goodsrules/goodsrules?pageName=produc",
      });
    },
    closeuse() {
      this.$emit("changeModal");
      // uni.$emit("showgetchange", "-1");
    },

    hideModalSku() {
      this.$emit("changeModal", false);
    },

    radioChange(e) {
      if (e.detail.value == "2") {
        this.showcustom = true;
        this.checkoutvalue = "2";
        this.radioslist.forEach((element) => {
          if (element.value == "2") {
            element.checked = true;
          } else {
            element.checked = false;
          }
        });
        return
      } else if (e.detail.value == "0") {
        this.showcustom = false;
        this.checkoutvalue = "";
        this.radioslist.forEach((element) => {
          if (element.value == "0") {
            element.checked = true;
          } else {
            element.checked = false;
          }
        });
        return
      } else if (e.detail.value == "1") {
        this.checkoutvalue = "1";
        this.showcustom = false;
        this.radioslist.forEach((element) => {
          if (element.value == "1") {
            element.checked = true;
          } else {
            element.checked = false;
          }
        });
        console.log(this.radioslist, "radioslist");
      }
    },
    define() {
      if (Number(this.value) > Number(this.usershow)) {
        this.value = "";
        uni.showToast({
          title: `最高使用零钱${this.usershow}`,
          icon: "none",
        });
      } else {
        this.radioslist.forEach((element) => {
          if (element.checked && element.value == "0") {
            this.usetext = "-1";
            this.$emit("changeModal");
            this.$EventBus.$emit("showgetchange", [element.value, 0]);
            return
          } else if (element.checked && element.value == "1") {
            this.$emit("changeModal");
            this.$EventBus.$emit("showgetchange", [this.usershow, element.value]); // 把传当前的抵扣金额传给零钱组件显示
            return
          } else if (element.checked && element.value == "2") {
            if (Number(this.value <= 1)) {
              uni.showToast({
                title: `使用零钱必须大于1元`,
                icon: "none",
              });
              return;
            }
            this.$emit("changeModal");
            this.$EventBus.$emit("showgetchange", [this.value, 2]);
            return
          }
          console.log("element.value",element.value);
        });
      }
    },
  },
};
</script>

<style scoped>
.typeof_input {
  width: 400rpx;
  height: 117rpx;
  font-size: 63rpx;
  font-weight: 900;
  margin-left: 10rpx;
  text-align: left;
  color: #000000;
}
.blod {
  font-weight: bold;
}
.my-btn {
  width: 497rpx;
  height: 90rpx;
  background: linear-gradient(0deg, #ff4e00 0%, #ff8463 100%);
  border-radius: 45rpx;
  padding: 10px 20px;
  color: white;
  font-weight: bold;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  width: 500rpx;
  position: fixed;
  bottom: 40rpx;
}
.cu-modal {
  z-index: 999999;
}
.leftmargin {
  margin-left: 30rpx;
}
.flextext {
  display: flex;
  align-items: center;
  color: #000000;
}
.margigtop {
  margin-top: 20rpx;
  color: #000000;
}
</style>
