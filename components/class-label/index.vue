<template>
	<view class="classLabel" :style="{marginBottom:marginBottom+'rpx'}">
		<view v-if="value=='LRRedWhite'" class="text-xsm label_LRRedWhiteBox" :style="{marginRight:marginRight+'rpx',marginLeft:marginLeft+'rpx'}">
			<view class="label_LRRedWhite">{{text}}</view>
			<view class="label_LRRedWhite_text">{{price}}</view>
		</view>

		<text v-else class="text-xsm" :class="'label_'+value" :style="{marginRight:marginRight+'rpx',marginLeft:marginLeft+'rpx',padding:padding[0]+'rpx '+padding[1]+'rpx',borderRadius:borderRadius+'rpx',fontSize:fontSize+'rpx'}" >{{text}}</text>
	</view>
</template>

<script>
	export default {
		name:"class-label",
		props:{
			text:{
				type:String,
				default:''
			},
			value:{
				type:String,
				default:''
			},
			price:{
				type:String,
				default:''
			},
			marginRight: {
				type: [Number, String],
				default: 0
			},
			marginLeft: {
				type: [Number, String],
				default: 0
			},
			marginBottom: {
				type: [Number, String],
				default: 16
			},
			padding: {
				type: Array,
				default(){
					return [4,10]
				}

			},
			borderRadius: {
				type: [Number, String],
				default: 10
			},
			fontSize: {
				type: [Number, String],
				default: 20
			},
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style scoped>
.classLabel{
	display: inline-block;
	margin-bottom: 16rpx;
}

/* 活动标题label */
.label_titleLable{
	background: linear-gradient(to right,#F42634,#CF1A26);
	color: #fff;
	font-weight: 300;
}
/* 自营 */
.label_zy{
	background: linear-gradient(to right,#FE3A16,#FF641A);
	border: 1rpx solid #FF2727;
	color: #fff;
}
/* 白底蓝字label_whiteBlue */
.label_1{
	background: #fff;
	border: 1rpx solid #0082BE;
	color: #0082BE;
}
/* 金底黑字label_goldenBlack */
.label_2{
	background: #FAD6A8;
	color: #343434;
	border: 1rpx solid #FAD6A8;
}
/* 异形金底白字label_goldenWhite */
.label_3{
	background: linear-gradient(to right,#C09979,#D5B79F);
	color: #fff;
	border-radius: 20rpx 6rpx 20rpx 6rpx!important;
	padding: 4rpx 16rpx!important;
}

/* 预售金底白字 */
.label_4{
	background: #F56F00;
	color: #FFFFFF;
	border: 1rpx solid #F56F00;
	font-weight: 500;
}
/* 购物预售白底金字 */
.label_5{
	background: #FFFFFF;
	color: #D7792D;
	border: 1rpx solid #D7792D;
}

/* 橙底白字 */
/* .label_zy{
	background: linear-gradient(to right,#FF9633,#FF6202);
	border: 1rpx solid #FF9633;
	color: #fff;
} */

/* 白底红字 */
.label_whiteRed{
	background: #fff;
	border: 1rpx solid #FF2727;
	color: #FF2727;
}

/* 绿底绿字 */
.label_greenBGreen{
	background: #eaf7ed;
	border-radius: 4rpx ;
	color: #73bb89;
}

 /* 粉底粉字 */
.label_pinkBPink{
	background: #ffe7f4;
	border-radius: 4rpx ;
	color: #fb5583;
}
/* 白底黑红字 */
.label_whiteBRed{
	background: #fff;
	border: 1rpx solid #C81823;
	color: #C81823;
}
/* 白底灰字 */
.label_whiteGrey{
	background: #fff;
	border: 1rpx solid #C2C2C2;
	color: #8B8B8B;
}
/* 灰底灰字 */
.label_lightDarkGrey{
	background: #F5F5F5;
	color: #777777;
	border: 1rpx solid #F5F5F5;
}

/* 新人礼 */
.label_LRRedWhiteBox{
	padding: 0;
	display: inline-flex;
	border: 1rpx solid #FF2727;
}
.label_LRRedWhite{
	padding: 4rpx 10rpx;
	background: linear-gradient(to right,#FF641A,#FE3A16);
}
.label_LRRedWhite_text{
	padding: 4rpx 10rpx;
	color: #FF2726;
}
</style>
