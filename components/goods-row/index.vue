<template>
	<view class="cu-card article no-card rowCard">
		<template v-if="goodsList && goodsList[0] && goodsList.length > 0">
			<view
				:style="{ paddingBottom: goodsSpace * multipleView + 'rpx' }"
				class="padding-top-xs padding-lr-sm cu-item goods-item"
				v-for="(item, index) in goodsList"
				:key="index"
			>
				<view
					v-if="item.picUrls"
					@click="
						toPage(
							`/pages/goods/goods-detail/index?id=${goodsListType === 'goodsrank' ? item.spuId : item.id}&skuId=${item.skus && item.skus[0].id}`,
							'goodDetail',
							item,
							index+1,
							searchKey,
							searchType,
							'商品单行列表'
						)
					"
				>
					<view class="content rowContent" style="padding-right: 0rpx">
						<view class="row-img">
							<view
								:class="
									(item &&
										item.estimatedPriceVo &&
										item.estimatedPriceVo.discountPrice &&
										item.estimatedPriceVo.discountPrice != '0.0' &&
										item.estimatedPriceVo.discountPrice != '0') ||
									(item.estimatedPriceVo &&
										item.estimatedPriceVo.promotionsDiscountPrice &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
									(item.estimatedPriceVo && item.estimatedPriceVo.coupon && item.estimatedPriceVo.coupon != '0.0' && item.estimatedPriceVo.coupon != '0')
										? 'activity-row-img'
										: ''
								"
								:style="{ borderColor }"
							>
								<lazy-load
									:image="item.picUrls[0] | formatImg360"
									mode="aspectFill"
									:markUrls="item.goodsMarkInfoVo ? item.goodsMarkInfoVo.angleMarkUrl : []"
								></lazy-load>
								<member-icon v-if="item.memberLevelLimit && item.memberLevelLimit != '101'" :memberLevelLimit="item.memberLevelLimit"></member-icon>
							</view>

							<view
								style="position: absolute"
								:style="{
									top: spuPriceStyle == 1 ? 0 : 'auto',
									bottom: spuPriceStyle == 1 ? 0 : '-10rpx',
									left: spuPriceStyle == 1 ? 0 : '-6rpx',
									right: spuPriceStyle == 1 ? 0 : '-6rpx'
								}"
								v-if="
									(item &&
										item.estimatedPriceVo &&
										item.estimatedPriceVo.discountPrice &&
										item.estimatedPriceVo.discountPrice != '0.0' &&
										item.estimatedPriceVo.discountPrice != '0') ||
									(item.estimatedPriceVo &&
										item.estimatedPriceVo.promotionsDiscountPrice &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
										item.estimatedPriceVo.promotionsDiscountPrice != '0') ||
									(item.estimatedPriceVo && item.estimatedPriceVo.coupon && item.estimatedPriceVo.coupon != '0.0' && item.estimatedPriceVo.coupon != '0')
								"
							>
								<good-price :goodsSpu="item" small></good-price>
							</view>

							<view
								v-if="goodsListType === 'rank'"
								class="iconfont icon-a-tuceng-22x2 rank-top"
								:style="{
									color: index == '0' ? '#FAC022' : index == '1' ? '#C9CEE7' : index == '2' ? '#F1A570' : '#B6B6B6'
								}"
							></view>
							<view v-if="goodsListType === 'rank'" class="rank-text" :style="{ left: isparseFloat(index) ? '24rpx' : '30rpx' }">
								{{ index + 1 }}
							</view>

							<!-- 商品销量排行榜 top -->
							<view v-if="goodsListType === 'goodsrank'">
								<view v-if="index == 0" style="position: absolute; top: 0; left: 0">
									<image style="width: 110rpx; height: 65rpx" class="margin-right-sm" :src="$imgUrl('rank/top-first.png')" />
									<text style="position: absolute; top: 2rpx; left: 0; font-weight: bold; font-size: 20rpx; color: #ffffff; width: 110rpx; text-align: center">
										榜首{{ item.topDays }}天
									</text>
								</view>
								<view v-else-if="index == 1" style="position: absolute; top: 0; left: 0">
									<image style="width: 110rpx; height: 65rpx" class="margin-right-sm" :src="$imgUrl('rank/top-second.png')" />
								</view>
								<view v-else-if="index == 2" style="position: absolute; top: 0; left: 0">
									<image style="width: 110rpx; height: 65rpx" class="margin-right-sm" :src="$imgUrl('rank/top-third.png')" />
								</view>
								<view v-else style="position: absolute; top: 0; left: 0">
									<image style="width: 53rpx; height: 59rpx" class="margin-right-sm" :src="$imgUrl('rank/top-bg.png')" />
									<text style="position: absolute; top: 16rpx; left: 0; font-weight: bold; font-size: 24rpx; color: #5d6d89; width: 53rpx; text-align: center">
										{{ index + 1 }}
									</text>
								</view>
							</view>

							<view class="sell-out" v-if="item.inventoryState == 1">
								<view class="sell-out-text">售罄</view>
							</view>
						</view>

						<view class="desc block item-content">
							<view class="titleCon text-black margin-top-xs text-df overflow-2 text-middle">
								<template v-if="item.goodsMarkInfoVo && item.goodsMarkInfoVo.activityName.length > 0">
									<class-label
										v-for="(item1, index_) in item.goodsMarkInfoVo.activityName"
										:key="index_"
										:text="item1"
										value="titleLable"
										:marginBottom="0"
										:marginRight="20"
									></class-label>
								</template>
								{{ item.name }}

								<template v-if="item.presaleState">
									<class-label :text="item.presaleState" value="4" :marginBottom="0" :padding="[6, 10]" :fontSize="22" :marginLeft="10"></class-label>
								</template>
							</view>
							
							<!-- 活动类型 1、常规活动，2、春节红包 -->
							<image
								v-if="actType == 2"
								:src="params.activityImage || 'https://img.songlei.com/live/NewYear-RedPacket/a-lucky-draw.png'"
								style="margin-top: -36rpx; width: 268rpx; height: 47rpx"
							/>

							<view class="applyScope margin-tb-xs" v-if="item.goodsMarkInfoVo">
								<view
									class="applyScopeCon margin-right-sm"
									v-for="(spuParameters, ind) in item.goodsMarkInfoVo.spuParametersList"
									:key="ind"
									v-if="ind < 3"
									style="max-width: 33%"
								>
									<view class="useScope text-thin text-xsm">{{ spuParameters.paremeterValue }}</view>
									<view class="useClass text-thin text-xsm" style="margin-top: 2rpx">{{ spuParameters.optionValue }}</view>
								</view>
								<!-- <view class="applyScopeCon">
								<view class="useScope">爽肤水</view>
								<view class="useClass">类别</view>
							</view> -->
								<!-- <view class="applyScopeCon">
								<view class="useScope">补水</view>
								<view class="useClass">功效</view>
							</view> -->
							</view>
							<!-- 功效 -->
							<view class="couponBox" v-if="pageType != 'retailStoreMy' && item.goodsMarkInfoVo">
								<class-label
									v-for="(item1, indexCoupon) in item.goodsMarkInfoVo.couponInfoName"
									:key="indexCoupon"
									:text="item1"
									value="whiteBRed"
									:marginRight="10"
									:borderRadius="6"
								></class-label>
							</view>
							<!-- 分销数据 有分销数据不展示分销 -->
							<view style="color: #bcbbc1" v-if="pageType == 'retailStoreMy'">
								<view class="flex">
									<text class="margin-right-sm">销量：{{ item.skus[0].distributionSaleNum ? item.skus[0].distributionSaleNum : 0 }}件</text>
									<text>退单：{{ item.skus[0].distributionRefundNum ? item.skus[0].distributionRefundNum : 0 }}件</text>
								</view>
								<view>累计返佣：¥{{ item.skus[0].distributionCommissionNum ? item.skus[0].distributionCommissionNum : '0.00' }}</view>
							</view>

							<view class="flex justify-between">
								<!-- <view class=""> -->
								<!-- 积分商品 -->
								<view v-if="item.spuType == 3 || item.spuType == 5" class="margin-top-xs">
									<text class="text-gray text-xs">积分：</text>
									<text class="text-red text-lg text-bold">{{ item.goodsPoints ? item.goodsPoints : 0 }}</text>
								</view>
								<!-- 付费商品 -->
								<view v-else>
									<view v-if="pageType == 'retailStoreAll' || pageType == 'retailStoreMy'">
										<text
											style="background: #efeeee; padding: 2rpx 0"
											v-for="(specsItem, index) in item.skus[0].specs"
											:key="index"
											:style="
												item.skus[0].specs.length > 1
													? index == 0
														? 'border-radius: 20rpx 0 0 20rpx; padding-left: 20rpx;'
														: index == item.skus[0].specs.length - 1
														? 'border-radius: 0 20rpx 20rpx 0; padding-right: 20rpx;'
														: 'padding: 2rpx 0;'
													: 'border-radius: 20rpx; padding: 2rpx 20rpx;'
											"
										>
											<text v-if="index == 0 && specsItem.specValueName">规格：</text>
											{{ specsItem.specValueName }}
											<text>{{ index < item.skus[0].specs.length - 1 ? '、' : '' }}</text>
										</text>
									</view>

									<!-- 活动类型 1、常规活动，2、春节红包 -->
									<view
										v-if="actType == 2 && item && item.estimatedPriceVo && item.estimatedPriceVo.originalPrice && item.estimatedPriceVo.estimatedPrice"
										class="direct-drop"
										:style="{
											background: `linear-gradient(0deg, ${params.reduceBgStart || '#304093'} 1%, ${params.reduceBgEnd || '#535DAD'} 100%)`
										}"
									>
										直降 ¥ {{ (item.estimatedPriceVo.originalPrice - item.estimatedPriceVo.estimatedPrice).toFixed(2) }}
									</view>

									<view class="text-price text-bold text-lg text-red">
										{{ actType == 2 ? item.estimatedPriceVo.estimatedPrice : item.priceDown || '0' }}
										<text
											v-if="item.estimatedPriceVo && item.priceDown != item.estimatedPriceVo.price"
											class="text-red text-xs"
											style="font-weight: 500; padding-left: 6rpx"
										>
											劵后价
										</text>

										<text
											v-if="item.estimatedPriceVo && item.estimatedPriceVo.originalPrice != '0.0' && item.priceDown != item.estimatedPriceVo.originalPrice"
											class="price-original text-xss text-gray"
										>
											￥{{ item.estimatedPriceVo.originalPrice }}
										</text>

										<text class="text-xss padding-lr-sm text-gray text-thin" v-if="goodsListType !== 'goodsrank'">已售{{ item.saleNum | saleNumFilter }}</text>
										<view
											v-if="pageType == 'retailStoreAll' || pageType == 'retailStoreMy'"
											class="flex"
											style="justify-content: flex-start; margin-top: 10rpx; margin-bottom: 10rpx"
										>
											<view
												class="text-df"
												style="
													line-height: 40rpx;
													height: 4;
													background: linear-gradient(-90deg, #ff4e00 0%, #ff8463 100%);
													border-radius: 30rpx;
													color: white;
													padding: 0 12rpx;
												"
											>
												<text>分享最高可赚：</text>
												<text>￥{{ item.rebateAmount ? item.rebateAmount : '0.00' }}</text>
											</view>
										</view>
									</view>
									<!-- 商品排行展示的销量文字，不展示具体的销量数字，例如“近千万人购买”等 -->
									<view v-if="goodsListType === 'goodsrank'" style="font-weight: 500; font-size: 24rpx; color: #888888">{{ item.saleNumStr }}</view>
								</view>
								<view
									v-if="showCart && item.spuType != '6'"
									@tap.stop="addShopCar($event, item, 230)"
									class="round buy text-xl"
									:class="'bg-' + theme.themeColor + '-border'"
									style="border: none"
								>
									<text class="cuIcon-cart"></text>
								</view>
							</view>
							<view class="titleBox" @click.stop="getshop(item)" style="margin-bottom: 12rpx; display: flex; align-items: center" v-if="item.shopName">
								<view style="display: flex; color: #757575; align-items: center">
									<view class="text-df text-black text-sm margin-top-xs flow-1">
										{{ item.shopName }}
									</view>
									<view style="font-size: 25rpx; padding-top: 8rpx; padding-right: 14rpx">
										<text class="cuIcon-right text-sm"></text>
									</view>
								</view>
							</view>
							<!-- </navigator> -->
						</view>

						<!-- 春节红包 限时秒杀 -->
						<image
							v-if="actType == 2"
							style="position: absolute; left: 0; width: 150rpx; height: 38rpx"
							:src="params.productCornerNotation || 'https://img.songlei.com/live/NewYear-RedPacket/spike.png'"
						/>
					</view>
				</view>
				<view :class="[index == '5' ? 'activerow' : '']" v-if="index == '5' && ShopInfos.length > 0">
					<view>
						<div-goods-row v-for="(items, inde) of ShopInfos" :value="items" :key="inde"></div-goods-row>
					</view>
				</view>
			</view>
			<!-- 加入购物车动画 cartx 和 carty 是购物车位置在屏幕位置的比例 例如左上角x0.1 y0.1 右下角 x0.9 y0.9-->
			<shopCarAnimation @caetAnimation="caetAnimation" ref="carAnmation" cartx="0.9" carty="1"></shopCarAnimation>
		</template>
	</view>
</template>

<script>
import divGoodsRow from '@/components/div-components/div-goods-row/div-goods-row.vue';
import memberIcon from '@/components/member-icon/index.vue';
import lazyLoad from 'components/lazy-load/index';
import goodPrice from 'components/good-price/good-price.vue';
import shopCarAnimation from 'components/cart/fly-in-cart.vue';
import api from 'utils/api';
import { navigateUtil } from '../../static/mixins/navigateUtil.js';
const app = getApp();
import { mapState } from 'vuex';

export default {
	mixins: [navigateUtil],
	data() {
		return {
			temp: {},
			ShopInfos: this.otherShopInfos,
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			titleLable: [
				// {
				// 	text:'松鼠秒杀',
				// 	value:'titleLable'
				// }
			],
			btmLabel: [
				// {
				// 	text:'自营',
				// 	value:'zy'
				// },
				// {
				// 	text:'放心购',
				// 	value:'whiteBlue'
				// },
				// {
				// 	text:'新人礼',
				// 	value:'LRRedWhite',
				// 	price:'288元'
				// }
			],
			applyScope: [
				// {
				// 	scope:'任何肤质',
				// 	class:'适合肤质'
				// }
			],
			isShake: true,
			cartCount: 0
		};
	},

	components: {
		lazyLoad,
		goodPrice,
		shopCarAnimation,
		divGoodsRow,
		memberIcon
	},
	props: {
		goodsList: {
			type: Array,
			default: () => []
		},
		goodsSpace: {
			type: String | Number,
			default: 8
		},
		//商品列表类型 rank是商品排行榜
		goodsListType: {
			type: String,
			default: ''
		},
		//是否显示购物车
		showCart: {
			type: Boolean,
			default: false
		},
		otherShopInfos: {
			type: Array,
			default: () => []
		},
		// 页面类型
		pageType: {
			type: String,
			default: ''
		},
		// 活动类型 1、常规活动，2、春节红包
		actType: {
			type: String,
			default: ''
		},

		params: {
			type: Object,
			default: () => {}
		},
		searchType: {
			type: String,
			default: ''
		},
		searchKey: {
			type: String,
			default: ''
		}
	},
	computed: {
		...mapState(['isPhone', 'multipleView']),
		borderColor: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.spuPriceStyle == 1 ? '' : customFiled.spuPicBorder;
			}
			return '#d9bda5';
		},
		spuPriceColor: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				if (customFiled.spuPriceStyle == 1) {
					return customFiled.spuPriceColor1 || '#fff';
				}
				return customFiled.spuPriceColor2 || '#fff';
			}
			return '#fff';
		},

		spuPriceStyle: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.spuPriceStyle || 2;
			}
			return 2;
		}
	},
	methods: {
		getshop(item) {
			uni.navigateTo({
				url: '/pages/shop/shop-detail/index?id=' + item.shopId
			});
		},
		//购物车摇
		caetAnimation(isShake) {
			console.log('=====isShake====', isShake);
			this.isShake = isShake;
		},

		onCaetAnimation() {
			this.$emit('onCaetAnimation', 'shake', this.cartCount);
		},

		//获取商品详情
		defaultAdd(id, i) {
			api.defaultAdd({
				spuId: id
			}, i)
				.then((res) => {
					console.log('添加购物车', res);
					//购物车数量
					if (res.code == '0') {
						this.cartCount = res.data.count;
						if (this.isShake) {
							this.onCaetAnimation();
						}
					}
				})
				.catch((res) => {});
		},

		// 加入购物车动画
		addShopCar(e, i, n) {
			console.log('加入购物车', e, i);
			// 成功的话，调用加入购物车动画 e 事件  i 商品  n 点击购物车的X坐标
			this.$refs.carAnmation.touchOnGoods(e, i.picUrls[0], n);
			this.defaultAdd(i.id, i);
		},

		isparseFloat(i) {
			if (parseInt(i) > 8) {
				console.log('true', i);

				return true;
			}
			console.log('false', i);
			return false;
		}
	},
	watch: {
		otherShopInfos() {
			console.log(this.otherShopInfos, 'otherShopInfos接收');
		}
	}
};
</script>
<style scoped>
.margin-bottom-sm {
	/* margin-bottom: -37rpx; */
}
.activerow {
	margin-top: 20rpx;
	height: auto;
}

.rank-text {
	font-size: 22rpx;
	text-align: center;
	position: absolute;
	top: 35rpx;
	left: 26rpx;
	z-index: 10;
	color: #fff;
}

.rank-top {
	width: 70rpx;
	height: 65rpx;
	text-align: center;
	z-index: 9;
	position: absolute;
	top: 10rpx;
	font-size: 65rpx;
}

.goods-item {
	margin: auto;
	border-radius: 10rpx !important;
}

.row-img {
	/* margin-top: 26rpx; */
	/* margin-left: -14rpx; */
	margin-left: -28rpx;
	width: 280rpx !important;
	height: 280rpx !important;
	min-width: 280rpx !important;
	min-height: 280rpx !important;
	overflow: hidden;
	border-radius: 10rpx;
	margin-right: 20rpx;
	border-radius: 6rpx;
	position: relative;
}

.activity-row-img {
	border-top: solid 6rpx transparent;
	border-left: solid 6rpx transparent;
	border-right: solid 6rpx transparent;
}

.buy {
	/* padding: 10rpx 20rpx 10rpx 20rpx; */
	display: inline-flex;
	justify-content: center;
	align-items: center;
	width: 60rpx;
	height: 60rpx;
	font-weight: 700;
	flex-shrink: 0;
}

.rowContent {
	padding: 0 20rpx;
	background-color: #fff;
	justify-content: space-around;
	position: relative;
}

/* 适用范围部分 */
.applyScope {
	display: flex;
	justify-content: space-around;
}

.applyScopeCon {
	text-align: center;
}

.useScope {
	color: #2b2b2b;
	line-height: 34rpx;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.useClass {
	color: #eb8c5c;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

/* 优惠券部分 */
.couponBox {
	line-height: 32rpx;
}

/* .couponCon{
		padding: 4rpx 10rpx;
		color: #C81823;
		font-size: 20rpx;
		border: 1rpx solid #C81823;
		border-radius: 6rpx;
		margin-right: 10rpx;
	} */
/* 底部标签 */
.btmLabelCon {
	display: flex;
	flex-wrap: wrap;
	overflow: hidden;
	height: 48rpx;
}

/* 直降 */
.direct-drop {
	height: 44rpx;
	background: linear-gradient(0deg, #304093 1%, #535dad 100%);
	border-radius: 8rpx;
	color: #fff;
	padding: 0 20rpx;
	line-height: 44rpx;
	display: inline-block;
	position: relative;
	overflow: hidden;
}
/* 白色条 */
.direct-drop::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%; /* 调整宽度以使白色条更窄 */
	height: 100%; /* 调整高度以使白色条更窄 */
	background: rgba(255, 255, 255, 1);
	transform: rotate(-45deg);
	animation: shine 2s infinite;
}

@keyframes shine {
	0% {
		transform: translate(-100%, 100%) rotate(-45deg);
	}
	100% {
		transform: translate(100%, -100%) rotate(-45deg);
	}
}

.item-content {
	max-width: 430rpx;
}

@media (min-width: 549px) {
	.row-img {
		width: 198rpx !important;
		height: 198rpx !important;
		min-width: 198rpx !important;
		min-height: 198rpx !important;
	}

	.item-content {
		max-width: 550rpx;
	}

	.applyScope {
		justify-content: flex-start;
	}
}
</style>
