<template>
  <view style="position: relative;z-index: 999999;">
    <view class="phone-popup-mask" v-if="visible" @click="handleClose"></view>
    <view class="phone-popup" v-if="visible">
      <!-- <view class="phone-popup-title">{{ title }}</view> -->
	  <view style="margin-top: 50rpx;text-align: center;font-size: 31rpx;padding-left: 20rpx;padding-right: 20rpx;color: #000000;">
	    请输入手机<text style="color:#BD976F;padding-left: 10rpx;padding-right: 10rpx;">{{userinfo.phone|phoneslice}}</text>收到的短信验证码	
	  </view>
      <view class="phone-popup-input">
        <input type="tel" class="phone-popup-input-text" v-model="verifyCode" placeholder="请输入验证码" />
        <view class="phone-popup-verify-btn" :disabled="verifyCodeBtnDisabled" @click="handleVerifyCodeBtnClick">{{ verifyCodeBtnText }}</view>
      </view>
      <view class="phone-popup-btn-group">
        <!-- <button class="phone-popup-btn" @click="handleCancel">{{ cancelText }}</button> -->
        <view class="phone-popup-btn" @click="handleConfirm">{{ confirmText }}</view>
      </view>
    </view>
  </view>
</template>
<script>
import api from 'utils/api'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '请输入手机号'
    },
    prefix: {
      type: String,
      default: '+86'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    confirmText: {
      type: String,
      default: '验证'
    },
  
  },
  data() {
    return {
      phoneNumber: '',
      verifyCode: '',
      verifyCodeBtnDisabled: false,
      verifyCodeBtnText: '获取验证码',
      verifyCodeTimeout: null,
      verifyCodeTime: 60,
	    userinfo:'',
      phonemsg:""
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.phoneNumber = ''
        this.verifyCode = ''
        this.verifyCodeBtnDisabled = false
        this.verifyCodeBtnText = '获取验证码'
        this.verifyCodeTime = 60
        clearInterval(this.verifyCodeTimeout)
      }
    }
  },
  filters:{
    phoneslice(value){
      return  value.slice(0,4)+'****'+value.slice(7,11)
    }
  },
  beforeDestroy() {
    clearInterval(this.verifyCodeTimeout)
    this.$EventBus.$off('Displayphone') //调用零钱组件关闭
      //调用组件显示零钱扣款金额
    this.$EventBus.$off('Calculateprice') //调用零钱组件关闭
  },
  methods: {
    handleClose() {
      clearInterval(this.verifyCodeTimeout)
      this.$emit('update:visible', false)
    },
    handleCancel() {
      this.handleClose()
    },
    handleConfirm() {
      // 处理提交手机号和验证码的逻辑
      if(this.verifyCode==''){
        uni.showToast({
				title:'请输入验证码',
				icon:'none'
			})
      }else{

      this.phonemsg  = uni.getStorageSync('changmsg')
      let obj ={}  
      obj.oritranFlow  = this.getphone.oritranFlow, 
      obj.accountNo  = this.getphone.accountNo, 
      obj.accountName  = this.getphone.accountName,
      obj.certType = this.getphone.certType,
      obj.certNo= this.getphone.certNo,
      obj.mobileNo  = this.phonemsg.mobilePhone,
      obj.phoneToken =   this.getphone.phoneToken,
      obj.phoneverCode=this.verifyCode
        api.testverify(obj).then(res=>{
        uni.showToast({
          title:'零钱钱包验证通过',
          icon:'none'
        })
        clearInterval(this.verifyCodeTimeout)
        this.$EventBus.$emit('Displayphone',false) //调用零钱组件关闭
        //调用组件显示零钱扣款金额
        // this.$EventBus.$emit('Calculateprice') 
        this.handleClose()
        })
      }
    },
    handleVerifyCodeBtnClick() {
      // 处理获取验证码的逻辑
      // ...
	  api.getcode().then(res=>{
		  console.log(res)
		  if(res.code=='0'){
        this.getphone = res.data
			  this.verifyCodeBtnDisabled = true
			  this.verifyCodeBtnText = `${this.verifyCodeTime}秒后重试`
			  this.verifyCodeTimeout = setInterval(() => {
			    this.verifyCodeTime--
			    this.verifyCodeBtnText = `${this.verifyCodeTime}秒后重试`
			    if (this.verifyCodeTime === 0) {
			      clearInterval(this.verifyCodeTimeout)
			      this.verifyCodeBtnText = '获取验证码'
			      this.verifyCodeBtnDisabled = false
			      this.verifyCodeTime = 60
			    }            
			  }, 1000)
		  }
	  })
    }
  },
  created() {
  	 this.userinfo = uni.getStorageSync('user_info')
     this.phonemsg  = uni.getStorageSync('changmsg')

  }
}
</script>
<style scoped>
.phone-popup-verify-btn{
  width: 247rpx;
  background: #DCC2AD;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #000000;

}
.phone-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
   width: 100%;
   height: 100%;
   background-color: rgba(0, 0, 0, 0.5);
   z-index: 9999;
 }
 
 .phone-popup {
   position: fixed;
   top: 27%;
   left: 50%;
   transform: translate(-50%, -50%);
   width: 90%;
   max-width: 400px;
   border-radius: 30px;
   overflow: hidden;
   background-color: #fff;
   z-index: 10000;
   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
 }
 
 .phone-popup-title {
   padding: 15px;
   font-size: 16px;
   font-weight: bold;
   text-align: center;
   background-color: #f5f5f5;
   color: #333;
 }
 
 .phone-popup-input {
   display: flex;
   align-items: center;
   margin: 30px 15px;
   border-bottom: 1px solid #e5e5e5;
   padding-bottom: 26rpx;
 }
 
 .phone-popup-inputs {
   display: flex;
   align-items: center;
   margin: 30px 15px;
   border-bottom: 1px solid #e5e5e5;
   padding-bottom: 10px;
 }
 
 
 .phone-popup-input-prefix {
   margin-right: 10px;
   font-size: 14px;
   color: #333;
 }
 
 .phone-popup-input-text {
   flex: 1;
   margin: 0;
   padding: 0;
   border: none;
   outline: none;
   font-size: 14px;
   color: #333;
 }
 
 .phone-popup-code-button {
   margin-left: 10px;
   padding: 5px 10px;
   font-size: 14px;
   color: #fff;
   background-color: #f80;
   border: none;
   border-radius: 2px;
   outline: none;
 }
 
 .phone-popup-buttons {
   display: flex;
   justify-content: space-between;
   margin-top: 20px;
 }
 
 .phone-popup-button {
   flex: 1;
   padding: 10px;
   font-size: 16px;
   font-weight: bold;
   text-align: center;
   background-color: #f5f5f5;
   color: #333;
   cursor: pointer;
   user-select: none;
 }
 
 .phone-popup-button-primary {
   background-color: #f80;
   color: #fff;
 }
 .phone-popup-btn-group{
	 display: flex;
   padding-bottom: 50rpx;
   justify-content: center;

 }
 .phone-popup-btn{
    width: 305rpx;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #000000;
    background: #DCC2AD;


 }
 </style>

