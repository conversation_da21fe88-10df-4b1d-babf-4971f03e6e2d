<template>
	<!-- 直播列表组件 -->
	<view :style="{
      marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
      marginTop: `${newData.marginTopSpacing * 2}rpx`,
      marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.marginRightSpacing * 2}rpx`,
    }" :class="
      newData.background &&
      newData.background.length > 0 &&
      newData.background != undefined
        ? newData.background
        : ''
    ">
		<view :style="{
        backgroundColor: newData.background,
        backgroundSize: 'contain',
        paddingBottom: `${newData.paddingBottomSpacing}px`,
        paddingTop: `${newData.paddingTopSpacing}px`,
        paddingLeft: `${newData.paddingLeftSpacing}px`,
        paddingRight: `${newData.paddingRightSpacing}px`,
        backgroundImage: 'url(' + newData.bgImage + ')',
        backgroundRepeat: 'repeat',
        minHeight: '100%',
      }">
			<live-item :roomList="roomLists" :loading="loading" :loadmore="loadmore" />
		</view>
	</view>

</template>
<script>
	const app = getApp();
	import liveItem from "@/components/div-components/div-living-list/live-item.vue";
	import api from 'utils/api';
	import {
		EventBus
	} from '@/utils/eventBus.js'
	export default {
		name: 'basic-image',
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {}
				}
			}
		},
		components: {
			liveItem
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				page: {
					start: 1
					// limit: 10
				},
				startTag: 1,
				loading: true,
				loadmore: false,
				canLoad: false,
				//有数统计使用
				page_title: '直播大厅',
				current: 1,
				total: '',
				roomLists: [],
			};
		},
		mounted() {
			this.refresh();
			EventBus.$on("pageFresh", () => {
				this.refresh();
			})
			EventBus.$on("divGoodsGroupsReachBottom", () => {
				this.scrolltolower();
			})
		},
		methods: {
			scrolltolower() {
				if (this.roomLists.length >= this.total) {
					this.loadmore = false
				} else {
					this.loadmore = true;
					this.current = this.current + 1
					if (this.loadmore) {
						// this.page.start = this.roomInfoList[this.selectMenuId].length|| 1;
						this.getData();
					}
				}
			},
			refresh() {
				this.getData();
			},

			getData() {
				let that = this;
				if (this.current == 1) {
					that.roomLists = [];
				}
				api.liveRoomInfoList({
					'liveStatus': '103,102,101',
					current: this.current,
					size: 10
				}).then(res => {
					//  直播间状态。101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期
					if (res.data&&res.data.records && res.data.records.length > 0) {
						this.total = res.data.total;
						that.roomLists = that.roomLists.concat(res.data.records);
						//如果没有数据的数据数据的时候
					}
					if (that.roomLists.length >= res.data.total) {
						this.loadmore = false
					}
					this.loading = false;
					uni.hideLoading();
				}).catch(e => {
					this.canLoad = true;
					uni.hideLoading();
				});
			}
		}
	}
</script>
