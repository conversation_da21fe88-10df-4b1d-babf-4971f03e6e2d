<template>
	<view>
		<view v-if="roomList&&roomList.length>0" class="live-container flex">
			<view class="live-box" v-for="(item, index) in roomList" :key="index">
				<navigator class="cu-card case"
					:url="'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=' + (item.roomid||item.roomId)">
					<view class="cu-item shadow" style="margin: 0rpx;">
						<view class="margin-top-sm-img">
							<image mode="aspectFill" :src="item.coverImg" class="live-image radius live-image-2">
							</image>
							<view
								style="position: absolute;top: 0;z-index: 1; background:linear-gradient(rgba(0,0,0,0.7),rgba(0,0,0,0)); padding-top: 6rpx; padding-bottom: 20rpx;">
								<view
									style="color: white;overflow-x: hidden;text-overflow: ellipsis;white-space: nowrap;width: 330rpx;margin-left: 10rpx;margin-right:20rpx">
									{{item.name}}
								</view>
								<view v-if="item.liveStatus=='101'"
									style="color: white;overflow-x: hidden;text-overflow: ellipsis;white-space: nowrap;width: 349rpx;margin-left: 10rpx;">
									<view
										style="display: flex;align-items: center;justify-content: flex-start;font-size: 24px;font-size:24rpx; font-weight: 500;color: #000000;position: relative;">
										<image style="width: 160rpx;height:40rpx"
											:src="$imgUrl('1/material/e6ce5cc2-5230-4989-bf20-ab306925b5f0.png')"
											mode=""></image>
										<view style="position: absolute;display: flex;align-items: center; left: 4rpx;">
											<img style="width: 32rpx; height: 32rpx;"
												:src="$imgUrl('1/material/99307817-c125-43a2-af9f-85613227dd8e.gif')"
												alt="">
											<text style="padding-left: 10rpx;">直播中</text>
										</view>
									</view>
								</view>
								<view v-else-if="item.liveStatus=='102'"
									style="color: white;overflow-x: hidden;text-overflow: ellipsis;white-space: nowrap;width: 349rpx;margin-left: 10rpx;display: flex;align-items: center;margin-top: 6rpx;">
									<view
										style="display: flex;align-items: center;justify-content: flex-start;font-size: 24px;font-size:24rpx; font-weight: 500;color: #000000;position: relative;">
										<image style="width: 160rpx;height:40rpx"
											:src="$imgUrl('1/material/e6ce5cc2-5230-4989-bf20-ab306925b5f0.png')"
											mode=""></image>
										<view style="position: absolute;display: flex;align-items: center; left: 4rpx;">
											<img style="width: 32rpx; height: 32rpx;"
												:src="$imgUrl('1/material/1ba0fa9e-2b1c-4a72-9023-86ef9c90898e.gif')"
												alt="">
											<text style="padding-left: 10rpx;">直播预告</text>
										</view>
									</view>
								</view>
								<view v-else-if="item.liveStatus=='103'&&item.closeReplay=='0'"
									style="color: white;overflow-x: hidden;text-overflow: ellipsis;white-space: nowrap;width: 349rpx;margin-left: 10rpx;">
									<view
										style="display: flex;align-items: center;justify-content: flex-start;font-size: 24px;font-size:24rpx; font-weight: 500;color: #000000;position: relative;">
										<image style="width: 160rpx;height:40rpx"
											:src="$imgUrl('1/material/e6ce5cc2-5230-4989-bf20-ab306925b5f0.png')"
											mode=""></image>
										<view style="position: absolute;display: flex;align-items: center; left: 4rpx;">
											<img style="width: 32rpx; height: 30rpx;"
												:src="$imgUrl('1/material/f7f866b8-4393-48e3-8649-601518c7371c.gif')"
												alt="">
											<text style="padding-left: 10rpx;">直播回放</text>
										</view>
									</view>
								</view>
							</view>

						</view>
						<view v-if="item.goods&&item.goods.length"
							style=" background:linear-gradient(rgba(0,0,0,0),rgba(0,0,0,0.7)); padding: 50rpx 0 20rpx 0; position: absolute;bottom:0;width: 100%;display: flex;justify-content: space-around;z-index: 1;">
							<view class="" v-for="(items,index) in item.goods.slice(0,3)" :key="index"
								style="background: white;width: 110rpx;border-radius: 11rpx;;height: 110rpx;display: flex;justify-content: center;align-items: center;">
								<image mode="aspectFill"
									:src="items.coverImgUrl? items.coverImgUrl:item.coverImg +'.jpg'"
									style="width: 100%;justify-content: space-around;display: flex;height: 100%;border-radius: 11rpx;">
								</image>
							</view>
						</view>
					</view>
				</navigator>
			</view>
		</view>
		<baseNodata v-else-if="!loading" />
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
	import divNoData from '../../base-nodata/index.vue'
	import lazyLoad from "components/lazy-load/index";
	export default {
		name: 'live-list-item',
		props: {
			roomList: {
				type: Array | null,
				default: function() {
					return []
				}
			},

			loading: {
				type: Boolean,
				default: function() {
					return false
				}
			},

			loadmore: {
				type: Boolean,
				default: function() {
					return false
				}
			},
		},
		components: {
			divBaseNavigator,
			divNoData,
			lazyLoad
		},

	}
</script>


<style scoped lang="scss">
	.live-image-1 {
		width: 158rpx;
		height: 49rpx;
		position: absolute;
		left: 0rpx;
		z-index: 2;
	}

	.live-image-2 {
		width: 36rpx;
		height: 36rpx;
		position: absolute;
		z-index: 2;
	}

	.living {
		display: block;
		margin: 0 auto;
		text-align: center;
		/* 文本过长显示省略号*/
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		position: relative;


		.shopInfo-font {
			margin-top: 7rpx;
			padding-top: 40rpx;
			font-size: 30rpx;
			font-weight: 500;
			color: #000000;
		}

		.living-name {
			margin-top: 10rpx;
			margin-bottom: 20rpx;
			font-size: 24rpx;
			font-weight: 500;
			color: #444444;
		}

		.living-btn {
			width: 255rpx;
			height: 45rpx;
			background: linear-gradient(90deg, #ed5279 0%, #ff0043 100%);
			border-radius: 23rpx;
			font-size: 24rpx;
			font-weight: 500;
			color: #ffffff;
			margin: 0 auto;
			line-height: 45rpx;
			margin-bottom: 10rpx;
		}


	}

	.live-container {
		justify-content: space-between;
		flex-wrap: wrap;
		box-sizing: content-box;
		padding: 20rpx;
	}

	.live-box {
		width: 349rpx;
		background-color: #fff;
		overflow: hidden;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
		position: relative;
	}

	.live-image {
		width: 100%;
		height: 456rpx;
	}

	.margin-top-sm-img {
		width: 100%;
		height: 560rpx;
		overflow: hidden;
		position: relative;
	}

	.live-image-1 {
		width: 158rpx;
		height: 49rpx;
		position: absolute;
		top: 3rpx;
		left: 0rpx;
		z-index: 2;
	}

	.live-image-2 {
		width: 100%;
		height: 560rpx;
		z-index: 1;
	}

	.live-image-3 {
		width: 125rpx;
		height: 250rpx;
		position: absolute;
		bottom: 0rpx;
		right: -10rpx;
		z-index: 2;
	}

	.live-image-4 {
		width: 82rpx;
		height: 82rpx;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: -21px;
		z-index: 1;
		border-radius: 50%;
	}

	.live-content {
		background-image: url(https://img.songlei.com/1/material/d2eb063b-ca23-47f1-b89d-7c40ef952eb2.png);
		background-size: 100% auto;
		background-repeat: no-repeat;
		padding: 10px 8px 0px 8px;
	}

	.broadcast {
		background: #fff;
		width: 100%;
		border-radius: 10px;
		display: flex;
		flex-wrap: wrap;
		padding: 10rpx 2% 20rpx 2%;
		box-sizing: border-box;
	}

	.broadcast-box {
		width: 25%;
		overflow: hidden;
		text-align: center;
		padding-top: 10px;
	}

	.broadcast-box image {
		width: 126rpx;
		height: 126rpx;
		display: block;
		margin: 0 auto;
	}

	.broadcast-box text {
		margin-top: 3px;
		font-size: 24rpx;
		color: #333333;
		line-height: 50rpx;
	}
</style>
