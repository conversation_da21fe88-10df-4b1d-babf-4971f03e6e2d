<template>
	<!-- 导航按钮组件 -->
	<!-- <view :class="'bg-'+theme.backgroundColor"> -->
	<view
		:style="{
			marginBottom: `${newData.marginBottomSpacing * multipleView}rpx`,
			marginTop: `${newData.marginTopSpacing * multipleView}rpx`,
			marginLeft: `${newData.marginLeftSpacing * multipleView}rpx`,
			marginRight: `${newData.marginRightSpacing * multipleView}rpx`
		}"
		:class="newData.background && newData.background.length > 0 && newData.background != undefined ? newData.background : ''"
	>
		<view
			:style="{
				backgroundColor: newData.background,
				backgroundSize: '100% 100%',
				backgroundImage: `url(${newData.bgImage})`,
				paddingBottom: `${newData.paddingBottomSpacing}px`,
				paddingTop: `${newData.paddingTopSpacing}px`,
				paddingLeft: `${newData.paddingLeftSpacing}px`,
				paddingRight: `${newData.paddingRightSpacing}px`
			}"
		>
			<swiper
				:next-margin="swiperList && swiperList.length > 1 ? newData.nextMargin * multipleView : 0 + 'rpx'"
				class="screen-swiper square-dot"
				:indicator-color="swiperList && swiperList.length > 1 ? newData.indicatorColor || '#CDA185' : ''"
				:indicator-active-color="swiperList && swiperList.length > 1 ? newData.indicatorActiveColor || '#CDA185' : ''"
				indicator-dots
				:style="{
					height: `${
						newData.swipeRowNum > 1
							?
							  (74 + Number(newData.fontSize * multipleView ) + Number(newData.imgFontSpacing * multipleView)) * 2 +
							  48
							: (isPhone ? 44 : 20) + Number(newData.fontSize * multipleView) + Number(newData.imgFontSpacing * multipleView) + 48
					}rpx`
				}"
			>
				<swiper-item v-for="(swiperItem, swiperIndex) in swiperList" :key="swiperIndex">
					<view
						class="cu-list grid no-border navButton"
						:class=" isPhone? 'col-' + newData.rowNum : ''"
						v-for="(listItem, listIndex) in swiperItem"
						:key="listIndex"
						:style="{
							justifyContent: 'space-between',
							backgroundColor: 'transparent',
							padding: 0,
							marginTop: listIndex > 0 ? `${newData.lineSpacing * multipleView}rpx` : '0'
						}"
					>
						<view
							class="cu-item"
							v-for="(item, index) in listItem"
							:key="index"
							:style="{
								backgroundColor: 'transparent',
								padding: 0,
								marginTop: listIndex > 0 ? `${newData.lineSpacing * multipleView}rpx` : '0'
							}"
						>
							<div-base-navigator :pageUrl="item.pageUrl" hover-class="none">
								<image
									:src="item.imageUrl"
									:style="{
										borderTopLeftRadius: `${newData.borderRadius == 1 ? newData.radiusLeftTop * multipleView + 'rpx' : '0rpx'}`,
										borderTopRightRadius: `${newData.borderRadius == 1 ? newData.radiusRightTop * multipleView + 'rpx' : '0rpx'}`,
										borderBottomLeftRadius: `${newData.borderRadius == 1 ? newData.radiusLeftBottom * multipleView + 'rpx' : '0rpx'}`,
										borderBottomRightRadius: `${newData.borderRadius == 1 ? newData.radiusRightBottom * multipleView + 'rpx' : '0rpx'}`
									}"
									class="nav_bt_img"
									mode="aspectFit"
								></image>
								<text
									:style="{
										marginTop: `${newData.imgFontSpacing * multipleView}rpx`,
										fontSize: `${newData.fontSize * multipleView }rpx`,
										color: `${newData.textColor}`
									}"
								>
									{{ item.navName }}
								</text>
							</div-base-navigator>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
import divBaseNavigator from '../div-base/div-base-navigator.vue';
import { mapState } from 'vuex';
const app = getApp();
export default {
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					rowNum: 4,
					textColor: '#333333',
					pageSpacing: 0,
					navButtons: [],
					height: 100
				};
			}
		}
	},
	computed: {
		...mapState(['isPhone', 'multipleView']),
		swiperSlideLength() {
			return Math.ceil((this.newData.navButtons.length / this.newData.rowNum) * (this.newData.swipeRowNum || 1));
		},
		// newData.navButtons 按钮转换成 滑动使用格式
		swiperList() {
			let threeDArray = [];
			let pageIndex = 0;
			// pad版本样式写死
			if(!this.isPhone){
				this.newData.swipeRowNum = 1;
				this.newData.rowNum = 10
			}else {
				this.newData.swipeRowNum = this.newData.swipeRowNum || 1;
			}

			for (let i = 0; i < this.newData.navButtons.length; i += this.newData.swipeRowNum * this.newData.rowNum) {
				if (!threeDArray[pageIndex]) {
					threeDArray[pageIndex] = [];
				}
				for (let j = i; j < i + this.newData.swipeRowNum * this.newData.rowNum; j += this.newData.rowNum) {
					let row = this.newData.navButtons.slice(j, j + this.newData.rowNum);
					threeDArray[pageIndex].push(row);
				}
				pageIndex++;
			}
			return threeDArray;
		}
	},
	components: {
		divBaseNavigator
	},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value
		};
	},
	methods: {
		jumpPage(page) {
			if (page) {
				uni.navigateTo({
					url: page
				});
			}
		}
	}
};
</script>

<style scoped lang="scss">
	
/* 导航 */
.nav_bt_img {
	width: 74rpx !important;
	height: 74rpx !important;
	margin: 0 auto;
}

@media screen and (min-width: 549px) {
	.nav_bt_img {
		width: 46rpx !important;
		height: 46rpx !important;
	}
}

.navButton {
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}

.cu-list + .cu-list {
	margin-top: 0;
}

.screen-swiper {
	min-height: 30rpx;
}

.text-center {
	text-align: center;
}

// .screen-swiper {
// 	max-height: 580rpx;
// 	// height: auto;
// 	min-height: 220rpx;

// }

.cu-list.grid.no-border {
	background-color: #1cbbb4;
}
</style>
