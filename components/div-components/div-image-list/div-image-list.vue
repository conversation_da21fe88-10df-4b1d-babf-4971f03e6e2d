<template>
	<!-- 单图显示组件 -->
	<view :style="{
	  marginBottom: `${newData.marginBottomSpacing*2}rpx`,
	  marginTop: `${newData.marginTopSpacing*2}rpx`,
	  marginLeft: `${newData.marginLeftSpacing*2}rpx`,
	  marginRight: `${newData.marginRightSpacing*2}rpx` }" :class="
	    newData.background&& newData.background.length>0 && newData.background!=undefined
	      ? newData.background
	      : ''
	  ">
		<view :style="{
		backgroundColor: newData.background,
		backgroundSize: 'cover',
		paddingBottom: `${newData.paddingBottomSpacing}px`,
		paddingTop: `${newData.paddingTopSpacing}px`,
		paddingLeft: `${newData.paddingLeftSpacing}px`,
		paddingRight: `${newData.paddingRightSpacing}px`,
		lineHeight:'0'
	}">
			<view v-for="(item,index) in newData.imageUrls" :key="index">
				<div-base-navigator :pageUrl="newData.pageUrls[index]" style="background: #FFFFFF;">
					<image :src="item | formatImg750" style="width: 100%; display: block;" mode="widthFix"  :show-menu-by-longpress="newData.canLongPress==1"></image>
				</div-base-navigator>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
	export default {
		name: 'basic-image',
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {
						pageUrls: [],
						imageUrls: [],
					}
				}
			}
		},
		components: {
			divBaseNavigator
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
			};
		},
		watch: {
			value(newData) {
				this.newData = newData;
			}
		},
		methods: {

		}
	}
</script>

<style scoped lang="scss">

</style>
