<template>
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''"
		style="width: 100vw;height: 100vh;">
		<view class="cu-dialog"
			style="background-color: transparent;position: fixed;left: 50%;top: 50%;transform: translate(-50%, -50%);width: auto;">

			<view class="flex justify-end">
				<view class="cuIcon-roundclose"
					style="margin: 0rpx;font-size: 60rpx;color: #fff;line-height: 1;margin: 15rpx 0;"
					@tap.stop="handleClose">
				</view>
			</view>

			<view :style="{
					backgroundSize: 'cover',
					width: '95%',
    			margin: '0 auto',
					marginTop: '15rpx',
					paddingTop: '10rpx',
					backgroundImage: newData.bgImageBottom?`url(${newData.bgImageBottom})`:`url(https://img.songlei.com/live/gold-medal-home.png)`,
					backgroundSize:' 100% auto',
					backgroundRepeat: 'no-repeat',
					height: '455rpx'
			}">
				<view class="flex justify-end">
					<view 
						style="width: 265rpx;
							height: 50rpx;
							line-height: 50rpx;
							margin-right: 45rpx;
							text-align: center;"
					>
						{{dateStr(newData.matchDate)}}
					</view>
				</view>

				<view class="padding-lr-sm padding-tb-sm">
					<view class="flex justify-between" style="padding: 25rpx 58rpx 17rpx">
						<view class="text-center text-bold text-df" style="color: #263989;">昨日赛况</view>
						<view class="text-center text-bold text-df" style="color: #263989;">数量</view>
						<view class="text-center text-bold text-df" style="color: #263989;">竞猜结果</view>
					</view>

					<view class="flex">
						<view>
							<view 
								class="flex text-black text-sm"
								style="width: 468rpx;height: 54rpx;line-height: 54rpx;background: #FFFFFF;border-radius: 19rpx; margin-bottom: 8rpx;"
								>
								<view class="padding-left-lg">昨日赛事结果</view>
								<view style="padding-left: 105rpx;">{{ newData.goldNum || '— —' }}</view>
							</view>

							<view 
								class="flex text-black text-sm"
								style="width: 468rpx;height: 54rpx;line-height: 54rpx;background: #FFFFFF;border-radius: 19rpx;"
								>
								<view class="padding-left-lg">昨日我的竞猜</view>
								<view style="padding-left: 105rpx;">
									<view v-if="newData.myGuess">
										<view v-if="newData.myGuess.type=='0'">{{ newData.myGuess.min }}-{{ newData.myGuess.max }}</view>
										<view v-if="newData.myGuess.type=='1'">{{ newData.myGuess.max }}以上</view>
									</view>
									<view v-else>{{ '— —' }}</view>
								</view>
							</view>
						</view>

						<view 
							style="
								width: 177rpx;
								height: 115rpx;
								line-height: 115rpx;
								text-align: center;
								background: #FFFFFF;
								margin-left: 8rpx;
								border-radius: 19rpx;"
							:style="{
								color:newData.guessResult==1?'#FF0000':'#B3B3B3'
							}"
							>
							{{newData.guessResult=='1'?'恭喜猜中':'未猜中'}}
						</view>
					</view>

					<view class="text-center" style="margin-top: 48rpx;">
						<button
							v-if="newData.myGuess==null"
							class="cu-btn"
							@click="handleAction"
							style="
								width: 410rpx;
								height: 78rpx;
								color: #ffffff;
								background: linear-gradient(0deg, #F51100 0%, #FF5C5C 100%);
								box-shadow: 0rpx 5rpx 0rpx 0rpx #9D0C00, 0rpx 7rpx 0rpx 0rpx #6B0800;
								border-radius: 27rpx;
								border-image: linear-gradient(0deg, #720900, #F51606, #FFFFFF) 1 1;"
						>立即抽奖({{newData.raffleNum}}次)</button>

						<view v-else class="flex justify-around">
							<button
								class="cu-btn"
								@click="handleMyAction"
								style="
									width: 278rpx;
									height: 85rpx;
									color: #ffffff;
									background: linear-gradient(0deg, #F51100 0%, #FF5C5C 100%);
									box-shadow: 0rpx 5rpx 0rpx 0rpx #9D0C00, 0rpx 7rpx 0rpx 0rpx #6B0800;
									border-radius: 27rpx;
									border-image: linear-gradient(0deg, #720900, #F51606, #FFFFFF) 1 1;"
							>
								我的竞猜
							</button>

							<button
								class="cu-btn"
								@click="handleMeetingplace"
								style="
									width: 278rpx;
									height: 85rpx;
									color: #ffffff;
									background: linear-gradient(0deg, #F51100 0%, #FF5C5C 100%);
									box-shadow: 0rpx 5rpx 0rpx 0rpx #9D0C00, 0rpx 7rpx 0rpx 0rpx #6B0800;
									border-radius: 27rpx;
									border-image: linear-gradient(0deg, #720900, #F51606, #FFFFFF) 1 1;"
							>
								去会场逛逛
							</button>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		name: 'div-gold-medal-dialog',
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {}
				}
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				showModal: true,
				cardCur: 0,
			};
		},
		// 组件的生命周期, 只能用vue的才行
		beforeCreate() {
			uni.hideTabBar()
			app.globalData.tabBarHide = app.globalData.tabBarHide + 1;
		},
		mounted() {},
		destroyed() {
			app.isBottomTabBar();
		},
		methods: {
			// 时间格式化
			dateStr(dateStr){
				// 将字符串日期转换成Date对象
				if(dateStr){
					const date = new Date(dateStr);
					// 格式化输出日期
					// 获取年、月、日
					const year = date.getFullYear();
					const month = date.getMonth() + 1; // 月份从0开始，需要加1
					const day = date.getDate();
	
					// 获取星期几
					const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
					const dayOfWeek = days[date.getDay()];
	
					// 格式化输出日期
					const formattedDate = `${year}.${month}.${day} ${dayOfWeek}`;
					return formattedDate
				}
				return "— —"
			},

			handleClose() {
				this.showModal = false;
				app.isBottomTabBar();
				this.$emit('close');
				uni.$emit('showBottomBar')
			},
			// guessResult 竞猜结果（1：猜中 2：未猜中）
			// isRaffle 是否可以抽奖（0否1是）
			handleAction() {
				uni.navigateTo({
					url: "/pages/activity/lottery/index?id=" + this.newData.marketId
				})
			},

			// 去会场逛逛
			handleMeetingplace() {
				// 1806136949085011970 写死去会场逛逛  微页面id
				uni.navigateTo({
					url: "/pages/micro-page/index?id=1806136949085011970"
				})
			},

			// 我的竞猜
			handleMyAction(){
				uni.navigateTo({
					url: "/pages/activity/my-gold-medal/index"
				})
			}
		}
	}
</script>

<style scoped lang="scss">
</style>