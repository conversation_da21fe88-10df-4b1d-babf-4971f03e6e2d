<template>
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''"
		style="width: 100vw;height: 100vh;">
		<view class="cu-dialog"
			style="background-color: transparent;position: fixed;left: 50%;top: 50%;transform: translate(-50%, -50%);width: auto;">
			<view class="cuIcon-roundclose" style="margin-top: 20rpx;font-size: 60rpx;color: #fff;line-height: 1;"
				@tap.stop="handleClose"></view>
			<view class="dialog-content">
				<view class="title">{{newData.matchDate}}抽奖结果公布</view>
				<view class="desc">本轮您参与了<text class="red">{{newData.joinNum}}</text>场竞猜，猜中<text
						class="red">{{newData.winNum}}</text>场，获得<text class="red">{{newData.raffleTotalNum}}</text>次抽奖机会
				</view>
				<view class="list">
					<view class="gap-time">
						<view>{{newData.matchDate}} {{newData.weekDay}}</view>
						<view>{{newData.myGuess.length}}场比赛</view>
					</view>
					<scroll-view :scroll-y="true" style="height: 39vh;max-height: 36vh;"
						v-if="newData.myGuess&&newData.myGuess.length>0">
						<view class="competition-item" v-for="(competitionItem,competitionIndex) in newData.myGuess">
							<view style="width: 180rpx;max-width: 180rpx;overflow: hidden;">
								<view style="font-size: 22rpx;color: #000000;">{{competitionItem.matchStartTime}}
									{{competitionItem.matchName}}
								</view>
								<view style="font-size: 22rpx;color: #A4A4A4;margin-top: 14rpx;">
									{{competitionItem.isEnd==1?'完赛':'未完赛'}}
								</view>
								<view style="width: 180rpx;max-width: 180rpx; overflow: hidden;font-size: 22rpx;color: #000000;margin-top: 14rpx;;">
									我的竞猜：{{competitionItem.guessDetail}}</view>
							</view>
							<view style="width: 240rpx;max-width: 240rpx;display: flex;flex-direction: column;justify-content: space-between;">
								<view style="display: flex;align-items: center; justify-content: center;">
									<image mode="aspectFill" style="width: 68rpx; height: 58rpx;"
										:src="competitionItem.teamA.img"></image>
									<view
										style="width: 140rpx;max-width: 140rpx;overflow: hidden;margin-left: 20rpx;font-size: 22rpx;color: #000000;">
										{{competitionItem.teamA.name}}
									</view>
									<view style="min-width: 60rpx;font-weight: 800;font-size: 30rpx;color: #000000;">
										{{competitionItem.teamA.score}}
									</view>
								</view>
								<view style="display: flex;align-items: center; justify-content: center;">
									<image mode="aspectFill" style="width: 68rpx; height: 58rpx;"
										:src="competitionItem.teamB.img"></image>
									<view
										style="width: 140rpx;max-width: 140rpx;overflow: hidden;margin-left: 20rpx;font-size: 22rpx;color: #000000;">
										{{competitionItem.teamB.name}}
									</view>
									<view style="min-width: 60rpx;font-weight: 800;font-size: 30rpx;color: #000000;">
										{{competitionItem.teamB.score}}
									</view>
								</view>

							</view>
							<view
								:style="{ width:'140rpx',marginTop: '20rpx',display:'flex', flexDirection:'column', alignItems:'center', justifyContent:'center', color:  competitionItem.guessResult==1? '#FF1304':'#A4A4A4'}">
								<image mode="aspectFill" style="width: 66rpx; height: 66rpx;margin-bottom: 10rpx;"
									:src="competitionItem.guessResult==0? $imgUrl('live/competition/no-result.png'):competitionItem.guessResult==1?$imgUrl('live/competition/win-result.png'):$imgUrl('live/competition/lose-result.png') ">
								</image>
								{{competitionItem.guessResult==0?'未出结果':competitionItem.guessResult==1?'您已猜中':'未猜中'}}
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="action-layout" @click="handleAction">
					立即抽奖({{newData.raffleNum}}次)
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		name: 'div-advert-dialog',
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {}
				}
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				showModal: true,
				cardCur: 0,
			};
		},
		// 组件的生命周期, 只能用vue的才行
		beforeCreate() {
			uni.hideTabBar()
			app.globalData.tabBarHide = app.globalData.tabBarHide + 1;
		},
		mounted() {},
		destroyed() {
			app.isBottomTabBar();
		},
		methods: {
			handleClose() {
				this.showModal = false;
				app.isBottomTabBar();
				this.$emit('close');
				uni.$emit('showBottomBar')
			},
			// guessResult 竞猜结果（0：未出结果 1：猜中 2：未猜中）
			// isRaffle 是否可以抽奖（0否1是）
			handleAction() {
				uni.navigateTo({
					url: "/pages/activity/lottery/index?id=" + this.newData.marketId
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.dialog-content {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-image: url(https://img.songlei.com/live/competition/competion-resbg.png);
		background-size: 100% 100%;
		background-repeat: no-repeat;
		width: 666rpx;
		height: 82vh;
		margin: 20rpx;

		.title {
			font-weight: bold;
			font-size: 40rpx;
			color: #000000;
			margin-top: 180rpx;
		}

		.desc {
			font-weight: 500;
			font-size: 28rpx;
			color: #000000;
			padding: 10rpx 0 18rpx;

			.red {
				color: #FF1304;
			}
		}

		.list {
			margin: 0 20rpx;
			height: 41vh;
			width: 100%;
			max-height: 41vh;
			background: #F3F3F3;
			border-radius: 30rpx;
			padding: 16rpx;

			.gap-time {
				font-size: 22rpx;
				display: flex;
				justify-content: space-between;
				padding: 0 12rpx;
			}

			.competition-item {
				display: flex;
				justify-content: space-between;
				padding: 14rpx;
				background-color: #fff;
				border-radius: 10rpx;
				margin-top: 10rpx;
			}
		}

		.action-layout {
			width: 432rpx;
			height: 94rpx;
			background: linear-gradient(0deg, #FF0F00 0%, #FF543C 100%);
			border-radius: 47rpx;
			text-align: center;
			margin-top: 10rpx;
			font-weight: 800;
			font-size: 30rpx;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>