<template>
  <view class="newPerson-page">
    <view
      class="main-box"
      :style="{ border: `2rpx solid ${setData.borderColor}` }"
    >
      <view class="head-outbox">
        <view class="head-box">
          <view>
            <text :style="[titleStyle]">{{ setData.title }}</text>
            <text :style="[subTitleStyle]">{{ setData.subTitle }}</text>
          </view>
          <view class="gz" @click="openRulePopup">规则</view>
        </view>
        <img v-if="setData.titleBg" class="tu" :src="setData.titleBg" />
      </view>
      <view class="body-outbox">
        <view class="core-outbox">
          <view class="core-box" v-for="(u, i) in giveList" :key="i">
            <view class="cell-outbox" v-for="(u2, i2) in u.details" :key="i2">
              <view class="cell-box">
                <view>
                  <view :style="[priceStyle]" v-if="'01' == u2.groupId">
                    {{ u2.amount }}
                  </view>
                  <block v-else>
                    <view
                      :style="[priceStyle]"
                      v-if="'A' != u2.groupId && '04' == u2.useType"
                    >
                      {{ u2.amount | discountsConvert }}折
                    </view>
                    <div class="jine" v-else>
                      <view :style="[symbolStyle]">￥</view>
                      <view :style="[priceStyle]">{{ u2.amount }}</view>
                    </div>
                  </block>
                </view>
                <view :style="[nameStyle]">{{ u2.couponName }}</view>
                <view :style="[nameStyle2]">{{ u.condAmount }}门槛</view>
              </view>
              <img v-if="setData.ticketBg" class="tu" :src="setData.ticketBg" />
              <img
                v-if="i < setData.progressVal"
                class="tu"
                :src="setData.noTicketBg"
              />
            </view>
            <img
              v-if="setData.divideBg && i + 1 != giveList.length"
              class="jiange"
              :src="setData.divideBg"
            />
          </view>
        </view>
        <view class="tiao-outbox" :style="[progressStyle]">
          <view v-for="(u, i) in giveList" :key="i" class="tiao-box">
            <view
              class="tiao"
              :style="{
                backgroundColor: setData.miniProBgColor,
                width: `${u.width * 2}rpx`,
                visibility: i == setData.progressVal ? 'visible' : 'hidden',
              }"
            ></view>
            <img
              class="tu"
              :src="setData.noMarkBg"
              v-if="i < setData.progressVal"
            />
            <img class="tu" :src="setData.markBg" v-else />
            <view
              class="wen"
              :style="{ opacity: i < setData.progressVal ? 0.7 : 1 }"
            >
              {{ u.name }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2024年2月7日
 **/
import { accMul } from "utils/numberUtil.js";
import { newPersonApi } from "@/utils/api";

export default {
  props: {
    value: {
      type: Object | null,
      default: null,
    },
  },
  data() {
    return {
      setData: null,
      titleStyle: null,
      subTitleStyle: null,
      symbolStyle: null,
      priceStyle: null,
      nameStyle: null,
      nameStyle2: null,
      progressStyle: null,
      giveList: [],
    };
  },
  filters: {
    discountsConvert(val) {
      return accMul(val, 10);
    },
  },
  watch: {
    value: {
      handler() {
        this.initStyle();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.getNewPerson();
  },
  methods: {
    initStyle() {
      this.setData = this.value;
      const {
        titleSize,
        titleColor,
        subTitleSize,
        subTitleColor,
        symbolSize,
        symbolColor,
        priceSize,
        priceHeight,
        priceColor,
        nameSize,
        nameHeight,
        nameColor,
        nameMargin,
        nameMargin2,
        giveWidth,
        bigProBgColor,
        giveList,
      } = this.setData;
      this.titleStyle = {
        fontSize: `${titleSize * 2}rpx`,
        color: titleColor,
        marginRight: "10rpx",
      };
      this.subTitleStyle = {
        fontSize: `${subTitleSize * 2}rpx`,
        color: subTitleColor,
      };
      this.symbolStyle = {
        fontSize: `${symbolSize * 2}rpx`,
        color: symbolColor,
      };
      this.priceStyle = {
        fontSize: `${priceSize * 2}rpx`,
        lineHeight: `${priceHeight * 2}rpx`,
        color: priceColor,
      };
      this.nameStyle = {
        fontSize: `${nameSize * 2}rpx`,
        lineHeight: `${nameHeight * 2}rpx`,
        color: nameColor,
        marginTop: `${nameMargin * 2}rpx`,
      };
      this.nameStyle2 = {
        fontSize: `${nameSize * 2}rpx`,
        lineHeight: `${nameHeight * 2}rpx`,
        color: nameColor,
        marginTop: `${nameMargin2 * 2}rpx`,
      };
      this.progressStyle = {
        width: `${giveWidth * 2}rpx`,
        backgroundColor: bigProBgColor,
      };
      this.giveList = giveList;
    },
    getNewPerson() {
      const userInfo = uni.getStorageSync("user_info");
      const params = {
        channel: "SONGSHU",
        custId: userInfo.erpCid,
        keys: this.setData.billno,
      };
      newPersonApi(params).then((res) => {
        if (res.data) {
          const { tag } = res.data.list[0];
          this.setData.progressVal = tag;
        } else {
          this.setData.progressVal = 0;
        }
      });
    },
    openRulePopup() {
      this.$emit("openRulePopup", "");
    },
  },
};
</script>

<style lang="scss" scoped>
.newPerson-page {
  position: relative;
  .main-box {
    margin: auto;
    padding: 12rpx;
    width: 710rpx;
    height: 372rpx;
    background: #ffffff;
    opacity: 0.77;
    border-radius: 20rpx;
    .head-outbox {
      position: relative;
      .head-box {
        width: 686rpx;
        height: 80rpx;
        padding: 0 26rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .gz {
          font-size: 28rpx;
          color: #343e81;
        }
      }
      .tu {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        width: 686rpx;
        height: 80rpx;
      }
    }
    .body-outbox {
      margin-top: 18rpx;
      width: 686rpx;
      overflow-x: auto;
      overflow-y: hidden;
      .core-outbox {
        display: flex;
        align-items: center;
        .core-box {
          display: flex;
          align-items: center;
          .cell-outbox {
            position: relative;
            margin: auto 20rpx;
            width: 180rpx;
            height: 184rpx;
            display: flex;
            align-items: center;
            .cell-box {
              text-align: center;
              width: 180rpx;
              height: 184rpx;
              padding-top: 12rpx;
              .jine {
                display: flex;
                align-items: start;
                justify-content: center;
              }
            }
            .tu {
              position: absolute;
              z-index: -1;
              top: 0;
              left: 0;
              width: 180rpx;
              height: 184rpx;
            }
          }
          .jiange {
            margin: auto 20rpx;
            width: 36rpx;
            height: 126rpx;
          }
        }
      }
      .tiao-outbox {
        height: 24rpx;
        margin-top: 24rpx;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .tiao-box {
          position: relative;
          .tiao {
            margin: auto 4rpx;
            height: 16rpx;
            border-radius: 8rpx;
          }
          .tu {
            position: absolute;
            width: 128rpx;
            height: 36rpx;
            top: -16rpx;
            left: 50%;
            transform: translateX(-50%);
          }
          .wen {
            position: absolute;
            width: 66rpx;
            height: 36rpx;
            line-height: normal;
            top: -8rpx;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            font-size: 22rpx;
          }
        }
      }
    }
  }
}
</style>
