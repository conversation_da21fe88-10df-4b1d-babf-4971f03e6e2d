<template>
	<view  v-if="liveInfo&&(liveInfo.liveStatus==102 || liveInfo.liveStatus==101)" :class="liveInfo&&liveInfo.liveStatus==102?'pre-component':'living-component'" >
		<!-- 直播预告 -->
		<navigator class="cu-card case"
			:url="'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=' + (liveInfo&&liveInfo.roomId)">
			<view v-if="liveInfo&&liveInfo.liveStatus==102" class="pre-layout">
				<image class="pre-img" :src="liveInfo.shareImg" mode="aspectFill"></image>
				<view class="pre-content">
					<view>{{liveInfo.name}}</view>
					<view style="display: flex;align-items: center;flex-wrap: wrap;">
						<image :src="$imgUrl('live/pre_icon.png')" style="width: 85rpx;min-width: 85rpx;height: 31rpx;"></image>
						<text class="live-time">{{lastTime}}开播</text>
					</view>
				</view>
				<view class="pre-right" >
					开播提醒
				</view>
			</view>

			<!-- 直播中 -->
			<view v-else-if="liveInfo&&liveInfo.liveStatus==101" class="living-layout">
				<image :src="liveInfo.coverImg" style="width: 296rpx;height: 420rpx;" mode="aspectFill"></image>
				<view class="living-content">
					<view class="top">{{liveInfo.name}}</view>
					<image :src="$imgUrl('live/living.png')" style="width: 113rpx; height: 33rpx;"></image>
					<view class="tip">
						主播等你好久了呢，遇到喜欢的商品就赶快下手吧
					</view>
					<view class="pro-layout" v-if="liveInfo && liveInfo.goods && liveInfo.goods.length>0">
						<image style="width: 134rpx; height: 134rpx;" :src="getShowPro.coverImg"></image>
						<view class="recommend-pro">
							<view class="recommend-tip">
								<image style="width: 35rpx; height: 35rpx;" :src="$imgUrl('live/icon_star.png')"></image>
								主播力推商品
							</view>
							<view class="recommend-name overflow-1">{{getShowPro.name}}</view>
							<view class="recommend-price">
								￥{{getShowPro.priceType == 1 ? getShowPro.price : getShowPro.priceType == 2 ? getShowPro.price+'~'+getShowPro.price2 : getShowPro.priceType == 3 ? getShowPro.price2 : ''}}
								<image style="width: 28rpx; height: 28rpx;margin-right: 10rpx;" :src="$imgUrl('live/arrow_right.png')"></image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</navigator>
	</view>
</template>

<script>
	import countDown from "components/count-down/index";
	const app = getApp();
	import api from '@/utils/api'
	export default {
		name: 'basic-live',
		components: {
			countDown
		},
		props: {
			shopId: {
				type: String
			}
		},
		computed: {
			getShowPro(){
				if(this.liveInfo && this.liveInfo.goods && this.liveInfo.goods.length>0) {
					return this.liveInfo.goods[this.liveInfo.goods.length-1];
				}
				return {}
			},
		    lastTime(){
				if(this.liveInfo && this.liveInfo.startTime>0){
					const  leftTime = this.liveInfo.startTime*1000 - new Date().getTime();
					var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10);
					var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10);
					var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);
					let res ='';
					if(days>0) {
						res+= days+'天'
					}
					if(hours>0) {
						res+= hours+'小时';
					}
					if(minutes>0) {
						res+= minutes+'分钟';
					}
					return res
				}else {
					return "暂无"
				}
			}	
		},
		watch: {
			shopId(newValue, oldValue) {
				this.getData();
			}
		},
		filters: {
			filterPrice: function(value) {
				if (!value) return ''
				value = value.toString()
				return value.substr(0, value.length - 2)
			},

			filterPriceEnd: function(value) {
				if (!value || value.length < 3) return ''
				value = value.toString()
				return value.substr(value.length - 2, 2)
			},
		},

		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				//  liveStatus 直播间状态。101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期
				liveInfo: {}
			};
		},
		created() {
			this.getData();
		},
		
		methods: {
			getData(){
				if (this.shopId > 0) {
          console.log("直播在这里开始请求数据")
					api.liveRoomInfoListByShop(this.shopId).then(res => {
						console.log("直播在这里结束请求数据")
            this.liveInfo = res.data;
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.pre-component {
		background: linear-gradient(to bottom, rgba(0,0,0,0.2), #ffffff);
	}
	
	.living-component {
		background: linear-gradient(180deg, #C09979 0%, #E2AE8D 0%, #F3F3F3 100%);
	}
	
	
	
	.pre-layout {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 16rpx;
		margin: 24rpx;
		height: 160rpx;
		.pre-img {
			width: 200rpx;
			height: 160rpx;
		}
		
		.pre-content {
			flex: 1;
			padding: 10rpx 20rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			height: 160rpx;
		}
		
		.pre-right {
			height: 52rpx;
			line-height: 52rpx;
			border: 2rpx solid #386FDB;
			border-radius: 26rpx;
			font-weight: 700;
			color: #406CD9;
			padding: 0 20rpx;
			margin-right: 26rpx;
		}
		
		.live-time {
			font-size: 25rpx;
			font-weight: 500;
			color: #406CD9;
			display: flex;
			align-items: center;
		}
	}
	
	
    .living-layout {
	  margin: 26rpx;
	  display: flex;
      background: rgba(0,0,0,0.3);	 
	  justify-content: space-between;
	  margin-bottom: 27rpx;
	  .living-content {
		  display: flex;
		  flex: 1;
		  flex-direction: column;
		  margin-left: 20rpx;
		  .top {
			  font-size: 30rpx;
			  font-weight: 500;
			  color: #F3F3F3;
			  line-height: 35rpx;
			  margin-top: 30rpx;
			  margin-bottom: 20rpx;
		  }
		  
		  .tip {
			  width: 360rpx;
			  background: rgba(0,0,0,0.3);
			  border-radius: 16rpx;
			  color: #F3F3F3;
			  font-size: 24rpx;
			  padding: 24rpx;
			  margin-top: 20rpx;
		  }
		  
		  .pro-layout {
			  margin-top: 20rpx;
			  width: 360rpx;
			  height: 134rpx;
			  background: rgba(0,0,0,0.3);
			  border-radius: 16rpx;
			  display: flex;
			  image {
				  width: 134rpx;
				  height: 134rpx;
			  }
		  }
	  }
	}
	
	
	.recommend-pro {
		margin-left: 20rpx;
		.recommend-tip {
			width: 195rpx;
			height: 36rpx;
			display: flex;
			background: rgba(255, 187, 54, 0.7);
			border-radius: 18rpx;
			color: #FFFFFF;
			font-size: 23rpx;
			margin-top: 12rpx;
		}
		
		.recommend-name {
			color: #F3F3F3;
			font-size: 24rpx;
			margin-top: 10rpx;
		}
		
		.recommend-price {
			color: #F3F3F3;
			font-size: 24rpx;
			margin-top: 5rpx;
			display: flex;
			justify-content: space-between;
		}
	}
	
	
</style>
