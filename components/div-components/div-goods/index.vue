<template>
	<!-- 商品显示 组件 -->
	<view>
		<div-goods v-model="newData" :params="params" :shopId="shopId" :size="recordCurrentPage" />
		<view v-if="newData&&newData.showType == 'tradeState'"
			:class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	const app = getApp();
	import divGoods from "./div-goods.vue"
	import api from '@/utils/api'
	import {
		EventBus
	} from '@/utils/eventBus.js'
	export default {
		name: 'goods-layout',
		components: {
			divGoods
		},
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {
						showType: 'row',
						pageSpacing: 0,
						goodsList: []
					}
				}
			},

			shopId: {
				type: String | Number,
				default: function() {
					return -1;
				}
			},
			params:{
				type: Object,
				default: function() {
					return {};
				}
			}

		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: {},
				goodsListRecom: [],
				//获取店铺商品的分页查询
				page: {
					searchCount: false,
					current: 0,
					size: 20,
					ascs: '',
					//升序字段
					descs: ''
				},
				recordCurrentPage: 3, //记录当前页
				loadmore: true,

				//如果手动配置好的商品很多，也需要分页去加载
				//还没加载的商品
				unLoadGoods: []
			};
		},

		watch: {
			value: {
				handler(newVal, oldVal) {
					// 推荐混排--瀑布流，包含商品，视频，直播，猜你想看更多等
					// tradeState
					if (newVal.showType == 'tradeState') {
						this.getRecommendMixedList();
						newVal.goodsList = [];
					} else if (newVal.goodsFromType == 1) { //处理商品名称修改 
					} else {
						/***
						 *  处理 如果类型为 分组 把 groupingList 字段 中id提前处理并赋值
						 */
						if (newVal.goodsFromType == 7) {
								let groupsId = [];
								newVal.groupingList.forEach(item => {
									groupsId.push(item.id)
								});
								newVal.groupingList = groupsId.join(",");
							}
						this.loadData(newVal);
						newVal.goodsList = [];
					}
					//大于20就分页展示
					if (newVal.goodsList && newVal.goodsList.length > 20) {
						this.unLoadGoods = newVal.goodsList;
						newVal.goodsList = [];
						const subArray = this.unLoadGoods.splice(0, newVal.isSwiper?11:10);
						if (subArray && subArray.length > 0) {
							subArray.forEach(item => {
								newVal.goodsList.push(item);
							})
						}
					}
					this.newData = newVal;
				},
				immediate: true
			}
		},

		created() {
			EventBus.$on("divGoodsGroupsReachBottom", () => {
				if (this.value.goodsStyle&&this.value.goodsStyle==2) return
				this.scrolltolower();
			})
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},
			loadData(newVal) {
				if (newVal.goodsFromType == 2) {
					this.getGoodsRecom();
				} else if (newVal.goodsFromType == 5) {
					this.getRecSpuCodeByUserId();
				} else if (newVal.goodsFromType == 6) {
					this.getCustomerFollowProduct();
				} else if (newVal.goodsFromType == 7) {
					this.getGroupsGoods();
				} else if (this.shopId > 0 && newVal.goodsFromType == 3) {
					this.getShopGoodsByCreateTime();
				} else if (this.shopId > 0 && newVal.goodsFromType == 4) {
					this.getShopGoodsBySaleNum();
				}
			},

			// 加载需要分页的商品列表
			scrolltolower() {
				// 推荐混排--瀑布流，包含商品，视频，直播，猜你想看更多等
				// tradeState
				if (this.value && this.value.showType == 'tradeState') {
					if (this.loadmore) {
						this.getRecommendMixedList();
					}
				} else if (this.value && this.value.goodsFromType == 2) {
					this.getGoodsRecom();
				} else if (this.value && this.value.goodsFromType == 6) {
					this.getCustomerFollowProduct();
				} else if (this.value && this.value.goodsFromType == 7) {
					this.getGroupsGoods();
				} else if (this.shopId > 0 && this.value && this.value.goodsFromType == 3) {
					this.getShopGoodsByCreateTime();
				} else if (this.shopId > 0 && this.value && this.value.goodsFromType == 4) {
					this.getShopGoodsBySaleNum();
				} else if (this.unLoadGoods && this.unLoadGoods.length > 0) {
						const subArray = this.unLoadGoods.splice(0, this.unLoadGoods.length>(this.newData.isSwiper?11:10)?(this.newData.isSwiper?11:10):this.unLoadGoods.length);
						if (subArray && subArray.length > 0) {
							subArray.forEach(item => {
								this.newData.goodsList.push(item);
							})
						}
				}
			},
			getRecSpuCodeByUserId() {
				let userId = 0,
					user = uni.getStorageSync('user_info');
				if (user && user.id > 0) {
					userId = user.id;
				}
				api.getRecSpuCodeByUserId({
					userId,
					count: 40
				}).then(res => {
					if (res.data && res.data.length > 0) {
						let goodsList = res.data
						this.newData.goodsList = [...goodsList];
					}
				}).catch(e => {

				});
			},

			/**
     * 获取分组商品
     * */
			getGroupsGoods () {
				console.log("this.newData===>",this.value);
				this.page.current++;
				if (this.value.goodsStyle==2&&this.value.goodsPuantity) {
					this.page.size = this.value.goodsPuantity
				}
				api.goodsGroupsGet(
					Object.assign(
						this.page, {
						goodsGroupIds: this.value.groupingList
					}
					)
				).then(res => {
					// console.log(res)
					const data = res.data?.records || []
					//记录第一页数据长度
					if (res.data && res.data.current == "1") {
						this.recordCurrentPage = data.length
					}
					console.log(res, data, data.length, '========商品分组======')
					if (res && res.data && res.data.records && res.data.records.length > 0) {
						this.newData.goodsList = this.newData.goodsList.concat(data);
						if (this.newData.goodsList.length === res.data.total) {
							this.loadmore = false;
						}
					}
				})
			},

			// 推荐混排-获取数据
			async getRecommendMixedList() {
				console.log('推荐混排-获取数据')
				try {
					this.page.current++;
					const params = {
						current: this.page.current,
						size: this.page.size,
						businessType: this.value.businessType ? this.value.businessType : 'all'
					}
					const res = await api.getRecommendMixedList(params)
					const data = res.data?.records || []
					//记录第一页数据长度
					if (res.data && res.data.current == "1") {
						this.recordCurrentPage = data.length
					}
					console.log(res, data, data.length, '========推荐混排======')
					if (data.length) {
						this.newData.goodsList = this.newData.goodsList.concat(data);
						if (this.newData.goodsList.length === res.data.total) {
							this.loadmore = false;
						}
					}
				} catch (e) {
					console.error(e)
				}
			},

			getGoodsRecom() {
				api.getRecommendList({
					sceneId: "sy101",
					returnCount: 20
				}).then(res => {
					if (res.data && res.data.length > 0) {
						this.newData.goodsList = this.newData.goodsList.concat(res.data);
					}
				}).catch(e => {

				});
			},

			// 获取店铺的商品按创建时间排序
			getShopGoodsByCreateTime() {
				this.page.current++;
				api.goodsPage(Object.assign({}, this.page, {
					shopId: this.shopId,
					descs: 'create_time'
				})).then(res => {
					if (res && res.data && res.data.records && res.data.records.length > 0) {
						this.newData.goodsList = this.newData.goodsList.concat(res.data.records);
					}
				});
			},

			// 获取店铺的商品按销量
			getShopGoodsBySaleNum() {
				this.page.current++;
				api.goodsPage(Object.assign({}, this.page, {
					shopId: this.shopId,
					descs: 'sale_num'
				})).then(res => {
					if (res && res.data && res.data.records && res.data.records.length > 0) {
						this.newData.goodsList = this.newData.goodsList.concat(res.data.records);
					}
				});
			},


			// 获取客户关注的商品列表
			getCustomerFollowProduct() {
				this.page.current++;
				api.userCollectPage(Object.assign({}, {
					...this.page,
					size: 40
				}, {
					descs: 'create_time',
					type: 1
				})).then(res => {
					if (res && res.data && res.data.records && res.data.records.length > 0) {
						res.data.records.forEach(item => {
							if (item && item.goodsSpu) this.newData.goodsList.push(item.goodsSpu)
						})
					}
				});
			}
		}
	}
</script>
