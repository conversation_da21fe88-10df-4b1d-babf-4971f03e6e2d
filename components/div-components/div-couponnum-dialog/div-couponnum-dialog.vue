<template>
	<!-- 优惠券数量提醒弹框 -->
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''">
		<view class="cu-dialog" style="background-color: transparent;">
			<view style="position: relative;">
				<view :style="{ 
					width: `77rpx`,
					display: 'flex',
					justifyContent:' flex-end',
					position:'absolute',
					top:'0rpx',
					right: '0rpx'} ">
					<image style="width: 30px; height: 30px; "
						:src="$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')" fit="cover"
						@tap.stop="handleClose" />
				</view>
				<image :src="couponDialogBg"
					style="width: 100%;display: block;pointer-events:none" mode="widthFix"></image>
				<view style="
						position: absolute;
						top: 126rpx;
						left: 142rpx;
						width: 168px;
						height: 22px;
						font-weight: 400;
						font-size: 20px;
						"
						:style="{color: couponDialogTxt1Color}">
					尊敬的会员,您有
				</view>
				<view style="
						position: absolute;
						bottom: 378rpx;
						width: 475rpx;
						left: 100rpx;
    				    height: 325rpx;
						font-weight: bold;
						font-size: 228rpx;
						color: #A67D44;" :style="{color: couponDialogTxt2Color}">
					<view style="margin:auto">
						{{value}}
						<text style="
							width: 39rpx;
							height: 40rpx;
							font-weight: 400;
							font-size: 43rpx;
							color: #A77E44;
						" :style="{color: couponDialogTxt1Color}">
							张
						</text>
					</view>
				</view>
				<view style="
						position: absolute;
						bottom: 360rpx;
						left: 226rpx;
						width: 223rpx;
						height: 38rpx;
						font-weight: 400;
						font-size: 36rpx;
						color: #282828;" :style="{color: couponDialogTxt3Color}">
					优惠券未使用
				</view>
				<view class="font-style" @click.stop="reading()" style="
					  position: absolute;
						bottom: 210rpx;
						left: 200rpx;
						color: #FBF7EF;
						width: 284rpx;
						height: 76rpx;
						line-height: 76rpx;
						font-size: 38rpx;
						background: #282828;
						border-radius: 38rpx;
					" :style="{color: couponDialogTxt4Color, background: couponDialogBgColor}">
					点击查看 >
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		name: 'div-couponnum-dialog',
		props: {
			value: {
				type: Number,
				default: function() {
					return 0
				}
			}
		},

		data() {
			return {
				showModal: true,
				theme: app.globalData.theme,
			};
		},
		computed: {
			couponDialogBg: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.couponDialogBg ||
						this.$imgUrl('live/couponnum/couponnum-dialog.png')
				}
				return this.$imgUrl('live/couponnum/couponnum-dialog.png')
			},
			
			couponDialogTxt1Color: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.couponDialogTxt1Color || "#A77E44"
				}
				return "#A77E44"
			},
			
			couponDialogTxt2Color: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.couponDialogTxt2Color || "#A67D44"
				}
				return "#A67D44"
			},
			
			couponDialogTxt3Color: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.couponDialogTxt3Color || "#282828"
				}
				return "#282828"
			},
			
			couponDialogTxt4Color: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.couponDialogTxt4Color || "#FBF7EF"
				}
				return "#FBF7EF"
			},
			
			couponDialogBgColor: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.couponDialogBgColor || "#282828"
				}
				return "#282828"
			},
		},
		methods: {
			handleClose() {
				this.showModal = false;
				this.$emit('close');
				// uni.$emit('showBottomBar')
			},

			reading() {
				uni.navigateTo({
					url: '/pages/coupon/coupon-user-list/index'
				})
				this.showModal = false;
			}

		}
	}
</script>

<style scoped lang="scss">
	.font-style {
		font-size: 29rpx;
		font-weight: 400;
		color: #64241A;
	}
</style>