<template>
	<!-- 单图显示组件 -->
	<view class="cu-modal" catchtouchmove="true" v-if="showModal" :class="showModal ? 'show' : ''" style="width: 100vw; height: 100vh">
		<view class="cu-dialog" style="background-color: transparent; position: fixed; left: 50%; top: 50%; transform: translate(-50%, -50%); width: auto">
			<view :style="{ width: `100%`, display: 'flex', justifyContent: ' flex-end', marginBottom: '20rpx', position: 'relative', transform: 'translateX(-50%)', left: '50%' }">
				<!-- <image style="width: 30px; height: 30px; " :src=$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')
					fit="cover" @tap.native.stop="handleClose" /> -->
				<view
					class="cuIcon-roundclose"
					style="color: #fff; line-height: 1"
					:style="{
						fontSize: isPhone ? '60rpx' : '30rpx'
					}"
					@tap.stop="handleClose"
				></view>
			</view>
			<div-base-navigator :style="{ margin: '0 auto' }">
				<view :style="{ position: 'relative', transform: 'translateX(-50%)', left: '50%', width: `${width}rpx`, height: `${height}rpx` }">
					<swiper
						class="screen-swiper"
						autoplay
						circular
						:duration="newData.interval > 10 ? newData.interval : 500"
						:indicator-dots="newData && newData.swiperList && newData.swiperList.length > 1"
						@change="cardSwiper"
						:indicator-color="indicatorColor"
						:indicator-active-color="indicatorActiveColor"
						:style="{ height: `100%`, width: `100%` }"
					>
						<swiper-item v-for="(item, index) in newData.swiperList" :key="index" @tap="jumpPage(item.pageUrl)">
							<image
								v-if="getFileType(item.imageUrl) == 'image'"
								:src="item.imageUrl | formatImg750"
								:style="{
									borderRadius: '25px',
									width: `100%`,
									height: `100%`
								}"
								mode="aspectFill"
							></image>

							<video
								v-else
								id="myVideo"
								autoplay
								loop
								:muted="muted"
								:src="item.imageUrl"
								@error="videoErrorCallback"
								enable-danmu
								danmu-btn
								:controls="false"
								:style="{
									borderRadius: '10px',
									width: `${newData.width * 2}rpx`
								}"
								object-fit="contain"
							></video>
							<view v-if="getFileType(item.imageUrl) == 'video'" class="text-xl close-icon" style="position: absolute; bottom: 20rpx; right: 20rpx; z-index: 1000">
								<text :class="muted ? 'cuIcon-notificationforbidfill' : 'cuIcon-notification'" style="color: #ff0000" @tap.stop="handleMuted"></text>
							</view>
						</swiper-item>
					</swiper>
				</view>
			</div-base-navigator>
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
const app = getApp();
import { gotoPage } from '../div-base/div-page-urls.js';
import divBaseNavigator from '../div-base/div-base-navigator.vue';
export default {
	name: 'div-advert-dialog',
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					pageUrl: ``,
					imageUrl: '',
					height: 100,
					showModal: true
				};
			}
		}
	},
	components: {
		divBaseNavigator
	},
	computed: {
		...mapState(['isPhone', 'multipleView']),
		indicatorColor() {
			if (this.newData && this.newData.indicatorColor) {
				return this.newData.indicatorColor;
			}
			return '#cccccc';
		},

		indicatorActiveColor() {
			if (this.newData && this.newData.indicatorActiveColor) {
				return this.newData.indicatorActiveColor;
			}
			return '#ffffff';
		},
		width() {
			let { width, height } = this.newData;
			const { safeArea } = uni.getSystemInfoSync();
			const { width: widths, height: heights } = safeArea;
			if (heights / widths < 1.3) {
				width = (width / height) * (750 / widths) * heights * 0.4;
			}
			return this.multipleView * width;
		},
		height() {
			let { width, height } = this.newData;
			const { safeArea } = uni.getSystemInfoSync();
			const { width: widths, height: heights } = safeArea;
			if (heights / widths < 1.3) {
				height = (750 / widths) * heights * 0.4;
			}
			return this.multipleView * height;
		}
	},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value,
			muted: true,
			showModal: true,
			cardCur: 0
		};
	},
	// 组件的生命周期, 只能用vue的才行
	beforeCreate() {
		uni.hideTabBar();
		app.globalData.tabBarHide = app.globalData.tabBarHide + 1;
	},
	mounted() {
		console.log('广告数据', this.value);
	},
	destroyed() {
		app.isBottomTabBar();
	},
	methods: {
		getFileType(fileUrl) {
			if (!fileUrl) return '';
			const fileName = fileUrl.split('//')[1].split('/');
			const file = fileName[fileName.length - 1].split('.')[1];
			if (file == 'jpg' || file == 'png' || file == 'gif' || file == 'webp') {
				return 'image';
			} else if (file == 'mp4' || file == 'ogg' || file == 'flv' || file == 'avi' || file == 'rmvb' || file == 'mov' || file == 'wmv') {
				return 'video';
			} else {
				return undefined;
			}
		},

		videoErrorCallback: function (e) {
			// this.newData.imageUrl = this.newData.imageUrl
			// uni.showModal({
			// 	content: e.target.errMsg,
			// 	showCancel: false
			// })
		},
		handleMuted() {
			this.muted = !this.muted;
		},
		handleClose() {
			this.showModal = false;
			app.isBottomTabBar();
			this.$emit('close');
			uni.$emit('showBottomBar');
		},

		cardSwiper(e) {
			this.cardCur = e.detail.current;
		},
		jumpPage(page) {
			if (page) {
				if(page=="dialog:showNortheastActivity"){
					this.handleClose();
				}
				gotoPage(page);
			} else {
				// uni.showToast({
				// 	title: '没有配置链接地址',
				// 	icon: 'none',
				// 	duration: 2000
				// });
			}
		}
	}
};
</script>

<style scoped lang="scss"></style>
