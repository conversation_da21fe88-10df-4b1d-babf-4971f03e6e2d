<template>
  <view class="ruleDialog-page">
    <view class="popup-outbox">
      <view class="popup-box" :style="[popupStyle]">
        <div class="hang">松鼠美淘新人三单礼：</div>
        <div class="hang">{{ setData.contentRule }}</div>
        <div class="hang" v-if="setData.contentRule2">
          {{ setData.contentRule2 }}
        </div>
        <div class="hang" v-if="setData.contentRule3">
          {{ setData.contentRule3 }}
        </div>
        <div class="hang" v-if="setData.contentRule4">
          {{ setData.contentRule4 }}
        </div>
        <view class="btn" :style="[btnStyle]" @click="closeRulePopup">
          关闭
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * <AUTHOR>
 * @date 2024年2月7日
 **/

export default {
  props: {
    setData: {
      type: Object,
      require: false,
      default: null,
    },
  },
  data() {
    return {
      popupStyle: null,
      btnStyle: null,
    };
  },
  watch: {
    setData: {
      handler() {
        this.initStyle();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    initStyle() {
      const {
        borderColorRule,
        textSizeRule,
        textColorRule,
        btnWeightRule,
        btnHeightRule,
        btnColorRule,
      } = this.setData;
      this.popupStyle = {
        border: `2rpx solid ${borderColorRule}`,
        fontSize: `${textSizeRule * 2}rpx`,
        color: textColorRule,
      };
      this.btnStyle = {
        width: `${btnWeightRule * 2}rpx`,
        height: `${btnHeightRule * 2}rpx`,
        lineHeight: `${btnHeightRule * 2}rpx`,
        background: btnColorRule,
      };
    },
    closeRulePopup() {
      this.$emit("closeRulePopup", "");
    },
  },
};
</script>

<style lang="scss" scoped>
.ruleDialog-page {
  position: absolute;
  top: 0;
  z-index: 2000;
  width: 100%;
  height: 100vh;
  .popup-outbox {
    position: absolute;
    z-index: 10;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6);
    .popup-box {
      background-color: #fefefe;
      margin: auto;
      margin-top: 40%;
      padding: 24rpx;
      width: 710rpx;
      border-radius: 20rpx;
      .hang {
        margin-bottom: 10rpx;
      }
      .btn {
        margin: auto;
        margin-top: 30rpx;
        text-align: center;
        border-radius: 16rpx;
      }
    }
  }
}
</style>
