<template>
	<view class="two-goods" v-if="bargainGoods&&bargainGoods.length>0">
		<view v-for="(item, index) in bargainGoods" class="item" :key="index">
			<view @click="imgclick(item)">
				<image :src="item.picUrl | formatImg360" mode="aspectFit" :style="{
	       		width: '100%',
	       		height:'124rpx',
	       		display: 'block',
	       	  }"> </image>
				<view style="
	       	        color: #000;
	       			font-size: 22rpx;   
	       			position: relative;
	       			width: 100%;
	       			line-height: 24rpx;
	       			height: 24rpx;
	       			overflow: hidden;
	       			margin-left: 6rpx;
	       			margin-top: 6rpx;
	       			margin-right: 6rpx;
	       			">{{item.name}}</view>
				<view style="width: 100%;display: flex;justify-content:space-between;margin-top: 20rpx;">
					<view
						style="font-weight: bold;color: #ccc;text-decoration: line-through; min-height: 20rpx;font-size: 16rpx;margin-left: 6rpx;margin-right: 6rpx;margin-top: 4rpx;">
						￥{{item.cutGoodsValue || item.goodsSku.salesPrice}}</view>
					<view style="color: #ff0000;min-height:50rpx;font-weight: bold;">
						<span style="font-size: 16rpx;">￥</span>
						<span style="font-size: 30rpx;">{{item.bargainPrice}}</span>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>	import {
		gotoPage
	} from "../../div-base/div-page-urls.js";
	import api from 'utils/api'
	export default {
		
		data() {
			return {
				bargainGoods: [],
				bargainInfo:{}
			}
		},
		created(){
			this.getGoods();
		},
		methods: {
			imgclick(item) {
					if (item) {
						gotoPage(`/pages/bargain/bargain-list/index`);
					} else {
						// uni.showToast({
						// 	title: '没有配置链接地址',
						// 	icon: 'none',
						// 	duration: 2000
						// });
					}
			},
			
			getGoods() {
				api.bargainhallNowSpuList().then(res => {
					if(res.data){
						if(res.data.bargainInfos&&res.data.bargainInfos.length>0){
							this.bargainGoods = res.data.bargainInfos.slice(0,2);
						}
						this.bargainInfo =  res.data;
					}
				});
			}
		}

	}
</script>

<style scoped lang="scss">
	.two-goods {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		// height: 100%;
		position: absolute;
	    top: 0;
		left: 0;
		// background-color: #fff;
		.item:nth-child(2n) {
			padding-left: 6rpx;
			flex: 1;
		}

		.item:nth-child(2n+1) {
			padding-right: 6rpx;
			flex: 1;
		}

	}
</style>
