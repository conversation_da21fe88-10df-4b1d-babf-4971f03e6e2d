<template>
  <view class="sec-container">
	  uuu
<!--    <text class="seckill_num">14点场</text>
    <text class="timer">{{formatSeconds}}</text> -->
  </view>
</template>
<script>
// export default {
//     name:"customcomponentseckilltimer",
//     data(){
//         return {
//            lastSecs:40000,
//            hour:10,
//         }
//     },

//     created(){
//          this.getLastTime();
//     },
//     computed:{
//         formatSeconds(){
//             let result = parseInt(this.lastSecs)
//             let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
//             let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60));
//             let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60));
        
//             let res = '';
//             if(h !== '00') res += `${h}:`;
//             if(m !== '00') res += `${m}:`;
//             res += `${s}`;
//             return res; 
//         }
//     },


    
//     methods: { 

//         getLastTime() {
//             this.timer = setInterval(()=>{
//             this.lastSecs--
//             if(this.lastSecs===0){
//             clearInterval(this.timer)
//             }
//            },1000)
//        }
//     }
// }
</script>
<!-- <style scoped>
.sec-container {
  display: flex;
  align-items: center;
  height: 25px;
}

.seckill_num {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  background-image: -webkit-gradient(
    linear,
    right top,
    left top,
    from(#f10101),
    to(#fe4d17)
  );
  background-image: -webkit-linear-gradient(right, #f10101, #fe4d17);
  background-image: linear-gradient(-90deg, #f10101, #fe4d17);
  box-shadow: 0 0.1rem 0.2rem 0 rgb(241 1 1 / 20%);
  font-size: 0.5rem;
  color: #fff;
  letter-spacing: 0;
  border-radius: 0.35rem 0.35rem 0.35rem 0;
  padding: 2px;
  z-index: 4;
}

.timer {
    background: #ecb4d9;
    color: #f10101;
    font-size: 0.5rem;
    padding: 3px 6px 3px 10px;
    margin-left: -6px;
}
</style> -->