<template>
	<view :style="{
		background: popProps.contentType=='pop'?popProps.background:'transparent',
		borderRadius:popProps.contentType=='pop'? popProps.backgroundRadius+'px' :'0'
	}">
		<view class="two-goods" v-if="popGoods&&popGoods.length>0">
			<view v-for="(item, index) in popGoods.slice(0,6)" class="item" :key="index">
				<view v-if="item.goodsSpu" @click="imgclick(item)" class="item-content">
					<image :src="item.goodsSpu.picUrls[0] | formatImg360" mode="aspectFit" :style="{
						width: '100%',
						height:'160rpx',
						display: 'block',
						textAlign: 'center',
						marginTop: '44rpx'
					  }"> </image>
					<view class="name">{{item.goodsSpu.name}}</view>
					<view style="width: 100%;color: #ccc;text-decoration: line-through;font-size: 16rpx;">
						￥{{item.goodsSpu.estimatedPriceVo.originalPrice}}</view>
					<view class="price-pop">
						<view style="display:flex;align-items: center;">
							<view
								style="background-color: #FF3955;color: #fff;border-radius: 50%;width:32rpx;height: 32rpx;font-size:22rpx;text-align: center;">
								省</view>
							<view style="color:#FF3955;font-size: 24rpx;margin-left: 2rpx;width: 55rpx;text-align: center;">{{Math.round(item.goodsSpu.estimatedPriceVo.originalPrice-item.goodsSpu.estimatedPriceVo.estimatedPrice)}}</view>
						</view>
						<view class="buy-price">
							<text style="font-size: 16rpx;">￥</text>
							<text style="font-size: 28rpx;">{{item.goodsSpu.estimatedPriceVo.estimatedPrice}}</text>
						</view>
					</view>
				</view>
				<image :src="getPopImg(index)" 
				 mode="aspectFit" 
				:style="{
					width: '42rpx',
					height:'72rpx',
					position:'absolute',
					left:'6rpx',
					top:0
				  }"> </image>
				<view v-if="index>2" style="
				  position:absolute;
					left:17rpx;
					top:25rpx;
					color: #717171;
					font-size: 28rpx;
					font-weight: bold;"
					>{{index+1}}</view>  
			</view>
			<view class="more" @tap="more">查看更多></view>
		</view>
		<view v-else style="height: 100rpx; line-height: 100rpx; text-align:center"> 请先配置排行榜 </view>
		
	</view>
</template>

<script>
	import {
		gotoPage
	} from "../../div-base/div-page-urls.js";
	import api from 'utils/api'
	export default {
		props: {
			popProps: {
				type: Object,
				default: {

				}
			}
		},
		watch: {
			popProps: {
				handler: function(newVal, oldVal) {
					if (newVal) {
						this.getGoods();
					}
				},
				immediate: true
			}
		},
		data() {
			return {
				popGoods: [],
				pagesLength:1,
			}
		},
		computed:{
			getPopImg(){
				return function(index){
					if(index<3){
						return `http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/pop${index+1}.png`
					} else {
						return `http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/popmore.png`
					}
				}
			},
		},
		created() {
			// this.getGoods();
			//监听当前页面栈的个数内容
			let pages = getCurrentPages()
			this. pagesLength = pages.length //当前页面栈的个数
		},
		methods: {
			//更多
			more(){
					gotoPage(
						`/pages/ranking/goods-ranking/index?id=${this.popProps.rankingGoodsId[0].id}`,this. pagesLength
					);
				
			},
			
			imgclick(item) {
				if (item) {
					const sourceModule = encodeURIComponent('排行榜');
					gotoPage(
						`/pages/goods/goods-detail/index?id=${item.spuId}&source_module=${sourceModule}`
					);
				}
			},

			skillCategoryId(pageUrl) {
				return pageUrl ? pageUrl.substring(pageUrl.indexOf('=') + 1, pageUrl.length) : ''
			},

			getGoods() {
				console.log("popProps",this.popProps);
				
				api.getRanking(
					this.popProps.rankingGoodsId[0].id
				).then(res => {
					console.log("nowSeckillSpus===",res.data)
					this.popGoods = res.data.rankSpuList;
					// this.popGoods = this.popGoods.concat(this.popGoods)
					console.log("popGoods===", this.popGoods)
				});
			}
		}

	}
</script>

<style scoped lang="scss">
	.two-goods {
		width: 100%;
		padding: 10rpx;

		.item {
			display: inline-block;
			width: 31%;
			position: relative;
			height: 330rpx;
			background: #FFFDFE;
			border-radius: 16rpx;
			margin: 8rpx;

			.item-content {
				display: flex;
				width: 100%;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				padding: 4rpx;
			}

			.name {
				color: #000;
				font-size: 22rpx;
				position: relative;
				width: 100%;
				line-height: 24rpx;
				height: 22rpx;
				overflow: hidden;
				margin-bottom: 6rpx;
				margin-top: 10rpx;
			}

			.price-original {
				padding-left: 6rpx;
				font-weight: bold;
				color: #939393;
				text-decoration: line-through;
				min-height: 20rpx;
				font-size: 16rpx;
				margin-left: 6rpx;
				margin-right: 6rpx;
				margin-top: 4rpx;
			}

			.price-pop {
				width: 100%;
				display: flex;
				justify-content: space-between;
				background-size: 100% 100%;
				height: 45rpx;
				background-image: url(http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/pop_price_bg.png)
			}

			.buy-price {
				color: #ff0000;
				width: 110rpx;
				font-weight: bold;
				background: #FF3955;
				padding: 8rpx;
				border-radius: 16rpx;
				color: #fff;
				height: 34rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 10rpx;
			}
		}
	}
	
	.more {
		font-size: 27rpx;
		font-weight: 500;
		color: #695A57;
		line-height: 60rpx;
		text-align: center;
	}
</style>
