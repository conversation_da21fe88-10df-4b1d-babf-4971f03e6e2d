<template>
	<view class="two-goods" v-if="skillGoods&&skillGoods.length>0">
		<view v-for="(item, index) in skillGoods" class="item" :key="index">
      <block v-if="item.length > 1">
        <view style="position: relative;width: 100%;height: 100%;">
          <block v-for="(n, m) in item" :key="n.id">
            <view @click="imgclick(n)" class="item-content" :class="m % 2 ? 'css-animate' : 'css-animate1'" :style="{ '--s': (index % 2) ? '3s' : '0s' }">
            	<image :src="n.picUrl | formatImg360" mode="aspectFill" :style="{
                  width: '100%',
                  maxWidth: '166rpx',
                  height:'124rpx',
                  display: 'block',
                  textAlign: 'center'
                }"
              ></image>
            	<view style="
                color: #000;
                font-size: 20rpx;   
                position: relative;
                width: 100%;
                line-height: 24rpx;
                height: 22rpx;
                overflow: hidden;
                margin-left: 6rpx;
                margin-top: 6rpx;
                margin-right: 6rpx;
                ">{{n.name}}</view>
            	<view style="width: 100%;display: flex;maxWidth: 166rpx;align-items: center;flex: 1;justify-content: space-around;">
            		<view class="decoration-through" style="color: #555;font-size: 18rpx;margin-top: 5rpx;">
            			￥{{n.originalPrice || n.salesPrice}}
                </view>
                <view class="flex align-center" style="height: 100%;color: #ff0000;">
                  <price-handle :value="n.seckillPrice" signFont="12rpx" bigFont="22rpx" smallFont="16rpx"></price-handle>
                </view>
            	</view>
            </view>
          </block>
        </view>
      </block>
      <block v-else-if="item.length === 1">
        <view style="position: relative;width: 100%;height: 100%;">
          <view @click="imgclick(item[0])" class="item-content" style="position: absolute;top: 0;left: 0;">
          	<image :src="item[0].picUrl | formatImg360" mode="aspectFill" :style="{
              width: '100%',
              maxWidth: '166rpx',
              height:'124rpx',
              display: 'block',
              textAlign: 'center'
              }"
              ></image>
          	<view style="
              color: #000;
              font-size: 20rpx;   
              position: relative;
              width: 100%;
              line-height: 24rpx;
              height: 22rpx;
              overflow: hidden;
              margin-left: 6rpx;
              margin-top: 6rpx;
              margin-right: 6rpx;
              ">{{item[0].name}}</view>
          	<view style="width: 100%;display: flex;max-width: 166rpx;align-items: center;flex: 1;justify-content: space-around;">
              <view class="decoration-through" style="color: #555;font-size: 18rpx;margin-top: 5rpx;">
                ￥{{n.originalPrice || n.salesPrice}}
              </view>
              <view class="flex align-center" style="height: 100%;color: #ff0000;">
                <price-handle style="line-height: 100%;" :value="item[0].seckillPrice" signFont="12rpx" bigFont="22rpx" smallFont="16rpx"></price-handle>
              </view>
          	</view>
          </view>
        </view>
      </block>
    </view>
	</view>
</template>

<script>	import {
		gotoPage
	} from "../../div-base/div-page-urls.js";
	import api from 'utils/api'
	export default {
		props: {
			categoryId: {
				type: String,
				default: ''
			}
		},
		watch: {
			categoryId: {
				handler: function(newVal, oldVal) {
					if (newVal) {
					 this.getGoods();
					}
				},
				immediate: true
			}
		},
		data() {
			return {
				skillGoods: []
			}
		},
		created(){
			this.getGoods();
		},
		methods: {
			imgclick(item) {
					// console.log("skillGoods===", this.categoryId)
					if (item) {
						gotoPage(`/pages/seckill/seckill-list/index?id=${item.id}&seckillHallId=${item.seckillHallId}&categoryId=${this.skillCategoryId(this.categoryId)}`);
					} else {
						// uni.showToast({
						// 	title: '没有配置链接地址',
						// 	icon: 'none',
						// 	duration: 2000
						// });
						
					}
			},
			skillCategoryId(pageUrl) {
				return pageUrl ? pageUrl.substring(pageUrl.indexOf('=') + 1, pageUrl.length) : ''
			},

			getGoods() {
				api.nowSeckillSpus({
					categoryId: this.skillCategoryId(this.categoryId),
					num: 4
				}).then(res => {
          const { data } = res;
          let skillGoods = []
          while (data.length >= 2) {
            skillGoods.push([data.shift(), data.shift()]);
          }
          if (data.length === 1) {
            skillGoods.push(data);
          }
          if (skillGoods.length > 2) {
            skillGoods = skillGoods.slice(0, 2)
          }
          this.skillGoods = skillGoods
				});
			}
		}

	}
</script>

<style scoped lang="scss">
	
	.item-content {
		display:flex; 
		flex-direction: column;
		justify-content: center;
		align-items: center;
		flex: 1;
		max-width: 166rpx;
    background-color: #fff;
    width: 100%;
    height: 100%;
	}
	
	.two-goods {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		height: 100%;
		// border: 1px solid red;
		// overflow: scroll;
		.item:nth-child(2n) {
			padding-left: 6rpx;
			flex: 1;
      height: 100%;
		}
		.item:nth-child(2n+1) {
			padding-right: 6rpx;
			flex: 1;
      height: 100%;
		}
	}
  .css-animate {
    animation: keys1 8s infinite var(--s);
    position: absolute;
    left: 0;
  }
  .css-animate1 {
    animation: keys2 8s infinite var(--s);
    position: absolute;
    left: 0;
  }
  .css-animate.noHover {
    opacity: 0;
    transform: scale(.8);
  }
  @keyframes keys1 {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    45% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0;
      transform: scale(.8);
    }
    95% {
      opacity: 0;
      transform: scale(.8);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  @keyframes keys2 {
    0% {
      opacity: 0;
      transform: scale(.8);
    }
    45% {
      opacity: 0;
      transform: scale(.8);
    }
    50% {
      opacity: 1;
      transform: scale(1);
    }
    95% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(.8);
    }
  }
</style>
