<template>
	<!-- 图文广告显示组件 -->
	<view
		:style="{
			backgroundSize: '100% auto',
			backgroundRepeat: 'no-repeat',
			backgroundImage: isStatusBar ? '' : `url(${newData.bgImage})`,
			backgroundColor: newData.background,
			marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
			marginTop: `${newData.marginTopSpacing * 2}rpx`,
			marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
			marginRight: `${newData.marginRightSpacing * 2}rpx`,
			paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
			paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
			paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
			paddingRight: `${newData.paddingRightSpacing * 2}rpx`
		}"
		:class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''"
	>
		<view
			:style="{
				display: newData.swiperList && newData.swiperList.length > 1 ? 'flex' : 'block'
			}"
		>
			<template v-for="(item, index) in newData.swiperList">
				<!-- <scroll-view scroll-x="true" scroll-left="0" :key="index"> -->
				<view
					class="hot-zone"
					:key="index"
					:style="{
						width: item.width && item.width != undefined && item.width > 0 ? item.width * 2 + 'rpx' : '100%',
						overflowX: 'scroll',
						borderTopLeftRadius: `${newData.borderRadius == 1 ? newData.radiusLeftTop * 2 + 'rpx' : '0rpx'}`,
						borderTopRightRadius: `${newData.borderRadius == 1 ? newData.radiusRightTop * 2 + 'rpx' : '0rpx'}`,
						borderBottomLeftRadius: `${newData.borderRadius == 1 ? newData.radiusLeftBottom * 2 + 'rpx' : '0rpx'}`,
						borderBottomRightRadius: `${newData.borderRadius == 1 ? newData.radiusRightBottom * 2 + 'rpx' : '0rpx'}`
					}"
				>
					<image
						:src="item.imageUrl | formatImg750"
						:show-menu-by-longpress="newData.canLongPress == 1"
						mode="widthFix"
						:style="{
							width: '100%',
							display: 'block',
							marginTop: '-1px'
						}"
					></image>
					<template v-if="item.hotZones && item.hotZones.length > 0">
						<view
							class="zone"
							v-for="(zone, index) in item.hotZones"
							:key="index"
							:style="{
								width: zone.pageUrl == 'customcomponentseckilltimer' ? '100%' : getZoneStyle(zone.widthPer),
								height: getZoneStyle(zone.heightPer),
								top: getZoneStyle(zone.topPer),
								left: getZoneStyle(zone.leftPer)
							}"
						>
							<view class="hot-area" @click="(e) => handleZoneClick(e, zone.pageUrl)" hover-class="none">
								<text class="hot-txt" hover-class="none">{{ zone.pageName }}{{ newData.borderRadius }}</text>
							</view>
						</view>
					</template>
				</view>
				<!-- </scroll-view> -->
			</template>
		</view>
	</view>
</template>

<script>
const app = getApp();
import { gotoPage } from '../div-base/div-page-urls.js';
export default {
	name: 'basic-advert',
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					pageUrl: ``,
					imageUrl: '',
					height: 100,
					lastSecs: 40000,
					hour: 10
				};
			}
		},
		isStatusBar: {
			type: Boolean,
			default: false
		}
	},

	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value,
			lastSecs: 40000,
			hour: 10,
			pagesLength: 1
		};
	},

	created() {
		//监听当前页面栈的个数内容
		let pages = getCurrentPages();
		this.pagesLength = pages.length; //当前页面栈的个数
	},
	
	methods: {
		getZoneStyle(val) {
			return `${(val || 0) * 100}%`;
		},
		handleZoneClick(e, page) {
			console.log(e, page);
			if (page) {
				gotoPage(page, this.pagesLength);
			} else {
				// uni.showToast({
				//   title: '没有配置链接地址',
				//   icon: 'none',
				//   duration: 2000
				// });
			}
		}
	}
};
</script>

<style scoped lang="scss">
.hot-zone {
	position: relative;
}

.zone {
	position: absolute;

	.hot-area {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;

		.hot-txt {
			overflow: hidden;
			text-overflow: ellipsis;
			-webkit-line-clamp: 2;
			height: 40px;
			color: rgba($color: #000000, $alpha: 0);
		}
	}
}

.sec-container {
	display: flex;
	align-items: center;
	height: 25px;
}
</style>
