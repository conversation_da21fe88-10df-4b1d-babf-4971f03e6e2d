<template>
	<view>
		<!-- 图文广告显示组件 -->
		<!-- <oneLineOne v-if="newData.advertType == 'oneLineOne'" :isStatusBar="isStatusBar" v-model="newData" /> -->
		<div-status-bar v-if="isStatusBar" :isStatusBar="isStatusBar"
			v-model="newData" />
		<div-hotHorizontal-zone v-else-if="newData.advertType == 'horizontalLine' " :isStatusBar="isStatusBar"
			v-model="newData" />
		<oneLineOne v-else-if="newData.advertType == 'oneLineOne'" :isStatusBar="isStatusBar" v-model="newData" />
		<divTabSwiper v-else-if="newData.advertType == 'tabSwiper'" :isStatusBar="isStatusBar" v-model="newData" />
		<divTabAnchor v-else-if="newData.advertType == 'tabAnchor'" :isStatusBar="isStatusBar" v-model="newData" />
		<seckillHot v-else-if="newData.advertType == 'seckillHotZone'" :isStatusBar="isStatusBar" v-model="newData" />
		<div-hot-swiper v-else v-model="newData" :isStatusBar="isStatusBar" />
	</view>
	
</template>

<script>
	const app = getApp();
	import divHotSwiper from "./div-hot-swiper.vue";
	import divHotHorizontalZone from "./div-hot-horizontal-zone.vue";
	import oneLineOne from "./oneLineOne.vue";
	import seckillHot from "./seckillHot.vue";
	import divTabSwiper from "./div-tab-swiper.vue";
	import divTabAnchor from "./div-tab-anchor.vue";
	import divStatusBar from "./div-status-bar.vue";
	export default {
		name: "basic-advert",
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						pageUrl: ``,
						imageUrl: "",
						height: 100,
					};
				},
			},
			isStatusBar: {
				type: Boolean,
				default: false
			}
		},
		components: {
			divHotSwiper,
			divTabAnchor,
			divHotHorizontalZone,
			oneLineOne,
			divTabSwiper,
			divStatusBar,
			seckillHot
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
			};
		},
		methods: {
		},
	};
</script>

