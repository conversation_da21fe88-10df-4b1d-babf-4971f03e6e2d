<template>
	<!-- 热区组件 -->
	<view class="div-advert">
		<cu-custom v-if="isStatusBar" :bgColor="newData.background" :bgImage="newData.bgImage">
			<block slot="backText">返回</block>
			<block slot="marchContent">
				<advert v-model="newData" :isStatusBar="true"></advert>
			</block>
		</cu-custom>

		<advert v-else v-model="newData"></advert>
	</view>

</template>

<script>
	import advert from "./div-advert.vue";

	export default {
		components: {
			advert
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
					}
				}
			},
			isStatusBar: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				newData: this.value,
			};
		}
	}
</script>

