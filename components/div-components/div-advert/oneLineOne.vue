<template>
  <!-- 图文广告显示组件 -->
  <view
    :style="{
      backgroundSize: '100% auto',
	  backgroundRepeat:'no-repeat',
      backgroundImage: isStatusBar?'':`url(${newData.bgImage})`,
      backgroundColor: newData.background,
      marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
      marginTop: `${newData.marginTopSpacing * 2}rpx`,
      marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.marginRightSpacing * 2}rpx`,
      paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
      paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
      paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
      paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
    }"
    :class="
      newData.background && newData.background.indexOf('bg-') != -1
        ? newData.background
        : ''
    "
  >
    <view>
      <template v-for="(item, swiperIndex) in newData.swiperList">
        <!-- <scroll-view scroll-x="true" scroll-left="0" :key="index"> -->
        <view
          class="hot-zone"
          :key="swiperIndex"
          :style="{
						  width: item.width ? (item.width*2) + 'rpx' : '100%',
						  overflowX: 'scroll',
						  overflowY: 'hidden',
						  lineHeight:'0',
							borderTopLeftRadius:`${newData.borderRadius == '1' ? (newData.radiusLeftTop*2)+'rpx':'0px'}`,
							borderTopRightRadius:`${newData.borderRadius == '1' ? (newData.radiusRightTop*2)+'rpx' : '0px'}`,
							borderBottomLeftRadius:`${newData.borderRadius == '1' ? (newData.radiusLeftBottom*2)+'rpx':'0px'}`,
							borderBottomRightRadius:`${newData.borderRadius == '1' ?(newData.radiusRightBottom*2)+'rpx':'0px'}`,
						}"
        >
          <image
            :src="item.imageUrl | formatImg750"
			:show-menu-by-longpress="newData.canLongPress==1"
            mode="widthFix"
            :style="{
							height:'auto',
							width: '100%',
							display: 'block',
							borderRadius: `${newData.borderRadius == '1' ? '12rpx' : '0px'}`,
						  }"
          ></image>

          <view
            class="zone"
            v-for="(zone, index) in item.hotZones"
            :key="index"
            :style="{
							width: zone.pageUrl=='customcomponentseckilltimer'?'100%':getZoneStyle(zone.widthPer),
							height: getZoneStyle(zone.heightPer),
							top: getZoneStyle(zone.topPer),
							left: getZoneStyle(zone.leftPer),
						  }"
          >
            <!-- 	<template v-if="zone.pageUrl.startsWith('/pages/seckill/seckill-list/index')">
								<seckill-spu style="position: absolute;" :categoryId="zone.pageUrl" />
							</template> -->

            <!-- <view v-if="zone.pageUrl&&zone.pageUrl.startsWith('customcomponent')">
								<view v-if="zone.pageUrl=='customcomponentseckilltimer' && hasSeckill"
									class="sec-container">
									<text class="seckill_num">{{curSeckillHall.hallTime}}点场</text>
									<text class="timer">{{hour}}: {{minute}}: {{second}}</text>
								</view>
							</view> -->
            <view
              class="hot-area"
              @tap="(e) => handleZoneClick(e, zone.pageUrl)"
              hover-class="none"
            >
              <text
                class="hot-txt"
                hover-class="none"
              >
                {{ zone.pageName }}{{zone.pageUrl.startsWith('/pages/seckill/seckill-list/index')}}
              </text>
            </view>
          </view>
        </view>
        <!-- </scroll-view> -->
      </template>
    </view>
  </view>
</template>

<script>
const app = getApp();
import {
  gotoPage
} from "../div-base/div-page-urls.js";
import seckillSpu from "./relate-component/seckill-spu.vue";
import bargainSpu from "./relate-component/bargain-spu.vue";
// import {
// 	seckillMixin
// } from "../../../static/mixins/seckill.js"

export default {
  // mixins: [seckillMixin], //混入文件
  components: {
    seckillSpu,
    bargainSpu
  },
  name: "basic-advert",
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {
          pageUrl: ``,
          imageUrl: "",
          height: 100,
        };
      },
    },
    isStatusBar: {
      type: Boolean,
      default: false
    },	
  },

  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      hour: 10,
	  pagesLength: 1,
    };
  },

  created () {
	//监听当前页面栈的个数内容
	let pages = getCurrentPages()
	this.pagesLength = pages.length; //当前页面栈的个数
  },

  methods: {
    getZoneStyle (val) {
      return `${(val || 0) * 100}%`;
    },
    handleZoneClick (e, page) {
      console.log('handleZoneClick-->',e, page);
      if (page) {
        gotoPage(page,this.pagesLength);
      } else {
        // uni.showToast({
        // 	title: '没有配置链接地址',
        // 	icon: 'none',
        // 	duration: 2000
        // });
      }
    }
  }
}
</script>

<style scoped lang="scss">
.hot-zone {
  position: relative;
}

.zone {
  position: absolute;
  // cursor: pointer;
  // border: 1px solid #155bd4;
  // background: rgba(21, 91, 212, 0.5);
  // font-size: 12px;
  // color: #fff;
  // user-select: auto;
  // touch-action: none;

  .hot-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    position: absolute;

    .hot-txt {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      height: 40px;
      color: rgba($color: #000000, $alpha: 0);
    }
  }
}

.sec-container {
  display: flex;
  align-items: center;
  height: 25px;
}

.seckill_num {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  background-image: -webkit-gradient(
    linear,
    right top,
    left top,
    from(#f10101),
    to(#fe4d17)
  );
  background-image: -webkit-linear-gradient(right, #f10101, #fe4d17);
  background-image: linear-gradient(-90deg, #f10101, #fe4d17);
  // box-shadow: 0 4rpx 8rpx 0 rgb(241 1 1 / 20%);
  font-size: 24rpx;
  color: #fff;
  letter-spacing: 0;
  border-radius: 2rpx 2rpx 2rpx 0;
  padding: 2px;
  z-index: 4;
  width: 80rpx;
}

.timer {
  background: #ecb4d9;
  color: #f10101;
  font-size: 24rpx;
  padding: 3px 6px 3px 10px;
  margin-left: -6px;
}
</style>
