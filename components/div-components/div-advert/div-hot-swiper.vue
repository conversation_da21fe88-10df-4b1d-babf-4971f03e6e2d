<template>
	<!-- 轮播图组件 -->
	<!-- <view :class="'bg-'+theme.backgroundColor"> -->
	<view
		class="bg-white"
		:style="{
			backgroundSize: '100% auto',
			backgroundRepeat: 'no-repeat',
			backgroundImage: `url(${newData.bgImage})`,
			backgroundColor: newData.background,
			marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
			marginTop: `${newData.marginTopSpacing * 2}rpx`,
			marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
			marginRight: `${newData.marginRightSpacing * 2}rpx`,
			paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
			paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
			paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
			paddingRight: `${newData.paddingRightSpacing * 2}rpx`
		}"
		:class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''"
	>
		<view v-show="newData.swiperType == 'card-swiper'" :class="'bg-' + theme.backgroundColor" :style="{ height: `${divHeight}rpx` }"></view>

		<view style="position: relative">
			<swiper
				@change="cardSwiper"
				class="screen-swiper"
				:class="newData.dotStyle"
				:circular="true"
				:indicator-color="indicatorColor"
				:indicator-active-color="indicatorActiveColor"
				:indicator-dots="newData && newData.swiperList && newData.swiperList.length > 1 && newData.dotStyle != 'none'"
				:style="{ height: `${divHeight}rpx` }"
				:autoplay="newData.interval > 0"
				:interval="newData.interval"
			>
				<swiper-item v-for="(item, index) in newData.swiperList" :key="index">
					<view
						class="hot-zone"
						:key="index"
						:style="{
							width: '100%',
							overflowX: 'scroll',
							overflowY: 'hidden',
							height: `${divHeight}rpx`,
							marginTop: '-1px'
						}"
					>
						<image
							:src="item.imageUrl | formatImg750"
							:show-menu-by-longpress="newData.canLongPress == 1"
							:style="{
								width: '100%',
								height: `${divHeight}rpx`,
								borderTopLeftRadius: `${newData.borderRadius == 1 ? newData.radiusLeftTop * 2 + 'rpx' : '0rpx'}`,
								borderTopRightRadius: `${newData.borderRadius == 1 ? newData.radiusRightTop * 2 + 'rpx' : '0rpx'}`,
								borderBottomLeftRadius: `${newData.borderRadius == 1 ? newData.radiusLeftBottom * 2 + 'rpx' : '0rpx'}`,
								borderBottomRightRadius: `${newData.borderRadius == 1 ? newData.radiusRightBottom * 2 + 'rpx' : '0rpx'}`
							}"
						></image>
						<view
							class="zone"
							v-for="(zone, index) in item.hotZones"
							:key="index"
							:style="{
								width: zone.pageUrl == 'customcomponentseckilltimer' ? '100%' : getZoneStyle(zone.widthPer),
								height: getZoneStyle(zone.heightPer),
								top: getZoneStyle(zone.topPer),
								left: getZoneStyle(zone.leftPer)
							}"
						>
							<!-- <view v-if="zone.pageUrl&&zone.pageUrl.startsWith('customcomponent')"> -->
							<!-- <seckilltimer  class="hot-area"/> -->
							<!-- <view v-if="zone.pageUrl=='customcomponentseckilltimer'" class="sec-container">
									<text class="seckill_num">14点场</text>
									<text class="timer">{{formatSeconds}}</text>
								</view>
							</view>  v-else -->
							<view class="hot-area" @tap="(e) => handleZoneClick(e, zone.pageUrl)" hover-class="none">
								<text class="hot-txt" hover-class="none">{{ zone.pageName }}{{ newData.borderRadius }}</text>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>

			<!-- <view class="left-back" @click.stop="leftImg">
				<i class="cuIcon-pullleft"></i>
			</view>
			<view class="right-forword" @click.stop="rightImg">
				<i class="cuIcon-pullright"></i>
			</view> -->
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
const app = getApp();
import { gotoPage } from '../div-base/div-page-urls.js';
export default {
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					swiperType: 'screen-swiper',
					height: 150,
					interval: 3000,
					borderRadius: 0,
					imageSpacing: 0,
					swiperList: [],
					pagesLength: 1
				};
			}
		}
	},
	components: {},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value,
			curDot: 0,
			lastSecs: 40000,
			hour: 10,
			pagesLength: 1
		};
	},

	created() {
		this.getLastTime();
		//监听当前页面栈的个数内容
		let pages = getCurrentPages();
		this.pagesLength = pages.length; //当前页面栈的个数
	},
	computed: {
		...mapState(['isPhone']),
		divHeight() {
			return this.isPhone ? this.newData.height * 2 : this.newData.height * 1.2;
		},
		indicatorColor() {
			if (this.value && this.value.indicatorColor) {
				return this.value.indicatorColor;
			}
			return '#aaa';
		},

		indicatorActiveColor() {
			if (this.value && this.value.indicatorActiveColor) {
				return this.value.indicatorActiveColor;
			}
			return '#eee';
		},

		formatSeconds() {
			let result = parseInt(this.lastSecs);
			let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
			let m = Math.floor((result / 60) % 60) < 10 ? '0' + Math.floor((result / 60) % 60) : Math.floor((result / 60) % 60);
			let s = Math.floor(result % 60) < 10 ? '0' + Math.floor(result % 60) : Math.floor(result % 60);

			let res = '';
			if (h !== '00') res += `${h}:`;
			if (m !== '00') res += `${m}:`;
			res += `${s}`;
			return res;
		}
	},
	methods: {
		leftImg() {
			let num = this.newData.swiperList.length - 1;
			if (this.curDot <= 0) {
				this.curDot = num;
			} else {
				this.curDot--;
			}
		},
		rightImg() {
			let num = this.newData.swiperList.length - 1;
			if (this.curDot >= num) {
				this.curDot = 0;
			} else {
				this.curDot++;
			}
		},

		cardSwiper(e) {
			this.curDot = e.detail.current;
		},

		getZoneStyle(val) {
			return `${(val || 0) * 100}%`;
		},
		handleZoneClick(e, page) {
			console.log(e, page);
			if (page) {
				gotoPage(page, this.pagesLength);
			} else {
				// uni.showToast({
				// 	title: '没有配置链接地址',
				// 	icon: 'none',
				// 	duration: 2000
				// });
			}
		},

		getLastTime() {
			this.timer = setInterval(() => {
				this.lastSecs--;
				if (this.lastSecs === 0) {
					clearInterval(this.timer);
				}
			}, 1000);
		}
	}
};
</script>

<style scoped lang="scss">
.screen-swiper {
	min-height: 90upx !important;
}

.hot-zone {
	position: relative;
}

.zone {
	position: absolute;
	// cursor: pointer;
	// border: 1px solid #155bd4;
	// background: rgba(21, 91, 212, 0.5);
	// font-size: 12px;
	// color: #fff;

	.hot-area {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;
		background: rgba($color: #000000, $alpha: 0);

		.hot-txt {
			overflow: hidden;
			text-overflow: ellipsis;
			-webkit-line-clamp: 2;
			height: 40px;
			color: rgba($color: #000000, $alpha: 0);
		}
	}
}

.left-back,
.right-forword {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	font-size: 60rpx;
	color: #c8b19b;
}

.left-back {
	left: 0;
}

.right-forword {
	right: 0;
}

.sec-container {
	display: flex;
	align-items: center;
	height: 25px;
}

.seckill_num {
	display: -webkit-inline-box;
	display: -webkit-inline-flex;
	display: inline-flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	background-image: -webkit-gradient(linear, right top, left top, from(#f10101), to(#fe4d17));
	background-image: -webkit-linear-gradient(right, #f10101, #fe4d17);
	background-image: linear-gradient(-90deg, #f10101, #fe4d17);
	// box-shadow: 0 4rpx 8rpx 0 rgb(241 1 1 / 20%);
	font-size: 24rpx;
	color: #fff;
	letter-spacing: 0;
	border-radius: 2rpx 2rpx 2rpx 0;
	padding: 2px;
	z-index: 4;
	width: 80rpx;
}

.timer {
	background: #ecb4d9;
	color: #f10101;
	font-size: 24rpx;
	padding: 3px 6px 3px 10px;
	margin-left: -6px;
}
</style>
