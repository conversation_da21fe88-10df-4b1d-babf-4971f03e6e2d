<template>
  <!-- 图文广告显示组件 -->
  <view
    :style="{
      backgroundSize: '100% auto',
	  backgroundRepeat:'no-repeat',
      backgroundImage: isStatusBar?'':`url(${newData.bgImage})`,
      backgroundColor: newData.background,
      marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
      marginTop: `${isStatusBar?(computedHeight-newData.searchHeight):(newData.marginTopSpacing*2)}rpx`,
      marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.marginRightSpacing * 2}rpx`,
      paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
      paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
      paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
      paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
	  height:`${computedHeight}px`,
    }"
    :class="
      newData.background && newData.background.indexOf('bg-') != -1
        ? newData.background
        : ''
    "
  >
    <view :style="{
			width: '100%'
		}">
      <view style="display: flex; flex-direction: row; align-items: center;">
        <template v-for="(item, index) in newData.swiperList">
          <view
            class="hot-zone"
            :key="index"
            :style="{
					   width: item.width ? (item.width*2) + 'rpx' : '100%',
					 }"
          >
            <image
              :src="item.imageUrl | formatImg750"
              mode="aspectFit"
			  :show-menu-by-longpress="newData.canLongPress==1"
              :style="{
							 width:  '100%',
							 display: 'block',
							 height:`${computedHeight}px`,
							 marginTop:'-1px',
							 borderTopLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftTop*2)+'rpx':'0rpx'}`,
							 borderTopRightRadius:`${newData.borderRadius == 1 ? (newData.radiusRightTop*2)+'rpx' : '0rpx'}`,
							 borderBottomLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
							 borderBottomRightRadius:`${newData.borderRadius == 1 ?(newData.radiusRightBottom*2)+'rpx':'0rpx'}`,
						   }"
            ></image>
            <view
              class="zone"
              v-for="(zone, index) in item.hotZones"
              :key="index"
              :style="{
							 width: getZoneStyle(zone.widthPer),
							 height: getZoneStyle(zone.heightPer),
							 top: getZoneStyle(zone.topPer),
							 left: getZoneStyle(zone.leftPer),
						   }"
            >
              <view
                class="hot-area"
                @click="(e) => handleZoneClick(e, zone.pageUrl)"
                hover-class="none"
              >
                <text
                  class="hot-txt"
                  hover-class="none"
                >
                  {{ zone.pageName }}{{newData.borderRadius}}
                </text>
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>
  </view>
  </view>
</template>

<script>
const app = getApp();
import {
  gotoPage
} from "../div-base/div-page-urls.js";
export default {
  name: "basic-advert",
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {
          pageUrl: ``,
          imageUrl: "",
          height: 100,
        };
      },
    },
    isStatusBar: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      StatusBar: this.StatusBar,
      CustomBar: this.CustomBar,
	  pagesLength: 1,
    };
  },
  created () {
  	//监听当前页面栈的个数内容
  	let pages = getCurrentPages()
  	this.pagesLength = pages.length; //当前页面栈的个数
  },
  computed: {
    computedHeight () {
      return (this.CustomBar - this.StatusBar)
    }
  },
  methods: {
    getZoneStyle (val) {
      return `${(val || 0) * 100}%`;
    },
    handleZoneClick (e, page) {
      console.log(e, page);
      if (page) {
        gotoPage(page,this.pagesLength);
      } else {
        // uni.showToast({
        // 	title: '没有配置链接地址',
        // 	icon: 'none',
        // 	duration: 2000
        // });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.hot-zone {
  position: relative;
}

.zone {
  position: absolute;
  // cursor: pointer;
  // border: 1px solid #155bd4;
  // background: rgba(21, 91, 212, 0.5);
  // font-size: 12px;
  // color: #fff;
  // user-select: auto;
  // touch-action: none;

  .hot-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;

    .hot-txt {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      height: 40px;
      color: rgba($color: #000000, $alpha: 0);
    }
  }
}
</style>
