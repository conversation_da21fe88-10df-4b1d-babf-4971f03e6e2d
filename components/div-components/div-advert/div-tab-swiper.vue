<template>
  <!-- 轮播图组件 -->
  <!-- <view :class="'bg-'+theme.backgroundColor"> -->
  <view
    class="bg-white"
    :style="{
      backgroundSize: '100% auto',
	  backgroundRepeat:'no-repeat',
      backgroundImage: `url(${newData.bgImage})`,
      backgroundColor: newData.background,
      marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
      marginTop: `${newData.marginTopSpacing * 2}rpx`,
      marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.marginRightSpacing * 2}rpx`,
      paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
      paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
      paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
      paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
    }"
    :class="
      newData.background && newData.background.indexOf('bg-') != -1
        ? newData.background
        : ''
    "
  >
    <view
      class="cu-bar"
      style="min-height: 20px"
    >
      <scroll-view
        scroll-x="true"
        style=" width: 100%; white-space: nowrap;"
        :scroll-into-view="'id-'+curDot"
      >
        <view class="nav text-white text-sm">
          <view style="position: relative; display: flex">
            <view v-for="(item, index) in newData.swiperList" :key="item.id">
              <image
                @click="tabSelect"
                :data-index="index"
                :id="'id-'+index"
				:show-menu-by-longpress="newData.canLongPress==1"
                :style="{
                  display: 'block',
                  overflowX: 'inherit',
                  overflowY: 'hidden',
                  color:(newData.textColor||'#fff'),
                  fontSize:(newData.textSize||24)+'rpx',
                  marginLeft: ((newData.itemSpace*2)||0) + 'rpx',
                  marginRight: ((newData.itemSpace*2)||0) + 'px',
                  width: ((item.itemWidth*2)||180)+'rpx',
                  minWidth: ((item.itemWidth*2)||180)+'rpx',
                  padding:0,
                  textAlign:'center',
                  marginTop: 0,
                  marginBottom: 0,	
                  borderTopLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftTop*2)+'rpx':'0rpx'}`,
                  borderTopRightRadius:`${newData.borderRadius == 1 ? (newData.radiusRightTop*2)+'rpx' : '0rpx'}`,
                  borderBottomLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
                  borderBottomRightRadius:`${newData.borderRadius == 1 ?(newData.radiusRightBottom*2)+'rpx':'0rpx'}`,
                 }"
                class="img"
                :src="item.iconPath"
                mode="widthFix"
              />
              <image
                @click="tabSelect"
                :data-index="index"
                :id="'id-'+index"
                :style="{
                  display: 'block',
                  overflowX: 'inherit',
                  overflowY: 'hidden',
                  color: (newData.selectTextColor||'#ff0'),
                  fontSize:(newData.selectTextSize||28)+'rpx',
                  marginLeft: ((newData.itemSpace*2)||0) + 'rpx',
                  marginRight: ((newData.itemSpace*2)||0) + 'px',
                  width: ((item.itemWidth*2)||180)+'rpx',
                  minWidth: ((item.itemWidth*2)||180)+'rpx',
                  padding:0,
                  textAlign:'center',
                  marginTop: 0,
                  marginBottom: 0,	
                  borderTopLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftTop*2)+'rpx':'0rpx'}`,
                  borderTopRightRadius:`${newData.borderRadius == 1 ? (newData.radiusRightTop*2)+'rpx' : '0rpx'}`,
                  borderBottomLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
                  borderBottomRightRadius:`${newData.borderRadius == 1 ?(newData.radiusRightBottom*2)+'rpx':'0rpx'}`,
                  opacity: `${index == curDot ? 1 : 0}`,
                  position: 'absolute',
                  top: 0
                 }"
                class="img"
                :src="item.selectedIconPath"
                mode="widthFix"
              />
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <view
      v-show="newData.swiperType == 'card-swiper'"
      :class="'bg-' + theme.backgroundColor"
      :style="{  height: `${newData.swiperList[curDot].height>0?(newData.swiperList[curDot].height*2):(newData.height * 2)}rpx` }"
    >
    </view>

    <view style="position: relative">
      <swiper
        @animationfinish="cardSwiper"
        class="screen-swiper"
        :class="newData.dotStyle"
        :circular="true"
        indicator-color="#cccccc"
        indicator-active-color="#ffffff"
        :style="{
          height: getHeight(curDot)
        }"
        :current="curDot"
        :duration='1'
      >
        <swiper-item
          v-for="(item, index) in newData.swiperList"
          :key="index"
        >
		  <!-- 排行榜 -->
		  <pop-spu  v-if="item.contentType=='pop'" :popProps="item"/>
		  <!-- 图片 -->
          <view
		    v-else
            class="hot-zone"
            :key="index"
            :style="{
					  width: '100%',
					  overflowX: 'scroll',
					  overflowY: 'hidden',
					  height: `${item.height>0?(item.height*2):(newData.height * 2)}rpx` ,
					  marginTop:'-1px',
					}"
          >
            <image
              :src="item.imageUrl | formatImg750"
            mode="scaleToFill"
              :style="{
							height: `${item.height>0?(item.height*2):(newData.height * 2)}rpx` ,
							borderTopLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftTop*2)+'rpx':'0rpx'}`,
							borderTopRightRadius:`${newData.borderRadius == 1 ? (newData.radiusRightTop*2)+'rpx' : '0rpx'}`,
							borderBottomLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
							borderBottomRightRadius:`${newData.borderRadius == 1 ?(newData.radiusRightBottom*2)+'rpx':'0rpx'}`,
						  }"
            ></image>

            <view
              class="zone"
              v-for="(zone, index) in item.hotZones"
              :key="index"
              :style="{
							width: zone.pageUrl=='customcomponentseckilltimer'?'100%':getZoneStyle(zone.widthPer),
							height: getZoneStyle(zone.heightPer),
							top: getZoneStyle(zone.topPer),
							left: getZoneStyle(zone.leftPer),
						  }"
            >
             
              <view
                class="hot-area"
                @tap="(e) => handleZoneClick(e, zone.pageUrl)"
                hover-class="none"
              >
                <text
                  class="hot-txt"
                  hover-class="none"
                >
                  {{ zone.pageName }}{{newData.borderRadius}}
                </text>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>

      <!-- <view class="left-back" @click.stop="leftImg">
				<i class="cuIcon-pullleft"></i>
			</view>
			<view class="right-forword" @click.stop="rightImg">
				<i class="cuIcon-pullright"></i>
			</view> -->
    </view>
  </view>
</template>

<script>
const app = getApp();
import popSpu from "./relate-component/pop-spu.vue";
import {
  gotoPage
} from "../div-base/div-page-urls.js";
export default {
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {
          swiperType: "screen-swiper",
          height: 150,
          interval: 3000,
          borderRadius: 0,
          imageSpacing: 0,
          swiperList: [],
		   pagesLength: 1,
        };
      },
    },
  },
  components: {
	  popSpu
  },
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      curDot: 0,
      lastSecs: 40000,
      hour: 10,
    };
  },

  created () {
    this.getLastTime();
	//监听当前页面栈的个数内容
	let pages = getCurrentPages()
	this.pagesLength = pages.length; //当前页面栈的个数
  },
  computed: {
    formatSeconds () {
      let result = parseInt(this.lastSecs)
      let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
      let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result /
        60 % 60));
      let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60));

      let res = '';
      if (h !== '00') res += `${h}:`;
      if (m !== '00') res += `${m}:`;
      res += `${s}`;
      return res;
    },
    getHeight(){
      return function(curDot){
        if(this.newData.swiperList[curDot].contentType=='pop'){
          return '760rpx'
        }else {
           return  (this.newData.swiperList[curDot].height>0?(this.newData.swiperList[curDot].height*2):(this.newData.height * 2))+'rpx'
        }
      }
    }
  },
  methods: {
    tabSelect (e) {
      let tabIndex = e.currentTarget.dataset.index;
      if (+tabIndex === +this.curDot) {
        return;
      }
      this.curDot = tabIndex;
    },
    leftImg () {
      let num = this.newData.swiperList.length - 1;
      if (this.curDot <= 0) {
        this.curDot = num;
      } else {
        this.curDot--;
      }
    },
    rightImg () {
      let num = this.newData.swiperList.length - 1;
      if (this.curDot >= num) {
        this.curDot = 0;
      } else {
        this.curDot++;
      }
    },
    cardSwiper (e) {
      if(+this.curDot === +e.detail.current) {
        return ;
      }
      this.curDot = +e.detail.current;
    },
    getZoneStyle (val) {
      return `${(val || 0) * 100}%`;
    },
    handleZoneClick (e, page) {
      if (page) {
        gotoPage(page,this.pagesLength);
      } else {
        // uni.showToast({
        // 	title: '没有配置链接地址',
        // 	icon: 'none',
        // 	duration: 2000
        // });
      }
    },

    getLastTime () {
      this.timer = setInterval(() => {
        this.lastSecs--
        if (this.lastSecs === 0) {
          clearInterval(this.timer)
        }
      }, 1000)
    }
  },
};
</script>

<style scoped lang="scss">
.img {
  width: 100%;
  height: auto;
}

.scrollBox {
  overflow-x: scroll;
}

.screen-swiper {
  min-height: 90upx !important;
}

.hot-zone {
  position: relative;
}

.zone {
  position: absolute;
  // cursor: pointer;
  // border: 1px solid #155bd4;
  // background: rgba(21, 91, 212, 0.5);
  // font-size: 12px;
  // color: #fff;

  .hot-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0);

    .hot-txt {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      height: 40px;
      color: rgba($color: #000000, $alpha: 0);
    }
  }
}

.left-back,
.right-forword {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 60rpx;
  color: #c8b19b;
}

.left-back {
  left: 0;
}

.right-forword {
  right: 0;
}

.sec-container {
  display: flex;
  align-items: center;
  height: 25px;
}

</style>
