<template>
  <!-- 商品分组组件 -->
  <view
    class="content"
    ref="content"
    :style="{
	  marginBottom: `${newData.marginBottomSpacing * multipleView}rpx`,
	  marginTop: `${newData.marginTopSpacing * multipleView}rpx`,
	  marginLeft: `${newData.marginLeftSpacing * multipleView}rpx`,
	  marginRight: `${newData.marginRightSpacing * multipleView}rpx`,
	}"
  >
    <view
      class="pageComponent"
      id="titleBox"
      :style="{ backgroundColor: newData.background,
	      paddingBottom: `${newData.paddingBottomSpacing}px`,
	      paddingTop: `${newData.paddingTopSpacing}px`,
	      paddingLeft: `${newData.paddingLeftSpacing}px`,
	      paddingRight: `${newData.paddingRightSpacing}px`,
		  top:fixedTab? (CustomBar)+'px' :''}"
      :class="[fixedTab?'fixedBox':'' ]"
    >
      <view
        class="cu-bar"
        style="min-height: 28rpx"
        :class="[newData.background && newData.background.indexOf('bg-') != -1
	            ? newData.background
	            : '' ]
	        "
      >
        <scroll-view
          scroll-x="true"
          style=" width: 100%; white-space: nowrap;"
          :scroll-into-view="'id-'+TabCur"
        >
          <view class="nav text-white text-sm">
            <view style="position: relative; display: flex;">
				 <!-- borderBottom: (item.titleStyle == '1' && index==TabCur)? ('solid '+ (newData.bottomBorderSize||1)+'px '+ (newData.selectTextColor||'#ff0')):'' -->
              <view
                :id="'id-'+index"
                @click="tabSelect"
                class="cu-item"
                v-for="(item, index) in newData.navButtons"
                :key="index"
                :data-index="index"
                :style="{
					  overflow: 'inherit',
	                  color: index==TabCur?(newData.selectTextColor||'#ff0'):(newData.textColor||'#fff'),
	                  fontSize:index==TabCur?(newData.selectTextSize*multipleView|| 26)+'rpx':(newData.textSize*multipleView || 24)+'rpx',
	                  marginLeft: ((newData.itemSpace*multipleView)||0) + 'rpx',
	                  marginRight: ((newData.itemSpace*multipleView)||0) + 'px',
	                  width: ((item.itemWidth*multipleView)||180)+'rpx',
					  minWidth: ((item.itemWidth*multipleView)||180)+'rpx',
					  padding:0,
					  textAlign:'center',
					  fontWeight: index==TabCur? 'bold':'normal'
	                }"

              >
                <view
                  v-if="item.titleStyle == '2'"
                  class="blockImg"
                  :class="item.titleStyle1 ? '' : 'singleStyle'"
                >
                  <image
                    :src="index == TabCur ? item.selectedIconPath : item.iconPath "
                    mode="widthFix"
                  />
                </view>
                <view v-else>{{ item.name }}</view>
                <view
                  v-if="item.titleStyle1"
                  class="subtitle"
                >
                  <!-- {{item.iconPath1  }} -->
                  <view
                    v-if="item.titleStyle1 == '2'"
                    class="imgBox"
                  >
                    <image
                      style="width: 100%"
                      mode="widthFix"
                      :src="
								index == TabCur ? item.selectedIconPath1 : item.iconPath1
							  "
                    ></image>
                  </view>

                  <text v-else>{{ item.name1 }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <swiper
      class="scroll-item-box"
	  easing-function="linear"
      :current="TabCur"
      @change="swipeChange"
      :style="{ height: swiperHeight[TabCur] + 'px' }"
      @animationfinish="transitionChange"
    >
      <block
        v-for="(item, index) in newData.navButtons"
        :key="index"
      >
        <swiper-item>
          <view
            :id="'content-wrap' + index"
          >

            <view-goods
              v-model="item.contentSet"
              :actType="newData.activityType"
			  :params="{
				  ...newData
			  }"
              :contentBack="newData.background1"
              :loadingWay="newData.loadingWay"
              :child="true"
            >
            </view-goods>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
</template>

<script>
const app = getApp();
import api from '@/utils/api'
import {
  EventBus
} from '@/utils/eventBus.js'
import viewGoods from "@/components/div-components/div-goods/div-goods.vue";
import { mapState } from 'vuex';
// let contentElemToTop = 0;
export default {
  components: {
    viewGoods
  },
  // inject: ['reachBottom'],
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {
          background: ``,
        }
      }
    }
  },

  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      TabCur: 0,
      scrollLeft: 0,
      pageInfoCopy: {
        current: 1,
        size: 10
      },
      pageInfo: {},
      current: 0, //初始化时slide的索引
      reachBottomState: {},
      swiperHeight: {}, //外部的高度
      fixedTab: false, // 内容可滚动状态
      titleBoxHeight: 0, // 标题内容的高度
      contentElemToTop: 0,
      CustomBar: this.CustomBar,
    };
  },


  computed: {
	...mapState(['isPhone', 'windowWidth', 'multipleView']),
    // 获取屏幕高度
    screenHeight: function () {
      // 一屏幕高度
      let _systemInfo = uni.getSystemInfoSync(),
        pixelRatio = _systemInfo.pixelRatio,
        screenHeight = _systemInfo.windowHeight;
      return (screenHeight - this.CustomBar - 90 / pixelRatio) + 'px';

    },
    // 计算宽度
    calculateWidth () {
      if (!this.newData || !this.newData.showType || !this.newData.navButtons)
        return 0;
      let _width = 0,
        code = this.newData.showType,
        num = this.newData.navButtons.length;
      // if (!code || !list) return 0;
      if (code == "60") {
        _width = (100 / 6) * num;
      } else if (code == "72") {
        _width = (100 / 5) * num;
      } else {
        _width = (100 / 4) * num;
      }
      return _width;
    },
  },
  created () {

    // },
    // mounted() {
    /**
     * 循环 判断按钮中的内容类型是什么类型；1：商品；2：分组；3：智能推荐
     * 	商品（1）： 不需要处理
     *  分组（2）：创建分页使用数据放入pageInfo中 key为当前索引，清空 goodsList 中的数据
     *  分组（3）：创建分页使用数据放入pageInfo中 key为当前索引，清空 goodsList 中的数据
     *
     * */
    let _navButtonList = this.newData.navButtons
    _navButtonList.forEach((item, index) => {
      if (item.contentSet.contentStyle != '1') {
        item.contentSet.goodsList = [];
        let _page = JSON.parse(JSON.stringify(this.pageInfoCopy))
        if (item.pageSize) _page.size = item.pageSize
        this.pageInfo[index] = _page
        this.reachBottomState[index] = {
          reachBottom: true
        }
      }
      // 处理 如果类型为 分组 把 contentSet.groupingList 字段 中id提前处理并赋值
      if (item.contentSet.contentStyle == '2') {
        let groupsId = [];
        item.contentSet.groupingList.forEach(item => {
          groupsId.push(item.id)
        });
        item.contentSet.groupingList = groupsId.join(",");
      }
      item.contentSet.paddingBottomSpacing = this.newData.paddingBottomSpacing;
      item.contentSet.paddingTopSpacing = this.newData.paddingTopSpacing;
      item.contentSet.paddingLeftSpacing = this.newData.paddingLeftSpacing;
      item.contentSet.paddingRightSpacing = this.newData.paddingRightSpacing;
    });
    /**
     * 判断 第一个 按钮下的商品类型是什么
     * 分组： 获取分组数据
     * 智能推荐：获取推荐数据
     * */
    let contentStyle = _navButtonList[0].contentSet.contentStyle;
    if (contentStyle == "2") {
      this.getGroupsGoods(1)
    } else if (contentStyle == "3") {
      // toDo
    } else {
      this.getElementHeight();
      setTimeout(() => {
        this.getElementHeight();
      }, 3000)
    }
    // 监听页面滑动事件 title固定顶部
    uni.$on('vonPageScroll', this.scrollToTop);
    EventBus.$on("divGoodsGroupsReachBottom", () => {
      this.scrolltolower();
    })
  },

  // mounted() {
  //    let that = this;
  //    this.$nextTick(function() {
  //      const query = uni.createSelectorQuery().in(this); //获得实力
  //      query.select('#titleBox').boundingClientRect();
  //      query.exec((res) => {
  //        if (res && res[0]) {
  //           that.contentElemToTop = res[0].top;
  //          that.titleBoxHeight = res[0].height;
  //        }
  //      });
  //    })

  // },

  mounted() {
	if(this.newData.isShowLottery==1){
	  this.$EventBus.$emit("handleNyRedPacket", this.newData.activityType)
	}
  },

 destroyed() {
	 EventBus.$off('divGoodsGroupsReachBottom');
 },

  methods: {
    tabSelect (e) {
      let tabIndex = e.currentTarget.dataset.index;
      if (tabIndex == this.TabCur) {
        return;
      }
      this.TabCur = tabIndex;
    },
    /** 判断 获取哪种类型的数据  */
    getData () {
      let _index = this.TabCur,
        _navButtonList = this.newData.navButtons,
        contentStyle = _navButtonList[_index].contentSet.contentStyle;

      if (_navButtonList[_index].contentSet.goodsList.length <= 0) {
        if (contentStyle == "2") {
          this.getGroupsGoods()
        } else if (contentStyle == "3") {
          // toDo
          this.getGoodsRecommend(_navButtonList[_index].contentSet.airecType)
        } else {
          this.getElementHeight()
        }
      } else {
        this.getElementHeight()
      }
    },
    /**
     * 获取分组商品
     * */
    getGroupsGoods () {
      let _index = this.TabCur,
        groupingList = this.newData.navButtons[_index].contentSet.groupingList;
      api.goodsGroupsGet(
        Object.assign(
          this.pageInfo[_index], {
          goodsGroupIds: groupingList
        }
        )
      ).then(res => {
        // console.log(res)
        let records = res.data.records;
        if (records.length < this.pageInfo[_index].size) {
          this.reachBottomState[_index].reachBottom = false
        }
        this.newData.navButtons[_index].contentSet.goodsList.push(...records);
        this.$nextTick(() => {
          this.getElementHeight();
        })
      })
    },
    /**
     * 获取智能推荐数据
     * */
    getGoodsRecommend (sceneId) {
      let _index = this.TabCur;
      api.getRecommendList({
        sceneId: sceneId,
        // current: 1,
        // size: 4,
      }).then(res => {
        if (res.data.length < this.pageInfo[_index].size) {
          this.reachBottomState[_index].reachBottom = false
        }
        this.newData.navButtons[_index].contentSet.goodsList.push(...res.data);
        this.$nextTick(() => {
          this.getElementHeight();
        })
        // this.goodsListRecom = res.data;
      });
    },
    /* swiper左右滑动 */
    swipeChange (event) {
      this.TabCur = event.detail.current;
      this.getData()
    },
    /* scrolltolower 触发底部 */
    scrolltolower (e) {
      console.error("=========触发底部============");
      // 判断 如果加载方式为 调整 不触发上滑加载
      if (this.newData.loadingWay == '1') {
        return;
      }
      let _index = this.TabCur,
        _navButtonList = this.newData.navButtons,
        contentStyle = _navButtonList[_index].contentSet.contentStyle,
        reachBottom = this.reachBottomState[_index] ? this.reachBottomState[_index].reachBottom : "";
      if (!reachBottom || contentStyle == "1") {
        return;
      }
      this.pageInfo[_index].current += 1;

      if (contentStyle == "2") {
        this.getGroupsGoods()

      } else if (contentStyle == "3") {
        // toDo
      }
    },

    //动态获取高度
    getElementHeight () {
      //一定要 this.$nextTick 完成之后在获取dom节点高度
      /**
       * row 单列
       * card 双列
       * three 三列
       * waterfall 瀑布流
       *
       * */
      // let _hei1 = 410
      // console.log(this.newData.navButtons, this.TabCur)
      // let _navButtons = this.newData.navButtons, // 分组数据集合
      // 	_TabCur = this.TabCur, // 当前选中哪个tab索引
      // 	_showType = _navButtons[_TabCur].showType; // 当前数据的展示方式
      // if (_showType != "row") {

      // }
      let that = this;
      this.$nextTick(() => {
        let element = "#content-wrap" + this.TabCur;
        let query = uni.createSelectorQuery().in(this);
        query.select(element).boundingClientRect();

        query.exec((res) => {
          if (res && res[0]) {
            let _hei = res[0].height > 0 ? res[0].height : parseInt(this.screenHeight);
            // if (this.swiperHeight[this.TabCur]) {
            // this.swiperHeight[this.TabCur] = _hei
            // } else {
            // 	this.swiperHeight.push({
            // 		this.TabCur: _hei
            // 	})
            // }
            console.log("看看高度：", _hei)
            this.$set(this.swiperHeight, this.TabCur, _hei)
            // console.log("this.swiperHeight", this.swiperHeight);
          }
        });
      })

    },
    transitionChange() {
      this.getElementHeight();
    },
    // 页面滚动执行
    scrollToTop (pageScroll) {
      let that = this;
      //获取单个节点方式 title固定顶部
      if (!(that.contentElemToTop > 0)) {
        const query = uni.createSelectorQuery().in(this); //获得实力
        query.select('#titleBox').boundingClientRect();
        query.exec((res) => {
          if (res && res[0]) {
            if (res[0].top <= that.CustomBar) {
              this.fixedTab = true;
              that.contentElemToTop = pageScroll.scrollTop;
            } else {
              this.fixedTab = false;
            }
          }
        });
      } else {
        let scrollTop = pageScroll.scrollTop;
        if (scrollTop >= (this.contentElemToTop) && this.contentElemToTop > 0) {
          this.fixedTab = true;
        } else {
          this.fixedTab = false;
        }
      }

      // let _el = this.$refs.titleBox;
      // let that = this;
      // if( !(that.tabMarginTab>0)){
      //  let element = "#groupTab";
      //  	let query = uni.createSelectorQuery().in(that);
      //  	query.select(element).boundingClientRect();
      //  	query.exec((res) => {
      //  	  console.error(res, "groupTab元素位置")
      //  	  that.tabMarginTab = res&&res[0]&&res[0].top>0?res&&res[0]&&res[0].top:0
      //  	})
      // }


      /**
       * contentElemToTop 分组组件距离底部多少
       * 判断 scrollTop > contentElemToTop  标题悬浮在顶部
       *
       * */
      // let scrollTop = res.scrollTop;
      // if (scrollTop >= (this.contentElemToTop + this.CustomBar)&& this.contentElemToTop>0) {
      //   this.fixedTab = true;
      // } else {
      //   this.fixedTab = false;
      // }

    },
    // scrollView 标签滚动事件
    scrollViewScroll (event) {

      // event.detail = {scrollLeft, scrollTop, scrollHeight, scrollWidth, deltaX, deltaY}
      // console.log("====**********",scrollTop)
      // 滚动时触发，event.detail =
      // if (scrollTop <= 0) {
      // 	this.scrollState = false;
      // }
    },


  },




}
</script>

<style scoped lang="scss">
.scrollBox {
  overflow-x: scroll;
}
// .cur {
// 	border-bottom: 2px solid;
// }

// .nav-name {
// 	position: absolute;
// 	left: 50%;
// 	transform: translateX(-50%);
// }
.content{
	max-width: 750rpx;
	overflow: hidden;
}

.action {
  position: absolute;
  right: 12rpx;
  top: 50%;
  transform: translateY(-50%);
}

.title-box {
  height: 90rpx;
}

.cu-item {
  // padding: 0;
  // display: block;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: none;
  overflow: hidden;
  flex-direction: column;
  line-height: normal;

  view {
    width: 100%;
  }

  .nav-name {
    text-align: center;
  }

  .title {
    font-size: 28rpx;
    text-align: center;
  }

  .subtitle {
    font-size: 24rpx;
  }

  .imgBox {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
  }

  .blockImg {
    display: block;
    height: 90rpx !important;
    // image{
    // 	display: block;

    // 	max-height: 100%;
    // 	max-width: 100%;
    // }
  }

  .singleStyle {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// .title-item-box {
// 	width: ;
// }

.tit-amplification {
  // transform: scale(1.2);
}

.scroll-item-box {
  min-height: 80vh;
  // height: ;
}

.fixedBox {
  position: fixed;
  left: 0;
  right: 0;
  top: 60px;
  z-index: 9999;
}

.paddingTop {
  padding-top: 60rpx;
}
</style>
