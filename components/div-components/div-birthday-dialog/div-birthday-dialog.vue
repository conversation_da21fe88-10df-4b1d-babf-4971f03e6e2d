<template>
	<!-- 单图显示组件 -->
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''">
		<view class="cu-dialog" style="background-color: transparent;">
			<view style="position: relative;">
				<image :src="$imgUrl('live/birthday-dialog/birthday_top.png')"
					style="width: 100%; display: block;" mode="widthFix"></image>
				<view class="font-style" style="position: absolute;bottom: 4rpx; left: 76rpx;">亲爱的{{userinfo.erpCustTypename?userinfo.erpCustTypename:''}}</view>
			</view>
			<view style="position: relative;">
				<image :src="$imgUrl('live/birthday-dialog/birthday_bottom.png')"
					style="width: 100%;display: block;" mode="widthFix"></image>
				<view style="position: absolute; top:2rpx;left: 94rpx;" class="font-style">祝您生日快乐!
				 <text style="font-size: 50rpx;color: red;font-weight: bold;padding-left: 10rpx;">{{value.totalAmount}}元生日礼包</text>
				</view>	
				<view class="font-style" style="position: absolute; top:90rpx;left: 66rpx;">
					已经放入您的账户，感谢您对松鼠美淘的信任
				</view>
				<view  class="font-style" style="position: absolute; top:150rpx;left: 80rpx;">
					愿您健康快乐，生活幸福
				</view>
			</view>

			<view :style="{ width: `77rpx`,display: 'flex',justifyContent:' flex-end',marginTop:'120rpx',marginBottom:'20rpx',position:'relative',transform: 'translateX(-50%)',
					     	left: '50%'} ">
				<image style="width: 30px; height: 30px; "
					:src="$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')"
					fit="cover" @tap.stop="handleClose" />
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		name: 'div-birthday-dialog',
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {
						level: '',
						gift: ''
					}
				}
			}
		},
		components: {},

		data() {
			return {
				showModal: true,
				cardCur: 0,
				userinfo:''
			};
		},
		mounted() {
		 // console.log(this.value,'birthdayData')	
		 // console.log(uni.getStorageSync('user_info'),'uni.getStorageInfoSync')
		 this.userinfo = uni.getStorageSync('user_info')
		 console.log(this.userinfo,'userinfo')
		},
		methods: {
			handleClose() {
				this.showModal = false;
				this.$emit('close');
				uni.$emit('showBottomBar')
			},
		}
	}
</script>

<style scoped lang="scss">
	.font-style {
		font-size: 29rpx;
		font-weight: 400;
		color: #64241A;
	}
</style>
