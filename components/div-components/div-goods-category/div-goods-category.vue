<template>
	<!-- 商品分类组件 -->
	<view
		:style="{
			minHeight:  20*multipleView+'rpx',
			backgroundSize: 'cover',
			backgroundImage: `url(${newData.bgImage})`,
			backgroundColor: newData.background,
			marginBottom: `${isStatusBar ? 0 : newData.marginBottomSpacing * multipleView}rpx`
		}"
	>
		<view
			:class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''"
			:style="{
				display: 'flex',
				justifyContent: 'space-between',
				paddingBottom: `${isStatusBar ? 0 : newData.paddingBottomSpacing * multipleView}rpx`,
				paddingTop: `${isStatusBar ? 0 : newData.paddingTopSpacing * multipleView}rpx`,
				paddingLeft: `${newData.paddingLeftSpacing * multipleView}rpx`,
				paddingRight: `${newData.paddingRightSpacing * multipleView}rpx`
			}"
		>
			<view
				class="category-container"
				:style="{
					flex: '1',
					height: computedHeight
				}"
			>
				<template v-if="newData.navButtons && newData.navButtons.length > 0">
					<view
						v-for="(item, index) in newData.navButtons"
						:key="index"
						:data-index="index"
						:style="'margin: 0 ' + (windowWidth < smallWidth && isStatusBar ? 10 : item.itemSpace * multipleView ) + 'rpx; padding: 0'"
						@click="tabSelect"
					>
						<view
							:style="{
								backgroundImage: `url(${item.imageUrl})`,
								backgroundPosition: 'center',
								backgroundSize: 'contain',
								backgroundRepeat: 'no-repeat',
								height: '100%',
								width: Number(item.itemWidth) * multipleView + 'rpx',
								margin: 0,
								padding: 0,
								display: 'flex',
								justifyContent: 'center',
								alignItems: 'center'
							}"
						>
							<!-- item.navName 中间有; 说明需要轮播展示 -->
							<view
								v-if="item.navName && item.navName.indexOf(';') > -1"
								class="height100"
								:style="{
									display: 'flex',
									alignItems: 'center',
									color: newData.textColor,
									borderBottom: newData.borderBottom == 'none' ? '' : '2rpx'
								}"
							>
								<swiper
									vertical
									autoplay
									circular
									:interval="3000"
									class="height100"
									:style="{
										width: Number(item.itemWidth) * multipleView + 'rpx'
									}"
								>
									<template v-for="(txt, index) in swipeTxtst(item.navName)">
										<swiper-item v-if="txt" :key="index" @click.stop="handleSwiper(item, txt)">
											<view
												:style="{
													lineHeight: computedHeight
												}"
												style="font-size: 24rpx; color: #3f3e3e; margin-left: 64rpx; text-transform: uppercase"
											>
												{{ txt }}
											</view>
										</swiper-item>
									</template>
								</swiper>
							</view>

							<view
								v-else-if="item.navName"
								:class="index == TabCur && !item.imageUrl ? 'text-bold  text-lg' : ''"
								:style="
									'display:flex; align-items:center;color:' +
									newData.textColor +
									';font-size:' +
									newData.fontSize * multipleView +
									'rpx;border-bottom:' +
									(newData.borderBottom == 'none' ? '' : '2rpx') +
									';'
								"
							>
								{{ item.navName }}
							</view>
						</view>
					</view>
				</template>
			</view>
			<view v-if="newData.fixedRight == 1">
				<div-base-navigator :pageUrl="newData.rightPageUrl" hover-class="none">
					<view
					    class="padding-right padding-left-xs"
						:style="
							'height:' +
							computedHeight +
							';display:flex;align-items:center;color:' +
							newData.textColor +
							';font-size:' +
							(newData.fontSize * multipleView ) +
							'rpx;'
						"
					>
						<!-- box-shadow:-2px 0 3px -1px #888888 -->
						<image
							v-if="newData.rightImageUrl"
							:src="newData.rightImageUrl"
							mode="aspectFit"
							:style="{
								width: isPhone ? '50rpx' : '30rpx'
							}"
						/>
						{{ newData.rightLabel }}
					</view>
				</div-base-navigator>
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp();
import api from '@/utils/api';
import { pageUrls, gotoPage } from '../div-base/div-page-urls.js';
import divBaseNavigator from '../div-base/div-base-navigator.vue';
import { EventBus } from '@/utils/eventBus.js';
import { mapState } from 'vuex';
export default {
	components: {
		divBaseNavigator
	},
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					background: ``
				};
			}
		},
		isStatusBar: {
			type: Boolean,
			default: false
		},
		divType: {
			type: String,
			default: 'home' //home 平台首页  micro 微页面
		},
		//设置对象方便更新
		pageSelectPoi: {
			type: Object,
			default: () => ({})
		}
	},

	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: {},
			TabCur: 0,
			scrollLeft: 0
		};
	},
	computed: {
		...mapState(['isPhone', 'HeightBar', 'CustomBar', 'windowWidth', 'menuWidth', 'leftMenuWidth', 'smallWidth', 'multipleView']),
		computedHeight() {
			if (this.isStatusBar) {
				return this.HeightBar + 'px';
			} else {
				return this.newData.height * this.multipleView + 'rpx';
			}
		},
		//正确写法
		swipeTxtst() {
			return function (date) {
				return date && date.split(';');
			};
		}
	},
	watch: {
		pageSelectPoi: {
			handler(newVal, oldVal) {
				if (newVal && newVal.poi > 0) {
					this.TabCur = newVal.poi;
					this.$nextTick(() => {
						this.dealPage(newVal.poi);
					});
				}
			},
			immediate: true,
			deep: true
		}
	},
	created() {
		this.newData = {
			...this.value
		};
		// 设置 后台配置的第几个分类
		if (this.newData && this.newData.selectPoi > -1) {
			this.TabCur = this.newData.selectPoi;
			this.dealPage(this.TabCur);
		}
	},
	methods: {
		getParamsFromURL(url) {
			if ('undefined' == url) {
				return {};
			}
			let paraString = url.substring(url.indexOf('?') + 1, url.length).split('&');
			let paraObj = {};
			for (let i = 0; i < paraString.length; i++) {
				let j = paraString[i];
				paraObj[j.substring(0, j.indexOf('='))] = j.substring(j.indexOf('=') + 1, j.length);
			}
			return paraObj;
		},

		tabSelect(e) {
			let TabCur = e.currentTarget.dataset.index;
			uni.setStorage({
				key: 'param-goods-category-index',
				data: TabCur - 1
			});
			this.dealPage(TabCur);
		},

		handleSwiper(item, key) {
			gotoPage(item.pageUrl + '?key=' + key);
		},

		dealPage(TabCur) {
			let that = this;
			if (that.newData && that.newData.navButtons && that.newData.navButtons[TabCur].isNewPage == 0) {
				that.$emit('getpage', {
					index: TabCur,
					microId: that.getParamsFromURL(that.newData.navButtons[TabCur].pageUrl).id
				});
				that.TabCur = TabCur;
			} else if (that.newData && that.newData.navButtons && that.newData.navButtons[TabCur].pageUrl) {
				gotoPage(that.newData.navButtons[TabCur].pageUrl);
			}
		}
	}
};
</script>

<style scoped lang="scss">
.cur {
	border-bottom: 2px solid;
}

.category-container {
	flex-wrap: nowrap;
	overflow-x: auto;
	position: relative;
	display: flex;
	white-space: nowrap;

	&::-webkit-scrollbar {
		display: none;
	}
}

.height100 {
	height: 100%;
}
</style>
