<template>
	<!-- 商品分类组件 -->
	<view class="div-category">
		<cu-custom v-if="isStatusBar" :bgColor="newData.background" :bgImage="newData.bgImage">
			<block slot="backText">返回</block>
			<block slot="marchContent">
				<category v-model="newData" @getpage="getpage" :isStatusBar="isStatusBar" :pageSelectPoi="pageSelectPoi"></category>
			</block>
		</cu-custom>

		<category v-else v-model="newData"  @getpage="getpage" :isStatusBar="isStatusBar"></category>
	</view>

</template>

<script>
	import category from './div-goods-category.vue';

	export default {
		components: {
			category
		},
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {
						background: ``,
					}
				}
			},
			isStatusBar: {
				type: Boolean,
				default: false
			},
			pageSelectPoi: {
				type:  Object,
				default:()=>({})  
			}
		},
		data() {
			return {
				newData: this.value,
			};
		},
		methods: {
			getpage(e){
				this.$emit("getpage",e)
			}
		}
	}
</script>

