<template>
	<!-- 恭喜您已成为分销员弹框组件 -->
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal?'show':''">
		<view class="cu-dialog" style="background-color: transparent;">
			<view style="position: relative;" @click="jumpPage">
				<image :src="$imgUrl('distribution/distribution-dialog-content.png')"
					style="width: 100%; display: block;" mode="widthFix"></image>
					<view :style="{ width: `77rpx`,display: 'flex',justifyContent:' flex-end',bottom:'20rpx',position:'absolute',transform: 'translateX(-50%)',
							     	left: '50%'} ">
						<image style="width: 30px; height: 30px; "
							:src="$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')"
							fit="cover" @tap.stop="handleClose" />
					</view>
			</view>
			
			
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		name: 'div-distribution-dialog',
		data() {
			return {
				showModal: true,
			};
		},
	    
		methods: {
			handleClose() {
				this.showModal = false;
				this.$emit('close');
			},
			jumpPage() {
				uni.navigateTo({
					url:"/pages/distribution/distribution-center/index"
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.font-style {
		font-size: 29rpx;
		font-weight: 400;
		color: #64241A;
	}
	
	.cu-modal{
		    background: rgba(0, 0, 0, 0.8);
	}
</style>
