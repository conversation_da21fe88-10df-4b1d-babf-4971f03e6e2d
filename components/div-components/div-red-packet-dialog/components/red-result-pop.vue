<template>
  <view class="cu-modal show modal-container">
    <view class="cu-dialog dialog-wrapper">
      <view class="relative result-top">
        <image
          class="result-image"
          :src="$imgUrl('live/home/<USER>')"
        />
        <view class="flex justify-end mr-xxxxl absolute close-wrapper">
          <view
            class="cuIcon-roundclose mr-xxxxxl close-icon"
            @tap.stop="handleClose"
          ></view>
        </view>

        <view class="host-area" @tap.stop="goMyPrizes"></view>
      </view>

      <scroll-view scroll-y="true" class="mlr-xxxxl prize-list">
        <view v-if="prizes && prizes.length > 0">
          <block v-for="(item, idx) in prizes" :key="idx">
            <view class="flex m-xxxs ptb-xxmd plr-xsm prize-item">
              <view class="size-200 radius-lg prize-image">
                <image class="size-200" :src="item.prizeImg" />
              </view>

              <view class="relative ml-sm">
                <view class="text-left text-bold overflow-2"
                  >奖品名称：{{ item.prizeName }}</view
                >
                <button class="cu-btn round use-btn" @click="goUse(item)">
                  去使用
                </button>
              </view>
            </view>
          </block>
        </view>

        <view
          v-else
          class="prize-list flex justify-center align-center flex-direction"
        >
          <image
            style="width: 284rpx; height: 114rpx"
            :src="$imgUrl('live/home/<USER>')"
            mode="scaleToFill"
          />
          <text class="font-xxxmd text-bold mt-md"
            >太遗憾了，没{{
              configInfo.isPop ? "抽中奖品" : "抽奖次数了"
            }}</text
          >
          <text class="font-md mt-sm">祝您大吉大利，请再接再厉！</text>
        </view>
      </scroll-view>

      <view class="text-center button-group">
        <view class="flex justify-around">
          <view
            v-if="remainChance > 0"
            class="text-right draw-btn draw-again-btn"
            @click="handleDrawAgain"
          >
            <text>剩余{{ remainChance }}次</text>
          </view>
          <view
            class="draw-btn order-btn"
            :class="remainChance > 0 ? 'draw-btn order-btn' : 'order-btnX'"
            @click="goPayOrder"
          ></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { finish } from "@/components/div-components/div-red-packet-dialog/api/redPacketApi.js";
import { myPrizeApi } from "@/pages/myPrize/api/myPrize";
import { navigateUtil } from "@/static/mixins/navigateUtil.js";
import { senTrack } from '@/public/js_sdk/sensors/utils.js'
const app = getApp();
export default {
  mixins: [navigateUtil],
  name: "redResultPop",
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {};
      },
    },
    // 活动配置信息，包含持续时间、保底奖品类型等 isPop是否弹出活动提醒 true 是 false 否
    configInfo: {
      type: Object,
      required: true,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      cardCur: 0,
      prizes: [],
      remainChance: 0, // 剩余抽奖次数
      pageDeviseUrl: "",
      // 神策埋点
      trackParams: {
        page_name: "首页",
        page_level: "一级",
        activity_id: "",
        activity_name: "",
        activity_type_first: "营销活动",
        activity_type_second: "首页红包雨",
      },
    };
  },

  // 组件的生命周期, 只能用vue的才行
  beforeCreate() {
    uni.hideTabBar();
    app.globalData.tabBarHide = app.globalData.tabBarHide + 1;
  },

  mounted() {
    this.configInfo.isPop ? this.getFinish() : this.getPrizeList();
  },

  destroyed() {
    app.isBottomTabBar();
  },

  methods: {
    /**
     * 获取奖品列表数据
     */
    getPrizeList() {
      const { actId } = this.value;
      // 我的页面跳转来不需要传，活动页跳转来传活动ID
      const params = {
        actType: "2", //活动类型（1:大转盘 2:红包雨）
        current: 1, //页码
        size: 999, //最多20条
      };
      if (actId) {
        params.actId = actId;
      }
      myPrizeApi(Object.assign({}, params)).then((res) => {
        let { records } = res.data || {};
        this.prizes = records;
        console.log("==myPrizeApi==", this.prizes);
      });
    },

    // 获取抽中奖品
    getFinish() {
      finish(this.value).then(({ data }) => {
        console.log("==获取抽中奖品==", data);
        
        const { winUserPrizes, remainChance, pageDeviseUrl, actId } = data || {};
        this.getPrizes(winUserPrizes, actId)
        this.prizes = winUserPrizes || [];
        this.remainChance = remainChance || 0;
        this.pageDeviseUrl = pageDeviseUrl || "";
      });
    },

    // 奖品结果埋点
    getPrizes(list,id) {
      // 遍历奖品列表，为每个奖品单独上传埋点
      if (Array.isArray(list) && list.length > 0) {
        list.forEach((prize) => {
          const { prizeType, couponNo, prizeValue, relatedActivityId } = prize;
          let prizeId = "";
          // 根据奖品类型选择对应的ID字段
          switch (+prizeType) {
            case 2:
              prizeId = relatedActivityId || "";
              break;
            case 3:
              prizeId = prizeValue || "";
              break;
            case 6:
              prizeId = couponNo || "";
              break;
            default:
              prizeId = prize.id || prize.prizeId || "";
          }
          senTrack("ActivityResult", {
            ...this.trackParams,
            activity_name: "首页红包雨",
            is_obtain_prize: true,
            activity_id: id,
            prize_name: prize.prizeName || "",
            prize_id: prizeId,
          });
        });
      }
    },

    handleClose() {
      this.$emit("close");
      this.$emit("showTransition", -1);
    },

    goMyPrizes() {
      this.$emit("showTransition", -1);
      // 跳转到我的奖品页面
      uni.navigateTo({
        url: "/pages/myPrize/index",
      });
    },

    goPayOrder() {
      this.$emit("showTransition", -1);
      this.$emit("close");
      if (this.pageDeviseUrl) {
        // 跳转到订单页面
        this.toPage(this.pageDeviseUrl);
      } else {
        this.toPage("/pages/home/<USER>");
      }
    },

    handleDrawAgain() {
      if (this.remainChance <= 0) {
        uni.showToast({
          title: "今日抽奖次数已用完",
          icon: "none",
        });
        return;
      }
      this.$emit("drawAgain", this.remainChance);
    },

    goUse(val) {
      const { prizeType, couponNo, prizeValue } = val;
      let url;
      switch (+prizeType) {
        case 3:
          url = `/pages/goods/goods-list/index?couponId=${prizeValue}`;
          break;
        case 4:
          url = `/pages/signrecord/signrecord-info/index`;
          break;
        case 6:
          url = `/pages/coupon/coupon-offline-detail-plus/index?id=${couponNo}`;
          break;
      }
      uni.navigateTo({
        url,
      });
      this.$emit("showTransition", -1);
    },
  },
};
</script>

<style scoped lang="scss">
.modal-container {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
}

.dialog-wrapper {
  background-color: transparent;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

.result-top {
  height: 472rpx;
}

.result-image {
  margin: 0 auto;
  display: inline-block;
  height: 100%;
  width: 100%;
}

.close-wrapper {
  top: 50rpx;
  right: 0;
}

.close-icon {
  margin: 0;
  font-size: 60rpx;
  color: #fff;
  line-height: 1;
  margin: 15rpx 0;
}

.prize-list {
  height: 544rpx;
  width: 670rpx;
  background: #f4f4f4;
  border-radius: 29rpx;
  overflow: hidden;
}

.prize-item {
  background: #ffffff;
  border-radius: 29rpx;
}

.prize-image {
  background: #e7e7e7;
}

.use-btn {
  width: 271rpx;
  height: 77rpx;
  position: absolute;
  left: 130rpx;
  bottom: 5rpx;
  color: #000000;
  background: #ffea00;
}

.button-group {
  margin-top: 48rpx;
}

/* 保留原有的按钮相关样式 */
.draw-btn {
  width: 340rpx;
  height: 137rpx;
  background-size: 100% auto;
  background-repeat: no-repeat;
}

.draw-again-btn {
  line-height: 137rpx;
  background-image: url(https://img.songlei.com/live/home/<USER>

  text {
    font-size: 22rpx;
    margin-right: 34rpx;
  }
}
.order-btn {
  background-image: url(https://img.songlei.com/live/home/<USER>
}

.order-btnX {
  width: 478rpx;
  height: 138rpx;
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-image: url(https://img.songlei.com/live/home/<USER>
}

.host-area {
  width: 200rpx;
  height: 137rpx;
  position: absolute;
  right: 0;
  bottom: 0;
}
</style>
