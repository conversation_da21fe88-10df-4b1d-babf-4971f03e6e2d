<template>
  <view
    class="cu-modal show"
    catchtouchmove="true"
    style="width: 100vw; height: 100vh;background: rgba(0, 0, 0, 0.8);"
  >
    <view
      class="cu-dialog"
      style="
        background-color: transparent;
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      "
    >
      <view
        class="text-center"
        :style="{
          backgroundSize: 'cover',
          backgroundImage: `url({{bgImageUrl}}))`,
          backgroundSize: '100% auto',
          backgroundRepeat: 'no-repeat',
          height: '623rpx',
          lineHeight: '623rpx',
          width: '100%',
        }"
      >
        <text
          class="countdown-text text-bold"
          :class="{ animate: animate }"
          :key="countdownArr[index] + '-' + index"
          @animationend="handleAnimationEnd"
          style="color: #ffe06d; display: inline-block"
        >
          {{ countdownArr[index] }}
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import { senTrack, getCurrentTitle } from '@/public/js_sdk/sensors/utils.js'
export default {
  name: "transitionPop",
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      newData: this.value,
      countdownArr: ["3", "2", "1", "GO"],
      index: 0,
      animate: true,
      bgImageUrl: this.$imgUrl('live/home/<USER>'),
      // 神策埋点
      trackParams: {
        page_name: "首页",
        page_level: "一级",
        activity_id: "",
        activity_name: "",
        activity_type_first: "营销活动",
        activity_type_second: "首页红包雨",
      },
    };
  },

  mounted() {
    this.index = 0;
    this.animate = true; // 触发第一次动画
    senTrack("ActivityPageView", {
			...this.trackParams,
			forward_source: getCurrentTitle(0),
			activity_id: this.newData.redPacketRainInfo.id,
      activity_name: this.newData.redPacketRainInfo.name,
		});
  },

  methods: {
    handleAnimationEnd() {
      console.log('animation ended, index:', this.index);
      this.index++;
      if (this.index >= this.countdownArr.length) {
        this.animate = false;
        this.$emit("showTransition", 2);
      } else {
        this.animate = false;
        this.$nextTick(() => {
          setTimeout(() => {
            this.animate = true;
          }, 50);
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.countdown-text {
  font-size: 240rpx;
  font-weight: bold;
  color: #ffe06d;
  opacity: 1;
  transform: scale(1);
  display: inline-block;
}

.countdown-text.animate {
  animation: zoomFade 0.8s forwards;
}

@keyframes zoomFade {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}
</style>
