<template>
  <view
    class="cu-modal show"
    catchtouchmove="true"
    style="width: 100vw; height: 100vh;background: rgba(0, 0, 0, 0.8);"
  >
    <view
      class="cu-dialog"
      style="
        background-color: transparent;
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
      "
    >
      <view
        class="red-packet-background"
        :style="{
          backgroundImage: `url(${dynamicBgImage})`,
        }"
      >
        <!-- 关闭 -->
        <view class="flex justify-end mr-xxxxl">
          <view
            class="cuIcon-roundclose mr-xxxxxl"
            style="
              margin: 0rpx;
              font-size: 60rpx;
              color: #fff;
              line-height: 1;
              margin: 15rpx 0;
            "
            @tap.stop="handleClose"
          >
          </view>
        </view>
        <!-- 参与次数 -->
        <view class="flex justify-end">
          <view
            class="height-70 lh-70 mt-54 text-left font-60 text-bold"
            style="width: 260rpx; color: red"
          >
            {{ newData.dayDrawNum }}
          </view>
        </view>
        
        <!-- 中间图片 -->
        <view class="relative">
          <!-- 红包图片 -->
          <image 
            mode="widthFix" 
            style="
              width: 504rpx;
              height: 433rpx;
              position: absolute;
              top: -10rpx;
              right: 95rpx;
            " 
            :src="iconPic.redPacket" />
          <!--商品图片  -->
          <image 
            mode="widthFix" 
            style="
              width: 736rpx;
              height: 339rpx;
              position: absolute;
              top: 230rpx;
              left: 15rpx;
            " 
            :src="iconPic.goods" />

          <!-- 立即参与 -->
          <image 
            mode="widthFix" 
            style="
              width: 521rpx;
              height: 233rpx;
              position: absolute;
              top: 570rpx;
              left: 145rpx;
            " 
            :src="iconPic.btn" 
            @click="handleMeetingplace"
            />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { senTrack } from '@/public/js_sdk/sensors/utils.js'
export default {
  name: "redPop",
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      cardCur: 0,
      iconPic:{
        redPacket:this.$imgUrl('live/home/<USER>'),
        goods:this.$imgUrl('live/home/<USER>'),
        btn:this.$imgUrl('live/home/<USER>')
      },
      // 神策埋点
      trackParams: {
        page_name: "首页",
        page_level: "一级",
        activity_id: "",
        activity_name: "",
        activity_type_first: "营销活动",
        activity_type_second: "首页红包雨",
      }
    };
  },
  // 组件的生命周期, 只能用vue的才行
  beforeCreate() {
    app.globalData.tabBarHide = app.globalData.tabBarHide + 1;
    uni.hideTabBar();
  },

  destroyed() {
    app.globalData.tabBarHide = app.globalData.tabBarHide - 1;
  },

  computed: {
    dynamicBgImage() {
      const { popImg } = this.newData?.redPacketRainInfo || {};
      return popImg
        ? popImg
        : this.$imgUrl('live/home/<USER>');
    },

    prizes() {
      const { redPacketRainPrizes: prizes } = this.newData || {};
      return prizes ? prizes : [];
    },
  },

  mounted() {
    senTrack("ActivityPageView", {
      page_name: "首页",
      forward_source: "首页",
      page_level: '一级',
      activity_id: this.newData.redPacketRainInfo.id,
      activity_name:this.newData.redPacketRainInfo.name,
      activity_type_first: "营销活动",
      activity_type_second: "首页红包雨",
    });
  },

  methods: {
    handleClose() {
      app.isBottomTabBar();
      this.$emit('close');
      this.$emit("showTransition", -1);
    },

    // 去抽奖
    handleMeetingplace() {
      senTrack("ActivityClick", {
        ...this.trackParams,
        activity_id: this.newData.redPacketRainInfo.id,
        activity_name:this.newData.redPacketRainInfo.name
      });
      this.$emit("showTransition", 1);
    },
  },
};
</script>

<style scoped lang="scss">
.red-packet-background {
  margin: 0 auto;
  padding-top: 40rpx;
  background-size: 100% auto; /* 宽度100%，高度自动 */
  background-repeat: no-repeat;
  height: 1056rpx;
  width: 100%;
}
</style>
