<template>
  <view 
    class="cu-modal show" 
    catchtouchmove="true" 
    style="width: 100vw; height: 100vh;background: rgba(0, 0, 0, 0.8);"
  >
    <view 
      class="cu-dialog"
      style="
        background-color: transparent;
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
      "
    >
      <view class="RedpacketRain-page">
        <!-- 活动进行中显示红包雨 -->
        <view v-show="isActivity">
          <view class="r-title">
            <!-- 标题图片 -->
            <image class="r-img" :src="configInfo.topImg || iconPic.title" />
            <!-- 已抢红包数量 -->
            <view class="r-packets">已抢红包：{{ clickedPackets.length }}</view>
            <!-- 倒计时 -->
            <view class="r-countdown">倒计时：{{ countdown }}</view>
          </view>

          <view class="r-wrapper">
            <!-- 红包容器，控制红包整体位置 -->
            <view
              class="r-content"
              :style="{
                top: `${rcTop}rpx`,
                left: `${rcLeft}rpx`,
                transition: `all ${totalTime}s linear`,
              }"
            >
              <!-- 遍历红包数组，渲染每个红包 -->
              <view
                class="r-item"
                v-for="(packet, index) in redPackets"
                :key="packet.id"
                :style="{ 
                  left: `${packet.x}rpx`, 
                  top: `${packet.y}rpx` ,
                }"
              >
                <!-- 红包打开后显示的文字 -->
                <view class="packet-text" v-if="packet.isAnimating">
                  <text v-if="packet.prizeType === '积分' && packet.type === 3">{{ packet.point }}积分</text>
                  <text v-else-if="packet.prizeType === '空的' && packet.type === 2">空的 ～</text>
                  <text v-else>{{ packet.prizeType }}</text>
                </view>

                <!-- 未打开红包显示封面 -->
                <image
                  v-show="!packet.isAnimating"
                  :src="configInfo.unclaimedStyleImage || iconPic.packet"
                  class="r-packet"
                  @click="handlePacketClick(packet, index)"
                />

                <!-- 打开红包动画图片 -->
                <image
                  v-show="packet.isAnimating"
                  :src="configInfo.claimedStyleImage || iconPic.openPacket"
                  :class="{ 'opening-packet': packet.isAnimating }"
                  class="r-packet"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'redPacketRain',

  props: {
    // 活动配置信息，包含持续时间、保底奖品类型等
    configInfo: {
      type: Object,
      required: true,
      default() {
        return {};
      },
    },

    // 奖品池信息，数组形式
    prizeInfo: {
      type: Array,
      required: true,
      default() {
        return [];
      },
    },
  },

  data() {
    return {
      speed: 1.8, // 红包下落速度系数
      isActivity: false, // 活动是否进行中
      redPackets: [], // 红包数组，存储所有红包信息
      pId: 0, // 红包唯一id计数器
      rcTop: 0, // 红包雨容器垂直位置（rpx）
      rcLeft: 0, // 红包雨容器水平位置（rpx）
      totalTime: this.configInfo.gameDurationSeconds || 10, // 活动持续时间，单位秒
      iconPic: {
        title: 'http://img.songlei.com/live/home/<USER>', // 标题图片
        packet: "http://img.songlei.com/paradise/redpackage/red-packet.png", // 未领取红包图片
        openPacket: "http://img.songlei.com/paradise/redpackage/oprn-redpacket.png", // 已领取红包图片
      },
      clickedPackets: [], // 存储已点击的红包信息
      screenWidth: 667, // 设计稿宽度，单位rpx
      countdown: 0, // 倒计时秒数
    };
  },

  mounted() {
    // 组件挂载后启动红包雨
    this.start();
  },

  methods: {
    // 关闭红包雨弹窗，通知父组件
    handleClose() {
      this.$emit('showTransition', 3);
    },

    /**
     * 活动开始，生成红包数组，初始化红包位置和奖品分布
     */
    async start() {
      this.isActivity = true; // 标记活动开始

      const packetWidth = 136; // 红包宽度，单位rpx
      const rowNum = 3; // 每行红包数（列数）
      // 计算红包总数 = 持续时间 * 速度 * 列数
      const totalPackets = Math.floor(this.totalTime * this.speed * rowNum);
      const prizeCount = this.prizeInfo.length; // 奖品池中奖品数量

      // 1. 生成包含所有奖品和保底奖品的数组
      const allPrizes = [];

      // 1.1 将奖品池中的奖品加入数组，类型标记为1
      for (let i = 0; i < prizeCount; i++) {
        const prizeItem = this.prizeInfo[i];
        allPrizes.push({
          prizeType: prizeItem.name, // 奖品名称
          prize: prizeItem.prizeDistributionId, // 奖品ID
          type: 1, // 1表示奖品红包
        });
      }

      // 1.2 剩余红包用保底奖品填充，保证红包总数
      for (let i = prizeCount; i < totalPackets; i++) {
        if (this.configInfo.guaranteedPrizeType === 0) {
          // 保底类型为“空的”
          allPrizes.push({
            prizeType: '空的',
            type: 2, // 2表示空包
            prize: 0,
            point: 0,
          });
        } else if (this.configInfo.guaranteedPrizeType === 1) {
          // 保底类型为“积分”，随机生成积分数值
          const min = this.configInfo.guaranteedPrizeMin || 1;
          const max = this.configInfo.guaranteedPrizeMax || 10;
          const point = Math.floor(Math.random() * (max - min + 1)) + min;
          allPrizes.push({
            prizeType: '积分',
            type: 3, // 3表示积分红包
            prize: 0,
            point,
          });
        }
      }

      // 2. 打乱 allPrizes 数组顺序，确保奖品随机分布
      for (let i = allPrizes.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [allPrizes[i], allPrizes[j]] = [allPrizes[j], allPrizes[i]];
      }

      // 3. 初始化每列的当前高度数组，初始都为0
      const columnHeights = new Array(rowNum).fill(0);

      // 4. 计算每列宽度区间，保证红包不会超出列宽
      const section = this.screenWidth / rowNum;

      // 5. 生成红包数组，模拟瀑布流布局
      this.redPackets = []; // 清空红包数组
      for (let i = 0; i < totalPackets; i++) {
        // 5.1 选择当前高度最小的列，优先放置红包
        let minColIndex = 0;
        for (let c = 1; c < rowNum; c++) {
          if (columnHeights[c] < columnHeights[minColIndex]) {
            minColIndex = c;
          }
        }

        // 5.2 计算横向随机位置，保证红包不会超出当前列范围
        const base = minColIndex * section;
        const margin = 10; // rpx边距 增加左右边距，避免红包紧贴列边界
        const xPosition = base + margin + Math.random() * (section - packetWidth - 2 * margin);

        // 5.3 计算纵向位置，当前列高度 + 随机偏移，增加落差
        const verticalOffset = 50 + Math.random() * 100; // 50~150rpx随机偏移
        const yPosition = columnHeights[minColIndex] + verticalOffset;

        // 5.4 获取当前红包对应奖品信息
        const prizeObj = allPrizes[i];

        // 5.5 将红包信息加入红包数组
        this.redPackets.push({
          x: xPosition,
          y: yPosition,
          id: this.pId++,
          type: prizeObj.type,
          prizeType: prizeObj.prizeType,
          prize: prizeObj.prize,
          point: prizeObj.point || 0,
          isAnimating: false, // 是否正在打开动画
        });

        // 5.6 更新该列高度，累加红包高度 + 垂直间距
        // 假设红包高度约为 194rpx，间距约为 50rpx，可根据实际调整
        columnHeights[minColIndex] = yPosition + 250;
      }

      // 6. 计算红包雨初始垂直位置，取所有列最高点的最大值
      const maxHeight = Math.max(...columnHeights);
      this.rcTop = 0 - maxHeight; // 初始位置在屏幕上方外
      this.rcLeft = 0;

      // 7. 启动倒计时
      this.handleCountdown();

      // 8. 触发红包雨下落动画，延迟100ms让页面渲染完成
      setTimeout(() => {
        this.rcTop = 0;
        this.rcLeft = 0;
      }, 100);

      console.log("===红包数组===", this.redPackets);
    },

    /**
     * 启动倒计时，每秒递减，倒计时结束触发红包雨结束
     */
    handleCountdown() {
      this.countdown = this.totalTime; // 初始化倒计时
      const intervalId = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown -= 1;
        } else {
          clearInterval(intervalId); // 清除定时器
          this.handleEnd(); // 触发红包雨结束逻辑
        }
      }, 1000);
    },

    /**
     * 红包雨结束处理，统计已点击红包的积分和奖品，通知父组件
     */
    async handleEnd() {
      try {
        this.isActivity = false; // 标记活动结束
        this.redPackets = []; // 清空红包数组
        this.pId = 0; // 重置红包id计数器

        // 过滤已点击红包中的积分和奖品
        let bonusPoint = [];
        let stageProps = [];
        if (this.clickedPackets && this.clickedPackets.length) {
          bonusPoint = this.clickedPackets.filter(p => p.prizeType === '积分' && p.type === 3);
          stageProps = this.clickedPackets.filter(p => p.type === 1 && p.prize !== 0);
        }

        // 计算总积分
        const points = bonusPoint.length ? bonusPoint.reduce((total, item) => total + item.point, 0) : 0;

        // 奖品ID数组
        const prizeDistributionIds = stageProps.map(item => item.prize);

        // 关闭红包雨弹窗
        this.handleClose();

        // 通知父组件红包雨结束，传递积分和奖品ID
        this.$emit("endCountdown", {
          point: points || 0,
          prizeDistributionIds: prizeDistributionIds || [],
          actId: this.configInfo.id,
        });

        console.log("points", points, "prizeDistributionIds", prizeDistributionIds);

      } catch (error) {
        console.error(error);
      }
    },

    /**
     * 点击红包事件，触发红包打开动画，记录已点击红包
     * @param {Object} packet 当前红包对象
     * @param {Number} index 当前红包索引
     */
    handlePacketClick(packet, index) {
      // 设置红包为打开动画状态
      this.$set(this.redPackets, index, { ...packet, isAnimating: true });

      // 非空红包才记录
      if (packet.prizeType !== '空的') {
        if (!this.clickedPackets.length) {
          // clickedPackets为空，直接添加
          this.clickedPackets.push({ ...packet });
        } else {
          // 查找是否已存在该红包
          const existingIndex = this.clickedPackets.findIndex(p => p.id === packet.id);
          if (existingIndex !== -1) {
            // 已存在则替换
            this.clickedPackets.splice(existingIndex, 1, { ...packet });
          } else {
            // 不存在则添加
            this.clickedPackets.push({ ...packet });
          }
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.RedpacketRain-page {
  min-height: 100vh;

  .r-title {
    width: 667rpx;
    height: 357rpx;
    position: absolute;
    top: 160rpx;
    left: 50%;
    transform: translateX(-50%);
    .r-img {
      width: 667rpx;
      height: 357rpx;
    }
    .r-countdown {
      position: absolute;
      bottom: 60rpx;
      right: 68rpx;
      color: #9F4227;
      font-size: 30rpx;
    }
    .r-packets {
      position: absolute;
      bottom: 60rpx;
      left: 68rpx;
      color: #9F4227;
      font-size: 30rpx;
    }
  }

  .r-wrapper {
    position: fixed;
    top: 521rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 999;
    width: 667rpx;
    min-height: 100vh;
    overflow: hidden;
  }

  .r-content {
    position: relative;
  }

  .r-item {
    position: absolute;
    width: 136rpx;
    height: 194rpx;

    .r-packet {
      width: 136rpx;
      height: 194rpx;
    }

    .packet-text {
      width: 200rpx;
      text-align: center;
      position: absolute;
      animation: packet-fade-up 0.5s forwards;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      color: #ffd816;
      font-size: 40rpx;
      font-weight: 600;
      z-index: 9999;
    }
  }
}

/* 红包文字上升动画 */
@keyframes packet-fade-up {
  0% {
    top: 100rpx;
  }
  5% {
    top: 60rpx;
  }
  100% {
    top: -20rpx;
  }
}

/* 红包打开动画 */
@keyframes openPacket {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.5);
    opacity: 0.5;
  }
}

.opening-packet {
  animation: openPacket 0.3s forwards;
}
</style>
