<template>
  <view>
    <!-- 红包弹窗第一部 -->
    <red-pop
      v-if="showPage === 0"
      v-model="newData"
      @showTransition="showPage = $event"
      @close="handleClose()"
    ></red-pop>
    <!-- 倒计时过渡页 -->
    <transition-pop
      v-else-if="showPage === 1"
      v-model="newData"
      @showTransition="showPage = $event"
    ></transition-pop>
    <!--下红包雨  -->
    <red-packet-rain
      v-else-if="showPage === 2"
			:configInfo="newData.redPacketRainInfo"
      :prizeInfo="prizeData"
			@endCountdown="lotteries = $event" 
      @showTransition="showPage = $event"
    ></red-packet-rain>
    <!-- 奖品结果 -->
    <red-result-pop
      v-else-if="showPage === 3"
			v-model="lotteries"
      :configInfo="newData"
      @showTransition="showPage = $event"
      @drawAgain="drawAgain"
      @close="handleClose()"
    ></red-result-pop>
  </view>
</template>

<script>
import redPop from "./components/red-pop.vue";
import transitionPop from "./components/transition-pop.vue";
import redPacketRain from "./components/red-packet-rain.vue";
import redResultPop from "./components/red-result-pop.vue";
import { getlotteries} from "./api/redPacketApi";
import { senTrack } from '@/public/js_sdk/sensors/utils.js'

export default {
  name: "div-red-packet-dialog",
  components: {
    redPop,
    transitionPop,
    redPacketRain,
    redResultPop,
  },
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {
          // redPacketRainInfo:{
          //   id:'1924758571305533442'
          // }
        };
      },
    },
  },
  data() {
    return {
      newData: this.value,
      showModal: false,
      showPage: null, //
			lotteries:{},//抽中的奖品id或者积分
			prizeData:[],//预拆奖品
      // 神策埋点
      trackParams: {
        page_name: "首页",
        forward_source: "首页",
        page_level: '一级',
        activity_id: "",
        activity_name: "",
        activity_type_first: "营销活动",
        activity_type_second: "首页红包雨",
      },
      enterTime: 0,  // 进入页面时间戳（毫秒）
    };
  },

  watch:{
    showPage: {
      handler(val, oldVal) {
        // 倒计时过渡页
        if (val === 1) {
          console.log("====1111111===");
          
          // 获取预中奖信息
          this.getRizeInfo();
        }
      },
      immediate: true,
    },
    value: {
      handler(val, oldVal) {
        console.log("====111---val---1111===",val);
        // 有次数就抽奖
        if (val.isPop) {
          this.showPage = 0
        }else{
          // 就展示结果页
          this.showPage = 3
        }
      },
      immediate: true,
    },
  },

  mounted() {
    // 页面显示时记录进入时间（单位：毫秒）
    this.enterTime = new Date().getTime();
    console.log('组件 mounted，进入时间enterTime:', this.enterTime);
  },

	methods: {
    // 关闭弹窗
    handleClose(){
      const leaveTime = new Date().getTime()
			// 组件销毁前记录离开时间
			const durationSeconds = Math.floor((leaveTime - this.enterTime) / 1000);
      console.log("进入时间",this.enterTime);
      console.log("离开时间",leaveTime);
      
			console.log("组件生命周期内停留时长（秒）：", durationSeconds);
      senTrack("ActivityPageLeave", {
        ...this.trackParams,
        activity_id: this.newData.redPacketRainInfo.id,
        activity_name: this.newData.redPacketRainInfo.name,
        stay_duration: durationSeconds,
      });
      this.$emit('close')
    },

    // 获取预中奖信息
		async	getRizeInfo(){
			try {
				const { data } = await getlotteries({id:this.newData.redPacketRainInfo.id})
				this.prizeData = data
        console.log("===prizeData===",this.prizeData);
        
			} catch (error) {
				console.log("error",error);
			}
		},

    drawAgain(val){
      this.newData.dayDrawNum = val
      this.showPage = 0
    },
	}
};
</script>

<style scoped lang="scss"></style>
