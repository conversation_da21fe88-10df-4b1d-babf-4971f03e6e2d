<template>
	<view class="pop-component">
		<template v-if="pageAdvertDialog && pageAdvertDialog.showPop == 1">
			<!-- <div-base-navigator v-for="(item, index) in pageAdvertDialog.navButtons" v-bind:key="index"
				:pageUrl="item.pageUrl"> -->
			<block v-for="(item, index) in pageAdvertDialog.navButtons">
				<image @click="navTo(item.pageUrl)" :src="item.imageUrl" :style="{
						position: 'fixed',
						width: ((isPhone ? item.width * 2 : item.width * 1.2) || 160) + 'rpx',
						height: ((isPhone ? item.height * 2 : item.height * 1.2) || 160) + 'rpx',
						right: item.rightSpacing > 0 ? Number(item.rightSpacing) + 'px' : '',
						top: item.topSpacing > 0 ? Number(item.topSpacing) + CustomBar + 'px' : '',
						bottom: item.bottomSpacing > 0 ? (isPhone ? Number(item.bottomSpacing) * 2 : Number(item.bottomSpacing) * 1.3) + 'rpx' : ''
					}" />
			</block>
			<!-- </div-base-navigator> -->
		</template>

		<!-- 直播 -->
		<!-- #ifdef MP -->
		<template v-if="isLiving && pageAdvertDialog && pageAdvertDialog.live && pageAdvertDialog.live.show == 1">
			<div-base-navigator :pageUrl="cusLivingPageUrl">
				<image :src="pageAdvertDialog.live.imageUrl" :style="{
						position: 'fixed',
						width: (pageAdvertDialog.live.width  * multipleView) + 'rpx',
						height: (pageAdvertDialog.live.height  * multipleView) + 'rpx',
						right: pageAdvertDialog.live.rightSpacing > 0 ? Number(pageAdvertDialog.live.rightSpacing) + 'px' : '',
						top: pageAdvertDialog.live.topSpacing > 0 ? Number(pageAdvertDialog.live.topSpacing) + CustomBar + 'px' : '',
						bottom:
							pageAdvertDialog.live.bottomSpacing > 0
								? (isPhone ? Number(pageAdvertDialog.live.bottomSpacing) * 2 : Number(pageAdvertDialog.live.bottomSpacing) * 1.3) + 'rpx'
								: ''
					}" />
			</div-base-navigator>
		</template>
		<!-- #endif -->

		<!-- 客服 -->
		<template v-if="pageAdvertDialog && pageAdvertDialog.cusService && pageAdvertDialog.cusService.show == 1">
			<!-- <div-base-navigator :pageUrl="cusServicePageUrl"> -->
			<image @click="handleCusServiceClick" :src="pageAdvertDialog.cusService.imageUrl" :style="{
					position: 'fixed',
					width: ( pageAdvertDialog.cusService.width * multipleView ) + 'rpx',
					height: ( pageAdvertDialog.cusService.height * multipleView ) + 'rpx',
					right: pageAdvertDialog.cusService.rightSpacing > 0 ? Number(pageAdvertDialog.cusService.rightSpacing) + 'px' : '',
					top: pageAdvertDialog.cusService.topSpacing > 0 ? Number(pageAdvertDialog.cusService.topSpacing) + CustomBar + 'px' : '',
					bottom:
						pageAdvertDialog.cusService.bottomSpacing > 0
							? (isPhone ? Number(pageAdvertDialog.cusService.bottomSpacing) * 2 : Number(pageAdvertDialog.cusService.bottomSpacing) * 1.3) + 'rpx'
							: ''
				}" />
			<!-- </div-base-navigator> -->
		</template>


		<!-- 红包雨 -->
		<template v-if="pageAdvertDialog && pageAdvertDialog.redpackage && pageAdvertDialog.redpackage.show == 1">
			<image @click="handleRedpackageClick" :src="pageAdvertDialog.redpackage.imageUrl" :style="{
					position: 'fixed',
					width: ( pageAdvertDialog.redpackage.width * multipleView ) + 'rpx',
					height: ( pageAdvertDialog.redpackage.height * multipleView ) + 'rpx',
					right: pageAdvertDialog.redpackage.rightSpacing > 0 ? Number(pageAdvertDialog.redpackage.rightSpacing) + 'px' : '',
					top: pageAdvertDialog.redpackage.topSpacing > 0 ? Number(pageAdvertDialog.redpackage.topSpacing) + CustomBar + 'px' : '',
					bottom:
						pageAdvertDialog.redpackage.bottomSpacing > 0
							? (isPhone ? Number(pageAdvertDialog.redpackage.bottomSpacing) * 2 : Number(pageAdvertDialog.redpackage.bottomSpacing) * 1.3) + 'rpx'
							: ''
				}" />
		</template>


		<!-- 消息 -->
		<view v-if="route === 'pages/home/<USER>'" :style="{
					position: 'fixed',
					width: ( pageAdvertDialog.cusService.width * multipleView ) + 'rpx',
					height: ( pageAdvertDialog.cusService.height * multipleView ) + 'rpx',
					right: pageAdvertDialog.cusService.rightSpacing > 0 ? (Number(pageAdvertDialog.cusService.rightSpacing)+2) + 'px' : '',
					bottom: (isPhone ? 40 : 26) + 'rpx',
				}">
			<image @click="navTo('/pages/message/list/index')" src="https://img.songlei.com/live/home/<USER>"
				:style="{
						width: ( pageAdvertDialog.cusService.width * multipleView ) + 'rpx',
						height: ( pageAdvertDialog.cusService.height * multipleView ) + 'rpx',
					}" />
			<view v-if="meagessinfo.isShow == 1" class="hover-radios text-sm flex align-center justify-center">
				{{meagessinfo.total|| 0}}</view>
		</view>

		<uni-popup ref="perpopup" type="center" :mask-click='false'>
			<view class="permissions_box">
				当您使用APP时，拨打电话咨询商家时候需要拨打电话权限，不授权上述权限，不影响APP其他功能使用。
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import divBaseNavigator from '../div-base/div-base-navigator.vue';
	import {
		gotoPage
	} from '../div-base/div-page-urls.js';
	import {
		EventBus
	} from '@/utils/eventBus.js';
	import {
		getInfo
	} from '@/api/message.js';
	const app = getApp();
	import api from 'utils/api';
	import __config from '@/config/env';
	import {
		mapState
	} from 'vuex';

	export default {
		name: 'div-pop',
		props: {
			pageAdvertDialog: {
				type: Object | null,
				default: function() {
					return {};
				}
			}
		},
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				cusLivingPageUrl: '',
				cusServicePageUrl: '',
				isLiving: false,
				getDataTimes: 0,
				meagessinfo: {},
				pagesLength: 1,
				route: ''
			};
		},
		computed: {
			...mapState(['isPhone', 'multipleView'])
		},
		components: {
			divBaseNavigator
		},
		watch: {
			pageAdvertDialog: {
				handler(newObj, oldObj) {
					if (newObj && newObj.cusService && newObj.cusService.show) {
						console.log('首页绝对定位数据', newObj);
						this.getCustomerServiceInfo();
					}
					// if (newObj && newObj.live && newObj.live.show) {
					// 	this.getLiveInfo();
					// }
				},
				immediate: true
			}
		},
		created() {
			const page = getCurrentPages();
			this.pagesLength = page.length; //当前页面栈的个数
			this.route = page[page.length - 1].route;
			EventBus.$on('refreshMessage', async () => {
				this.getmeagessInfo();
			});
		},

		beforeDestroy() {
			// 或 beforeUnload 在小程序中
			console.log('beforeDestroy'); // 或 beforeUnload 在小程序中
			EventBus.$off('refreshMessage');
		},

		methods: {
			handleCusServiceClick() {
				let that = this;
				// #ifdef MP
				wx.openCustomerServiceChat({
					extInfo: {
						url: this.cusServicePageUrl
					},
					corpId: __config.chatId,
					success(res) {}
				});
				// #endif
				// #ifdef APP
				uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					openCustomerServiceChat: true,
					corpid: __config.chatId,
					customerUrl: this.cusServicePageUrl,
					fail(err) {
						console.log('打开客服错误', err);
						// that.handleCall();
					}
				});
				// #endif
			},

			handleCall() {
				// #ifdef MP-WEIXIN
				this.callFun();
				// #endif 
				// #ifdef APP-PLUS
				let that = this;
				var platform = uni.getSystemInfoSync().platform;
				if (platform == 'android') {
					console.error("=====**********======");
					plus.android.checkPermission(
						'android.permission.CALL_PHONE',
						granted => {
							cosole.error("=====22222222======");
							if (granted.checkResult == -1) {
								//弹出
								that.$refs.perpopup.open('top')
							} else {
								//执行你有权限后的方法
								that.callFun();
							}
						},
						error => {
							console.error('Error checking permission:', error.message);
						}
					);
					plus.android.requestPermissions(['android.permission.CALL_PHONE'],
						(e) => {
							//关闭
							that.$refs.perpopup.close()
							if (e.granted.length > 0) {
								//执行你有权限后的方法
								that.callFun();
							}
						})

				} else {
					//执行你有权限后的方法 ios
					that.callFun();
				}
				// #endif 
			},
			callFun() {
				uni.makePhoneCall({
					phoneNumber: '4006171819'
				})
			},

			navTo(url) {
				if (app.isLogin(true)) {
					if (url.startsWith('customUrl:')) {
						gotoPage(url, this.pagesLength);
					} else {
						uni.navigateTo({
							url
						});
					}
				} else {
					uni.reLaunch({
						url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
					});
				}
			},
			// getLiveInfo() {
			// 	let that = this;
			// 	api.liveRoomInfoList().then(res => {
			// 		let roomList = res.data;
			// 		const livingRooms = [];
			// 		if (roomList && roomList.length > 0) {
			// 			roomList.forEach(item => {
			// 				if (item.liveStatus == 101) {
			// 					that.isLiving = true
			// 					livingRooms.push(item);
			// 				}
			// 			})
			// 			if (livingRooms && livingRooms.length > 0) {
			// 				if (livingRooms.length == 1) {
			// 					that.cusLivingPageUrl =
			// 						'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=' + (
			// 							livingRooms[0].roomid || livingRooms[0].roomId);
			// 				} else {
			// 					that.cusLivingPageUrl = "pages/live/room-list/index";
			// 				}
			// 			}
			// 		} else {
			// 			api.liveRoomInfoList().then(res => {
			// 				let roomList = res.data;
			// 				const livingRooms = [];
			// 				if (roomList && roomList.length > 0) {
			// 					roomList.forEach(item => {
			// 						if (item.liveStatus == 101) {
			// 							that.isLiving = true
			// 							livingRooms.push(item);
			// 						}
			// 					})
			// 					if (livingRooms && livingRooms.length > 0) {
			// 						if (livingRooms.length == 1) {
			// 							that.cusLivingPageUrl =
			// 								'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=' +
			// 								livingRooms[0].roomid;
			// 						} else {
			// 							that.cusLivingPageUrl = "pages/live/room-list/index";
			// 						}
			// 					}
			// 				}
			// 			});
			// 		}

			// 	});
			// },

			getCustomerServiceInfo() {
				let that = this;
				api.getLivingTag().then((res) => {
					if (res && res.data) {
						that.cusServicePageUrl = res.data.platWxCustomerUrl;
						app.globalData.wxCustomerUrl = res.data.platWxCustomerUrl;
						const liveingRoomIds = res.data.liveingRoomIds;
						if (liveingRoomIds) {
							this.isLiving = true;
							const liveingRoomArray = liveingRoomIds.split(',');
							if (liveingRoomArray && liveingRoomArray.length > 1) {
								that.cusLivingPageUrl = '/pages/third-tab/index?selectPoi=3';
							} else {
								that.cusLivingPageUrl =
									'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=' +
									liveingRoomArray[0];
							}
						}
					}
				});
			},
			getmeagessInfo() {
				getInfo().then((res) => {
					this.meagessinfo = res.data;
				});
			},
			
			handleRedpackageClick(){
				this.$emit("showRedpackage");
			}
		}
	};
</script>

<style scoped lang="scss">
	.pop-component {
		z-index: 9999;
	}

	.hover-radios {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		background-color: #f55151;
		border-radius: 50%;
		top: 00rpx;
		right: 0rpx;
		color: #fff;
	}

	@media screen and (min-width: 549px) {
		.hover-radios {
			width: 30rpx;
			height: 30rpx;
		}
	}
</style>