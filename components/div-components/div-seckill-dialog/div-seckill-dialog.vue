<template>
	<!--秒杀弹框 -->
	<view class="cu-modal" catchtouchmove="true" v-if="showModal" :class="showModal ? 'show' : ''" style="width: 100vw; height: 100vh">
		<view class="cu-dialog bg">
			<view class="container">
				<image :src="seckillBg" class="bg-image" mode="widthFix"></image>
				<view class="seckill-info ">
					<template v-for="(item, index) in value.listSeckillInfo">
						<view @click="handleGo(item)" v-if="index < 3" :key="item.id" class="item">
							<image class="goods-img" :src="item.picUrl | formatImg360"></image>
							<view class="content flex flex-direction justify-between margin-left-sm">
								<view class="name overflow-1 text-sm text-bold">
									{{ item.name }}
								</view>
								<view class="seckill-text text-sm text-red text-bold">秒杀价</view>
								<view class="price-layout flex flex-direction justify-between pt-xxxs">
									<price-handle color="#FFFFFF" :value="item.seckillPrice" signFont="20rpx" bigFont="36rpx" smallFont="20rpx"></price-handle>
									<view class="flex justify-between tip text-sm">
										<text>￥{{ item.originalPrice }}</text>
										<text class="text-xs">即将恢复</text>
									</view>
								</view>
							</view>
						</view>
					</template>
				</view>
			</view>
			<image @click="handleGo" class="go-img" :src="goBg"></image>
			<view class="close-layout">
				<image style="width: 50rpx; height: 50rpx;" fit="cover" :src="$imgUrl('/1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')" @tap.stop="handleClose" />
			</view>
		</view>
	</view>
</template>

<script>
import { gotoPage } from '@/components/div-components/div-base/div-page-urls.js';
import { senTrack, getCurrentTitle } from "@/public/js_sdk/sensors/utils.js"

const app = getApp();
export default {
	name: 'div-seckill-dialog',
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {};
			}
		}
	},

	data() {
		return {
			showModal: true,
			theme: app.globalData.theme
		};
	},
	computed: {
		seckillBg: function () {
			const bg = this.$imgUrl('live/seckill/dia-bg.webp');
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.seckillDialogBg || bg;
			}
			return bg;
		},

		goBg: function () {
			const bg = this.$imgUrl('live/seckill/go.webp');
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.seckillDialogGo || bg;
			}
			return bg;
		}
	},

	mounted() {
		this.userinfo = uni.getStorageSync('user_info');
       setTimeout(()=>{
           //神策添加埋点
		  senTrack("ActivityGuidePopExposure", {
			page_name: getCurrentTitle(0),
			activity_id: this.value.id,
			activity_name: '秒杀',
			activity_type_first: '营销活动',
			activity_type_second: '秒杀'
		  });
	   })
		
	},

	methods: {
		handleGo(item) {
			if (item && item.id > 0) {
				gotoPage(`/pages/seckill/seckill-list/index?id=${item.id}&seckillHallId=${this.value.id}&categoryId=${this.value.categoryId}`);
			} else {
				gotoPage(`/pages/seckill/seckill-list/index?seckillHallId=${this.value.id}&categoryId=${this.value.categoryId}`);
			}
		},
		handleClose() {
			this.showModal = false;
			this.$emit('close');
		},

		skillCategoryId(pageUrl) {
			return pageUrl ? pageUrl.substring(pageUrl.indexOf('=') + 1, pageUrl.length) : '';
		}
	}
};
</script>

<style scoped lang="scss">
.bg {
	background-color: transparent;
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: auto;
	.container {
		position: relative;
		.bg-image {
			width: 531rpx;
		}
	}
}

.seckill-info {
	position: absolute;
	top: 18%;
	left: 7%;
}

.item {
	display: flex;
	margin-top: 27rpx;
	.name {
		text-align: left;
	}
	.goods-img {
		width: 168rpx;
		height: 168rpx;
		border-radius: 20rpx;
	}
	.content {
		flex: 1;
	}

	.price-layout {
		flex: 1;
		width: 286rpx;
		height: 106rpx;
		background-image: url(https://img.songlei.com/live/seckill/price-bg.webp);
		background-size: 100% 100%;
		background-repeat: no-repeat;
		.tip {
			color: #afafaf;
		}
	}
}

.go-img {
	width: 312rpx;
	height: 104rpx;
	margin-top: -104rpx;
}

.close-layout {
	width: 62rpx;
	display: flex;
	justifycontent: flex-end;
	margin-top: 24rpx;
	position: relative;
	transform: translateX(-50%);
	left: 50%;
}
</style>
