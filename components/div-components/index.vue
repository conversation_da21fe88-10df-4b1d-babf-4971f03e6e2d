<template>
  <!-- uni-app 不支持动态组件的方式, 所以都是if -else -->
  <view style="position: relative">
    <view
      v-if="
        pageDivData.pageComponent.togetherComponent &&
        pageDivData.pageComponent.togetherComponent.bgImage
      "
      :style="{
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
        height: pageDivData.pageComponent.togetherComponent.height * 2 + 'rpx',
        backgroundSize: 'cover',
        backgroundImage: pageDivData.pageComponent.togetherComponent
          ? `url(${pageDivData.pageComponent.togetherComponent.bgImage})`
          : '',
      }"
    ></view>
    <home-skeleton :loading="loading"></home-skeleton>
    <template
      v-if="
        pageDivData.pageComponent.componentsList &&
        pageDivData.pageComponent.componentsList.length > 0
      "
    >
      <div-componentlist
        :componentsList="pageDivData.pageComponent.componentsList"
        @getpage="handleGetMicroPage"
        @openRulePopup="openRulePopup"
        :isBack="isBack"
        ref="divComponentlist"
        :showScreen="showScreen"
        :showMinutes="showMinutes"
        :pageSelectPoi="pageSelectPoi"
      />
    </template>
    <template v-else>
      <cu-custom
        :bgColor="'bg-' + theme.backgroundColor"
        :isBack="true"
        :hideMarchContent="true"
      >
        <block slot="content">微页面</block>
      </cu-custom>
      <view
        style="
          font-size: 28rpx;
          margin-top: 200rpx;
          color: #8799a3;
          display: flex;
          justify-content: center;
        "
        >该页面未搭建或者未发布</view
      >
    </template>

    <!-- 根据 currentPopup.name 动态渲染对应弹窗，保证同一时刻只显示一个弹窗 -->
    <!-- 秒杀弹窗 -->
    <div-seckill-dialog
      v-if="currentPopup && currentPopup.name === 'seckill'"
      v-model="currentPopup.data"
      @close="closeCurrentPopup"
    />

    <!-- 首页弹窗轮播 -->
    <div-advert-dialog
      v-if="currentPopup && currentPopup.name === 'advert'"
      v-model="currentPopup.data"
      @close="closeCurrentPopup"
    />

    <!-- 生日提醒弹窗 -->
    <div-birthday-dialog
      v-if="currentPopup && currentPopup.name === 'birthday'"
      v-model="currentPopup.data"
      @close="closeCurrentPopup"
    />

    <!-- 券数量提醒弹窗 -->
    <div-couponnum-dialog
      v-if="currentPopup && currentPopup.name === 'couponnum'"
      v-model="currentPopup.data"
      @close="closeCurrentPopup"
    />

    <!-- 分销提醒弹窗 -->
    <div-distribution-dialog
      v-if="currentPopup && currentPopup.name === 'distribution'"
      @close="closeCurrentPopup"
    />

    <!-- 新客专享弹窗 -->
    <div-new-costomer-dialog
      v-if="currentPopup && currentPopup.name === 'newcustomer'"
      :showDialog="true"
      :newCustomerData="currentPopup.data"
      @close="closeCurrentPopup"
    />

    <!-- 浮动客服等小图标（不属于弹窗，单独显示） -->
    <div-pop
      v-if="pageDivData.pageComponent.pageAdvertDialog"
      :pageAdvertDialog="pageDivData.pageComponent.pageAdvertDialog"
      @showRedpackage="handleShowRedpackage"
    />

    <!-- 大转盘弹窗 -->
    <pop-lottery
      v-if="currentPopup && currentPopup.name === 'lottery'"
      :lotteryId="currentPopup.data.lotteryId"
      @close="closeCurrentPopup"
    />

    <!-- 新人三单礼弹窗 -->
    <div-rule-dialog
      v-if="currentPopup && currentPopup.name === 'rule'"
      :setData="currentPopup.data"
      @closeRulePopup="closeCurrentPopup"
    />

    <!-- 竞猜弹窗 -->
    <div-competion-dialog
      v-if="currentPopup && currentPopup.name === 'competion'"
      v-model="currentPopup.data"
      @close="closeCurrentPopup"
    />

    <!-- 金牌弹窗 -->
   <div-gold-medal-dialog
      v-if="currentPopup && currentPopup.name === 'goldMedal'"
      v-model="currentPopup.data"
      @close="closeCurrentPopup"
    />

    <!-- 红包雨弹窗 -->
    <div-red-packet-dialog
      v-if="currentPopup && currentPopup.name === 'redPacket'"
      v-model="currentPopup.data"
      @close="closeCurrentPopup('redPacket')"
    />
  </view>
</template>

<script>
const app = getApp();
import api from "@/utils/api";
import { distributionUserInitialLogin } from "@/api/distribution.js";
import homeSkeleton from "@/components/base-skeleton/page-skeleton/home-skeleton";
import divComponentlist from "@/components/div-components/div-componentslist/index";
import divAdvertDialog from "@/components/div-components/div-advert-dialog/div-advert-dialog";
import divCompetionDialog from "@/components/div-components/div-comptetion-dialog/div-comptetion-dialog";
import divBirthdayDialog from "@/components/div-components/div-birthday-dialog/div-birthday-dialog";
import divNewCostomerDialog from "@/components/div-components/div-newcostomer-dialog/div-newcostomer-dialog";
import divDistributionDialog from "@/components/div-components/div-distribution-dialog/div-distribution-dialog";
import divCouponnumDialog from "@/components/div-components/div-couponnum-dialog/div-couponnum-dialog";
import divSeckillDialog from "@/components/div-components/div-seckill-dialog/div-seckill-dialog";
import divPop from "@/components/div-components/div-pop/index.vue";
import PopLottery from "@/components/lottery/pop-lottery.vue";
import divRuleDialog from "@/components/div-components/div-rule-dialog/div-rule-dialog";
import __config from "@/config/env";
import divGoldMedalDialog from "@/components/div-components/div-gold-medal-dialog/index.vue";
import divRedPacketDialog from "@/components/div-components/div-red-packet-dialog/index.vue";
import { getHomePop } from "@/components/div-components/div-red-packet-dialog/api/redPacketApi.js";

import { EventBus } from "@/utils/eventBus.js";
// 新人专享
import { getNewcustomerPop, getCouponnum } from "api/newcostomer.js";
// 竞猜活动首页弹框
import { guessPop, getGoldPopResult } from "api/activity.js";
const util = require("utils/util.js");

import microPageStack from "@/public/js_sdk/sensors/globalStack";
import { senTrack } from "@/public/js_sdk/sensors/utils.js";

export default {
  components: {
    homeSkeleton,
    divComponentlist,
    divAdvertDialog,
    divBirthdayDialog,
    divSeckillDialog,
    divDistributionDialog,
    divPop,
    PopLottery,
    divNewCostomerDialog,
    divRuleDialog,
    divCompetionDialog,
    divCouponnumDialog,
    divGoldMedalDialog,
    divRedPacketDialog,
  },
  data() {
    return {
      loading: true,
      theme: app.globalData.theme, //全局颜色变量
      loadmore: true,
      pageDivData: {
        pageComponent: {
          componentsList: [],
        },
      },
      pageName: "",
      loadmore3: true, // 自定义页面的加载状态
      shares: {
        title: "",
        imageUrl: "",
        path: "",
      },
      id: "",
      birthdayData: {},
      currentPageParams: null,
      lotteryId: "1727263244893495298", // 大转盘活动 Id
      // 新人专享
      newCustomerLotteryId: "", // 新人专享抽奖 Id
      newCustomerData: null, // 新人专享商品推荐数据
      showRuleData: "",
      // competionInfo: {},
      couponNum: 0, //劵链路 0不用弹，大于0弹
      goldMedalInfo: {},
      curSeckillHall: {}, // 当前秒杀活动
      redpacketData: {},
      dongbeiData: {},
      showRedPacketDialog: false,
      popupQueue: [], // 弹窗队列，存放待显示弹窗对象
      currentPopup: null, // 当前显示的弹窗对象 { name, data, priority, timestamp }
      noClick: true,
    };
  },

  props: {
    divType: {
      type: String,
      default: "home", //home 平台首页  micro 微页面
    },
    microId: {
      type: String | Number,
      default: "",
    },

    isBack: {
      type: Boolean,
      default: true,
    },

    dialogIsShowing: {
      type: Boolean,
      default: true,
    },

    showScreen: {
      type: Boolean,
      default: false,
    },
    showMinutes: {
      type: Number,
      default: -1,
    },
    //设置对象方便更新
    pageSelectPoi: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    microId: {
      handler(val, oldVal) {
        //普通的watch监听
        if (val > 0) {
          this.loading = true;
          this.loadData();
        }
      },
      immediate: true,
    },
  },

  created() {
    EventBus.$on("refreshNewCustomerDialog", async () => {
      if (app.isUser() == 1) {
        const pageAdvertDialog =
          this.pageDivData.pageComponent.pageAdvertDialog;
        // 新人专享
        if (
          pageAdvertDialog &&
          pageAdvertDialog.newcustomerinfo &&
          pageAdvertDialog.newcustomerinfo.isShow == 1
        ) {
          await this.getNewcustomerPopData(
            pageAdvertDialog.newcustomerinfo.newcustomerId,
          );
        }
        //获取路由信息
        const pages = getCurrentPages();
        //获取当前路由
        let nowPage = pages[pages.length - 1];
        // 首页红包雨
        if (nowPage.route == "pages/home/<USER>") {
          await this.$noMultipleClicks(this.getRedPacketDialog, 1);
        }
      }
    });
    //点击东北话入口。弹窗东北话弹框
    EventBus.$on("dialog:showNortheastActivity", async () => {
      //获取路由信息
      const pages = getCurrentPages();
      //获取当前路由
      let nowPage = pages[pages.length - 1];
      if (nowPage.route == "pages/home/<USER>") {
        //首页东北话弹框
        uni.navigateTo({
          url: "/pages/activity/audio-game/index",
        });
      }
    });
  },

  mounted() {
    this.shareMessage();
    if (this.divType == "home") {
      this.getSeckillInfo();
    }
  },

  beforeDestroy() {
    microPageStack.pop();
  },

  methods: {
    /**
     * 添加弹窗到队列
     * @param {Object} popup - 弹窗对象 { name, data, priority }
     * priority 数字越大优先级越高
     */
    addPopup(popup) {
      // 防止重复弹窗，若已存在则更新数据和优先级
      const existingIndex = this.popupQueue.findIndex(
        (p) => p.name === popup.name,
      );
      if (existingIndex !== -1) {
        this.popupQueue[existingIndex].data = popup.data;
        this.popupQueue[existingIndex].priority = popup.priority;
        this.popupQueue[existingIndex].timestamp = Date.now();
      } else if (this.currentPopup && this.currentPopup.name === popup.name) {
        // 如果当前弹窗就是这个，更新数据
        this.currentPopup.data = popup.data;
        this.currentPopup.priority = popup.priority;
        this.currentPopup.timestamp = Date.now();
        return;
      } else {
        // 新弹窗，添加入队列并记录时间戳
        popup.timestamp = Date.now();
        this.popupQueue.push(popup);
      }
      // 按优先级降序排序，优先级相同按时间升序（先加入先显示）
      this.popupQueue.sort((a, b) => {
        if (b.priority === a.priority) {
          return a.timestamp - b.timestamp;
        }
        return b.priority - a.priority;
      });
      // 尝试显示下一个弹窗
      this.showNextPopup();
    },

    /**
     * 显示队列中优先级最高的弹窗（如果当前无弹窗显示）
     */
    showNextPopup() {
      if (!this.currentPopup && this.popupQueue.length > 0) {
        this.currentPopup = this.popupQueue.shift();
      }
    },
    /**
     * 关闭当前弹窗，自动显示下一个
     */
    closeCurrentPopup(pageName) {
      this.currentPopup = null;
      this.shares = {};
      this.shareMessage();
      this.showNextPopup();
    },

    /**
     * 清空所有弹窗队列和当前弹窗
     */
    clearAllPopups() {
      this.popupQueue = [];
      this.currentPopup = null;
    },

    pullDownRefresh() {
      // 显示顶部刷新图标
      uni.showNavigationBarLoading();
      this.refresh(); // 隐藏导航栏加载框
      uni.hideNavigationBarLoading(); // 停止下拉动作
      uni.stopPullDownRefresh();
    },
    shareMessage: function () {
      //获取路由信息
      const pages = getCurrentPages();
      //获取当前路由
      let nowPage = pages[pages.length - 1];
      this.shares.path = nowPage.route;
      let title = this.shares.title ? this.shares.title : this.pageName;
      let imageUrl = this.shares.imageUrl ? this.shares.imageUrl : "";
      const userInfo = uni.getStorageSync("user_info");
      const userCode = userInfo ? "&sharer_user_code=" + userInfo.userCode : "";
      //标题
      this.shares.title = title;
      //路径
      this.shares.path = `${this.shares.path}?id=${this.microId}${userCode}`;
      //图片
      this.shares.imageUrl = imageUrl;
    },
    updataShareInfo(data) {
      this.shares.imageUrl = data.sharePicUrl;
      this.shares.path = `pages/home/<USER>
    },

    checkLoadData() {
      if (this.divType == "home" || (this.microId && this.divType == "micro")) {
        this.loadData();
      }
    },

    loadData() {
      api.pagedevisePage(this.microId).then(async (res) => {
        let pageDivData = res.data;
        if (pageDivData) {
          // 神策埋点 下单来源使用
          this.pageName = pageDivData.pageName;
          microPageStack.push(pageDivData.id, this.pageName);
          this.pageDivData = pageDivData;
          let pageComponent = pageDivData.pageComponent;
          this.id = pageDivData.id;
          let pageAdvertDialog = pageDivData.pageComponent.pageAdvertDialog;
          if (
            pageComponent &&
            pageAdvertDialog &&
            pageAdvertDialog.shareShow != 0
          ) {
            this.shares.title = pageAdvertDialog.shareTitle;
            this.shares.imageUrl = pageAdvertDialog.shareImageUrl;
          }
          if (pageAdvertDialog && pageAdvertDialog.canShare) {
            this.$emit("canMicroPageShare", pageAdvertDialog.canShare);
          }
          // 新人专享
          if (
            pageAdvertDialog &&
            pageAdvertDialog.newcustomerinfo &&
            pageAdvertDialog.newcustomerinfo.isShow == 1
          ) {
            if (app.isUser() == 1) {
              await this.getNewcustomerPopData(
                pageAdvertDialog.newcustomerinfo.newcustomerId,
              );
            }
          }

          if (
            pageAdvertDialog &&
            pageAdvertDialog.lottery &&
            pageAdvertDialog.lottery.isShow == 1
          ) {
            this.handleShowSingleDialog("lottery");
          }
          if (app.isUser() == 1) {
            await this.getCouponnumPopData();
          }
        }
        this.loading = false;
        const userInfo = uni.getStorageSync("user_info");
        if (this.divType == "home" && userInfo && userInfo.erpCid) {
          distributionUserInitialLogin()
            .then((res) => {
              if (res.code == 0 && res.data && res.data.isShow == 1) {
                this.handleShowSingleDialog("distribution");
              }
            })
            .catch((e) => {
              console.error(e);
            });

          api
            .getBirthDayDialogInfo({
              channel: "SONGSHU",
              custId: userInfo.erpCid,
            })
            .then((res) => {
              if (res.code == 0) {
                this.handleShowSingleDialog("birthday");
                this.birthdayData = res.data;
              } else {
                this.dealShowDialog(pageDivData);
              }
            })
            .catch((e) => {
              this.dealShowDialog(pageDivData);
            });

          this.guessPopDialog();
          this.goldMedalDialog();

          return;
        }
        this.dealShowDialog(pageDivData);
      });
    },

    // 足球竞猜
    guessPopDialog() {
      guessPop().then((res) => {
        if (res.data && res.data.myGuess && res.data.myGuess.length > 0) {
          if (res.data.isPop == 1) this.handleShowSingleDialog("competion");
          this.competionInfo = res.data;
          const { myGuess } = this.competionInfo;
          // 遍历奖品列表，为每个奖品单独上传埋点
          if (Array.isArray(myGuess) && myGuess.length > 0) {
            myGuess.forEach((prize) => {
              const { matchName, id, guessResult } = prize;

              senTrack("ActivityResult", {
                page_name: "首页",
                page_level: "一级",
                activity_id: id,
                activity_name: matchName,
                activity_type_first: "营销活动",
                activity_type_second: "足球竞猜",
                is_obtain_prize: guessResult == "1",
              });
            });
          }
        }
      });
    },

    // 金牌弹框
    goldMedalDialog() {
      getGoldPopResult().then((res) => {
        if (res.data) {
          this.handleShowSingleDialog("goldMedal");
        }
        this.goldMedalInfo = res.data;
        if (this.goldMedalInfo&&this.goldMedalInfo.id) {
          senTrack("ActivityResult", {
            page_name: '首页',
            page_level: '一级',
            activity_id: this.goldMedalInfo.id,
            activity_name: '金牌竞猜',
            activity_type_first: '营销活动',
            activity_type_second: '金牌竞猜',
            is_obtain_prize: this.goldMedalInfo.guessResult == '1',
          });
        }
      });
    },

    // num 1标识第一次调用 2标识点击图标
    async getRedPacketDialog(num) {
      console.error("====getRedPacketDsalog=======", num);
      try {
        const { data } = await getHomePop();
        if (data) {
          this.redpacketData = data;
          if (num !== 1 || data.isPop) {
            this.handleShowSingleDialog("redPacket");
          }
          if (this.redpacketData&&this.redpacketData.redPacketRainInfo&&this.redpacketData.redPacketRainInfo.id) {
            
            senTrack("ActivityGuidePopExposure", {
              page_name: "首页",
              activity_id: this.redpacketData.redPacketRainInfo.id,
              activity_name: this.redpacketData.redPacketRainInfo.name,
              activity_type_first: '营销活动',
              activity_type_second: '首页红包雨',
            });
          }
        }
      } catch (error) {
        console.error("获取红包弹窗数据失败:", error);
      }
    },

    dealShowDialog(pageDivData) {
      let showDialog =
        this.dialogIsShowing &&
        pageDivData &&
        pageDivData.pageComponent &&
        pageDivData.pageComponent.pageAdvertDialog &&
        pageDivData.pageComponent.pageAdvertDialog.showAdvertDialog == 1;
      if (showDialog) {
        showDialog =
          showDialog &&
          util.isShowAdvert(
            pageDivData.pageComponent.pageAdvertDialog.intervalTime,
            pageDivData.pageComponent.pageAdvertDialog.intervalNumber,
            "advert",
          );
      }
      if (showDialog) {
        this.handleShowSingleDialog("advert");
      } else {
        uni.$emit("showBottomBar");
      }
    },

    refresh() {
      this.loadmore = true;
      this.goodsList = [];
      this.goodsListNew = [];
      this.goodsListHot = [];
      if (this.currentPageParams && this.currentPageParams.microId) {
        this.handleGetMicroPage(this.currentPageParams);
      } else {
        this.loadData();
      }
    },

    handleGetMicroPage(params) {
      if (params.index == 0) {
        this.currentPageParams = null;
        this.loadData();
        return;
      }
      this.currentPageParams = params;
      if (params.microId > 0) {
        // this.loading=true;
        api.pagedevisePage(params.microId).then((res) => {
          let result = res.data;
          if (result) {
            let temp = [];
            for (
              let j = 0;
              j < this.pageDivData.pageComponent.componentsList.length;
              j++
            ) {
              temp.push(this.pageDivData.pageComponent.componentsList[j]);
              if (
                this.pageDivData.pageComponent.componentsList[j].id ==
                params.componentId
              ) {
                break;
              }
            }
            if (
              result.pageComponent.componentsList &&
              result.pageComponent.componentsList.length > 0 &&
              result.pageComponent.componentsList[0].componentName ==
                "headdivComponent"
            ) {
              result.pageComponent.componentsList.splice(0, 1);
            }
            temp = temp.concat(result.pageComponent.componentsList);
            this.pageDivData.pageComponent.componentsList = temp;
          }
          this.loading = false;
        });
      }
    },

    // 下拉加载
    reachBottom() {
      console.log("调用页面组件");
      this.$refs.divComponentlist.reachBottom();
    },

    // 新人专享
    async getNewcustomerPopData(id) {
      try {
        const res = await getNewcustomerPop({
          id,
        });
        this.newCustomerData = res?.data || null;
        if (
          this.newCustomerData?.activityType == 1 &&
          this.newCustomerData?.newCustomerGoodsList?.length
        ) {
          this.handleShowSingleDialog("newcustomer");
        } else if (this.newCustomerData?.activityType == 2) {
          this.newCustomerLotteryId = this.newCustomerData.actId;
          this.handleShowSingleDialog("lottery");
        }
      } catch (error) {
        console.error("获取新人专享数据失败:", error);
        this.newCustomerData = null;
      }
    },

    // 劵链路
    async getCouponnumPopData() {
      try {
        const res = await getCouponnum({
          type: 1,
        });
        this.couponNum = res?.data || null;
        if (this.couponNum > 0) {
          this.handleShowSingleDialog("couponnum");
        }
      } catch (error) {
        console.error("获取优惠券数据失败:", error);
        this.couponNum = 0;
      }
    },

    openRulePopup(info) {
      this.showRuleData = info;
      this.handleShowSingleDialog("rule");
    },

    // 获取秒杀商品
    async getSeckillInfo() {
      try {
        const res = await api.getSeckillhallProductList();
        let seckillList = res.data;
        if (seckillList && seckillList.length > 0) {
          for (let i = 0; i < seckillList.length; i++) {
            if (
              seckillList[i] &&
              seckillList[i].listSeckillInfo &&
              seckillList[i].listSeckillInfo.length > 0
            ) {
              this.curSeckillHall = seckillList[0];
              if (
                this.curSeckillHall.listSeckillInfo &&
                this.curSeckillHall.listSeckillInfo.length > 2
              ) {
                setTimeout(() => {
                  this.handleShowSingleDialog("seckill");
                }, 500);
              }
              return;
            }
          }
        }
      } catch (error) {
        console.log(error);
      }
    },

    handleShowRedpackage() {
      this.$noMultipleClicks(this.getRedPacketDialog, 2);
    },

    getPopupConfig() {
      return {
        seckill: {
          priority: 8,
          data: this.curSeckillHall,
        },
        advert: {
          priority: 5,
          data: this.pageDivData.pageComponent.pageAdvertDialog,
        },
        birthday: {
          priority: 10,
          data: this.birthdayData,
        },
        couponnum: {
          priority: 6,
          data: this.couponNum,
        },
        distribution: {
          priority: 2,
          data: null,
        },
        newcustomer: {
          priority: 12,
          data: this.newCustomerData,
        },
        lottery: {
          priority: 7,
          data: {
            lotteryId:
              this.newCustomerLotteryId ||
              (this.pageDivData.pageComponent.pageAdvertDialog &&
                this.pageDivData.pageComponent.pageAdvertDialog.lottery &&
                this.pageDivData.pageComponent.pageAdvertDialog.lottery
                  .lotteryId),
          },
        },
        rule: {
          priority: 11,
          data: this.showRuleData,
        },
        competion: {
          priority: 4,
          data: this.competionInfo,
        },
        goldMedal: {
          priority: 3,
          data: this.goldMedalInfo,
        },
        redPacket: {
          priority: 9,
          data: this.redpacketData,
        },
        dongbeihua: {
          priority: 13,
          data: this.dongbeiData || null,
        },
      };
    },

    handleShowSingleDialog(type) {
      const config = this.getPopupConfig()[type];
      if (config) {
        this.addPopup({
          name: type,
          data: config.data,
          priority: config.priority,
        });
      }
    },
  },
};
</script>
<style>
@import "@/components/div-components/index.css";
</style>
