<template>
	<!-- 单图显示组件 -->
	<view class="cu-modal" catchtouchmove='true' v-if="showModal" :class="showModal ? 'show' : ''">
		<view class="cu-dialog " style="width:720rpx;background-color: transparent;">
			<view class="customer-box">
				<image class="bg" :src="newCustomerData.backdropPicUrl || $imgUrl('live/customer-dialog/bg.png')" />
				<view style="display: flex; flex-wrap: wrap;">
					<view v-for="(item, index) in newCustomerData.newCustomerGoodsList" :key="index" class="item"
						@click="goToDetail(item)">
						<image style="width: 302rpx; height: 302rpx; border-radius: 12rpx;" :src="item.picUrl" fit="cover" />
						<view class="goods-info" v-if="item.stock > 0">
							<view class="price" style="color: #ff0000;">
								￥{{ item.newPersonPrice.toFixed(2) }}
							</view>
							<view class="price" style="color: #bababa; text-decoration: line-through;">
								￥{{ item.priceOriginal.toFixed(2) }}
							</view>
						</view>
						<view class="goods-info" style="" v-else>
							<view class="price" style="width: 100%; color: #bababa; text-align: center;">
								抢光了
							</view>
						</view>
					</view>
				</view>

				<div-base-navigator :pageUrl="newCustomerData.skipMicropage || newCustomerData.skipApplet" @tap="handleAddnum">
					<image style="width: 500rpx; height: 100rpx; margin: 30rpx 0;"
						:src="newCustomerData.bottomButtonPicUrl || $imgUrl('live/customer-dialog/go.png')" fit="cover" />
				</div-base-navigator>
			</view>

			<view
				:style="{ width: `77rpx`, display: 'flex', justifyContent: ' flex-end', marginTop: '20rpx', marginBottom: '20rpx', position: 'relative', transform: 'translateX(-50%)', left: '50%' }">
				<image style="width: 30px; height: 30px; " :src="$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')"
					fit="cover" @tap.stop="handleClose" />
			</view>

		</view>
	</view>
</template>

<script>
import DivBaseNavigator from "@/components/div-components/div-base/div-base-navigator.vue"
import {
	getClickNum
} from 'api/newcostomer.js'
import {
	getCurrentTitle
} from '@/public/js_sdk/sensors/utils.js'
import {
	senTrack
} from '@/public/js_sdk/sensors/utils.js'

export default {
	name: 'div-newcostomer-dialog',
	props: {
		newCustomerData: {
			default: ''
		}
	},
	data () {
		return {
			showModal: false,
			enterTime: ''
		}
	},
	components: {
		DivBaseNavigator
	},
	watch: {
		newCustomerData: {
			handler (val) {
				this.showModal = val.newCustomerGoodsList?.length || false
			},

			immediate: true
		}
	},
	created () {
		this.enterTime = new Date().getTime()
		senTrack('ActivityGuidePopExposure', {
			'page_name': getCurrentTitle(0),
			'activity_id': this.newCustomerData.id,
			'activity_name': this.newCustomerData.name,
			'activity_type_first': '营销活动',
			'activity_type_second': '新客专享'
		})
		senTrack('ActivityPageView', {
			'page_name': getCurrentTitle(0),
			'forward_source': getCurrentTitle(0),
			'page_level': '一级',
			'activity_id': this.newCustomerData.id,
			'activity_name': this.newCustomerData.name,
			'activity_type_first': '营销活动',
			'activity_type_second': '新客专享'
		})
	},
	destroyed () {
		const leaveTime = new Date().getTime()
		const stayDuration = Math.floor((leaveTime - this.enterTime) / 1000)

		senTrack('ActivityPageLeave', {
			'page_name': getCurrentTitle(0),
			'forward_source': getCurrentTitle(0),
			'page_level': '一级',
			'activity_id': this.newCustomerData.id,
			'activity_name': this.newCustomerData.name,
			'activity_type_first': '营销活动',
			'activity_type_second': '新客专享',
			'stay_duration': stayDuration
		})
	},
	methods: {
		handleClose () {
			this.showModal = false
			this.$emit('close')
			uni.$emit('showBottomBar')
		},
		goToDetail (item) {
			if (!item.stock) {
				uni.showToast({
					title: '商品已售罄',
					icon: 'none'
				})
				return
			}
			senTrack('ActivityClick', {
				'page_name': getCurrentTitle(0),
				'page_level': '一级',
				'activity_id': this.newCustomerData.id,
				'activity_name': this.newCustomerData.name,
				'activity_type_first': '营销活动',
				'activity_type_second': '新客专享'
			})

			try {
				getClickNum({
					newCustomerId: this.newCustomerData.id,
					isClickGoods: "1"
				})
				const sourceModule = encodeURIComponent('新客弹框')
				uni.navigateTo({
					url: `/pages/goods/goods-detail/index?id=${item.spuId}&skuId=${item.skuId}&customerGoodsId=${item.id}&customerId=${this.newCustomerData.id}&source_module=${sourceModule}`
				})
			} catch (error) {
				console.log(error)
			}
		},
		handleAddnum () {
			senTrack('ActivityClick', {
				'page_name': getCurrentTitle(0),
				'page_level': '一级',
				'activity_id': this.newCustomerData.id,
				'activity_name': this.newCustomerData.name,
				'activity_type_first': '营销活动',
				'activity_type_second': '新客专享'
			})

			getClickNum({
				newCustomerId: this.newCustomerData.id,
				isClickButton: "1"
			})
		}
	}
}
</script>

<style scoped lang="scss">
.customer-box {
	height: 1180rpx;
	width: 100%;
	position: relative;
	padding: 280rpx 46rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;

	.bg {
		height: 1180rpx;
		width: 100%;
		position: absolute;
		top: 0;
		left: 0;
		z-index: -1;
	}

	.item {
		width: 302rpx;
		height: 358rpx;
		background: #FFFFFF;
		border-radius: 12rpx;
		margin: 7rpx;
		display: flex;
		flex-direction: column;
		padding: 2rpx;

		.goods-info {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 44rpx;
			background: #F4F4F4;
			border-radius: 12rpx;
			flex: 1;
			margin-top: 4rpx;

			.price {
				height: 52rpx;
				line-height: 52rpx;
				font-size: 26rpx;
			}
		}
	}
}

.font-style {
	font-size: 29rpx;
	font-weight: 400;
	color: #64241A;
}
</style>