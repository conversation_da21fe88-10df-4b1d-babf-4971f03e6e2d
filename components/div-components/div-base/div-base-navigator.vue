<template>
	<!-- 组件中应用的常用组件  -->
	<!-- 跳转页面组件 -->
	<view @click="goPage" :style="styleProps">
		<slot></slot>
	</view>
</template>

<script>
const app = getApp();
import { EventBus } from '@/utils/eventBus.js';
import { pageUrls, gotoPage } from './div-page-urls.js';

export default {
	props: {
		pageUrl: {
			type: String,
			default: ''
		},
		styleProps: {
			background: '#FFFFFF'
		}
	},
	watch: {
		pageUrl(newValue, oldValue) {
			console.log('===pageUrl==111======', newValue, oldValue);
		}
	},
	data() {
		return {
			pageUrls: pageUrls,
			pagesLength: 1
		};
	},
	created: function () {
		//监听当前页面栈的个数内容
		let pages = getCurrentPages();
		this.pagesLength = pages.length; //当前页面栈的个数
	},
	methods: {
		goPage(e) {
			if (this.pageUrl) {
				gotoPage(this.pageUrl, this.pagesLength);
			}
		}
	}
};
</script>

<style scoped lang="scss"></style>
