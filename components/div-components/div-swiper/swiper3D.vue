<template>
  <view class="banner-container">
    <!-- 图片式展示 -->
    <swiper 
      v-if="setData.isShow=='1'"
      :class="setData.dotStyle == 'square-dot' ? 'square-dot':(setData.dotStyle == 'round-dot' ? 'round-dot': '')"
      :style="{height: `${42+setData.height*2}rpx`}"
      :indicator-dots="setData.dotStyle == '' ? false : true " 
      :indicator-color="'#cccccc'" 
      :indicator-active-color="'#ffffff'"
      :autoplay="true" 
      :interval="setData.interval?setData.interval:3000" 
      :duration="1000" 
      :circular="true"
      :previous-margin="(190-setData.leftRightpadding)+'rpx'"
      :next-margin="(190-setData.leftRightpadding)+'rpx'"
      @change="swiperChange" 
      >
      <swiper-item v-for="(item, i) in setData.swiperList" :key="i">
        <view class="image-container" v-if="curIndex===0">
          <view v-if="i===listLen-1" class="item-left" 
          :style="{
              // width: '90%',
              // right:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              top:'5px',
              left: `${setData.leftRightpadding<='0'?(58+Math.abs(setData.leftRightpadding*2)):(setData.leftRightpadding*2+38)}rpx`,
            }"
          >
            <div
              v-if="setData.shadow!='0'"
              style=" z-index: 9999"
              :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }"
              >
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)"/>
          </view>
          <view v-else-if="i===1" class="item-right"
          :style="{
              // width: '90%',
              // left:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              right: `${setData.leftRightpadding<='0'?(setData.leftRightpadding=='0'?setData.leftRightpadding:(-(setData.leftRightpadding*2-50))):(setData.leftRightpadding*2+38)}rpx`,
              top:'5px',
            }"
          >
            <div
              v-if="setData.shadow!='0'"
              style=" z-index: 9999"
              :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }"
              >
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)"/>
          </view>
          <view v-else class="item-center"
          :style="{
            width: `${setData.mainWidth}%`,
              textAlign: `center`,
            }"
          >
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)"/>
          </view>
        </view>

        <view class="image-container" v-else-if="curIndex===listLen-1">
          <view v-if="i===0" class="item-right"
          :style="{
              // width: '90%',
              // left:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              right: `${setData.leftRightpadding<='0'?(setData.leftRightpadding=='0'?setData.leftRightpadding:(-(setData.leftRightpadding*2-50))):(setData.leftRightpadding*2+38)}rpx`,
              top:'5px',
            }"
          >
            <div
              v-if="setData.shadow!='0'"
              style=" z-index: 9999"
              :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }"
              >
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)"/>
          </view>
          <view v-else-if="i===listLen-2" class="item-left" 
          :style="{
              // width: '90%',
              // right:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              top:'5px',
              left: `${setData.leftRightpadding<='0'?(58+Math.abs(setData.leftRightpadding*2)):(setData.leftRightpadding*2+38)}rpx`,
            }"
          >
            <div
              v-if="setData.shadow!='0'"
              style=" z-index: 9999"
              :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }"
              >
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
            }" @tap="jumpPage(item.pageUrl)"/>
          </view>
          <view v-else class="item-center"
          :style="{
              width: `${setData.mainWidth}%`,
              textAlign: `center`,
            }"
          >
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)"/>
          </view>
        </view>

        <view class="image-container" v-else>
          <view v-if="i===curIndex-1" class="item-left"
          :style="{
              // width: '90%',
              // right:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              top:'5px',
              left: `${setData.leftRightpadding<='0'?(58+Math.abs(setData.leftRightpadding*2)):(setData.leftRightpadding*2+38)}rpx`,
            }"
          >
            <div
              v-if="setData.shadow!='0'"
              style=" z-index: 9999"
              :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }"
              >
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)"/>
          </view>
          <view v-else-if="i===curIndex+1" class="item-right"
          :style="{
              // width: '90%',
              // left:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              right: `${setData.leftRightpadding<='0'?(setData.leftRightpadding=='0'?setData.leftRightpadding:(-(setData.leftRightpadding*2-50))):(setData.leftRightpadding*2+38)}rpx`,
              top:'5px',
            }"
          >
            <div
              v-if="setData.shadow!='0'"
              style=" z-index: 9999"
              :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }"
              >
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)"/>
          </view>
          <view v-else class="item-center"
          :style="{
              width: `${setData.mainWidth}%`,
              textAlign: `center`,
            }"
          >
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)"/>
          </view>
        </view>
      </swiper-item>
    </swiper>
      <!-- 商品式展示 -->
      <view v-else>
        <swiper-components :setData="setData" :list="[]" @getGroupsGoods="reachEnd"></swiper-components>
      </view>
  
  </view>
</template>
<script>
import formatPrice from "@/components/format-price/index.vue"
import swiperComponents from "./swiper-components.vue"
import { gotoPage } from "../div-base/div-page-urls.js";
export default {
    components: {
      formatPrice,
      swiperComponents
    },
    props: {
    setData: {
      type: Object,
      default () {
        return {
          indicatorDots: true,
          indicatorColor: '#cccccc',
          indicatorActiveColor: '#ffffff',
          interval: 5000,
          duration: 1000,
          circular: true,
          previousMargin: '58rpx',//前边距，可用于露出前一项的一小部分，接受 px 和 rpx 值
          nextMargin: '58rpx',//后边距，可用于露出后一项的一小部分，接受 px 和 rpx 值
        }
      }
    },
    // 图片在X轴展示的效果
    scaleX: {
      type: String,
      default: (375 / 317).toFixed(4)
    },
    // 图片在Y轴展示的效果
    scaleY: {
      type: String,
      default: (375 / 317).toFixed(4)
    },
    //判断是否还有下一页
    loadmore:{
      type: Boolean,
      default: true
    }
  },
  computed:{
    listLen () {
      return this.setData.swiperList.length
    },
    dataList(){
      console.log("this.setData.goodsFromType",this.setData.goodsFromType);
      this.setData.goodsList.forEach((ele, index) => {
        let surplus = ''
        if (ele.estimatedPriceVo) {
          if (ele.estimatedPriceVo.originalPrice != ele.estimatedPriceVo.estimatedPrice) {
            surplus = ele.estimatedPriceVo.originalPrice - ele.estimatedPriceVo.estimatedPrice
            this.$set(this.setData.goodsList[index], 'surplus', surplus)
            console.log("surplus", surplus);
          }
        }
      });
      return this.setData.goodsList
    },
    
    dataListLen () {
      return this.setData.goodsList.length
    },
  },

  data () {
    return {
      curIndex: 0,
      descIndex: 0,
    }
  },

  methods: {
    jumpPage (page) {
      if (page) {
        gotoPage(page);
      } else {
        // uni.showToast({
        //   title: '没有配置链接地址',
        //   icon: 'none',
        //   duration: 2000
        // });
      }
    },
    // 滑动到最后一页，请求下一页数据
    reachEnd(){
      this.$emit('getGroupsGoods');
    },

    swiperChange (e) {
      const that = this
      console.log("e.detail.current>>>>",e.detail)
      const currentIndex = e.detail.current;
      this.curIndex = e.mp.detail.current
      if (this.loadmore) {
        if (currentIndex === this.setData.goodsList.length - 1) {
          console.log("请求下一页数据");
          // 滑动到最后一页，请求下一页数据
          if (this.setData.goodsStyle=="2"&&this.setData.goodsPuantity) {
            return
          }
          // this.reachEnd();
        }
      }
    },
    getBannerDetail (id) {
      // uni.showLoading({
      // title: '将前往详情页面',
      // duration: 2000,
      // mask: true
      // })
	  const sourceModule = encodeURIComponent('轮播组件');
      uni.navigateTo({
		url: `/pages/goods/goods-detail/index?id=${id}&source_module=${sourceModule}`
	  });
    }
  }
}
</script>
<style lang="scss" scoped>
.banner-container {
  width: 100vw;

  .active {
      transform: scale(1);
      transition: all 0.2s ease-in 0s;
      z-index: 20;
  }
  .image{
    height: 386rpx;
    width: 386rpx;
  }
  // height: 90%;
  
  // swiper-item{
  //   height: 90% !important;
  // }
  .swiper-shadow{
    border-left: 10rpx solid #ccc;
    box-shadow: -10rpx 0 10rpx -10rpx rgba(0, 0, 0, 0.3);
    box-shadow: 0 30rpx 60rpx rgba(0,0,0,.3);
  }
  .card-icon {
    width: 106rpx;
    height: 93rpx;
    position: absolute;
    top: 0rpx;
    left: 0rpx;
  }
  .swiper-shadow{
    border-right: 10rpx solid #ccc;
    box-shadow: 10rpx 0 10rpx -10rpx rgba(0, 0, 0, 0.3);
    box-shadow: 0 30rpx 60rpx rgba(0,0,0,.3);
  }

  .image-container {
	box-sizing: border-box;
	width: 100%;
	// height: 100%;
	display: flex;

	.slide-image {
    width: 317rpx;
	  height: 156rpx;
	  z-index: 200;
	}
  }
  .item-left {
	justify-content: flex-end;
	margin: 14rpx 0rpx 0 0;
  }
  .item-right {
	justify-content: flex-start;
  margin: 14rpx 0 0 0rpx;
  }
  .item-center {
	justify-content: center;
	margin: 19rpx 0 0 0;
  }
  .desc-wrap {
    box-sizing: border-box;
    width: 100%;
    height: 135rpx;
    padding: 24rpx 66rpx 0;
    .title {
      width: 100%;
      height: 42rpx;
      line-height: 42rpx;
      color: #222222;
      font-size: 30rpx;
      font-weight: 600;
      // text-align: left;
    }
    .desc {
      margin-top: 4rpx;
      width: 100%;
      height: 51rpx;
      line-height: 51rpx;
      color: #282828;
      font-size: 24rpx;
    }
  }
  @keyframes descAnimation {
    0% {
      opacity: 1;
    }
    25% {
      opacity: .5;
    }
    50% {
      opacity: 0;
    }
    75% {
      opacity: .5;
    }
    100% {
      opacity: 1;
    }
  }
  @-webkit-keyframes descAnimation {
    0% {
      opacity: 1;
    }
    25% {
      opacity: .5;
    }
    50% {
      opacity: 0;
    }
    75% {
      opacity: .5;
    }
    100% {
      opacity: 1;
    }
  }
  .hideAndShowDesc {
    animation: descAnimation .3s ease 1;
    -webkit-animation: descAnimation .3s ease 1;
  }
}
</style>
