<template>
<view class="con-part2-con" >
    <swiper 
    class="swiper-tall"
    :style="{height: `${42+setData.height*2}rpx`}"
    :indicator-dots="setData.dotStyle == '' ? false : true " 
    :indicator-color="'#cccccc'" 
    :indicator-active-color="'#ffffff'"
    :autoplay="true" 
    :interval="setData.interval?setData.interval:3000" 
    :duration="setData.duration?setData.duration:1000" 
    :circular="true"
    :previous-margin="(190-setData.leftRightpadding)+'rpx'"
    :next-margin="(190-setData.leftRightpadding)+'rpx'" 
    current='0' 
    @change="swiperChange">
        <swiper-item class="con-part2-con-container"
        v-for="(item, i) in dataList" :key="i"
        >
            <view :class="['slide-image', curIndex === i?'active':(setData.shadow=='0'?'':'bg-shadow')]"
                :style="{position: 'relative',background:'url('+ item.pic +') center no-repeat',backgroundSize:'100%'}"
                @tap="getBannerDetail(item.id)"
                >
                <!-- 角标配置 -->
                <view
                  class="card-icon"
                  v-if="setData"
                  :style="{
                    top: `${setData.imgTop*2}rpx`,
                    left: `${setData.imgLeft*2}rpx`,
                  }"
                >
                  <view class="card-icon">
                    <image
                      v-if="item.estimatedPriceVo"
                      style=" width: 100rpx !important;height: 100rpx !important;"
                      mode="widthFix"
                      :src="setData.imageUrls"
                    />
                    <view
                      v-if="item.estimatedPriceVo&&(item.estimatedPriceVo.totalDiscountPrice&&item.estimatedPriceVo.totalDiscountPrice!='0'&&item.estimatedPriceVo.totalDiscountPrice!='0.0')&setData.ShowPrice==1"
                      class="text-bold text-white text-sm"
                      :style="{
                            position: 'absolute',
                            bottom: '16rpx',
                            left: '0rpx',
                            textAlign: 'center',
                        }"
                    >
                      <view class="text-bold">
                        <format-price 
                          :color="'#FFFFFF'" 
                          signFontSize="20rpx" 
                          smallFontSize="24rpx" 
                          :priceFontSize="`${setData.topPriceSize*2}rpx`" 
                          :price="Number(item.estimatedPriceVo.totalDiscountPrice)" 
                        />
                      </view>
                    </view>
                  </view>
                </view>
                <view class="part2-con-img" 
                :style="{
                    borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
                    borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
                    borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
                    borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
                  }" >
                    <image :src="item.picUrls[0]" class="img"></image>
                </view>
                <view class="part2-con-txt2">
                  <!-- 商品信息 -->
                  <view style="background-color: #FFFFFF;">
                    <view class="text-sm text-black padding-lr-xs overflow-2">{{item.name}}</view>
                    <!-- 积分商品 -->
                    <view v-if="item.spuType == 3 || item.spuType == 5" class="margin-top-xs  margin-left-xs">
                    <text class="text-gray text-xs">积分：</text>
                      <text
                        class="text-red text-lg text-bold"
                      >{{item.goodsPoints ? item.goodsPoints : 0}}</text>
                      
                    </view>
                    <!-- 付费商品 -->
                    <view v-else class="flex padding-lr-xs" 
                      style="position: relative;justify-content: space-around;">
                        <view>
                          <view class=" text-gray text-sm">
                            原价
                          </view>
                          <view class="text-gray">
                            <format-price 
                              v-if="item.estimatedPriceVo"
                              styleProps="line-height: 36rpx;"
                              :color="'#AAAAAA'"  
                              signFontSize="20rpx" 
                              smallFontSize="24rpx" 
                              priceFontSize="34rpx" 
                              :price="Number(item.estimatedPriceVo.originalPrice)" 
                            />
                          </view>
                        </view>
      
                        <view>
                          <view class=" text-sm text-red">
                            预估到手价
                          </view>
                          <view class="text-bold text-red">
                            <format-price 
                              v-if="item.estimatedPriceVo"
                              styleProps="line-height: 36rpx;"
                              :color="'#FF0000'"  
                              signFontSize="20rpx" 
                              smallFontSize="24rpx" 
                              priceFontSize="34rpx" 
                              :price="Number(item.estimatedPriceVo.estimatedPrice)" 
                            />
                          </view>
                        </view>
                    </view>
                  </view>
                </view>
            </view>
        </swiper-item>
    </swiper>
</view>


</template>
<script>
import formatPrice from "@/components/format-price/index.vue"
import { gotoPage } from "../div-base/div-page-urls.js";
export default {
  components: {
    formatPrice
  },
  props: {
    list: {
      type: Array,
      default () {
        return []
      }
    },
    setData: {
      type: Object,
      default () {
        return {
          indicatorDots: true,
          indicatorColor: '#cccccc',
          indicatorActiveColor: '#ffffff',
          interval: 3000,
          duration: 1000,
          circular: true,
          goodsList: [],
          previousMargin: '58rpx',//前边距，可用于露出前一项的一小部分，接受 px 和 rpx 值
          nextMargin: '58rpx'//后边距，可用于露出后一项的一小部分，接受 px 和 rpx 值
        }
      }
    },
    //判断是否还有下一页
    loadmore:{
      type: Boolean,
      default: true
    }
  },
  computed:{
    listLen () {
      return this.setData.swiperList.length
    },
    dataList(){
      if(!this.setData.goodsList) {
        return []
      }
      console.log("this.setData.goodsList",this.setData.goodsList);
      return this.setData.goodsList
    },
    
    dataListLen () {
      return this.setData.goodsList.length
    },
  },

  data () {
    return {
      curIndex: 0,
    }
  },

  methods: {
    jumpPage (page) {
      if (page) {
        gotoPage(page);
      } else {
        // uni.showToast({
        //   title: '没有配置链接地址',
        //   icon: 'none',
        //   duration: 2000
        // });
      }
    },
    partSwiperChange(event) {
      this.partcurrentIndex = event.detail.current
    },

    // 滑动到最后一页，请求下一页数据
    reachEnd(){
      this.$emit('getGroupsGoods');
    },

    swiperChange (e) {
      const that = this
      // console.log("e.detail.current>>>>",e.detail)
      const currentIndex = e.detail.current;
      this.curIndex = e.mp.detail.current
      if (this.loadmore) {
        if (currentIndex === this.setData.goodsList.length - 1) {
          console.log("请求下一页数据");
          // 滑动到最后一页，请求下一页数据
          if (this.setData.goodsStyle=="2"&&this.setData.goodsPuantity) {
            return
          }
          this.reachEnd();
        }
      }
    },

    //跳转到详情页
    getBannerDetail (id) {
      // uni.showLoading({
      // title: '将前往详情页面',
      // duration: 2000,
      // mask: true
      // })
	  const sourceModule = encodeURIComponent('轮播组件');
      uni.navigateTo({
				url: `/pages/goods/goods-detail/index?id=${id}&source_module=${sourceModule}`
	  });
    }
  }
}
</script>
<style lang="scss" scoped>
.con-part2-con {
    width: 100%;
    // height: 390rpx;

    .swiper-tall {
        display: flex;
        align-items: center;
        height: 100%;

        .con-part2-con-container {
            // display: flex;
            // align-items: center;
            // width: 50% !important;

          .card-icon {
            width: 106rpx;
            height: 93rpx;
            position: absolute;
            top: 0rpx;
            left: 0rpx;
            z-index: 21;
          }
          .bg-shadow{
            width: 100%;
            height: 100%;
            background-color:#000;
            opacity: 0.5;
          }
            .slide-image {
              height: 386rpx;
              width: 386rpx;
                z-index: 1;
                margin: 0 auto;
                padding-top: 12rpx;

                .part2-con-title {
                    text-align: center;
                    font-size: 22rpx;
                    font-weight: 400;
                    color: #3596F1;
                }

                .part2-con-img {
                    margin-top: 13rpx;
                    width: 356rpx;
                    height: 356rpx;
                    margin: 0 auto;

                    .img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .part2-con-txt1-img {
                    display: block;
                    margin: 10rpx auto 0rpx;
                    height: 28rpx;
                    width: 170rpx;
                }

                .part2-con-txt2 {
                  text-align: left;
                  font-weight: 500;
                  width: 374rpx;
                  padding: 10rpx;
                  padding-top: 0px;
                  margin: 0 auto;
                }
                .transform-txt{
                  transform:none !important;
                  padding-bottom: 10rpx;
                }
            }

            .active {
                transform: scale(1.14);
                transition: all 0.3s ease-in 0.1s;
                z-index: 20;
            }
        }
    }
}

</style>
