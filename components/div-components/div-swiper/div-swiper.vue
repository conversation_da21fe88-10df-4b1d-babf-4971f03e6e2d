<template>
  <!-- 轮播图组件 -->
  <!-- <view :class="'bg-'+theme.backgroundColor"> -->
  <view class="bg-white">
    <view
      v-show="newData.swiperType == 'card-swiper'"
      :class="'bg-' + theme.backgroundColor"
    ></view>

    <view :style="{
        backgroundImage: `${`url(${newData.bgImage})`}`,
        backgroundColor: newData.background,
        backgroundSize: '100% auto',
        backgroundRepeat:'no-repeat',
        padding: `${`${newData.paddingTopSpacing * 2}rpx ${
          newData.paddingRightSpacing * 2
        }rpx ${newData.paddingBottomSpacing * 2}rpx ${
          newData.paddingLeftSpacing * 2
        }rpx`}`,
      }">
      <view
        style="width: 100%"
        :style="{
	        padding: `${
	        newData.swiperType == 'card-swiper' ? '0 28rpx 0 28rpx' : ''
	        }`,
					overflow:'hidden',
          transform: 'translateY(0)',
					borderTopLeftRadius:`${newData.swiperType =='3D-swiper'?'':(newData.borderRadius == 1 ? (newData.radiusLeftTop*2)+'rpx':'0rpx')}`,
					borderTopRightRadius:`${newData.swiperType =='3D-swiper'?'':(newData.borderRadius == 1 ? (newData.radiusRightTop*2)+'rpx' : '0rpx')}`,
          borderBottomLeftRadius:`${newData.swiperType =='3D-swiper'?'':(newData.borderRadius == 1 ? (newData.radiusLeftBottom*2)+'rpx':'0rpx')}`,
          borderBottomRightRadius:`${newData.swiperType =='3D-swiper'?'':(newData.borderRadius == 1 ?(newData.radiusRightBottom*2)+'rpx':'0rpx')}`,
	        }"
      >
        <view v-if="newData.swiperType!=='3D-swiper'">
          <swiper
            class="screen-swiper"
            :class="newData.dotStyle == 'square-dot' ? 'square-dot':(newData.dotStyle == 'round-dot' ? 'round-dot': '')"
            :indicator-dots="true"
            :circular="true"
            :autoplay="true"
            :interval="newData.interval"
            duration="500"
            @change="cardSwiper"
            indicator-color="#cccccc"
            indicator-active-color="#ffffff"
            :style="{ 
                height: `${newData.height * 2}rpx`
               }"
          >
            <swiper-item
              v-for="(item, index) in newData.swiperList"
              :key="index"
              :class="cardCur == index ? 'cur' : ''"
              @tap="jumpPage(item.pageUrl)"
            >
              <image
                :src="item.imageUrl | formatImg750"
                mode="scaleToFill"
                :style="{
                  height: `${newData.height * 2}rpx`
                }"
              ></image>
            </swiper-item>
          </swiper>
        </view>

        <view v-else>
		      <special-banner :setData="newData" @getGroupsGoods="getGroupsGoods" :loadmore="loadmore"></special-banner>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import { gotoPage } from "../div-base/div-page-urls.js";
import specialBanner from "./swiper3D.vue";
import api from '@/utils/api'
export default {
  props: {
    value: {
      type: Object | null,
      default: function () {
        return {
          swiperType: 'screen-swiper',
          height: 150,
          interval: 3000,
          borderRadius: 0,
          imageSpacing: 0,
          swiperList: []
        }
      }
    }
  },
  watch: {
			value: {
				handler(newVal, oldVal) {
          console.log("swiper开始数据");
        let newVals = JSON.parse(JSON.stringify(newVal))
			   if (newVals.goodsFromType == 1) { //处理商品名称修改 
					} else {
						/***
						 *  处理 如果类型为 分组 把 groupingList 字段 中id提前处理并赋值
						 */
						if (newVals.goodsFromType == 7) {
								let groupsId = [];
								newVals.groupingList.forEach(item => {
									groupsId.push(item.id)
								});
								newVals.groupingList = groupsId.join(",");
                console.log("------->请求下一页数据")
                this.getGroupsGoods()
							}
						newVals.goodsList = [];
					}
					//大于20就分页展示
					if (newVals.goodsList && newVals.goodsList.length > 20) {
						this.unLoadGoods = newVals.goodsList;
						newVals.goodsList = [];
						const subArray = this.unLoadGoods.splice(0, newVals.isSwiper?11:10);
						if (subArray && subArray.length > 0) {
							subArray.forEach(item => {
								newVals.goodsList.push(item);
							})
						}
					}
					this.newData = newVals;
				},
				immediate: true,
        deep: true
			}
		},
  components: {
    specialBanner
  },
  data () {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      cardCur: 0,
      //获取店铺商品的分页查询
			page: {
				searchCount: false,
				current: 0,
				size: 10,
				ascs: '',
				//升序字段
				descs: ''
			},
			recordCurrentPage: 3, //记录当前页
      loadmore: true,
      //如果手动配置好的商品很多，也需要分页去加载
			//还没加载的商品
			unLoadGoods: []
    };
  },
  methods: {
    /**
     * 获取分组商品
     * */
		getGroupsGoods () {
			console.log("this.newData===>",this.value);
			this.page.current++;
			if (this.value.goodsStyle==2&&this.value.goodsPuantity) {
				this.page.size = this.value.goodsPuantity
			}
			api.goodsGroupsGet(
				Object.assign(
					this.page, {
					goodsGroupIds: this.value.groupingList
				}
				)
			).then(res => {
				// console.log(res)
				const data = res.data?.records || []
				//记录第一页数据长度
				if (res.data && res.data.current == "1") {
					this.recordCurrentPage = data.length
				}
				console.log(res, data, data.length, '========商品分组======')
				if (res && res.data && res.data.records && res.data.records.length > 0) {
					this.newData.goodsList = this.newData.goodsList.concat(data);
					if (this.newData.goodsList.length === res.data.total) {
						this.loadmore = false;
					}
				}
			})
		},

    cardSwiper (e) {
      this.cardCur = e.detail.current
    },

    jumpPage (page) {
      if (page) {
        gotoPage(page);
      } else {
        // uni.showToast({
        //   title: '没有配置链接地址',
        //   icon: 'none',
        //   duration: 2000
        // });
      }
    }
  }
}
</script>

<style scoped lang="scss">
.screen-swiper {
  min-height: 90upx !important;
}
</style>
