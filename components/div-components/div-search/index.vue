<template>
	<!-- 商品分类组件 -->
	<view class="div-search" >
		<cu-custom v-if="isStatusBar" :bgColor="newData.background" :bgImage="newData.bgImage"
			:isBack="isBack" 
			:leftTitleProps="{
				  showLeftTitle:newData.showLeftTitle,
				  leftTitleColor:newData.leftTitleColor,
				  leftTitleSize:newData.leftTitleSize,
				  leftTitle:newData.leftTitle,
				  leftTitleWidth:newData.leftTitleWidth,
				  }" >
			<block slot="backText">返回</block>
			<block slot="marchContent">
				<search v-model="newData" :isStatusBar="isStatusBar" 	:isBack="isBack" ></search>
			</block>
		</cu-custom>

		<search v-else v-model="newData" :isStatusBar="isStatusBar"></search>
	</view>

</template>

<script>
	import search from './div-search.vue';

	export default {
		components: {
			search
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
					}
				}
			},
			isStatusBar: {
				type: Boolean,
				default: false
			},
		
			isBack: {
				type: <PERSON>olean,
				default: false
			},
		},
		data() {
			return {
				newData: this.value,
			};
		},
		
	}
</script>



