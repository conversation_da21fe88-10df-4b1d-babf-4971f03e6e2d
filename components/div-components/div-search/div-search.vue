<template>
	<!-- 搜索组件 -->
	<view
		class="height100"
		:class="isStatusBar ? '' : newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''"
		:style="{
			marginBottom: `${isStatusBar ? 0 : Number(newData.marginBottomSpacing) * multipleView}rpx`,
			marginTop: `${isStatusBar ? 0 : newData.marginTopSpacing * multipleView}rpx`,
			marginLeft: `${newData.marginLeftSpacing * multipleView}rpx`,
			marginRight: `${newData.marginRightSpacing * multipleView}rpx`,
			height: `${computedHeight}`,
			width: isStatusBar
				? CanBack && isBack
					? `${windowWidth - menuWidth - leftMenuWidth - 17}px`
					: !CanBack && isBack
					? `${windowWidth - menuWidth - leftMenuWidth / 2 - 17}px`
					: `${windowWidth - menuWidth - 10}px`
				: '100%'
		}"
	>
		<view
			class="height100"
			:style="{
				backgroundColor: isStatusBar ? '' : newData.background,
				backgroundImage: isStatusBar ? '' : `url(${newData.bgImage})`,
				backgroundSize: `cover`,
				overflow: `hidden`,
				paddingBottom: `${isStatusBar ? 0 : newData.paddingBottomSpacing * multipleView}rpx`,
				paddingTop: `${isStatusBar ? 0 : newData.paddingTopSpacing * multipleView}rpx`,
				paddingLeft: `${newData.paddingLeftSpacing * multipleView}rpx`,
				paddingRight: `${newData.paddingRightSpacing * multipleView}rpx`
			}"
		>
			<view class="search content-s height100">
				<!-- 标题连接 -->
				<view
					class="height100"
					v-if="newData.showLeftTitle == 1"
					:style="{
						minWidth: `${newData.leftTitleWidth * 2}rpx`,
						color: `${newData.leftTitleColor}`,
						fontSize: `${newData.leftTitleSize * multipleView}rpx`
					}"
				>
					{{ newData.leftTitle }}
				</view>
				<!-- 标题图片 -->
				<view class="height100" v-else-if="newData.showLeftTitle == 2" style="display: flex; align-items: center">
					<image :style="{ maxHeight: `${computedHeight}`, width: `${newData.leftTitleWidth * multipleView}rpx` }" mode="aspectFit" :src="newData.LeftIcon"></image>
				</view>
				<!-- 搜索框 -->
				<view
					class="search-field round height100"
					:style="{
						backgroundColor: newData.color,
						borderRadius: `${newData.radius * 2}rpx`,
						color: `${newData.textColor}`,
						height: `${(isStatusBar ? typeHeight : newData.searchHeight) * multipleView}rpx`,
						maxHeight: '100%',
						border: `${newData.borderSize * 2}rpx solid ${newData.borderColor}`
					}"
				>
					<text v-if="!isStatusBar" class="cuIcon-search" style="padding: 0rpx 16rpx 0rpx 20rpx"></text>
					<view v-else class="height100" :style="{ display: 'flex', alignItems: 'center' }">
						<text @click="scanCode" class="cuIcon-scan text-xml padding-right-xs padding-left-sm"></text>
						<image :style="{ maxHeight: `48rpx`, width: `4rpx` }" mode="aspectFit" :src="$imgUrl('share/scan.png')"></image>
					</view>
					<view
						@click="navTo('/pages/base/search/index')"
						class="response height100"
						:style="{
							color: newData.textColor,
							'text-align': newData.textPosition == 'center' ? 'center' : 'left',
							marginLeft: newData.textPosition == 'center' ? '-25px' : '8rpx'
						}"
					>
						<swiper class="height100" :style="{ maxHeight: `${computedHeight}` }" vertical autoplay circular :interval="3000">
							<template v-for="(item, index) in newData.texts">
								<swiper-item v-if="item != '[object Object]'" :key="index" @click="placeholderText(item)" style="display: flex; align-items: center">
									<view class="overflow-1 text-df" style="color: #888888">{{ item }}</view>
								</swiper-item>
							</template>
						</swiper>
					</view>
					<view class="height100" :style="{ display: 'flex', alignItems: 'center' }"></view>
					<view @click="cameraImages" class="height100" :style="{ display: 'flex', alignItems: 'center' }">
						<image v-if="isStatusBar" :style="{ maxHeight: `48rpx`, width: `4rpx` }" mode="aspectFit" src="https://img.songlei.com/share/scan.png"></image>
						<text class="cuIcon-camera text-xml padding-right-sm padding-left-xs"></text>
						<image v-if="!isStatusBar" :style="{ maxHeight: `48rpx`, width: `4rpx` }" mode="aspectFit" src="https://img.songlei.com/share/scan.png"></image>
						<text v-if="!isStatusBar" @click="scanCode" class="cuIcon-scan padding-right-sm padding-left-xs"></text>
					</view>
				</view>
				<!-- #ifdef APP -->
				<view class="navIcon" style="padding-left: 20rpx" v-if="newData.showMessage == 1" @click="gotoMessageList">
					<view :style="{ color: newData.messageColor || '#ffffff' }">
						<text class="cuIcon-message text-xdf"></text>
						<view style="font-size: 18rpx">消息</view>
					</view>
					<view
						v-if="msgCount"
						class="total"
						:style="{
							display: 'flex',
							alignItems: 'center',
							justifyContent: 'center',
							width: '32rpx',
							height: '32rpx',
							color: newData.messageNumColor || '#ffffff',
							backgroundColor: newData.messageDotColor || '#ff0000'
						}"
					>
						{{ msgCount > 0 ? (msgCount > 99 ? '99+' : msgCount) : '' }}
					</view>
				</view>
				<!-- #endif -->
			</view>
		</view>
		<uni-popup ref="perpopup" type="center" :mask-click="false">
			<view class="permissions_box">
				当您使用APP时，扫一扫查看相关商品功能需要授权相机权限，获取本地图片需要获取访问设备照片权限，不授权上述权限，不影响APP其他功能使用。
			</view>
		</uni-popup>
		<camera-model :showPopup.sync="showPopup"></camera-model>
	</view>
</template>

<script>
import api from '@/utils/api';
import util from '@/utils/util.js';
import CameraModel from '@/components/camera-model/index';
import { mapState } from 'vuex';
const app = getApp();
// #ifdef APP-PLUS
const mpaasScanModule = uni.requireNativePlugin('Mpaas-Scan-Module');
// #endif
export default {
	name: 'basic-search',
	components: {
		CameraModel
	},
	props: {
		value: {
			type: Object | null,
			default: function () {
				return {
					background: `#efeff4`,
					color: '#ffffff',
					placeholder: '请输入关键字',
					radius: 38,
					textColor: '#999999',
					textPosition: `center`,
					texts: []
				};
			}
		},
		isStatusBar: {
			type: Boolean,
			default: false
		},

		isBack: {
			type: Boolean,
			default: false
		}
	},

	computed: {
		...mapState(['HeightBar', 'CustomBar', 'windowWidth', 'menuWidth', 'leftMenuWidth', 'multipleView']),
		computedHeight() {
			if (this.isStatusBar) {
				return this.HeightBar + 'px';
			} else {
				return 'auto';
			}
		},
		typeHeight() {
			let height = this.HeightBar;
			// #ifdef APP
			height = +height - +this.newData.borderSize * 2 - 4;
			// #endif

			return height;
		}
	},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value,
			searchText: '',
			msgCount: 0,
			CanBack: true,
			showPopup: false
		};
	},
	created() {
		this.getHitSearchList();
		// #ifdef MP-WEIXIN || MP-QQ
		let pages = getCurrentPages();
		if (pages.length <= 1 || pages[pages.length - 1].route == 'pages/login/index') {
			//判断是否能返回
			this.CanBack = false;
		} else {
			this.CanBack = true;
		}
		// #endif
		console.log('获取高度数据', this.HeightBar);
	},
	destroyed() {
		uni.$off('msg_unread');
	},
	methods: {
		//扫码
		scanCode() {
			// #ifdef MP-WEIXIN
			// 允许从相机和相册扫码
			uni.scanCode({
				scanType: ['barCode', 'qrCode'], //所扫码的类型 barCode	一维码 qrCode	二维码
				success: (res) => {
					if (res.result) {
						const code = res.result; //result	所扫码的内容 测试：6902902006811
						console.log('code', code);
						if (code) {
							if (code.indexOf('https://') > -1) {
								util.scanH5ToMaPage(code, api.qrcodequery);
								return;
							}
							api.goodsGetCode(code)
								.then((res) => {
									console.log('res', res);
									let goodsList = res.data;
									if (res.ok) {
										if (goodsList) {
											let data = JSON.stringify(goodsList);
											if (goodsList.length == '1') {
												//有一个商品就到商品详情页
												const sourceModule = encodeURIComponent('搜索');
												uni.navigateTo({
													url: `/pages/goods/goods-detail/index?id=${goodsList[0].id}&source_module=${sourceModule}`
												});
												return;
											}
											//有超过一个商品就展示到列表
											uni.navigateTo({
												url: `/pages/goods/goods-code-list/index?goodsList=${encodeURIComponent(data)}`
											});
										} else {
											//没有商品提示用户
											uni.showToast({
												title: `该商品不存在`,
												duration: 2000
											});
										}
									}
								})
								.catch((res) => {
									console.log('=res=', res);
								});
						}
					} else {
						console.log('请重新扫描');
						return false;
					}
				},
				fail: (res) => {
					console.log('未识别到二维码');
				}
			});
			// #endif

			// #ifdef APP-PLUS
			let that = this;
			// 允许从相机和相册扫码
			var platform = uni.getSystemInfoSync().platform;
			if (platform == 'android') {
				plus.android.checkPermission(
					'android.permission.CAMERA',
					(granted) => {
						if (granted.checkResult == -1) {
							//弹出
							that.$refs.perpopup.open('top');
							plus.android.requestPermissions(['android.permission.CAMERA', 'android.permission.WRITE_EXTERNAL_STORAGE'], (e) => {
								//关闭
								if (e.granted.length > 0) {
									//执行你有权限后的方法
									that.$refs.perpopup.close();
									that.appScan();
								}
								if (e.deniedAlways.length > 0) {
									//权限被永久拒绝
									uni.showModal({
										title: '提示',
										content: '扫码和识别本地图片权限被拒绝，是否前往开启权限',
										success: (res) => {
											if (res.confirm) {
												// 弹出提示框解释为何需要读写手机储存权限，引导用户打开设置页面开启
												var main = plus.android.runtimeMainActivity();
												var Intent = plus.android.importClass('android.content.Intent');
												//直接进入应用列表的权限设置
												var mIntent = new Intent('android.settings.APPLICATION_SETTINGS');
												main.startActivity(mIntent);
												that.$refs.perpopup.close();
											} else if (res.cancel) {
												console.log('用户点击取消');
												that.$refs.perpopup.close();
											}
										}
									});
								}
								if (e.deniedPresent.length > 0) {
									//权限被临时拒绝
									// 弹出提示框解释为何需要读写手机储存权限，可再次调用plus.android.requestPermissions申请权限
									plus.android.requestPermissions(['android.permission.CAMERA']);
									that.$refs.perpopup.close();
								}
							});
						} else {
							//执行你有权限后的方法
							that.appScan();
						}
					},
					(error) => {
						console.error('Error checking permission:', error.message);
					}
				);
			} else {
				//执行你有权限后的方法 ios
				that.appScan();
			}
			// #endif
		},

		appScan() {
			// #ifdef APP-PLUS
			// 允许从相机和相册扫码
			mpaasScanModule.mpaasScan(
				{
					// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
					scanType: ['qrCode', 'barCode'],
					// 是否隐藏相册，默认false不隐藏
					hideAlbum: false
				},
				(res) => {
					if (res.resp_code == 1000 && res.resp_message == 'success') {
						const code = res.resp_result; //result	所扫码的内容
						if (code) {
							if (code.indexOf('https://') > -1) {
								util.scanH5ToMaPage(code, api.qrcodequery);
								return;
							}
							api.goodsGetCode(code)
								.then((res) => {
									console.log('res', res);
									let goodsList = res.data;
									if (res.ok) {
										if (goodsList) {
											let data = JSON.stringify(goodsList);
											if (goodsList.length == '1') {
												const sourceModule = encodeURIComponent('搜索');
												//有一个商品就到商品详情页
												uni.navigateTo({
													url: `/pages/goods/goods-detail/index?id=${goodsList[0].id}&source_module=${sourceModule}`
												});
												return;
											}
											//有超过一个商品就展示到列表
											uni.navigateTo({
												url: `/pages/goods/goods-code-list/index?goodsList=${encodeURIComponent(data)}`
											});
										} else {
											//没有商品提示用户
											uni.showToast({
												title: `该商品不存在`,
												icon: 'none',
												duration: 2000
											});
										}
									}
								})
								.catch((res) => {
									console.log('=res=', res);
								});
						} else {
							uni.showToast({
								title: `未识别到内容`,
								icon: 'none',
								duration: 2000
							});
						}
					} else {
						// uni.showToast({
						// 	title: `未识别到信息`,
						// 	duration: 2000
						// });
					}
				}
			);
			// #endif
		},

		gotoMessageList() {
			uni.navigateTo({
				url: '/pages/message/list/index'
			});
		},

		//获取选中的底纹
		placeholderText(item) {
			let searchText = item;
			uni.setStorageSync('searchPlaceholderText', searchText);
		},
		//获取底纹列表
		getHitSearchList() {
			// 前两条显示最近搜索的内容
			let hitList = [];
			const searchHistory = uni.getStorageSync('searchHistory') ? uni.getStorageSync('searchHistory') : [];
			if (searchHistory && searchHistory.length) {
				typeof searchHistory[0]?.name === 'string' && searchHistory[0].name !== '[object Object]' && hitList.push(searchHistory[0].name);
				typeof searchHistory[1]?.name === 'string' && searchHistory[1].name !== '[object Object]' && hitList.push(searchHistory[1].name);
			}
			// 接着添加后台微页面配置的搜索项目，
			if (this.value.texts && this.value.texts.length) {
				this.value.texts.forEach((item) => {
					typeof item.name === 'string' && item.name !== '[object Object]' && hitList.push(item.name);
				});
			}

			// 然后添加接口返回的搜索高频词
			api.HitSearchList({
				hit: 10
			})
				.then((res) => {
					let addHitList = res.data.result;
					let searchPlaceholderRequestid = res.data.request_id;
					// 保存底纹id
					uni.setStorageSync('searchPlaceholderRequestid', searchPlaceholderRequestid);
					if (addHitList && addHitList.length) {
						addHitList.forEach((item) => {
							typeof item.name === 'string' && item.name !== '[object Object]' && hitList.push(item.name);
						});
					}
					// 数组去重复
					this.newData.texts = Array.from(new Set(hitList));
				})
				.catch((e) => {
					console.error(e);
					this.newData.texts = Array.from(new Set(hitList));
				});
		},
		navTo(url) {
			uni.navigateTo({
				url
			});
		},
		cameraImages() {
			this.showPopup = true;
		},
		cancelBtn() {
			this.showPopup = false;
		}
	}
};
</script>

<style scoped lang="scss">
.navIcon {
	display: flex;
	justify-content: space-around;
	text-align: center;
	align-content: center;
	font-size: 18px;
	color: #fff;

	.total {
		position: relative;
		right: 22rpx;
		height: 22rpx;
		width: 22rpx;
		text-align: center;
		font-size: 22rpx;
		border-radius: 50%;
		background-color: #f22230;
		color: #ffffff;
	}
}

.content-s {
	display: flex;
	position: relative;
	align-items: center;
	justify-content: space-between;
}

.search-field {
	font-size: 24rpx;
	color: #333333;
	flex: 1;
	display: flex;
	align-items: center;
}

.height100 {
	height: 100%;
}

.permissions_box {
	padding: 200rpx 30rpx 50rpx;
	background-color: #fff;
	color: #000000;
}

/* 弹出框 */
.songlei-popup {
	position: fixed;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.5);
	top: 0;
	left: 0;
}

.popup-box {
	width: 100vw;
	background-color: #fff;
	border-radius: 30rpx 30rpx 0 0;
	position: absolute;
	bottom: 0;
	left: 0;
}

.popup-item {
	text-align: center;
	color: #000;
	border-bottom: 1rpx solid #f1f1f1;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
