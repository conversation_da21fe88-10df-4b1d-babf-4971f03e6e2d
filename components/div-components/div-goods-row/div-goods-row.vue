<template>
	<!-- 商品显示组件-横向排列方式显示 -->
	<view :class="classCom" :style="{
	        marginBottom: `${Number(newData.marginBottomSpacing)*multipleView}rpx`,
	        marginTop: `${newData.marginTopSpacing*multipleView}rpx`,
	        marginLeft: `${newData.marginLeftSpacing*multipleView}rpx`,
	        marginRight: `${newData.marginRightSpacing*multipleView}rpx`,
	      }">
		<view class="wrapper-list-goods" :style="{
		   backgroundColor: newData.background,
		   backgroundImage: `url(${newData.bgImage})`,
		   backgroundSize: `cover`,
		   overflow: `hidden`,
		   paddingBottom: `${newData.paddingBottomSpacing*multipleView}rpx`,
		   paddingTop: `${newData.paddingTopSpacing*multipleView}rpx`,
		   paddingLeft: `${newData.paddingLeftSpacing*multipleView}rpx`,
		   paddingRight: `${newData.paddingRightSpacing*multipleView}rpx`,
		}">
			<view v-if="newData.titleShow == '1' || newData.moreType == '1'" class="flex align-center justify-between margin-bottom"
				>
				<view class="text-df margin-left-sm" :style="{color: `${newData.titleColor}`}">
					<text class="text-bold" v-if="newData.titleShow == '1'" :class="newData.titleIcon"></text>
					<text class="margin-left-xs" v-if="newData.titleShow == '1'">{{newData.title}}</text>
				</view>
				<div-base-navigator v-if="newData.moreType == '1'" :pageUrl="newData.pageUrl" hover-class="none"
					class="text-sm">更多<text class="cuIcon-right"></text></div-base-navigator>
			</view>
			<navigator hover-class="none" style="flex: 1;" class="titleBox"  v-if="newData.name"
				:url="'/pages/shop/shop-detail/index?id=' + newData.id">
				<view class="" style='display: flex;justify-content:space-between;align-items: center;'>
					<view style="display:flex;padding-bottom: 10rpx;">
                <view class="cuIcon-shop"></view>
						<text class="text-black text-bold margin-left-sm shop-name">{{newData.name}}</text>
					</view>
					<view class="" style="font-size: 25rpx;">
						进店看看 <text class="cuIcon-right text-sm"></text>
					</view>
				</view>
			</navigator>
			<view class="flex">
				<view>
					<image mode="aspectFill" style="width: 262rpx;height: 416rpx;" v-if="newData.bannerImage"
						class="margin-right-xs" :src="newData.bannerImage"></image>
				</view>

				<scroll-view class="scroll-view_x" scroll-x="true"  style="overflow:hidden;">
					<block v-for="(item, index) in newData.goodsList" :key="index">
						<view hover-class="none" @click="toPage('/pages/goods/goods-detail/index?id=' + item.id,'goodDetail', item, undefined, undefined, undefined, encodeURIComponent('商品行'))"
							class="item flex goods-box radius">
							<view class="img-box">
								<view :class="item && item.estimatedPriceVo && (item.estimatedPriceVo.discountPrice &&
                item.estimatedPriceVo.discountPrice != '0.0'&&
                item.estimatedPriceVo.discountPrice != '0') ||
                (item.estimatedPriceVo.promotionsDiscountPrice &&
                  item.estimatedPriceVo.promotionsDiscountPrice !=
                    '0.0'&&item.estimatedPriceVo.promotionsDiscountPrice !='0') ||
                (item.estimatedPriceVo.coupon &&
                  item.estimatedPriceVo.coupon != '0.0'
                  &&item.estimatedPriceVo.coupon != '0')?'activity-img-box':''" :style="{
                  borderColor
                }">
							
									<image lazy-load :lazy-load-margin="0" fade-show :src="item.picUrls | formatImg360"
										mode="aspectFill" height="280"></image>
											<member-icon v-if="item.memberLevelLimit&&item.memberLevelLimit!='101'" :memberLevelLimit = "item.memberLevelLimit">
											</member-icon>
								</view>
								<view style="position: absolute;
                  left: 0;
                  right: 0;" :style="{
                  top: spuPriceStyle==1? 0:'auto',
                  bottom: spuPriceStyle==1? 0:'-10rpx'
                }" v-if="
              item && item.estimatedPriceVo && (item.estimatedPriceVo.discountPrice &&
              item.estimatedPriceVo.discountPrice != '0.0'&&
              item.estimatedPriceVo.discountPrice != '0') ||
              (item.estimatedPriceVo.promotionsDiscountPrice &&
                item.estimatedPriceVo.promotionsDiscountPrice !=
                  '0.0'&&item.estimatedPriceVo.promotionsDiscountPrice !='0') ||
              (item.estimatedPriceVo.coupon &&
                item.estimatedPriceVo.coupon != '0.0'
                &&item.estimatedPriceVo.coupon != '0')">
									<good-price :goodsSpu="item" small></good-price>
								</view>
								<view class="sell-out" v-if="item.inventoryState==1">
									<view class="sell-out-text">售罄</view>
								</view>
							</view>
							<view class="text-cut text-black text-xsm padding-left-sm padding-top-sm"  :class="isIOS?'text-bold':'text-middle'">
								{{item.name}}
							</view>
										
							<!-- <view class="margin-top-xs text-sm text-gray padding-left-sm overflow-2">{{item.sellPoint}}</view> -->
							<!-- 积分商品 -->
							<view v-if="item.spuType == 3 || item.spuType == 5" class="margin-top-xs margin-left-sm ">
								<text class="text-gray text-xs">积分：</text>
								<text
									class="text-red text-lg text-bold">{{item.goodsPoints ? item.goodsPoints : 0}}</text>
							</view>
							
					
							<!-- 付费商品 -->
							<view v-else class="text-red margin-left-sm margin-top-xs text-lg text-bold">
								<!-- <text>{{item.priceDown}}</text> -->
                <price-handle :value="item.priceDown" signFont="24rpx" bigFont="28rpx" smallFont="22rpx">
                  <view slot="append" style="display: inline-block;">
                    <text v-if="item.estimatedPriceVo&&item.priceDown!=item.estimatedPriceVo.price"
                      class="text-red text-xs" style="font-weight: 500;padding-left: 6rpx">劵后价</text>
                    
                    <text v-if="item.inventoryState==1" class="text-gray text-xs"
                      style="font-weight: 500;padding-left: 6rpx">已售罄</text>
                    
                    <text
                      v-if="item.estimatedPriceVo&&item.estimatedPriceVo.originalPrice!='0.0'&&item.priceDown!= item.estimatedPriceVo.originalPrice"
                      class="price-original text-xs">￥{{ item.estimatedPriceVo.originalPrice }}</text>
                    
                    <text class="text-xs padding-lr-xs" style="color: #9c9ba1;font-weight: 300;">已售{{item.saleNum | saleNumFilter}}</text>
                  </view>
                </price-handle>
								
							</view>

						</view>
					</block>
          <view hover-class="none" class="item flex goods-box radius" style="background-color: #F1F1F1;">
            <div-base-navigator v-if="newData.moreType == '2'" :pageUrl="newData.pageUrl"
            	:styleProps="{backgroundColor: '#F1F1F1'}"
            	style="background-color: #F1F1F1; font-size: 24rpx;top: 0; box-shadow: 0;height: 344rpx;"
            	class="item flex goods-box radius">
            	<view style="position: absolute; top: 120rpx;">
            		<text style="padding-left: 68rpx;">更多全部</text>
            		<image style="width: 30rpx; height: 30rpx;vertical-align: text-top;"
            			:src="$imgUrl('1/material/8fe2787f-939e-4f08-be2a-5725d8a19894.png')">
            	</view>
            </div-base-navigator>
          </view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	import lazyLoad from "components/lazy-load/index";
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
	import goodPrice from "components/good-price/good-price.vue";
	import memberIcon from '@/components/member-icon/index.vue';
	import { mapState } from 'vuex';
	import utils from "@/utils/util.js"

	const app = getApp();
	import {
		navigateUtil
	} from "@/static/mixins/navigateUtil.js"
	export default {
		mixins: [navigateUtil],
		components: {
			lazyLoad,
			divBaseNavigator,
			goodPrice,
			memberIcon
		},
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {
						title: '商品甄选',
						titleColor: 'red',
						titleIcon: 'cuIcon-message',
						pageSpacing: 0,
						goodsList: [],
						background: "#fff",
						marginBottomSpacing: '0',
						marginBottomSpacing: '0'
					}
				}
			}
		},
		data() {
			return {
				isIOS: utils.isIOS(),
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
			};
		},
		computed: {
			...mapState(['isPhone', 'multipleView']),
			classCom() {
				return this.newData.background && this.newData.background.indexOf('bg-') != -1 ?
					this.newData.background :
					''
			},
			borderColor: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.spuPriceStyle == 1 ? '' : customFiled.spuPicBorder
				}
				return ""
			},
			spuPriceColor: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					if (customFiled.spuPriceStyle == 1) {
						return customFiled.spuPriceColor1 || '#fff'
					}
					return customFiled.spuPriceColor2 || '#fff'
				}
				return "#fff"
			},

			spuPriceStyle: function() {
				if (this.theme && this.theme.customFiled) {
					const customFiled = JSON.parse(this.theme.customFiled);
					return customFiled.spuPriceStyle || 2
				}
				return 2
			},
		},
		
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},

		}
	}
</script>

<style scoped lang="scss">
	.wrapper-list-goods {
		white-space: nowrap;
		// padding: 10rpx;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 260rpx;
		// height: 500rpx;
		height: auto;
		// margin-top: 20px;
		padding-bottom: 10px;
		// margin-left: 10rpx;
		margin-right: 10rpx;
		background-color: #fff;
		box-shadow: 0px 0px 20px #e5e5e5;
	}
	

	.wrapper-list-goods .item .img-box {
		width: 100%;
		height: 280rpx;
		max-height: 280rpx;
		overflow: hidden;
		position: relative;
	}

	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 280rpx;
		// border-radius: 5rpx 5rpx 0 0;
	}

	.activity-img-box {
		width: 100%;
		border-left: solid 6rpx transparent;
		border-top: solid 6rpx transparent;
		border-right: solid 6rpx transparent;
	}

	.goods-box {
		overflow: hidden;
	}
	
	
</style>
