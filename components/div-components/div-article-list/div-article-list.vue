<template>
	<!-- 文章列表组件 -->
	<view :style="{
	  marginBottom: `${newData.marginBottomSpacing*2}rpx`,
	  marginTop: `${newData.marginTopSpacing*2}rpx`,
	  marginLeft: `${newData.marginLeftSpacing*2}rpx`,
	  marginRight: `${newData.marginRightSpacing*2}rpx` }" :class="
	    newData.background&& newData.background.length>0 && newData.background!=undefined
	      ? newData.background
	      : ''
	  ">
		<view :style="{
		backgroundColor: newData.background,
		backgroundSize: 'cover',
		paddingBottom: `${newData.paddingBottomSpacing}px`,
		paddingTop: `${newData.paddingTopSpacing}px`,
		paddingLeft: `${newData.paddingLeftSpacing}px`,
		paddingRight: `${newData.paddingRightSpacing}px`,
	}">
			<scroll-view scroll-x class="nav" style="margin: 12rpx 0 6rpx 0;">
				<view class="menu-bar">
					<!-- <view class="menu-item" :class="-1==tabCur?'select':''" @tap="getHot()">热门 -->
					<!-- </view> -->
					<view class="menu-item" :class="index==tabCur?'select':''"
						v-for="(item, index) in articleCategoryList" :key="index" @tap="tabSelect(item.id, index)">
						{{item.name}}
					</view>
				</view>
			</scroll-view>

			<template v-if="articleList&&articleList.length>0">
				<view class="goods-container flex">
					<view style="width: 50%" v-for="(item, index) in articleList" :key="index">
						<view style="width: 100%">
							<view :style="{
								padding: `${newData.liveSpace * 2}rpx`,
							  }">
								<view class="goods-box">
									<navigator hover-class="none" style="background-color: #ffffff"
										:url="'/pages/article/article-info/index?id=' + item.id">
										<view class="img-box" v-if="">
											<lazy-load 
											    :borderRadius="12"
												:image="item.picUrl | formatImg360"
												:markUrls="item.goodsMarkInfoVo?item.goodsMarkInfoVo.angleMarkUrl:''">
											</lazy-load>
										</view>

										<view class="cu-list"
											style="background-color: #FFFFFF; padding: 20rpx; border-radius: 0 0 20rpx 20rpx ">
											<view class="text-black overflow-2" style="height: 78rpx; line-height: 37rpx;">{{item.articleTitle}}
											</view>
											<view class="text-gray text-sm flex justify-between margin-top-xs">
												<view style="display: flex;">
													<image style="width: 30rpx; height: 30rpx; border-radius: 50%; "
														:src="item.picUrl">
													</image>
													<view class="text-xs text-gray" style="margin-left: 10rpx;">
														{{item.authorName}}
													</view>
												</view>

												<!-- <view class="text-xs text-gray">{{item.updateTime}}</view> -->
											</view>
										</view>
									</navigator>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>
			<div-no-data v-else />
          	<view  v-if="loadmore" :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
		</view>
	</view>
</template>

<script>
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
	import divNoData from '../../base-nodata/index.vue'
	import lazyLoad from "components/lazy-load/index";
	import {
		EventBus
	} from '@/utils/eventBus.js'
	export default {
		name: 'basic-image',
		props: {
			value: {
				type: Object | null,
				default: function() {
					return {}
				}
			}
		},
		components: {
			divBaseNavigator,
			divNoData,
			lazyLoad
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,

				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				loadmore: true,
				articleListBanner: [],
				articleList: [],
				articleCategoryList: [],
				tabCur: 0,
				categoryId: null,
			};
		},
		created() {
		  EventBus.$on("divGoodsGroupsReachBottom", () => {
		  	this.reachBottom();
		  })	
		},
		mounted() {
			//查出热门文章
			// this.getHot()
			//查出文章分类
			this.articleCategoryPage()
		},
		methods: {
			handleClickMenu(item) {
				this.selectMenuId = item.id;
				console.log("selectMenuId===", this.selectMenuId)
			},
			
			reachBottom() {
				if (this.loadmore) {
					this.page.current = this.page.current + 1;
					this.articlePage();
				}
			},

			articlePage() {
				api.articlePage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let articleList = res.data.records;
					this.articleList = [...this.articleList, ...articleList];
					if (articleList.length < this.page.size || this.page.size==0) {
						this.loadmore = false;
					}
				});
			},
			articleCategoryPage() {
				api.articleCategoryPage({
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: ''
				}).then(res => {
					let articleCategoryList = res.data.records;
					this.articleCategoryList = articleCategoryList;
					if (articleCategoryList && articleCategoryList.length > 0) {
						this.tabSelect(articleCategoryList[0].id, 0);
					}else {
						this.loadmore = false;
					}
				});
			},
			// getHot() {
			// 	this.tabCur = -1
			// 	this.articleList = []
			// 	this.loadmore = true
			// 	this.page.current = 1
			// 	this.parameter = {
			// 		categoryId: null,
			// 		isHot: '1'
			// 	}
			// 	this.articlePage()
			// },
			tabSelect(id, index) {
				this.tabCur = index
				this.articleList = []
				this.loadmore = true
				this.page.current = 1
				this.parameter = {
					categoryId: id,
					isHot: null
				}
				this.articlePage()
			},
			jumpPage(id) {
				uni.navigateTo({
					url: '/pages/article/article-info/index?id=' + id
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.goods-container {
		justify-content: space-between;
		flex-wrap: wrap;
		box-sizing: content-box;
		/* padding: 20rpx; */
	}

	.menu-bar {
		display: flex;
		background: #f0f0f0;

		.menu-item {
			background-color: #fff;
			font-size: 12px;
			color: #404040;
			height: 46rpx;
			/* width: 104rpx; */
			line-height: 46rpx;
			border-radius: 46rpx;
			text-align: center;
			margin: 0 6rpx;
			width: 229rpx;
		}

		.select {
			background: linear-gradient(0deg, #CDAD90 0%, #CDA185 100%);
			color: #fff;
		}
	}

	.goods-box {
		width: 100%;
		/* height: 566rpx; */
		background-color: #fff;
		overflow: hidden;
		/* margin-bottom: 20rpx; */
		border-radius: 12rpx;
		/* box-shadow: 0px 0px 30px #e5e5e5; */
	}

	.goods-box .img-box {
		width: 100%;
		height: 370rpx;
		overflow: hidden;
	}

	.goods-box .img-box image {
		width: 100%;
		height: 349rpx;
	}
</style>
