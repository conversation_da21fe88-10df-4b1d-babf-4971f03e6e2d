<template>
	<!-- 秒杀 -->
	<view class="bg-white" v-if="listSeckillGoodsInfo&&listSeckillGoodsInfo.length > 0">
		<view :style="{  
			  marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
			  marginTop: `${newData.marginTopSpacing * 2}rpx`,
			  marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
			  marginRight: `${newData.marginRightSpacing * 2}rpx`,
			  height:  newData.showType==2? '100%':'460rpx'
			}" class="seckill-bg" >

			<view class="wrapper-list-goods radius" :style="{backgroundColor: newData.background,
			  paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
			  paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
			  paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
			  paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
			  height:  newData.showType==2? '100%':'440rpx',
			  backgroundImage: `url(${newData.backgroundImg})`,
			  backgroundSize: '100%'
			  }"
				:class="newData.background? newData.background.indexOf('bg-') != -1 ? newData.background : '': '' ">
				<view class="cu-bar ">
					<view class="goods-selection text-df margin-top">
						<view class="margin-left-xs text-bold"
							:style="{color: `${newData.titleColor}`, fontSize: `${newData.titleSize}px`}">
							{{newData.title}}
							<text
								:style="{color: `${newData.subtitleColor}`, fontSize:'20rpx', marginLeft:'30rpx'}">{{seckillState}}</text>
							<count-down v-if="outTime>0" :outTime="outTime" :textColor="'white'"
								:backgroundColor="'red'" :connectorColor="'red'" @countDownDone="countDownDone">
							</count-down>
						</view>
						<view class="margin-left-xs text-sm"
							:style="{marginTop:'10rpx',color: `${newData.subtitleColor}`, fontSize: `${newData.subtitleSize}px`}">
							{{newData.subtitle}}
						</view>
					</view>
					<navigator url="/pages/seckill/seckill-list/index"
						class="bg-white round margin-top-sm text-sm margin-right-xs seckill-more">更多</navigator>
				</view>
				<view class="seckill-list">
					<view v-if="listSeckillGoodsInfo&&listSeckillGoodsInfo.length > 0 && newData.showType==2"
						style="margin-top: 20rpx;display: flex;justify-content: space-between; flex-wrap: wrap; box-sizing: content-box;position:relative;">
						<navigator hover-class="none"
								:url="'/pages/seckill/seckill-detail/index?seckillHallInfoId=' + item.seckillHallInfoId+'&source_module=秒杀组件'" v-for="(item, index) in listSeckillGoodsInfo" :key="index"
							style="width:50%;  margin-bottom:10px;">
							<view style="width:100%">
								<view :style="{margin:`${newData.lineSpacing}px`, backgroundColor:'#fff',  borderRadius: '20rpx' }">
									<view class="img-box" style="height: 370rpx;border-radius: 20rpx;">
										<image class="bg-white" style="border-radius: 20rpx;height: 370rpx;" mode="aspectFill" :src="item.picUrl | formatImg360"></image>
									</view>
									
									<view style="padding: 10rpx 20rpx 10rpx 20rpx; background: #fff;    border-radius: 0 0 20rpx 20rpx;">
										<view class=" margin-top-xs overflow-1"
											style="color: #000; width: 300rpx; height: 88rpx; 
											display:inline-block; word-wrap: break-word;
											font-size: 32rpx;word-break: break-all; text-overflow: ellipsis;
											white-space: pre-wrap;  -webkit-line-clamp: 2;display: -webkit-box;              
											-webkit-box-orient: vertical;overflow: hidden;ext-overflow: ellipsis;">
											{{item.name}}            
										</view>	
									
										<view style="display:flex; justify-content:space-between; align-items:center; padding-bottom: 6px">
											<view style="display:flex; align-items:center;   margin-top: 6px;">
											  <view :style="{fontSize: '20px',color: `#f44`}" class="text-price ">{{item.seckillPrice}}</view>
												 <text v-if="item.salesPrice>0 || (item.goodsSku&&item.goodsSku.salesPrice>0)"  class="price-original"  style=" margin-left:10px"> ￥{{item.salesPrice ||item.goodsSku.salesPrice}} </text>
											  </view>
											  <view style=" margin-top: 10px;border-radius: 10px;background-color:#f44; color:#fff; font-size:12px; padding:2px 10px;">马上抢</view>
										</view>
									</view>
								</view>
							</view>
						</navigator>
					</view>

					<scroll-view v-else-if="listSeckillGoodsInfo&&listSeckillGoodsInfo.length > 0 "
						class="scroll-view_x goods-detail margin-top-xs" scroll-x>
						<block v-for="(item, index) in listSeckillGoodsInfo" :key="index">
							<navigator hover-class="none"
								:url="'/pages/seckill/seckill-detail/index?seckillHallInfoId=' + item.seckillHallInfoId+'&source_module=秒杀组件'"
								class="item margin-left-xs margin-right-xs">
								<view class="img-box">
									<image class="bg-white" :src="item.picUrl | formatImg360"></image>
								</view>
								<view >
									<view class="text-cut text-sm margin-top-xs" :style="{color: `#000`}">{{item.name}}</view>
									<!-- <view :style="{color: `${newData.subtitleColor}`}"
										class="sm margin-top-xs cu-tag line-white radius">限量 {{item.limitNum}}</view> -->
										<view style="display:flex; align-items:center;   margin-top: 6px;">
													 <view :style="{fontSize: '30rpx',color: `rgb(255, 68, 68)`}" class="text-price ">{{item.seckillPrice}}</view>
													 <text v-if="item.salesPrice>0 || (item.goodsSku&&item.goodsSku.salesPrice>0)" class="price-original"  > ￥{{item.salesPrice || item.goodsSku.salesPrice}} </text>
												  </view>
									<!-- <view class="ext-price text-white text-bold margin-top-xs"><text class="text-price"
											:style="{color: `${newData.subtitleColor}`}">{{item.seckillPrice}}</text>
									</view> -->
								</view>
							</navigator>
						</block>
					</scroll-view>

					<view v-else class="goods-detail text-center" :style="{color: `${newData.titleColor}`}">
						<view class="text-sm padding margin-top-lg">暂无商品信息</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	const util = require("utils/util.js");
	import countDown from "@/components/count-down/index";

	export default {
		components: {
			countDown
		},
		props: {
			value: {
				type: Object  | null,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
			shopId: {
				type: String
			}
		},

		mounted() {
			if (this.shopId > 0) {
				this.parameter.shopId = this.shopId;
			}
			// this.getData();
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				curSeckillHall: {}, //当前会场
				outTime: -1,
				seckillState: '',
				hasSeckill: false,
				listSeckillGoodsInfo: [], //当前时间段 秒杀商品
				parameter: {},
				page: {
					searchCount: false,
					current: 1,
					size: 30,
					ascs: 'sort',
					descs: '' //升序字段
				},
				
			};
		},
		watch:{
		  newData: {
			handler(newVal,oldVal) {
			   console.error("=================",newVal,newVal.seckillId);
				if(newVal&&newVal.seckillId>0) {
					this.getSeckillDataById(newVal.seckillId);
				}else {
					this.getData();
				}
			},
			deep: true,
			immediate: true
		   },
		},
		methods: {
			countDownDone() {
				this.getData();
			},
			getData() {
				let that = this;
				api.getSeckillhallProductList().then(res => {
					let seckillList = res.data;
					if (seckillList && seckillList.length > 0) {
						for(let i=0; i<seckillList.length; i++ ){
							if(seckillList[i] && seckillList[i].listSeckillInfo && seckillList[i].listSeckillInfo.length>0){
								this.hasSeckill = true;
								this.curSeckillHall = seckillList[0];
								this.listSeckillGoodsInfo = seckillList[0].listSeckillInfo;
								this.setCountDown();
								return;
							}
						}
						
					}
				});
			},
			
			getSeckillDataById(id) {
					api.seckillinfoPage(Object.assign({seckillHallId: id},this.page))
						.then((res) => {
							this.hasSeckill = true;
							this.listSeckillGoodsInfo =  res.data.records;
							if(this.listSeckillGoodsInfo&&this.listSeckillGoodsInfo.length>0){
								this.curSeckillHall = this.listSeckillGoodsInfo[0].seckillHall;
								this.setCountDown();
							}
						});
			},
		
			
			setCountDown() {
				// 设置倒计时时间，
				// 如果当前时间大于会场时间，结束
				// 如果当前时间等于，正在进行中
				// 如果小于暂未开始
				console.log("===this.curSeckillHall========",this.curSeckillHall)
				if (this.curSeckillHall.beginSecond > 0  ) {
					this.outTime = this.curSeckillHall.beginSecond * 1000;
					this.seckillState = "距离开始"
				} else if (this.curSeckillHall.remainSecond > 0) { //计算倒计时多少秒
					this.outTime = this.curSeckillHall.remainSecond * 1000;
					this.seckillState = "距离结束还有"
				} else {
					this.outTime = -1;
					this.seckillState = "已结束"
				}
			},

			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	
	.seckill-bg {
		height: 498rpx;
		margin: auto;
	}

	.seckill-image {
		height: 498rpx;
		margin: auto;
		position: absolute;
		min-width: 100%;
		max-width: none;
	}
	
	.seckill-ver-image {
		height: 100%;
		margin: auto;
		position: absolute;
		min-width: 100%;
		max-width: none;
	}

	.seckill-more {
		padding: 2px 10px;
		background: rgba(255, 255, 255, 0.71);
	}

	.seckill-list {
		padding-left: 5rpx;
		padding-right: 5rpx;
	}

	.goods-detail {
		width: auto;
		overflow: hidden;
	}

	.wrapper-list-goods {
		height: 498rpx;
		white-space: nowrap;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 200rpx;
		height: 200rpx;
	}

	.wrapper-list-goods .item .img-box {
		width: 100%;
		height: 200rpx;
		margin-top: 20rpx;
	}

	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx;
	}
</style>
