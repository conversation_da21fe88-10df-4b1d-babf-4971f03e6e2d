<template>
	<view class="cu-modal bottom-modal" :class="modalCoupon ? 'show' : ''" @tap="hideModalCoupon" catchtouchmove="touchMove">
		<view class="cu-dialog bg-white " :class="modalCoupon ? 'animation-slide-bottom' : ''" @tap.stop>
			<view class="article">
				<view class="cu-item">
					<view class="content text-lg margin-top-sm">优惠券</view>
				</view>
				<view class="text-xl close-icon">
					<text class="cuIcon-close" @tap="hideModalCoupon"></text>
				</view>
			</view>
			<scroll-view scroll-y scroll-with-animation style="max-height: 70vh;">
				<view class="cu-list text-left padding-bottom-sm padding-top-sm">
					<view class="cu-item padding-xs" v-for="(item, index) in couponInfoList" :key="index">
						<coupon-info :couponInfo="item" :toUse="false" @receiveCoupon="receiveCouponChange($event,index)"></coupon-info>
					</view>
					<view class="cu-load over" v-if="couponInfoList.length <= 0"></view>
				</view>
			</scroll-view>
			<view class="cu-bar bg-white tabbar">
				<view class="btn-group">
					<button class="cu-btn round shadow-blur lg btn-confirm" :class="'bg-'+theme.themeColor" @tap="hideModalCoupon">确定</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
			};
		},
		onLoad() {},
		props: {
			couponInfoList: {
				type: Array,
				default: () => []
			},
			modalCoupon: {
				type: Boolean,
				default: false
			},
		},
		watch: {
			
		},
		methods: {
			touchMove() {
				return;
			},
			hideModalCoupon() {
				this.$emit('changeModalCoupon', false);
			},
			receiveCouponChange(item, index) { //更新单条数据
				this.$emit('receiveCouponChange', {
					item: item,
					index: index
				});
			},
		}
	};
</script>
<style>
	.close-icon {
		position: absolute;
		right: 20rpx;
		top: 20rpx
	}
	
	.btn-confirm{
		width: 96%;
	}
</style>