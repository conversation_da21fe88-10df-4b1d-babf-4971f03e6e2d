<template>
	<view>
		<waterfall :loadMore="loadMore" :setData="newData" :list="newGoodsList" :goodsSpace="goodsSpace"
			@click="handleClick" :fullWidth="fullWidth" :borderRadiusLeftTop="borderRadiusLeftTop"  :borderRadiusRightTop="borderRadiusRightTop" :customCoverImage="customCoverImage">
		</waterfall>
	</view>
</template>

<script>
	import waterfall from "components/waterfall/index.vue"
	export default {
		data() {
			return {
				newGoodsList: []
			};
		},

		components: {
			waterfall
		},
		props: {
			newData: {
				type: Object,
				default: () => {}
			},
			goodsList: {
				type: Array,
				default: () => []
			},
			goodsSpace: {
				type: String | Number,
				default: 3
			},
			customCoverImage: {
				type: String | Number,
				default:"",
			},
			
			borderRadiusLeftTop:{
				type: String | Number,
				default:12,
			},
			
			borderRadiusRightTop:{
				type: String | Number,
				default:12,
			},
			fullWidth: {
				type: Number,
				default: 750
			},

			loadMore: {
				type: Boolean,
				default: false
			}
		},
		watch: {
			newData: {
				handler(newValue, oldValue) {
					if (!newValue) {
						return;
					}
					let {
						goodsList = [], swiperobj = {}, isSwiper = false
					} = newValue;
					if (+isSwiper) {
						goodsList.unshift(swiperobj);
					}
					this.newGoodsList = goodsList;
				},
				immediate: true
			},
			goodsList: {
				handler(newValue) {
					this.newGoodsList = newValue;
				}
			}
		},
		methods: {
			handleClick(data) {
				const sourceModule = encodeURIComponent('瀑布流');
				uni.navigateTo({
					url: "/pages/goods/goods-detail/index?id=" + data.id +  `&source_module=${sourceModule}`
				})
			}
		}
	};
</script>
<style>
	/* 优惠券部分 */
	.couponBox {
		line-height: 26rpx;
	}
</style>