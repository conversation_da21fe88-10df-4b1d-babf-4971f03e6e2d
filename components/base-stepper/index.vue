<template>
  <view
    :class="'flex text-center ' + customClass"
    style="width:230rpx;background: #F9F6F7;"
  >
    <view class="flex-sub">
      <button
        class="cu-btn cuIcon-move sm st-num-bt"
        :style="reduceBtnStyle"
        :disabled="cartNum <= min"
        @tap.stop="stNumMinus"
      ></button>
    </view>
    <view
      v-if="stNum!=0"
      class="flex-sub"
	  style="border-left: solid 1rpx #eee; border-right: solid 1rpx #eee;"
      @click.stop
    >
      <input
        type="number"
        class="st-num text-center radius"
        style="position: relative;z-index: 1;"
        :style="inputStyle"
        v-model="cartNum"
        :disabled="disabled"
        @blur="numChange"
      />
    </view>

    <view
      v-else
      class="flex-sub"
      @click.stop
    >
      <input
        type="number"
        class="st-num text-center bg-gray radius"
        :style="inputStyle"
        v-model="cartNum"
        disabled
        @blur="numChange"
      />
    </view>
    <view class="flex-sub">
      <button
        class="cu-btn cuIcon-add sm st-num-bt"
        :style="addBtnStyle"
        :disabled="max >= 0 ? cartNum >= max : false"
        @tap.stop="stNumAdd"
      ></button>
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
		cartNum:0
	};
  },
  components: {},
  watch: {
    stNum:{
	   handler(newVal, oldVal){
		   this.cartNum = newVal;
	   },
	   immediate: true
	},
  },
  computed: {},
  props: {
    max: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: true
    },
    min: {
      type: Number,
      default: 0
    },
    stNum: {
      type: Number,
      default: 0
    },
    customClass: {
      type: String,
      default: ''
    },
    reduceBtnStyle: {
      type: String,
      default: ''
    },
    addBtnStyle: {
      type: String,
      default: ''
    },
    inputStyle: {
      type: String,
      default: ''
    },
  },
  methods: {
    stNumMinus () {
      this.$emit('numChange', this.stNum - 1);
    },

    stNumAdd () {
      this.$emit('numChange', this.stNum + 1);
    },

    numChange (e) {
      //限制null 0 就等于 1
      if (this.cartNum != null && this.cartNum && this.cartNum != 0) {
        //限制最大值
        if (parseInt(this.cartNum) > parseInt(this.max)) {
          this.cartNum = this.max
		  uni.showModal({
		  	title: `您好，最多可购买${this.max}件`,
		  })
        }else if(parseInt(this.cartNum) < parseInt(this.min)){
			this.cartNum = this.min
			uni.showModal({
				title: `您好，最少需购买${this.min}件`,
			})
		}
      } else {
        this.cartNum = this.min
      }
	  if(this.stNum!=this.cartNum){
		this.$emit('numChange', this.cartNum);
	  }
    },
  }
};
</script>
<style scoped>
.st-num {
  width: 80rpx;
  height: 50rpx;
}

.st-num-bt {
  line-height: 50rpx;
  overflow: hidden;
  font-size: 30rpx !important;
  height: 50rpx !important;
  background: none !important;
}
</style>
