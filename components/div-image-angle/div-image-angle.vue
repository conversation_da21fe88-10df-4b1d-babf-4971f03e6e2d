<template>
	<view class="zero-content">
		<!-- <view class=""> -->
		<image class="imgBox" :src="image ? image : $imgUrl('live/img/no_pic.png')"
			:style="'borderRadius:'+borderRadius"></image>
		<!-- </view> -->
		<view class="markUrlsList" v-if="markUrlsObj">
			<image class="imgbox" v-for="(item,index) in markUrlsObj" :src="item"></image>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				markUrlsObj: this.markUrls,
			}
		},
		props: {
			// 要显示的图片
			image: {
				type: String,
				default: ''
			},
			markUrls: {
				type: Array,
			},
			borderRadius: {
				type: String,
				default: '5rpx'
			}
		},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.zero-content {
		overflow: hidden;
		position: relative;
		width: 100%;
		height: 100%;

		.imgBox {
			width: 100%;
			height: 100%;
			border-radius: 5rpx 5rpx 0 0;
		}
	}

	.markUrlsList {
		width: 100%;
		position: absolute;
		top: 0;
		left: 0;
		height: 40rpx;
		display: flex;
		flex-direction: row-reverse;

		.imgbox {
			display: block;
			width: 40rpx;
			height: auto;
		}
	}
</style>
