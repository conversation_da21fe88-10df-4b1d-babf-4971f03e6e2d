<template>
	<view class="margin-top-xm padding-top-sm margin-lr-sm">
		<view class="text-black text-bold text-df-px">支付方式</view>
		<!-- 微信支付 -->
		<radio-group class="" style="min-height: 100rpx; align-items: center; width: 100%" @change="changePaymentPattern">
			<label v-if="!payType || payType == '1' || payType == '2'" class="flex justify-between align-center cu-bar" style="width: 100%">
				<view class="margin-left-sm flex align-center text-df">
					<view class="margin-right-sm" style="width: 35rpx">
						<image :src="$imgUrl('live/pay/weixinzhifu.png')" mode="widthFix" style="display: block; margin-top: 4rpx; width: 34rpx; height: 34rpx" />
					</view>
					微信支付
				</view>
				<radio :value="JSON.stringify({ type: 1 })" style="transform: scale(0.7)" :checked="paymentType == 1" class="round orange" />
			</label>
			<!-- #ifdef APP -->
			<label v-if="!islocker && (!payType || payType == '2' || payType == '1')" class="flex justify-between align-center cu-bar" style="width: 100%">
				<view class="margin-left-sm flex align-center text-df">
					<view class="margin-right-sm" style="width: 35rpx">
						<image src="https://img.alicdn.com/tfs/TB1qEwuzrj1gK0jSZFOXXc7GpXa-32-32.ico" mode="widthFix" style="width: 34rpx; height: 34rpx"></image>
					</view>
					支付宝支付
				</view>
				<radio :value="JSON.stringify({ type: 2 })" style="transform: scale(0.7)" :checked="paymentType == 2" class="round orange" />
			</label>

			<label class="flex justify-between align-center cu-bar" style="width: 100%" v-if="!islocker && false">
				<view class="margin-left-sm flex align-center text-df">
					<view class="margin-right-sm" style="width: 35rpx">
						<text class="icon iconfont icon-weixinzhifu" style="font-size: 35rpx"></text>
					</view>
					银行卡支付
				</view>
				<radio :value="JSON.stringify({ type: 3 })" style="transform: scale(0.7)" :checked="paymentType == 3" class="round orange" />
			</label>
			<!-- #endif -->
			<!-- 后端 订单类型（0:普通订单；1:砍价订单；2:拼团订单；3:秒杀订单; 4:积分兑换; 5:付费优惠券; 6:团购订单; 7:礼品卡订单；8:奖品订单） -->
			<!-- 	前端 订单类型 （1-普通订单(砍价订单 bargainId)，2-立即购买，3-秒杀订单，4-积分兑换，5-付费优惠券，6-团购订单， 7-礼品卡订单，8-奖品订单） -->
			<!-- 后端 0、7 可以使用对公转账；前端 1、2、7 且 bargainId 没有值 可以使用对公转账 -->

			<label
				v-if="!islocker && (!payType || payType == '4') && !bargainId && (orderType == '0' || orderType == '1' || orderType == '2' || orderType == '7')"
				class="flex justify-between align-center cu-bar"
				style="width: 100%"
			>
				<view class="margin-left-sm flex align-center text-df">
					<view class="margin-right-sm" style="width: 35rpx">
						<image :src="$imgUrl('live/pay/duigongzhuanzhang.png')" mode="widthFix" style="display: block; margin-top: 4rpx; width: 34rpx; height: 34rpx" />
					</view>
					对公转账
					<view class="margin-left-sm" style="width: 35rpx" @tap.stop="openTransferRef">
						<text class="cuIcon-info" style="font-size: 30rpx"></text>
					</view>
				</view>
				<radio :value="JSON.stringify({ type: 4 })" style="transform: scale(0.7)" :checked="paymentType == 4" class="round orange" />
			</label>

			<!-- 储物柜订单不展示找人代付 -->
			<label v-if="!islocker" class="flex justify-between align-center cu-bar" style="width: 100%">
				<view class="margin-left-sm flex align-center text-df">
					<view class="margin-right-sm" style="width: 35rpx">
						<!-- <text class="icon iconfont icon-weixinzhifu" style="font-size:35rpx;"></text> -->
						<image :src="$imgUrl('rank/pay-demand-icon.png')" mode="aspectFill" style="width: 27rpx; height: 31rpx" />
					</view>
					找人代付
				</view>
				<radio :value="JSON.stringify({ type: 5 })" style="transform: scale(0.7)" :checked="paymentType == 5" class="round orange" />
			</label>
		</radio-group>
		<!-- 银行卡支付 v-if="false"微信不支持-->
		<view v-if="!islocker && false" class="cu-bar" style="min-height: 100rpx; border-top: 2rpx dashed rgba(0, 0, 0, 0.05); align-items: center" @tap.stop="showBankDia">
			<view class="margin-left-sm">
				<view class="flex align-center">
					<view class="margin-right-sm" style="width: 35rpx">
						<image :src="$imgUrl('live/pay/yinxingqia.png')" mode="widthFix" style="display: block; margin-top: 4rpx; width: 34rpx; height: 34rpx" />
					</view>
					<view>银行卡支付</view>
					<!-- </view> -->
					<view v-if="selectedBank.length" class="text-orange text-bold margin-left-lg">
						{{ selectedBank[0].bankName }}({{ selectedBank[0].accountNo.substring(selectedBank[0].accountNo.length - 4) }})
					</view>
				</view>
				<text class="text-red text-xs text-bold">使用银行卡支付必须大于1元</text>
			</view>
			<text v-if="bankLoading" class="loader margin-right-sm text-col"></text>
			<text v-else class="cuIcon-right padding-right-sm text-col text-right" style="width: 50rpx"></text>
		</view>
		<!-- 银行卡支付  -->
		<view v-if="!islocker && showType == 2 && bankAsync && !goodsShow" style="height: 800rpx; position: absolute; top: 0; left: 0; width: 100%; background: #fff">
			<view class="text-xl back-icon text-black" @tap.stop="goBack">
				<text class="cuIcon-back margin-right-sm text-col"></text>
			</view>
			<view class="text-center text-black text-lg text-bold">银行卡列表</view>
			<scroll-view scroll-y scroll-with-animation class="margin-top-sm" style="height: 600rpx" v-if="bankList.length">
				<radio-group class="cu-bar solid-bottom" @change="changePaymentPattern" v-for="item in bankList" :key="item.id">
					<label class="flex justify-between align-center" style="width: 100%">
						<view class="margin-left-sm flex align-center">
							<image
								style="width: 60rpx; height: 60rpx; border-radius: 50%"
								:src="item.bankLogo || $imgUrl('live/img/no_pic.png')"
								mode="aspectFill"
								class="margin-right-sm"
							/>
							<view class="text-black">{{ item.bankName }}({{ item.accountNo.substring(item.accountNo.length - 4) }})</view>
						</view>
						<radio :value="JSON.stringify({ type: 3, id: item.id })" style="transform: scale(0.7)" :checked="item.id == payBankAccountId && paymentType == 3" class="round checked red checked" />
					</label>
				</radio-group>
			</scroll-view>
			<view v-else class="text-center" style="margin-top: 200rpx">
				<view class="text-center" style="color: #bc5713">没有银行卡可以支付，快去添加银行卡吧！！！</view>
				<!-- <text class="icon iconfont icon-shiliangzhinengduixiang" style="font-size:160rpx;"></text> -->
				<image :src="$imgUrl('live/pay/shiliangzhinengduixiang-.png')" mode="widthFix" style="width: 160rpx; height: auto" />
			</view>
			<navigator url="/pages/bankpay/add-bank/add-bank" open-type="navigate">
				<view class="add-bank text-red text-center">添加银行卡</view>
			</navigator>
		</view>
		<!-- 银行卡支付 弹框 -->
		<view v-if="showType != 2 && bankAsync" class="cu-modal" :class="showType != 2 && bankAsync ? 'show' : ''" @tap.stop="bankAsync = false">
			<view class="close-icon">
				<text class="cuIcon-close" @tap.stop="bankAsync = false"></text>
			</view>
			<view class="cu-dialog bg-white padding-sm" style="width: 90%; height: 600rpx; border-radius: 20rpx" @tap.stop>
				<view class="text-center text-black text-lg text-bold">银行卡列表</view>
				<scroll-view v-if="bankList.length" scroll-y scroll-with-animation class="margin-top-sm" style="height: 360rpx">
					<radio-group class="cu-bar solid-bottom" @change="changePaymentPattern" v-for="item in bankList" :key="item.id">
						<label class="flex justify-between align-center" style="width: 100%">
							<view class="margin-left-sm flex align-center">
								<image
									style="width: 60rpx; height: 60rpx; border-radius: 50%"
									:src="item.bankLogo || $imgUrl('live/img/no_pic.png')"
									mode="aspectFill"
									class="margin-right-sm"
								/>
								<view class="text-black">{{ item.bankName }}({{ item.accountNo.substring(item.accountNo.length - 4) }})</view>
							</view>
							<radio
								:value="JSON.stringify({ type: 3, id: item.id })"
								:checked="item.id == payBankAccountId && paymentType == 3"
								class="round checked red checked margin-right-sm"
							/>
						</label>
					</radio-group>
				</scroll-view>
				<view v-else class="text-center" style="margin-top: 70rpx">
					<view class="text-center" style="color: #bc5713">没有银行卡可以支付，快去添加银行卡吧！！！</view>
					<!-- <text class="icon iconfont icon-shiliangzhinengduixiang" style="font-size:160rpx;"></text> -->
					<image :src="$imgUrl('live/pay/shiliangzhinengduixiang-.png')" mode="widthFix" style="width: 160rpx; height: auto" />
				</view>
				<navigator url="/pages/bankpay/add-bank/add-bank" open-type="navigate">
					<view class="add-bank text-red text-center">添加银行卡</view>
				</navigator>
				<!-- @tap.stop="goAddBank" -->
			</view>
		</view>

		<!-- 对公转账说明 -->
		<view class="cu-modal" :class="transferAsync ? 'show' : ''" @tap.stop="closeTransferRef">
			<view class="cu-dialog padding-sm bg-white" style="width: 90%; height: 600upx; border-radius: 20rpx" @tap.stop>
				<view class="text-xl text-black">对公转账说明</view>
				<view class="text-df text-black margin-top-sm padding-sm text-left">
					对公转账请确保银行账户信息与身份信息一致。有任何支付问题，请联系客服处理。超出1个工作日未对账，请提供订单号及正规的汇款底单
					(汇款底单需包含收付款户名，收付款账号，公章等，如存在代汇款、汇款后委托他人或企业用款等情况，还需提供对应的代付/委托声明)
					邮件至*************************，我司会在一个工作日内受理。
				</view>
				<button class="cu-btn round margin-top-sm bg-red" style="width: 260upx" @tap.stop="closeTransferRef">我知道了</button>
			</view>
		</view>

		<!-- 账户信息 -->
		<view v-if="bankAccountAsync && bankAccountInfo" class="cu-modal bottom-modal" :class="bankAccountAsync ? 'show' : ''" @tap.stop="handleBankAccount">
			<view class="cu-dialog bg-white padding-tb-lg padding-lr-lg" @tap.stop>
				<view style="position: absolute; top: 20upx; left: 30upx">
					<text class="cuIcon-close text-lg" @tap.stop="handleBankAccount"></text>
				</view>
				<view class="text-df margin-top-lg text-black" style="line-height: 80upx">
					<view style="line-height: 40upx">
						<view class="text-sm text-black">松雷南岗百货</view>
						<view class="text-sxl text-black text-bold">¥ {{ bankAccountInfo.tranamt }}</view>
					</view>
					<view class="margin-top-lg flex justify-between align-center">
						<view>订单收款账号</view>
						<view style="text-decoration: underline" @tap.stop="handleBankAccountCopyDetails(bankAccountInfo.payeeaccount, '订单收款账号')">
							{{ bankAccountInfo.payeeaccount }}
						</view>
					</view>
					<view class="flex justify-between align-center">
						<view>收款账户名称</view>
						<view style="text-decoration: underline" @tap.stop="handleBankAccountCopyDetails(bankAccountInfo.payeeaccountname, '收款账户名称')">
							{{ bankAccountInfo.payeeaccountname }}
						</view>
					</view>
					<view class="flex justify-between align-center">
						<view>收款银行名称</view>
						<view style="text-decoration: underline" @tap.stop="handleBankAccountCopyDetails(bankAccountInfo.payeebankname, '收款银行名称')">
							{{ bankAccountInfo.payeebankname }}
						</view>
					</view>
					<view class="flex justify-between align-center">
						<view>收款银行网点号</view>
						<view style="text-decoration: underline" @tap.stop="handleBankAccountCopyDetails(bankAccountInfo.payeebanksiteno, '收款银行网点号')">
							{{ bankAccountInfo.payeebanksiteno }}
						</view>
					</view>
					<view class="flex justify-between align-center">
						<view>收款银行网点名称</view>
						<view style="text-decoration: underline" @tap.stop="handleBankAccountCopyDetails(bankAccountInfo.payeebanksitename, '收款银行网点名称')">
							{{ bankAccountInfo.payeebanksitename }}
						</view>
					</view>
					<view class="flex justify-between align-center">
						<view>收款银行开户地</view>
						<view style="text-decoration: underline" @tap.stop="handleBankAccountCopyDetails(bankAccountInfo.payeebankopencity, '收款银行开户地')">
							{{ bankAccountInfo.payeebankopencity }}
						</view>
					</view>
				</view>
				<button class="cu-btn margin-top-lg bg-green" style="width: 280upx" @tap.stop="handleBankAccountCopy">一键复制</button>
			</view>
		</view>
	</view>
</template>

<script>
import api from 'utils/api';
import { payActivityOrder } from '@/api/memberactivity';

const util = require('utils/util.js');

export default {
	props: {
		// 选择银行卡支付展示：1-弹框，2-覆盖页面
		type: {
			type: Number,
			default: 1
		},
		// 数据埋点标题
		pageTitle: {
			type: String,
			default: '支付'
		},
		// 商品详情-立即购买：支付展示
		goodsShow: {
			type: Boolean,
			default: true
		},
		showtext: {
			type: Boolean,
			default: true
		},
		callBack: {
			type: Boolean,
			default: false
		},
		payType: {
			// type: String,
			// default: 1
		},
		orderType: {}, // 订单类型
		bargainId: {}, // 砍价id 用于 是否展示对公转账
		islocker: {
			// 是否是储物柜订单
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			showType: this.type, // 选择银行卡支付展示：1-弹框，2-覆盖页面
			bankList: [], // 可支付银行卡数据
			bankAsync: false, // 选择银行卡支付弹框
			paymentType: this.payType || 1, // 支付类型：1-微信支付，2-支付宝支付，3-银行卡支付, 4-对公转账，5-找人代付
			payBankAccountId: '', // 银行卡id
			bankLoading: false, // 请求可支付银行卡列表
			selectedBank: [], // 以选中的银行卡数据，只有一条
			paymentObj: {
				// #ifdef MP
				1: {
					way: 'WX_MA',
					value: ''
				},
				// #endif
				// #ifdef APP
				1: {
					way: 'WX_APP',
					value: ''
				},
				// #endif
				2: {
					way: 'ALI_APP',
					value: ''
				},
				3: {
					way: '',
					value: ''
				},
				4: {
					way: 'TRANSFER',
					value: ''
				}
			},
			transferAsync: false, // 对公转账说明
			bankAccountAsync: false, // 账户信息
			paymentPrice: 0.0, // 支付金额
			bankAccountInfo: null // 账户信息
			// orderType: 0, // 订单类型
		};
	},
	watch: {
		payType(val) {
			this.paymentType = val;
		}
	},
	created() {
		// this.getBankList()
	},
	methods: {
		goAddBank() {
			uni.redirectTo({
				url: `/pages/bankpay/add-bank/add-bank`
			});
		},
		// 请求银行卡可支付列表
		async getBankList() {
			try {
				if (this.bankLoading) return;
				this.bankLoading = true;
				const res = await api.getPaybankaccount();
				if (res.code == 0) {
					this.bankList = res.data;
				}
			} catch (e) {
				console.log(e);
			} finally {
				this.bankLoading = false;
			}
		},
		// 商品详情-立即购买：支付展示返回按钮
		goBack() {
			this.$emit('changeGoodsShow', true);
		},
		// 显示银行卡支付弹框
		showBankDia() {
			this.bankAsync = true;
			if (this.showType == 2) {
				this.$emit('changeGoodsShow', false);
			}
			this.getBankList();
		},
		// 切换支付方式
		changePaymentPattern(e) {
			const obj = JSON.parse(e.target.value);
			this.paymentType = obj.type;
			if (obj.type == 3) {
				this.payBankAccountId = obj.id;
				this.selectedBank = this.bankList.length ? this.bankList.filter((item) => item.id == obj.id) : [];

				// 选中银行卡后关闭选择银行卡弹框或者界面
				if (this.showType == 2) {
					this.$emit('changeGoodsShow', true);
				} else {
					this.bankAsync = false;
				}
			} else {
				this.payBankAccountId = '';
				this.selectedBank = [];
			}
		},
		// 其他微信支付
		weChatPayOther(payData, orderData, orderType) {
			let that = this;
			let provider = 'wxpay';
			let payDatas = payData;
			// #ifdef APP
			if (+this.paymentType === 1) {
				payDatas = JSON.parse(payDatas);
				payDatas.partnerid = payDatas.partnerId;
				payDatas.prepayid = payDatas.prepayId;
			} else if (+this.paymentType === 2) {
				provider = 'alipay';
			}
			// #endif
			uni.requestPayment({
				provider,
				// #ifdef APP
				orderInfo: payDatas,
				// #endif
				// #ifdef MP
				timeStamp: payData.timeStamp,
				nonceStr: payData.nonceStr,
				package: payData.package1,
				signType: payData.signType,
				paySign: payData.paySign,
				// #endif
				success: (res) => {
					//支付成功跳转到支付结果页面
					// 会员活动信息
					// orderType == "memberactivity"
					if (orderType == 'memberactivity' && that.callBack) {
						console.log('=======memberactivity=======');
						that.$emit('success');
						return;
					}
					if (that.callBack) {
						console.log('支付成功', that.callBack, '===============');
						that.$emit('success');
						return;
					}
					const orderIds = [];
					orderData.listOrderInfo.length &&
						orderData.listOrderInfo.forEach((item) => {
							orderIds.push(item.id);
						});

					if (orderType == 'PARK') {
						uni.redirectTo({
							url: `/pages/parkinglot/fee-success/fee-success?id=${orderData.listOrderInfo[0].id}`
						});
					} else {
						uni.redirectTo({
							url: `/pages/order/order-pay-result/index?paymentPrice=${orderData.paymentPrice}&orderMainId=${orderData.listOrderInfo[0].orderMainId}`
						});
					}
				},
				fail: (res) => {
					if (that.callBack) {
						that.$emit('fail');
						return;
					}
					console.log('====支付fail=====', res);
					//如果不是客户取消支付就弹出错误提示，方便查看问题
					if (res && +res.code === -100) {
						if (orderType == 'PARK') {
							uni.redirectTo({
								url: '/pages/parkinglot/fast-fee/fast-fee'
							});
						} else {
							uni.redirectTo({
								url: '/pages/order/order-list/index?status=0&orderType=' + orderType
							});
						}
					} else if (res && res.errMsg && res.errMsg != 'requestPayment:fail cancel') {
						uni.showModal({
							title: '支付未成功',
							showCancel: false,
							content: res.errMsg || '支付返回未成功信息',
							success() {},
							complete() {
								//判断是跳转到接龙的订单列表还是跳转到商城的订单列表
								// 跳转待支付页面
								if (orderType == 'PARK') {
									uni.redirectTo({
										url: '/pages/parkinglot/fast-fee/fast-fee'
									});
								} else {
									uni.redirectTo({
										url: '/pages/order/order-list/index?status=0&orderType=' + orderType
									});
								}
							}
						});
					} else {
						//判断是跳转到接龙的订单列表还是跳转到商城的订单列表
						// 跳转待支付页面
						if (orderType == 'PARK') {
							uni.redirectTo({
								url: '/pages/parkinglot/fast-fee/fast-fee'
							});
						} else {
							uni.redirectTo({
								url: '/pages/order/order-list/index?status=0&orderType=' + orderType
							});
						}
					}
				},
				complete: (res) => {}
			});
		},
		// 视频号微信支付
		weChatPayVideo(payData, orderData, orderType) {
			let that = this;
			wx.requestOrderPayment({
				timeStamp: payData.timeStamp,
				nonceStr: payData.nonceStr,
				package: payData.package1,
				signType: payData.signType,
				paySign: payData.paySign,
				success(res) {
					//支付成功跳转到支付结果页面
					const orderIds = [];
					orderData.listOrderInfo.length &&
						orderData.listOrderInfo.forEach((item) => {
							orderIds.push(item.id);
						});
				
					if (that.callBack) {
						that.$emit('success');
						return;
					}

					if (orderType == 'PARK') {
						uni.redirectTo({
							url: `/pages/parkinglot/fee-success/fee-success?id=${orderData.listOrderInfo[0].id}`
						});
					} else {
						uni.redirectTo({
							url: `/pages/order/order-pay-result/index?paymentPrice=${orderData.paymentPrice}&orderMainId=${orderData.listOrderInfo[0].orderMainId}`
						});
					}
				},
				fail(res) {
					if (that.callBack) {
						that.$emit('fail');
						return;
					}
					console.log('====支付fail=====', res);
					//如果不是客户取消支付就弹出错误提示，方便查看问题
					if (res && +res.code === -100) {
						if (orderType == 'PARK') {
							uni.redirectTo({
								url: `/pages/parkinglot/fee-success/fee-success?id=${orderData.listOrderInfo[0].id}`
							});
						} else {
							uni.redirectTo({
								url: '/pages/order/order-list/index?status=0&orderType=' + orderType
							});
						}
					} else if (res && res.errMsg && res.errMsg != 'requestOrderPayment:fail cancel' && orderType != 'PARK') {
						uni.showModal({
							title: '支付未成功',
							showCancel: false,
							content: res.errMsg || '支付返回未成功信息',
							success() {},
							complete() {
								//判断是跳转到接龙的订单列表还是跳转到商城的订单列表
								// 跳转待支付页面
								if (orderType == 'PARK') {
									uni.redirectTo({
										url: `/pages/parkinglot/fee-success/fee-success?id=${orderData.listOrderInfo[0].id}`
									});
								} else {
									uni.redirectTo({
										url: '/pages/order/order-list/index?status=0&orderType=' + orderType
									});
								}
							}
						});
					} else if (orderType != 'PARK') {
						//判断是跳转到接龙的订单列表还是跳转到商城的订单列表
						// 跳转待支付页面
						if (orderType == 'PARK') {
							uni.redirectTo({
								url: `/pages/parkinglot/fee-success/fee-success?id=${orderData.listOrderInfo[0].id}`
							});
						} else {
							uni.redirectTo({
								url: '/pages/order/order-list/index?status=0&orderType=' + orderType
							});
						}
					}
				}
			});
		},
		// 唤起微信支付
		weChatPay(payData, orderData, orderType) {
			const payMode = getApp().globalData.payMode;
			if (payMode) {
				this.weChatPayVideo(payData, orderData, orderType);
			} else {
				this.weChatPayOther(payData, orderData, orderType);
			}
		},
		// 银行卡支付
		// priceSyanc表示不需要传给接口这个参数paymentPrice  在订单列表和详情里面调支付不需要传paymentPrice参数
		// orderType  给订单列表使用。orderType==6订单列表显示接龙的样式，否则正常显示
		async payment(orderData, priceSyanc, orderType) {
			// debugger
			try {
				const params = {
					...orderData,
					listOrderInfo: orderData.listOrderInfo,
					paymentType: this.paymentType, // 支付类型：1-微信支付，2-支付宝支付，3-银行卡支付
					paymentWay: this.paymentObj[this.paymentType + ''].way
				};
				if (priceSyanc) {
					delete params.paymentPrice;
				}

				if (this.paymentType == 3) {
					params.payBankAccountId = this.payBankAccountId;
				}

				const activityParams = {
					paymentPrice: orderData.paymentPrice,
					activityOrder: orderData,
					paymentType: this.paymentType, // 支付类型：1-微信支付，2-支付宝支付，3-银行卡支付
					paymentWay: this.paymentObj[this.paymentType + ''].way
				};

				// 会员活动支付接口 orderType === "memberactivity"
				const payData = await (orderType == 'memberactivity' ? payActivityOrder(activityParams) : api.payOrder(params));
				if (payData?.code == 0) {
					// 获取订单信息以便微信支付
					const payRes = payData.data;
					if (orderData.paymentPrice <= 0) {
						// 0元付款 支付成功跳转到支付结果页面
						if (orderType == 'PARK') {
							uni.redirectTo({
								url: `/pages/parkinglot/fee-success/fee-success?id=${orderData.listOrderInfo[0].id}`
							});
						} else {
							uni.redirectTo({
								url: `/pages/order/order-pay-result/index?paymentPrice=${orderData.paymentPrice}&orderMainId=${orderData.listOrderInfo[0].orderMainId}`
							});
						}
					} else {
						if (this.paymentType == 1 || this.paymentType == 2) {
							// #ifdef APP
							this.weChatPay(payRes.payData, orderData, orderType); // 支付
							// #endif
							// #ifdef MP
							this.weChatPay(payRes.barCodePay, orderData, orderType); // 支付
							// #endif
						} else if (this.paymentType == 3) {
							uni.redirectTo({
								url: `/pages/order/order-pay-result/index?paymentPrice=${orderData.paymentPrice}&orderMainId=${orderData.listOrderInfo[0].orderMainId}`
							});
						} else if (this.paymentType == 4) {
							// 对公转账
							this.bankAccountAsync = true;
							this.bankAccountInfo = JSON.parse(payRes.payData);
							this.paymentPrice = payRes.paymentPrice;
							this.$emit('showTransfer', false);
						}
					}
				}
				if (this.paymentType == 3 && payData?.code != 0 && orderType != 'PARK') {
					uni.redirectTo({
						url: '/pages/order/order-list/index?status=0&orderType=' + orderType
					});
				}
			} catch (e) {
				console.log(e, '===========error=============');
			} finally {
			}
		},
		// 支付订单
		async payOrder(orderData, priceSyanc, orderType) {
			console.log('orderData===>', orderData, priceSyanc, orderType);

			try {
				// this.orderType = orderType
				if (orderData.paymentPrice < 1 && this.paymentType == 3) {
					uni.showModal({
						title: '提示',
						showCancel: false,
						content: '支付金额低于1元无法使用银行卡支付，请选择其他支付方式',
						success() {},
						complete() {}
					});
					return;
				}
				if (this.paymentType == 1 || this.paymentType == 2 || this.paymentType == 4) {
					// 微信支付 对公转账
					this.payment(orderData, priceSyanc, orderType);
				} else if (this.paymentType == 3) {
					// 银行卡支付
					uni.showModal({
						title: '提示',
						content: '请确认支付' + orderData.paymentPrice + '元',
						success: (res) => {
							if (res.confirm) {
								this.payment(orderData, priceSyanc, orderType);
							} else {
								//如果是停车场订单支付，取消不做处理
								if (orderType == 'PARK') return;
								uni.redirectTo({
									url: '/pages/order/order-list/index?status=0&orderType=' + orderType
								});
							}
						},
						complete: () => {}
					});
				} else if (this.paymentType == 5) {
					// 找人代付
					uni.redirectTo({
						url: `/pages/prepaid/pay-me/index?price=${orderData.paymentPrice}&orderOn=${orderData.listOrderInfo[0].orderMainId}`
					});
				}
			} catch (e) {
				console.log(e, '===========error============');
			} finally {
			}
		},
		
		// 打开
		openTransferRef() {
			this.transferAsync = true;
			this.$emit('showTransfer', false);
		},
		closeTransferRef() {
			this.transferAsync = false;
			this.$emit('showTransfer', true);
		},
		// 对公转账
		handleBankAccount() {
			this.bankAccountAsync = false;
			this.$emit('showTransfer', true);
			uni.redirectTo({
				url: '/pages/order/order-list/index?status=0&orderType=' + this.orderType
			});
		},
		// 对公转账 单独复制
		async handleBankAccountCopyDetails(copyData, tips) {
			// const { payeeaccount, payeeaccountname, payeebankname, payeebanksiteno, payeebanksitename, payeebankopencity } = this.bankAccountInfo
			// const cppyData = `${payeeaccount}\n${payeeaccountname}\n${payeebankname}\n${payeebanksiteno}\n${payeebanksitename}\n${payeebankopencity}`

			await uni.setClipboardData({
				data: copyData,
				success: () => {
					uni.showToast({
						title: `${tips}已复制到粘贴板`,
						icon: 'none'
					});
				},
				fail: (e) => {
					console.log(e, '=====e======e==');
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		},
		// 对公转账 一键复制
		async handleBankAccountCopy() {
			const { payeeaccount, payeeaccountname, payeebankname, payeebanksiteno, payeebanksitename, payeebankopencity } = this.bankAccountInfo;
			const copyData = `${payeeaccount}\n${payeeaccountname}\n${payeebankname}\n${payeebanksiteno}\n${payeebanksitename}\n${payeebankopencity}`;

			await uni.setClipboardData({
				data: copyData,
				success: () => {
					uni.showToast({
						title: '已复制到粘贴板',
						icon: 'none'
					});
					setTimeout(() => {
						this.handleBankAccount();
					}, 800);
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		}
	}
};
</script>
<style lang="scss" scoped>
.close-icon {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background: rgb(157, 157, 157);
	font-size: 40rpx;
	text-align: center;
	line-height: 50rpx;
	position: fixed;
	bottom: 400rpx;
	left: 50%;
	transform: translateX(-50%);
}

.add-bank {
	width: 400rpx;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 40rpx;
	background: #f3f0f0;
	position: absolute;
	bottom: 50rpx;
	left: 50%;
	transform: translateX(-50%);
}

.back-icon {
	position: absolute;
	left: 20rpx;
	top: 0;
}

.loader {
	width: 35rpx;
	height: 35rpx;
	border: 5rpx solid rgb(222, 222, 222);
	border-bottom-color: #ff3d00;
	border-radius: 50%;
	display: inline-block;
	// -webkit-animation: rotation 1s linear infinite;
	animation: rotation 1s linear infinite;
}

@keyframes rotation {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}
</style>
