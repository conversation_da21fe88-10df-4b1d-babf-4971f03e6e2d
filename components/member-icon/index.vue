<template>
	<!-- <view>
    <view v-for="(item, index) in dicData" :key="index" @click="handleClick(item)">
      <image :src="getBackgroundImage(item.value)"></image>
      <text>{{ item.label }}</text>
    </view>
  </view> -->
	<view class="text-xss absolute flex radius-lg align-center" :style="{
			width: '132rpx',
			height: '36rpx',
			top: '5rpx',
			right: '5rpx',
		    backgroundSize: 'cover',
     	    backgroundPosition: 'center',
			backgroundImage: `${getBackgroundImage(memberLevelLimit)}`}
		">
		<view :style="{
        margin: '0 auto', 
        color: '#FFFFFF',
        }">
			<text style="margin-right: 4rpx;">{{ getLabelByValue(memberLevelLimit) }}</text>
			<text :style="{
          color: memberLevelLimit=='102'?'#A46F37':'#FFFFFF',
        }">
				专享
			</text>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			memberLevelLimit: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				dicData: [{
						label: '不限等级',
						value: '',
						disabled: false
					},
					// {
					//   label: '积分卡',
					//   value: '101',
					//   disabled: false
					// }, 
					{
						label: '金卡',
						value: '102',
						disabled: false
					}, {
						label: '钻石卡',
						value: '103',
						disabled: false
					}, {
						label: '黑钻卡',
						value: '108',
						disabled: false
					}
				]
			};
		},
		methods: {
			getLabelByValue(value) {
				const item = this.dicData.find(item => item.value === value);
				return item ? item.label : '未知等级';
			},

			handleClick(item) {
				// 处理点击事件
			},

			getBackgroundImage(value) {
				// let imagePath = '';
				// switch (value) {
				//   case '101':
				//     imagePath = '积分卡.png';
				//     break;
				//   case '102':
				//     imagePath = '金卡.png';
				//     break;
				//   case '103':
				//     imagePath = '钻石卡.png';
				//     break;
				//   case '108':
				//     imagePath = '黑钻卡.png';
				//     break;
				//   default:
				//     imagePath = '默认背景.png';
				// }
				return `url(https://img.songlei.com/signrecordInfo/bg-${value}.png)`;
			}
		}
	};
</script>