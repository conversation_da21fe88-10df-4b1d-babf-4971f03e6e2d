<template>
	<view v-if="promptlyBuySt">
		<view v-show="goodsShow">
			<view v-show="isModalShow">
				<!-- 订单备注 -->
				<view class="cu-bar padding-right-sm">
					<text class="padding-left-sm text-black text-df">备注</text>
					<input class="leave-message" placeholder="给卖家留言" @blur="userMessageInput" :data-shopInfo="shopInfo" />
				</view>

				<view class="bg-gray" style="height: 10rpx"></view>
				<!-- 运费金额 -->
				<view class="cu-bar response" v-if="distributionMode != 1 && shopList[0] && shopList[0].freight" style="min-height: 70rpx">
					<text class="margin-left-sm text-black text-df">运费金额</text>
					<view class="flex-twice flex justify-end text-df margin-right-sm text-red arrow-width">
						<format-price signFontSize="26rpx" smallFontSize="26rpx" priceFontSize="30rpx" :price="shopList[0].freight" />
					</view>
				</view>
				<!-- 发票 -->
				<view class="cu-bar solid-top" @tap.stop="$emit('invoiceOpen', '')">
					<view class="margin-left-sm text-black text-df">发票</view>
					<view>
						<text v-if="invoiceState">电子普通发票</text>
						<text v-else>不开发票</text>
						<text class="cuIcon-right margin-right-sm text-col"></text>
					</view>
				</view>
				<!-- 折扣金额 -->
				<view class="cu-bar flex response solid-top" v-if="promotionsPrice">
					<view class="flex-sub">
						<view class="text-black margin-left-sm text-height text-col text-df">折扣金额</view>
					</view>
					<view class="flex-twice flex justify-end text-df margin-right-sm text-red arrow-width">
						-
						<format-price signFontSize="24rpx" smallFontSize="24rpx" priceFontSize="28rpx" :price="promotionsPrice" />
					</view>
				</view>
				<!-- 优惠券 -->
				<view class="cu-bar flex response" v-if="spuCoupons && spuCoupons.length">
					<!-- 平台券 -->
					<view class="flex-sub">
						<view class="text-black margin-left-sm text-height text-col text-df">
							平台券
							<text v-if="selectedCouponIds.length" style="border: 1px solid red; padding: 2rpx 5rpx" class="text-xs margin-left-xs text-red">
								已选{{ selectedCouponIds.length }}张
							</text>
						</view>
					</view>
					<!-- 优惠券 -->
					<view class="flex-sub text-sm">
						<text class="text-gray" v-if="!discountPrice">{{ spuCoupons.length ? '有可用优惠券' : '无可用优惠券' }}</text>
					</view>

					<view class="flex-sub text-right">
						<view class="flex justify-end" @tap="$emit('changeModalCoupon', 'show')">
							<view v-if="discountPrice" class="flex justify-end text-df text-red">
								-
								<format-price signFontSize="24rpx" smallFontSize="24rpx" priceFontSize="28rpx" :price="discountPrice" />
							</view>
							<text v-else class="text-red">选择券</text>
							<text class="cuIcon-right margin-right-sm"></text>
						</view>
					</view>
				</view>
				<!-- 积分展示 -->
				<view v-if="creditPointsShopSt || creditPointsFreightSt" class="cu-bar flex response">
					<view class="text-black margin-left-sm text-height text-col text-df">积分</view>
					<text v-if="creditPointsShopSt" class="text-sm text-gray">可用{{ availableDeductionPoint }}积分抵{{ availableExchangePrice }}元</text>
					<text v-else-if="creditPointsFreightSt" class="text-sm text-gray">可用{{ freightPoints }}积分抵运费{{ shopList[0].freight ? shopList[0].freight : 0 }}元</text>
					<view class="text-df text-gray text-right margin-right-sm">
						<switch :checked="creditPointsSt" @change="changeSwitchFreight" color="#CDA185" class="brown" />
					</view>
					<view class="flex flex-end text-right margin-right-sm arrow-width">
						<template v-if="creditPointsShopSt">
							<text v-if="creditPointsSt" class="text-red">-</text>
							<format-price v-if="creditPointsSt" signFontSize="24rpx" smallFontSize="24rpx" priceFontSize="28rpx" :price="availableExchangePrice" />
							<text v-else class="text-df">不使用</text>
						</template>
						<template v-else-if="creditPointsFreightSt">
							<text v-if="creditPointsSt" class="text-red">-</text>
							<format-price v-if="creditPointsSt" signFontSize="24rpx" smallFontSize="24rpx" priceFontSize="28rpx" :price="comPFreight" />
							<text v-else class="text-df">不使用</text>
						</template>
					</view>
				</view>

				<!-- 使用零钱  -->
				<block v-if="payMode && payMode.includes(1)">
					<view v-if="purseStatus != '0' && purseStatus">
						<view class="cu-bar flex response">
							<view style="width: 100%">
								<smallchange
									ref="smallchange"
									:purseDeductPrice="purseDeductPrice.toString()"
									:pursePrice="pursePrice"
									:newChangprop="parseInt(changprop)"
									:purseStatus="purseStatus"
									:paymentPursePrice="paymentPursePrice"
									@radioPurseDeductPrice="radioPurseDeductPrice($event)"
								></smallchange>
							</view>
						</view>
					</view>
				</block>
			</view>
			<!-- 礼品卡支付 -->
			<view v-if="payMode && payMode.includes(2)">
				<gift-modal
					:giftCardPrice="giftCardPrice"
					:giftDeductPrice="giftDeductPrice"
					:newGiftPrice="giftPrice"
					@giftOrderConfirm="giftOrderConfirm"
					@giftPriceValue="giftPriceValue"
					@showModal="showModal"
				></gift-modal>
			</view>

			<!-- 积分赠送 -->
			<view class="cu-bar flex response solid-top" v-if="givePoints">
				<view class="flex-sub">
					<view class="text-black margin-left-sm text-height text-col text-df">赠送积分</view>
				</view>
				<view class="flex-twice text-df text-right margin-right-sm arrow-width">
					{{ givePoints }}
				</view>
			</view>
		</view>

		<view v-if="isModalShow">
			<pay-components
				ref="pay"
				:pageTitle="pageTitle"
				:type="2"
				:goodsShow="goodsShow"
				@changeGoodsShow="changeGoodsShow"
				@showTransfer="showTransfer"
				:orderType="orderSubParms.orderType"
				:bargainHallId="orderSubParms.bargainHallId"
			/>
		</view>
		<uni-popup ref="perpopup" type="center" :mask-click="false">
			<view class="permissions_box">当您使用APP时，新增编辑收货地址的时候需要位置权限，不授权上述权限，不影响APP其他功能使用。</view>
		</uni-popup>
	</view>
</template>
<script>
const app = getApp();
const util = require('utils/util.js');
import api from 'utils/api';
import payComponents from '@/components/pay-components/pay-components.vue';
import deliveryTimeperiod from '@/components/delivery-timeperiod/delivery-timeperiod.vue';
import smallchange from '@/components/smallchange/smallchange';
import giftModal from '@/components/goods-sku/gift-modal.vue';
import formatPrice from '@/components/format-price/index.vue';
import { handleSenOrder } from '@/public/js_sdk/sensors/utils.js';

export default {
	components: {
		payComponents,
		deliveryTimeperiod,
		smallchange,
		giftModal,
		formatPrice
	},
	props: {
		goodsShow: {
			type: Boolean,
			default: true
		},
		shopInfo: {
			type: Object,
			default: () => {}
		},
		goodsSpu: {
			type: Object,
			default: () => {}
		},
		goodsSku: {
			type: Object,
			default: () => {}
		},

		modalSkuType: {
			type: String,
			default: ''
		},
		cartNum: {
			type: Number,
			default: 1
		},
		goodsSpecData: {
			type: Object,
			default: () => {}
		},
		invoiceRemark: {
			type: String,
			default: ''
		},
		invoiceState: {
			type: Boolean,
			require: true,
			default: false
		},
		selectedCouponIds: {
			type: Array,
			default: []
		},
		pursePriceVal: {
			type: String | Number,
			default: ''
		},
		// 礼品卡商品是否需要实名认证 0不需要  1 需要
		AuthenticationType: {
			type: String,
			default: ''
		},
		customerGoodsId: {
			type: String,
			default: ''
		},
		customerId: {
			type: String,
			default: ''
		},
		deviverParams: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		let orderType = 2;
		switch (this.goodsSpu.spuType) {
			case 3:
				orderType = 4;
				break;
			case 5:
				orderType = this.goodsSpu.spuType;
				break;
			default:
				orderType = 2;
				break;
		}
		return {
			theme: app.globalData.theme, //全局颜色变量
			userAddress: null, // 收货地址
			spuCoupons: [], // 可用优惠券
			discountPrice: 0, // 平台优惠券金额
			promotionsPrice: 0, // 折扣金额
			orderConfirmLoading: false,
			infoCouponLoading: false,
			distributionMode: '', // 配送方式：0-快递发货，1-到店自提，2-同城配送费
			couponId: '',
			orderKey: '', // 订单编号，预下单接口不传，下单接口传
			orderSubParms: {
				paymentType: '', // 订单支付类型 1、微信支付；2、支付宝支付；3、银行卡支付 4、转账支付
				orderType, // 订单类型：1-普通，2-立即购买，3-秒杀，4-积分商城，5-付费优惠券，7-礼品卡
				isOpenPoint: 1, // 积分兑换商品：0-关闭，1-开启
				buyerMessage: {}, // 给商家留言
				invoiceRemark: '', // 发票信息
				giftPrice: 0, //礼品卡支付金额
				couponIds: [], // 选择的优惠券列表
				orderTotalPrice: '', // 商品价格
				userAddressId: '', // 地址Id
				paymentPursePrice: '-1', //-1 零钱标记用户默认最高抵扣
				customerGoodsId: this.customerGoodsId, // 新人专享 商品 id
				customerId: this.customerId, // 新人专享 活动 id
				singleSpu: {
					// 商品信息
					spuId: this.goodsSpu.id,
					skuId: this.goodsSku.id,
					quantity: '', // 商品数量
					specInfo: '' // 商品规格
				}
			},
			availableDeductionPoint: 0, // 可用积分
			availableExchangePrice: 0, // 积分商品抵扣价格
			freightPoints: 0, // 积分兑换邮费--积分
			shopList: [{}], // 商品
			shopHoldList: [], // 自提数据
			shopHoldObj: {}, // 当前选择得数据自提
			pageTitle: '商品详情',
			listOrderInfo: [],
			sureDeliveryTime: '', // 同城配送配送时间
			takeTime: '', // 自提时间
			title: '', // 配送-自提时间标题
			givePoints: 0, // 赠送积分
			changprop: '-1', //零钱字段
			// usershow: false, //是否展示零钱钱包
			giftCardPrice: '', //可用礼品卡金额
			giftDeductPrice: '', //礼品卡抵扣金额
			giftPrice: null, //礼品卡支付金额
			giftPwd: '', //礼品卡支付密码
			pursePrice: 0, //零钱余额
			purseDeductPrice: 0, //零钱抵扣金额
			purseStatus: '', //零钱没开通钱包：0；开通没签约：1；开通并签约：2
			payMode: [], //判断显示不显示1钱包2礼品卡
			paymentPursePrice: 0, //零钱 自定义返显默认值
			radioChange: null, //零钱标记用户默认0还是最高抵扣1
			needUserCertified: '0', //礼品卡商品是否需要实名认证 0不需要  1 需要
			userCertifiedPrompt: '', //未实名认证做提示词
			isModalShow: true //处理礼品卡在IOS里显示不全，没有遮罩
		};
	},
	computed: {
		comPFreight() {
			return this.shopList && this.shopList[0] && this.shopList[0].freight ? this.shopList[0].freight : 0;
		},
		// 判断是否可以购买
		promptlyBuySt() {
			const { skus, shelf, verifyStatus, presaleType, spuType, stock } = this.goodsSpu;
			const stocks = skus?.length ? skus.reduce((pre, cur) => cur.stock + pre, 0) : stock; // 计算商品库存
			/**
			 * shelf verifyStatus 上架、审核通过
			 * stock 商品库存
			 * presaleType 预售商品
			 * spuType 3-积分商品，5-付费优惠券；都可用积分兑换
			 * modalSkuType 2-立即购买按钮 3-立即兑换
			 */
			return (
				(shelf == '1' && verifyStatus == '1' && stocks && (!presaleType || presaleType != '1') && this.modalSkuType == '2') ||
				this.modalSkuType == '3' ||
				spuType == 3 ||
				spuType == 5
			);
		},

		// 下单提示
		orderPrompt() {
			return this.shopInfo.shopId == '1549975071225999362' ? '请等待老师送货' : '30分钟可提货';
		},
		// 积分抵扣商品
		creditPointsShopSt: {
			get() {
				const { spuType } = this.goodsSpu;
				return this.availableDeductionPoint && spuType != 3 && spuType != 5;
			},
			set(val) {
				return val;
			}
		},
		// 积分抵扣运费
		creditPointsFreightSt: {
			get() {
				console.log(this.freightPoints && this.availableDeductionPoint && this.goodsSpu.spuType != 5);
				// return this.freightPoints && this.availableDeductionPoint && this.goodsSpu.spuType != 5
			},
			set(val) {
				return val;
			}
		},
		// 积分兑换
		creditPointsSt: {
			get() {
				if (this.creditPointsShopSt && this.orderSubParms.isOpenPoint == 1) {
					// 兑换商品--状态默认开启
					return true;
				} else if (this.creditPointsFreightSt) {
					// 兑换邮费--状态默认关闭
					return false;
				}
				return false;
			},
			set(val) {
				return val;
			}
		},
	},
	watch: {
		//监听实名变化
		AuthenticationType: {
			handler(newVal, oldVal) {
				if (newVal) {
					this.needUserCertified = newVal;
				}
			},
			deep: true
		},

		//监听零钱不变化
		pursePriceVal: {
			handler(newVal, oldVal) {
				if (newVal) {
					this.changprop = newVal;
				}
			},
			deep: true
		},

		goodsSku: {
			handler(newVal, oldVal) {
				if (oldVal && oldVal.id > 0 && newVal.id != oldVal.id) {
					this.orderSubParms.singleSpu.spuId = this.goodsSpu.id;
					this.orderSubParms.singleSpu.skuId = this.goodsSku.id;
					// if(this.goodsSku.minBuyNum>0&&this.cartNum<this.goodsSku.minBuyNum){
					// 	this.orderSubParms.singleSpu.quantity = this.goodsSku.minBuyNum;
					// }
					this.infoCoupon();
				}
			},
			immediate: true,
			deep: true
		},

		// 更新收货方式或者地址
		deviverParams: {
			handler(newVal) {
				this.userAddress = newVal.address;
				this.distributionMode = newVal.distributionMode;
				if (this.distributionMode == 0) {
					this.orderSubParms.userAddressId = this.userAddress.id;
				}
				if (this.distributionMode == 1) {
					this.takeTime = newVal.time;
					if (this.userAddress && this.userAddress.shopId > 0) {
						this.orderSubParms.shopTakeAddressIdMap = {};
						this.orderSubParms.shopTakeAddressIdMap[this.userAddress.shopId] = this.userAddress.id;
					}
				} else if (this.distributionMode == 2) {
					this.sureDeliveryTime = newVal.time;
					this.orderSubParms.userAddressId = this.userAddress.id;
				}

				this.orderSubParms.distributionMode = newVal.distributionMode;
				const flag = this.orderSubParms.singleSpu.skuId && this.goodsSku?.id;
				const same = this.orderSubParms.singleSpu.skuId == this.goodsSku?.id;
				//礼品卡金额重置 0
				this.giftPrice = null;
				if (flag && same) {
					this.orderConfirmDo('deviverParams');
				} else {
					this.infoCoupon();
				}
			},
			deep: true
		},

		// 按钮类型 2-立即购买 ｜ 1-添加购物车
		modalSkuType: {
			handler(newVal) {
				if (newVal == '2' || newVal == '3') this.infoCoupon();
			}
		},
		// 商品数量
		cartNum: {
			handler(newVal) {
				this.orderSubParms.singleSpu.quantity = newVal;
				const flag = this.orderSubParms.singleSpu.skuId && this.goodsSku?.id;
				const same = this.orderSubParms.singleSpu.skuId == this.goodsSku?.id;
				//礼品卡金额重置 0
				this.giftPrice = null;
				if ((this.modalSkuType == '2' || this.modalSkuType == '3') && flag && same) {
					this.orderConfirmDo('cartNum');
				} else if (this.modalSkuType == '2' || this.modalSkuType == '3') {
					this.infoCoupon();
				}
			},
			immediate: true,
			deep: true
		},
		// 商品规格
		goodsSpecData: {
			handler(newVal) {
				if (newVal?.specs) {
					const specsArr = new Array(...newVal.specs);
					let specsInfo = [];
					specsArr.length &&
						specsArr.forEach((item) => {
							item?.specValueName !== '' && specsInfo.push(item.specValueName);
						});

					this.orderSubParms.singleSpu.specInfo = specsInfo.length > 1 ? specsInfo.join(';') : specsInfo.join('');
					if (this.modalSkuType == '2' || this.modalSkuType == '3') {
						this.infoCoupon();
					}
				}
			},
			deep: true
		},
		// 优惠券金额
		discountPrice(newVal) {
			this.$emit('getCouponPrice', newVal);
		},
		// 发票信息
		invoiceRemark(newVal) {
			this.orderSubParms.invoiceRemark = newVal;
		},

		// 选中优惠券
		selectedCouponIds: {
			handler(newVal, oldVal) {
				if (JSON.stringify(newVal) === JSON.stringify(oldVal)) {
					return;
				}
				this.orderSubParms.couponIds = newVal;
				//礼品卡金额重置 0
				this.giftPrice = null;
				if (newVal.includes(this.couponId)) {
					this.couponId = '';
					return;
				}
				if (newVal[0] !== oldVal[0]) {
					this.orderConfirmDo('coupon');
				}
			},
			deep: true
		}
	},
	beforeDestroy() {
		clearInterval(this.verifyCodeTimeout);
		// this.$EventBus.$off('smallchange') //调用零钱组件关闭
		//   //调用组件显示零钱扣款金额
		// this.$EventBus.$off('showgetchange') //调用零钱组件关闭
	},
	created() {
		this.userAddressPage();
		//解决一直累加进行请求
		this.$EventBus.$off('showgetchange');

		// 获取礼品卡密码
		this.$EventBus.$on('gift-pwd-value', (v) => {
			this.giftPwd = v;
		});

		// this.pursepages();
		// 获取零钱金额
		this.$EventBus.$on('showgetchange', (v) => {
			//礼品卡金额重置 0
			this.giftPrice = null;
			this.radioChange = v[1];
			this.changprop = v[0];
			this.orderConfirmDo('showgetchange');
		});
	},
	methods: {
		//处理礼品卡在IOS里显示不全，没有遮罩
		showModal(parameter) {
			this.isModalShow = parameter;
			this.$emit('showModal', this.isModalShow);
		},
		showTransfer(parameter) {
			// showTransfer
			this.$emit('showTransfer', parameter);
		},
		//零钱选着最高抵扣
		async radioPurseDeductPrice(purseDeductPrice, radioChange) {
			if (purseDeductPrice && radioChange == '1') {
				this.changprop = purseDeductPrice;
			}
		},
		//获取礼品卡输入金额
		giftPriceValue(val) {
			this.giftPrice = val;
		},
		//礼品卡调用预下单
		giftOrderConfirm() {
			this.orderConfirmDo('gift');
		},

		// // 默认配送方式数据
		// defaultDistribution() {
		// 	// 展示配送方式
		// 	const arr = [
		// 		{
		// 			type: '0',
		// 			name: '快递发货',
		// 			parameterName: 'distributionVo'
		// 		},
		// 		{
		// 			type: '2',
		// 			name: '同城配送',
		// 			parameterName: 'sameCityVo'
		// 		},
		// 		{
		// 			type: '1',
		// 			name: '到店自提',
		// 			parameterName: 'selfMentionVo'
		// 		}
		// 	];
		// 	const distribution = arr.filter((item) => this.goodsSpu.distributionMode.indexOf(item.type) !== -1);
		// 	return distribution;
		// },
		// getDistributionDate(type) {
		// 	const distribution = this.defaultDistribution();
		// 	if (type == '2' || distribution[0].type == '2') {
		// 		this.title = '同城配送';
		// 		setTimeout(() => {
		// 			this.$nextTick(() => {
		// 				this.$EventBus.$emit('eventName', '2');
		// 			});
		// 		}, 500);
		// 	} else if (type == '1' || distribution[0].type == '1') {
		// 		this.title = '上门自提';
		// 		setTimeout(() => {
		// 			this.$nextTick(() => {
		// 				this.$EventBus.$emit('eventName', '1');
		// 			});
		// 		}, 500);
		// 	}
		// },
		// //获取自提天数
		// getSelfLimitDay() {
		// 	let that = this;
		// 	api.getLivingTag().then((res) => {
		// 		if (res && res.data) {
		// 			that.selfLimitDay = res.data.selfLimitDay ? res.data.selfLimitDay : null;
		// 		}
		// 	});
		// },

		// 获取商品信息、获取优惠券
		async infoCoupon() {
			try {
				if (this.infoCouponLoading) return;
				this.infoCouponLoading = true;
				this.orderSubParms.singleSpu.skuId = this.goodsSku?.id;
				const apiCouponNameId = this.goodsSku?.estimatedPriceVo?.apiCouponNameIdVO;
				let res = null;
				if (apiCouponNameId && !apiCouponNameId?.couponUserId) {
					const params = {
						couponId: apiCouponNameId.couponId,
						errModalHide: true // 不显示错误弹框
					};
					res = await api.couponUserSave(params);
					this.couponId = res?.data?.couponId;
				} else if (apiCouponNameId?.couponUserId) {
					this.couponId = apiCouponNameId.couponId;
				}

				this.orderSubParms.couponIds = this.couponId ? [this.couponId] : [];
				//礼品卡金额重置 0
				this.giftPrice = null;

				if ((this.modalSkuType == '2' || this.modalSkuType == '3') && this.goodsSku?.id) {
					if (res?.code == 0) {
						setTimeout(() => {
							this.orderConfirmDo('infoCoupon');
						}, 500);
					} else {
						this.orderConfirmDo('infoCoupon');
					}
				}
			} catch (e) {
			} finally {
				this.infoCouponLoading = false;
			}
		},
		// 买家留言
		userMessageInput(e) {
			const key = e.target.dataset.shopinfo.id;
			const value = e.detail.value;
			if (this.orderSubParms.buyerMessage[key]) {
				this.orderSubParms.buyerMessage[key] += value;
			} else {
				this.orderSubParms.buyerMessage[key] = value;
			}
		},

		// 配送时间：
		handleDeliveryTime(e, type) {
			if (type == '1') {
				// 到店自提
				this.sureDeliveryTime = '';
				this.takeTime = e;
			} else if (type == '2') {
				// 同城配送
				this.takeTime = '';
				this.sureDeliveryTime = e;
			} else {
				this.sureDeliveryTime = '';
				this.takeTime = '';
			}
		},
		// 获取默认的收货地址收货地址
		async userAddressPage() {
			try {
				const params = {
					searchCount: false,
					current: 1,
					size: 10,
					isDefault: '1'
				};
				const res = await api.userAddressPage(params);
				const records = res.data.records;
				if (records && records.length) {
					this.userAddress = records[0];
					this.orderSubParms.userAddressId = records[0].id;
				}
			} catch (e) {}
		},
		// 积分兑换
		changeSwitchFreight(e) {
			let type = '';
			if (this.creditPointsShopSt) {
				type = 'shop';
			} else if (this.creditPointsFreightSt) {
				type = 'freight';
			}
			this.changeAvailableIntegral(e, type);
		},
		// 开启积分 商品积分兑换、邮费积分兑换
		changeAvailableIntegral(e, type) {
			this.creditPointsSt = e.detail.value;
			//礼品卡金额重置 0
			this.giftPrice = null;

			switch (type) {
				case 'shop':
					this.creditPointsShopSt = true;
					this.creditPointsFreightSt = false;
					this.orderSubParms.isOpenPoint = e.detail.value ? 1 : 0;
					break;
				case 'freight':
					this.creditPointsShopSt = false;
					this.creditPointsFreightSt = true;
					this.orderSubParms.freightPointsSwitch = e.detail.value ? 1 : 2;
					break;
			}
			this.orderConfirmDo('point');
		},

		// 确认订单信息接口
		async orderConfirmDo(reason) {
			console.log('----reason--更新原因--', reason, this.orderSubParms,this.goodsSpu);
			// 礼品卡电子卡默认地址方式-1
			if (this.goodsSpu.spuType == 6 && this.goodsSpu.productType == 2) {
				this.orderSubParms.distributionMode = '-1'
			}
			//必须先有发货方式才调预下单接口
			if(!this.orderSubParms.distributionMode) return
			try {
				// if (this.orderConfirmLoading) return;
				// this.orderConfirmLoading = true;
				uni.showLoading({
					title: '加载中'
				});
				if (this.orderSubParms.orderTotalPrice) {
					delete this.orderSubParms.orderTotalPrice;
				}
				//判断是否有零钱
				if (this.radioChange && this.radioChange == '1') {
					this.orderSubParms.paymentPursePrice = '-1';
				} else {
					this.orderSubParms.paymentPursePrice = this.changprop;
				}

				// 礼品卡
				if (this.goodsSpu.spuType == 6) {
					this.orderSubParms.orderType = 7;
				}

				// 新客专享
				if (this.goodsSpu.newCustomerGoods) {
					this.orderSubParms.orderType = 9;
				}

				//礼品卡金额，giftPrice  礼品卡支付金额 暂不使用 不传，
				if (this.giftPrice && (this.giftPrice != null || this.giftPrice != 'null')) {
					this.orderSubParms.giftPrice = this.giftPrice;
				} else {
					delete this.orderSubParms.giftPrice;
				}

				//礼品卡密码
				if (this.giftPwd) {
					this.orderSubParms.giftPwd = this.giftPwd;
				} else {
					delete this.orderSubParms.giftPwd;
				}

				//分销需要添加的字段
				if (this.orderSubParms && this.orderSubParms.singleSpu) {
					this.orderSubParms.singleSpu = util.dataAddSharerUserCode(this.orderSubParms.singleSpu);
				}

				// 给卖家留言做处理，如果从松雷教育小程序转过来的话,备注上学生的姓名学号等信息
				const schoolUserInfo = util.getStorage(2, 'school_user_info');
				if (schoolUserInfo && schoolUserInfo.name && schoolUserInfo.name != 'undefined') {
					//判断是否已经添加过从中学小程序带过来学生信息
					if (this.orderSubParms.buyerMessage[this.shopInfo.id] && this.orderSubParms.buyerMessage[this.shopInfo.id].indexOf('学生Id') == -1) {
						this.orderSubParms.buyerMessage[
							this.shopInfo.id
						] += ` 性别:${schoolUserInfo.gender};姓名:${schoolUserInfo.name};学生Id:${schoolUserInfo.student_id};联系方式:${schoolUserInfo.parent_contact}`;
					} else {
						this.orderSubParms.buyerMessage[
							this.shopInfo.id
						] = ` 性别:${schoolUserInfo.gender};姓名:${schoolUserInfo.name};学生Id:${schoolUserInfo.student_id};联系方式:${schoolUserInfo.parent_contact}`;
					}
				}
				const data = await api.getOrderGenerate(this.orderSubParms);
				if (data.code == 0) {
					const res = data.data;
					//外层组件需要展示这些信息，回传给外层组件
					this.$emit('preOrder', {...res, isOpenPoint: this.orderSubParms.isOpenPoint});
					this.orderKey = res.orderKey; // 订单编号
					this.orderSubParms.orderTotalPrice = res.totalPrice; // 支付价格
					this.discountPrice = res.discountPrice; // 优惠券金额
					this.promotionsPrice = res.promotionsPrice; // 折扣金额
					this.giftCardPrice = res.giftCardPrice; // 可用礼品卡金额
					this.giftDeductPrice = res.giftDeductPrice; //礼品卡抵扣金额
					this.needUserCertified = res.needUserCertified; //礼品卡商品是否需要实名认证 0不需要  1 需要
					this.userCertifiedPrompt = res.userCertifiedPrompt; //未实名认证提示词
					// this.$emit("changeGiftDeductPrice", this.giftDeductPrice);

					// subtotalPrice 小计
					let subtotalPrice = res.subtotalPrice || 0;
					// reductionPrice 共减
					let reductionPrice = res.reductionPrice || 0;
					this.$emit('priceDiscount', [subtotalPrice, reductionPrice]); //传给父组件展示

					this.pursePrice = res.pursePrice; //零钱可用余额
					this.purseDeductPrice = res.purseDeductPrice; //零钱抵扣金额
					this.purseStatus = res.purseStatus; //零钱 没开通钱包：0；开通没签约：1；开通并签约：2
					this.paymentPursePrice = res.paymentPursePrice; //零钱 自定义返显默认值

					// 判断是否显示零钱或者礼品卡 1是零钱 2是礼品卡 没有就不展示
					this.payMode = res.payMode;

					// 传给零钱兄弟组件
					this.$EventBus.$emit('purse-price', this.pursePrice);
					this.$EventBus.$emit('purse-deduct-price', this.purseDeductPrice);

					this.availableDeductionPoint = res.availableDeductionPoint; // 可用积分
					this.availableExchangePrice = res.availableExchangePrice; // 积分可抵扣价格
					this.freightPoints = res.freightPoints; // 积分

					this.shopList = res.shopList; // 商品

					this.givePoints = res.givePoints; // 赠送积分

					// 处理优惠券数据
					res?.spuCoupons?.length &&
						res.spuCoupons.forEach((item) => {
							if (!res.availableSelectCoupons.includes(item.couponId) && !this.orderSubParms.couponIds.includes(item.couponId)) {
								item.disabled = true;
							}
							if (item.couponId === this.couponId || this.selectedCouponIds.includes(item.couponId)) {
								item.checked = true;
							}
						});
					this.spuCoupons = res.spuCoupons; // 平台券列表
					const availableExchangePrice = this.orderSubParms.isOpenPoint ? res.availableExchangePrice : 0;
					this.$emit('getPayPrice', res.totalPrice, availableExchangePrice);
					this.$emit('getCoupons', this.spuCoupons);
					this.$emit('changeQuoat', false);
				}
			} catch (e) {
				const { code } = e?.data || {};
				if (+code === 1) {
					this.orderKey = '';
					this.giftPrice = '';
					this.$emit('changeQuoat', true);
				}
				// if (e.data.msg == '超过商品限购数量' || e.data.msg == '可用积分不足') { // 禁用立即支付按钮
				//   this.$emilistOrderInfot('changeQuoat', true)
				// }
			} finally {
				uni.hideLoading();
				this.orderConfirmLoading = false;
			}
		},

		// 提交订单
		async orderSub() {
			let that = this;
			if (this.checkAddress()) return;
			if (this.needUserCertified == '1') {
				uni.showModal({
					title: '实名认证',
					content: this.userCertifiedPrompt,
					confirmText: '实名认证',
					confirmColor: '#ff4444',
					success(res) {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/user/user-authentication/index'
							});
						}
					}
				});
				return;
			}
			try {
				if (app.globalData.orderSubLoading) return;
				app.globalData.orderSubLoading = true;
				uni.showLoading({
					title: '加载中'
				});
				this.orderKey && (this.orderSubParms.orderKey = this.orderKey);
				if (this.orderSubParms.distributionMode == 2 && this.sureDeliveryTime && this.sureDeliveryTime.indexOf('undefined') == -1) {
					this.orderSubParms.deliveryTime = this.sureDeliveryTime;
				}
				if (this.orderSubParms.distributionMode == 1 && this.takeTime && this.takeTime.indexOf('undefined') == -1) {
					this.orderSubParms.takeTime = this.takeTime;
				}

				// console.log("this.changprop==>orderSub==>", (this.changprop && this.changprop != "-1" && this
				// 	.changprop != "0"), this.changprop, this.changprop == "-1" && (this.purseDeductPrice &&
				// 	this.purseDeductPrice != '0'), this.purseDeductPrice);

				//判断是否有零钱
				if (this.changprop && this.changprop != '-1' && this.changprop != '0') {
					this.orderSubParms.paymentPursePrice = this.changprop;
				} else if (this.changprop == '-1' && this.purseDeductPrice && this.purseDeductPrice != '0') {
					this.orderSubParms.paymentPursePrice = this.purseDeductPrice;
				} else {
					delete this.orderSubParms.paymentPursePrice;
				}

				// 礼品卡spuType == 6
				if (this.goodsSpu.spuType == 6) {
					this.orderSubParms.orderType = 7;
				}

				if (this.goodsSpu.newCustomerGoods) {
					this.orderSubParms.orderType = 9;
				}

				//礼品卡金额，giftPrice  礼品卡支付金额 暂不使用 不传，
				if (this.giftPrice && this.giftPrice != '0') {
					this.orderSubParms.giftPrice = this.giftPrice;
				} else {
					delete this.orderSubParms.giftPrice;
				}

				//礼品卡密码
				if (this.giftPwd) {
					this.orderSubParms.giftPwd = this.giftPwd;
				} else {
					delete this.orderSubParms.giftPwd;
				}

				// 找人代付为 5，使用的是微信支付
				this.orderSubParms.paymentType = this.$refs.pay?.paymentType == 5 ? 1 : this.$refs.pay?.paymentType;
				const orderData = await api.orderSub(this.orderSubParms);
				if (orderData?.code == 0) {
					// 提交订单成功
					if (orderData.data.listOrderInfo && orderData.data.listOrderInfo.length > 0) {
						orderData.data.listOrderInfo.forEach((order) => {
							const sub_orders = [];
							// 开票需要订单id
							let orders = [];
							orders.push({
								orderId: order.id,
								spuType: this.goodsSpu.spuType
							});
							that.changeInvoicing(true, orders);
							//神策锚点 发送提交订单
							handleSenOrder(order,this.orderSubParms.paymentType, this.orderSubParms.seckillId);
						});
					}
					
					// 如果使用零钱，前端自己需要再计算支付金额，不使用零钱直接用接口返回的
					if (this.changprop != '-1') {
						orderData.data.paymentPrice = this.$parent.payPrice;
					}
					this.$refs.pay?.payOrder(orderData.data);
				} else {
					this.changeInvoicing(false);
				}
			} catch (e) {
			} finally {
				uni.hideLoading();
				app.globalData.orderSubLoading = false;
			}
		},

        
		//是否开票
		changeInvoicing(bool, ord) {
			console.log('=====id===', bool, ord);
			this.$emit('changeInvoicing', bool, ord);
		},

		// 地址校验
		checkAddress() {
			console.error("this.distributionMode===",this.distributionMode);
			console.error("this.distributionMode===",this.orderSubParms,this.goodsSpu.spuType == 6 && this.goodsSpu.productType == 2 && !this.distributionMode && this.orderSubParms.distributionMode == '-1');
			// 是礼品卡电子卡并且默认发货方式是-1，不需要校验地址
			if (this.goodsSpu.spuType == 6 && this.goodsSpu.productType == 2 ) {
				return false;
			}
			if (!this.distributionMode ) {
				uni.showToast({
					title: '请选择收货方式',
					icon: 'none',
					duration: 2000
				});
				return true;
			}
			if (this.distributionMode == '0' || this.distributionMode == '2') {
				if (!this.orderSubParms.userAddressId) {
					uni.showToast({
						title: '请填写收货地址',
						icon: 'none',
						duration: 2000
					});
					return true;
				} else if (this.userAddress?.id > 0 && !(this.userAddress?.longitude > 0 && this.userAddress?.latitude > 0)) {
					uni.showToast({
						title: '请先完善当前收货地址的经纬度',
						icon: 'none',
						duration: 2000
					});
					return true;
				} else {
					return false;
				}
			} else if (+this.distributionMode === 1 && !this.userAddress.id) {
				uni.showToast({
					title: '请先选择自提地址',
					icon: 'none',
					duration: 2000
				});
				return true;
			} else {
				return false;
			}
		},
		changeGoodsShow(bool) {
			this.$emit('changeGoodsShow', bool);
		},

		// 更改店铺地址 已不需要
		// updateShopAddress(obj) {
		// 	this.shopHoldObj = obj;
		// 	// this.$parent.shopHoldObj = this.shopHoldObj;
		// },

		// 跳转 已不需要
		// navHoldTor(id) {
		// 	if (this.shopHoldList.length > 1) {
		// 		let url = '/pages/user/user-hold-address/index?shopId=' + this.shopInfo.id;
		// 		if (+id) {
		// 			url += `&addressId=${id}`;
		// 		}
		// 		uni.navigateTo({
		// 			url
		// 		});
		// 	}
		// },
		// 银行卡刷新
		updataBankList() {
			this.$refs.pay?.getBankList();
		}
	}
};
</script>

<style lang="scss" scoped>
checkbox::before {
	right: 2px !important;
}

.Tips {
	display: flex;
	background-color: #fdf7f7;
	margin: 30rpx 35rpx 0rpx;
	padding: 20rpx;
	border-top-left-radius: 15rpx;
	border-top-right-radius: 15rpx;

	.Tips-title {
		font-size: 22rpx;
		width: 30%;
		color: #e54d42;
		font-weight: 700;
	}

	.Tips-text {
		font-size: 20rpx;
		color: #555555;
	}
}

// 给商家留言
.leave-message {
	width: 83%;
	height: 60rpx;
	border-radius: 20rpx;
	border: 2rpx solid #e6dede;
	padding-left: 20rpx;
}

// 配送方式
.distribution {
	width: 220rpx;
	height: 60rpx;
	border-radius: 40rpx;
	line-height: 60rpx;
}

// 自提地址
.pick-address {
	width: 90%;
	margin: 30rpx auto 20rpx;
	border-radius: 10rpx;
}

// 公用
.mt-20 {
	margin-top: 20rpx;
}

.fc-24 {
	color: #777777;
	font-size: 24rpx;
}

.fc-26 {
	color: #242223;
	font-size: 26rpx;
}

.arrow-width {
	padding-right: 27rpx;
}

.permissions_box {
	padding: 200rpx 30rpx 50rpx;
	background-color: #fff;
	color: #000000;
}
</style>
