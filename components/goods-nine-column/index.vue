<template>
  <view class="goods-container flex">
    <view
      style="width: 33%;"
      v-for="(item, index) in newGoodsList.slice(0,8)"
      :key="index"
      :class="index==3?'margin-right-xl':''"
    >
      <view style="width: 100%;">
        <view :style="{
				  padding: `${goodsSpace*2}rpx`,
				}">
          <view class="goods-box">
            <navigator
              hover-class="none"
              style="background-color: #FFFFFF;"
              :url="'/pages/goods/goods-detail/index?id=' + item.id+ '&source_module=九宫格组件'"
            >
              <view class="img-box">
                <view style="position: relative;">
                  <!-- <image :src="item.picUrls[0] ? item.picUrls[0] : $imgUrl('live/img/no_pic.png')">
								</image> -->
                  <lazy-load
                    :image="item.picUrls[0] |  formatImg360"
                    mode="aspectFill"
                    imgHeight="200"
                  >
                  </lazy-load>

                  <view
                    class="card-icon"
                    v-if="newData"
                    :style="{
                     top: `${newData.imgTop*2}rpx`,
                     left: `${newData.imgLeft*2}rpx`,
                   }"
                  >
                    <view class="card-icon">
                      <image
                        v-if="item.estimatedPriceVo"
                        style=" width: 100rpx !important;height: 100rpx !important;"
                        mode="widthFix"
                        :src="newData.imageUrls ? newData.imageUrls : noPic"
                      />

                      <view
                        v-if="item.estimatedPriceVo&&(item.estimatedPriceVo.totalDiscountPrice&&item.estimatedPriceVo.totalDiscountPrice!='0'&&item.estimatedPriceVo.totalDiscountPrice!='0.0')&&newData.ShowPrice==1"
                        class="text-bold text-white text-sm"
                        :style="{
                              position: 'absolute',
                              bottom: '-2rpx',
                              left: '0rpx',
                              width: '65rpx',
                              height: '35rpx',
                              lineHeight: '35rpx',
                              textAlign: 'center',
                              fontSize: `${newData.topPriceSize*2}rpx`
                          }"
                      >
                        {{item.estimatedPriceVo.totalDiscountPrice}}
                      </view>
                    </view>

                  </view>
                </view>
              </view>

              <view class="text-black margin-top-xs padding-lr-sm overflow-nine text-xs">
                {{item.name}}
              </view>
			  <!-- 积分商品 -->
			  <view v-if="item.spuType == 3 || item.spuType == 5" class="margin-top-xs margin-left-sm">
				<text class="text-gray text-xs">积分：</text>
				<text 
					class="text-red text-lg text-bold " 
					:style="{fontSize:`${newData.priceSize*2}rpx`}"
				>
					{{item.goodsPoints ? item.goodsPoints : 0}}
				</text>
			  </view>
			  <!-- 付费商品 -->
              <view v-else style="position: relative;">
                <view
                  v-if="newData.ShowTagPrice==1"
                  class="text-gray text-xs padding-lr-sm "
                  style=" padding-top: 5rpx;text-decoration:line-through;"
                >
                  吊牌价:{{ item.estimatedPriceVo.originalPrice }}
                </view>

                <view class="flex ">
                  <view style="padding-top: 5rpx;">
                    <view
                      class=" text-gray text-xs padding-left-sm"
                      :style="newData.ShowTagPrice!=1?'margin-top: 10rpx;':''"
                    >
                      到手价:<text class="text-price "></text>
                    </view>
                  </view>
                </view>

                <view
                  class=" text-bold text-red text-lg"
                  :style="{
                    position: 'absolute',
                    bottom: `${newData.priceBottom*2}rpx`,
                    left: `${newData.priceRight*2}rpx`,
                    height: '50rpx',
                    fontSize:`${newData.priceSize*2}rpx`
                  }"
                >
                  {{ item.estimatedPriceVo.estimatedPrice }}
                </view>
              </view>

            </navigator>
          </view>
        </view>

      </view>

    </view>
  </view>
</template>

<script>
import lazyLoad from "components/lazy-load/index";
export default {
  data () {
    return {
      newGoodsList: this.goodsList
    };
  },

  components: {
    lazyLoad
  },
  props: {
    goodsList: {
      type: Array,
      default: () => []
    },
    goodsSpace: {
      type: String | Number,
      default: 8
    },
    newData: {
      type: Object,
      default: () => { }
    }
  },

  mounted () {
  },
  computed: {
  },
  methods: {
  }
};
</script>
<style scoped lang="scss" >
.goods-container {
  justify-content: space-between;
  flex-wrap: wrap;
  box-sizing: content-box;
  /* padding: 20rpx; */
}
.overflow-nine {
  word-break: break-all;
  text-overflow: -o-ellipsis-lastline;
  // text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.card-icon {
  width: 70rpx;
  height: 70rpx;
  position: absolute;
  top: 0rpx;
  left: 0rpx;
}
.goods-box {
  width: 100%;
  // height: 300rpx;
  background-color: #fff;
  overflow: hidden;
  /* margin-bottom: 20rpx; */
  border-radius: 10rpx;
  /* box-shadow: 0rpx 0rpx 30rpx #e5e5e5; */
}

.goods-box .img-box {
  width: 100%;
  height: 200rpx;
  overflow: hidden;
}

.goods-box .img-box image {
  width: 100%;
  height: 200rpx;
}
</style>
