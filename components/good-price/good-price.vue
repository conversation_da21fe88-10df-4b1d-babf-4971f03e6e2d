<template>
	<!-- 商品档期图片只显示底部的样式 -->
	<view
		v-if="spuPriceStyle === 2"
		class="item_price_bg"
		:style="{
			backgroundImage: `url(${spuPicPriceBg || ''})`
		}"
	>
		<view
			class="price-left-content"
			:style="{
				color: spuPriceColorLeft
			}"
			v-if="
				(goodsSpu &&
					goodsSpu.estimatedPriceVo &&
					goodsSpu.estimatedPriceVo.discountPrice &&
					goodsSpu.estimatedPriceVo.discountPrice != '0.0' &&
					goodsSpu.estimatedPriceVo.discountPrice != '0') ||
				(goodsSpu.estimatedPriceVo.promotionsDiscountPrice &&
					goodsSpu.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
					goodsSpu.estimatedPriceVo.promotionsDiscountPrice != '0') ||
				(goodsSpu.estimatedPriceVo.coupon && goodsSpu.estimatedPriceVo.coupon != '0.0' && goodsSpu.estimatedPriceVo.coupon != '0')
			"
		>
			<view
				:style="{
					fontSize: (windowWidth < smallWidth ? 8 : small ? 14 : 18) + 'rpx',
					lineHeight: (windowWidth < smallWidth ? 8 : small ? 14 : 18) + 'rpx',
					paddingLeft: '12rpx',
					paddingTop: (small ? 9 : 13) + 'rpx'
				}"
			>
				预估到手
			</view>
			<view
				:style="{
					paddingBottom: (small ? 8 : 0) + 'rpx',
					color: spuPriceValueColorLeft
				}"
			>
				<text
					:style="{
						fontSize: (windowWidth < smallWidth ? (small ? 8 : 12) : small ? 12 : 18) + 'rpx',
						lineHeight: (windowWidth < smallWidth ? (small ? 8 : 12) : small ? 12 : 18) + 'rpx'
					}"
				>
					￥
				</text>
				<text
					:style="{
						fontWeight: 'bold',
						fontSize: (windowWidth < smallWidth ? (small ? 14 : 16) : small ? 28 : 38) + 'rpx',
						lineHeight: (windowWidth < smallWidth ? (small ? 18 : 22) : small ? 32 : 44) + 'rpx'
					}"
				>
					{{ goodsSpu.estimatedPriceVo.estimatedPrice | filterIntPrice }}
				</text>
				<text
					:style="{
						fontSize: (small ? 22 : 32) + 'rpx',
						fontWeight: 'bold',
						lineHeight: (small ? 22 : 32) + 'rpx'
					}"
				>
					{{ goodsSpu.estimatedPriceVo.estimatedPrice | filterPointPrice }}
				</text>
			</view>
		</view>
		<view
			class="price-right-content"
			:style="{
				paddingTop: (small ? 12 : 18) + 'rpx',
				marginRight: (small ? 4 : 6) + 'rpx'
			}"
		>
			<view
				class="price-signal-item"
				:style="{
					fontSize: (small ? 16 : 26) + 'rpx',
					marginLeft: (small ? 4 : 6) + 'rpx',
					color: spuPriceColorRight
				}"
			>
				=
			</view>
			<view
				class="price-item"
				:style="{
					color: spuPriceColorRight
				}"
			>
				<text
					:style="{
						fontSize: (small ? 14 : 18) + 'rpx',
						marginLeft: '6rpx'
					}"
				>
					零售价
				</text>
				<text>
					<text
						class="price-signal-item"
						:style="{
							fontSize: (small ? 6 : 10) + 'rpx',
							color: spuPriceColorRight
						}"
					>
						￥
					</text>
					<text
						:style="{
							fontSize: (small ? 20 : 28) + 'rpx',
							lineHeight: (small ? 26 : 28) + 'rpx',
							color: spuPriceColorRight
						}"
					>
						{{ goodsSpu.estimatedPriceVo.originalPrice }}
					</text>
				</text>
			</view>
			<view
				class="price-signal-item"
				:style="{
					fontSize: (small ? 8 : 14) + 'rpx',
					color: spuPriceColorRight
				}"
				v-if="discount"
			>
				—
			</view>
			<view
				class="price-item"
				:style="{
					fontSize: (small ? 12 : 14) + 'rpx',
					color: spuPriceColorRight
				}"
				v-if="discount"
			>
				<text
					:style="{
						fontSize: (small ? 14 : 18) + 'rpx',
						marginLeft: '4rpx',
						color: spuPriceColorRight
					}"
				>
					优惠
				</text>
				<view>
					<text
						class="price-signal-item"
						:style="{
							fontSize: (small ? 8 : 14) + 'rpx',
							color: spuPriceColorRight
						}"
					>
						￥
					</text>
					<text
						:style="{
							fontSize: (small ? 20 : 28) + 'rpx',
							lineHeight: (small ? 26 : 28) + 'rpx',
							color: spuPriceColorRight
						}"
					>
						{{ discount }}
					</text>
				</view>
			</view>
		</view>
	</view>
	<!-- 商品档期图片整图覆盖 -->
	<view
		v-else-if="spuPriceStyle === 1"
		class="item_price_bg style2-bg"
		:style="{
			backgroundImage: `url()`,
			height: '100%',
			display: 'flex',
			flexDirection: 'column',
			justifyContent: 'flex-end',
			backgroundSize: '100% 100%',
			borderWidth: '30',
			borderStyle: 'solid',
			borderImageSource: `url(${spuPicPriceBg || ''})`,
			borderImageWidth: !isPhone? '40rpx 60rpx 52rpx 60rpx' :  small ? '40rpx 60rpx 68rpx 60rpx' : '56rpx 60rpx 80rpx 60rpx',
			'clip-path': 'inset(0 round 6px)'
		}"
	>
		<!-- <view v-else class="item_price_bg" :style="{
		  backgroundImage:`url(${spuPicPriceBg || ''})`,
			height: '100%',
			display:'flex',
			flexDirection: 'column',
			justifyContent: 'flex-end',
			backgroundSize: '100% 100%',
		}"> -->
		<view
			class="flex  text-center"
			:class="windowWidth < smallWidth  ? 'justify-center' : 'justify-around'"
			style="position: relative"
			:style="{
				padding: windowWidth < smallWidth ? '0' : small ? '0 6rpx' : '0 10rpx',
				top: windowWidth < smallWidth ? '4rpx' : '0'
			}"
		>
			<view
				class="flex flex-direction text-center self-end justify-between"
				style="padding-top: 0; height: 100%;"
				:style="{
					color: spuPriceColorLeft,
					transform: `scale(${windowWidth < smallWidth ? 0.8 : 1})`,
					flex: 1.3
				}"
				v-if="
					(goodsSpu &&
						goodsSpu.estimatedPriceVo &&
						goodsSpu.estimatedPriceVo.discountPrice &&
						goodsSpu.estimatedPriceVo.discountPrice != '0.0' &&
						goodsSpu.estimatedPriceVo.discountPrice != '0') ||
					(goodsSpu.estimatedPriceVo.promotionsDiscountPrice &&
						goodsSpu.estimatedPriceVo.promotionsDiscountPrice != '0.0' &&
						goodsSpu.estimatedPriceVo.promotionsDiscountPrice != '0') ||
					(goodsSpu.estimatedPriceVo.coupon && goodsSpu.estimatedPriceVo.coupon != '0.0' && goodsSpu.estimatedPriceVo.coupon != '0')
				"
			>
				<view
					:style="{
						'margin-bottom': isIOS? '6rpx': '4rpx',
						fontSize: (small ? 14 : 18) + 'rpx',
						lineHeight: 1,
					}"
				>
					{{ windowWidth < smallWidth || !isPhone ? '预估' : '预估到手' }}
				</view>
				<view
					class="flex align-center justify-center"
					:style="{
						'padding-bottom': !isPhone ? '4rpx' : '0'
					}"
				>
					<price-handle
						:signFont="`${small ? 6 : 20}rpx`"
						:bigFont="`${small ? 24 : 38}rpx`"
						:color="spuPriceValueColorLeft"
						:smallFont="`${small ? 16 : 20}rpx`"
						:value="goodsSpu.estimatedPriceVo.estimatedPrice"
					/>
				</view>
			</view>
			<view
				class="price-signal-item"
				:style="{
					lineHeight: 1,
					fontSize: (small ? 16 : 20) + 'rpx',
					color: spuPriceColorRight,
					transform: `scale(${windowWidth < smallWidth ? 0.8 : 1})`
				}"
			>
				=
			</view>
			<view
				class="flex flex-direction text-center self-end justify-between"
				style="padding-top: 0; height: 100%;"
				:style="{
					fontSize: (windowWidth < smallWidth ? 4 : small ? 12 : 14) + 'rpx',
					color: spuPriceColorRight,
					transform: `scale(${windowWidth < smallWidth ? 0.8 : 1})`,
					flex: 1
				}"
			>
				<view
					style="margin-bottom: 4rpx"
					:style="{
						fontSize: (small ? 14 : 18) + 'rpx',
						lineHeight: 1
					}"
				>
					{{ windowWidth < smallWidth ? '售价' : '零售价' }}
				</view>
				<view
					class="flex align-end justify-center"
					:style="{
						height: `${small ? 30 : 34}rpx`,
						'padding-bottom': windowWidth < smallWidth ? '0' : '2rpx'
					}"
				>
					<price-handle
						:signFont="`${small ? 16 : 20}rpx`"
						:bigFont="`${small ? 24 : 28}rpx`"
						:color="spuPriceValueColorRight"
						:smallFont="`${small ? 16 : 20}rpx`"
						:value="goodsSpu.estimatedPriceVo.originalPrice"
					/>
				</view>
			</view>
			<view
				class="price-signal-item"
				:style="{
					lineHeight: 1,
					fontSize: (small ? 16 : 20) + 'rpx',
					color: spuPriceColorRight,
					transform: `scale(${windowWidth < smallWidth? 0.8 : 1})`
				}"
				v-if="discount"
			>
				—
			</view>
			<view
				class="flex flex-direction text-center self-end justify-between"
				style="padding-top: 0; height: 100%;"
				:style="{
					fontSize: (windowWidth < smallWidth ? 4 : small ? 12 : 14) + 'rpx',
					color: spuPriceColorRight,
					transform: `scale(${windowWidth < smallWidth ? 0.8 : 1})`,
					flex: 1
				}"
				v-if="discount"
			>
				<view
					style="margin-bottom: 4rpx"
					:style="{
						fontSize: (small ? 14 : 18) + 'rpx',
						lineHeight: 1
					}"
				>
					{{ windowWidth < smallWidth ? '优惠' : '优惠' }}
				</view>
				<view
					class="flex align-end justify-center"
					:style="{
						height: `${small ? 30 : 34}rpx`,
						'padding-bottom': windowWidth < smallWidth ? '0' : '2rpx'
					}"
				>
					<price-handle
						:signFont="`${small ? 16 : 20}rpx`"
						:bigFont="`${small ? 24 : 28}rpx`"
						:color="spuPriceValueColorRight"
						:smallFont="`${small ? 16 : 20}rpx`"
						:value="discount"
					/>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp();
import { mapState } from 'vuex';
const util = require("utils/util.js");
export default {
	name: 'good-price',
	props: {
		goodsSpu: {
			type: Object,
			default: () => {}
		},
		small: {
			type: Boolean,
			default: false
		},
		// 自定义覆盖商品图片的活动框
		customCoverImage: {
			type: String,
			default: ''
		}
	},
	filters: {
		filterIntPrice(value) {
			return value > 0 && value.indexOf('.') != -1 ? value.substring(0, value.indexOf('.') + 1) : value;
		},
		filterPointPrice(value) {
			return value > 0 && value.indexOf('.') != -1 ? value.substring(value.indexOf('.') + 1, value.length) : '';
		}
	},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
		}
	},

	computed: {
		...mapState(['windowWidth', 'isPhone', 'padWidth', 'smallWidth', 'isIOS']),
		discount: function () {
			const tempNum =
				Number(this.goodsSpu.estimatedPriceVo.discountPrice || 0) +
				Number(this.goodsSpu.estimatedPriceVo.promotionsDiscountPrice || 0) +
				Number(this.goodsSpu.estimatedPriceVo.coupon || 0);
			return tempNum;
		},

		borderColor: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.spuPicBorder;
			}
			return '';
		},

		spuPicPriceBg: function () {
			if (this.customCoverImage) {
				return this.customCoverImage;
			} else if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				if (customFiled.spuPriceStyle == 1) {
					return customFiled.spuPicPriceBigBg || this.$imgUrl('1/material/b1deb9ff-1687-44e9-a64d-616ad97e17aa.png');
				}
				return customFiled.spuPicPriceBg || 'http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/list_price_bg2.png';
			}
			return 'http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/list_price_bg.png';
		},

		spuPriceColorLeft: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.leftColor;
			} else {
				return '#ffffff';
			}
		},
		spuPriceValueColorLeft: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.leftValueColor || customFiled.leftColor;
			} else {
				return '#ffffff';
			}
		},
		spuPriceColorRight: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.rightColor;
			} else {
				return '#ffffff';
			}
		},

		spuPriceValueColorRight: function () {
			if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.rightValueColor || customFiled.rightColor;
			} else {
				return '#ffffff';
			}
		},
		spuPriceStyle: function () {
			if(this.goodsSpu.showDiscount==0) return ''; //直接不显示优惠信息
			if (this.customCoverImage) {
				return 1;
			} else if (this.theme && this.theme.customFiled) {
				const customFiled = JSON.parse(this.theme.customFiled);
				return customFiled.spuPriceStyle || 2;
			}
			return 2;
		}
	}
};
</script>

<style>
.item_price_bg {
	width: 100%;
	background-image: url(http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/list_price_bg2.png);
	background-size: 100%;
	background-repeat: no-repeat;
	display: flex;
}

.price-left-content {
	flex: 2;
	height: 100%;
	padding-top: 4rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: #ffffff;
	font-size: 18rpx;
}

.price-right-content {
	margin-left: 10rpx;
	flex: 3;
	display: flex;
	/* align-items: center; */
	height: 100%;
	padding-top: 20rpx;
}

.price-signal-item {
	color: #ffffff;
	font-size: 12rpx;
}

.price-item {
	display: flex;
	flex: 1;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: #ffffff;
	font-size: 18rpx;
}

.style2-bg {
	border-image-slice: 105 115 165 95;
	border-image-width: 30px 30px 47px 30px;
	border-image-outset: 0px 0px 0px 0px;
	border-image-repeat: stretch stretch;
}
</style>
