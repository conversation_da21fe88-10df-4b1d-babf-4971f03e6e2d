<template>
  	<!--输入短信验证码组件,与钱包余额通用--> 
		<view
      class="cu-modal"
      :class="hideModal ? 'show' : ''"
    >
      <view
        class="cu-dialog dialog-bg "
        style="background-color: #FFFFFF"
      >
        <view class="bg-img">
          <view
            class="cu-bar text-white justify-center"
            :style="{
              backgroundColor:'#FFFFFF',
              paddingTop: '32rpx'
              }"
          >
            <view class="codePhone">
              <div>发送验证码至</div>
              <div>{{mobileNo}}</div>
            </view>
            <div
              class="cuIcon-close"
              style="color:#000000;position: absolute;right: 20rpx;top: 20rpx;"
              @tap="noHideModal"
            ></div>
          </view>

          <view style="background-color:'#FFFFFF'">
            <view class="line">
            </view>

            <view class="margin-top">
              <view style="color: #000000; font-size:34rpx">
                请输入验证码
              </view>
              <view style="color: #000000;margin:30rpx 0">

                <input
                  class="cinput"
                  adjust-position="false"
                  auto-blur="true"
                  @blur="blur"
                  @input="codenum"
                  :focus="focus"
                  value="code"
                  v-model="code"
                  type="number"
                  maxlength="6"
                />
                <view class="code-input">
                  <view
                    v-for="(item,index) in 6"
                    :key="index"
                    @click="codefocus(index)"
                    :style='(index == code.length? "border: 5rpx solid #1195db;width: 80rpx;height: 80rpx;line-height: 80rpx;":"color: " + codeclolor + ";" +"border: 2rpx solid" + codeclolor)'
                  >
                    {{code[index]||''}}
                  </view>
                </view>

              </view>

            </view>
          </view>
        </view>
      </view>
    </view>
</template>

<script>
export default {
  data() {
			return {
        code: '',//手机验证码 .toString().replace(/,/gi,'-')
				// 验证码输入聚焦
				focus: false,//input焦点，用于键盘隐藏后重新唤起
				// 验证码框颜色
				codeclolor: "#313131",//自定义光标的颜色,
        //隐藏弹框
        hideModal:this.showModal
      };
		},
  props: {
    // 弹框控制
    showModal:{
      type:Boolean,
      default: false
    },
  //手机号
    mobileNo: {
				type: String,
				default: ''
			},
  //父组件传的验证接口
    fatherMethod: {
        type: Function,
        default: null
      }
  },

  watch:{
    showModal(newVal,oldval){
      this.hideModal =newVal
    },
    // deep:true,
    // immediate:true
  },
  methods:{
    		  // 输入验证码
			codenum: function (event) {
				let that = this
				let code = event.target.value
				that.code = code
				if (code.length == 6) {
					// if (code == '123456') {
					//输入六位验证码后自动进行验证并执行验证成功的函数
					// console.log("code", this.code);
					// this.form.phoneverCode =code
          this.$emit('change', this.code);
					//2.如果需要验证码要传给后台
					//第三个接口
					// this.fatherMethod()
					}
			},

			// 键盘隐藏后设置失去焦点
			blur: function () {
				let that = this
				that.focus = false
			},

			// 点击自定义光标显示键盘
			codefocus: function (e) {
				let that = this
				if (e == that.code.length) {
					console.log("eeee", e);
					that.focus = true
				}
			},

			//隐藏弹框
			noHideModal () {
				this.hideModal = false
        this.$emit('HideModal', this.hideModal);
				this.code =''
    	},
  }

}
</script>

<style lang="less" scoped>
.code-input {
  margin: auto;
  width: 519rpx;
  height: 100rpx;
  display: flex;
}

.code-input > view {
  margin-top: 5rpx;
  margin-left: 15rpx;
  width: 74rpx;
  height: 74rpx;
  line-height: 74rpx;
  font-size: 60rpx;
  font-weight: bold;
  color: #313131;
  text-align: center;
  border-radius: 10rpx;
}

.code-input > view:nth-child(1) {
  margin-left: 0rpx;
}

.cinput {
  position: fixed;
  left: -100rpx;
  width: 50rpx;
  height: 50rpx;
}


.line {
  width: 519rpx;
  margin: 0 auto;
  border-bottom: 1rpx dotted #ccc;
}

.line:after {
  height: 3rpx;
  content: '';
  display: block;
  border-top: 1rpx dotted #ccc;
}

.codePhone {
  color: #000000;
  font-size: 30rpx;
}

</style>>