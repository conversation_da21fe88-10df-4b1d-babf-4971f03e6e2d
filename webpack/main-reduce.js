const path = require("path")
const _ = require('lodash');

const isWin = /^win/.test(process.platform)

const normalizePath = path => (isWin ? path.replace(/\\/g, '/') : path)

function removeExt (str, ext) {
  if (ext) {
    const reg = new RegExp(ext.replace(/\./, '\\.') + '$')
    return normalizePath(str.replace(reg, ''))
  }
  return normalizePath(str.replace(/\.\w+$/g, ''))
}


function restoreNodeModules (str) {
  if (process.env.UNI_PLATFORM === 'mp-alipay') {
    str = str.replace('node-modules/npm-scope-', 'node-modules/@')
  }
  str = str.replace('node-modules', 'node_modules')
  return str
}

// 这里是获取各个分包的名称
const subPackageRoots = Object.keys(process.UNI_SUBPACKAGES).map(
  (root) => {
    return root + "/"
  }
)

// 获得主包页面
const mainPackageRoots = process.UNI_PAGES.pages.map(
  root => {
    return root.path
  }
)
// 主包组件
const mainComponents = new Set([]);

// 只能第一次进入
let hasEmit = false

function isComponent(chunk) {
  let name = chunk.name
  if (!~name.indexOf("common")) {
      let isComponent = true
      for (const chunkGroup of chunk.groupsIterable) {
          isPage = chunkGroup.isInitial()
          if (isPage) {
              isComponent = false
          }
      }
      if (isComponent) return true
  }
  return false
}


function createComponent(oldName, newName, type, assets) {
  let newFile = _.cloneDeep(assets[`${oldName}.${type}`]);
 
  let newSource;
  let oldNameReg;

  const newSourceFn = function() {
    return newSource
  }
  newSourceFn.__$wrappered = true
  
  if(type === 'js' || type === 'json') {
    newSource = newFile.source();
    oldNameReg = new RegExp(oldName, 'g')
    newSource = newSource.replace(oldNameReg, newName);
    newFile.source = newSourceFn;
  }

  return newFile;
}

class MoveComponents {
  constructor(options = {}) {
    this.options = options;
    this.subpackList = [];
    this.moveNames = [];
    this.childNames = [];
  }
  apply(compiler) {
    if(process.env.NODE_ENV !== 'production') {
      return;
    }
    // 初始化compiler
    compiler.hooks.thisCompilation.tap('MoveComponents', (compilation) => {
      compilation.hooks.optimizeChunks.tap('MoveComponents', chunks => {
        //  找到所有组件
        let componentChunk = chunks.filter((e) => isComponent(e))
        // 找到主包组件，这里不是通用的，我只找了components主包
        let subpackageComponentChunk = componentChunk.filter((e) => e.name.startsWith('components/'))
        // 找到所有主包页面没有用到的组件
        this.subpackList = filterComponents(subpackageComponentChunk);
		console.log("========this.subpackList=====",this.subpackList);
        // this.subpackList = this.subpackList.filter(item => (item.name !== 'components/jyf-parser/jyf-parser' && item.name !== 'components/jyf-parser/libs/trees'));
        this.subpackList = this.subpackList.filter(item => (!(item.name == 'components/jyf-parser/libs/trees' || item.name == 'components/delivery-timeperiod/delivery-timeperiod' ||  item.name == 'components/smallchange/smallchange'  ||  item.name == 'components/pay-components/pay-components' ||  item.name == 'components/goods-sku/gift-modal' || item.name == 'components/verification-code/index' )));
        this.moveNames = this.copyComponent(this.subpackList) || [];
        this.moveNames = this.componentAddComponent();
        // 已经复制完成了，现在就差在最后将json和html放入了
        if(this.subpackList.length && !hasEmit) {
          hasEmit = true;
          compiler.hooks.emit.tapPromise('MoveComponents', compilation => {
            const assets = compilation.assets
            let wxmlDelSet = new Set()
            this.moveNames.forEach(({ oldName, newName, parentName, replaceReg }) => {
              wxmlDelSet.add(oldName)
              if (assets[oldName + ".wxml"]) {
                assets[newName + ".wxml"] = createComponent(oldName, newName, 'wxml', assets);
              }
              if (assets[oldName + '.wxss']) {
                assets[newName + ".wxss"] = createComponent(oldName, newName, 'wxss', assets);
              }
              if (assets[oldName + ".json"]) {
                assets[newName + ".json"] = createComponent(oldName, newName, 'json', assets);
              }
              if (assets[oldName + ".js"]) {
                assets[newName + ".js"] = createComponent(oldName, newName, 'js', assets);
              }
              
              if(assets[parentName + ".json"]) {
                let newSource = assets[parentName + ".json"].source();
                newSource = newSource.replace(oldName, newName);
                const newSourceFn = function() {
                  return newSource
                }
                newSourceFn.__$wrappered = true
                assets[parentName + ".json"].source = newSourceFn
              }
            })
            for (var oldName of wxmlDelSet) {
              if (assets[oldName + ".wxml"]) {
                delete assets[oldName + ".wxml"]
              }
              if (assets[oldName + '.wxss']) {
                delete assets[oldName + ".wxss"]
              }
              if (assets[oldName + ".json"]) {
                delete assets[oldName + ".json"]
              }
              if (assets[oldName + ".js"]) {
                delete assets[oldName + ".js"]
              }
            }
            return Promise.resolve()
          })
        }
      })
    })
  }
  copyComponent(subpackList) {
    let moveNames = []
    // 移动组件
    // const chunkComponent = subpackList[0];
    for(let chunkComponent of subpackList) {
      // 组件之前的名字
      const oldName = chunkComponent.name;
      
      // 父页面
      let parentsPage;
      let groupPage;
      for(let group of chunkComponent.groupsIterable) {
        parentsPage = group.getParents()
        groupPage = group;
      }
      
      
  
      // 通过父页面循环， 将组件都放进父页面所在的分包
      for(let parentChunk of parentsPage) {
        // 组件父页面的名字
        const parentName = parentChunk.options.name;

        // 当前分包的名称
        const pkgName = subPackageRoots.find((root) => parentName.indexOf(root) === 0)
        if(!pkgName) {
          // 组件父页面不是页面，是组件，先将它放入childNames
          this.childNames.push({
            oldName,
            parentName
          })
          continue;
        }
        // 新名字
        const newName = pkgName + 'mainComponents/' + oldName;
  
        moveNames.push({
          oldName,
          newName,
          parentName
        });
      }
    }
  
    return moveNames
  }
  componentAddComponent() {
    const moveNames = [...this.moveNames];
    let childNames = [...this.childNames];
    let childName = childNames
    let copyNameList = [];
    do {
      childName = [];
      childNames.forEach(item => {
        let obj = {};
        let arr = moveNames.filter(e => e.oldName === item.parentName).filter(e => {
          if(obj[e.newName]) {
            return false;
          } 
          obj[e.newName] = true;
          return true
        });

        
        copyNameList.filter(e => e.oldName === item.parentName).forEach(n => {
          copyNameList.push({
            oldName: item.oldName,
            parentName: n.newName,
            newName: n.newName.replace(n.oldName, item.oldName)
          })
        });

        arr.forEach(e => {
          copyNameList.push({
            oldName: item.oldName,
            parentName: e.newName,
            newName: e.newName.replace(e.oldName, item.oldName)
          })
        })
        
        
      })
      childNames = childName;
    } while(childName.length)

    return [...moveNames, ...copyNameList];
  }
}

// 将组件中主包所有的组件剔除
function filterComponents(subpackageComponentChunk) {
  return subpackageComponentChunk.filter((item, index) => {
    // 判断主包组件中是否有它，有就过滤掉
    if(mainComponents.has(item.name)){
      return false;
    }          
    let status
    for (const group of item.groupsIterable) {
      status = findPkgs(group)
    } 
    // 组件在主包页面中使用过, 放入主包组件
    if(!status) {
      mainComponents.add(item.name);
    }
    return status   
  })
}


// 判断当前是否是分包页面
function findPkgs(chunkGroup) {
  if (!chunkGroup) return
  let parentChunkGroups = chunkGroup.getParents()
  for (let i = 0; i < parentChunkGroups.length; i++) {
    const status = judgePage(parentChunkGroups[i]);
    if(!status) {
      // 是主包组件，组件直接返回false
      // 将主包组件的children放入主包中，也不能动
      let childChunkGroups = chunkGroup.getChildren();
      addDeepMainComponent(childChunkGroups);
      return false
    }
    return true
  }
}

// 深度遍历添加主组件
function addDeepMainComponent(childChunkGroups = []) {
  if(childChunkGroups.length) {
    childChunkGroups.forEach(item => {
      mainComponents.add(item.options.name);
      childern = item.getChildren();
      addDeepMainComponent(childern)
    }) 
  }
}

// 判断当前是主包页面或组件
function judgePage(group) {
  let name = group.options.name
  let pkg = mainPackageRoots.find((root) => name.indexOf(root) === 0)
  if(pkg) {
    // 有主包页面引用，返回false
    return false
  }
  // 如果是组件的话, 看看组件里面有没有
  let pkgCom = mainComponents.has(name);
  if(pkgCom) {
    // 如果主组件有的话，返回false
    return false;
  }

  return true;
}

// 路径更改
function generatePath(name) {
  const modulePath = removeExt(restoreNodeModules(name))
  let resPath = normalizePath(
      path.resolve(process.env.UNI_INPUT_DIR, modulePath)
  )
  return resPath
}

function findReplaceModule(modules, name) {
  let resource = generatePath(name)
  let mainModule = findModule(modules, resource)
  if (!mainModule) {
      throw new Error("编译失败：找不到迁移组件")
  }
  return mainModule
}

function findModule(modules, resource) {
  return modules.find((module) => {
      let moduleResource = module.resource
      if (
          !moduleResource ||
          (moduleResource.indexOf(".vue") === -1 &&
              moduleResource.indexOf(".nvue") === -1)
      ) {
          return
      }
      moduleResource = removeExt(module.resource)
      return moduleResource === resource
  })
}

module.exports = MoveComponents;