import Vue from 'vue';
import store from './store'
import App from './App';
import cuCustom from '@/public/colorui/components/cu-custom.vue'

Vue.component('cu-custom', cuCustom)

//时间日期js处理库
import moment from '@/public/moment/moment.js'
import myShare from '@/public/common/share.js' //自定义分享功能
Vue.mixin(myShare)
// window.eventBus = new Vue();

// 将 $imgUrl 设为方法
Vue.prototype.$imgUrl = function(path) {
  return 'https://img.songlei.com/' + path.replace(/^\//, '');
};

Vue.prototype.$EventBus = new Vue();
Vue.prototype.$moment = moment //赋值使用

import common from "@/utils/common.js";
Vue.prototype.$noMultipleClicks = common.noMultipleClicks

// #ifdef H5
// 路由API： http://hhyang.cn/src/router/start/quickstart.html
import Router, {
	RouterMount
} from 'uni-simple-router';
Vue.use(Router)
//初始化
const router = new Router({
	encodeURI: false, //不编码传输
	h5: {
		loading: false,
	},
	routes: ROUTES //路由表
});

router.afterEach((to, from) => { // 页面跳转后做的处理操作
	if (history) {
		let query = to.query
		delete query.detail
		//H5会自动给每个跳转url自动加上tenant_id、app_id，以便链接的复制
		if (!query.tenant_id) {
			query.tenant_id = App.globalData.tenantId
		}
		if (!query.app_id) {
			query.app_id = App.globalData.appId
		}
		if (!query.component_appid) {
			query.component_appid = App.globalData.componentAppId
		}
		// 记录进入app时的url
		if (typeof window.entryUrl === 'undefined' || window.entryUrl === '') {
			window.entryUrl = location.href.split('#')[0]
		}
		var ary = [];
		for (var p in query) {
			if (query.hasOwnProperty(p) && query[p]) {
				ary.push(encodeURIComponent(p) + '=' + encodeURIComponent(query[p]));
			}
		}
		if (ary.length > 0) {
			let url = "?" + ary.join('&');
			// history.replaceState(null, null, url);//替换页面显示url
			setTimeout(() => {
				history.replaceState(history.state, null,
					url); // uni-simple-router 组件中应该做了什么处理操作，这里延迟替换路由页面
			}, 100);
		}
	}
})
import 'utils/ican-H5Api'; // 对齐H5的部分API，保持API通用跨平台；文档：https://ext.dcloud.net.cn/plugin?id=415
// #endif

import * as filters from './filter' // 全局过滤器

// 全局过滤器注册
Object.keys(filters).forEach(key => {
	Vue.filter(key, filters[key])
});


Vue.prototype.$onLaunched = new Promise(resolve => {
	Vue.prototype.$isResolve = resolve;
})
Vue.prototype.$store = store;

Vue.config.productionTip = false;
App.mpType = 'app';

const app = new Vue({
	store,
	...App
});

//v1.3.5起 H5端 你应该去除原有的app.$mount();使用路由自带的渲染方式
// #ifdef H5
RouterMount(app, '#app');
// #endif

// #ifndef H5
app.$mount(); //为了兼容小程序及app端必须这样写才有效果
// #endif