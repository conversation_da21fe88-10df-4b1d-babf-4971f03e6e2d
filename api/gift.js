import {
  requestApi as request
} from '@/utils/api.js'

// 礼品卡支付二维码
export const getPaypayCode = () => {
  return request({
    url: '/mallapi/giftCard/rcode',
    method: 'get',
	showLoading: false,
  })
}

// 礼品卡支付查询是否设置密码
export const getIsPassword = () => {
  return request({
    url: '/mallapi/paypaymentuserpassword',
    method: 'get',
    showLoading: false,
  })
}

// 礼品卡支付验证密码
export const passwordCheck = (data = {}) => {
  return request({
    url: '/mallapi/paypaymentuserpassword/passwordCheck',
    method: 'post',
    data
  })
}

// 红包信息
export const getRedPackInfo = () => {
  return request({
    url: '/mallapi/giftCard/redPack',
    method: 'get',
  })
}