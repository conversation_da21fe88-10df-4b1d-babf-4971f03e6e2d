import {
	requestApi as request
} from '@/utils/api.js'


// 模板消息
export const getInfo = () => {
	return request({
		url: '/mallapi/pushinternalmsg/getInternalMsg',
		method: 'get',
		data: { tag: '1' }
	})
}

// 模板消息
export const getList = (data) => {
	return request({
		url: '/mallapi/pushinternalmsg/page',
		method: 'get',
		data
	})
}

// 更新， 已读和删除
export const update = (data) => {
	return request({
		url: '/mallapi/pushinternalmsg/update',
		method: 'post',
    showLoading: false,
		data
	})
}

// 维新订阅模板信息
export const getWxTemplate = (data) => {
	return request({
		url: '/mallapi/pushnotificationconfig/getWxTemplateIdsByType',
		method: 'get',
		data
	})
}

