import {
	requestApi as request
} from '@/utils/api.js'
import {
	senTrack,
	getCurrentTitle
} from "@/public/js_sdk/sensors/utils.js"

// 提交信息
export const submitInfo = (data = {}, errModalHide = false) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid,
		erpCustType,
		phone
	} = userInfo;
	 data = {
		channel: 'SONGSHU',
		custId: erpCid,
		mobile: phone,
		num: 1,
		amount: 0,
		points: 0,
		...data
	}
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.exec.submit',
		method: 'post',
		data,
		errModalHide
	}).then(res => {
		// 埋点字段从接口返回值里取
		// 埋点字段从接口返回值里取
		module.exports.getActiveDetails({
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}, false).then(res => {
			const coupon = res.data;
			const params = {
				page_name: getCurrentTitle(0), // 建议页面传
				coupon_id: coupon.typeId || '',
				coupon_name: coupon.typeName || '',
				coupon_type: coupon.useTypeName || '',
				coupon_use_method: coupon.eventTypeName || '',
				effect_time: coupon.eventEffDatetime || '',
				expire_time: coupon.eventExpDatetime || '',
				coupon_buy_number: data.num,
				coupon_threshold_amount: coupon.condAmount > 0 ? coupon.condAmount : 0,
				coupon_amount: coupon.faceValue,
				is_success: res && res.code === 0,
				fail_reason: ''
			}
			if (coupon.eventTypeCode == '5' || coupon.eventTypeCode == '4' || coupon
				.eventTypeCode == '1') {
				params.coupon_buy_method = coupon.eventTypeCode == '5' ? '人民币+积分' : coupon
					.eventTypeCode == '4' ? '积分' : coupon.eventTypeCode == '1' ? '人民币' : '';
				params.coupon_buy_integral = coupon.points * data.num;
				params.coupon_buy_amount = coupon.cash * data.num;
			}
			senTrack('CouponReceive', params);
		}).catch(e => {
			console.error(e)
		});

		return res;
	}).catch(err => {
		// 失败埋点
		// 获取优惠券信息
		module.exports.getActiveDetails({
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}).then(res => {
			const coupon = res.data;
			const params = {
				page_name: getCurrentTitle(0), // 建议页面传
				coupon_id: coupon.typeId || '',
				coupon_name: coupon.typeName || '',
				coupon_type: coupon.useTypeName || '',
				coupon_use_method: coupon.eventTypeName || '',
				effect_time: coupon.eventEffDatetime || '',
				expire_time: coupon.eventExpDatetime || '',
				coupon_threshold_amount: coupon.condAmount > 0 ? coupon.condAmount : 0,
				coupon_amount: coupon.faceValue,
				coupon_buy_number: data.num,
				is_success: false,
				fail_reason: (err && err.msg) || (err && err.message) || JSON.stringify(err)
			}
			if (coupon.eventTypeCode == '5' || coupon.eventTypeCode == '4' || coupon
				.eventTypeCode == '1') {
				params.coupon_buy_method = coupon.eventTypeCode == '5' ? '人民币+积分' : coupon
					.eventTypeCode == '4' ? '积分' : coupon.eventTypeCode == '1' ? '人民币' : '';
				params.coupon_buy_integral = coupon.points* data.num;
				params.coupon_buy_amount = coupon.cash * data.num;
			}
			senTrack('CouponReceive', params);
		}).catch(e => {
			console.error(e)
		})

		throw err;
	});
}

// 券激活接口
export const activeAccntNo = (id) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid
	} = userInfo;
	return request({
		url: '/sco-coupon-admin/batchcoupon/activebyaccntno',
		method: 'post',
		data: {
			custId: erpCid,
			channel: 'SONGSHU',
			accntNo: id
		}
	})
}

// 获取活动详情   isSenTrack  是否发生神策埋点 
export const getActiveDetails = (data = {}, isSenTrack = true) => {
	const userInfo = uni.getStorageSync('user_info');
	const {
		erpCid,
		erpCustType
	} = userInfo;
	return request({
		url: '/sco-activity-api/rest?method=sco.activity.api.info.getinfo',
		method: 'post',
		data: {
			channel: 'SONGSHU',
			custId: erpCid,
			...data
		}
	}).then(res => {
		// 上传神策埋点
		const coupon = res.data;
		if (isSenTrack) {
			const params = {
				page_name: getCurrentTitle(0), // 建议页面传
				coupon_id: coupon.typeId || '',
				coupon_name: coupon.typeName || '',
				coupon_type: coupon.useTypeName || '',
				coupon_use_method: coupon.eventTypeName || '',
				effect_time: coupon.eventEffDatetime || '',
				expire_time: coupon.eventExpDatetime || '',
				coupon_threshold_amount: coupon.condAmount > 0 ? coupon.condAmount : 0,
				coupon_amount: coupon.faceValue,
			}
			if (coupon.eventTypeCode == '5' || coupon.eventTypeCode == '4' || coupon
				.eventTypeCode == '1') {
				params.coupon_buy_method = coupon.eventTypeCode == '5' ? '人民币+积分' : coupon
					.eventTypeCode == '4' ? '积分' : coupon.eventTypeCode == '1' ? '人民币' : '';
				params.coupon_buy_integral = coupon.points;
				params.coupon_buy_amount = coupon.cash;
			}

			senTrack('CouponDetailPageView', params);
		}
		return res;
	}).catch(err => {
		console.error(err);
		throw err;
	})
}