import {
  requestApi as request
} from '@/utils/api.js'

//获取大转盘信息和奖品信息
export const getActivityInfo = (data) => {
  return request({
    url: '/mallmarket/actmarket/actandprize',
    method: 'get',
	data
  })
}

// 春节红包 是否在为页面弹出大转盘
export const getRedPacketPop = () => {
  return request({
    url: '/mallmarket/actinfo/pop',
    method: 'get',
  })
}


// 获取参加活动人的信息
export const getActivityUser = (data = {}) => {
  return request({
    url: '/mallmarket/actuser',
    method: 'post',
    showLoading: false,
    data,
	contentType: 'application/x-www-form-urlencoded; charset=UTF-8'
  })
}


//获取历史中奖人
export const getHistoryUsers = (data) => {
  return request({
    url: '/mallmarket/actmarket/carousel',
    method: 'get',
	data
  })
}


// 中奖信息远程获取
export const getPrizeInfo = (id) => {
  return request({
    url: '/mallmarket/actmarket/'+id,
    method: 'get',
	showLoading: false
  })
}



// 当前用户当前大转盘获取清单
export const getUserPrizes = (data) => {
  return request({
    url: '/mallmarket/actmarket/userprizes',
    method: 'get',
	data,
	showLoading: false
  })
}

// 领取奖品
export const receivePrize = (data) => {
  return request({
    url: '/mallmarket/actmarket/receive',
    method: 'get',
	data,
	showLoading: false
  })
}



// 竞猜活动奖品列表
export const getCompetitionPrize = () => {
  return request({
    url: '/mallmarket/actprize/match',
    method: 'get',
	showLoading: false
  })
}


// 竞猜活动场次列表
export const getCompetitionGame = () => {
  return request({
    url: '/mallmarket/match/game',
    method: 'get',
	showLoading: false
  })
}

// 竞猜活动下注接口
export const getCompetitionBetting = (data) => {
  return request({
    url: '/mallmarket/match/betting',
    method: 'post',
	showLoading: true,
	data
  })
}



// 我的竞猜列表 默认定位到的位置
export const getMyCompetitionType = () => {
  return request({
    url: '/mallmarket/match/currentType',
    method: 'get',
  })
}



// 我的竞猜列表
export const getMyCompetitionList = (data) => {
  return request({
    url: '/mallmarket/match/myGuess',
    method: 'get',
	showLoading: true,
	data
  })
}

// 竞猜游戏，首页弹框
export const guessPop = () => {
  return request({
    url: '/mallmarket/match/guessPop',
    method: 'get',
	showLoading: true
  })
}

//获取金牌活动首页信息
export const getGoldGameInfo = () => {
  return request({
    url: '/mallmarket/gold/game',
    method: 'get',
	  showLoading: false,
  })
}

//获取金牌活动首页大转盘奖品列表信息
export const getActprizeGameInfo = () => {
  return request({
    url: '/mallmarket/actprize/gold',
    method: 'get',
	  showLoading: false,
  })
}

// 用户参与竞猜接口
export const getUserGoldGame = (data) => {
  return request({
    url: '/mallmarket/gold/guess',
    method: 'post',
    showLoading: true,
    data
  })
}

// 我的金牌竞猜列表
export const getMyGoldList = () => {
  return request({
    url: '/mallmarket/gold/myGuess',
    method: 'get',
    showLoading: true,
  })
}

// 我的金牌竞猜首页弹框
export const getGoldPopResult = () => {
  return request({
    url: '/mallmarket/gold/popResult',
    method: 'get',
    showLoading: true,
  })
}

// 顶部送券弹框
export const topPop = (data) => {
  return request({
    url: '/mallapi/scenefeed/pop',
    method: 'get',
    showLoading: false,
    data
  })
}

// 通过分享获取助力信息口
export const getAssistanceInfo = (data) => {
  return request({
    url: '/mallmarket/acthelpuser/info',
    method: 'get',
    showLoading: false,
    data
  })
}

// 助力
export const assistanceAction = (data) => {
  return request({
    url: '/mallmarket/acthelpuser/help',
    method: 'get',
    showLoading: false,
    data
  })
}


// 获取活动场次
export const getActCycle = (data) => {
  return request({
    url: '/mallmarket/actmarket/actcycle',
    method: 'get',
    showLoading: true,
    data
  })
}


export const getInviteInfo = (data) => {
  return request({
    url: '/mallmarket/actmarket/invite',
    method: 'get',
    showLoading: true,
    data
  })
}


export function getTiktokLivingList() {
  return request({
    url: `/mallmarket/tiktokinfo/getOne`,
    method: 'get'
  })
}
