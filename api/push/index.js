// 初始化Push组件
export function startPush() {
	const jyJPush = uni.requireNativePlugin('JY-JPushThird');
	if (!jyJPush) {
		return
	}
	const systemInfo = uni.getSystemInfoSync().platform;
	if (systemInfo === 'android') {
		jyJPush && jyJPush.android_init(result => {
			console.log('初始化android推送：' + JSON.stringify(result));
		});
	} else {
		jyJPush.ios_requestNotificationAuthorization(result => {
			/*
			status = 0，用户尚未对该应用的权限做出选择
			status = 1, 被关闭了权限
			status = 2, 开启了权限
			status = 3, 开启了非中断用户通知权限，iOS12支持
			*/
			console.log('初始化iost推送：' + JSON.stringify(result));
		});
	}


	jyJPush.addJYJPushReceiveNotificationListener(result => {
		console.log('收到了' + JSON.stringify(result));

	});

	jyJPush.addJYJPushReceiveOpenNotificationListener(result => {
		console.log('打开了' + JSON.stringify(result));
		let data = result;
		const systemInfo = uni.getSystemInfoSync().platform;
		console.log("systemInfo:" + systemInfo);
		if (systemInfo === 'android') {
			let tmp = JSON.parse(result);
			console.log(tmp.n_extras);
			data = tmp.n_extras;
			data.title = tmp.n_title;
		} else {
			data.title = data.aps.alert.title;
		}
		if (data.type == 1) {
			uni.navigateTo({
				url: data.path,
			});
		} else if (data.type == 2) {
			uni.navigateTo({
				url: '/pages/public/webPage?url=' + encodeURIComponent(data.path) +
					'&title=' + encodeURIComponent(data.title),
			});
		}
	});
}

// 绑定用户ID，针对用户推送
export function setPushAlias(eid) {
	setTimeout(function() {
		const jyJPush = uni.requireNativePlugin('JY-JPushThird');
		console.log("准备去推送绑定别名" + eid);
		jyJPush.deleteJYJPushAlias({
			//  可以不用传值进去，但是需要配置这项数据
		}, result => {
			console.log("先删除别名");
			jyJPush.setJYJPushAlias({
				userAlias: eid || 'sandbox'
			}, result => {
				//  设置成功或者失败，都会通过这个result回调返回数据；数据格式保持极光返回的安卓/iOS数据一致
				//  注：若没有返回任何数据，考虑是否初始化完成
				console.log("推送绑定别名结果====>" + JSON.stringify(result));
				// jyJPush.getJYJPushAlias({
				// 	// , ;可以不用传值进去，但是需要配置这项数据
				// }, result => {
				// 	console.log("======getJYJPushAlias=======", result);
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: JSON.stringify('查询设备的别名是:' + JSON.stringify(
				// 			result))
				// 	})
				// });
			});
		});

	}, 500);
}

// 退出登录，解除绑定
export function deletePushAlias() {
	const jyJPush = uni.requireNativePlugin('JY-JPushThird');
	jyJPush.deleteJYJPushAlias({

	}, result => {
		console.log(JSON.stringify(result));
	});
}

// 绑定用户组，按组推送
export function setPushTag(tag) {
	setTimeout(function() {
		const jyJPush = uni.requireNativePlugin('JY-JPushThird');
		jyJPush.addJYJPushTagsWithArr({
			userTags: [tag]
		}, result => {
			console.log(JSON.stringify(result));
		});
	}, 500);
}

// 解除所有用户组
export function cleanJPushTags() {
	const jyJPush = uni.requireNativePlugin('JY-JPushThird');
	jyJPush.cleanJYJPushTags(result => {
		console.log(JSON.stringify(result));
	});
}

function addJYJPushReceiveOpenNotificationListener() {
	const jyJPush = uni.requireNativePlugin('JY-JPushThird');
	jyJPush.addJYJPushReceiveOpenNotificationListener(result => {
		console.log('打开了' + JSON.stringify(result));
		setTimeout(function() {
			let data = JSON.parse(result.notificationExtras);
			uni.navigateTo({
				url: data.path,
			});
		}, 1000);
	});
}

function addJYJPushReceiveNotificationListener() {
	const jyJPush = uni.requireNativePlugin('JY-JPushThird');
	jyJPush.addJYJPushReceiveNotificationListener(result => {
		console.log('收到了' + JSON.stringify(result));
	});
}

function addJYJPushReceiveBackgroudNotificationListener() {
	const jyJPush = uni.requireNativePlugin('JY-JPushThird');
	jyJPush.addJYJPushReceiveBackgroudNotificationListener(result => {
		console.log('后台收到了' + JSON.stringify(result));
	});

}

function addJYJPushCustomReceiveNotificationListener() {
	const jyJPush = uni.requireNativePlugin('JY-JPushThird');
	jyJPush.addJYJPushCustomReceiveNotificationListener(result => {
		console.log('后台收到了' + JSON.stringify(result));
	});
}