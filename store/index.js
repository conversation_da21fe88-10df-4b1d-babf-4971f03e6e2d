// 页面路径：store/index.js
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex); //vue的插件机制

//Vuex.Store 构造器选项
const store = new Vuex.Store({
	state: {
		//存放数据
		windowWidth: 0, // 屏幕宽度
		pixelRatio: 0, // 密度比
		CustomBar: 0,
		HeightBar: 0,
		StatusBar: 0,
		menuHeight: 0,
		menuWidth: 0,
		leftMenuWidth: 0,
		leftMenuHeight: 0,
		rootFontSize: 16, // 默认值
	},

	mutations: {
		updateData(state, payload) {
			Object.assign(state, payload);
		},
		setRootFontSize(state, size) {
			state.rootFontSize = size;
			console.log("==size====");
		}
	},
	actions: {
		updateRootFontSize({
			commit
		}) {
			const systemInfo = uni.getSystemInfoSync();
			const screenWidth = systemInfo.windowWidth;
			const rootFontSize = screenWidth >= 400 ? 1 * 0.7 : 1; // 根据屏幕宽度动态设置
			commit('setRootFontSize', rootFontSize);
		},
	},
})
export default store