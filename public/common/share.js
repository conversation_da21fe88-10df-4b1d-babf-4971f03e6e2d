import api from 'utils/api'
import utils from 'utils/util'

export default {
  data() {
    return {
      share: {
        title: '', //标题
        imageUrl: '', //图片大小建议500*400
        path: '',
        scene: '' //场景值
      }
    }
  },

 

  onLoad(options) {
    // console.log("======options=====",options)
    utils.saveSceneCode(options)   
    // --------小程序分享朋友圈开始------
    // #ifdef MP-WEIXIN
    // uni.showShareMenu({
    //   withShareTicket: true,
    //   menus: ['shareAppMessage', 'shareTimeline']
    // });

    // --------小程序分享朋友圈结束------
    let getLaunchOptions = uni.getLaunchOptionsSync()
    this.scene = getLaunchOptions.scene;
    const app = getApp();
    const {
      gdt_vid,
      weixinadinfo
    } = options;
    if (gdt_vid) {
      app.globalData.gdtVid = gdt_vid;
    }
	//  console.log("获取weixinadinfo---", weixinadinfo);
    if (weixinadinfo) {
      let weixinadinfoArr = weixinadinfo.split('.');
      app.globalData.weixinadinfoId = weixinadinfoArr[0];
    }
    // #endif
   
    //白名单
    const urlList = [
      "goods-detail/index", //商品详情页
      'pages/home/<USER>', //首页
      "pages/third-tab/index",
      "pages/micro-page/index",
      "pages/second-tab/index", //松鼠好物
      "pages/shopping-cart/index", //购物车
      "pages/tab-personal/index", //个人中心
      "pages/signrecord/signrecord-info/index", //积分签到
      "pages/shop/shop-detail/index", //店铺首页
      "pages/public/webview/webview", // 大转盘页面
	   "pages/public/activity/webview", // 大转盘页面
    ]
    //获取路由信息
    const pages = getCurrentPages()
    //获取当前路由
    let nowPage = pages[pages.length - 1]
    //判断路由包含‘PackageA’标识的同时不包含有白名单的路由就禁用
    // if (nowPage.route.search('tPackageA') != -1 && !urlList.includes(nowPage.route)) {
    //   uni.hideShareMenu()
    // }
    // if (!urlList.includes(nowPage.route)) {
    //   uni.hideShareMenu()
    // }
    if (nowPage.route.search('order') != -1) {
      uni.hideShareMenu()
    }
  },

  onShareAppMessage(res) { //发送给朋友
    // return this.$x.mpShare
    const app = getApp();
    this.postTimelineAdvertAction('SHARE');
    if (this.share.imageUrl) this.share.imageUrl + '-jpg_w360_q90'

    return {
      title: this.share.title,
      imageUrl: this.share.imageUrl,
      // title: '测试标题',
      // imageUrl: 'https://img.songlei.com/1/material/0975066e-67d4-4165-abe9-0e4d49de1ecc.png',
      path: this.share.path, //分享默认打开是小程序首页
      success(res) {
        uni.showToast({
          title: '分享成功'
        })
      },
      fail(res) {
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        })
      }
    }
  },

  // #ifdef MP-WEIXIN
  //朋友圈
  onShareTimeline(res) {
    // 获取加载的页面
    let pages = getCurrentPages(),
      // 获取当前页面的对象
      view = pages[pages.length - 1];
    // console.log("获取加载的页面", pages);
    console.log("当前页面的对象", view, this.share);

    // this.share.path = `/${view.route}`;

    // return this.$x.mpShare
    this.postTimelineAdvertAction('SHARE');
    return {
      title: this.share.title ? this.share.title : '松鼠美淘',
      imageUrl: this.share.imageUrl,
      query: this.share.path ? this.share.path : view.$page.fullPath, //分享默认打开是小程序首页
      success(res) {
        uni.showToast({
          title: '分享成功'
        })
      },
      fail(res) {
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        })
      }
    }
  },
  // #endif


  methods: {
  
    postTimelineAdvertAction(action) {
      const app = getApp();
      if (app.globalData.gdtVid) {
        api.postTimelineAdvertAction({
          action,
          clickId: app.globalData.gdtVid || ''
        });
      }
    },
  }
}