// 此方法是接口请求方法
/**** 此文件说明请看注释 *****/
// 可以用自己项目的请求方法
// 请求配置说明：
import configService from '@/config/env.js'
/**** 结束 *****/
const platform = uni.getSystemInfoSync().platform;
export default {
	// 发起ajax请求获取服务端版本号
	getServerNo: (version, isPrompt = false, callback) => {
		let httpData = {
			version: version.versionCode,
			// 版本名称
			versionName: version.versionName,
			// setupPage参数说明（判断用户是不是从设置页面点击的更新，如果是设置页面点击的更新，有不要用静默更新了，不然用户点击没反应很奇怪的）
			setupPage: isPrompt
		};
		if (platform == "android") {
			httpData.type = 1101;
		} else {
			httpData.type = 1102;
		}
		/* 接口入参说明
		 * version: 应用当前版本号（已自动获取）
		 * versionName: 应用当前版本名称（已自动获取）
		 * type：平台（1101是安卓，1102是IOS）
		 */
		/****************以下是示例*******************/
		// 可以用自己项目的请求方法（接口自己找后台要，插件不提供）
		uni.request({
			url: configService.appUpdateUrl,
			success: (rets) => {
				var javaData = rets.data;
				console.log("====javaData===", javaData)
				// forcibly = 强制更新, solicit = 弹窗确认更新, silent = 静默更新
				let res = {
					...rets.data
					// "versionCode": 3491,
					// "versionCodeIOS": 1101,
					// "versionName": "3.4.91",
					// "versionInfo": "松鼠美淘优化功能，提升系统稳定性",
					// "forceUpdate": false,
					// "updateType": "solicit",
					// "downloadUrl": "https://img.songlei.com/apkdev/meitao/__UNI__9C3B8BC_0913092539.apk",
					// "downloadUrlIOS": "",
					// "huawei": 3471,
					// "xiaomi": 3491,
					// "samsung": 3491,
					// "vivo": 3491,
					// "oppo": 3491,
					// "qq": 3491,
				}
				plus.runtime.getProperty(plus.runtime.appid, function(wgtinfo) {
					let client_version = wgtinfo.versionCode;
					//ios用ios的专属字段比对
					if (httpData.type == '1102') {
						res.versionCode = res.versionCodeIOS;
						res.downloadUrl = res.downloadUrlIOS;
					}
					console.log('当前版本', client_version, '最新版本', res.versionName);
					let flag_update = client_version < res.versionCode;
					if (flag_update) //true 有新版本 false 没有新版本
					{
						if (res && res.downloadUrl) {
							// 兼容之前的版本（updateType是新版才有的参数）
							if (res.updateType) {
								callback && callback(res);
							} else {
								if (res.forceUpdate) {
									//强制更新
									res.updateType = "forcibly";
								} else {
									// 弹窗确认更新
									res.updateType = "solicit";
								}
								callback && callback(res);
							}
						} else if (isPrompt) {
							uni.showToast({
								title: "已是最新版本",
								icon: "none"
							});
						}
					}

				});
			}
		});

		// http.get('/upgrade/sysAppUpgrade/list').then(par => {

		// res的数据说明
		// * | 参数名称        | 一定返回     | 类型        | 描述
		// * | -------------|--------- | --------- | ------------- |
		// * | versionCode     | y        | int       | 版本号        |
		// * | versionName     | y        | String    | 版本名称      |
		// * | versionInfo     | y        | String    | 版本信息      |
		// * | updateType      | y        | String    | forcibly = 强制更新, solicit = 弹窗确认更新, silent = 静默更新 |
		// * | downloadUrl     | y        | String    | 版本下载链接（IOS安装包更新请放跳转store应用商店链接,安卓apk和wgt文件放文件下载链接）  |

		// }).catch(err => {
		// 	console.log(err);
		// });

		// });  
		/****************以上是示例*******************/
	},
	// 弹窗主颜色（不填默认粉色）
	appUpdateColor: "f00",
	// 弹窗图标（不填显示默认图标，链接配置示例如： '/static/demo/ic_attention.png'）
	appUpdateIcon: ''
}