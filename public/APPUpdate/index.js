// #ifdef APP-PLUS 
import componentConfig from "./componentConfig"
const platform = uni.getSystemInfoSync().platform;  // ios 如果直接写 ios就都是跳转链接了
//手机品牌
const brand = uni.getSystemInfoSync().brand.toLowerCase();
// 主颜色
const $mainColor = "0081ff";
// 弹窗图标url
const $iconUrl = componentConfig.appUpdateIcon ? componentConfig.appUpdateIcon :
	"https://img.songlei.com/live/update/ic_ar.png";
let tempPath = "";
// 获取当前应用的版本号
export const getCurrentNo = function(callback) {
	// 获取本地应用资源版本号
	plus.runtime.getProperty(plus.runtime.appid, function(inf) {
		console.log('本地版本号', inf);
		callback && callback({
			versionCode: inf.versionCode,
			versionName: inf.version
		});
	});
}

// 安装本地下载好的文件
const installClient = function(callback) {
	console.log("=====installClient=====")
	let popupData = {
		progress: false,
		buttonNum: 1
	};
	let popupObj = downloadPopup(popupData);
	// 重启APP
	popupObj.reboot = function() {
		plus.runtime.restart();
	}
	//说明最新版本已下载完成，无需再次下载
	const loadedFile = uni.getStorageSync("lastDownloadFile");
	plus.runtime.install(loadedFile.fileName, {}, function() {
		console.error("安装本地文件成功");
		popupObj.change({
			contentText: /\.wgt$/i.test(loadedFile.fileName) ? "应用资源更新完成!" : "请先安装再重启!",
			buttonNum: 1,
			progress: false
		});
	}, function(e) {
		callback && callback();
		console.error("安装文件失败[" + e.code + "]：" + e.message);
		// plus.nativeUI.alert("安装文件失败[" + e.code + "]：" + e.message);
	});
}

// 从服务器下载应用资源包（wgt文件）
const getDownload = function(data) {
	//测试静默更新代码
	// data.updateType = 'silent';
	let dtask;
	if (data.updateType == 'forcibly' || data.updateType == 'solicit') {
		// forcibly = 强制更新, solicit = 弹窗确认更新, silent = 静默更新 
		let popupData = {
			progress: true,
			buttonNum: 2
		};
		if (data.updateType == 'forcibly') {
			popupData.buttonNum = 0;
		}
		let lastProgressValue = 0;
		let popupObj = downloadPopup(popupData);
		// 取消下载
		popupObj.cancelDownload = function() {
			dtask && dtask.abort();
			uni.showToast({
				title: "已取消下载",
				icon: "none"
			});
		}
		// 重启APP
		popupObj.reboot = function() {
			plus.runtime.restart();
		}
		dtask = plus.downloader.createDownload(data.downloadUrl, {
			filename: "_doc/update/"
		}, function(download, status) {
			if (status == 200) {
				popupObj.change({
					progressValue: 100,
					progressTip: "正在安装文件...",
					progress: true,
					buttonNum: 0
				});
				tempPath = download.filename;
				uni.setStorageSync("lastDownloadFile", {
					versionCode: data.versionCode,
					fileName: download.filename
				});
				const loadedFile = uni.getStorageSync("lastDownloadFile");
				plus.runtime.install(download.filename, {}, function() {
					popupObj.change({
						contentText: /\.wgt$/i.test(data.downloadUrl) ? "应用资源更新完成!" :
							"请先安装再重启!",
						buttonNum: 1,
						progress: false
					});
				}, function(e) {
					popupObj.cancel();
					plus.nativeUI.alert("安装文件失败[" + e.code + "]：" + e.message);
				});
			} else {
				popupObj.change({
					contentText: "文件下载失败...",
					buttonNum: 1,
					progress: false
				});
			}
		});
		dtask.start();
		dtask.addEventListener("statechanged", function(task, status) {
			switch (task.state) {
				case 1: // 开始
					popupObj.change({
						progressValue: 0,
						progressTip: "准备下载...",
						progress: true
					});
					break;
				case 2: // 已连接到服务器  
					popupObj.change({
						progressValue: 0,
						progressTip: "开始下载...",
						progress: true
					});
					break;
				case 3:
					const progress = parseInt(task.downloadedSize / task.totalSize * 100);
					if (progress - lastProgressValue >= 2) {
						lastProgressValue = progress;
						popupObj.change({
							progressValue: progress,
							progressTip: "已下载" + progress + "%",
							progress: true
						});
					}
					break;
			}
		});
	} else if (data.updateType == "silent") {
		// forcibly = 强制更新, solicit = 弹窗确认更新, silent = 静默更新 |
		dtask = plus.downloader.createDownload(data.downloadUrl, {
			filename: "_doc/update/"
		}, function(download, status) {
			if (status == 200) {
				uni.setStorageSync("lastDownloadFile", {
					versionCode: "asda",
					fileName: download.filename
				});
				const loadedFile = uni.getStorageSync("lastDownloadFile");
				tempPath = download.filename;
				plus.runtime.install(download.filename, {}, function() {
					console.log("应用资源更新完成");
				}, function(e) {
					plus.nativeUI.alert("安装文件失败[" + e.code + "]：" + e.message);
				});
			} else {
				plus.nativeUI.alert("文件下载失败...");
			}
		});
		dtask.start();
	}
}
// 文字换行
function drawtext(text, maxWidth) {
	let textArr = text.split("");
	let len = textArr.length;
	// 上个节点
	let previousNode = 0;
	// 记录节点宽度
	let nodeWidth = 0;
	// 文本换行数组
	let rowText = [];
	// 如果是字母，侧保存长度
	let letterWidth = 0;
	// 汉字宽度
	let chineseWidth = 14;
	// otherFont宽度
	let otherWidth = 7;
	for (let i = 0; i < len; i++) {
		if (/[\u4e00-\u9fa5]|[\uFE30-\uFFA0]/g.test(textArr[i])) {
			if (letterWidth > 0) {
				if (nodeWidth + chineseWidth + letterWidth * otherWidth > maxWidth) {
					rowText.push({
						type: "text",
						content: text.substring(previousNode, i)
					});
					previousNode = i;
					nodeWidth = chineseWidth;
					letterWidth = 0;
				} else {
					nodeWidth += chineseWidth + letterWidth * otherWidth;
					letterWidth = 0;
				}
			} else {
				if (nodeWidth + chineseWidth > maxWidth) {
					rowText.push({
						type: "text",
						content: text.substring(previousNode, i)
					});
					previousNode = i;
					nodeWidth = chineseWidth;
				} else {
					nodeWidth += chineseWidth;
				}
			}
		} else {
			if (/\n/g.test(textArr[i])) {
				rowText.push({
					type: "break",
					content: text.substring(previousNode, i)
				});
				previousNode = i + 1;
				nodeWidth = 0;
				letterWidth = 0;
			} else if (textArr[i] == "\\" && textArr[i + 1] == "n") {
				rowText.push({
					type: "break",
					content: text.substring(previousNode, i)
				});
				previousNode = i + 2;
				nodeWidth = 0;
				letterWidth = 0;
			} else if (/[a-zA-Z0-9]/g.test(textArr[i])) {
				letterWidth += 1;
				if (nodeWidth + letterWidth * otherWidth > maxWidth) {
					rowText.push({
						type: "text",
						content: text.substring(previousNode, i + 1 - letterWidth)
					});
					previousNode = i + 1 - letterWidth;
					nodeWidth = letterWidth * otherWidth;
					letterWidth = 0;
				}
			} else {
				if (nodeWidth + otherWidth > maxWidth) {
					rowText.push({
						type: "text",
						content: text.substring(previousNode, i)
					});
					previousNode = i;
					nodeWidth = otherWidth;
				} else {
					nodeWidth += otherWidth;
				}
			}
		}
	}
	if (previousNode < len) {
		rowText.push({
			type: "text",
			content: text.substring(previousNode, len)
		});
	}
	return rowText;
}
// 是否更新弹窗
function updatePopup(data, callback, callback2) {
	// 弹窗遮罩层
	let maskLayer = new plus.nativeObj.View("maskLayer", { //先创建遮罩层
		top: '0px',
		left: '0px',
		height: '100%',
		width: '100%',
		backgroundColor: 'rgba(0,0,0,0.5)'
	});
	// 以下为计算菜单的nview绘制布局，为固定算法，使用者无关关心
	const screenWidth = plus.screen.resolutionWidth;
	const screenHeight = plus.screen.resolutionHeight;
	//弹窗容器宽度
	const popupViewWidth = screenWidth * 0.7;
	// 弹窗容器的Padding
	const viewContentPadding = 20;
	// 弹窗容器的宽度
	const viewContentWidth = parseInt(popupViewWidth - (viewContentPadding * 2));
	// 描述的列表
	const descriptionList = drawtext(data.versionInfo, viewContentWidth);
	// 弹窗容器高度
	let popupViewHeight = 80 + 20 + 20 + 90 + 10;

	let popupViewContentList = [{
			src: $iconUrl,
			id: "logo",
			tag: "img",
			position: {
				top: "0px",
				left: (popupViewWidth - 124) / 2 + "px",
				width: "124px",
				height: "80px",
			}
		},
		{
			tag: 'font',
			id: 'title',
			text: "发现新版本" + data.versionName,
			textStyles: {
				size: '18px',
				color: "#333",
				weight: "bold",
				whiteSpace: "normal"
			},
			position: {
				top: '90px',
				left: viewContentPadding + "px",
				width: viewContentWidth + "px",
				height: "30px",
			}
		}
	];
	const textHeight = 18;
	let contentTop = 130;
	descriptionList.forEach((item, index) => {
		if (index > 0) {
			popupViewHeight += textHeight;
			contentTop += textHeight;
		}
		popupViewContentList.push({
			tag: 'font',
			id: 'content' + index + 1,
			text: item.content,
			textStyles: {
				size: '14px',
				color: "#666",
				lineSpacing: "50%",
				align: "left"
			},
			position: {
				top: contentTop + "px",
				left: viewContentPadding + "px",
				width: viewContentWidth + "px",
				height: textHeight + "px",
			}
		});
		if (item.type == "break") {
			contentTop += 10;
			popupViewHeight += 10;
		}
	});
	console.log("没有市场更新按钮", platform != "android" || /\.wgt$/i.test(data.downloadUrl) || (data.qq < data
		.versionCode && (!data[brand] ||
			(data[brand] && data[brand] < data.versionCode))));

	if (data.updateType == "forcibly") {
		//强制更新  如果是ios、wgt更新、qq和市场版本号都低于oss上的 只有一个更新按钮  如果是安装
		if (platform != "android" || /\.wgt$/i.test(data.downloadUrl) || (data.qq < data.versionCode && (!data[brand] ||
				(data[brand] && data[brand] < data.versionCode)))) {
			popupViewContentList.push({
				tag: 'rect', //绘制底边按钮
				rectStyles: {
					radius: "6px",
					color: $mainColor
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: viewContentWidth + "px",
					height: "30px"
				}
			});
			popupViewContentList.push({
				tag: 'font',
				id: 'confirmText',
				text: "立即升级",
				textStyles: {
					size: '14px',
					color: "#FFF",
					lineSpacing: "0%",
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: viewContentWidth + "px",
					height: "30px"
				}
			});
		} else {
			// 绘制底边按钮
			popupViewContentList.push({
				tag: 'rect',
				id: 'marketBox',
				rectStyles: {
					radius: "3px",
					color: $mainColor,
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'rect',
				id: 'confirmBox',
				rectStyles: {
					radius: "3px",
					color: $mainColor,
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) / 2 + viewContentPadding * 2) + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'font',
				id: 'marketText',
				text: "市场升级",
				textStyles: {
					size: '14px',
					color: "#FFF",
					lineSpacing: "0%",
					whiteSpace: "normal"
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'font',
				id: 'confirmText',
				text: "本地升级",
				textStyles: {
					size: '14px',
					color: "#FFF",
					lineSpacing: "0%",
					whiteSpace: "normal"
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) / 2 + viewContentPadding * 2) + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			});

		}
	} else {
		//强制更新  如果是ios、wgt更新、qq和市场版本号都低于oss上的 只有一个更新按钮  如果是安装
		if (platform != "android" || /\.wgt$/i.test(data.downloadUrl) || (data.qq < data.versionCode && (!data[brand] ||
				(data[brand] && data[brand] < data.versionCode)))) {
			// 绘制底边按钮
			popupViewContentList.push({
				tag: 'rect',
				id: 'cancelBox',
				rectStyles: {
					radius: "3px",
					borderColor: "#f1f1f1",
					borderWidth: "1px",
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'rect',
				id: 'confirmBox',
				rectStyles: {
					radius: "3px",
					color: $mainColor,
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) / 2 + viewContentPadding * 2) + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'font',
				id: 'cancelText',
				text: "暂不升级",
				textStyles: {
					size: '14px',
					color: "#666",
					lineSpacing: "0%",
					whiteSpace: "normal"
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'font',
				id: 'confirmText',
				text: "立即升级",
				textStyles: {
					size: '14px',
					color: "#FFF",
					lineSpacing: "0%",
					whiteSpace: "normal"
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) / 2 + viewContentPadding * 2) + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			});
		} else {
			// 绘制底边按钮
			popupViewContentList.push({
				tag: 'rect',
				id: 'cancelBox',
				rectStyles: {
					radius: "3px",
					borderColor: "#f1f1f1",
					borderWidth: "1px",
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: (viewContentWidth - viewContentPadding) / 3 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'rect',
				id: 'marketBox',
				rectStyles: {
					radius: "3px",
					color: $mainColor,
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) / 3 + viewContentPadding * 2 - 10) + "px",
					width: (viewContentWidth - viewContentPadding) / 3 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'rect',
				id: 'confirmBox',
				rectStyles: {
					radius: "3px",
					color: $mainColor,
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) * 2 / 3 + viewContentPadding * 2) + "px",
					width: (viewContentWidth - viewContentPadding) / 3 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'font',
				id: 'cancelText',
				text: "暂不升级",
				textStyles: {
					size: '14px',
					color: "#666",
					lineSpacing: "0%",
					whiteSpace: "normal"
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: (viewContentWidth - viewContentPadding) / 3 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'font',
				id: 'marketText',
				text: "市场升级",
				textStyles: {
					size: '14px',
					color: "#FFF",
					lineSpacing: "0%",
					whiteSpace: "normal"
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) / 3 + viewContentPadding * 2 - 10) + "px",
					width: (viewContentWidth - viewContentPadding) / 3 + "px",
					height: "30px",
				}
			});
			popupViewContentList.push({
				tag: 'font',
				id: 'confirmText',
				text: "立即升级",
				textStyles: {
					size: '14px',
					color: "#FFF",
					lineSpacing: "0%",
					whiteSpace: "normal"
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) * 2 / 3 + viewContentPadding * 2) + "px",
					width: (viewContentWidth - viewContentPadding) / 3 + "px",
					height: "30px",
				}
			});
		}

	}
	// 弹窗内容
	let popupView = new plus.nativeObj.View("popupView", { //创建底部图标菜单
		tag: "rect",
		top: (screenHeight - popupViewHeight) / 2 + "px",
		left: '15%',
		height: popupViewHeight + "px",
		width: "70%"
	});
	// 绘制白色背景
	popupView.drawRect({
		color: "#FFFFFF",
		radius: "8px"
	}, {
		top: "40px",
		height: popupViewHeight - 40 + "px",
	});
	popupView.draw(popupViewContentList);
	popupView.addEventListener("click", function(e) {
		let maxTop = popupViewHeight - viewContentPadding;
		let maxLeft = popupViewWidth - viewContentPadding;
		let buttonWidth = (viewContentWidth - viewContentPadding) / 2;
		if (e.clientY > maxTop - 30 && e.clientY < maxTop) {
			if (data.updateType == "forcibly") {
				if (platform != "android" || /\.wgt$/i.test(data.downloadUrl) || (data.qq < data.versionCode &&
						(!data[brand] ||
							(data[brand] && data[brand] < data.versionCode)))) {
					if (e.clientX > viewContentPadding && e.clientX < maxLeft) {
						// 立即升级
						maskLayer.hide();
						popupView.hide();
						callback && callback();
					}
				} else {
					// 市场升级
					if (e.clientX > viewContentPadding && e.clientX < maxLeft - buttonWidth -
						viewContentPadding) {
						callback2 && callback2();
					} else if (e.clientX > maxLeft - buttonWidth && e.clientX < maxLeft) {
						// 本地升级
						maskLayer.hide();
						popupView.hide();
						callback && callback();
					}
				}
			} else {
				if (platform != "android" || /\.wgt$/i.test(data.downloadUrl) || (data.qq < data.versionCode &&
						(!data[brand] ||
							(data[brand] && data[brand] < data.versionCode)))) {
					// 暂不升级
					if (e.clientX > viewContentPadding && e.clientX < maxLeft - buttonWidth -
						viewContentPadding) {
						maskLayer.hide();
						popupView.hide();
					} else if (e.clientX > maxLeft - buttonWidth && e.clientX < maxLeft) {
						// 立即升级
						maskLayer.hide();
						popupView.hide();
						callback && callback();
					}
				} else {
					// 3个按钮
					// 暂不升级
					if (e.clientX > viewContentPadding && e.clientX < (viewContentWidth - viewContentPadding) /
						3 +
						viewContentPadding) {
						maskLayer.hide();
						popupView.hide();
					} else if (e.clientX > ((viewContentWidth - viewContentPadding) / 3 + viewContentPadding *
							2 - 10) && e.clientX < ((viewContentWidth - viewContentPadding) * 2 / 3 +
							viewContentPadding * 2 - 10)) {
						// 市场升级
						callback2 && callback2();
					} else if (e.clientX > ((viewContentWidth - viewContentPadding) * 2 / 3 +
							viewContentPadding * 2) && e.clientX < ((viewContentWidth - viewContentPadding) +
							viewContentPadding * 2)) {
						// 立即升级
						maskLayer.hide();
						popupView.hide();
						callback && callback();
					}
				}

			}
		}
	});

	if (data.updateType == "solicit") {
		// 点击遮罩层
		maskLayer.addEventListener("click", function() { //处理遮罩层点击
			maskLayer.hide();
			popupView.hide();
		});
	}
	// 显示弹窗
	maskLayer.show();
	popupView.show();
}

// 文件下载的弹窗绘图
function downloadPopupDrawing(data) {
	// 以下为计算菜单的nview绘制布局，为固定算法，使用者无关关心
	const screenWidth = plus.screen.resolutionWidth;
	const screenHeight = plus.screen.resolutionHeight;
	//弹窗容器宽度
	const popupViewWidth = screenWidth * 0.7;
	// 弹窗容器的Padding
	const viewContentPadding = 20;
	// 弹窗容器的宽度
	const viewContentWidth = popupViewWidth - (viewContentPadding * 2);
	// 弹窗容器高度
	let popupViewHeight = viewContentPadding * 3 + 60;
	let progressTip = data.progressTip || "准备下载...";
	let contentText = data.contentText || "正在为您更新，请耐心等待";
	let elementList = [{
			tag: 'rect', //背景色
			color: '#FFFFFF',
			rectStyles: {
				radius: "8px"
			}
		},
		{
			tag: 'font',
			id: 'title',
			text: "升级APP",
			textStyles: {
				size: '16px',
				color: "#333",
				weight: "bold",
				verticalAlign: "middle",
				whiteSpace: "normal"
			},
			position: {
				top: viewContentPadding + 'px',
				height: "30px",
			}
		},
		{
			tag: 'font',
			id: 'content',
			text: contentText,
			textStyles: {
				size: '14px',
				color: "#333",
				verticalAlign: "middle",
				whiteSpace: "normal"
			},
			position: {
				top: viewContentPadding * 2 + 30 + 'px',
				height: "20px",
			}
		}
	];
	// 是否有进度条
	if (data.progress) {
		popupViewHeight += viewContentPadding + 40;
		elementList = elementList.concat([{
				tag: 'font',
				id: 'progressValue',
				text: progressTip,
				textStyles: {
					size: '14px',
					color: $mainColor,
					whiteSpace: "normal"
				},
				position: {
					top: viewContentPadding * 4 + 20 + 'px',
					height: "30px"
				}
			},
			{
				tag: 'rect', //绘制进度条背景
				id: 'progressBg',
				rectStyles: {
					radius: "4px",
					borderColor: "#f1f1f1",
					borderWidth: "1px",
				},
				position: {
					top: viewContentPadding * 4 + 60 + 'px',
					left: viewContentPadding + "px",
					width: viewContentWidth + "px",
					height: "8px"
				}
			},
		]);
	}
	if (data.buttonNum == 2) {
		popupViewHeight += viewContentPadding + 30;
		elementList = elementList.concat([{
				tag: 'rect', //绘制底边按钮
				rectStyles: {
					radius: "3px",
					borderColor: "#f1f1f1",
					borderWidth: "1px",
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px"
				}
			},
			{
				tag: 'rect', //绘制底边按钮
				rectStyles: {
					radius: "3px",
					color: $mainColor
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) / 2 + viewContentPadding * 2) + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px"
				}
			},
			{
				tag: 'font',
				id: 'cancelText',
				text: "取消下载",
				textStyles: {
					size: '14px',
					color: "#666",
					lineSpacing: "0%",
					whiteSpace: "normal"
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			},
			{
				tag: 'font',
				id: 'confirmText',
				text: "后台下载",
				textStyles: {
					size: '14px',
					color: "#FFF",
					lineSpacing: "0%",
					whiteSpace: "normal"
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: ((viewContentWidth - viewContentPadding) / 2 + viewContentPadding * 2) + "px",
					width: (viewContentWidth - viewContentPadding) / 2 + "px",
					height: "30px",
				}
			}
		]);
	}
	if (data.buttonNum == 1) {
		popupViewHeight += viewContentPadding + 40;
		elementList = elementList.concat([{
				tag: 'rect', //绘制底边按钮
				rectStyles: {
					radius: "6px",
					color: $mainColor
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: viewContentWidth + "px",
					height: "40px"
				}
			},
			{
				tag: 'font',
				id: 'confirmText',
				text: "关闭",
				textStyles: {
					size: '14px',
					color: "#FFF",
					lineSpacing: "0%",
				},
				position: {
					bottom: viewContentPadding + 'px',
					left: viewContentPadding + "px",
					width: viewContentWidth + "px",
					height: "40px"
				}
			}
		]);
	}
	return {
		popupViewHeight: popupViewHeight,
		popupViewWidth: popupViewWidth,
		screenHeight: screenHeight,
		viewContentWidth: viewContentWidth,
		viewContentPadding: viewContentPadding,
		elementList: elementList
	};
}
// 文件下载的弹窗
function downloadPopup(data) {
	// 弹窗遮罩层
	let maskLayer = new plus.nativeObj.View("maskLayer", { //先创建遮罩层
		top: '0px',
		left: '0px',
		height: '100%',
		width: '100%',
		backgroundColor: 'rgba(0,0,0,0.5)'
	});
	let popupViewData = downloadPopupDrawing(data);
	// 弹窗内容
	let popupView = new plus.nativeObj.View("popupView", { //创建底部图标菜单
		tag: "rect",
		top: (popupViewData.screenHeight - popupViewData.popupViewHeight) / 2 + "px",
		left: '15%',
		height: popupViewData.popupViewHeight + "px",
		width: "70%",
	});
	let progressValue = 0;
	let progressTip = 0;
	let contentText = 0;
	let buttonNum = 2;
	if (data.buttonNum >= 0) {
		buttonNum = data.buttonNum;
	}
	popupView.draw(popupViewData.elementList);
	let callbackData = {
		change: function(res) {
			let progressElement = [];
			if (res.progressValue) {
				progressValue = res.progressValue;
				// 绘制进度条
				progressElement.push({
					tag: 'rect', //绘制进度条背景
					id: 'progressValueBg',
					rectStyles: {
						radius: "4px",
						color: $mainColor
					},
					position: {
						top: popupViewData.viewContentPadding * 4 + 60 + 'px',
						left: popupViewData.viewContentPadding + "px",
						width: popupViewData.viewContentWidth * (res.progressValue / 100) + "px",
						height: "8px"
					}
				});
			}
			if (res.progressTip) {
				progressTip = res.progressTip;
				progressElement.push({
					tag: 'font',
					id: 'progressValue',
					text: res.progressTip,
					textStyles: {
						size: '14px',
						color: $mainColor,
						whiteSpace: "normal"
					},
					position: {
						top: popupViewData.viewContentPadding * 4 + 20 + 'px',
						height: "30px"
					}
				});
			}
			if (res.contentText) {
				contentText = res.contentText;
				progressElement.push({
					tag: 'font',
					id: 'content',
					text: res.contentText,
					textStyles: {
						size: '16px',
						color: "#333",
						whiteSpace: "normal"
					},
					position: {
						top: popupViewData.viewContentPadding * 2 + 30 + 'px',
						height: "30px",
					}
				});
			}
			if (res.buttonNum >= 0 && buttonNum != res.buttonNum) {
				buttonNum = res.buttonNum;
				popupView.reset();
				popupViewData = downloadPopupDrawing(Object.assign({
					progressValue: progressValue,
					progressTip: progressTip,
					contentText: contentText,
				}, res));
				let newElement = [];
				popupViewData.elementList.map((item, index) => {
					let have = false;
					progressElement.forEach((childItem, childIndex) => {
						if (item.id == childItem.id) {
							have = true;
						}
					});
					if (!have) {
						newElement.push(item);
					}
				});
				progressElement = newElement.concat(progressElement);
				popupView.setStyle({
					tag: "rect",
					top: (popupViewData.screenHeight - popupViewData.popupViewHeight) / 2 + "px",
					left: '15%',
					height: popupViewData.popupViewHeight + "px",
					width: "70%",
				});
				popupView.draw(progressElement);
			} else {
				popupView.draw(progressElement);
			}
		},
		cancel: function() {
			maskLayer.hide();
			popupView.hide();
		}
	}
	popupView.addEventListener("click", function(e) {
		console.log("===点击了=====")
		let maxTop = popupViewData.popupViewHeight - popupViewData.viewContentPadding;
		let maxLeft = popupViewData.popupViewWidth - popupViewData.viewContentPadding;
		if (e.clientY > maxTop - 40 && e.clientY < maxTop) {
			if (buttonNum == 1) {
				// 单按钮
				if (e.clientX > popupViewData.viewContentPadding && e.clientX < maxLeft) {
					maskLayer.hide();
					popupView.hide();
					callbackData.reboot();
				}
			} else if (buttonNum == 2) {
				// 双按钮
				let buttonWidth = (popupViewData.viewContentWidth - popupViewData.viewContentPadding) / 2;
				if (e.clientX > popupViewData.viewContentPadding && e.clientX < maxLeft - buttonWidth -
					popupViewData.viewContentPadding) {
					maskLayer.hide();
					popupView.hide();
					callbackData.cancelDownload();
				} else if (e.clientX > maxLeft - buttonWidth && e.clientX < maxLeft) {
					maskLayer.hide();
					popupView.hide();
				}
			}
		}

	});
	// 显示弹窗
	maskLayer.show();
	popupView.show();
	// 点击遮罩层
	if (buttonNum == 1) {
		maskLayer.addEventListener("click", function() { //处理遮罩层点击
			maskLayer.hide();
			popupView.hide();
		});
	}
	// 改变进度条
	return callbackData;
}


const marketPlay = {
	"huawei": "com.huawei.appmarket",
	"xiaomi": "com.xiaomi.market",
	"vivo": "com.bbk.appstore",
	"samsung": "com.sec.android.app.samsungapps",
	"meizu": "com.meizu.mstore",
	"oppo": "com.oppo.market",
	"qq": "com.tencent.android.qqdownloader",
	"baidu": "com.baidu.appsearch",
	"qihu": "com.qihoo.appstore"
}

function goToMarket(res) {
	// 如果 res.versionCode!=res[brand] 说明应用市场的版本目前不是最新版本，需去移动互联更新最新版本
	var Uri = plus.android.importClass("android.net.Uri");
	var Intent = plus.android.importClass('android.content.Intent');
	var main = plus.android.runtimeMainActivity();
	var uri = Uri.parse("market://details?id=com.squirrel.meitao");
	var intent = new Intent(Intent.ACTION_VIEW, uri);
	//应用商店的版本号大于等于QQ移动互联直接去应用市场，否则就去QQ移动互联
	console.log("=====res[brand]=====",res[brand]);
	if (brand && res[brand]  &&res[brand] >= res.qq) {
		// 选择进入商店  
		intent.setPackage(marketPlay[brand]);
		intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK;
		// 没有该商店应用  
		if (intent.resolveActivity(main.getPackageManager()) !== null) {
			main.startActivity(intent);
		} else {
			// 跳转浏览器  
			//去应用宝的移动互联
			intent.setPackage(marketPlay.qq);
			intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK;
			if (intent.resolveActivity(main.getPackageManager()) !== null) {
				main.startActivity(intent);
			} else {
				// 跳转浏览器  
				//去应用宝的移动互联
				plus.runtime.openURL("https://a.app.qq.com/o/simple.jsp?pkgname=com.squirrel.meitao");
			};
		}
	} else {
		intent.setPackage(marketPlay.qq);
		intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK;
		if (intent.resolveActivity(main.getPackageManager()) !== null) {
			main.startActivity(intent);
		} else {
			// 跳转浏览器  
			//去应用宝的移动互联
			plus.runtime.openURL("https://a.app.qq.com/o/simple.jsp?pkgname=com.squirrel.meitao");
		}
	}
}

export default function APPUpdate(isPrompt) {
	getCurrentNo(versionInfo => {
		componentConfig.getServerNo(versionInfo, isPrompt, res => {
			console.log("getCurrentNo===", versionInfo);
			console.log("getServerNo===", res)
			const loadedFile = uni.getStorageSync("lastDownloadFile");
			// 强制或者静默
			if (res.updateType == "silent") {
				if (/\.wgt$/i.test(res.downloadUrl)) {
					if (loadedFile && loadedFile.versionCode == res.versionCode) {
						installClient(function() {
							console.error("==installClient错误1===")
							getDownload(res);
						});
					} else {
						getDownload(res);
					}
				} else if (/\.html$/i.test(res.downloadUrl)) {
					plus.runtime.openURL(res.downloadUrl);
				} else {
					if (platform == "android") {
						if (loadedFile.versionCode == res.versionCode) {
							installClient(function() {
								console.error("==installClient错误2====")
								getDownload(res);
							});
						} else {
							getDownload(res);
						}
					} else {
						plus.runtime.openURL(res.downloadUrl);
					}
				}
			} else if (res.updateType == "solicit" || res.updateType == "forcibly") {
				if (loadedFile && loadedFile.versionCode == res.versionCode) {
					installClient(function() {
						console.error("==installClient错误3====")
						getDownload(res);
					});
					return
				}
				//弹窗确认更新
				updatePopup(res, function() {
					if (/\.wgt$/i.test(res.downloadUrl)) {
						getDownload(res);
					} else if (/\.html$/i.test(res.downloadUrl)) {
						plus.runtime.openURL(res.downloadUrl);
					} else {
						if (platform == "android") {
							getDownload(res);
						} else {
							plus.runtime.openURL(res.downloadUrl);
						}
					}
				}, function() {
					goToMarket(res)
				});
			}
		});
	});
}
// #endif