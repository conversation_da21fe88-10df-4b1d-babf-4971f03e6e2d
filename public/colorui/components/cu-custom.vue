<template>
	<view>
		<view class="cu-custom " :style="[{ height: CustomBar + 'px', backgroundColor: bgColor, display: 'block' }]">
			<view class="cu-bar fixed" :style="style" :class="[bgImage != '' ? 'none-bg text-white bg-img' : '', bgColor]">
				<view v-if="simple" class="action" @tap="BackPage" style="padding-right: 20rpx">
					<text>
						<text
							class="cuIcon-back font-xxxmd"
							:style="{
								color: iconColor ? dealSameColor(iconColor) : dealSameColor(customFiled.topTabbarIconColor)
							}"
						></text>
					</text>
				</view>
				<view class="action font-xxxmd" v-else-if="CanBack && isBack">
					<view
						:style="{
							color: iconColor ? dealSameColor(iconColor) : dealSameColor(customFiled.topTabbarIconColor),
							fontWeight: '500',
							justifyContent: 'space-between',
							width: leftMenuWidth + 'px',
							height: leftMenuHeight + 'px',
							borderRadius: '60rpx',
							display: 'flex',
							alignItems: 'center',
							background: customFiled.topTabbarIconBackgroundColor,
							border: `solid ${boderColor ? dealSameColor(boderColor) : dealSameColor(customFiled.topTabbarBorderColor)} 2rpx`,
						}"
					>
						<text @tap="BackPage" style="display: flex; align-items: center; justify-content: center; flex: 1">
							<text class="cuIcon-back"></text>
						</text>
						<text
						    class="height-60"
							:style="{
								transform: 'scale(0.45)',
								width: '1px',
								backgroundColor: dealSameColor(lineColor || customFiled.topTabbarSplitLineColor)
							}"
						></text>

						<text @tap="GoHome" style="display: flex; align-items: center; justify-content: center; flex: 1"><text class="cuIcon-home"></text></text>
					</view>
				</view>
				<view class="action" @tap="GoHome" v-else-if="!CanBack && isBack">
					<text
						class="text-xdf"
						:style="[
							{
								color: iconColor ? dealSameColor(iconColor) : dealSameColor(customFiled.topTabbarIconColor),
								width: leftMenuWidth / 2 + 'px',
								height: leftMenuHeight + 'px',
								display: 'flex',
								justifyContent: 'center',
								alignItems: 'center',
								background: dealSameColor(customFiled.topTabbarIconBackgroundColor),
								border: `solid ${boderColor ? dealSameColor(boderColor) : dealSameColor(customFiled.topTabbarBorderColor)} 2rpx`,
								borderRadius: '50rpx'
							}
						]"
					>
						<text class="cuIcon-home"></text>
					</text>
				</view>

				<!--#ifdef MP-->
				<view
					class="march-content"
					v-if="!hideMarchContent"
					:style="{
						height: `${menuHeight}px`,
						lineHeight: `${CustomBar}px`,
						display: 'flex',
						alignItems: 'center',
						minWidth:
							CanBack && isBack
								? `${windowWidth - menuWidth - leftMenuWidth - 17}px`
								: !CanBack && isBack
								? `${windowWidth - menuWidth - leftMenuWidth / 2 - 17}px`
								: `${windowWidth - menuWidth - 10}px`,
						width:
							CanBack && isBack
								? `${windowWidth - menuWidth - leftMenuWidth - 17}px`
								: !CanBack && isBack
								? `${windowWidth - menuWidth - leftMenuWidth / 2 - 17}px`
								: `${windowWidth - menuWidth - 10}px`,
						maxWidth:
							CanBack && isBack
								? `${windowWidth - menuWidth - leftMenuWidth - 17}px`
								: !CanBack && isBack
								? `${windowWidth - menuWidth - leftMenuWidth / 2 - 17}px`
								: `${windowWidth - menuWidth - 10}px`
					}"
				>
					<slot name="marchContent"></slot>
				</view>
				<!--#endif-->

				<!--#ifdef APP-->
				<view
					class="march-content flex align-center"
					v-if="!hideMarchContent"
					:style="{
						width: '100vw',
						height: `${menuHeight}px`
					}"
				>
					<slot name="marchContent"></slot>
				</view>
				<!--#endif-->

				<!--#ifdef H5-->
				<view
					class="march-content"
					v-if="!hideMarchContent"
					:style="{
						width: '100vw',
						height: `${menuHeight}px`
					}"
				>
					<slot name="marchContent"></slot>
				</view>
				<!--#endif-->

				<view class="content" :style="[{ top: StatusBar + 'px' }]" v-else>
					<slot name="content"></slot>
				</view>
				<slot name="right"></slot>
			</view>
		</view>
	</view>
</template>

<script>
import Vue from 'vue';
import { mapState } from 'vuex';
import { EventBus } from '@/utils/eventBus.js';
const app = getApp();
import util from '@/utils/util';
export default {
	data() {
		return {
			CanBack: true,
			theme: getApp() && getApp().globalData && getApp().globalData.theme ? getApp().globalData.theme : {}, //全局颜色变量
			customFiled: {},
			showOpenScreen: getApp() && getApp().globalData && getApp().globalData.showOpenScreen ? getApp().globalData.showOpenScreen : false
		};
	},
	name: 'cu-custom',
	computed: {
		...mapState(['windowWidth', 'pixelRatio', 'CustomBar', 'StatusBar', 'menuHeight', 'menuWidth', 'leftMenuWidth', 'leftMenuHeight']),
		style() {
			var bgImage = this.bgImage;
			var textColor = this.textColor;
			var bgColor = this.bgColor;
			var style = `background-size: 750rpx ${this.CustomBar}px;background-repeat: noRepeat;height:${this.CustomBar}px;padding-top:${this.windowWidth>310?this.StatusBar: (this.StatusBar+4) }px;color: ${textColor};background-color: ${bgColor};justify-content: flex-start;`;
			if (this.bgImage) {
				style = `${style}background-image:url(${bgImage});`;
			}
			return style;
		},

		dealSameColor() {
			return function (color) {
				let tempBgColor = this.dealColor(this.bgColor);
				let tempColor = this.dealColor(color);
				return tempBgColor == tempColor ? '#333333' : color;
			};
		}
	},
	props: {
		pageTo: {
			type: String,
			default: ''
		},
		textColor: {
			type: [String, null],
			default: '#ffffff'
		},
		menuColor: {
			type: [String, null],
			default: '#000000'
		},
		bgColor: {
			type: [String, null],
			default: ''
		},
		isBack: {
			type: [Boolean, String],
			default: false
		},
		bgImage: {
			type: [String, null],
			default: ''
		},
		hideMarchContent: {
			type: Boolean,
			default: false
		},
		//边框色值
		 boderColor: {
			type: [String, null],
			default: ''
		},
		//图标背景颜色
		iconBgColor: {
			type: [String, null],
			default: ''
		},
		//图标颜色
		iconColor: {
			type: [String, null],
			default: ''
		},
		//胶囊中间分割线
		lineColor: {
			type: String,
			default: ''
		},
		//简洁版本，只有返回按钮
		simple: {
			type: Boolean,
			default: false
		},
		leftTitleProps: {
			type: [Object, null],
			default: function () {
				return {
					showLeftTitle: false,
					leftTitleColor: '#000',
					leftTitleSize: '12',
					leftTitle: '松鼠美淘',
					leftTitleWidth: 30
				};
			}
		}
	},

	// 组件所在页面的生命周期函数
	mounted: function () {
		// #ifdef H5
		this.theme = getApp().globalData.theme;
		this.showOpenScreen = getApp().globalData.showOpenScreen;
		// #endif
		let customFiled = this.theme.customFiled;
		if (customFiled) this.customFiled = JSON.parse(customFiled);
		let pages = getCurrentPages();
		if (pages.length <= 1 || pages[pages.length - 1].route == 'pages/login/index') {
			//判断是否能返回
			this.CanBack = false;
		} else {
			this.CanBack = true;
		}
	},
	methods: {
		dealColor(color) {
			if (!color) return '';
			let tempBgColor = color;
			tempBgColor = tempBgColor.toLowerCase();
			if (tempBgColor && tempBgColor.length == 4) {
				tempBgColor += tempBgColor[1] + tempBgColor[2] + tempBgColor[3];
			}
			return tempBgColor;
		},

		BackPage() {
			/* Tab页中的路径*/
			let pages = getCurrentPages();
			console.log('pages', pages[0].route);
			if (pages[0].route == 'pages/home/<USER>') {
				EventBus.$emit('showcompons');
			}
			let homePages = pages[0].route;
			let leng = getCurrentPages().length;
			console.log('页面栈长度', leng);
			// const tabBarUrl = ['pages/home/<USER>', 'pages/second-tab/index', 'pages/third-tab/index', 'pages/shopping-cart/index', 'pages/tab-personal/index']
			let curRoute = pages[pages.length - 1].route; //获取当前页面的路由
			console.log('curRoute===', curRoute);
			if (curRoute == 'pages/login/index') {
				uni.reLaunch({
					url: '/pages/home/<USER>'
				});
				return;
			}
			let toPage = curRoute;
			// console.log("当前页面路由地址=2=>", toPage);
			let prevpage = pages[pages.length - 2]; //上一个页面对象
			// console.log("上一个页面的路由=2=>", prevpage.route) //上一个页面路由地址
			// const index = pages.findIndex(item => item.route === pathname)
			// console.log("index", index);
			// let pageTo = uni.getStorageSync('pageTo');
			// console.log("pageTo===>", pageTo, this.isPagesTo(prevpage.route, tabBarUrl));
			// console.log("=======", tabBarUrl.indexOf(prevpage.route) != -1);
			// console.log('this.pageTo', this.pageTo);
			if (pages && pages.length > 1) {
				// if (this.pageTo == "pages/base/search/index") {
				//   console.log('prevpage.route', prevpage.route);
				//   uni.switchTab({
				//     url: '/' + `${homePages}`
				//   });
				// } else
				if (toPage == 'pages/wallet/wallet-pages/index') {
					uni.switchTab({
						url: '/pages/tab-personal/index'
					});
				} else {
					uni.navigateBack({
						delta: 1
					});
				}
			} else {
				uni.switchTab({
					url: '/pages/home/<USER>'
				});
			}
		},

		GoHome() {
			uni.switchTab({
				url: '/pages/home/<USER>'
			});
		}
	}
};
</script>
