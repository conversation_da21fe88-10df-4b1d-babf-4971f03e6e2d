var sa={is_first_launch:!1,launched:!1,_queue:[],mpshow_time:null,sa_referrer:'\u76f4\u63a5\u6253\u5f00',query_share_depth:0,share_distinct_id:'',share_method:'',current_scene:'',inited:!1,para:{server_url:'',send_timeout:1e3,show_log:!1,allow_amend_share_path:!0,max_string_length:5e3,datasend_timeout:3e3,source_channel:[],batch_send:{send_timeout:6e3,max_length:6},preset_properties:{}},platform:'',lib:{version:'0.14.9',name:'MiniGame',method:'code'},properties:{$lib:'MiniGame',$lib_version:'0.14.9'},currentProps:{}};const _toString=Object.prototype.toString,_hasOwnProperty=Object.prototype.hasOwnProperty,indexOf=Array.prototype.indexOf,slice=Array.prototype.slice,_isArray=Array.prototype.isArray,forEach=Array.prototype.forEach,bind=Function.prototype.bind;function isUndefined(e){return void 0===e}function isString(e){return'[object String]'==_toString.call(e)}function isDate(e){return'[object Date]'==_toString.call(e)}function isBoolean(e){return'[object Boolean]'==_toString.call(e)}function isNumber(e){return'[object Number]'==_toString.call(e)&&/[\d\\.]+/.test(String(e))}function isJSONString(e){try{JSON.parse(e)}catch(e){return!1}return!0}function isObject(e){return null!=e&&'[object Object]'===_toString.call(e)}function isPlainObject(e){return'[object Object]'===_toString.call(e)}function isArray(e){return _isArray||'[object Array]'===_toString.call(e)}function isFuction(e){try{return/^\s*\bfunction\b/.test(e)}catch(e){return!1}}function isArguments(e){return!(!e||!_hasOwnProperty.call(e,'callee'))}function toString(e){return null==e?'':isArray(e)||isPlainObject(e)&&e.toString===_toString?JSON.stringify(e,null,2):String(e)}function each(e,t,s){if(null==e)return!1;if(forEach&&e.forEach===forEach)e.forEach(t,s);else if(e.length===+e.length){for(var n=0,r=e.length;n<r;n++)if(n in e&&t.call(s,e[n],n,e)==={})return!1}else for(var a in e)if(_hasOwnProperty.call(e,a)&&t.call(s,e[a],a,e)==={})return!1}function toArray(e,t){if(!e)return[];var s=[];return e.toArray&&(s=e.toArray()),isArray(e)&&(s=slice.call(e)),isArguments(e)&&(s=slice.call(e)),s=values(e),t&&isNumber(t)&&(s=s.slice(t)),s}function values(e){var t=[];return null==e?t:(each(e,function(e){t[t.length]=e}),t)}function include(e,t){var s=!1;return null==e?s:indexOf&&e.indexOf===indexOf?-1!=e.indexOf(t):(each(e,function(e){if(s||(s=e===t))return{}}),s)}function unique(e){for(var t,s=[],n={},r=0;r<e.length;r++)n[t=e[r]]||(n[t]=!0,s.push(t));return s}function formatDate(e){function t(e){return e<10?'0'+e:e}return e.getFullYear()+'-'+t(e.getMonth()+1)+'-'+t(e.getDate())+' '+t(e.getHours())+':'+t(e.getMinutes())+':'+t(e.getSeconds())+'.'+t(e.getMilliseconds())}function searchObjDate(e){(isObject(e)||isArray(e))&&each(e,function(t,s){isObject(t)||isArray(t)?searchObjDate(e[s]):isDate(t)&&(e[s]=formatDate(t))})}function trim(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,'')}function isFunction(e){if(!e)return!1;var t=Object.prototype.toString.call(e);return'[object Function]'==t||'[object AsyncFunction]'==t||'[object GeneratorFunction]'==t}function extend(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];if('[object Object]'===Object.prototype.toString.call(s))for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&void 0!==s[n]&&(e[n]=s[n])}return e}function extend2Lev(e){return each(slice.call(arguments,1),function(t){for(var s in t)void 0!==t[s]&&null!==t[s]&&(isObject(t[s])&&isObject(e[s])?extend(e[s],t[s]):e[s]=t[s])}),e}function isEmptyObject(e){if(isObject(e)){for(var t in e)if(_hasOwnProperty.call(e,t))return!1;return!0}return!1}function deepCopy(e){var t={};return function e(t,s){for(var n in s){var r=s[n];isArray(r)?(t[n]=[],e(t[n],r)):isObject(r)?(t[n]={},e(t[n],r)):t[n]=r}}(t,e),t}function formatString(e){return e.length>sa.para.max_string_length?(sa.log('\u5b57\u7b26\u4e32\u957f\u5ea6\u8d85\u8fc7\u9650\u5236\uff0c\u5df2\u7ecf\u505a\u622a\u53d6--'+e),e.slice(0,sa.para.max_string_length)):e}function searchObjString(e){isObject(e)&&each(e,function(t,s){isObject(t)?searchObjString(e[s]):isString(t)&&(e[s]=formatString(t))})}function encodeDates(e){return each(e,function(t,s){isDate(t)?e[s]=formatDate(t):isObject(t)&&(e[s]=encodeDates(t))}),e}function utf8Encode(e){var t,s,n,r,a='';for(t=s=0,n=(e=(e+'').replace(/\r\n/g,'\n').replace(/\r/g,'\n')).length,r=0;r<n;r++){var i=e.charCodeAt(r),o=null;i<128?s++:o=i>127&&i<2048?String.fromCharCode(i>>6|192,63&i|128):String.fromCharCode(i>>12|224,i>>6&63|128,63&i|128),null!==o&&(s>t&&(a+=e.substring(t,s)),a+=o,t=s=r+1)}return s>t&&(a+=e.substring(t,e.length)),a}function base64Encode(e){var t,s,n,r,a,i='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',o=0,c=0,u='',l=[];if(!e)return e;e=utf8Encode(e);do{t=(a=e.charCodeAt(o++)<<16|e.charCodeAt(o++)<<8|e.charCodeAt(o++))>>18&63,s=a>>12&63,n=a>>6&63,r=63&a,l[c++]=i.charAt(t)+i.charAt(s)+i.charAt(n)+i.charAt(r)}while(o<e.length);switch(u=l.join(''),e.length%3){case 1:u=u.slice(0,-2)+'==';break;case 2:u=u.slice(0,-1)+'='}return u}function _decodeURIComponent(e){var t='';try{t=decodeURIComponent(e)}catch(s){t=e}return t}var SOURCE_CHANNEL_STANDARD='utm_source utm_medium utm_campaign utm_content utm_term',LATEST_SOURCE_CHANNEL=['$latest_utm_source','$latest_utm_medium','$latest_utm_campaign','$latest_utm_content','$latest_utm_term','$latest_sa_utm'],LATEST_SHARE_INFO=['$latest_share_distinct_id','$latest_share_url_path','$latest_share_depth','$latest_share_method'],IDENTITY_KEY={EMAIL:'$identity_email',MOBILE:'$identity_mobile',LOGIN:'$identity_login_id'},IDENTITIES={},RESERVE_CHANNEL=' utm_source utm_medium utm_campaign utm_content utm_term sa_utm ',meta={lib_version:'',launched:!1,lib_name:'',query_share_depth:0,page_show_time:Date.now(),mp_show_time:null,promise_list:[],current_scene:'',is_first_launch:!1,_queue:[],inited:!1,hasExeInit:!1,scene_prefix:'',share_distinct_id:'',sa_referrer:'\u76f4\u63a5\u6253\u5f00',source_channel_standard:SOURCE_CHANNEL_STANDARD,latest_source_channel:LATEST_SOURCE_CHANNEL,latest_share_info:LATEST_SHARE_INFO};function getAppId(){var e=sa.system_api.getAppInfoSync();return e&&e.appId?e.appId:''}function getObjFromQuery(e){var t=e.split('?'),s=[],n={};return t&&t[1]?(each(t[1].split('&'),function(e){(s=e.split('='))[0]&&s[1]&&(n[s[0]]=s[1])}),n):{}}function getMixedQuery(e){var t=detectOptionQuery(e),s=t.scene,n=t.q,r=t.query;for(var a in r)r[a]=_decodeURIComponent(r[a]);return s&&extend(r,getObjFromQuery(s=-1!==(s=_decodeURIComponent(s)).indexOf('?')?'?'+s.replace(/\?/g,''):'?'+s)),n&&extend(r,getObjFromQuery(_decodeURIComponent(n))),r}function detectOptionQuery(e){if(!e||!isObject(e.query))return{};var t,s,n,r,a={};return a.query=extend({},e.query),isString(a.query.scene)&&(t=a.query,s=['utm_source','utm_content','utm_medium','utm_campaign','utm_term','sa_utm'].concat(sa.para.source_channel),n=new RegExp('('+s.join('|')+')%3D','i'),1===(r=Object.keys(t)).length&&'scene'===r[0]&&n.test(t.scene))&&(a.scene=a.query.scene,delete a.query.scene),e.query.q&&e.query.scancode_time&&'101'===String(e.scene).slice(0,3)&&(a.q=String(a.query.q),delete a.query.q,delete a.query.scancode_time),a}function setUtm(e,t){var s={},n=getMixedQuery(e),r=getCustomUtmFromQuery(n,'$','_','$'),a=getCustomUtmFromQuery(n,'$latest_','_latest_','$latest_');return s.pre1=r,s.pre2=a,extend(t,r),s}function setLatestChannel(e){isEmptyObject(e)||(function(e,t){var s=!1;for(var n in t)e[t[n]]&&(s=!0);return s}(e,LATEST_SOURCE_CHANNEL)&&sa.clearAppRegister(LATEST_SOURCE_CHANNEL),sa.registerApp(e))}function getCustomUtmFromQuery(e,t,s,n){if(!isObject(e))return{};var r={};if(e.sa_utm)for(var a in e)'sa_utm'!==a?include(sa.para.source_channel,a)&&(r[s+a]=e[a]):r[n+a]=e[a];else for(var i in e)-1===(' '+SOURCE_CHANNEL_STANDARD+' ').indexOf(' '+i+' ')?include(sa.para.source_channel,i)&&(r[s+i]=e[i]):r[t+i]=e[i];return r}function existLatestUtm(){var e=!1;return each(LATEST_SOURCE_CHANNEL,function(t){sa.properties[t]&&(e=!0)}),e}function setQuery(e,t){if(e&&isObject(e)&&!isEmptyObject(e)){var s=[];return each(e,function(e,n){'q'===n&&isString(e)&&0===e.indexOf('http')||(!0===t?s.push(n+'='+e):!1===t?s.push(n+'='+encodeURIComponent(e)):s.push(n+'='+_decodeURIComponent(e)))}),s.join('&')}return''}function getCurrentPage(){var e={};try{var t=isFunction(sa.system_api.getCurrentPages)?sa.system_api.getCurrentPages():getCurrentPages();e=t[t.length-1]}catch(e){sa.log('getCurrentPage:'+e)}return e}function getCurrentPath(){var e='\u672a\u53d6\u5230';try{var t=getCurrentPage();e=t?t.route:e}catch(e){sa.log('getCurrentPath:'+e)}return e}function isPresetIdKeys(e,t){var s=['$identity_anonymous_id'];for(var n of(isArray(t)&&(s=s.concat(t)),s))if(n===e)return!0;return!1}var isSafeInteger=Number.isSafeInteger||function(e){return isInteger(e)&&Math.abs(e)<=Math.pow(2,53)-1},isInteger=Number.isInteger||function(e){return'number'==typeof e&&isFinite(e)&&Math.floor(e)===e},check={checkKeyword:function(e){return/^((?!^distinct_id$|^original_id$|^device_id$|^time$|^properties$|^id$|^first_id$|^second_id$|^users$|^events$|^event$|^user_id$|^date$|^datetime$|^user_group|^user_tag)[a-zA-Z_$][a-zA-Z\d_$]{0,99})$/i.test(e)},checkIdLength:function(e){return!(String(e).length>255)||(sa.log('id \u957f\u5ea6\u8d85\u8fc7 255 \u4e2a\u5b57\u7b26\uff01'),!1)}};function getOpenidNameByAppid(e){if(''==e||!isString(e))return sa.log('error: \u53c2\u6570\u5fc5\u987b\u662f\u6709\u6548\u503c'),!1;var t=getAppId(),s="$identity_"+e+"_openid";return t&&(s="$identity_"+e+'_'+t+"_openid"),s}function validId(e){return!isString(e)&&!isNumber(e)||''===e?(sa.log('\u8f93\u5165 ID \u7c7b\u578b\u9519\u8bef'),!1):isNumber(e)&&(e=String(e),!/^\d+$/.test(e))?(sa.log('\u8f93\u5165 ID \u7c7b\u578b\u9519\u8bef'),!1):!!check.checkIdLength(e)&&e}function isNewLoginId(e,t){return e!==sa.store._state.history_login_id.name||sa.store._state.history_login_id.value!==t}function isSameAndAnonymousID(e){var t=sa.store.getFirstId(),s=sa.store.getDistinctId();return t?e===t:e===s}function setUpperCase(e){return isString(e)?e.toLocaleUpperCase():e}function getIsFirstDay(){return!!('object'==typeof sa.store._state&&isNumber(sa.store._state.first_visit_day_time)&&sa.store._state.first_visit_day_time>(new Date).getTime())}function getPresetProperties(){if(sa.properties&&sa.properties.$lib){var e={};each(sa.properties,function(t,s){0===s.indexOf('$')&&(e[s]=t)});var t={$url_path:getCurrentPath(),$is_first_day:getIsFirstDay(),$is_first_time:meta.is_first_launch},s=extend(e,t,sa.currentProps,sa.properties,sa.store.getProps());return delete s.$lib,s}return{}}function joinUrl(e,t){return!!e&&('\u672a\u53d6\u5230'===e?'\u672a\u53d6\u5230':t?e+'?'+t:e)}function getPath(e){return e=isString(e)?e.replace(/^\//,''):'\u53d6\u503c\u5f02\u5e38'}function getAppProps(e){var t={};return e&&e.path&&(t.$url_path=getPath(e.path),t.$url_query=setQuery(e.query),t.$url=joinUrl(t.$url_path,t.$url_query)),t}function getPageProps(){var e=getCurrentPage(),t=getCurrentPath(),s=e.sensors_mp_url_query||'';return{$url_path:t,$url:joinUrl(t,s),$url_query:s}}function rot13defs(e){return rot13obfs(e=String(e),113)}function rot13obfs(e,t){t='number'==typeof t?t:13;for(var s=(e=String(e)).split(''),n=0,r=s.length;n<r;n++){s[n].charCodeAt(0)<126&&(s[n]=String.fromCharCode((s[n].charCodeAt(0)+t)%126))}return s.join('')}var decodeURIComponent$1=_decodeURIComponent;function formatSystem(e){var t=e.toLowerCase();return'ios'===t?'iOS':'android'===t?'Android':e}var getRandomBasic=function(){var e=(new Date).getTime();return function(t){return Math.ceil((e=(9301*e+49297)%233280)/233280*t)}}();function getRandom(){if('function'==typeof Uint32Array){var e='';if('undefined'!=typeof crypto?e=crypto:'undefined'!=typeof msCrypto&&(e=msCrypto),isObject(e)&&e.getRandomValues){var t=new Uint32Array(1);return e.getRandomValues(t)[0]/Math.pow(2,32)}}return getRandomBasic(1e19)/1e19}function getUUID(){return Date.now()+'-'+Math.floor(1e7*getRandom())+'-'+getRandom().toString(16).replace('.','')+'-'+String(31242*getRandom()).replace('.','').slice(0,8)}const _={getUUID:getUUID,formatSystem:formatSystem,indexOf:indexOf,slice:slice,forEach:forEach,bind:bind,_hasOwnProperty:_hasOwnProperty,_toString:_toString,isUndefined:isUndefined,isString:isString,isDate:isDate,isBoolean:isBoolean,isNumber:isNumber,isJSONString:isJSONString,isObject:isObject,isPlainObject:isPlainObject,isArray:isArray,isFuction:isFuction,isArguments:isArguments,toString:toString,unique:unique,include:include,values:values,toArray:toArray,each:each,formatDate:formatDate,searchObjDate:searchObjDate,utf8Encode:utf8Encode,decodeURIComponent:decodeURIComponent$1,encodeDates:encodeDates,base64Encode:base64Encode,trim:trim,isFunction:isFunction,extend:extend,extend2Lev:extend2Lev,isEmptyObject:isEmptyObject,searchObjString:searchObjString,formatString:formatString,setLatestChannel:setLatestChannel,getObjFromQuery:getObjFromQuery,getMixedQuery:getMixedQuery,detectOptionQuery:detectOptionQuery,setUtm:setUtm,getCustomUtmFromQuery:getCustomUtmFromQuery,existLatestUtm:existLatestUtm,setQuery:setQuery,getCurrentPage:getCurrentPage,getCurrentPath:getCurrentPath,rot13defs:rot13defs,rot13obfs:rot13obfs,isSafeInteger:isSafeInteger,isInteger:isInteger,isPresetIdKeys:isPresetIdKeys,deepCopy:deepCopy,check:check,getOpenidNameByAppid:getOpenidNameByAppid,validId:validId,isNewLoginId:isNewLoginId,isSameAndAnonymousID:isSameAndAnonymousID,setUpperCase:setUpperCase,getIsFirstDay:getIsFirstDay,getPageProps:getPageProps,getAppProps:getAppProps,getPath:getPath,joinUrl:joinUrl,getPresetProperties:getPresetProperties};function request(e){var t;e.timeout&&(t=e.timeout,delete e.timeout);var s=sa.platform_obj.request(e);setTimeout(function(){try{isObject(s)&&isFunction(s.abort)&&s.abort()}catch(e){sa.log(e)}},t)}function getStorage(e,t){try{sa.platform_obj.getStorage({key:e,success:s,fail:s})}catch(t){try{sa.platform_obj.getStorage({key:e,success:s,fail:s})}catch(e){sa.log('\u83b7\u53d6 storage \u5931\u8d25\uff01',e)}}function s(e){if(e&&e.data&&isJSONString(e.data))try{var s=JSON.parse(e.data);e.data=s}catch(e){sa.log('parse res.data \u5931\u8d25\uff01',e)}t(e)}}function setStorage(e,t){var s;try{s=JSON.stringify(t)}catch(e){sa.log('\u5e8f\u5217\u5316\u7f13\u5b58\u5bf9\u8c61\u5931\u8d25\uff01',e)}try{sa.platform_obj.setStorage({key:e,data:s})}catch(t){try{sa.platform_obj.setStorage({key:e,data:s})}catch(e){sa.log('\u8bbe\u7f6e storage \u5931\u8d25: ',e)}}}function getStorageSync(e){var t='';try{t=sa.platform_obj.getStorageSync(e)}catch(s){try{t=sa.platform_obj.getStorageSync(e)}catch(e){sa.log('\u83b7\u53d6 storage \u5931\u8d25\uff01')}}return isJSONString(t)&&(t=JSON.parse(t)),t}function setStorageSync(e,t){var s;try{s=JSON.stringify(t)}catch(e){sa.log('\u5e8f\u5217\u5316\u7f13\u5b58\u5bf9\u8c61\u5931\u8d25\uff01',e)}var n=function(){sa.platform_obj.setStorageSync(e,s)};try{n()}catch(e){sa.log('set Storage fail --',e);try{n()}catch(e){sa.log('set Storage fail again --',e)}}}function getNetworkType(){return sa.platform_obj.getNetworkType.apply(null,arguments)}function getSystemInfo(){return sa.platform_obj.getSystemInfo.apply(null,arguments)}function getAppId$1(){var e;if(sa.platform_obj.getAccountInfoSync&&(e=sa.platform_obj.getAccountInfoSync()),isObject(e)&&isObject(e.miniProgram))return e.miniProgram}var compose={request:request,getStorage:getStorage,setStorage:setStorage,getStorageSync:getStorageSync,setStorageSync:setStorageSync,getAppInfoSync:getAppId$1,getNetworkType:getNetworkType,getSystemInfo:getSystemInfo};function setStorageSync$1(e,t){return my.setStorageSync({key:e,data:t})}function getStorageSync$1(e){var t=my.getStorageSync({key:e});return null!==t.data?t.data:''}function getAppInfoSync(){if(my.getAppIdSync){var e=my.getAppIdSync();return{appId:e&&e.appId?e.appId:'',appEnv:'',appVersion:''}}return{}}var compose$1={getStorageSync:getStorageSync$1,setStorageSync:setStorageSync$1,getAppInfoSync:getAppInfoSync,onNetworkStatusChange:my.onNetworkStatusChange};function request$1(e){var t,s;if(!e||isEmptyObject(e))return!1;e.complete=function(){s&&clearTimeout(s)},t=my.canIUse('request')?my.request(e):my.httpRequest(e),s=setTimeout(function(){isObject(t)&&isFunction(t.abort)&&t.abort()},sa.para.datasend_timeout)}var compose$2={request:request$1};function getNetwork(){return new Promise(function(e){sa.system_api.getNetworkType({success(e){sa.properties.$network_type=setUpperCase(e.networkType)},fail(e){sa.log('\u83b7\u53d6\u7f51\u7edc\u72b6\u6001\u4fe1\u606f\u5931\u8d25\uff1a ',e)},complete(){e()}})})}function getSystemInfo$1(){return new Promise(e=>{sa.system_api.getSystemInfo({success(e){var t=sa.properties;if(isObject(e)){t.$manufacturer=e.brand,t.$model=e.model,t.$brand=setUpperCase(e.brand)||'',t.$screen_width=Number(e.screenWidth),t.$screen_height=Number(e.screenHeight),t.$os=formatSystem(e.platform),t.$os_version=e.system.indexOf(' ')>-1?e.system.split(' ')[1]:e.system,t.$mp_client_app_version=e.version||'';var s=e.SDKVersion||'';s&&(t.$mp_client_basic_library_version=s)}},fail(e){sa.log('\u83b7\u53d6\u7cfb\u7edf\u4fe1\u606f\u5931\u8d25: ',e)},complete(){e()}})})}var system={inited:!1,init:function(){var e=(new Date).getTimezoneOffset();isNumber(e)&&(sa.properties.$timezone_offset=e);var t=getAppId()||sa.para.app_id||sa.para.appid;t&&(sa.properties.$app_id=t);var s=getNetwork(),n=getSystemInfo$1();Promise.all([s,n]).then(function(){sa.system.inited=!0,sa.checkInit()})}},identity_id='$identity_ali_mp_id';function stripProperties(e){return isObject(e)?(each(e,function(t,s){if(isArray(t)){var n=[];each(t,function(e){if(isString(e))n.push(e);else if(isUndefined(e))n.push('null');else try{n.push(JSON.stringify(e))}catch(e){sa.log('\u60a8\u7684\u6570\u636e - '+s+':'+t+' - \u7684\u6570\u7ec4\u91cc\u7684\u503c\u6709\u9519\u8bef,\u5df2\u7ecf\u5c06\u5176\u5220\u9664')}}),e[s]=n}if(isObject(t))try{e[s]=JSON.stringify(t)}catch(n){delete e[s],sa.log('\u60a8\u7684\u6570\u636e - '+s+':'+t+' - \u7684\u6570\u636e\u503c\u6709\u9519\u8bef,\u5df2\u7ecf\u5c06\u5176\u5220\u9664')}else isString(t)||isNumber(t)||isDate(t)||isBoolean(t)||isArray(t)||(sa.log('\u60a8\u7684\u6570\u636e - ',t,'-\u683c\u5f0f\u4e0d\u6ee1\u8db3\u8981\u6c42\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664'),delete e[s])}),e):e}function parseSuperProperties(e){isObject(e)&&each(e,function(t,s){if(isFunction(t))try{e[s]=t(),isFunction(e[s])&&(sa.log('\u60a8\u7684\u5c5e\u6027 - '+s+' \u683c\u5f0f\u4e0d\u6ee1\u8db3\u8981\u6c42\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664'),delete e[s])}catch(t){delete e[s],sa.log('\u60a8\u7684\u5c5e\u6027 - '+s+' \u629b\u51fa\u4e86\u5f02\u5e38\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664')}})}function batchRequest(e){if(isArray(e.data)&&e.data.length>0){var t=Date.now(),s=sa.para.datasend_timeout;e.data.forEach(function(e){e._flush_time=t}),e.data=JSON.stringify(e.data);let n={url:sa.para.server_url,method:'POST',dataType:'text',data:'data_list='+encodeURIComponent(base64Encode(e.data)),timeout:s,success:function(){e.success(e.len)},fail:function(){e.fail()}};sa.system_api.request(n)}else e.success(e.len)}function onceSend(e){e._flush_time=Date.now();var t='',s=JSON.stringify(e);t=-1!==sa.para.server_url.indexOf('?')?sa.para.server_url+'&data='+encodeURIComponent(base64Encode(s)):sa.para.server_url+'?data='+encodeURIComponent(base64Encode(s));var n=sa.para.datasend_timeout;sa.system_api.request({url:t,dataType:'text',method:'GET',timeout:n})}IDENTITIES.identity_unionid='',IDENTITIES.identity_id=identity_id,IDENTITIES.openid_name='ali',IDENTITIES.bind_preset_id=[identity_id],IDENTITIES.unbind_without_check=[identity_id],IDENTITIES.login_preset_id=[identity_id];var kit={batchRequest:batchRequest,onceSend:onceSend};function batchSend(){if(sa.batch_state.sended){var e,t,s=sa.batch_state.mem;(t=(e=s.length>=100?s.slice(0,100):s).length)>0&&(sa.batch_state.sended=!1,kit.batchRequest({data:e,len:t,success:batchRemove,fail:sendFail}))}}function sendFail(){sa.batch_state.sended=!0,sa.batch_state.failTime++}function batchRemove(e){sa.batch_state.clear(e),sa.batch_state.sended=!0,sa.batch_state.changed=!0,batchWrite(),sa.batch_state.failTime=0}function batchWrite(){sa.batch_state.changed&&(sa.batch_state.is_first_batch_write&&(sa.batch_state.is_first_batch_write=!1,setTimeout(function(){batchSend()},1e3)),sa.batch_state.syncStorage&&(sa.system_api.setStorageSync('sensors_prepare_data',sa.batch_state.mem),sa.batch_state.changed=!1))}function batchInterval(){!function e(){setTimeout(function(){batchWrite(),e()},1e3)}(),function e(){setTimeout(function(){batchSend(),e()},sa.para.batch_send.send_timeout*Math.pow(2,sa.batch_state.failTime))}()}function reportEvent(e){var t='';e._flush_time=Date.now(),t=e.event?"sensors_"+e.event:"sensors_"+e.type,e.dataSource='sensors',sa.log('report_event, name: ',t,'-- key: ',e),__mp_private_api__.reportEvent(t,e)}sa.batch_state={mem:[],changed:!1,sended:!0,is_first_batch_write:!0,sync_storage:!1,failTime:0,getLength:function(){return this.mem.length},add:function(e){this.mem.push(e)},clear:function(e){this.mem.splice(0,e)}},sa.batchWrite=batchWrite,sa.prepareData=function(e){var t={distinct_id:sa.store.getDistinctId(),lib:{$lib:sa.lib.name,$lib_method:sa.lib.method,$lib_version:String(sa.lib.version)},properties:{}};if(isObject(sa.store._state.identities)&&(t.identities=extend({},sa.store.getIdentities())),'track_id_unbind'===e.type&&'$UnbindID'===e.event&&(t.identities=_.deepCopy(e.unbind_value),delete e.unbind_value),extend(t,sa.store.getUnionId(),e),isObject(e.properties)&&!isEmptyObject(e.properties)&&extend(t.properties,e.properties),'track_id_unbind'===e.type&&'$UnbindID'===e.event&&(t.login_id&&delete t.login_id,t.anonymous_id&&delete t.anonymous_id),e.type&&'profile'===e.type.slice(0,7)||(t._track_id=Number(String(getRandom()).slice(2,5)+String(getRandom()).slice(2,4)+String(Date.now()).slice(-4)),t.properties=extend({},sa.properties,sa.store.getProps(),sa.currentProps,t.properties),'track'===e.type&&(t.properties.$is_first_day=getIsFirstDay())),t.properties.$time&&isDate(t.properties.$time)?(t.time=1*t.properties.$time,delete t.properties.$time):t.time=1*new Date,sa.ee.data.emit('beforeBuildCheck',t),parseSuperProperties(t.properties),searchObjDate(t),stripProperties(t.properties),searchObjString(t),sa.ee.data.emit('finalAdjustData',t),!sa.para.server_url)return!1;sa.log(t),sa.send(t)},sa.send=function(e){if('sensorsdata2015_binance'===sa.storageName&&'native'===sa.para.data_report_type)return reportEvent(e),!1;sa.para.batch_send?(sa.batch_state.getLength()>=500&&(sa.log('\u6570\u636e\u91cf\u5b58\u50a8\u8fc7\u5927\uff0c\u6709\u5f02\u5e38'),sa.batch_state.mem.shift()),sa.batch_state.add(e),sa.batch_state.changed=!0,sa.batch_state.getLength()>=sa.para.batch_send.max_length&&batchSend()):kit.onceSend(e)},sa.log=function(){if(sa.para.show_log&&'object'==typeof console&&console.log)try{var e=Array.prototype.slice.call(arguments);return console.log.apply(console,e)}catch(e){console.log(arguments[0])}},sa.track=function(e,t,s){sa.prepareData({type:'track',event:e,properties:t},s)},sa.setProfile=function(e){sa.prepareData({type:'profile_set',properties:e})},sa.setOnceProfile=function(e,t){sa.prepareData({type:'profile_set_once',properties:e},t)},sa.login=function(e){var t=sa.store.getFirstId(),s=sa.store.getDistinctId();e!==s&&(t?sa.trackSignup(e,'$SignUp'):(sa.store.set('first_id',s),sa.trackSignup(e,'$SignUp')))},sa.logout=function(e){var t=sa.store.getFirstId();t?(sa.store.set('first_id',''),!0===e?sa.store.set('distinct_id',getUUID()):sa.store.set('distinct_id',t)):sa.log('\u6ca1\u6709first_id\uff0clogout\u5931\u8d25')},sa.identify=function(e){(e=_.validId(e))&&(sa.store.getFirstId()?sa.store.set('first_id',e):sa.store.set('distinct_id',e))},sa.trackSignup=function(e,t,s){var n,r,a,i,o;isObject(e)?(n=e.id,r=e.event_name,a=e.id_name):(n=e,r=t),sa.store.set('distinct_id',n),i=a&&a!==IDENTITY_KEY.LOGIN?a+'+'+n:n,o=sa.store.getFirstId()||sa.store.getDistinctId(),sa.prepareData({original_id:o,distinct_id:i,type:'track_signup',event:r,properties:s})},sa.registerApp=function(e){isObject(e)&&!isEmptyObject(e)&&(sa.currentProps=extend(sa.currentProps,e))},sa.clearAppRegister=function(e){isArray(e)&&each(sa.currentProps,function(t,s){include(e,s)&&delete sa.currentProps[s]})},sa.register=function(e){isObject(e)&&!isEmptyObject(e)&&sa.store.setProps(e)},sa.clearAllRegister=function(){sa.store.setProps({},!0)},sa.getPresetProperties=function(){return _.getPresetProperties()},sa.use=function(e){const t=toArray(arguments,1);return t.unshift(this),isObject(e)&&isFunction(e.init)&&e.init.apply(e,t),e},sa.usePlugin=sa.use,sa.getServerUrl=function(){return sa.para.server_url},sa.registerPropertyPlugin=function(e){isFunction(e.properties)?!e.isMatchedWithFilter||isFunction(e.isMatchedWithFilter)?sa.ee.data.on('finalAdjustData',function(t){try{isFunction(e.isMatchedWithFilter)?e.isMatchedWithFilter(t)&&e.properties(t):e.properties(t)}catch(e){sa.log('execute registerPropertyPlugin callback error:'+e)}}):sa.log('registerPropertyPlugin arguments error, isMatchedWithFilter must be function'):sa.log('registerPropertyPlugin arguments error, properties must be function')};var hasOwnProperty$1=Object.prototype.hasOwnProperty,store={inited:!0,storageInfo:null,store_queue:[],getStorage:function(){return this.storageInfo?this.storageInfo:(this.storageInfo=sa.system_api.getStorageSync(sa.storageName)||'',this.storageInfo)},_state:{},getUUID:function(){return getUUID()},toState:function(e){var t=null,s=this;function n(){t.distinct_id?s._state=t:s.set('distinct_id',getUUID())}isJSONString(e)?(t=JSON.parse(e),n()):isObject(e)?(t=e,n()):this.set('distinct_id',getUUID());var r=this._state._first_id||this._state.first_id,a=this._state._distinct_id||this._state.distinct_id,i=(this._state.history_login_id?this._state.history_login_id:{}).name;if(this._state.identities&&isString(this._state.identities)){var o=JSON.parse(rot13defs(this._state.identities));this._state.identities=o}function c(e){for(var t in store._state.identities)hasOwnProperty$1.call(store._state.identities,t)&&t!==IDENTITIES.identity_id&&t!==e&&delete store._state.identities[t]}this._state.identities&&isObject(this._state.identities)&&!isEmptyObject(this._state.identities)||(this._state.identities={},this._state.identities[IDENTITIES.identity_id]=getUUID()),r?i&&hasOwnProperty$1.call(this._state.identities,i)?this._state.identities[i]!==a&&(this._state.identities[i]=a,c(i),this._state.history_login_id.value=a):(this._state.identities[IDENTITY_KEY.LOGIN]=a,c(IDENTITY_KEY.LOGIN),this._state.history_login_id={name:IDENTITY_KEY.LOGIN,value:a}):this._state.history_login_id={name:'',value:''},this.save()},getFirstId:function(){return this._state._first_id||this._state.first_id},getDistinctId:function(){var e=this.getLoginDistinctId();return e||(this._state._distinct_id||this._state.distinct_id)},getUnionId:function(){var e={},t=this._state._first_id||this._state.first_id,s=this.getDistinctId();return t&&s?(e.login_id=s,e.anonymous_id=t):e.anonymous_id=s,e},getIdentities:function(){var e=JSON.parse(JSON.stringify(this._state.identities));return e.$identity_anonymous_id=this.getAnonymousId(),e},getAnonymousId:function(){return this.getUnionId().anonymous_id},getHistoryLoginId:function(){return isObject(this._state.history_login_id)?this._state.history_login_id:null},getLoginDistinctId:function(){var e=this.getHistoryLoginId();return isObject(e)&&e.value?e.name!==IDENTITY_KEY.LOGIN?e.name+'+'+e.value:e.value:null},getProps:function(){return this._state.props||{}},setProps:function(e,t){var s=this._state.props||{};t?this.set('props',e):(extend(s,e),this.set('props',s))},set:function(e,t){var s={};for(var n in'string'==typeof e?s[e]=t:'object'==typeof e&&(s=e),this._state=this._state||{},s)this._state[n]=s[n],'first_id'===n?delete this._state._first_id:'distinct_id'===n&&(delete this._state._distinct_id,sa.events.emit('changeDistinctId'));this.save()},identitiesSet:function(e){var t={};switch(e.type){case'login':t[IDENTITIES.identity_id]=this._state.identities[IDENTITIES.identity_id],t[e.id_name]=e.id;break;case'logout':t[IDENTITIES.identity_id]=this._state.identities[IDENTITIES.identity_id]}this.set('identities',t)},change:function(e,t){this._state['_'+e]=t},encryptStorage:function(){var e=this.getStorage(),t='data:enc;';isObject(e)?e=t+rot13obfs(JSON.stringify(e)):isString(e)&&-1===e.indexOf(t)&&(e=t+rot13obfs(e)),sa.system_api.setStorageSync(sa.storageName,e)},save:function(){var e=deepCopy(this._state),t=rot13obfs(JSON.stringify(e.identities));if(e.identities=t,delete e._first_id,delete e._distinct_id,sa.para.encrypt_storage){e='data:enc;'+rot13obfs(JSON.stringify(e))}sa.system_api.setStorageSync(sa.storageName,e)},init:function(){var e=this.getStorage(),t=getUUID();if(e)isString(e)&&-1!==e.indexOf("data:enc;")&&(e=e.substring("data:enc;".length),e=JSON.parse(rot13defs(e))),this.toState(e);else{meta.is_first_launch=!0;var s=new Date,n=s.getTime();s.setHours(23),s.setMinutes(59),s.setSeconds(60),this.set({distinct_id:t,first_visit_time:n,first_visit_day_time:s.getTime(),identities:{[IDENTITIES.identity_id]:t},history_login_id:{name:'',value:''}}),isObject(sa.para)&&isString(sa.para.identify_id)&&sa.identify(sa.para.identify_id),sa.setOnceProfile({$first_visit_time:s})}this.checkStoreInit&&this.checkStoreInit()}};function trackSignup(e,t,s){var n,r,a,i;_.isObject(e)?(n=e.id,r=e.event_name,a=e.id_name):(n=e,r=t),store.set('distinct_id',n),i=a&&a!==IDENTITY_KEY.LOGIN?a+'+'+n:n;var o=store.getFirstId()||store.getDistinctId();sa.prepareData({original_id:o,distinct_id:i,type:'track_signup',event:r,properties:s})}function bindWithoutCheck(e,t){store._state.identities[e]=t,store.save(),sa.prepareData({type:'track_id_bind',event:'$BindID'})}function bind$1(e,t){var s='';if(_.isNumber(t)){if(_.isInteger(t)&&!1===_.isSafeInteger(t))return sa.log('Value must be String'),!1;t=String(t)}if(!_.isString(e))return sa.log('Key must be String'),!1;var n=store.getHistoryLoginId(),r=n?n.name:'',a=[IDENTITY_KEY.LOGIN,r];return _.isArray(IDENTITIES.bind_preset_id)&&(a=[IDENTITY_KEY.LOGIN,r].concat(IDENTITIES.bind_preset_id)),!_.check.checkKeyword(e)||_.isPresetIdKeys(e,a)?(s='Key ['+e+'] is invalid',sa.log(s),!1):t&&''!==t?_.isString(t)?!!_.check.checkIdLength(t)&&void bindWithoutCheck(e,t):(sa.log('Value must be String'),!1):(sa.log('Value is empty or null'),!1)}function unbindWithoutCheck(e,t){hasOwnProperty.call(store._state.identities,e)&&t===store._state.identities[e]&&(IDENTITIES.unbind_without_check&&IDENTITIES.unbind_without_check.indexOf(e)<0&&delete store._state.identities[e],store.save());var s=store.getDistinctId(),n=store.getFirstId();s===e+'+'+t&&(store.set('first_id',''),store.set('distinct_id',n),store.set('history_login_id',{name:'',value:''}));var r={};r[e]=t,sa.prepareData({type:'track_id_unbind',event:'$UnbindID',unbind_value:r})}function unbind(e,t){var s='';if(_.isNumber(t)){if(_.isInteger(t)&&!1===_.isSafeInteger(t))return sa.log('Value must be String'),!1;t=String(t)}return _.isString(e)?!_.check.checkKeyword(e)||_.isPresetIdKeys(e,[IDENTITY_KEY.LOGIN])?(s='Key ['+e+'] is invalid',sa.log(s),!1):t&&''!==t?_.isString(t)?!!_.check.checkIdLength(t)&&void unbindWithoutCheck(e,t):(sa.log('Value must be String'),!1):(sa.log('Value is empty or null'),!1):(sa.log('Key must be String'),!1)}function loginWithKey(e,t){if(sa.log('loginWithKey is deprecated !!!'),!_.isString(e))return sa.log('Key must be String'),!1;var s='';if(!_.check.checkKeyword(e)&&e.length>100)s='Key ['+e+'] is invalid',sa.log(s);else if(!_.check.checkKeyword(e))return s='Key ['+e+'] is invalid',sa.log(s),!1;if(_.isPresetIdKeys(e,IDENTITIES.login_preset_id))return s='Key ['+e+'] is invalid',sa.log(s),!1;if(!(t=_.validId(t)))return!1;if(_.isSameAndAnonymousID(t))return!1;var n=store.getFirstId(),r=store.getDistinctId();_.isNewLoginId(e,t)&&(store._state.identities[e]=t,store.set('history_login_id',{name:e,value:t}),n||store.set('first_id',r),sa.trackSignup({id:t,event_name:'$SignUp',id_name:e}),store.identitiesSet({type:'login',id:t,id_name:e}))}function login(e){if(!(e=_.validId(e)))return!1;if(_.isSameAndAnonymousID(e))return!1;var t=store.getFirstId(),s=store.getDistinctId(),n=IDENTITY_KEY.LOGIN;_.isNewLoginId(n,e)&&(store._state.identities&&(store._state.identities[n]=e),store.set('history_login_id',{name:n,value:e}),t||store.set('first_id',s),sa.trackSignup({id:e,event_name:'$SignUp'}),store.identitiesSet({type:'login',id:e,id_name:n}))}function logout(){var e=store.getFirstId();store.identitiesSet({type:'logout'}),store.set('history_login_id',{name:'',value:''}),e?(store.set('first_id',''),store.set('distinct_id',e)):sa.log('\u6ca1\u6709first_id\uff0clogout\u5931\u8d25')}function getIdentities(){return _.isEmptyObject(store._state)?null:store.getIdentities()||null}function resetAnonymousIdentity(e){if(store.getFirstId())return sa.log('resetAnonymousIdentity must be used in a logout state \uff01'),!1;if('number'==typeof e&&(e=String(e)),void 0===e){var t=store.getUUID();store._state.identities[IDENTITIES.identity_id]=t,store.set('distinct_id',t)}else _.validId(e)&&(store._state.identities[IDENTITIES.identity_id]=e,store.set('distinct_id',e))}function getAnonymousID(){if(!_.isEmptyObject(sa.store._state))return sa.store._state._first_id||sa.store._state.first_id||sa.store._state._distinct_id||sa.store._state.distinct_id;sa.log('\u8bf7\u5148\u521d\u59cb\u5316SDK')}sa.store=store;var functions=Object.freeze({__proto__:null,trackSignup:trackSignup,bindWithoutCheck:bindWithoutCheck,bind:bind$1,unbindWithoutCheck:unbindWithoutCheck,unbind:unbind,loginWithKey:loginWithKey,login:login,logout:logout,getIdentities:getIdentities,resetAnonymousIdentity:resetAnonymousIdentity,getAnonymousID:getAnonymousID});for(var f in functions)sa[f]=functions[f];function isValidListener(e){return'function'==typeof e||!(!e||'object'!=typeof e)&&isValidListener(e.listener)}sa.quick=function(){var e=arguments[0],t=arguments[1],s=arguments[2],n=_.isObject(s)?s:{};'appLaunch'===e||'appShow'===e?t?sa[e](t,n):sa.log('App\u7684launch\u548cshow\uff0c\u5728sensors.quick\u7b2c\u4e8c\u4e2a\u53c2\u6570\u5fc5\u987b\u4f20\u5165App\u7684options\u53c2\u6570'):'appHide'===e&&(n=_.isObject(t)?t:{},sa[e](n))};class EventEmitterBase{constructor(){this._events={}}on(e,t){if(!e||!t)return!1;if(!isValidListener(t))throw new Error('listener must be a function');this._events[e]=this._events[e]||[];var s='object'==typeof t;return this._events[e].push(s?t:{listener:t,once:!1}),this}prepend(e,t){if(!e||!t)return!1;if(!isValidListener(t))throw new Error('listener must be a function');this._events[e]=this._events[e]||[];var s='object'==typeof t;return this._events[e].unshift(s?t:{listener:t,once:!1}),this}prependOnce(e,t){return this.prepend(e,{listener:t,once:!0})}once(e,t){return this.on(e,{listener:t,once:!0})}off(e,t){var s=this._events[e];if(!s)return!1;if('number'==typeof t)s.splice(t,1);else if('function'==typeof t)for(var n=0,r=s.length;n<r;n++)s[n]&&s[n].listener===t&&s.splice(n,1);return this}emit(e,t){var s=this._events[e];if(!s)return!1;for(var n=0;n<s.length;n++){var r=s[n];r&&(r.listener.call(this,t||{}),r.once&&this.off(e,n))}return this}removeAllListeners(e){e&&this._events[e]?this._events[e]=[]:this._events={}}listeners(e){return e&&'string'==typeof e?this._events[e]:this._events}}class EventEmitterEx extends EventEmitterBase{constructor(){super(),this.cacheEvents=[],this.maxLen=20}replay(e,t){this.on(e,t),this.cacheEvents.length>0&&this.cacheEvents.forEach(function(s){s.type===e&&t.call(null,s.data)})}emit(e,t){super.emit.apply(this,arguments),this.cacheEvents.push({type:e,data:t}),this.cacheEvents.length>this.maxLen&&this.cacheEvents.shift()}}var ee={};ee.sdk=new EventEmitterEx,ee.data=new EventEmitterEx;var eventEmitter=function(){this.sub=[]};eventEmitter.prototype={add:function(e){this.sub.push(e)},emit:function(e,t){this.sub.forEach(function(s){s.on(e,t)})}};var eventSub=function(e){sa.events.add(this),this._events=[],this.handle=e,this.ready=!1};eventSub.prototype={on:function(e,t){if(this.ready){if(isFunction(this.handle))try{this.handle(e,t)}catch(e){sa.log(e)}}else this._events.push({event:e,data:t})},isReady:function(){var e=this;e.ready=!0,e._events.forEach(function(t){if(isFunction(e.handle))try{e.handle(t.event,t.data)}catch(e){sa.log(e)}})}},sa.ee=ee,sa.meta=meta,sa.kit=kit,sa.modules={},sa.eventSub=eventSub,sa.events=new eventEmitter,sa.init=function(e){if(!0===meta.hasExeInit)return!1;e&&isObject(e)&&sa.setPara(e),meta.hasExeInit=!0,e&&isObject(e)&&sa.setPara(e),ee.sdk.emit('afterInitPara'),sa.store.init(),sa.system.init(),sa.para.batch_send&&(sa.system_api.getStorage('sensors_prepare_data',function(e){var t=[];e&&e.data&&isArray(e.data)&&(t=e.data,sa.batch_state.mem=t.concat(sa.batch_state.mem)),sa.batch_state.syncStorage=!0}),batchInterval())},sa.setPara=function(e){sa.para=extend2Lev(sa.para,e);var t=[];if(isArray(sa.para.source_channel))for(var s=sa.para.source_channel.length,n=0;n<s;n++)-1===RESERVE_CHANNEL.indexOf(' '+sa.para.source_channel[n]+' ')&&t.push(sa.para.source_channel[n]);sa.para.source_channel=t,'number'!=typeof sa.para.send_timeout&&(sa.para.send_timeout=1e3);var r={send_timeout:6e3,max_length:6};e&&e.datasend_timeout||sa.para.batch_send&&(sa.para.datasend_timeout=1e4),!0===sa.para.batch_send?sa.para.batch_send=extend({},r):isObject(sa.para.batch_send)&&(sa.para.batch_send=extend({},r,sa.para.batch_send)),sa.para.server_url?sa.para.preset_properties=isObject(sa.para.preset_properties)?sa.para.preset_properties:{}:sa.log('\u8bf7\u4f7f\u7528 setPara() \u65b9\u6cd5\u8bbe\u7f6e server_url \u6570\u636e\u63a5\u6536\u5730\u5740,\u8be6\u60c5\u53ef\u67e5\u770bhttps://www.sensorsdata.cn/manual/mp_sdk_new.html#112-%E5%BC%95%E5%85%A5%E5%B9%B6%E9%85%8D%E7%BD%AE%E5%8F%82%E6%95%B0')},sa.checkInit=function(){!0===sa.system.inited&&!0===sa.store.inited&&(sa.inited=!0,sa._queue.length>0&&(each(sa._queue,function(e){sa[e[0]].apply(sa,slice.call(e[1]))}),sa._queue=[]))},each(['setProfile','setOnceProfile','track','identify','bind','unbind','login','logout','registerApp','clearAppRegister'],function(e){var t=sa[e];sa[e]=function(){sa.inited?t.apply(sa,arguments):sa._queue.push([e,arguments])}});const forEach$1=Array.prototype.forEach,slice$1=Array.prototype.slice,_hasOwnProperty$1=Object.prototype.hasOwnProperty,_toString$1=Object.prototype.toString;function extend$1(e){return each$1(slice$1.call(arguments,1),function(t){for(var s in t)void 0!==t[s]&&(e[s]=t[s])}),e}function isObject$1(e){return null!==e&&'object'==typeof e}function isFunction$1(e){if(!e)return!1;var t=Object.prototype.toString.call(e);return'[object Function]'==t||'[object AsyncFunction]'==t||'[object GeneratorFunction]'==t}function isString$1(e){return'[object String]'==_toString$1.call(e)}function each$1(e,t,s){if(null==e)return!1;if(forEach$1&&e.forEach===forEach$1)e.forEach(t,s);else if(e.length===+e.length){for(var n=0,r=e.length;n<r;n++)if(n in e&&t.call(s,e[n],n,e)==={})return!1}else for(var a in e)if(_hasOwnProperty$1.call(e,a)&&t.call(s,e[a],a,e)==={})return!1}var global={},lifeCycleHook=['appOnLaunch','appOnShow','appOnHide','pageOnShow','pageOnLoad'],miniLifeCycleAPI={};function registerLifeCycleHook(e){Object.assign(miniLifeCycleAPI,e)}function getScene(e,t){if(!isObject$1(t))return!1;var s=t.meta.scene_prefix;return!(!s||!isString$1(s))&&('number'==typeof e||'string'==typeof e&&''!==e?e=s+String(e):'\u672a\u53d6\u5230\u503c')}function hookFunc(e,t,s){var n=e[t];e[t]=function(e){isFunction$1(s)&&s.call(this,e),n&&isFunction$1(n)&&n.call(this,e)}}function hookAppFunc(e){hookFunc(e,'onLaunch',miniLifeCycleAPI.appOnLaunch),hookFunc(e,'onShow',miniLifeCycleAPI.appOnShow),hookFunc(e,'onHide',miniLifeCycleAPI.appOnHide)}function proxyApp(e,t){function s(e){var s=App;App=function(n){try{e&&e(n),n[t.para.name]=t,s.apply(this,arguments)}catch(e){s.apply(this,arguments),global.sensors.log('App:'+e)}}}isObject$1(t)&&isFunction$1(t.platform_obj.onAppShow)&&isFunction$1(t.platform_obj.onAppHide)&&(!t.config||isObject$1(t.config)&&t.config.is_support_onshow)?(s(),t.platform_obj.onAppShow(function(e){if(!t.para.launched){var s=e||isFunction$1(t.platform_obj.getLaunchOptionsSync)&&t.platform_obj.getLaunchOptionsSync()||{};miniLifeCycleAPI.appOnLaunch(s),t.para.launched=!0}miniLifeCycleAPI.appOnShow(e)}),t.platform_obj.onAppHide(function(){miniLifeCycleAPI.appOnHide()})):s(e)}function appOnLaunch(e,t){if(isObject$1(e)){var s={};if(e&&e.path&&extend$1(s,global.sensors._.getAppProps(e)),e&&e.scene){var n=getScene(e.scene,global.sensors);n&&(s.$scene=n,global.sensors.meta.current_scene=n,global.sensors.registerApp({$latest_scene:n}))}else s.$scene='\u672a\u53d6\u5230\u503c';var r=global.sensors._.setUtm(e,s);global.sensors.meta.is_first_launch?(s.$is_first_time=!0,global.sensors._.isEmptyObject(r.pre1)||global.sensors.setOnceProfile(r.pre1)):s.$is_first_time=!1,global.sensors._.isEmptyObject(r.pre2)||global.sensors._.setLatestChannel(r.pre2),isObject$1(t)&&(s=extend$1(s,t)),global.sensors.para&&global.sensors.para.autoTrack&&global.sensors.para.autoTrack.appLaunch&&global.sensors.track('$MPLaunch',s)}else global.sensors.log('appOnLaunch:\u8bf7\u4f20\u5165\u6b63\u786e\u7684\u53c2\u6570')}function appOnShow(e,t){var s={};global.sensors.meta.mp_show_time=(new Date).getTime(),e&&e.path&&extend$1(s,global.sensors._.getAppProps(e));var n=global.sensors._.setUtm(e,s);if(global.sensors._.isEmptyObject(n.pre2)||global.sensors._.setLatestChannel(n.pre2),e&&e.scene){var r=getScene(e.scene,global.sensors);r&&(s.$scene=r,global.sensors.registerApp({$latest_scene:r}))}isObject$1(t)&&(s=extend$1(s,t)),global.sensors.para&&global.sensors.para.autoTrack&&global.sensors.para.autoTrack.appShow&&global.sensors.track('$MPShow',s)}function appOnHide(e){var t=(new Date).getTime(),s={};isObject$1(e)&&(s=extend$1(s,e)),extend$1(s,global.sensors._.getPageProps());var n=global.sensors.meta.mp_show_time;n&&t-n>0&&(t-n)/36e5<24&&(s.event_duration=(t-n)/1e3),global.sensors.para&&global.sensors.para.autoTrack&&global.sensors.para.autoTrack.appHide&&global.sensors.track('$MPHide',s)}lifeCycleHook.forEach(function(e){miniLifeCycleAPI[e]=function(){throw new Error(`\u9700\u8981\u5148\u5b9a\u4e49 '${e}' \u624d\u80fd\u4f7f\u7528`)}});var presetEvents={appLaunch:!0,appShow:!0,appHide:!0},AutoTrackApp={name:'AutoTrackApp'};function getMixedQuery$1(e){var t={};if(e&&global.sensors._.isObject(e.query)&&(t=global.sensors._.extend({},e.query),e.query.qrCode&&global.sensors._.extend(t,global.sensors._.getObjFromQuery(global.sensors._.decodeURIComponent(e.query.qrCode)))),e&&global.sensors._.isObject(e.referrerInfo)&&e.referrerInfo.extraData){var s={};global.sensors._.isObject(e.referrerInfo.extraData)&&!global.sensors._.isEmptyObject(e.referrerInfo.extraData)?s=e.referrerInfo.extraData:global.sensors._.isJSONString(e.referrerInfo.extraData)&&(s=JSON.parse(e.referrerInfo.extraData)),global.sensors._.extend(t,s)}return t}function setUtm$1(e,t){var s={},n=getMixedQuery$1(e),r=global.sensors._.getCustomUtmFromQuery(n,'$','_','$'),a=global.sensors._.getCustomUtmFromQuery(n,'$latest_','_latest_','$latest_');return s.pre1=r,s.pre2=a,global.sensors._.extend(t,r),s}function appOnLaunch$1(e,t){if(isObject$1(e)){var s={};if(e&&e.path&&extend$1(s,global.sensors._.getAppProps(e)),e&&e.scene){var n=getScene(e.scene,global.sensors);n&&(s.$scene=n,global.sensors.meta.current_scene=n,global.sensors.registerApp({$latest_scene:n}))}else s.$scene='\u672a\u53d6\u5230\u503c';var r=global.sensors.para&&global.sensors.para.autoTrack&&global.sensors.para.autoTrack.appLaunch,a=setUtm$1(e,s);global.sensors.meta.is_first_launch?(s.$is_first_time=!0,global.sensors._.isEmptyObject(a.pre1)||r&&global.sensors.setOnceProfile(a.pre1)):s.$is_first_time=!1,global.sensors._.isEmptyObject(a.pre2)||global.sensors._.setLatestChannel(a.pre2),s.$url_query=global.sensors._.setQuery(e.query),isObject$1(t)&&(s=extend$1(s,t)),r&&global.sensors.track('$MPLaunch',s)}else global.sensors.log('appOnLaunch:\u8bf7\u4f20\u5165\u6b63\u786e\u7684\u53c2\u6570')}function appOnShow$1(e,t){var s={};global.sensors.meta.mp_show_time=(new Date).getTime(),e&&e.path&&extend$1(s,global.sensors._.getAppProps(e));var n=setUtm$1(e,s);if(global.sensors._.isEmptyObject(n.pre2)||global.sensors._.setLatestChannel(n.pre2),e&&e.scene){var r=getScene(e.scene,global.sensors);r&&(s.$scene=r,global.sensors.registerApp({$latest_scene:r}))}isObject$1(t)&&(s=extend$1(s,t)),global.sensors.para&&global.sensors.para.autoTrack&&global.sensors.para.autoTrack.appShow&&global.sensors.track('$MPShow',s)}AutoTrackApp.init=function(e,t){if(!e)return console.log('\u8bf7\u6b63\u786e\u521d\u59cb\u5316 sensorsdata\uff0c\u624d\u80fd\u4f7f\u7528\u63d2\u4ef6'),!1;global.sensors=e,global.sensors.para.autoTrack=extend$1(presetEvents,t),AutoTrackApp.lifeCycleAPI(),AutoTrackApp.proxyFrameworkInterface()},AutoTrackApp.lifeCycleAPI=function(){var e={};e.appOnLaunch=appOnLaunch,e.appOnShow=appOnShow,e.appOnHide=appOnHide,registerLifeCycleHook(e)},AutoTrackApp.proxyFrameworkInterface=function(){proxyApp(hookAppFunc,global.sensors)},AutoTrackApp.lifeCycleAPI=function(){var e={};e.appOnLaunch=appOnLaunch$1,e.appOnShow=appOnShow$1,e.appOnHide=appOnHide,registerLifeCycleHook(e)};var global$1={};function getCurrentPath$1(e){var t='\u672a\u53d6\u5230',s=getCurrentPage$1(e);return s&&s.route&&(t=s.route),t}function getCurrentPage$1(e){var t,s={};if(e)try{s=(t=isFunction$1(e.platform_obj.getCurrentPages)?e.platform_obj.getCurrentPages():getCurrentPages())[t.length-1]}catch(t){e.log(t)}else console.log('getCurrentPage:\u8bf7\u4f20\u5165 sa \u5bf9\u8c61');return s}function isClick(e){return!!{tap:1,longtap:1,longpress:1}[e]}function createClickData(e){var t={},s={},n=e.currentTarget||{},r=n.dataset||{};return t.$element_id=n.id,t.$element_type=r.type,t.$element_content=r.content,t.$element_name=r.name,isObject$1(e.event_prop)&&(s=e.event_prop),t.$url_path=getCurrentPath$1(global$1.sensors),t.$url=global$1.sensors._.getPageProps().$url,t=extend$1(t,s)}var ClickTrack={track:function(e){var t=createClickData(e),s=e.currentTarget||{},n=e.target||{},r=e.type,a=global$1.sensors.para;if(isObject$1(a.framework)&&isObject$1(a.framework.taro)&&!a.framework.taro.createApp&&n.id&&s.id&&n.id!==s.id)return!1;if(isObject$1(t)&&r&&isClick(r)){if(a.preset_events&&a.preset_events.collect_element&&!1===a.preset_events.collect_element(arguments[0]))return!1;global$1.sensors.track('$MPClick',t)}}},MP_HOOKS={data:1,onLoad:1,onShow:1,onReady:1,onPullDownRefresh:1,onReachBottom:1,onShareAppMessage:1,onPageScroll:1,onResize:1,onTabItemTap:1,onHide:1,onUnload:1};function clickProxy(e,t){var s=e[t];e[t]=function(){var e=s.apply(this,arguments),t=arguments[0];return isObject$1(t)&&ClickTrack.track(t),e}}function monitorClick(e){var t=[],s=global$1.sensors.para.autoTrack;if(s&&s.mpClick){t=getMethods(e),tabProxy(e);for(var n=t.length,r=0;r<n;r++)clickProxy(e,t[r])}}function tabProxy(e){var t=e.onTabItemTap;e.onTabItemTap=function(e){t&&t.apply(this,arguments);var s={};e&&(s.$element_content=e.text||''),s.$element_type='tabBar',s.$url_path=e.pagePath?e.pagePath:global$1.sensors._.getCurrentPath(),global$1.sensors.track('$MPClick',s)}}function getMethods(e){var t=[];for(var s in e)'function'!=typeof e[s]||MP_HOOKS[s]||t.push(s);return t}function hookPageFunc(e){hookFunc(e,'onShow',miniLifeCycleAPI.pageOnShow),hookFunc(e,'onLoad',miniLifeCycleAPI.pageOnLoad),hookFunc(e,'onUnload',miniLifeCycleAPI.pageOnUnload),hookFunc(e,'onHide',miniLifeCycleAPI.pageOnHide)}function proxyPage(e,t){var s=Page;Page=function(n){try{n||(n={}),isFunction$1(e)&&e(n),isFunction$1(t)&&t(n),s.apply(this,arguments)}catch(e){s.apply(this,arguments),console.log('Page:'+e)}};var n=Component;Component=function(s){try{s||(s={}),s.methods||(s.methods={}),isFunction$1(e)&&e(s.methods),isFunction$1(t)&&t(s.methods),n.apply(this,arguments)}catch(e){n.apply(this,arguments),console.log('Component:'+e)}}}var pageLeave=function(){if(global$1.sensors.para.autoTrack&&global$1.sensors.para.autoTrack.pageLeave){var e={},t='';try{t=(e=getCurrentPage$1(global$1.sensors))?e.route:''}catch(e){global$1.sensors.log(e)}if(global$1.sensors.meta.page_show_time>=0&&''!==t){var s={},n=(Date.now()-global$1.sensors.meta.page_show_time)/1e3;(isNaN(n)||n<0)&&(n=0),extend$1(s,global$1.sensors._.getPageProps()),s.event_duration=n,global$1.sensors.track('$MPPageLeave',s),global$1.sensors.meta.page_show_time=-1}}};function pageOnLoad(e){var t=this;if(global$1.sensors._.isObject(e)){try{t=getCurrentPage$1(global$1.sensors)}catch(e){global$1.sensors.log('pageOnLoad:'+e)}t.sensors_mp_url_query=global$1.sensors._.setQuery(e),t.sensors_mp_encode_url_query=global$1.sensors._.setQuery(e,!0)}}function pageOnShow(){global$1.sensors.meta.page_show_time=Date.now();var e={},t='';try{var s=getCurrentPage$1(global$1.sensors);t=s?s.route:''}catch(e){global$1.sensors.log('pageOnShow:'+e)}extend$1(e,global$1.sensors._.getPageProps()),e.$referrer=global$1.sensors.meta.sa_referrer,global$1.sensors.para&&global$1.sensors.para.autoTrack&&global$1.sensors.para.autoTrack.pageShow&&global$1.sensors.track('$MPViewScreen',e),global$1.sensors.meta.sa_referrer=t}function pageOnUnload(){pageLeave()}function pageOnHide(){pageLeave()}var presetEvents$1={pageShow:!0,mpClick:!0,pageLeave:!1},AutoTrackPage={name:'AutoTrackPage',init:function(e,t){if(!e)return console.log('\u8bf7\u6b63\u786e\u521d\u59cb\u5316 sensorsdata\uff0c\u624d\u80fd\u4f7f\u7528\u63d2\u4ef6'),!1;global$1.sensors=e,global$1.sensors.para.autoTrack=extend$1(presetEvents$1,t),AutoTrackPage.lifeCycleAPI(),AutoTrackPage.proxyFrameworkInterface()},lifeCycleAPI:function(){var e={};e.pageOnShow=pageOnShow,e.pageOnLoad=pageOnLoad,e.pageOnUnload=pageOnUnload,e.pageOnHide=pageOnHide,registerLifeCycleHook(e)},proxyFrameworkInterface:function(){proxyPage(hookPageFunc,monitorClick)}},presetEvents$2={appLaunch:!0,appShow:!0,appHide:!0,pageShow:!0,mpClick:!0,pageLeave:!1},AutoTrack={name:'AutoTrack',init:function(e,t){if(!e)return console.log('\u8bf7\u6b63\u786e\u521d\u59cb\u5316 sensorsdata\uff0c\u624d\u80fd\u4f7f\u7528\u63d2\u4ef6'),!1;e.ee.sdk.on('afterInitPara',function(){var s=extend$1(presetEvents$2,e.para.autoTrack,t);e.use(AutoTrackApp,s),e.use(AutoTrackPage,s)})}};sa.platform_obj=my,sa._=_,sa.system=system,sa.system_api=Object.assign(compose,compose$1,compose$2),sa.lib.name='AlipayMini',sa.properties.$lib='AlipayMini',sa.storageName='sensorsdata2015_zfb',sa.meta.scene_prefix='ali-',sa.use(AutoTrack);export default sa;