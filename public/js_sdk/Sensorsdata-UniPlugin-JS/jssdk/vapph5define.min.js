!function(){var e=null,t=null,n={getVisibility:function(e){function t(e){var i=e.parentNode;if(9===i.nodeType)return!0;if("0"===n(e,"opacity")||"none"===n(e,"display")||"hidden"===n(e,"visibility"))return!1;if(i){var r=i.getBoundingClientRect();if("hidden"===n(i,"overflow")||"scroll"===n(i,"overflow")){if(o.bottom<=r.top||o.top>=r.bottom||o.right<=r.left||o.left>=r.right)return!1}else if("hidden"===n(i,"overflow-y")||"scroll"===n(i,"overflow-y")){if(o.bottom<=r.top||o.top>=r.bottom)return!1}else if(("hidden"===n(i,"overflow-x")||"scroll"===n(i,"overflow-x"))&&(o.right<=r.left||o.left>=r.right))return!1;return t(i)}return!0}function n(e,t){return window.getComputedStyle?document.defaultView.getComputedStyle(e,null)[t]:e.currentStyle?e.currentStyle[t]:void 0}function i(){var e=document.body.getBoundingClientRect();return!(o.top>=e.bottom||o.bottom<=e.top||o.left>=e.right||o.right<=e.left)}var o=e.getBoundingClientRect();return t(e)&&i(e)},getSubElements:function(t){var n=[];if(t.children.length)for(var i=0;i<t.children.length;i++)e.isElement(t.children[i])&&e.isObject(t.children[i].sensorsDefineStore)&&n.push(t.children[i].sensorsDefineStore.id);return n},getInfo:function(n){if(!e.isElement(n)||!e.isObject(n.sensorsDefineStore))return null;var i=n.getBoundingClientRect(),o=n.tagName,r={id:n.sensorsDefineStore.id,$element_content:e.getEleInfo({target:n}).$element_content,$element_selector:t.heatmap.getDomSelector(n),tagName:o,top:i.top,left:i.left,scrollX:window.pageXOffset,scrollY:window.pageYOffset,width:i.width,height:i.height,scale:window.devicePixelRatio,visibility:this.getVisibility(n),$url:location.href,$title:document.title,lib_version:t.lib_version,subelements:this.getSubElements(n),level:n.sensorsDefineStore.level,is_list_view:"li"===o.toLowerCase(),$element_path:t.heatmap.getElementPath(n,t.para.heatmap&&"not_use_id"===t.para.heatmap.element_selector)},l=t.heatmap.getElementPosition(n,r.$element_path,t.para.heatmap&&"not_use_id"===t.para.heatmap.element_selector);return e.isNumber(l)&&(r.$element_position=l),t.heatmap.getClosestLi(n)&&(r.list_selector=this.getListSelector(n)),e.isString(n.sensorsDefineStore.list_selector)&&(r.list_selector=n.sensorsDefineStore.list_selector),r},getZIndex:function(e){var t=window.getComputedStyle(e,null).getPropertyValue("z-index");return t&&!isNaN(+t)?t:0},getlevel:function(e,t){var n=Number(this.getZIndex(e)),i=0,o=window.getComputedStyle(e,null).getPropertyValue("position");return!o||"absolute"!==o&&"fixed"!==o||(i=1e5),t+n+i},getListSelector:function(e){var n="",i=t.heatmap.getClosestLi(e);if(i){var o=t.heatmap.getDomSelector(i,[],!0),r=t.heatmap.getDomSelector(e,[],!0);n=r.replace(o,"")}return n},initElements:function(){function n(e,n){var i={level:n,id:"h"+r};r++,e.sensorsDefineStore=i;var o=t.heatmap.getTargetElement(e);if(o){o.sensorsdata_enable_click=!0;var l=window.getComputedStyle(o,null).getPropertyValue("display");"inline"===l&&(o.style.display="inline-block")}}function i(t,r){for(var s=0;s<t.length;s++)if(e.isElement(t[s])){var a=t[s],f=o.getlevel(a,r);n(a,f),l.push(a),a.children&&i(a.children,f+1)}}var o=this,r=0,l=[];return i(document.body.children,1),l},getElementsInfo:function(){var t=this,n=[],i=[],o=this.initElements();return e.isArray(o)&&e.each(o,function(o){if(e.isElement(o)){var r=o,l=t.getInfo(r);e.isObject(l)&&l.visibility&&(r.sensorsdata_enable_click===!0?(l.enable_click=!0,n.push(l)):(l.enable_click=!1,i.push(l)))}}),{data:this.sortLevel(n),extra_elements:this.sortLevel(i)}},sortLevel:function(e){return e.sort(function(e,t){return e.level-t.level}),e},postDefineInfo:function(){var e=n.getElementsInfo(),i={data:e.data,extra_elements:e.extra_elements};new t.SDKJSBridge("visualized_track").notifyApp(i)},addDefineListener:function(n){function i(e,t,n){var i=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver,o=new i(n);o.observe(e,t)}var o=this,r={childList:!0,subtree:!0,attributes:!0},l=function(){return e.throttle(n,1e3)}();i(document.body,r,l),e.addEvent(window,"scroll",l),e.addEvent(window,"resize",l),e.addEvent(window,"load",l),t.ee&&t.ee.spa&&t.ee.spa.on("switch",function(e){e!==location.href&&o.postPageInfo()})},postPageInfo:function(){var e={data:{$title:document.title,$url:location.href,lib_version:t.lib_version}};new t.SDKJSBridge("page_info").notifyApp(e)},init:function(){var n=this;window.sa_jssdk_app_define_mode=function(i,o){o?(n.postPageInfo(),n.postDefineInfo()):(t=i,e=t._,n.postPageInfo(),e.bindReady(function(){n.postDefineInfo(),n.addDefineListener(n.postDefineInfo)}))}}};n.init()}();