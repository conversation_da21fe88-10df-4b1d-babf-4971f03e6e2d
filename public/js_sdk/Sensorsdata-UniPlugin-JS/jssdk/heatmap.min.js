!function(){var e,t=function(e){function t(){if("object"==typeof console&&console.log)try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}}function n(e,t){t=t||z;var n=t.createElement("script");n.text=e,t.head.appendChild(n).parentNode.removeChild(n)}function r(e){var t=!!e&&"length"in e&&e.length,n=re.type(e);return"function"!==n&&!re.isWindow(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function i(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}function o(e,t,n){return re.isFunction(t)?re.grep(e,function(e,r){return!!t.call(e,r,e)!==n}):t.nodeType?re.grep(e,function(e){return e===t!==n}):"string"!=typeof t?re.grep(e,function(e){return J.call(t,e)>-1!==n}):pe.test(t)?re.filter(t,e,n):(t=re.filter(t,e),re.grep(e,function(e){return J.call(t,e)>-1!==n&&1===e.nodeType}))}function a(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}function s(e){var t={};return re.each(e.match(xe)||[],function(e,n){t[n]=!0}),t}function l(e){return e}function u(e){throw e}function c(e,t,n,r){var i;try{e&&re.isFunction(i=e.promise)?i.call(e).done(t).fail(n):e&&re.isFunction(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}function f(){z.removeEventListener("DOMContentLoaded",f),e.removeEventListener("load",f),re.ready()}function d(){this.expando=re.expando+d.uid++}function p(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:De.test(e)?JSON.parse(e):e)}function h(e,n,r){var i;if(void 0===r&&1===e.nodeType)if(i="data-"+n.replace(Ee,"-$&").toLowerCase(),r=e.getAttribute(i),"string"==typeof r){try{r=p(r)}catch(o){t(o)}Te.set(e,n,r)}else r=void 0;return r}function g(e,t,n,r){var i,o=1,a=20,s=r?function(){return r.cur()}:function(){return re.css(e,t,"")},l=s(),u=n&&n[3]||(re.cssNumber[t]?"":"px"),c=(re.cssNumber[t]||"px"!==u&&+l)&&Se.exec(re.css(e,t));if(c&&c[3]!==u){u=u||c[3],n=n||[],c=+l||1;do o=o||".5",c/=o,re.style(e,t,c+u);while(o!==(o=s()/l)&&1!==o&&--a)}return n&&(c=+c||+l||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=u,r.start=c,r.end=i)),i}function v(e){var t,n=e.ownerDocument,r=e.nodeName,i=Ie[r];return i?i:(t=n.body.appendChild(n.createElement(r)),i=re.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),Ie[r]=i,i)}function m(e,t){for(var n,r,i=[],o=0,a=e.length;o<a;o++)r=e[o],r.style&&(n=r.style.display,t?("none"===n&&(i[o]=Ce.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&Le(r)&&(i[o]=v(r))):"none"!==n&&(i[o]="none",Ce.set(r,"display",n)));for(o=0;o<a;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}function y(e,t){var n;return n="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&i(e,t)?re.merge([e],n):n}function x(e,t){for(var n=0,r=e.length;n<r;n++)Ce.set(e[n],"globalEval",!t||Ce.get(t[n],"globalEval"))}function b(e,t,n,r,i){for(var o,a,s,l,u,c,f=t.createDocumentFragment(),d=[],p=0,h=e.length;p<h;p++)if(o=e[p],o||0===o)if("object"===re.type(o))re.merge(d,o.nodeType?[o]:o);else if(Oe.test(o)){for(a=a||f.appendChild(t.createElement("div")),s=(Me.exec(o)||["",""])[1].toLowerCase(),l=Fe[s]||Fe._default,a.innerHTML=l[1]+re.htmlPrefilter(o)+l[2],c=l[0];c--;)a=a.lastChild;re.merge(d,a.childNodes),a=f.firstChild,a.textContent=""}else d.push(t.createTextNode(o));for(f.textContent="",p=0;o=d[p++];)if(r&&re.inArray(o,r)>-1)i&&i.push(o);else if(u=re.contains(o.ownerDocument,o),a=y(f.appendChild(o),"script"),u&&x(a),n)for(c=0;o=a[c++];)He.test(o.type||"")&&n.push(o);return f}function w(){return!0}function _(){return!1}function k(){try{return z.activeElement}catch(e){t(e)}}function C(e,t,n,r,i,o){var a,s;if("object"==typeof t){"string"!=typeof n&&(r=r||n,n=void 0);for(s in t)C(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),i===!1)i=_;else if(!i)return e;return 1===o&&(a=i,i=function(e){return re().off(e),a.apply(this,arguments)},i.guid=a.guid||(a.guid=re.guid++)),e.each(function(){re.event.add(this,t,i,r,n)})}function T(e,t){return i(e,"table")&&i(11!==t.nodeType?t:t.firstChild,"tr")?re(">tbody",e)[0]||e:e}function D(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function E(e){var t=Ve.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function A(e,t){var n,r,i,o,a,s,l,u;if(1===t.nodeType){if(Ce.hasData(e)&&(o=Ce.access(e),a=Ce.set(t,o),u=o.events)){delete a.handle,a.events={};for(i in u)for(n=0,r=u[i].length;n<r;n++)re.event.add(t,i,u[i][n])}Te.hasData(e)&&(s=Te.access(e),l=re.extend({},s),Te.set(t,l))}}function S(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Be.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function N(e,t,r,i){t=X.apply([],t);var o,a,s,l,u,c,f=0,d=e.length,p=d-1,h=t[0],g=re.isFunction(h);if(g||d>1&&"string"==typeof h&&!te.checkClone&&$e.test(h))return e.each(function(n){var o=e.eq(n);g&&(t[0]=h.call(this,n,o.html())),N(o,t,r,i)});if(d&&(o=b(t,e[0].ownerDocument,!1,e,i),a=o.firstChild,1===o.childNodes.length&&(o=a),a||i)){for(s=re.map(y(o,"script"),D),l=s.length;f<d;f++)u=o,f!==p&&(u=re.clone(u,!0,!0),l&&re.merge(s,y(u,"script"))),r.call(e[f],u,f);if(l)for(c=s[s.length-1].ownerDocument,re.map(s,E),f=0;f<l;f++)u=s[f],He.test(u.type||"")&&!Ce.access(u,"globalEval")&&re.contains(c,u)&&(u.src?re._evalUrl&&re._evalUrl(u.src):n(u.textContent.replace(Xe,""),c))}return e}function L(e,t,n){for(var r,i=t?re.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||re.cleanData(y(r)),r.parentNode&&(n&&re.contains(r.ownerDocument,r)&&x(y(r,"script")),r.parentNode.removeChild(r));return e}function P(e,t,n){var r,i,o,a,s=e.style;return n=n||Ye(e),n&&(a=n.getPropertyValue(t)||n[t],""!==a||re.contains(e.ownerDocument,e)||(a=re.style(e,t)),!te.pixelMarginRight()&&Je.test(a)&&Ge.test(t)&&(r=s.width,i=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=r,s.minWidth=i,s.maxWidth=o)),void 0!==a?a+"":a}function I(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}function B(e){if(e in nt)return e;for(var t=e[0].toUpperCase()+e.slice(1),n=tt.length;n--;)if(e=tt[n]+t,e in nt)return e}function M(e){var t=re.cssProps[e];return t||(t=re.cssProps[e]=B(e)||e),t}function H(e,t,n){var r=Se.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function F(e,t,n,r,i){var o,a=0;for(o=n===(r?"border":"content")?4:"width"===t?1:0;o<4;o+=2)"margin"===n&&(a+=re.css(e,n+Ne[o],!0,i)),r?("content"===n&&(a-=re.css(e,"padding"+Ne[o],!0,i)),"margin"!==n&&(a-=re.css(e,"border"+Ne[o]+"Width",!0,i))):(a+=re.css(e,"padding"+Ne[o],!0,i),"padding"!==n&&(a+=re.css(e,"border"+Ne[o]+"Width",!0,i)));return a}function O(e,t,n){var r,i=Ye(e),o=P(e,t,i),a="border-box"===re.css(e,"boxSizing",!1,i);return Je.test(o)?o:(r=a&&(te.boxSizingReliable()||o===e.style[t]),"auto"===o&&(o=e["offset"+t[0].toUpperCase()+t.slice(1)]),o=parseFloat(o)||0,o+F(e,t,n||(a?"border":"content"),r,i)+"px")}function j(e){var t=e.match(xe)||[];return t.join(" ")}function R(e){return e.getAttribute&&e.getAttribute("class")||""}function q(e,t,n,r){var i;if(Array.isArray(t))re.each(t,function(t,i){n||ut.test(e)?r(e,i):q(e+"["+("object"==typeof i&&null!=i?t:"")+"]",i,n,r)});else if(n||"object"!==re.type(t))r(e,t);else for(i in t)q(e+"["+i+"]",t[i],n,r)}var U=function(){function e(){return n=(9301*n+49297)%233280,n/233280}var t=new Date,n=t.getTime();return Math.ceil(1e5*e())/1e5},W=[],z=e.document,$=Object.getPrototypeOf,V=W.slice,X=W.concat,G=W.push,J=W.indexOf,Y={},K=Y.toString,Q=Y.hasOwnProperty,Z=Q.toString,ee=Z.call(Object),te={},ne="3.2.1 -ajax,-ajax/jsonp,-ajax/load,-ajax/parseXML,-ajax/script,-ajax/var/location,-ajax/var/nonce,-ajax/var/rquery,-ajax/xhr,-manipulation/_evalUrl,-event/ajax,-effects,-effects/Tween,-effects/animatedSelector",re=function(e,t){return new re.fn.init(e,t)},ie=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,oe=/^-ms-/,ae=/-([a-z])/g,se=function(e,t){return t.toUpperCase()};re.fn=re.prototype={jquery:ne,constructor:re,length:0,toArray:function(){return V.call(this)},get:function(e){return null==e?V.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=re.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return re.each(this,e)},map:function(e){return this.pushStack(re.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(V.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:G,sort:W.sort,splice:W.splice},re.extend=re.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,l=arguments.length,u=!1;for("boolean"==typeof a&&(u=a,a=arguments[s]||{},s++),"object"==typeof a||re.isFunction(a)||(a={}),s===l&&(a=this,s--);s<l;s++)if(null!=(e=arguments[s]))for(t in e)n=a[t],r=e[t],a!==r&&(u&&r&&(re.isPlainObject(r)||(i=Array.isArray(r)))?(i?(i=!1,o=n&&Array.isArray(n)?n:[]):o=n&&re.isPlainObject(n)?n:{},a[t]=re.extend(u,o,r)):void 0!==r&&(a[t]=r));return a},re.extend({expando:"jQuery"+(ne+U()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===re.type(e)},isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){var t=re.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==K.call(e))&&(!(t=$(e))||(n=Q.call(t,"constructor")&&t.constructor,"function"==typeof n&&Z.call(n)===ee))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?Y[K.call(e)]||"object":typeof e},globalEval:function(e){n(e)},camelCase:function(e){return e.replace(oe,"ms-").replace(ae,se)},each:function(e,t){var n,i=0;if(r(e))for(n=e.length;i<n&&t.call(e[i],i,e[i])!==!1;i++);else for(i in e)if(t.call(e[i],i,e[i])===!1)break;return e},trim:function(e){return null==e?"":(e+"").replace(ie,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(r(Object(e))?re.merge(n,"string"==typeof e?[e]:e):G.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:J.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r,i=[],o=0,a=e.length,s=!n;o<a;o++)r=!t(e[o],o),r!==s&&i.push(e[o]);return i},map:function(e,t,n){var i,o,a=0,s=[];if(r(e))for(i=e.length;a<i;a++)o=t(e[a],a,n),null!=o&&s.push(o);else for(a in e)o=t(e[a],a,n),null!=o&&s.push(o);return X.apply([],s)},guid:1,proxy:function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),re.isFunction(e))return r=V.call(arguments,2),i=function(){return e.apply(t||this,r.concat(V.call(arguments)))},i.guid=e.guid=e.guid||re.guid++,i},now:Date.now,support:te}),"function"==typeof Symbol&&(re.fn[Symbol.iterator]=W[Symbol.iterator]),re.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){Y["[object "+t+"]"]=t.toLowerCase()});var le=function(e){function n(e,t,n,r){var i,o,a,s,l,u,c,f=t&&t.ownerDocument,p=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==p&&9!==p&&11!==p)return n;if(!r&&((t?t.ownerDocument||t:W)!==B&&I(t),t=t||B,H)){if(11!==p&&(l=xe.exec(e)))if(i=l[1]){if(9===p){if(!(a=t.getElementById(i)))return n;if(a.id===i)return n.push(a),n}else if(f&&(a=f.getElementById(i))&&R(t,a)&&a.id===i)return n.push(a),n}else{if(l[2])return ee.apply(n,t.getElementsByTagName(e)),n;if((i=l[3])&&k.getElementsByClassName&&t.getElementsByClassName)return ee.apply(n,t.getElementsByClassName(i)),n}if(k.qsa&&!G[e+" "]&&(!F||!F.test(e))){if(1!==p)f=t,c=e;else if("object"!==t.nodeName.toLowerCase()){for((s=t.getAttribute("id"))?s=s.replace(ke,Ce):t.setAttribute("id",s=q),u=E(e),o=u.length;o--;)u[o]="#"+s+" "+h(u[o]);c=u.join(","),f=be.test(e)&&d(t.parentNode)||t}if(c)try{return ee.apply(n,f.querySelectorAll(c)),n}catch(g){}finally{s===q&&t.removeAttribute("id")}}}return S(e.replace(ue,"$1"),t,n,r)}function r(){function e(n,r){return t.push(n+" ")>C.cacheLength&&delete e[t.shift()],e[n+" "]=r}var t=[];return e}function i(e){return e[q]=!0,e}function o(e){var t=B.createElement("fieldset");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function a(e,t){for(var n=e.split("|"),r=n.length;r--;)C.attrHandle[n[r]]=t}function s(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function l(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function u(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function c(e){return function(t){return"form"in t?t.parentNode&&t.disabled===!1?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&De(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function f(e){return i(function(t){return t=+t,i(function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))})})}function d(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}function p(){}function h(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function g(e,t,n){var r=t.dir,i=t.next,o=i||r,a=n&&"parentNode"===o,s=$++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||a)return e(t,n,i);return!1}:function(t,n,l){var u,c,f,d=[z,s];if(l){for(;t=t[r];)if((1===t.nodeType||a)&&e(t,n,l))return!0}else for(;t=t[r];)if(1===t.nodeType||a)if(f=t[q]||(t[q]={}),c=f[t.uniqueID]||(f[t.uniqueID]={}),i&&i===t.nodeName.toLowerCase())t=t[r]||t;else{if((u=c[o])&&u[0]===z&&u[1]===s)return d[2]=u[2];if(c[o]=d,d[2]=e(t,n,l))return!0}return!1}}function v(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function m(e,t,r){for(var i=0,o=t.length;i<o;i++)n(e,t[i],r);return r}function y(e,t,n,r,i){for(var o,a=[],s=0,l=e.length,u=null!=t;s<l;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),u&&t.push(s)));return a}function x(e,t,n,r,o,a){return r&&!r[q]&&(r=x(r)),o&&!o[q]&&(o=x(o,a)),i(function(i,a,s,l){var u,c,f,d=[],p=[],h=a.length,g=i||m(t||"*",s.nodeType?[s]:s,[]),v=!e||!i&&t?g:y(g,d,e,s,l),x=n?o||(i?e:h||r)?[]:a:v;if(n&&n(v,x,s,l),r)for(u=y(x,p),r(u,[],s,l),c=u.length;c--;)(f=u[c])&&(x[p[c]]=!(v[p[c]]=f));if(i){if(o||e){if(o){for(u=[],c=x.length;c--;)(f=x[c])&&u.push(v[c]=f);o(null,x=[],u,l)}for(c=x.length;c--;)(f=x[c])&&(u=o?ne(i,f):d[c])>-1&&(i[u]=!(a[u]=f))}}else x=y(x===a?x.splice(h,x.length):x),o?o(null,a,x,l):ee.apply(a,x)})}function b(e){for(var t,n,r,i=e.length,o=C.relative[e[0].type],a=o||C.relative[" "],s=o?1:0,l=g(function(e){return e===t},a,!0),u=g(function(e){return ne(t,e)>-1},a,!0),c=[function(e,n,r){var i=!o&&(r||n!==N)||((t=n).nodeType?l(e,n,r):u(e,n,r));return t=null,i}];s<i;s++)if(n=C.relative[e[s].type])c=[g(v(c),n)];else{if(n=C.filter[e[s].type].apply(null,e[s].matches),n[q]){for(r=++s;r<i&&!C.relative[e[r].type];r++);return x(s>1&&v(c),s>1&&h(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(ue,"$1"),n,s<r&&b(e.slice(s,r)),r<i&&b(e=e.slice(r)),r<i&&h(e))}c.push(n)}return v(c)}function w(e,t){var r=t.length>0,o=e.length>0,a=function(i,a,s,l,u){var c,f,d,p=0,h="0",g=i&&[],v=[],m=N,x=i||o&&C.find.TAG("*",u),b=z+=null==m?1:U()||.1,w=x.length;for(u&&(N=a===B||a||u);h!==w&&null!=(c=x[h]);h++){if(o&&c){for(f=0,a||c.ownerDocument===B||(I(c),s=!H);d=e[f++];)if(d(c,a||B,s)){l.push(c);break}u&&(z=b)}r&&((c=!d&&c)&&p--,i&&g.push(c))}if(p+=h,r&&h!==p){for(f=0;d=t[f++];)d(g,v,a,s);if(i){if(p>0)for(;h--;)g[h]||v[h]||(v[h]=Q.call(l));v=y(v)}ee.apply(l,v),u&&!i&&v.length>0&&p+t.length>1&&n.uniqueSort(l)}return u&&(z=b,N=m),g};return r?i(a):a}var _,k,C,T,D,E,A,S,N,L,P,I,B,M,H,F,O,j,R,q="sizzle"+1*new Date,W=e.document,z=0,$=0,V=r(),X=r(),G=r(),J=function(e,t){return e===t&&(P=!0),0},Y={}.hasOwnProperty,K=[],Q=K.pop,Z=K.push,ee=K.push,te=K.slice,ne=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},re="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ie="[\\x20\\t\\r\\n\\f]",oe="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",ae="\\["+ie+"*("+oe+")(?:"+ie+"*([*^$|!~]?=)"+ie+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+oe+"))|)"+ie+"*\\]",se=":("+oe+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ae+")*)|.*)\\)|)",le=new RegExp(ie+"+","g"),ue=new RegExp("^"+ie+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ie+"+$","g"),ce=new RegExp("^"+ie+"*,"+ie+"*"),fe=new RegExp("^"+ie+"*([>+~]|"+ie+")"+ie+"*"),de=new RegExp("="+ie+"*([^\\]'\"]*?)"+ie+"*\\]","g"),pe=new RegExp(se),he=new RegExp("^"+oe+"$"),ge={ID:new RegExp("^#("+oe+")"),CLASS:new RegExp("^\\.("+oe+")"),TAG:new RegExp("^("+oe+"|[*])"),ATTR:new RegExp("^"+ae),PSEUDO:new RegExp("^"+se),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ie+"*(even|odd|(([+-]|)(\\d*)n|)"+ie+"*(?:([+-]|)"+ie+"*(\\d+)|))"+ie+"*\\)|)","i"),bool:new RegExp("^(?:"+re+")$","i"),needsContext:new RegExp("^"+ie+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ie+"*((?:-\\d)?\\d*)"+ie+"*\\)|)(?=[^-]|$)","i")},ve=/^(?:input|select|textarea|button)$/i,me=/^h\d$/i,ye=/^[^{]+\{\s*\[native \w/,xe=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,be=/[+~]/,we=new RegExp("\\\\([\\da-f]{1,6}"+ie+"?|("+ie+")|.)","ig"),_e=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},ke=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,Ce=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},Te=function(){I()},De=g(function(e){return e.disabled===!0&&("form"in e||"label"in e)},{dir:"parentNode",next:"legend"});try{ee.apply(K=te.call(W.childNodes),W.childNodes),K[W.childNodes.length].nodeType}catch(Ee){ee={apply:K.length?function(e,t){Z.apply(e,te.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}k=n.support={},D=n.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},I=n.setDocument=function(e){var t,n,r=e?e.ownerDocument||e:W;return r!==B&&9===r.nodeType&&r.documentElement?(B=r,M=B.documentElement,H=!D(B),W!==B&&(n=B.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",Te,!1):n.attachEvent&&n.attachEvent("onunload",Te)),k.attributes=o(function(e){return e.className="i",!e.getAttribute("className")}),k.getElementsByTagName=o(function(e){return e.appendChild(B.createComment("")),!e.getElementsByTagName("*").length}),k.getElementsByClassName=ye.test(B.getElementsByClassName),k.getById=o(function(e){return M.appendChild(e).id=q,!B.getElementsByName||!B.getElementsByName(q).length}),k.getById?(C.filter.ID=function(e){var t=e.replace(we,_e);return function(e){return e.getAttribute("id")===t}},C.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&H){var n=t.getElementById(e);return n?[n]:[]}}):(C.filter.ID=function(e){var t=e.replace(we,_e);return function(e){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},C.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&H){var n,r,i,o=t.getElementById(e);if(o){if(n=o.getAttributeNode("id"),n&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if(n=o.getAttributeNode("id"),n&&n.value===e)return[o]}return[]}}),C.find.TAG=k.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):k.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[i++];)1===n.nodeType&&r.push(n);return r}return o},C.find.CLASS=k.getElementsByClassName&&function(e,t){if("undefined"!=typeof t.getElementsByClassName&&H)return t.getElementsByClassName(e)},O=[],F=[],(k.qsa=ye.test(B.querySelectorAll))&&(o(function(e){M.appendChild(e).innerHTML="<a id='"+q+"'></a><select id='"+q+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&F.push("[*^$]="+ie+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||F.push("\\["+ie+"*(?:value|"+re+")"),e.querySelectorAll("[id~="+q+"-]").length||F.push("~="),e.querySelectorAll(":checked").length||F.push(":checked"),e.querySelectorAll("a#"+q+"+*").length||F.push(".#.+[+~]")}),o(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=B.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&F.push("name"+ie+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&F.push(":enabled",":disabled"),M.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&F.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),F.push(",.*:")})),(k.matchesSelector=ye.test(j=M.matches||M.webkitMatchesSelector||M.mozMatchesSelector||M.oMatchesSelector||M.msMatchesSelector))&&o(function(e){k.disconnectedMatch=j.call(e,"*"),j.call(e,"[s!='']:x"),O.push("!=",se)}),F=F.length&&new RegExp(F.join("|")),O=O.length&&new RegExp(O.join("|")),t=ye.test(M.compareDocumentPosition),R=t||ye.test(M.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},J=t?function(e,t){if(e===t)return P=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n?n:(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&n||!k.sortDetached&&t.compareDocumentPosition(e)===n?e===B||e.ownerDocument===W&&R(W,e)?-1:t===B||t.ownerDocument===W&&R(W,t)?1:L?ne(L,e)-ne(L,t):0:4&n?-1:1)}:function(e,t){if(e===t)return P=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],l=[t];if(!i||!o)return e===B?-1:t===B?1:i?-1:o?1:L?ne(L,e)-ne(L,t):0;if(i===o)return s(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)l.unshift(n);for(;a[r]===l[r];)r++;return r?s(a[r],l[r]):a[r]===W?-1:l[r]===W?1:0},B):B},n.matches=function(e,t){return n(e,null,null,t)},n.matchesSelector=function(e,r){if((e.ownerDocument||e)!==B&&I(e),r=r.replace(de,"='$1']"),k.matchesSelector&&H&&!G[r+" "]&&(!O||!O.test(r))&&(!F||!F.test(r)))try{var i=j.call(e,r);if(i||k.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(o){t(o)}return n(r,B,null,[e]).length>0},n.contains=function(e,t){return(e.ownerDocument||e)!==B&&I(e),R(e,t)},n.attr=function(e,t){(e.ownerDocument||e)!==B&&I(e);var n=C.attrHandle[t.toLowerCase()],r=n&&Y.call(C.attrHandle,t.toLowerCase())?n(e,t,!H):void 0;return void 0!==r?r:k.attributes||!H?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},n.escape=function(e){return(e+"").replace(ke,Ce)},n.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},n.uniqueSort=function(e){var t,n=[],r=0,i=0;if(P=!k.detectDuplicates,L=!k.sortStable&&e.slice(0),e.sort(J),P){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return L=null,e},T=n.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=T(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=T(t);return n},C=n.selectors={cacheLength:50,createPseudo:i,match:ge,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(we,_e),e[3]=(e[3]||e[4]||e[5]||"").replace(we,_e),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||n.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&n.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return ge.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&pe.test(n)&&(t=E(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(we,_e).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=V[e+" "];return t||(t=new RegExp("(^|"+ie+")"+e+"("+ie+"|$)"))&&V(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,r){return function(i){var o=n.attr(i,e);return null==o?"!="===t:!t||(o+="","="===t?o===r:"!="===t?o!==r:"^="===t?r&&0===o.indexOf(r):"*="===t?r&&o.indexOf(r)>-1:"$="===t?r&&o.slice(-r.length)===r:"~="===t?(" "+o.replace(le," ")+" ").indexOf(r)>-1:"|="===t&&(o===r||o.slice(0,r.length+1)===r+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,l){var u,c,f,d,p,h,g=o!==a?"nextSibling":"previousSibling",v=t.parentNode,m=s&&t.nodeName.toLowerCase(),y=!l&&!s,x=!1;if(v){if(o){for(;g;){for(d=t;d=d[g];)if(s?d.nodeName.toLowerCase()===m:1===d.nodeType)return!1;h=g="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?v.firstChild:v.lastChild],a&&y){for(d=v,f=d[q]||(d[q]={}),c=f[d.uniqueID]||(f[d.uniqueID]={}),u=c[e]||[],p=u[0]===z&&u[1],x=p&&u[2],d=p&&v.childNodes[p];d=++p&&d&&d[g]||(x=p=0)||h.pop();)if(1===d.nodeType&&++x&&d===t){c[e]=[z,p,x];break}}else if(y&&(d=t,f=d[q]||(d[q]={}),c=f[d.uniqueID]||(f[d.uniqueID]={}),u=c[e]||[],p=u[0]===z&&u[1],x=p),x===!1)for(;(d=++p&&d&&d[g]||(x=p=0)||h.pop())&&((s?d.nodeName.toLowerCase()!==m:1!==d.nodeType)||!++x||(y&&(f=d[q]||(d[q]={}),c=f[d.uniqueID]||(f[d.uniqueID]={}),c[e]=[z,x]),d!==t)););return x-=i,x===r||x%r===0&&x/r>=0}}},PSEUDO:function(e,t){var r,o=C.pseudos[e]||C.setFilters[e.toLowerCase()]||n.error("unsupported pseudo: "+e);return o[q]?o(t):o.length>1?(r=[e,e,"",t],C.setFilters.hasOwnProperty(e.toLowerCase())?i(function(e,n){for(var r,i=o(e,t),a=i.length;a--;)r=ne(e,i[a]),e[r]=!(n[r]=i[a])}):function(e){return o(e,0,r)}):o}},pseudos:{not:i(function(e){var t=[],n=[],r=A(e.replace(ue,"$1"));return r[q]?i(function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}}),has:i(function(e){return function(t){return n(e,t).length>0}}),contains:i(function(e){return e=e.replace(we,_e),function(t){return(t.textContent||t.innerText||T(t)).indexOf(e)>-1}}),lang:i(function(e){return he.test(e||"")||n.error("unsupported lang: "+e),e=e.replace(we,_e).toLowerCase(),function(t){var n;do if(n=H?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===M},focus:function(e){return e===B.activeElement&&(!B.hasFocus||B.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:c(!1),disabled:c(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!C.pseudos.empty(e)},header:function(e){return me.test(e.nodeName)},input:function(e){return ve.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:f(function(){return[0]}),last:f(function(e,t){return[t-1]}),eq:f(function(e,t,n){return[n<0?n+t:n]}),even:f(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:f(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:f(function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e}),gt:f(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}},C.pseudos.nth=C.pseudos.eq;for(_ in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})C.pseudos[_]=l(_);for(_ in{submit:!0,reset:!0})C.pseudos[_]=u(_);return p.prototype=C.filters=C.pseudos,C.setFilters=new p,E=n.tokenize=function(e,t){var r,i,o,a,s,l,u,c=X[e+" "];if(c)return t?0:c.slice(0);for(s=e,l=[],u=C.preFilter;s;){r&&!(i=ce.exec(s))||(i&&(s=s.slice(i[0].length)||s),l.push(o=[])),r=!1,(i=fe.exec(s))&&(r=i.shift(),o.push({value:r,type:i[0].replace(ue," ")}),s=s.slice(r.length));for(a in C.filter)!(i=ge[a].exec(s))||u[a]&&!(i=u[a](i))||(r=i.shift(),o.push({value:r,type:a,matches:i}),s=s.slice(r.length));if(!r)break}return t?s.length:s?n.error(e):X(e,l).slice(0)},A=n.compile=function(e,t){var n,r=[],i=[],o=G[e+" "];if(!o){for(t||(t=E(e)),n=t.length;n--;)o=b(t[n]),o[q]?r.push(o):i.push(o);o=G(e,w(i,r)),o.selector=e}return o},S=n.select=function(e,t,n,r){var i,o,a,s,l,u="function"==typeof e&&e,c=!r&&E(e=u.selector||e);if(n=n||[],1===c.length){if(o=c[0]=c[0].slice(0),o.length>2&&"ID"===(a=o[0]).type&&9===t.nodeType&&H&&C.relative[o[1].type]){if(t=(C.find.ID(a.matches[0].replace(we,_e),t)||[])[0],!t)return n;u&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=ge.needsContext.test(e)?0:o.length;i--&&(a=o[i],!C.relative[s=a.type]);)if((l=C.find[s])&&(r=l(a.matches[0].replace(we,_e),be.test(o[0].type)&&d(t.parentNode)||t))){if(o.splice(i,1),e=r.length&&h(o),!e)return ee.apply(n,r),n;break}}return(u||A(e,c))(r,t,!H,n,!t||be.test(e)&&d(t.parentNode)||t),n},k.sortStable=q.split("").sort(J).join("")===q,k.detectDuplicates=!!P,I(),k.sortDetached=o(function(e){return 1&e.compareDocumentPosition(B.createElement("fieldset"))}),o(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||a("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),k.attributes&&o(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||a("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),o(function(e){return null==e.getAttribute("disabled")})||a(re,function(e,t,n){var r;if(!n)return e[t]===!0?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null}),n}(e);re.find=le,re.expr=le.selectors,re.expr[":"]=re.expr.pseudos,re.uniqueSort=re.unique=le.uniqueSort,re.text=le.getText,re.isXMLDoc=le.isXML,re.contains=le.contains,re.escapeSelector=le.escape;var ue=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&re(e).is(n))break;r.push(e)}return r},ce=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},fe=re.expr.match.needsContext,de=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i,pe=/^.[^:#\[\.,]*$/;re.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?re.find.matchesSelector(r,e)?[r]:[]:re.find.matches(e,re.grep(t,function(e){return 1===e.nodeType}))},re.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(re(e).filter(function(){for(t=0;t<r;t++)if(re.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)re.find(e,i[t],n);return r>1?re.uniqueSort(n):n},filter:function(e){return this.pushStack(o(this,e||[],!1))},not:function(e){return this.pushStack(o(this,e||[],!0))},is:function(e){return!!o(this,"string"==typeof e&&fe.test(e)?re(e):e||[],!1).length}});var he,ge=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,ve=re.fn.init=function(e,t,n){
var r,i;if(!e)return this;if(n=n||he,"string"==typeof e){if(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:ge.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof re?t[0]:t,re.merge(this,re.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:z,!0)),de.test(r[1])&&re.isPlainObject(t))for(r in t)re.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return i=z.getElementById(r[2]),i&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):re.isFunction(e)?void 0!==n.ready?n.ready(e):e(re):re.makeArray(e,this)};ve.prototype=re.fn,he=re(z);var me=/^(?:parents|prev(?:Until|All))/,ye={children:!0,contents:!0,next:!0,prev:!0};re.fn.extend({has:function(e){var t=re(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(re.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&re(e);if(!fe.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&re.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?re.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?J.call(re(e),this[0]):J.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(re.uniqueSort(re.merge(this.get(),re(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),re.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return ue(e,"parentNode")},parentsUntil:function(e,t,n){return ue(e,"parentNode",n)},next:function(e){return a(e,"nextSibling")},prev:function(e){return a(e,"previousSibling")},nextAll:function(e){return ue(e,"nextSibling")},prevAll:function(e){return ue(e,"previousSibling")},nextUntil:function(e,t,n){return ue(e,"nextSibling",n)},prevUntil:function(e,t,n){return ue(e,"previousSibling",n)},siblings:function(e){return ce((e.parentNode||{}).firstChild,e)},children:function(e){return ce(e.firstChild)},contents:function(e){return i(e,"iframe")?e.contentDocument:(i(e,"template")&&(e=e.content||e),re.merge([],e.childNodes))}},function(e,t){re.fn[e]=function(n,r){var i=re.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=re.filter(r,i)),this.length>1&&(ye[e]||re.uniqueSort(i),me.test(e)&&i.reverse()),this.pushStack(i)}});var xe=/[^\x20\t\r\n\f]+/g;re.Callbacks=function(e){e="string"==typeof e?s(e):re.extend({},e);var t,n,r,i,o=[],a=[],l=-1,u=function(){for(i=i||e.once,r=t=!0;a.length;l=-1)for(n=a.shift();++l<o.length;)o[l].apply(n[0],n[1])===!1&&e.stopOnFalse&&(l=o.length,n=!1);e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},c={add:function(){return o&&(n&&!t&&(l=o.length-1,a.push(n)),function r(t){re.each(t,function(t,n){re.isFunction(n)?e.unique&&c.has(n)||o.push(n):n&&n.length&&"string"!==re.type(n)&&r(n)})}(arguments),n&&!t&&u()),this},remove:function(){return re.each(arguments,function(e,t){for(var n;(n=re.inArray(t,o,n))>-1;)o.splice(n,1),n<=l&&l--}),this},has:function(e){return e?re.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=a=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=a=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=n||[],n=[e,n.slice?n.slice():n],a.push(n),t||u()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},re.extend({Deferred:function(t){var n=[["notify","progress",re.Callbacks("memory"),re.Callbacks("memory"),2],["resolve","done",re.Callbacks("once memory"),re.Callbacks("once memory"),0,"resolved"],["reject","fail",re.Callbacks("once memory"),re.Callbacks("once memory"),1,"rejected"]],r="pending",i={state:function(){return r},always:function(){return o.done(arguments).fail(arguments),this},"catch":function(e){return i.then(null,e)},pipe:function(){var e=arguments;return re.Deferred(function(t){re.each(n,function(n,r){var i=re.isFunction(e[r[4]])&&e[r[4]];o[r[1]](function(){var e=i&&i.apply(this,arguments);e&&re.isFunction(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[r[0]+"With"](this,i?[e]:arguments)})}),e=null}).promise()},then:function(t,r,i){function o(t,n,r,i){return function(){var s=this,c=arguments,f=function(){var e,f;if(!(t<a)){if(e=r.apply(s,c),e===n.promise())throw new TypeError("Thenable self-resolution");f=e&&("object"==typeof e||"function"==typeof e)&&e.then,re.isFunction(f)?i?f.call(e,o(a,n,l,i),o(a,n,u,i)):(a++,f.call(e,o(a,n,l,i),o(a,n,u,i),o(a,n,l,n.notifyWith))):(r!==l&&(s=void 0,c=[e]),(i||n.resolveWith)(s,c))}},d=i?f:function(){try{f()}catch(e){re.Deferred.exceptionHook&&re.Deferred.exceptionHook(e,d.stackTrace),t+1>=a&&(r!==u&&(s=void 0,c=[e]),n.rejectWith(s,c))}};t?d():(re.Deferred.getStackHook&&(d.stackTrace=re.Deferred.getStackHook()),e.setTimeout(d))}}var a=0;return re.Deferred(function(e){n[0][3].add(o(0,e,re.isFunction(i)?i:l,e.notifyWith)),n[1][3].add(o(0,e,re.isFunction(t)?t:l)),n[2][3].add(o(0,e,re.isFunction(r)?r:u))}).promise()},promise:function(e){return null!=e?re.extend(e,i):i}},o={};return re.each(n,function(e,t){var a=t[2],s=t[5];i[t[1]]=a.add,s&&a.add(function(){r=s},n[3-e][2].disable,n[0][2].lock),a.add(t[3].fire),o[t[0]]=function(){return o[t[0]+"With"](this===o?void 0:this,arguments),this},o[t[0]+"With"]=a.fireWith}),i.promise(o),t&&t.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=V.call(arguments),o=re.Deferred(),a=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?V.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(c(e,o.done(a(n)).resolve,o.reject,!t),"pending"===o.state()||re.isFunction(i[n]&&i[n].then)))return o.then();for(;n--;)c(i[n],a(n),o.reject);return o.promise()}});var be=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;re.Deferred.exceptionHook=function(t,n){e.console&&e.console.warn&&t&&be.test(t.name)&&e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,n)},re.readyException=function(t){e.setTimeout(function(){throw t})};var we=re.Deferred();re.fn.ready=function(e){return we.then(e)["catch"](function(e){re.readyException(e)}),this},re.extend({isReady:!1,readyWait:1,ready:function(e){(e===!0?--re.readyWait:re.isReady)||(re.isReady=!0,e!==!0&&--re.readyWait>0||we.resolveWith(z,[re]))}}),re.ready.then=we.then,"complete"===z.readyState||"loading"!==z.readyState&&!z.documentElement.doScroll?e.setTimeout(re.ready):(z.addEventListener("DOMContentLoaded",f),e.addEventListener("load",f));var _e=function(e,t,n,r,i,o,a){var s=0,l=e.length,u=null==n;if("object"===re.type(n)){i=!0;for(s in n)_e(e,t,s,n[s],!0,o,a)}else if(void 0!==r&&(i=!0,re.isFunction(r)||(a=!0),u&&(a?(t.call(e,r),t=null):(u=t,t=function(e,t,n){return u.call(re(e),n)})),t))for(;s<l;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:u?t.call(e):l?t(e[0],n):o},ke=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};d.uid=1,d.prototype={cache:function(e){var t=e[this.expando];return t||(t={},ke(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[re.camelCase(t)]=n;else for(r in t)i[re.camelCase(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][re.camelCase(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){Array.isArray(t)?t=t.map(re.camelCase):(t=re.camelCase(t),t=t in r?[t]:t.match(xe)||[]),n=t.length;for(;n--;)delete r[t[n]]}(void 0===t||re.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!re.isEmptyObject(t)}};var Ce=new d,Te=new d,De=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Ee=/[A-Z]/g;re.extend({hasData:function(e){return Te.hasData(e)||Ce.hasData(e)},data:function(e,t,n){return Te.access(e,t,n)},removeData:function(e,t){Te.remove(e,t)},_data:function(e,t,n){return Ce.access(e,t,n)},_removeData:function(e,t){Ce.remove(e,t)}}),re.fn.extend({data:function(e,t){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(i=Te.get(o),1===o.nodeType&&!Ce.get(o,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&(r=a[n].name,0===r.indexOf("data-")&&(r=re.camelCase(r.slice(5)),h(o,r,i[r])));Ce.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each(function(){Te.set(this,e)}):_e(this,function(t){var n;if(o&&void 0===t){if(n=Te.get(o,e),void 0!==n)return n;if(n=h(o,e),void 0!==n)return n}else this.each(function(){Te.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){Te.remove(this,e)})}}),re.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=Ce.get(e,t),n&&(!r||Array.isArray(n)?r=Ce.access(e,t,re.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=re.queue(e,t),r=n.length,i=n.shift(),o=re._queueHooks(e,t),a=function(){re.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,a,o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Ce.get(e,n)||Ce.access(e,n,{empty:re.Callbacks("once memory").add(function(){Ce.remove(e,[t+"queue",n])})})}}),re.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?re.queue(this[0],e):void 0===t?this:this.each(function(){var n=re.queue(this,e,t);re._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&re.dequeue(this,e)})},dequeue:function(e){return this.each(function(){re.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=re.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)n=Ce.get(o[a],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var Ae=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Se=new RegExp("^(?:([+-])=|)("+Ae+")([a-z%]*)$","i"),Ne=["Top","Right","Bottom","Left"],Le=function(e,t){return e=t||e,"none"===e.style.display||""===e.style.display&&re.contains(e.ownerDocument,e)&&"none"===re.css(e,"display")},Pe=function(e,t,n,r){var i,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];i=n.apply(e,r||[]);for(o in t)e.style[o]=a[o];return i},Ie={};re.fn.extend({show:function(){return m(this,!0)},hide:function(){return m(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Le(this)?re(this).show():re(this).hide()})}});var Be=/^(?:checkbox|radio)$/i,Me=/<([a-z][^\/\0>\x20\t\r\n\f]+)/i,He=/^$|\/(?:java|ecma)script/i,Fe={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};Fe.optgroup=Fe.option,Fe.tbody=Fe.tfoot=Fe.colgroup=Fe.caption=Fe.thead,Fe.th=Fe.td;var Oe=/<|&#?\w+;/;!function(){var e=z.createDocumentFragment(),t=e.appendChild(z.createElement("div")),n=z.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),te.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",te.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue}();var je=z.documentElement,Re=/^key/,qe=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ue=/^([^.]*)(?:\.(.+)|)/;re.event={global:{},add:function(e,t,n,r,i){var o,a,s,l,u,c,f,d,p,h,g,v=Ce.get(e);if(v)for(n.handler&&(o=n,n=o.handler,i=o.selector),i&&re.find.matchesSelector(je,i),n.guid||(n.guid=re.guid++),(l=v.events)||(l=v.events={}),(a=v.handle)||(a=v.handle=function(t){return"undefined"!=typeof re&&re.event.triggered!==t.type?re.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(xe)||[""],u=t.length;u--;)s=Ue.exec(t[u])||[],p=g=s[1],h=(s[2]||"").split(".").sort(),p&&(f=re.event.special[p]||{},p=(i?f.delegateType:f.bindType)||p,f=re.event.special[p]||{},c=re.extend({type:p,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&re.expr.match.needsContext.test(i),namespace:h.join(".")},o),(d=l[p])||(d=l[p]=[],d.delegateCount=0,f.setup&&f.setup.call(e,r,h,a)!==!1||e.addEventListener&&e.addEventListener(p,a)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),i?d.splice(d.delegateCount++,0,c):d.push(c),re.event.global[p]=!0)},remove:function(e,t,n,r,i){var o,a,s,l,u,c,f,d,p,h,g,v=Ce.hasData(e)&&Ce.get(e);if(v&&(l=v.events)){for(t=(t||"").match(xe)||[""],u=t.length;u--;)if(s=Ue.exec(t[u])||[],p=g=s[1],h=(s[2]||"").split(".").sort(),p){for(f=re.event.special[p]||{},p=(r?f.delegateType:f.bindType)||p,d=l[p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=d.length;o--;)c=d[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,f.remove&&f.remove.call(e,c));a&&!d.length&&(f.teardown&&f.teardown.call(e,h,v.handle)!==!1||re.removeEvent(e,p,v.handle),delete l[p])}else for(p in l)re.event.remove(e,p+t[u],n,r,!0);re.isEmptyObject(l)&&Ce.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=re.event.fix(e),l=new Array(arguments.length),u=(Ce.get(this,"events")||{})[s.type]||[],c=re.event.special[s.type]||{};for(l[0]=s,t=1;t<arguments.length;t++)l[t]=arguments[t];if(s.delegateTarget=this,!c.preDispatch||c.preDispatch.call(this,s)!==!1){for(a=re.event.handlers.call(this,s,u),t=0;(i=a[t++])&&!s.isPropagationStopped();)for(s.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!s.rnamespace.test(o.namespace)||(s.handleObj=o,s.data=o.data,r=((re.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,l),void 0!==r&&(s.result=r)===!1&&(s.preventDefault(),s.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,i,o,a,s=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!("click"===e.type&&e.button>=1))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||u.disabled!==!0)){for(o=[],a={},n=0;n<l;n++)r=t[n],i=r.selector+" ",void 0===a[i]&&(a[i]=r.needsContext?re(i,this).index(u)>-1:re.find(i,this,null,[u]).length),a[i]&&o.push(r);o.length&&s.push({elem:u,handlers:o})}return u=this,l<t.length&&s.push({elem:u,handlers:t.slice(l)}),s},addProp:function(e,t){Object.defineProperty(re.Event.prototype,e,{enumerable:!0,configurable:!0,get:re.isFunction(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[re.expando]?e:new re.Event(e)},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==k()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===k()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if("checkbox"===this.type&&this.click&&i(this,"input"))return this.click(),!1},_default:function(e){return i(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},re.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},re.Event=function(e,t){return this instanceof re.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?w:_,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&re.extend(this,t),this.timeStamp=e&&e.timeStamp||re.now(),void(this[re.expando]=!0)):new re.Event(e,t)},re.Event.prototype={constructor:re.Event,isDefaultPrevented:_,isPropagationStopped:_,isImmediatePropagationStopped:_,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=w,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=w,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=w,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},re.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&Re.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&qe.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},re.event.addProp),re.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){re.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,o=e.handleObj;return i&&(i===r||re.contains(r,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),re.fn.extend({on:function(e,t,n,r){return C(this,e,t,n,r)},one:function(e,t,n,r){return C(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,re(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return t!==!1&&"function"!=typeof t||(n=t,t=void 0),n===!1&&(n=_),this.each(function(){re.event.remove(this,e,n,t)})}});var We=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,ze=/<script|<style|<link/i,$e=/checked\s*(?:[^=]|=\s*.checked.)/i,Ve=/^true\/(.*)/,Xe=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;re.extend({htmlPrefilter:function(e){return e.replace(We,"<$1></$2>")},clone:function(e,t,n){var r,i,o,a,s=e.cloneNode(!0),l=re.contains(e.ownerDocument,e);if(!(te.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||re.isXMLDoc(e)))for(a=y(s),o=y(e),r=0,i=o.length;r<i;r++)S(o[r],a[r]);if(t)if(n)for(o=o||y(e),a=a||y(s),r=0,i=o.length;r<i;r++)A(o[r],a[r]);else A(e,s);return a=y(s,"script"),a.length>0&&x(a,!l&&y(e,"script")),s},cleanData:function(e){for(var t,n,r,i=re.event.special,o=0;void 0!==(n=e[o]);o++)if(ke(n)){if(t=n[Ce.expando]){if(t.events)for(r in t.events)i[r]?re.event.remove(n,r):re.removeEvent(n,r,t.handle);n[Ce.expando]=void 0}n[Te.expando]&&(n[Te.expando]=void 0)}}}),re.fn.extend({detach:function(e){return L(this,e,!0)},remove:function(e){return L(this,e)},text:function(e){return _e(this,function(e){return void 0===e?re.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return N(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=T(this,e);t.appendChild(e)}})},prepend:function(){return N(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=T(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return N(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return N(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(re.cleanData(y(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return re.clone(this,e,t)})},html:function(e){return _e(this,function(e){var n=this[0]||{},r=0,i=this.length;if(void 0===e&&1===n.nodeType)return n.innerHTML;if("string"==typeof e&&!ze.test(e)&&!Fe[(Me.exec(e)||["",""])[1].toLowerCase()]){e=re.htmlPrefilter(e);try{for(;r<i;r++)n=this[r]||{},1===n.nodeType&&(re.cleanData(y(n,!1)),n.innerHTML=e);n=0}catch(o){t(o)}}n&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return N(this,arguments,function(t){var n=this.parentNode;re.inArray(this,e)<0&&(re.cleanData(y(this)),n&&n.replaceChild(t,this))},e)}}),re.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){re.fn[e]=function(e){for(var n,r=[],i=re(e),o=i.length-1,a=0;a<=o;a++)n=a===o?this:this.clone(!0),re(i[a])[t](n),G.apply(r,n.get());return this.pushStack(r)}});var Ge=/^margin/,Je=new RegExp("^("+Ae+")(?!px)[a-z%]+$","i"),Ye=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)};!function(){function t(){if(s){s.style.cssText="box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",s.innerHTML="",je.appendChild(a);var t=e.getComputedStyle(s);n="1%"!==t.top,o="2px"===t.marginLeft,r="4px"===t.width,s.style.marginRight="50%",i="4px"===t.marginRight,je.removeChild(a),s=null}}var n,r,i,o,a=z.createElement("div"),s=z.createElement("div");s.style&&(s.style.backgroundClip="content-box",s.cloneNode(!0).style.backgroundClip="",te.clearCloneStyle="content-box"===s.style.backgroundClip,a.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",a.appendChild(s),re.extend(te,{pixelPosition:function(){return t(),n},boxSizingReliable:function(){return t(),r},pixelMarginRight:function(){return t(),i},reliableMarginLeft:function(){return t(),o}}))}();var Ke=/^(none|table(?!-c[ea]).+)/,Qe=/^--/,Ze={position:"absolute",visibility:"hidden",display:"block"},et={letterSpacing:"0",fontWeight:"400"},tt=["Webkit","Moz","ms"],nt=z.createElement("div").style;re.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=P(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":"cssFloat"},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=re.camelCase(t),l=Qe.test(t),u=e.style;return l||(t=M(s)),a=re.cssHooks[t]||re.cssHooks[s],void 0===n?a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:u[t]:(o=typeof n,"string"===o&&(i=Se.exec(n))&&i[1]&&(n=g(e,t,i),o="number"),null!=n&&n===n&&("number"===o&&(n+=i&&i[3]||(re.cssNumber[s]?"":"px")),te.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(l?u.setProperty(t,n):u[t]=n)),void 0)}},css:function(e,t,n,r){var i,o,a,s=re.camelCase(t),l=Qe.test(t);return l||(t=M(s)),a=re.cssHooks[t]||re.cssHooks[s],a&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=P(e,t,r)),"normal"===i&&t in et&&(i=et[t]),""===n||n?(o=parseFloat(i),n===!0||isFinite(o)?o||0:i):i}}),re.each(["height","width"],function(e,t){re.cssHooks[t]={get:function(e,n,r){if(n)return!Ke.test(re.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?O(e,t,r):Pe(e,Ze,function(){return O(e,t,r)})},set:function(e,n,r){var i,o=r&&Ye(e),a=r&&F(e,t,r,"border-box"===re.css(e,"boxSizing",!1,o),o);return a&&(i=Se.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=re.css(e,t)),H(e,n,a)}}}),re.cssHooks.marginLeft=I(te.reliableMarginLeft,function(e,t){if(t)return(parseFloat(P(e,"marginLeft"))||e.getBoundingClientRect().left-Pe(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),re.each({margin:"",padding:"",border:"Width"},function(e,t){re.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+Ne[r]+t]=o[r]||o[r-2]||o[0];return i}},Ge.test(e)||(re.cssHooks[e+t].set=H)}),re.fn.extend({css:function(e,t){return _e(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Ye(e),i=t.length;a<i;a++)o[t[a]]=re.css(e,t[a],!1,r);return o}return void 0!==n?re.style(e,t,n):re.css(e,t)},e,t,arguments.length>1)}}),re.fn.delay=function(t,n){return t=re.fx?re.fx.speeds[t]||t:t,n=n||"fx",this.queue(n,function(n,r){var i=e.setTimeout(n,t);r.stop=function(){e.clearTimeout(i)}})},function(){var e=z.createElement("input"),t=z.createElement("select"),n=t.appendChild(z.createElement("option"));e.type="checkbox",te.checkOn=""!==e.value,te.optSelected=n.selected,e=z.createElement("input"),e.value="t",e.type="radio",te.radioValue="t"===e.value}();var rt,it=re.expr.attrHandle;re.fn.extend({attr:function(e,t){return _e(this,re.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){re.removeAttr(this,e)})}}),re.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?re.prop(e,t,n):(1===o&&re.isXMLDoc(e)||(i=re.attrHooks[t.toLowerCase()]||(re.expr.match.bool.test(t)?rt:void 0)),void 0!==n?null===n?void re.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:(r=re.find.attr(e,t),null==r?void 0:r))},attrHooks:{type:{set:function(e,t){if(!te.radioValue&&"radio"===t&&i(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(xe);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),rt={set:function(e,t,n){return t===!1?re.removeAttr(e,n):e.setAttribute(n,n),n}},re.each(re.expr.match.bool.source.match(/\w+/g),function(e,t){var n=it[t]||re.find.attr;it[t]=function(e,t,r){var i,o,a=t.toLowerCase();return r||(o=it[a],it[a]=i,i=null!=n(e,t,r)?a:null,it[a]=o),i}});var ot=/^(?:input|select|textarea|button)$/i,at=/^(?:a|area)$/i;re.fn.extend({prop:function(e,t){return _e(this,re.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[re.propFix[e]||e]})}}),re.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&re.isXMLDoc(e)||(t=re.propFix[t]||t,i=re.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=re.find.attr(e,"tabindex");return t?parseInt(t,10):ot.test(e.nodeName)||at.test(e.nodeName)&&e.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),te.optSelected||(re.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),re.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){re.propFix[this.toLowerCase()]=this}),re.fn.extend({addClass:function(e){var t,n,r,i,o,a,s,l=0;if(re.isFunction(e))return this.each(function(t){re(this).addClass(e.call(this,t,R(this)))});if("string"==typeof e&&e)for(t=e.match(xe)||[];n=this[l++];)if(i=R(n),r=1===n.nodeType&&" "+j(i)+" "){for(a=0;o=t[a++];)r.indexOf(" "+o+" ")<0&&(r+=o+" ");s=j(r),i!==s&&n.setAttribute("class",s)}return this},removeClass:function(e){var t,n,r,i,o,a,s,l=0;if(re.isFunction(e))return this.each(function(t){re(this).removeClass(e.call(this,t,R(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(xe)||[];n=this[l++];)if(i=R(n),r=1===n.nodeType&&" "+j(i)+" "){for(a=0;o=t[a++];)for(;r.indexOf(" "+o+" ")>-1;)r=r.replace(" "+o+" "," ");s=j(r),i!==s&&n.setAttribute("class",s)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):re.isFunction(e)?this.each(function(n){re(this).toggleClass(e.call(this,n,R(this),t),t)}):this.each(function(){var t,r,i,o;if("string"===n)for(r=0,i=re(this),o=e.match(xe)||[];t=o[r++];)i.hasClass(t)?i.removeClass(t):i.addClass(t);else void 0!==e&&"boolean"!==n||(t=R(this),t&&Ce.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||e===!1?"":Ce.get(this,"__className__")||""))})},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+j(R(n))+" ").indexOf(t)>-1)return!0;return!1}});var st=/\r/g;re.fn.extend({val:function(e){var t,n,r,i=this[0];{if(arguments.length)return r=re.isFunction(e),this.each(function(n){var i;1===this.nodeType&&(i=r?e.call(this,n,re(this).val()):e,null==i?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=re.map(i,function(e){return null==e?"":e+""})),t=re.valHooks[this.type]||re.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))});if(i)return t=re.valHooks[i.type]||re.valHooks[i.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:(n=i.value,"string"==typeof n?n.replace(st,""):null==n?"":n)}}}),re.extend({valHooks:{option:{get:function(e){var t=re.find.attr(e,"value");return null!=t?t:j(re.text(e))}},select:{get:function(e){var t,n,r,o=e.options,a=e.selectedIndex,s="select-one"===e.type,l=s?null:[],u=s?a+1:o.length;for(r=a<0?u:s?a:0;r<u;r++)if(n=o[r],(n.selected||r===a)&&!n.disabled&&(!n.parentNode.disabled||!i(n.parentNode,"optgroup"))){if(t=re(n).val(),s)return t;l.push(t)}return l},set:function(e,t){for(var n,r,i=e.options,o=re.makeArray(t),a=i.length;a--;)r=i[a],(r.selected=re.inArray(re.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),re.each(["radio","checkbox"],function(){re.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=re.inArray(re(e).val(),t)>-1}},te.checkOn||(re.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var lt=/^(?:focusinfocus|focusoutblur)$/;re.extend(re.event,{trigger:function(t,n,r,i){var o,a,s,l,u,c,f,d=[r||z],p=Q.call(t,"type")?t.type:t,h=Q.call(t,"namespace")?t.namespace.split("."):[];if(a=s=r=r||z,3!==r.nodeType&&8!==r.nodeType&&!lt.test(p+re.event.triggered)&&(p.indexOf(".")>-1&&(h=p.split("."),p=h.shift(),h.sort()),u=p.indexOf(":")<0&&"on"+p,t=t[re.expando]?t:new re.Event(p,"object"==typeof t&&t),t.isTrigger=i?2:3,t.namespace=h.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=r),n=null==n?[t]:re.makeArray(n,[t]),f=re.event.special[p]||{},i||!f.trigger||f.trigger.apply(r,n)!==!1)){if(!i&&!f.noBubble&&!re.isWindow(r)){for(l=f.delegateType||p,lt.test(l+p)||(a=a.parentNode);a;a=a.parentNode)d.push(a),s=a;s===(r.ownerDocument||z)&&d.push(s.defaultView||s.parentWindow||e)}for(o=0;(a=d[o++])&&!t.isPropagationStopped();)t.type=o>1?l:f.bindType||p,c=(Ce.get(a,"events")||{})[t.type]&&Ce.get(a,"handle"),c&&c.apply(a,n),c=u&&a[u],c&&c.apply&&ke(a)&&(t.result=c.apply(a,n),t.result===!1&&t.preventDefault());return t.type=p,i||t.isDefaultPrevented()||f._default&&f._default.apply(d.pop(),n)!==!1||!ke(r)||u&&re.isFunction(r[p])&&!re.isWindow(r)&&(s=r[u],s&&(r[u]=null),re.event.triggered=p,r[p](),re.event.triggered=void 0,s&&(r[u]=s)),t.result}},simulate:function(e,t,n){var r=re.extend(new re.Event,n,{type:e,isSimulated:!0});re.event.trigger(r,null,t)}}),re.fn.extend({trigger:function(e,t){return this.each(function(){re.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return re.event.trigger(e,t,n,!0)}}),re.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){re.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),re.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),te.focusin="onfocusin"in e,te.focusin||re.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){re.event.simulate(t,e.target,re.event.fix(e));
};re.event.special[t]={setup:function(){var r=this.ownerDocument||this,i=Ce.access(r,t);i||r.addEventListener(e,n,!0),Ce.access(r,t,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this,i=Ce.access(r,t)-1;i?Ce.access(r,t,i):(r.removeEventListener(e,n,!0),Ce.remove(r,t))}}});var ut=/\[\]$/,ct=/\r?\n/g,ft=/^(?:submit|button|image|reset|file)$/i,dt=/^(?:input|select|textarea|keygen)/i;return re.param=function(e,t){var n,r=[],i=function(e,t){var n=re.isFunction(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(Array.isArray(e)||e.jquery&&!re.isPlainObject(e))re.each(e,function(){i(this.name,this.value)});else for(n in e)q(n,e[n],t,i);return r.join("&")},re.fn.extend({serialize:function(){return re.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=re.prop(this,"elements");return e?re.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!re(this).is(":disabled")&&dt.test(this.nodeName)&&!ft.test(e)&&(this.checked||!Be.test(e))}).map(function(e,t){var n=re(this).val();return null==n?null:Array.isArray(n)?re.map(n,function(e){return{name:t.name,value:e.replace(ct,"\r\n")}}):{name:t.name,value:n.replace(ct,"\r\n")}}).get()}}),re.fn.extend({wrapAll:function(e){var t;return this[0]&&(re.isFunction(e)&&(e=e.call(this[0])),t=re(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return re.isFunction(e)?this.each(function(t){re(this).wrapInner(e.call(this,t))}):this.each(function(){var t=re(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=re.isFunction(e);return this.each(function(n){re(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){re(this).replaceWith(this.childNodes)}),this}}),re.expr.pseudos.hidden=function(e){return!re.expr.pseudos.visible(e)},re.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},te.createHTMLDocument=function(){var e=z.implementation.createHTMLDocument("").body;return e.innerHTML="<form></form><form></form>",2===e.childNodes.length}(),re.parseHTML=function(e,t,n){if("string"!=typeof e)return[];"boolean"==typeof t&&(n=t,t=!1);var r,i,o;return t||(te.createHTMLDocument?(t=z.implementation.createHTMLDocument(""),r=t.createElement("base"),r.href=z.location.href,t.head.appendChild(r)):t=z),i=de.exec(e),o=!n&&[],i?[t.createElement(i[1])]:(i=b([e],t,o),o&&o.length&&re(o).remove(),re.merge([],i.childNodes))},re.offset={setOffset:function(e,t,n){var r,i,o,a,s,l,u,c=re.css(e,"position"),f=re(e),d={};"static"===c&&(e.style.position="relative"),s=f.offset(),o=re.css(e,"top"),l=re.css(e,"left"),u=("absolute"===c||"fixed"===c)&&(o+l).indexOf("auto")>-1,u?(r=f.position(),a=r.top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(l)||0),re.isFunction(t)&&(t=t.call(e,n,re.extend({},s))),null!=t.top&&(d.top=t.top-s.top+a),null!=t.left&&(d.left=t.left-s.left+i),"using"in t?t.using.call(e,d):f.css(d)}},re.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){re.offset.setOffset(this,e,t)});var t,n,r,i,o=this[0];if(o)return o.getClientRects().length?(r=o.getBoundingClientRect(),t=o.ownerDocument,n=t.documentElement,i=t.defaultView,{top:r.top+i.pageYOffset-n.clientTop,left:r.left+i.pageXOffset-n.clientLeft}):{top:0,left:0}},position:function(){if(this[0]){var e,t,n=this[0],r={top:0,left:0};return"fixed"===re.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),i(e[0],"html")||(r=e.offset()),r={top:r.top+re.css(e[0],"borderTopWidth",!0),left:r.left+re.css(e[0],"borderLeftWidth",!0)}),{top:t.top-r.top-re.css(n,"marginTop",!0),left:t.left-r.left-re.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===re.css(e,"position");)e=e.offsetParent;return e||je})}}),re.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;re.fn[e]=function(r){return _e(this,function(e,r,i){var o;return re.isWindow(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i?o?o[t]:e[r]:void(o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i)},e,r,arguments.length)}}),re.each(["top","left"],function(e,t){re.cssHooks[t]=I(te.pixelPosition,function(e,n){if(n)return n=P(e,t),Je.test(n)?re(e).position()[t]+"px":n})}),re.each({Height:"height",Width:"width"},function(e,t){re.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){re.fn[r]=function(i,o){var a=arguments.length&&(n||"boolean"!=typeof i),s=n||(i===!0||o===!0?"margin":"border");return _e(this,function(t,n,i){var o;return re.isWindow(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?re.css(t,n,s):re.style(t,n,i,s)},t,a?i:void 0,a)}})}),re.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),re.holdReady=function(e){e?re.readyWait++:re.ready(!0)},re.isArray=Array.isArray,re.parseJSON=JSON.parse,re.nodeName=i,re}(window);!function(){function t(e){this.mode=c.MODE_8BIT_BYTE,this.data=e,this.parsedData=[];for(var t=0,n=this.data.length;t<n;t++){var r=[],i=this.data.charCodeAt(t);i>65536?(r[0]=240|(1835008&i)>>>18,r[1]=128|(258048&i)>>>12,r[2]=128|(4032&i)>>>6,r[3]=128|63&i):i>2048?(r[0]=224|(61440&i)>>>12,r[1]=128|(4032&i)>>>6,r[2]=128|63&i):i>128?(r[0]=192|(1984&i)>>>6,r[1]=128|63&i):r[0]=i,this.parsedData.push(r)}this.parsedData=Array.prototype.concat.apply([],this.parsedData),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}function n(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}function r(e,t){if(void 0==e.length)throw new Error(e.length+"/"+t);for(var n=0;n<e.length&&0==e[n];)n++;this.num=new Array(e.length-n+t);for(var r=0;r<e.length-n;r++)this.num[r]=e[r+n]}function i(e,t){this.totalCount=e,this.dataCount=t}function o(){this.buffer=[],this.length=0}function a(){return"undefined"!=typeof CanvasRenderingContext2D}function s(){var e=!1,t=navigator.userAgent;if(/android/i.test(t)){e=!0;var n=t.toString().match(/android ([0-9]\.[0-9])/i);n&&n[1]&&(e=parseFloat(n[1]))}return e}function l(e,t){for(var n=1,r=u(e),i=0,o=v.length;i<=o;i++){var a=0;switch(t){case f.L:a=v[i][0];break;case f.M:a=v[i][1];break;case f.Q:a=v[i][2];break;case f.H:a=v[i][3]}if(r<=a)break;n++}if(n>v.length)throw new Error("Too long data");return n}function u(e){var t=encodeURI(e).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return t.length+(t.length!=e?3:0)}t.prototype={getLength:function(e){return this.parsedData.length},write:function(e){for(var t=0,n=this.parsedData.length;t<n;t++)e.put(this.parsedData[t],8)}},n.prototype={addData:function(e){var n=new t(e);this.dataList.push(n),this.dataCache=null},isDark:function(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++){this.modules[r]=new Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++)this.modules[r][i]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=n.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)},setupPositionProbePattern:function(e,t){for(var n=-1;n<=7;n++)if(!(e+n<=-1||this.moduleCount<=e+n))for(var r=-1;r<=7;r++)t+r<=-1||this.moduleCount<=t+r||(0<=n&&n<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==n||6==n)||2<=n&&n<=4&&2<=r&&r<=4?this.modules[e+n][t+r]=!0:this.modules[e+n][t+r]=!1)},getBestMaskPattern:function(){for(var e=0,t=0,n=0;n<8;n++){this.makeImpl(!0,n);var r=p.getLostPoint(this);(0==n||e>r)&&(e=r,t=n)}return t},createMovieClip:function(e,t,n){var r=e.createEmptyMovieClip(t,n),i=1;this.make();for(var o=0;o<this.modules.length;o++)for(var a=o*i,s=0;s<this.modules[o].length;s++){var l=s*i,u=this.modules[o][s];u&&(r.beginFill(0,100),r.moveTo(l,a),r.lineTo(l+i,a),r.lineTo(l+i,a+i),r.lineTo(l,a+i),r.endFill())}return r},setupTimingPattern:function(){for(var e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(var t=8;t<this.moduleCount-8;t++)null==this.modules[6][t]&&(this.modules[6][t]=t%2==0)},setupPositionAdjustPattern:function(){for(var e=p.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var n=0;n<e.length;n++){var r=e[t],i=e[n];if(null==this.modules[r][i])for(var o=-2;o<=2;o++)for(var a=-2;a<=2;a++)o==-2||2==o||a==-2||2==a||0==o&&0==a?this.modules[r+o][i+a]=!0:this.modules[r+o][i+a]=!1}},setupTypeNumber:function(e){for(var t=p.getBCHTypeNumber(this.typeNumber),n=0;n<18;n++){var r=!e&&1==(t>>n&1);this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=r}for(var n=0;n<18;n++){var r=!e&&1==(t>>n&1);this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=r}},setupTypeInfo:function(e,t){for(var n=this.errorCorrectLevel<<3|t,r=p.getBCHTypeInfo(n),i=0;i<15;i++){var o=!e&&1==(r>>i&1);i<6?this.modules[i][8]=o:i<8?this.modules[i+1][8]=o:this.modules[this.moduleCount-15+i][8]=o}for(var i=0;i<15;i++){var o=!e&&1==(r>>i&1);i<8?this.modules[8][this.moduleCount-i-1]=o:i<9?this.modules[8][15-i-1+1]=o:this.modules[8][15-i-1]=o}this.modules[this.moduleCount-8][8]=!e},mapData:function(e,t){for(var n=-1,r=this.moduleCount-1,i=7,o=0,a=this.moduleCount-1;a>0;a-=2)for(6==a&&a--;;){for(var s=0;s<2;s++)if(null==this.modules[r][a-s]){var l=!1;o<e.length&&(l=1==(e[o]>>>i&1));var u=p.getMask(t,r,a-s);u&&(l=!l),this.modules[r][a-s]=l,i--,i==-1&&(o++,i=7)}if(r+=n,r<0||this.moduleCount<=r){r-=n,n=-n;break}}}},n.PAD0=236,n.PAD1=17,n.createData=function(e,t,r){for(var a=i.getRSBlocks(e,t),s=new o,l=0;l<r.length;l++){var u=r[l];s.put(u.mode,4),s.put(u.getLength(),p.getLengthInBits(u.mode,e)),u.write(s)}for(var c=0,l=0;l<a.length;l++)c+=a[l].dataCount;if(s.getLengthInBits()>8*c)throw new Error("code length overflow. ("+s.getLengthInBits()+">"+8*c+")");for(s.getLengthInBits()+4<=8*c&&s.put(0,4);s.getLengthInBits()%8!=0;)s.putBit(!1);for(;;){if(s.getLengthInBits()>=8*c)break;if(s.put(n.PAD0,8),s.getLengthInBits()>=8*c)break;s.put(n.PAD1,8)}return n.createBytes(s,a)},n.createBytes=function(e,t){for(var n=0,i=0,o=0,a=new Array(t.length),s=new Array(t.length),l=0;l<t.length;l++){var u=t[l].dataCount,c=t[l].totalCount-u;i=Math.max(i,u),o=Math.max(o,c),a[l]=new Array(u);for(var f=0;f<a[l].length;f++)a[l][f]=255&e.buffer[f+n];n+=u;var d=p.getErrorCorrectPolynomial(c),h=new r(a[l],d.getLength()-1),g=h.mod(d);s[l]=new Array(d.getLength()-1);for(var f=0;f<s[l].length;f++){var v=f+g.getLength()-s[l].length;s[l][f]=v>=0?g.get(v):0}}for(var m=0,f=0;f<t.length;f++)m+=t[f].totalCount;for(var y=new Array(m),x=0,f=0;f<i;f++)for(var l=0;l<t.length;l++)f<a[l].length&&(y[x++]=a[l][f]);for(var f=0;f<o;f++)for(var l=0;l<t.length;l++)f<s[l].length&&(y[x++]=s[l][f]);return y};for(var c={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},f={L:1,M:0,Q:3,H:2},d={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},p={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){for(var t=e<<10;p.getBCHDigit(t)-p.getBCHDigit(p.G15)>=0;)t^=p.G15<<p.getBCHDigit(t)-p.getBCHDigit(p.G15);return(e<<10|t)^p.G15_MASK},getBCHTypeNumber:function(e){for(var t=e<<12;p.getBCHDigit(t)-p.getBCHDigit(p.G18)>=0;)t^=p.G18<<p.getBCHDigit(t)-p.getBCHDigit(p.G18);return e<<12|t},getBCHDigit:function(e){for(var t=0;0!=e;)t++,e>>>=1;return t},getPatternPosition:function(e){return p.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,n){switch(e){case d.PATTERN000:return(t+n)%2==0;case d.PATTERN001:return t%2==0;case d.PATTERN010:return n%3==0;case d.PATTERN011:return(t+n)%3==0;case d.PATTERN100:return(Math.floor(t/2)+Math.floor(n/3))%2==0;case d.PATTERN101:return t*n%2+t*n%3==0;case d.PATTERN110:return(t*n%2+t*n%3)%2==0;case d.PATTERN111:return(t*n%3+(t+n)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new r([1],0),n=0;n<e;n++)t=t.multiply(new r([1,h.gexp(n)],0));return t},getLengthInBits:function(e,t){if(1<=t&&t<10)switch(e){case c.MODE_NUMBER:return 10;case c.MODE_ALPHA_NUM:return 9;case c.MODE_8BIT_BYTE:return 8;case c.MODE_KANJI:return 8;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case c.MODE_NUMBER:return 12;case c.MODE_ALPHA_NUM:return 11;case c.MODE_8BIT_BYTE:return 16;case c.MODE_KANJI:return 10;default:throw new Error("mode:"+e)}else{if(!(t<41))throw new Error("type:"+t);switch(e){case c.MODE_NUMBER:return 14;case c.MODE_ALPHA_NUM:return 13;case c.MODE_8BIT_BYTE:return 16;case c.MODE_KANJI:return 12;default:throw new Error("mode:"+e)}}},getLostPoint:function(e){for(var t=e.getModuleCount(),n=0,r=0;r<t;r++)for(var i=0;i<t;i++){for(var o=0,a=e.isDark(r,i),s=-1;s<=1;s++)if(!(r+s<0||t<=r+s))for(var l=-1;l<=1;l++)i+l<0||t<=i+l||0==s&&0==l||a==e.isDark(r+s,i+l)&&o++;o>5&&(n+=3+o-5)}for(var r=0;r<t-1;r++)for(var i=0;i<t-1;i++){var u=0;e.isDark(r,i)&&u++,e.isDark(r+1,i)&&u++,e.isDark(r,i+1)&&u++,e.isDark(r+1,i+1)&&u++,0!=u&&4!=u||(n+=3)}for(var r=0;r<t;r++)for(var i=0;i<t-6;i++)e.isDark(r,i)&&!e.isDark(r,i+1)&&e.isDark(r,i+2)&&e.isDark(r,i+3)&&e.isDark(r,i+4)&&!e.isDark(r,i+5)&&e.isDark(r,i+6)&&(n+=40);for(var i=0;i<t;i++)for(var r=0;r<t-6;r++)e.isDark(r,i)&&!e.isDark(r+1,i)&&e.isDark(r+2,i)&&e.isDark(r+3,i)&&e.isDark(r+4,i)&&!e.isDark(r+5,i)&&e.isDark(r+6,i)&&(n+=40);for(var c=0,i=0;i<t;i++)for(var r=0;r<t;r++)e.isDark(r,i)&&c++;var f=Math.abs(100*c/t/t-50)/5;return n+=10*f}},h={glog:function(e){if(e<1)throw new Error("glog("+e+")");return h.LOG_TABLE[e]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return h.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},g=0;g<8;g++)h.EXP_TABLE[g]=1<<g;for(var g=8;g<256;g++)h.EXP_TABLE[g]=h.EXP_TABLE[g-4]^h.EXP_TABLE[g-5]^h.EXP_TABLE[g-6]^h.EXP_TABLE[g-8];for(var g=0;g<255;g++)h.LOG_TABLE[h.EXP_TABLE[g]]=g;r.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=new Array(this.getLength()+e.getLength()-1),n=0;n<this.getLength();n++)for(var i=0;i<e.getLength();i++)t[n+i]^=h.gexp(h.glog(this.get(n))+h.glog(e.get(i)));return new r(t,0)},mod:function(e){if(this.getLength()-e.getLength()<0)return this;for(var t=h.glog(this.get(0))-h.glog(e.get(0)),n=new Array(this.getLength()),i=0;i<this.getLength();i++)n[i]=this.get(i);for(var i=0;i<e.getLength();i++)n[i]^=h.gexp(h.glog(e.get(i))+t);return new r(n,0).mod(e)}},i.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],i.getRSBlocks=function(e,t){var n=i.getRsBlockTable(e,t);if(void 0==n)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var r=n.length/3,o=[],a=0;a<r;a++)for(var s=n[3*a+0],l=n[3*a+1],u=n[3*a+2],c=0;c<s;c++)o.push(new i(l,u));return o},i.getRsBlockTable=function(e,t){switch(t){case f.L:return i.RS_BLOCK_TABLE[4*(e-1)+0];case f.M:return i.RS_BLOCK_TABLE[4*(e-1)+1];case f.Q:return i.RS_BLOCK_TABLE[4*(e-1)+2];case f.H:return i.RS_BLOCK_TABLE[4*(e-1)+3];default:return}},o.prototype={get:function(e){var t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(var n=0;n<t;n++)this.putBit(1==(e>>>t-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var v=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]],m=function(){var e=function(e,t){this._el=e,this._htOption=t};return e.prototype.draw=function(e){function t(e,t){var n=document.createElementNS("http://www.w3.org/2000/svg",e);for(var r in t)t.hasOwnProperty(r)&&n.setAttribute(r,t[r]);return n}var n=this._htOption,r=this._el,i=e.getModuleCount();Math.floor(n.width/i),Math.floor(n.height/i);this.clear();var o=t("svg",{viewBox:"0 0 "+String(i)+" "+String(i),width:"100%",height:"100%",fill:n.colorLight});o.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink"),r.appendChild(o),o.appendChild(t("rect",{fill:n.colorLight,width:"100%",height:"100%"})),o.appendChild(t("rect",{fill:n.colorDark,width:"1",height:"1",id:"template"}));for(var a=0;a<i;a++)for(var s=0;s<i;s++)if(e.isDark(a,s)){var l=t("use",{x:String(s),y:String(a)});l.setAttributeNS("http://www.w3.org/1999/xlink","href","#template"),o.appendChild(l)}},e.prototype.clear=function(){for(;this._el.hasChildNodes();)this._el.removeChild(this._el.lastChild)},e}(),y="svg"===document.documentElement.tagName.toLowerCase(),x=y?m:a()?function(){function e(){this._elImage.src=this._elCanvas.toDataURL("image/png"),this._elImage.style.display="block",this._elCanvas.style.display="none"}function t(e,t){var n=this;if(n._fFail=t,n._fSuccess=e,null===n._bSupportDataURI){var r=document.createElement("img"),i=function(){n._bSupportDataURI=!1,n._fFail&&n._fFail.call(n)},o=function(){n._bSupportDataURI=!0,n._fSuccess&&n._fSuccess.call(n)};return r.onabort=i,r.onerror=i,r.onload=o,void(r.src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==")}n._bSupportDataURI===!0&&n._fSuccess?n._fSuccess.call(n):n._bSupportDataURI===!1&&n._fFail&&n._fFail.call(n)}if(this._android&&this._android<=2.1){var n=1/window.devicePixelRatio,r=CanvasRenderingContext2D.prototype.drawImage;CanvasRenderingContext2D.prototype.drawImage=function(e,t,i,o,a,s,l,u,c){if("nodeName"in e&&/img/i.test(e.nodeName))for(var f=arguments.length-1;f>=1;f--)arguments[f]=arguments[f]*n;else"undefined"==typeof u&&(arguments[1]*=n,arguments[2]*=n,arguments[3]*=n,arguments[4]*=n);r.apply(this,arguments)}}var i=function(e,t){this._bIsPainted=!1,this._android=s(),this._htOption=t,this._elCanvas=document.createElement("canvas"),this._elCanvas.width=t.width,this._elCanvas.height=t.height,e.appendChild(this._elCanvas),this._el=e,this._oContext=this._elCanvas.getContext("2d"),this._bIsPainted=!1,this._elImage=document.createElement("img"),this._elImage.alt="Scan me!",this._elImage.style.display="none",this._el.appendChild(this._elImage),this._bSupportDataURI=null};return i.prototype.draw=function(e){var t=this._elImage,n=this._oContext,r=this._htOption,i=e.getModuleCount(),o=r.width/i,a=r.height/i,s=Math.round(o),l=Math.round(a);t.style.display="none",this.clear();for(var u=0;u<i;u++)for(var c=0;c<i;c++){var f=e.isDark(u,c),d=c*o,p=u*a;n.strokeStyle=f?r.colorDark:r.colorLight,n.lineWidth=1,n.fillStyle=f?r.colorDark:r.colorLight,n.fillRect(d,p,o,a),n.strokeRect(Math.floor(d)+.5,Math.floor(p)+.5,s,l),n.strokeRect(Math.ceil(d)-.5,Math.ceil(p)-.5,s,l)}this._bIsPainted=!0},i.prototype.makeImage=function(){this._bIsPainted&&t.call(this,e)},i.prototype.isPainted=function(){return this._bIsPainted},i.prototype.clear=function(){this._oContext.clearRect(0,0,this._elCanvas.width,this._elCanvas.height),this._bIsPainted=!1},i.prototype.round=function(e){return e?Math.floor(1e3*e)/1e3:e},i}():function(){var e=function(e,t){this._el=e,this._htOption=t};return e.prototype.draw=function(e){for(var t=this._htOption,n=this._el,r=e.getModuleCount(),i=Math.floor(t.width/r),o=Math.floor(t.height/r),a=['<table style="border:0;border-collapse:collapse;">'],s=0;s<r;s++){a.push("<tr>");for(var l=0;l<r;l++)a.push('<td style="border:0;border-collapse:collapse;padding:0;margin:0;width:'+i+"px;height:"+o+"px;background-color:"+(e.isDark(s,l)?t.colorDark:t.colorLight)+';"></td>');a.push("</tr>")}a.push("</table>"),n.innerHTML=a.join("");var u=n.childNodes[0],c=(t.width-u.offsetWidth)/2,f=(t.height-u.offsetHeight)/2;c>0&&f>0&&(u.style.margin=f+"px "+c+"px")},e.prototype.clear=function(){this._el.innerHTML=""},e}();return e=function(e,t){if(this._htOption={width:256,height:256,typeNumber:4,colorDark:"#000000",colorLight:"#ffffff",correctLevel:f.H},"string"==typeof t&&(t={text:t}),t)for(var n in t)this._htOption[n]=t[n];"string"==typeof e&&(e=document.getElementById(e)),this._htOption.useSVG&&(x=m),this._android=s(),this._el=e,this._oQRCode=null,this._oDrawing=new x(this._el,this._htOption),this._htOption.text&&this.makeCode(this._htOption.text)},e.prototype.makeCode=function(e){this._oQRCode=new n(l(e,this._htOption.correctLevel),this._htOption.correctLevel),this._oQRCode.addData(e),this._oQRCode.make(),this._el.title=e,this._oDrawing.draw(this._oQRCode),this.makeImage()},e.prototype.makeImage=function(){"function"==typeof this._oDrawing.makeImage&&(!this._android||this._android>=3)&&this._oDrawing.makeImage()},e.prototype.clear=function(){this._oDrawing.clear()},e.CorrectLevel=f,e}();var n=e;!function(){var e=null,r=null,i={originalHeatData:null,ajaxHeatData:null,heatDataElement:[],heatMapList:[],heatMode:1,getCurrentUrl:function(){var e=r.urlParse(location.href),n={};return n["sa-request-url"]=sessionStorage.getItem("sensors_heatmap_url"),n["sa-request-url"]=n["sa-request-url"]?encodeURIComponent(n["sa-request-url"]):"",n["sa-request-id"]=sessionStorage.getItem("sensors_heatmap_id"),n["sa-request-type"]=sessionStorage.getItem("sensors_heatmap_type")||"1",t.each(n,function(e,t){t||delete n[e]}),e.addQueryString(n),e.getUrl()},setHeatState:function(t,n,i,o){if(o)"1"===n?this.setClickMap(t,i):"2"===n?this.setScrollMap(t,i):"3"===n&&this.setNoticeMap(t,i);else{var a=e._.urlParse(location.href);if(!t)return!1;n||(n=1);var s={"sa-request-id":t,"sa-request-type":n,"sa-request-url":sessionStorage&&sessionStorage.getItem?sessionStorage.getItem("sensors_heatmap_url")||"":""};try{var l={};r.isJSONString(window.name)?(l=JSON.parse(window.name),window.name=JSON.stringify(r.extend(l,s))):""==window.name&&(window.name=JSON.stringify(s))}catch(u){}1==this.requestType?(a.addQueryString(s),location.href=a.getUrl()):(sessionStorage&&sessionStorage.setItem&&sessionStorage.setItem("sensors_heatmap_type",n),location.reload())}},setDropDown:function(n,r,o){function a(){var n=null,r=!1;return t(window).on("scroll.v2",function(){r||(t("#heatMapContainer").html(""),r=!0),clearTimeout(n),n=setTimeout(function(){i.refreshHeatData(i.heatMode),r=!1},e.para.heatmap.renderRefreshTime||1e3)}),t(window).on("resize.v2",function(){r||(t("#heatMapContainer").html(""),r=!0),clearTimeout(n),n=setTimeout(function(){i.refreshHeatData(i.heatMode),r=!1},e.para.heatmap.renderRefreshTime||1e3)}),function(){t(window).off("scroll.v2"),t(window).off("resize.v2"),n&&(clearTimeout(n),n=null,r=!1)}}function s(e){function n(t,n){r=t,e.click(t,n),"type"==o?p.text(u[r]):"version"==o&&p.text(c[r])}var r=e.init(),o=e.name,s=e.id,l=t(s+">div"),d=t(s+">ul"),p=l.find("span:first");l.on("click",function(e){e.preventDefault(),e.stopPropagation(),t(s+">ul:visible").is(":visible")?d.hide():(t(".sa-sdk-heatmap-toolbar-selectmap ul").css("display","none"),d.css("display","block"),t(document).on("click.sa-jssdk-dropdown",function(){d.hide(),t(document).off("click.sa-jssdk-dropdown")}))}),d.on("click","li",function(){var e=t(this).attr("data-state");r!==e&&n(e)}),e.init&&n(r,!0),"version"===o&&t(document).on("keypress",function(e){f&&(f(),f=null),114==e.keyCode&&i.refreshHeatData(i.heatMode),122==e.keyCode&&(t("#chooseVersion").find("span:first").text("\u65b9\u6848\u4e00"),i.refreshHeatData(1),r="1"),120==e.keyCode&&(t("#chooseVersion").find("span:first").text("\u65b9\u6848\u4e8c"),i.refreshHeatData(2),f=a(),r="2")})}r=r||"1";var l="1"===r?"1":"0",u={1:"\u70b9\u51fb\u56fe",2:"\u89e6\u8fbe\u7387\u56fe",3:"\u6ce8\u610f\u529b\u56fe"},c={1:"\u65b9\u6848\u4e00",2:"\u65b9\u6848\u4e8c"},f=null,d=this;s({init:function(){return r},id:"#chooseType",name:"type",click:function(e,r){d.setHeatState(n,e,o,r),t("#sa_sdk_heatmap_toolbar_filter").toggle("1"==e)}}),"1"===r&&s({init:function(){return t("#chooseVersion").css("display","block"),l},name:"version",id:"#chooseVersion",click:function(e,t){t||(f&&(f(),f=null),"1"===e?i.refreshHeatData(1):"2"===e&&(i.refreshHeatData(2),f=a()))}})},setScrollMap:function(n,i){var a=this;if("string"==typeof n&&e.para.web_url){var s=new r.urlParse(e.para.web_url);s._values.Path="/api/scroll_heat_map/report/"+n;var l=new r.urlParse(e.para.web_url);l._values.Path="/api/scroll_heat_map/report/"+n,l.addQueryString({pathUrl:encodeURIComponent(i)});var u=l.getUrl(),c=new r.urlParse(e.para.web_url);c._values.Path="/api/v2/sa/scroll_heat_maps/report/jsonp/"+n;var f=new r.urlParse(e.para.web_url);f._values.Path="/api/v2/sa/scroll_heat_maps/report/jsonp/"+n,f.addQueryString({pathUrl:encodeURIComponent(i)});var d=f.getUrl(),p=function(e){function n(n){var r=parseInt((n.pageY+15)/10),i=0;i=r<=e.detail.length&&e.detail[r]?Math.floor(e.detail[r]/e.total*100*100)/100:0,u&&u.remove(),u=t(l.replace("{{top}}",n.pageY+15).replace("{{percent}}",i+"%")),t(document.body).append(u)}if("object"!=typeof e||!r.isArray(e.result)||0===e.result.length)return a.showErrorInfo(2,{error:"\u672a\u53d6\u5230\u6570\u636e"}),!1;if(e.detail=e.result||[],!e.total||0===e.total||"number"!=typeof e.total||e.total<2)return a.showErrorInfo(2,{error:"\u6709\u6548\u7684\u89e6\u53d1\u7528\u6237\u6570\u5c11\u4e8e2\u4eba"}),!1;e.origin_total=e.total,e.total=e.result[0],e.percent={};var i={setData:function(e,t,n){e=String(e),this.data[e]=this.data[e]||{},this.data[e][t]=n},data:{},getData:function(){var e={},t=[],n=null;for(var r in this.data){t=[];for(var i in this.data[r])t.push([i,this.data[r][i]]);this.data[r]=t,n=this.data[r].sort(function(e,t){return Math.abs(e[0]-Number(r))-Math.abs(t[0]-Number(r))})[0],e[n[0]]=n[1]}return e}};r.each(e.result,function(t,n){t/e.total==1?e.percent[100]=10*(n+1):t/e.total>.7&&t/e.total<.8?i.setData(75,parseInt(t/e.total*100),10*(n+1)):t/e.total>.45&&t/e.total<.55?i.setData(50,parseInt(t/e.total*100),10*(n+1)):t/e.total>.2&&t/e.total<.3&&i.setData(25,parseInt(t/e.total*100),10*(n+1))}),r.extend(e.percent,i.getData());var o='<div style="border-bottom: 1px dashed #4C4C4D;height:1px;width:100%;position: absolute;top:{{top}}px;"><span style="font-size:12px;position:absolute;padding:0 12px;top:-24px;height:26px;line-height: 26px;left:0;background:#000;color:#eee;border-radius: 2px;">{{percent}}</span></div>';for(var s in e.percent)t(document.body).append(t(o.replace("{{top}}",e.percent[s]-2).replace("{{percent}}",s+"%")));var l='<div style="z-index:99999;border-bottom: 1px solid #272727;height:1px;width:100%;position: absolute;top:{{top}}px;text-align:center;"><span style="font-size:12px;height:26px;line-height: 26px;background:#000;color:#eee;border-radius: 2px;left:50%;margin-left:-65px;position: absolute;top:-13px;padding: 0 5px;">{{percent}}\u7684\u7528\u6237\u6d4f\u89c8\u5230\u8fd9\u91cc</span></div>',u=null;t(document).on("mousemove",r.throttle(n,150))},h=function(e){r.isObject(e)&&e.error?a.showErrorInfo(2,{error:e.error}):a.showErrorInfo(2,{error:"\u670d\u52a1\u5f02\u5e38"}),sessionStorage.removeItem("sensors_heatmap_id")};i?this.requestType=3:this.requestType=1,o.getServerData.start({url:{ajax:3===this.requestType?u:s.getUrl(),jsonp:3===this.requestType?d:c.getUrl()},success:p,error:h})}else e.log("\u7f3a\u5c11web_url")},setNoticeMap:function(){},setContainer:function(e){if(!e)return!1;if(e.classList.add("saContainer"),e&&e.children)for(var t=e.children,n=0;n<t.length;n++)this.setContainer(t[n])},setToolbar:function(e,r,o){function a(e,n,r){for(var i=function(t){var i=Number(n),o=Number(r);return"gt"===e?t>i:"lt"===e?t<i:t>Math.min(i,o)&&t<Math.max(i,o);
},o=t.extend(!0,{},l.processOriginalHeatData2()),a=o.rows,u=[],c=0,f=a.length;c<f;c++)i(a[c].values[0][0])&&u.push(a[c]);o.rows=u,l.ajaxHeatData=o,s()}function s(){var e=""+i.heatMode;"1"===e?i.refreshHeatData(1):"2"===e&&i.refreshHeatData(2)}var l=this,u=document.createElement("div");u.setAttribute("style","height:50px !important;z-index:9999999;background:#272727;width:100%;position:fixed;top:0;left:0; font-size:14px;color:#EFF2F7;margin:0;clear: both;"),u.innerHTML='<div style="height:39px;line-height:39px;padding:3px 15px 9px"><div class="sa-sdk-heatmap-toolbar-selectmap"  id="chooseType" style="position:relative;width:70px;float:left" title="\u9009\u62e9\u67e5\u770b\u7c7b\u578b"><div style="cursor:pointer"><span>\u70b9\u51fb\u56fe</span> <svg style="position:absolute;top:9px" width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="icon" transform="translate(-199.000000, -18.000000)" fill="#99A9BF"><polygon id="Triangle-1-Copy-29" transform="translate(209.000000, 28.000000) scale(1, -1) translate(-209.000000, -28.000000) " points="209 26 213 30 205 30"></polygon></g></g></svg></div><ul style="display:none;list-style:none;margin:0;padding:0;width:100px"><li data-state="1">\u70b9\u51fb\u56fe</li><li data-state="2">\u89e6\u8fbe\u7387\u56fe</li></ul></div><div class="sa-sdk-heatmap-toolbar-selectmap" id="chooseVersion" style="display:none;position:relative;width:70px;float:left" title="\u5207\u6362\u70b9\u51fb\u56fe\u65b9\u6848"><div style="cursor:pointer"><span>\u65b9\u6848\u4e00</span> <svg style="position:absolute;top:9px" width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="icon" transform="translate(-199.000000, -18.000000)" fill="#99A9BF"><polygon id="Triangle-1-Copy-29" transform="translate(209.000000, 28.000000) scale(1, -1) translate(-209.000000, -28.000000) " points="209 26 213 30 205 30"></polygon></g></g></svg></div><ul style="display:none;list-style:none;margin:0;padding:0;width:100px"><li data-state="1">\u65b9\u6848\u4e00</li><li data-state="2">\u65b9\u6848\u4e8c</li></ul></div><div id="sa_sdk_heatmap_toolbar_close" style="float:right;position:relative;width:30px;height:100%;cursor:pointer" title="\u6536\u8d77\u6253\u5f00"><svg style="position:absolute;top:9px;right:0" width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-129.000000, -260.000000)" fill-rule="nonzero" fill="#99A9BF"><polygon points="132.110192 274.35347 130.5 272.842901 138.860144 265 147.23 272.842902 145.619784 274.35347 138.864999 268.016603"></polygon></g></g></svg></div><div style="float:right;padding:0 10px;width:1px;color:#99A9BF">|</div><div id="sa_sdk_heatmap_toolbar_refresh" style="float:right;position:relative;cursor:pointer;width:30px;height:100%" title="\u5237\u65b0\u6570\u636e"><svg style="position:absolute;top:9px;left:5px" width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g><g><path d="M18.1201298,5.45190941 L15.7071603,6.65839414 C14.3331082,3.91029003 11.3336531,2.11731966 7.94879319,2.56975143 C4.59744671,3.02218321 1.91636953,5.78704405 1.54772141,9.13839053 C1.04501944,13.6627083 4.58068998,17.5 9.00446733,17.5 C12.1882465,17.5 14.8693237,15.5227056 15.9585113,12.7243313 L14.098514,12.1043322 L14.0817572,12.1043322 C13.1098668,14.433518 10.5796002,15.9416239 7.7979826,15.3551383 C5.73690451,14.9194632 4.06123127,13.24379 3.62555623,11.1659552 C2.88826001,7.61352789 5.56933719,4.48001893 9.00446733,4.48001893 C11.1660858,4.48001893 13.0093264,5.72001713 13.9141899,7.52974422 L11.4006801,8.80325589 C11.3336531,8.83676935 11.3336531,8.95406648 11.4174368,8.97082321 L16.4612132,10.6297397 C16.5114834,10.6464964 16.5617536,10.612983 16.5785104,10.5627128 L18.2374269,5.51893634 C18.2876971,5.48542287 18.2039134,5.41839594 18.1201298,5.45190941 L18.1201298,5.45190941 Z" fill="#99A9BF"></path><rect x="0" y="0" width="20" height="20"></rect></g></g></g></svg></div><div style="float:right;padding:0 10px;width:1px;color:#99A9BF">|</div><div id="sa_sdk_heatmap_toolbar_share" style="float:right;position:relative;width:30px;height:100%;cursor:pointer" title="\u6253\u5f00\u5206\u4eab"><svg style="position:absolute;top:11px; left: 5px;" width="14px" height="15px" viewBox="0 0 14 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-42.000000, -62.000000)"><g transform="translate(39.000000, 60.000000)"><rect x="0" y="0" width="20" height="20"></rect><path d="M12.9177778,12.725 L7.76833333,9.72777778 C7.80444444,9.56166667 7.83333333,9.39555556 7.83333333,9.22222222 C7.83333333,9.04888889 7.80444444,8.88277778 7.76833333,8.71666667 L12.86,5.74833333 C13.25,6.10944444 13.7627778,6.33333333 14.3333333,6.33333333 C15.5322222,6.33333333 16.5,5.36555556 16.5,4.16666667 C16.5,2.96777778 15.5322222,2 14.3333333,2 C13.1344444,2 12.1666667,2.96777778 12.1666667,4.16666667 C12.1666667,4.34 12.1955556,4.50611111 12.2316667,4.67222222 L7.14,7.64055556 C6.75,7.27944444 6.23722222,7.05555556 5.66666667,7.05555556 C4.46777778,7.05555556 3.5,8.02333333 3.5,9.22222222 C3.5,10.4211111 4.46777778,11.3888889 5.66666667,11.3888889 C6.23722222,11.3888889 6.75,11.165 7.14,10.8038889 L12.2822222,13.8083333 C12.2461111,13.96 12.2244444,14.1188889 12.2244444,14.2777778 C12.2244444,15.4405556 13.1705556,16.3866667 14.3333333,16.3866667 C15.4961111,16.3866667 16.4422222,15.4405556 16.4422222,14.2777778 C16.4422222,13.115 15.4961111,12.1688889 14.3333333,12.1688889 C13.7844444,12.1688889 13.2933333,12.3855556 12.9177778,12.725 Z" id="Shape" fill="#99A9BF"></path></g></g></g></svg></div><div style="float:right;padding:0 10px;width:1px;color:#99A9BF">|</div><div id="sa_sdk_heatmap_toolbar_filter" style="float:right;position:relative;cursor:pointer;width:30px;height:100%;" title="\u7b5b\u9009"><svg style="position: absolute; top: 11px; left: 5px;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="17px" height="15px" viewBox="0 0 17 15" version="1.1"><g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="\u64cd\u4f5c\u680f" transform="translate(-1068.000000, -341.000000)" fill="#99A9BF" fill-rule="nonzero"><g id="screen" transform="translate(1068.000000, 341.000000)"><polygon id="\u8def\u5f84" points="9.13824444 13.2863778 9.13824444 6.65411111 12.5159778 2.08801111 4.52081111 2.08801111 7.8378 6.56684444 7.8378 12.6447111 6.23534444 11.8541778 6.23534444 7.20851111 0.8 0.4 16.2 0.4 10.7646556 7.2299 10.7646556 14.0888889 9.13824444 13.2863778"/></g></g></g></svg></div></div>',document.body.appendChild(u),this.setContainer(u),this.setDropDown(e,r,o);var c=t('<div id="sa_sdk_heatmap_toolbar_corner" style="cursor:pointer;display:none;position: fixed;z-index:999999;top:0;right:10px;padding:3px 8px 0;background:#000;"></div>');c.html('<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-360.000000, -119.000000)" fill-rule="nonzero" fill="#C0CCDA"><polygon transform="translate(370.365000, 129.117652) scale(1, -1) translate(-370.365000, -129.117652) " points="364.4177 133.235303 363 131.905316 370.360724 125 377.73 131.905317 376.312279 133.235302 370.364999 127.655981"></polygon></g></g></svg>'),t(document.body).append(c),this.setContainer(c[0]),t(u).on("click","#sa_sdk_heatmap_toolbar_refresh",function(){"1"===r||null===r?(l.refreshHeatData(i.heatMode),l.showErrorInfo(5)):location.reload()});var f=this.getCurrentUrl(),d=function(){var e=t('<div style="z-index:999999;width:260px;height:260px;position:fixed;right:2px;top:55px;background:#FFF;box-shadow:0 2px 9px 3px rgba(168,173,178,.39);border-radius:3px;"><div style="height:44px;line-height:44px;border-bottom:1px solid #E9F0F7;text-align:center;color:#475669;font-size:14px;position:relative;">\u5206\u4eab\u94fe\u63a5<span style="position:absolute;top:4px;color:#99A9BF;cursor:pointer;right:4px"><svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g><g transform="translate(-1.000000, -1.000000)"><polygon fill="#99A9BF" transform="translate(11.106602, 11.106602) rotate(-45.000000) translate(-11.106602, -11.106602) " points="12.3566017 12.3566017 12.3566017 18.6066017 9.85660172 18.6066017 9.85660172 12.3566017 3.60660172 12.3566017 3.60660172 9.85660172 9.85660172 9.85660172 9.85660172 3.60660172 12.3566017 3.60660172 12.3566017 9.85660172 18.6066017 9.85660172 18.6066017 12.3566017"></polygon><rect x="1" y="1" width="20" height="20"></rect></g></g></g></svg></span></div><div style="width:128px;height:128px;margin-left:66px;margin-top:16px"></div><div style="margin:20px"><input style="font-size:14px;outline:none;color:#475669;width:92%;border:1px solid #D3DCE6;border-radius:3px;height:32px;line-height:32px;padding:0 10px;" type="text" value=""></div></div>');t(document.body).append(e);var r=e.find("div:eq(1)")[0];return e.find("input").val(f),e.find("span").on("click",function(){e.css("display","none")}),new n(r,{text:f,width:128,height:128,colorDark:"#000000",colorLight:"#ffffff",correctLevel:n.CorrectLevel.L}),e.css("top",t(u).height()+1),d=function(){return e},e};t(u).on("click","#sa_sdk_heatmap_toolbar_share",function(){var e=d();e.css("display","block"),l.setContainer(e[0]),setTimeout(function(){e.find("input").focus(),e.find("input").select()},1)});var p='<div id="sa_sdk_heatmap_filterFlyout" style="z-index: 999999; position: fixed; right: 2px; top: 51px; width:432.5px; height: 171px; background:rgba(255,255,255,1);box-shadow:0px 2px 9px 3px; rgba(10,10,10,0.39); border-radius:3px;"><form>';p+='<div style="height: 45px; text-align: center; line-height: 45px; font-size:14px; font-family:PingFangSC-Medium,PingFang SC; font-weight:500; color:rgba(32,45,61,1); border-bottom: 1px solid #E9F0F7;">',p+="\u7b5b\u9009\u5c55\u793a\u5143\u7d20",p+="</div>",p+='<div style="height: 78px; border-bottom: 1px solid #E9F0F7;">',p+='<p style="margin: 0; line-height: 78px; padding-left: 20px; font-size:14px; font-family:PingFangSC-Regular,PingFang SC; font-weight:400; color:rgba(71,86,105,1);">\u70b9\u51fb\u6570 <select name="filter_type"><option value="gt" selected>&gt;</option><option value="lt">&lt;</option><option value="between">\u5728\u533a\u95f4</option></select><input type="text" name="filterValue"><span class="filter_between_span" style="display:none;"><input type="text" name="filterValueStart" style="margin-right: 5px;">\u81f3<input type="text" name="filterValueEnd" style="margin-left: 5px;"></span> \u7684\u5143\u7d20</p>',p+="</div>",p+='<div style="height: 47px; overflow: hidden; padding: 10px 15px; box-sizing: border-box;">',p+='<div class="sa-sdk-heatmap-filterflyout-submitbtn" style="display: inline-block; float: right; width:56px; height:28px; background:rgba(4,203,148,1); border-radius:3px; font-size:13px; font-family:PingFangSC-Medium,PingFang SC; font-weight:500; color:rgba(255,255,255,1); text-align: center; line-height: 28px; cursor: pointer;">\u786e\u5b9a</div><a class="sa-sdk-heatmap-filterflyout-cancelbtn" href="#" style="float: right; display: inline-block; line-height: 28px; margin-right: 6px; font-size:13px; font-family:PingFangSC-Regular,PingFang SC; font-weight:400; color:rgba(71,86,105,1); text-decoration: none;">\u53d6\u6d88</a>',p+="</div>",p+="</form></div>",t(u).on("click","#sa_sdk_heatmap_toolbar_filter",function(e){e.stopPropagation(),t("#sa_sdk_heatmap_filterFlyout").length?t("#sa_sdk_heatmap_filterFlyout").toggle():(t(document.body).append(p),l.setContainer(t("#sa_sdk_heatmap_filterFlyout")[0]))}),t(document.body).on("change",'#sa_sdk_heatmap_filterFlyout select[name="filter_type"]',function(){var e=t(this).val(),n=t('#sa_sdk_heatmap_filterFlyout input[name="filterValue"]'),r=t("#sa_sdk_heatmap_filterFlyout .filter_between_span");"between"===e?(n.hide(),r.show()):(n.show(),r.hide())}),t(document.body).on("click","#sa_sdk_heatmap_filterFlyout .sa-sdk-heatmap-filterflyout-cancelbtn",function(){t("#sa_sdk_heatmap_filterFlyout").hide(),t("#sa_sdk_heatmap_filterFlyout form")[0].reset();var e=t('#sa_sdk_heatmap_filterFlyout input[name="filterValue"]'),n=t("#sa_sdk_heatmap_filterFlyout .filter_between_span");e.show(),n.hide();var r=t.extend(!0,{},l.originalHeatData);return l.ajaxHeatData=r,s(),!1}),t(document.body).on("click","#sa_sdk_heatmap_filterFlyout .sa-sdk-heatmap-filterflyout-submitbtn",function(){var e=t('#sa_sdk_heatmap_filterFlyout select[name="filter_type"]').val(),n=t('#sa_sdk_heatmap_filterFlyout input[name="filterValue"]'),r=t('#sa_sdk_heatmap_filterFlyout input[name="filterValueStart"]'),i=t('#sa_sdk_heatmap_filterFlyout input[name="filterValueEnd"]'),o=function(e){if(!t.isNumeric(e))return!1;var n=Number(e);return Math.floor(n)===n&&n>=0},s="\u8f93\u5165\u7684\u6570\u503c\u4e0d\u5408\u6cd5\uff0c\u8bf7\u8f93\u5165\u5927\u4e8e\u7b49\u4e8e 0 \u7684\u6574\u6570",u="\u533a\u95f4\u5de6\u4fa7\u7684\u6570\u5b57\u5927\u4e8e\u53f3\u4fa7\u7684\u6570\u5b57\uff0c\u65e0\u6cd5\u7b5b\u9009\uff0c\u8bf7\u4fee\u6539\u6570\u503c";if("between"===e){if(!o(r.val()))return void l.showErrorInfo(2,{error:s});if(!o(i.val()))return void l.showErrorInfo(2,{error:s});if(Number(r.val())>Number(i.val()))return void l.showErrorInfo(2,{error:u});a(e,r.val(),i.val())}else{if(!o(n.val()))return void l.showErrorInfo(2,{error:s});a(e,n.val())}}),t("#sa_sdk_heatmap_toolbar_filter").on("mouseenter",function(){t('#sa_sdk_heatmap_toolbar_filter g[fill^="#"]').attr("fill","#559FF0")}).on("mouseleave",function(){t('#sa_sdk_heatmap_toolbar_filter g[fill^="#"]').attr("fill","#99A9BF")}),t("#sa_sdk_heatmap_toolbar_share").on("mouseenter",function(){t("#sa_sdk_heatmap_toolbar_share path").attr("fill","#559FF0")}).on("mouseleave",function(){t("#sa_sdk_heatmap_toolbar_share path").attr("fill","#99A9BF")}),t("#sa_sdk_heatmap_toolbar_refresh").on("mouseenter",function(){t("#sa_sdk_heatmap_toolbar_refresh path").attr("fill","#559FF0")}).on("mouseleave",function(){t("#sa_sdk_heatmap_toolbar_refresh path").attr("fill","#99A9BF")}),t("#sa_sdk_heatmap_toolbar_close").on("mouseenter",function(){t("#sa_sdk_heatmap_toolbar_close g").eq(1).attr("fill","#559FF0")}).on("mouseleave",function(){t("#sa_sdk_heatmap_toolbar_close g").eq(1).attr("fill","#99A9BF")}),t("#sa_sdk_heatmap_toolbar_corner").on("mouseenter",function(){t("#sa_sdk_heatmap_toolbar_corner g").eq(1).attr("fill","#559FF0")}).on("mouseleave",function(){t("#sa_sdk_heatmap_toolbar_corner g").eq(1).attr("fill","#99A9BF")}),t(u).on("click","#sa_sdk_heatmap_toolbar_close",function(){t(u).hide(0),t("#sa_sdk_heatmap_toolbar_corner").show(0)}),t("#sa_sdk_heatmap_toolbar_corner").on("click",function(){t("#sa_sdk_heatmap_toolbar_corner").hide(0),t(u).show(0)})},showErrorInfo:function(e,t){var n=document.createElement("div");n.setAttribute("style","background:#e55b41;border:none;border-radius:4px;color:#fff;font-size:18px;left:50%;margin-left:-300px;padding:12px;position: fixed;top:60px;text-align: center;width:600px;z-index:999999;"),1===e?n.innerHTML="\u5f53\u524d\u9875\u9762\u5728\u6240\u9009\u65f6\u95f4\u6bb5\u5185\u6682\u65f6\u6ca1\u6709\u70b9\u51fb\u6570\u636e":2===e?t.error?n.innerHTML=t.error:n.innerHTML="\u8bf7\u6c42\u6570\u636e\u5f02\u5e38":3===e?n.innerHTML="\u5f53\u524d\u9875\u9762\u5728\u6240\u9009\u65f6\u95f4\u6bb5\u5185\u6682\u65f6\u6ca1\u6709\u70b9\u51fb\u6570\u636e":4===e?t.error?n.innerHTML=t.error:n.innerHTML="\u8bf7\u6c42\u6570\u636e\u5f02\u5e38":5===e&&(n.style.backgroundColor="#13CE66",n.innerHTML="\u5237\u65b0\u6570\u636e\u6210\u529f"),document.body.appendChild(n),setTimeout(function(){document.body.removeChild(n)},5e3)},requestType:1,getHeatType:function(){},setClickMap:function(n,i){var a=this;if("string"==typeof n&&e.para.web_url){var s=new r.urlParse(e.para.web_url);s._values.Path="/api/heat_map/report/"+n;var l=new r.urlParse(e.para.web_url);l._values.Path="/api/heat_map/report/path/"+n;var u=l.getUrl();u=u.indexOf("?")===-1?u+"?pathUrl="+encodeURIComponent(i):u+"&pathUrl="+encodeURIComponent(i);var c=new r.urlParse(e.para.web_url);c._values.Path="/api/v2/sa/heat_maps/report/jsonp/"+n;var f=new r.urlParse(e.para.web_url);f._values.Path="/api/v2/sa/heat_maps/report/path/jsonp/"+n;var d=f.getUrl();d=d.indexOf("?")===-1?d+"?pathUrl="+encodeURIComponent(i):d+"&pathUrl="+encodeURIComponent(i),t("body").append('<div id="heatMapContainer"></div>'),i?this.requestType=3:this.requestType=1,o.getServerData.start({url:{ajax:3===this.requestType?u:s.getUrl(),jsonp:3===this.requestType?d:c.getUrl()},success:function(e){a.originalHeatData=a.processOriginalHeatData(e),a.bindEffect(),a.calculateHeatData(e)},error:function(e){a.showErrorInfo(2,e),sessionStorage.removeItem("sensors_heatmap_id")}})}else e.log("\u7f3a\u5c11web_url")},processOriginalHeatData:function(n){var i=t.extend(!0,{},n);return t.each(i.rows,function(t,n){try{var i=r.querySelectorAll(n.by_values[0]);i.length&&(n.ele=i[0])}catch(o){e.log("\u5143\u7d20\u7c7b\u540d\u9519\u8bef\uff01",o)}}),i},processOriginalHeatData2:function(){var e=this.originalHeatData,n=t.extend(!0,{},e),r=[],i=[],o=e.rows.slice();return t.each(o,function(e,n){if(!n.ele)return!0;var o=t.inArray(n.ele,i);o===-1?(i.push(n.ele),r.push(t.extend(!0,{},n))):r[o].values[0][0]+=n.values[0][0]}),n.rows=r,n},calculateHeatData:function(n){n=t.extend(!0,{},n),this.ajaxHeatData=n;var i=this;if(!r.isObject(n)||!r.isArray(n.rows)||!r.isObject(n.rows[0]))return i.showErrorInfo(i.requestType),!1;if(!n.page_view||0===Number(n.page_view))return i.showErrorInfo(2,{error:"\u70b9\u51fb\u7387\u8ba1\u7b97\u5931\u8d25\uff0c\u6ca1\u6709\u5f00\u542fautoTrack!"}),!1;var o=parseInt(n.page_view,10),a=n.heat_map_id;n=n.rows;var s=0,l=[],u=[],c=[];if(r.each(n,function(e){var t=null;e.by_values[0]&&(t=r.querySelectorAll(e.by_values[0])[0])&&(l.push(e),c.push(t))}),l.length>1)for(var f=0;f<c.length;f++)for(var d=f+1;d<c.length;d++)if(c[f]===c[d]){l[d].values[0][0]+=l[f].values[0][0],l[f].values[0][0]=0,l[f].by_values="";break}r.each(l,function(e){e.by_values[0]&&r.querySelectorAll(e.by_values[0])[0]&&u.push(e)}),u=r.filter(u,function(e){return e}),0===u.length&&i.showErrorInfo(i.requestType),n=u,r.each(n,function(e){e.value_fix=e.values[0][0],s+=e.value_fix}),i.data_render=n,r.each(n,function(t,n){if(t.by_values[0]){t.data_page_percent=Number(t.value_fix/s*100).toFixed(2)+"%",t.data_click_percent=Number(t.value_fix/o*100).toFixed(2)+"%",t.data_click=Number(t.value_fix/o),t.data_page=Number(t.value_fix/s);var l=new r.urlParse(e.para.web_url);l._values.Path="/web-click/users",3===i.requestType?t.data_user_link=l.getUrl()+"#heat_map_id="+a+"&detail=true&element_selector="+encodeURIComponent(t.by_values[0])+"&page_url="+encodeURIComponent(location.href):t.data_user_link=l.getUrl()+"#heat_map_id="+a+"&detail=true&element_selector="+encodeURIComponent(t.by_values[0]),"null"===String(t.top_values[0])?t.data_top_value="\u6ca1\u6709\u503c":t.data_top_value=String(t.top_values[0]);var u=r.querySelectorAll(t.by_values[0]);"object"==typeof u&&u.length>0&&setTimeout(function(){i.renderHeatData(u,t,n)},100)}})},heatData:function(e){for(var t=[.005,.01,.025,.05,.1,.5],n=0;n<t.length;n++)if(e<t[n])return n;return 6},heatDataTitle:function(e){return"\u70b9\u51fb\u6b21\u6570 "+e.value_fix+"\r\n\u70b9\u51fb\u6982\u7387 "+e.data_click_percent+"\r\n\u70b9\u51fb\u5360\u6bd4 "+e.data_page_percent+"\r\n\u5386\u53f2\u6570\u636e "+String(e.top_values[0]).slice(0,30)},renderHeatData:function(e,n,i){var o=r.ry(e[0]),a=null,s=o.ele.tagName.toLowerCase();if(1==this.heatMode){if("input"===s||"textarea"===s||"img"===s||"svg"===s){o.attr("data-heat-place",String(i));var l=t(e[0]).width();a=o.wrap("span"),"number"==typeof l&&(a.ele.style.width=l),a.ele.style.display="inline-block","svg"===s&&(a.ele.style.minWidth="300px")}else a=o;this.heatDataElement.push(o),t(a.ele).data("clickdata",t.extend(!0,{},n)),a.attr("data-heat-place",String(i)).attr("sa-click-area",this.heatData(n.data_click)).attr("data-click",n.data_click_percent),"inline"===a.getStyle("display")&&(e[0].style.display="inline-block",t(e[0]).attr("sa-heatmap-inlineBlock",""))}else if(2===this.heatMode){var u,c,f,d;if(t(e[0]).is(":visible")&&"0"!==String(t(e[0]).css("opacity"))&&("a"===s&&"inline"===t(e[0]).css("display")?e[0].children[0]?(u=t(e[0].children[0]).outerWidth(),c=t(e[0].children[0]).outerHeight(),f=t(e[0]).children()[0].getBoundingClientRect().left,d=t(e[0]).children()[0].getBoundingClientRect().top):(t(e[0]).css("display","inline-block"),u=t(e[0]).outerWidth(),c=t(e[0]).outerHeight(),f=t(e[0]).offset().left-t(window).scrollLeft(),d=t(e[0]).offset().top-t(window).scrollTop(),t(e[0]).css("display","inline")):(u=t(e[0]).outerWidth(),c=t(e[0]).outerHeight(),f=t(e[0]).offset().left-t(window).scrollLeft(),d=t(e[0]).offset().top-t(window).scrollTop()),t(o.ele).attr("sa-click-area-v2",""),t(o.ele).data("clickdata",t.extend(!0,{},n)),c&&u)){var p={width:u,height:c,left:f,top:d,position:"fixed","z-index":999998,"pointer-events":"none"},h=t("<div sa-click-area></div>");h.css(p),h.attr("data-click",n.data_click_percent),h.attr("sa-click-area",this.heatData(n.data_click)),h.attr("selector",e[0]),h.attr("data-heat-place",String(i)),t("#heatMapContainer").append(h),this.setContainer(t("#heatMapContainer")[0])}}},refreshHeatData:function(e){1==this.heatMode&&(r.each(this.heatDataElement,function(e){var n=e.ele.tagName.toLowerCase();if("input"===n||"textarea"===n||"img"===n||"svg"===n){var i=e.parent();i&&"span"===i.ele.tagName.toLowerCase()&&!r.isUndefined(t(i.ele).attr("sa-click-area"))&&t(e.ele).unwrap()}else t(e.ele).removeAttr("sa-click-area")}),t("[sa-heatmap-inlineBlock]").css("display","inline"),t("[sa-heatmap-inlineBlock]").removeAttr("sa-heatmap-inlineBlock"),this.heatDataElement=[]),2==this.heatMode&&(this.heatDataElement=[],t("[sa-click-area-v2]").removeAttr("sa-click-area-v2"),t("#heatMapContainer").html("")),this.heatMode=e,this.calculateHeatData(this.ajaxHeatData)},refreshScrollData:function(){},is_fix_state:null,showEffectBox:function(e,t,n){if("fixslidedown"===this.is_fix_state)t.style.position="fixed",t.style.left="auto",t.style.right=0,t.style.top=0,n&&(t.className="sa-heat-box-effect-2017314");else if("notfix"===this.is_fix_state){var i=o.getBrowserWidth(),a=o.getBrowserHeight(),s=e.target,l=r.ry(s).offset(),u=r.ry(s).getSize(),c=l.left+u.width+2,f=l.top+1;i<c+220&&(c=l.left-220,l.left<220&&(c=e.pageX));var d=267,p=s.getBoundingClientRect().top;p<0&&(f=e.pageY),a&&p+d>a&&(f=l.top+u.height-d),t.style.position="absolute",t.style.left=c+"px",t.style.top=f+"px"}"block"!==t.style.display&&(t.style.display="block")},bindEffect:function(){function n(n){for(var i=n.target,a=n.currentTarget,d=t(i).data("clickdata");!d&&i.parentNode&&(i=i.parentNode,d=t(i).data("clickdata"),i!==a););if(!d)return!1;var p,h=r.isObject(e.para.heatmap)?e.para.heatmap.setContent:null;h&&"function"==typeof h?(p=h(i),p=p&&"string"==typeof p?r.trim(p):""):p=r.trim(i.textContent),p&&(p=p.replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)),d.data_current_content=p||"\u6ca1\u6709\u503c",l=s.replace(/\{\{[^\{\{]+\}\}/g,function(e){if(e=e.slice(2,-2),"string"==typeof e&&"object"==typeof d)return d[e]}),f.innerHTML=l,o.showEffectBox(n,c,u),o.setContainer(c)}function i(e){var t=e.target;p&&clearTimeout(p),p=setTimeout(function(){t===h&&n(e)},d)}var o=this,a=!1,s='<div style="padding: 8px;"><div style="color: #CACACA">\u5f53\u524d\u5185\u5bb9\uff1a</div><div style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">{{data_current_content}}</div></div><div style="background: #444; height:1px;"></div><div style="padding: 8px;"><table style="width:100%;color:#fff;font-size:13px;background:#333;border:1px solid #333;"><tr><td>\u70b9\u51fb\u6b21\u6570: </td><td style="text-align:right;">{{value_fix}}\u6b21</td></tr><tr><td style="cursor:pointer;" title="\u70b9\u51fb\u6b21\u6570/\u5f53\u524d\u9875\u9762\u7684\u6d4f\u89c8\u6b21\u6570"><span style="float:left;">\u70b9\u51fb\u7387</span><span style="float:left;margin-left:3px;"><svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-1803.000000, -158.000000)" fill="#979797"><g transform="translate(1737.000000, 84.000000)"><path d="M71,74 C68.24,74 66,76.24 66,79 C66,81.76 68.24,84 71,84 C73.76,84 76,81.76 76,79 C76,76.24 73.76,74 71,74 L71,74 Z M71.5,82.5 L70.5,82.5 L70.5,81.5 L71.5,81.5 L71.5,82.5 L71.5,82.5 Z M72.535,78.625 L72.085,79.085 C71.725,79.45 71.5,79.75 71.5,80.5 L70.5,80.5 L70.5,80.25 C70.5,79.7 70.725,79.2 71.085,78.835 L71.705,78.205 C71.89,78.025 72,77.775 72,77.5 C72,76.95 71.55,76.5 71,76.5 C70.45,76.5 70,76.95 70,77.5 L69,77.5 C69,76.395 69.895,75.5 71,75.5 C72.105,75.5 73,76.395 73,77.5 C73,77.94 72.82,78.34 72.535,78.625 L72.535,78.625 Z" id="prompt"></path></g></g></g></svg></span></td><td style="text-align:right;">{{data_click_percent}}</td></tr><tr><td style="cursor:pointer;" title="\u70b9\u51fb\u6b21\u6570/\u5f53\u524d\u9875\u9762\u7684\u70b9\u51fb\u603b\u6b21\u6570"><span style="float:left;">\u70b9\u51fb\u5360\u6bd4</span> <span style="float:left;margin-left:3px;"><svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-1803.000000, -158.000000)" fill="#979797"><g transform="translate(1737.000000, 84.000000)"><path d="M71,74 C68.24,74 66,76.24 66,79 C66,81.76 68.24,84 71,84 C73.76,84 76,81.76 76,79 C76,76.24 73.76,74 71,74 L71,74 Z M71.5,82.5 L70.5,82.5 L70.5,81.5 L71.5,81.5 L71.5,82.5 L71.5,82.5 Z M72.535,78.625 L72.085,79.085 C71.725,79.45 71.5,79.75 71.5,80.5 L70.5,80.5 L70.5,80.25 C70.5,79.7 70.725,79.2 71.085,78.835 L71.705,78.205 C71.89,78.025 72,77.775 72,77.5 C72,76.95 71.55,76.5 71,76.5 C70.45,76.5 70,76.95 70,77.5 L69,77.5 C69,76.395 69.895,75.5 71,75.5 C72.105,75.5 73,76.395 73,77.5 C73,77.94 72.82,78.34 72.535,78.625 L72.535,78.625 Z" id="prompt"></path></g></g></g></svg></span></td><td style="text-align:right;">{{data_page_percent}}</td></tr></table></div><div style="background: #444; height:1px;"></div><div style="padding: 8px;"><div style="color: #CACACA;">\u5386\u53f2\u5185\u5bb9\uff1a</div><div style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">{{data_top_value}}</div></div><div style="background: #444; height:1px;"></div><div style="padding: 6px 8px;"><a style="color:#2a90e2;text-decoration: none;" href="{{data_user_link}}" target="_blank">\u67e5\u770b\u7528\u6237\u5217\u8868</a ></div>',l="",u=!0,c=document.createElement("div");document.body.appendChild(c),c.setAttribute("style",'border-radius:3px;display:none;border:1px solid #000;position: fixed; right:0; top:0; background: #333;line-height:24px;font-size:13px;width:220px;height:265px;color: #fff;font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;box-shadow: 0 2px 4px rgba(0,0,0,0.24);z-index:999999;'),c.innerHTML='<div id="sa_heat_float_right_box_content" style="clear:both;"></div>';var f=document.getElementById("sa_heat_float_right_box_content");r.addEvent(c,"mouseleave",function(){"notfix"===o.is_fix_state&&(a=!1,c.style.display="none")}),r.addEvent(c,"mouseenter",function(){"notfix"===o.is_fix_state&&(a=!0)}),r.addEvent(c,"animationend",function(){c.className=""}),this.is_fix_state="notfix";var d=600,p=null,h=null;/iPhone|Android/i.test(navigator.userAgent)?t(document).on("mouseover","[sa-click-area],[sa-click-area-v2]",function(e){var n=e.target;h=n,t(n).on("mouseleave",function(){"notfix"===o.is_fix_state&&setTimeout(function(){a||(a=!1,c.style.display="none")},d)}),i(e)}):t(document).on("mouseover","[sa-click-area],[sa-click-area-v2]",function(e){var t=e.target;h=t,i(e)})},setCssStyle:function(){var e='.saContainer{margin:0;padding:0;font-size:13px;}[sa-click-area] video{visibility:hidden;}.sa-sdk-heatmap-toolbar-selectmap ul{position:absolute;top:40px;left:0;background:#fff;box-shadow:1px 1px 1px rgba(200,200,200,.6);border-radius:3px;}.sa-sdk-heatmap-toolbar-selectmap ul li{cursor:pointer;height:32px;color:#475669;line-height:32px;padding-left:8px}.sa-sdk-heatmap-toolbar-selectmap ul li:hover{background:#00cd90;color:#fff;}.sa-sdk-heatmap-toolbar-selectmap ul li a{text-decoration:none}.sa-heat-box-head-2017322{border-bottom:1px solid rgba(0, 0, 0, .06);cursor:move;height:30px;background:#e1e1e1;color:#999;clear:both}.sa-heat-box-effect-2017314{animation-duration:.5s;animation-fill-mode:both;animation-iteration-count:1;animation-name:sa-heat-box-effect-2017314}@keyframes "sa-heat-box-effect-2017314"{0%{opacity:.6;}to{opacity:1;}} [sa-click-area]{position:relative} [sa-click-area]:before{pointer-events:none;cursor:pointer;content:"";width:100%;position:absolute;left:0;top:0;bottom:0}[sa-click-area="0"]:before{background:hsla(60, 98%, 80%, .75);box-shadow:0 0 0 2px #fefe9b inset}img[sa-click-area="0"]{border:2px solid #fefe9b}[sa-click-area="0"]:hover:before,input[sa-click-area="0"],textarea[sa-click-area="0"]{background:hsla(60, 98%, 80%, .85)}[sa-click-area="1"]:before{background:rgba(255, 236, 142, .75);box-shadow:0 0 0 2px #ffec8e inset}img[sa-click-area="1"]{border:2px solid #ffec8e}[sa-click-area="1"]:hover:before,input[sa-click-area="1"],textarea[sa-click-area="1"]{background:rgba(255, 236, 142, .85)}[sa-click-area="2"]:before{background:rgba(255, 188, 113, .75);box-shadow:0 0 0 2px #ffbc71 inset}img[sa-click-area="2"]{border:2px solid #ffbc71}[sa-click-area="2"]:hover:before,input[sa-click-area="2"],textarea[sa-click-area="2"]{background:rgba(255, 188, 113, .85)}[sa-click-area="3"]:before{background:rgba(255, 120, 82, .75);box-shadow:0 0 0 2px #ff7852 inset}img[sa-click-area="3"]{border:2px solid #ff7852}[sa-click-area="3"]:hover:before,input[sa-click-area="3"],textarea[sa-click-area="3"]{background:rgba(255, 120, 82, .85)}[sa-click-area="4"]:before{background:rgba(255, 65, 90, .75);box-shadow:0 0 0 2px #ff415a inset}img[sa-click-area="4"]{border:2px solid #ff415a}[sa-click-area="4"]:hover:before,input[sa-click-area="4"],textarea[sa-click-area="4"]{background:rgba(255, 65, 90, .85)}[sa-click-area="5"]:before{background:rgba(199, 0, 18, .75);box-shadow:0 0 0 2px #c70012 inset}img[sa-click-area="5"]{border:2px solid #c70012}[sa-click-area="5"]:hover:before,input[sa-click-area="5"],textarea[sa-click-area="5"]{background:rgba(199, 0, 18, .85)}[sa-click-area="6"]:before{background:rgba(127, 0, 79, .75);box-shadow:0 0 0 3px #7f004f inset}img[sa-click-area="6"]{border:2px solid #7f004f}[sa-click-area="6"]:hover:before,input[sa-click-area="6"],textarea[sa-click-area="6"]{background:rgba(127, 0, 79, .85)}[sa-click-area] [sa-click-area]:before{background:0 0 !important}[sa-click-area]:after{pointer-events:none;height:14px;line-height:14px;margin:-7px 0 0 -28px;width:56px;color:#fff;content:attr(data-click);font-size:14px;font-weight:700;left:50%;line-height:1em;position:absolute;text-align:center;text-indent:0;text-shadow:1px 1px 2px #000;top:50%;z-index:10}';e+="#sa_heat_float_right_box_content table td { color: #fff !important; font-size: 13px !important;}",e+="#sa_sdk_heatmap_filterFlyout select {padding-left: 10px;width: 82px; height: 32px;background:rgba(255,255,255,1); border-radius:3px; border:1px solid rgba(211,220,230,1); margin-right: 10px; margin-left: 10px; outline: none;}",e+="#sa_sdk_heatmap_filterFlyout select:hover {border:1px solid rgba(4,203,148,1);}",e+="#sa_sdk_heatmap_filterFlyout select:active, #sa_sdk_heatmap_filterFlyout select:focus {border:2px solid rgba(4,203,148,0.2);}",e+="#sa_sdk_heatmap_filterFlyout input {outline:none;box-sizing: border-box; width:77px; height:32px; background:rgba(255,255,255,1); border-radius:3px; border:1px solid rgba(211,220,230,1); padding-left:10px; padding-right:10px;}",e+="#sa_sdk_heatmap_filterFlyout input:hover {border:1px solid rgba(4,203,148,1);}",e+="#sa_sdk_heatmap_filterFlyout input:active #sa_sdk_heatmap_filterFlyout input:focus {border:2px solid rgba(4,203,148,0.2);}";var t=document.createElement("style");t.type="text/css";try{t.appendChild(document.createTextNode(e))}catch(n){t.styleSheet.cssText=e}document.getElementsByTagName("head")[0].appendChild(t)}},o={jsonp_timer:null,getServerData:{ajax:function(t){var n=this;r.ajax({url:t.url.ajax,type:"POST",cors:!0,header:{cors:"true"},success:function(e){t.success(e)},error:function(i){r.isObject(i)&&i.error?t.error(i):(e.log("AJAX \u8bf7\u6c42\u5931\u8d25\uff0c\u8f6c\u6362\u4e3a JSONP \u8bf7\u6c42",i),
n.jsonp(t))},timeout:5e3})},start:function(e){var t=window.localStorage.getItem("sensors_heatmap_method");t&&"jsonp"===t?this.jsonp(e):this.ajax(e)},jsonp:function(t){var n=function(e){t.success(e),window.localStorage.setItem("sensors_heatmap_method","jsonp")},i=r.isFunction(t.error)?t.error:function(){},o=t.timeout||8e3,a=this;null!==this.jsonp_timer&&clearTimeout(this.jsonp_timer),this.jsonp_timer=setTimeout(function(){i({error:"\u7531\u4e8e\u6570\u636e\u91cf\u8f83\u5927\uff0c\u8bf7\u6c42\u8017\u65f6\u8f83\u957f\uff0c\u8bf7\u7ee7\u7eed\u7b49\u5f85"})},o),r.jsonp({url:t.url.jsonp,callbackName:"saJSSDKHeatRender",success:function(t){null!==a.jsonp_timer&&clearTimeout(a.jsonp_timer),t&&r.isObject(t)&&t.is_success?r.isObject(t.data)?n(t.data):(i({error:"JSONP \u70b9\u51fb\u6570\u636e\u89e3\u6790\u5f02\u5e38"}),e.log("\u89e3\u6790\u6570\u636e\u5f02\u5e38",t)):t&&r.isObject(t)&&t.is_success===!1?(i({error:t.error_msg}),e.log("\u83b7\u53d6\u6570\u636e\u5931\u8d25",t.error_msg)):(i({error:"JSONP \u6570\u636e\u7ed3\u6784\u5f02\u5e38"}),e.log("\u83b7\u53d6\u6570\u636e\u5f02\u5e38",t))},error:function(e){null!==a.jsonp_timer&&clearTimeout(a.jsonp_timer),i("timeout"===e?{error:"JSONP \u8bf7\u6c42\u8d85\u65f6\uff0c\u8bf7\u5c1d\u8bd5\u5237\u65b0\u9875\u9762"}:"https:"===r.URL(location.href).protocol&&"http:"===r.URL(t.url.jsonp).protocol?{error:"\u8be5\u9875\u9762\u534f\u8bae\u4e3a https \uff0c\u8bf7\u4f7f\u7528 https \u7684\u795e\u7b56\u540e\u53f0\u67e5\u770b\u70ed\u529b\u56fe"}:{error:'<div>\u540e\u53f0 JSONP \u5730\u5740\u4e0d\u901a\uff0c\u53ef\u80fd\u539f\u56e0\u5982\u4e0b\uff1a</div><div style="font-size:15px;margin-top:10px;text-align:left;display:inline-block"><div>1. \u68c0\u67e5\u795e\u7b56\u5206\u6790\u7248\u672c\u662f\u5426\u4f4e\u4e8e v2.2.0\uff0c\u5982\u679c\u4f4e\u4e8e\u6b64\u7248\u672c\u8bf7\u8054\u7cfb\u503c\u73ed\u540c\u5b66\u5347\u7ea7\u7248\u672c</div><div style="margin-top:5px">2. \u795e\u7b56\u5206\u6790\u540e\u53f0\u4e0d\u5141\u8bb8\u8bbf\u95ee\uff0c\u8bf7\u8d35\u53f8\u7814\u53d1\u53c2\u8003\u5f53\u524d\u9875\u9762\u7684\u63a7\u5236\u53f0\u62a5\u9519\uff0c\u53bb\u5bf9\u5e94\u7684\u540e\u53f0\u914d\u7f6e\u89e3\u51b3</div></div>'})}})}},getScrollHeight:function(){var e=parseInt(document.body.scrollHeight,10);return isNaN(e)?0:e},getBrowserWidth:function(){var e=window.innerWidth||document.body.clientWidth;return isNaN(e)?0:parseInt(e,10)},getBrowserHeight:function(){var e=window.innerHeight||document.body.clientHeight;return isNaN(e)?0:parseInt(e,10)},sendIframeData:function(){var t=this;r.bindReady(function(){if(window&&window.parent&&window.parent.window&&window!==window.parent.window){var n=t.getScrollHeight();window.parent.window.postMessage({method:"setHeight",params:{height:n>600?n:600}},e.para.web_url),window.parent.window.postMessage({method:"setUrl",params:{request_type:sessionStorage.getItem("sensors_heatmap_type")||"1",url:r.getUrl()}},e.para.web_url)}})},prepare:function(t,n,o){function a(){setTimeout(function(){i.setToolbar(t,n,o),s.sendIframeData()},r.isObject(e.para.heatmap)&&e.para.heatmap.loadTimeout?e.para.heatmap.loadTimeout:2500)}var s=this;if(!document.querySelectorAll)return alert("\u8bf7\u66f4\u65b0\u5230\u6700\u65b0\u7248\u6d4f\u89c8\u5668,\u5efa\u8bae\u7528chrome\u6216\u8005firefox"),!1;e.errorMsg&&i.showErrorInfo(2,{error:e.errorMsg});var l=e.para.web_url||null;return!l&&r.sessionStorage.isSupport()&&sessionStorage.getItem&&sessionStorage.getItem("sensors_heatmap_url")&&(l=sessionStorage.getItem("sensors_heatmap_url")||null),i.setCssStyle(),l?(e.para.web_url=l,sessionStorage.setItem("sensors_heatmap_url",l),a(),void 0):(i.showErrorInfo(2,{error:"\u83b7\u53d6web_url\u8d85\u65f6"}),!1)}};window.sa_jssdk_heatmap_render=function(t,n,i,a){e=t,e.heatmap_version="1.24.13",r=e._,r.querySelectorAll=function(t){if("string"!=typeof t)return e.log("\u9009\u62e9\u5668\u9519\u8bef",t),[];var n=t.split(" ");1===n.length?/^#\d+/.test(n[0])&&(t="#"+r.strToUnicode(n[0].slice(1))):/^#\d+/.test(n[0])&&(n[0]="#"+r.strToUnicode(n[0].slice(1)),t=n.join(" "));try{return document.querySelectorAll(t)}catch(i){return e.log("\u9519\u8bef",t),[]}},o.prepare(n,i,a)}}()}();