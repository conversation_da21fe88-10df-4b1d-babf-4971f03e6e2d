var sensors=getApp().sensors;Component({options:{multipleSlots:!0},lifetimes:{attached:function(){sensors.popupEmitter.attached()}},properties:{},data:{flag:!0,isShow:!1,template:{},image_list:[],plan_obj:{}},methods:{loadImage:function(t){if(t&&t.length>0){var e=[].concat(this.image_list,t);this.setData({image_list:e})}},handle:function(t){return!!this.data.flag&&(t.popupTree||t.popupTree.properties?(this.setData({plan_obj:t}),this.setData({template:t.popupTree}),void(this.data.flag&&(this.setData({isShow:!0}),this.showPopup()))):(this.popupFail(1001),!1))},popupFail(t){var e={$sf_succeed:!1};e.fail_code=t,e.$sf_fail_reason={1000:"图片加载失败",1001:"预览信息解析失败，请检查计划配置",2000:"对照组"}[t];var a=this.data.plan_obj;a.props=e,2e3!==t&&sensors.events.emit("popup_load_fail",a),sensors.events.emit("popup_end",this.data.plan_obj)},clickMask:function(){if(this.data.template.properties.maskCloseEnabled){var t={};t.$sf_msg_action_id=this.data.template.properties.maskActionId,t.$sf_close_type="POPUP_CLOSE_MASK",t.$sf_msg_element_type="mask";var e=this.data.plan_obj;e.props=t,sensors.events.emit("popup_click",e),this.hidePopup()}},hidePopup:function(){this.setData({flag:!0}),this.setData({isShow:!1}),sensors.events.emit("popup_end",this.data.plan_obj)},showPopup(){this.setData({flag:!1});var t=this.data.plan_obj;sensors.events.emit("popup_display",t)},tapContent(){console.log("点击了弹窗窗体")},topCloseButton(){var t=this.data.template.image_button;t.$sf_close_type||(t.$sf_close_type="POPUP_CLOSE_TOPRIGHT"),this.popupCLick(t)},buttomCloseButton(){var t=this.data.template.image_button;t.$sf_close_type||(t.$sf_close_type="POPUP_CLOSE_BOTTOM"),this.popupCLick(t)},clickImage(){var t=this.data.template.img;t.$sf_close_type||(t.$sf_close_type="POPUP_CLOSE_BUTTON"),this.popupCLick(t)},buttonFirst(){var t=this.data.template.button[0];"close"!==t.action_type||t.$sf_close_type||(t.$sf_close_type="POPUP_CLOSE_BUTTON"),this.popupCLick(t)},buttonSecond(){var t=this.data.template.button[1];"close"!==t.action_type||t.$sf_close_type||(t.$sf_close_type="POPUP_CLOSE_BUTTON"),this.popupCLick(t)},popupCLick(t){var e={};switch(e.$sf_msg_element_type=t.type,e.$sf_msg_element_content=t.innerText,e.$sf_msg_action_id=t.id,e.action_value=t.value,e.$sf_msg_element_action=t.action_type,e.$sf_close_type=t.$sf_close_type,t.action_type){case"copy":wx.setClipboardData({data:t.value,success(t){console.log("复制文本成功")}});break;case"navigateTo":var a={path:t.path};this.navigatePage(a);break;case"navigateToMiniProgram":a={path:t.path,appid:t.appid};this.navigateMini(a)}var s=this.data.plan_obj;s.props=e,sensors.events.emit("popup_click",s),t.closeable&&this.hidePopup()},navigatePage(t){wx.navigateTo({url:t.path,success:function(){console.log("navigate success")},fail:function(t){console.log("navigate fail: ",t)}})},navigateMini(t){wx.navigateToMiniProgram({appId:t.appid,path:t.path,success(){console.log("navigate success")},fail(t){console.log("navigate fail： ",t)}})}}});