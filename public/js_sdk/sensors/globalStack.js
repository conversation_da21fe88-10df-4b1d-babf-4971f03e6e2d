// 定义全局数组
const microPageNameStack = [];

// 导出操作方法
export default {
    //微页面id
  push(id,name) {
    if(microPageNameStack&&microPageNameStack.length>0 && microPageNameStack[microPageNameStack.length - 1].id != id){
        microPageNameStack.push({
            id,
            name
        });
    }else {
        microPageNameStack.push({
            id,
            name
        });   
    }
  },
  pop() {
    return microPageNameStack.pop();
  },
  top() {
    if(microPageNameStack && microPageNameStack.length>0){
        return microPageNameStack[microPageNameStack.length - 1].name;
    }
    return "";
  },
  getAll() {
    return [...microPageNameStack];
  },
  clear() {
    microPageNameStack.length = 0;
  }
};