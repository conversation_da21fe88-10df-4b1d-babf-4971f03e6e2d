// 在 App.vue 或者 main.js 中设置
// #ifdef MP-WEIXIN
import sensors from "../Sensorsdata-UniPlugin-JS/index.js";
// #endif

// #ifdef APP
const sensors = uni.requireNativePlugin("Sensorsdata-UniPlugin-App");
// #endif

import pagesJson from "@/pages.json";

import { getSharerUserCode } from "@/utils/util.js";
import microPageStack from "@/public/js_sdk/sensors/globalStack";

const senLogin = (userInfo) => {
  if (userInfo && userInfo.erpCid > 0) {
    console.error("调神策的登录======");
    sensors.login(userInfo.erpCid);
  }
};

const senTrack = (eventName, params) => {
  const userInfo = uni.getStorageSync("user_info");
  if (userInfo) {
    const { erpCid, erpCustTypename } = userInfo;
    params = {
      ...params,
      is_login: erpCid > 0 ? true : false,
      vip_card_level: erpCustTypename,
    };
  }

  try {
    console.error("===发送神策自定义事件==1===", eventName, params);
    console.error("===发送神策自定义事件==2===", sensors);
    sensors.track(eventName, params);
  } catch (e) {
    console.error("=====发送神策自定义事件失败=========", e);
  }
};

const senGoods = (eventName, customParams, item) => {
  const params = {
    // goods_label_price_type: '', //尊享价、秒杀价、新人价、换购价（接口没这个字段，先不传）
    goods_id: item.id,
    goods_name: item.name,
    store_id: item.shopCode,
    goods_status: item.inventoryState == 1 ? "售罄" : "有货",
    LCID: item.floor,
    PLID: item.categoryId,
    goods_brand_id: item.brand || "",
    shoppe_id: item.cabinetGroup || "",
    vender_id: item.shopId,
    // 是否预售
    is_pre_sale: item.isPresale == 1 ? true : false,
    goods_sale: item.saleNum,
    business_format: item.formatType,
    ...customParams,
  };
  senTrack(eventName, params);
};

const handleSenOrder = (order, paymentType, seckillId) => {
  try {
    const shareCode = getSharerUserCode(true, true);
    let goods_id_list = [],
      sku_id_list = [],
      sku_name_list = [],
      goods_amount = 0,
      goods_name_list = [],
      paid_amount = 0,
      discount_amount = 0,
      coupon_id_list = [],
      store_id_list = [],
      vender_id_list = [];
    if (order && order.listOrderItem && order.listOrderItem.length > 0) {
      order.listOrderItem.forEach((item) => {
        goods_id_list.push(item.spuId);
        sku_id_list.push(item.skuId);
        sku_name_list.push(item.specInfo);
        goods_amount += item.quantity;
        goods_name_list.push(item.spuName);
        if (item.paymentCouponDetail) {
          coupon_id_list = coupon_id_list.concat(
            Object.keys(JSON.parse(item.paymentCouponDetail)),
          );
        }
      });
    }

    // 神策发送 提交订单
    senTrack("SubmitOrder", {
      parent_id: order.listOrderItem[0].orderMainId,
      order_id: order.id,
      // 线上订单来源模块列表  这里沟通传场景值
      online_order_source_module_list: shareCode ? [shareCode] : [],
      // 配送方式
      delivery_method:
        order.deliveryWay == "1"
          ? "普通快递"
          : order.deliveryWay == "2"
          ? "上门自提"
          : order.deliveryWay == "3"
          ? "同城配送"
          : order.deliveryWay == "4"
          ? "自动发货"
          : "",
      // 活动ID列表
      activity_id_list: seckillId ? [seckillId] : [],
      // 支付方式
      pay_method_list: [
        paymentType == "1"
          ? "微信"
          : paymentType == "2"
          ? "支付宝"
          : paymentType == "4"
          ? "对公转账"
          : "",
      ],
      // goods_id_list,
      // sku_id_list,
      goods_id_number: [...new Set(goods_id_list)].length,
      sku_id_number: [...new Set(sku_id_list)].length,
      // sku_name_list,
      goods_amount,
      // goods_name_list,
      goods_crossed_price: Number(order.salesPrice),
      paid_amount: Number(order.paymentPrice),
      discount_amount: Number(order.paymentPromotionsPrice),
      coupon_amount: Number(order.paymentCouponPrice),
      coupon_id_number: [...new Set(coupon_id_list)].length,
      store_id: order.listOrderItem[0].shopCode,
      vender_id: order.listOrderItem[0].shopId,
      // is_flash_sale: seckillId > 0 ? true : false
    });

    // 像神策发送SubmitOrderDetail，（sku维度，一个sku一条事件）
    if (order && order.listOrderItem && order.listOrderItem.length > 0) {
      order.listOrderItem.forEach((item) => {
        senTrack("SubmitOrderDetail", {
          parent_id: item.orderMainId,
          order_id: order.id,
          // 线上订单来源模块列表  这里沟通传场景值
          online_order_source_module_list: shareCode ? [shareCode] : [],
          goods_id: item.spuId,
          sku_id: item.skuId,
          sku_name: item.specInfo,
          goods_amount: Number(item.quantity),
          goods_name: item.spuName,
          goods_crossed_price: Number(item.salesPrice),
          paid_amount: Number(item.paymentPrice),
          discount_amount: Number(item.paymentPromotionsPrice),
          coupon_amount: Number(item.paymentCouponPrice),
          coupon_id_list: item.paymentCouponDetail
            ? Object.keys(JSON.parse(item.paymentCouponDetail))
            : [],
          store_id: item.shopCode,
          vender_id: item.shopId,
          LCID: item.floor,
          PLID: item.categoryId,
          goods_brand_id: item.brand || "",
          shoppe_id: item.cabinetGroup || "",
          delivery_method:
            order.deliveryWay == "1"
              ? "普通快递"
              : order.deliveryWay == "2"
              ? "上门自提"
              : order.deliveryWay == "3"
              ? "同城配送"
              : order.deliveryWay == "4"
              ? "自动发货"
              : "",
          // 活动ID列表
          // 支付方式
          pay_method_list: [
            paymentType == "1"
              ? "微信"
              : paymentType == "2"
              ? "支付宝"
              : paymentType == "4"
              ? "对公转账"
              : "",
          ],
          business_format: item.formatType,
          // 是否预售
          is_pre_sale: item.isPresale == 1 ? true : false,
          is_flash_sale: seckillId > 0 ? true : false,
          activity_id: seckillId || "",
        });
      });
    }
  } catch (error) {
    console.error("数据失败:", error);
  }
};

function getCurrentPage(pre) {
  const pages = getCurrentPages();
  // 某些特殊情况下(比如页面进行redirectTo时的一些时机)，pages可能为空数组
  return `${pages[pages.length - 1 - pre]?.route ?? ""}`;
}

function getCurrentStyle(pre) {
  const route = getCurrentPage(pre);
  // 1. 主包查找
  let pageObj = (pagesJson.pages || []).find((item) => item.path === route);
  if (pageObj && pageObj.style) return pageObj.style;
  // 2. 子包查找
  if (Array.isArray(pagesJson.subPackages)) {
    for (const sub of pagesJson.subPackages) {
      if (Array.isArray(sub.pages)) {
        // 子包页面路径拼接root
        const subPageObj = sub.pages.find((item) => {
          // 兼容子包path前无/有/的情况
          const fullPath =
            sub.root.replace(/\/$/, "") + "/" + item.path.replace(/^\//, "");
          return fullPath === route;
        });
        if (subPageObj && subPageObj.style) return subPageObj.style;
      }
    }
  }
  return undefined;
}

// 当前页 pre传0 ，上一页 传1 依次类推
function getCurrentTitle(pre = 0) {
  try {
    const style = getCurrentStyle(pre);

    // 如果没有获取到样式信息，返回空字符串
    // 可能前面的没获取到，就传值  getSharerUserCode();
    if (!style) {
      return getSharerUserCode();
    }

    const navigationBarTitleText = style.navigationBarTitleText || "";

    // 获取微页面堆栈中的当前页面名称
    const currentMicroPageName = microPageStack.top();

    // 如果当前页面是微页面且堆栈中有名称
    if (navigationBarTitleText === "微页面" && currentMicroPageName) {
      return currentMicroPageName;
    }

    // 如果堆栈中有微页面名称且当前页面有标题，拼接显示
    if (currentMicroPageName && navigationBarTitleText) {
      return `${currentMicroPageName} - ${navigationBarTitleText}`;
    }

    // 默认返回页面配置的标题
    return navigationBarTitleText;
  } catch (error) {
    console.error("获取页面标题失败:", error);
    return "";
  }
}

module.exports = {
  senLogin,
  senTrack,
  senGoods,
  getCurrentTitle,
  handleSenOrder,
};
