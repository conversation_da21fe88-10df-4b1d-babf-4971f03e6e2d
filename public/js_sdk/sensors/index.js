import { getPlatformType } from "@/utils/util.js"
// 在 App.vue 或者 main.js 中设置 
// #ifdef MP-WEIXIN
import sensors from '../Sensorsdata-UniPlugin-JS/index.js';

export default function initSensors() {
	sensors.init({
		server_url: 'https://cdpdata.songleigroup.com/sa?project=default',
		show_log: true, //是否开启日志
		name: "sensors",
		global_properties: { // 配置全局属性，所有上报事件属性中均会携带
			platform_type: getPlatformType(),
		},
		autoTrack: { //小程序全埋点配置
			appLaunch: true, // 默认为 true，false 则关闭 $MPLaunch 事件采集
			appShow: true, // 默认为 true，false 则关闭 $MPShow 事件采集
			appHide: true, // 默认为 true，false 则关闭 $MPHide 事件采集
			pageShow: true, // 默认为 true，false 则关闭 $MPViewScreen 事件采集
			pageShare: true, // 默认为 true，false 则关闭 $MPShare 事件采集
			mpClick: true, // 默认为 false，true 则开启 $MPClick 事件采集
			mpFavorite: true, // 默认为 true，false 则关闭 $MPAddFavorites 事件采集
			pageLeave: true // 默认为 false， true 则开启 $MPPageLeave事件采集
		},
		app: { // Android & iOS 初始化配置
			remote_config_url: "",
			flush_interval: 15000, //两次数据发送的最小时间间隔，单位毫秒
			flush_bulkSize: 100, //设置本地缓存日志的最大条目数，最小 50 条， 默认 100 条
			flush_network_policy: 1, //设置 flush 时网络发送策略
			auto_track: 3, // 1 应用启动， 2 应用退出，3 应用启动和退出 默认 0
			encrypt: false, //是否开启加密
			add_channel_callback_event: false, //是否开启渠道事件
			javascript_bridge: false, // WebView 打通功能
			android: { //Android 特有配置
				session_interval_time: 30000,
				request_network: true,
				max_cache_size: 32, // 默认 32MB，最小 16MB
				mp_process_flush: false, //使用小程序 SDK 时，小程序进程是否可发送数据
			},
			ios: { //iOS 特有配置
				max_cache_size: 10000, //最大缓存条数，默认 10000 条
			}
		}
	});
}
// #endif

// #ifdef APP
const sensors = uni.requireNativePlugin('Sensorsdata-UniPlugin-App');
export default function initSensors() {
    try {
		sensors.initSDK({
			server_url: 'https://cdpdata.songleigroup.com/sa?project=default',
			show_log: true, //是否开启日志
			global_properties: { // 配置全局属性，所有上报事件属性中均会携带
				platform_type: getPlatformType(),
			},
			app: { // Android & iOS 初始化配置
				remote_config_url: "",
				flush_interval: 15000, //两次数据发送的最小时间间隔，单位毫秒
				flush_bulkSize: 100, //设置本地缓存日志的最大条目数，最小 50 条， 默认 100 条
				flush_network_policy: 1, //设置 flush 时网络发送策略
				auto_track: 3, // 1 应用启动， 2 应用退出，3 应用启动和退出 默认 0
				encrypt: false, //是否开启加密
				track_crash: false, // 是否采集 AppCrash 事件
				add_channel_callback_event: false, //是否开启渠道事件
				javascript_bridge: false, // WebView 打通功能
				android: { //Android 特有配置
					session_interval_time: 30000,
					request_network: true,
					max_cache_size: 32, // 默认 32MB，最小 16MB
					mp_process_flush: false, //使用小程序 SDK 时，小程序进程是否可发送数据
				},
				ios: { //iOS 特有配置
					max_cache_size: 10000, //最大缓存条数，默认 10000 条
				}
			}
		});
		sensors.enableLog(true);
		sensors.setFlushNetworkPolicy(255);
	} catch (e) {
		console.error("=====APP初始化神策失败=========", e);
	}

};
// #endif