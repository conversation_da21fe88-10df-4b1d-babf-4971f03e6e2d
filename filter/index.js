// 手机号加密
export const phoneEncryption = (val) => {
  if (val) {
    return (val.substring(3, 0)) + '****' + (val.substring(7));
  }
};

export const formatImg750 = (url) => {
  if (!url) return "https://img.songlei.com/live/img/no_pic.png"
  if (url.endsWith('gif') && url.startsWith('https://img.songlei.com/')) return url + '-gif_q90_s80';
  else if (url.startsWith('https://img.songlei.com/')) return url + '-jpg_w750_q70';
  return url
};

export const formatImg360 = (url) => {
  if (Array.isArray(url)) {
    url = url[0]
  }
  if (!url) return "https://img.songlei.com/live/img/no_pic.png"
  else if (url && url.endsWith('gif') && url.startsWith('https://img.songlei.com/')) return url;
  else if (url.startsWith('https://img.songlei.com/')) return url + '-jpg_w360_q90';
  return url
};

export const saleNumFilter = (saleNum) => {
  // if (saleNum > 300) return saleNum
  // return saleNum + 300
  return saleNum
}

export const rounding = (value) => {
  if (!isNaN(value)) {
    return ((value + '').indexOf('.') == -1) ? value : Number(value).toFixed(1);
  }
}

/**
 * 保留前后四位 中间每4个*会有一个空格  6212 **** **** *** 0222
 * @param {value} 传入数据
 */
export const bankCard = (value) => {
  if (value && value.length > 8) {
    return `${value.substring(0, 4)} ${"*".repeat(value.length - 8).replace(/(.{4})/g, `
    $1 `)}${value.length % 4 ? " " : ""}${value.slice(-4)}`;
  }
  return value;
}

/**
 * 只显示后四位 *********** 0222
 * @param {value} 传入数据
 */
export const onlyFourBank = (value) => {
  if (value && value.length > 8) {
    return `${"*".repeat(value.length - 8)}${value.length % 4 ? " " : ""}${value.slice(-4)}`;
  }
  return value;
}

/**
 * 一串数字每四位数字用空格隔开,8222 8000 1000 11 
 * @param {num} 传入数据
 */
export const fourNums = (num) => {
  return num.replace(/(.{4})/g, "$1 ")
}